MXView one risk
===
[toc]

## 1. Excessive State Management
Global Event Bus System
The application uses a custom event bus system through the AppState service that emits global events:
```javascript=
@Injectable()
export class AppState {
  private eventSource = new Subject<GlobalEvent>();
  events = this.eventSource.asObservable();

  emitEvent(event: GlobalEvent): void {
    if (this.eventSource) {
      // Push up a new event
```

Risks:
  - This creates implicit dependencies between components that are hard to track
  - Event-based communication makes the application flow difficult to follow
  - No type safety for event payloads
  - Difficult to debug when events are missed or handled incorrectly
  - Mixed State Management Approaches

The application mixes different state management approaches:
  - Custom event bus (AppState)
  - Component-level state
  - NgRx/Component Store in some newer components
  - Direct service calls with state
  
This inconsistency makes it hard to understand how data flows through the application.

## 2. Complex Component Structure
### MxTopologyComponent
The topology component is extremely complex with many responsibilities:
```javascript=
export class MxTopologyComponent implements OnInit, OnChanges, <PERSON><PERSON><PERSON>roy {
  @Input() sideNav: MatSidenav;
  @Input() commandBar: MxCommandBarComponent;
  @Input() siteId: any;
  @Input() groupId: number;
  @Input() trigger: EventBusService;
  @Input() hasSideNav = false;
  @Input() viewMode: TopologyViewMode;
  @Input() moveToDevice: DeviceUnit;
  @Input() moveToLink: LinkUnit;
  @Input() isEditableTopology: boolean;
```

Risks:
  - Too many inputs/outputs (over 20 inputs and 15 outputs)
  - Handles multiple concerns (rendering, selection, events, layout)
  - Complex lifecycle management
  - Tightly coupled with many other components and services

### MainComponent
The main component has excessive responsibilities:
```javascript=
export class MainComponent implements OnInit {
  @ViewChild(MxSiteNavComponent) mxSiteNav: MxSiteNavComponent;
  @ViewChild('topology') mxTopology: MxTopologyComponent;
  sideNav = false;
  parentSiteId: string;
  parentGroupId: number;
  isFullNetworkMode = false;
  isTopologyMode = true;
  site: SiteUnit;
  group: GroupUnit;
  device: DeviceUnit;
  link: LinkUnit;
```
Risks:
  - Manages too many different UI states
  - Handles multiple layout configurations
  - Directly accesses child component methods
  - Mixes presentation and business logic

## 3. Heavy Coupling Between Components
The application has tight coupling between components, making it difficult to modify one component without affecting others:
```javascript=
<app-mx-topology
  #topology
  [hasSideNav]="sideNav"
  [trigger]="eventBus"
  [siteId]="parentSiteId"
  [groupId]="parentGroupId"
  [viewMode]="topologyViewMode"
  [topologyText]="topologyTextObj"
  [wirelessAddon]="isWirelessAddon"
  (siteNotSelected)="onSiteNotSelected($event)"
  (siteSelected)="onSiteSelected($event)"
  (siteMultiSelected)="onSiteMultiSelected($event)"
```
Risks:
  - Components are tightly coupled through numerous inputs/outputs
  - Direct ViewChild references create rigid dependencies
  - Changes to one component often require changes to multiple others
  - Difficult to test components in isolation

## 4. Complex Event Handling
The application uses a complex event system with multiple layers:
```javascript=
subscribeMxTopologyEvent(): void {
  this.mxTopologyEventSubscription = this.mxUtils.events.subscribe((event: MxTopologyEvent) => {
    if (event.type === MxTopologyEventType.CREATE_SELECTION) {
      this.createSelection(event.value);
    } else if (event.type === MxTopologyEventType.SITE_NOT_SELECTED) {
      this.siteNotSelected.emit();
    } else if (event.type === MxTopologyEventType.SITE_MULTI_SELECTED) {
      this.siteMultiSelected.emit(event.value);
    }
```
Risks:
  - Multiple event systems (global events, component events, D3 events)
  - Complex event propagation chains
  - Difficult to track event flow
  - Easy to introduce race conditions or missed events

## 5. Inconsistent Error Handling
The application has inconsistent error handling patterns:
```javascript=
handleError(errorRes: HttpErrorResponse): Observable<never> {
  const errCode = errorRes.error?.error?.code || errorRes.error?.code || '';

  const match = errorRes.message?.match(/{.*}/);
  if (match && match.length > 0) {
    console.log(JSON.parse(match[0]));
    const errMsg = JSON.parse(match[0])?.msg || '';
    if (errMsg === 'This token is revoked.') return;
  }
```
Risks:
  - Multiple error handling services with different approaches
  - Inconsistent error reporting to users
  - Some errors are logged but not handled
  - Complex error parsing logic

## 6. Navigation and Routing Issues
The application has complex navigation logic, particularly around the Moxa logo click behavior:
```javascript=
returnTopology(): void {
  // 使用者無法建立 cache 時，不讓使用者可以跳到 Topology 頁面
  const sitesData = this.dataRepository.getData(DataType.SITE);
  if (sitesData.length > 0) {
    // Always reset to root group (id=0) when clicking the Moxa logo
    localStorage.setItem('groupId', '0');

    if (this.router.url.startsWith('/pages/network')) {
      // wireless table view 回 topology
      this.appState.emitEvent(new GlobalEvent(GlobalEventType.CLOSE_WIRELESS_TABLE, null));
    } else {
      // 其他功能回topology
      this.router.navigate(['/pages/network']);
    }
  }
}
```
Risks:
  - Navigation logic is spread across multiple components
  - Uses localStorage for state that should be in the router
  - Mixes navigation with state management
  - Special case handling for different routes

## 7. Poor Separation of Concerns
Many components mix multiple responsibilities:
```javascript=
resetToRootGroup(): void {
  // Always reset to root group when clicking the Moxa logo
  this.networkService.selectedGroupId = 0;

  // Clear any selected IOPAC device
  this.networkService.selectedIopacDevice = null;
  this.selectedIopacModule = 0;

  // Reset to root group
  if (this.mxSiteNav) {
    this.mxSiteNav.selectGroup(0, 'Root');
  }

  // Redraw topology to reflect the change
  if (this.mxTopology) {
    this.mxTopology.redrawTopology();
  }
}
```
Risks:
  - Components handle UI, business logic, and data access
  - Services mix state management with API calls
  - No clear separation between data, presentation, and business logic
  - Difficult to reuse code across the application

## 8. Complex D3 Integration
The application has complex D3.js integration for topology visualization:
```javascript=
onClick(object: any): void {
  if (this.mxData.isAffectedPrevewMode) return;
  const elementData = this.mxUtils.getElement(object);
  // elementData.type = object.device_group ? 'device' : elementData.type;
  if (this.mxData.isEditable || elementData.type === 'group' || elementData.type === 'neighbor_device') {
    const d3 = this.mxD3.d3;
    // Handle double click
    const currentTime = new Date().getTime();
```
Risks:
  - Complex D3 event handling
  - Direct DOM manipulation
  - Tightly coupled with application state
  - Difficult to test and debug

## 9. Inconsistent Data Fetching
```javascript=
sendPost(url: string, content: { [key: string]: any } | FormData): Observable<any> {
  const headers = new HttpHeaders({
    Authorization: this.dataObject.getAuthorization(),
    skipLoading: 'false',
  });

  return this._http.post<{ [key: string]: any }>(environment.apiBasePath + url, content, { headers }).pipe(
    map(res => {
      if (res === null) {
        return null;
      }
      if (Object.keys(res).length === 1) {
        return res.data;
      } else {
        return res || '';
      }
    })
  );
}
```
Risks:
  - Multiple HTTP services with different approaches
  - Inconsistent error handling
  - Inconsistent response transformation
  - Mix of Promise and Observable patterns

## 10. Specific Issue with Moxa Logo Navigation
The current implementation for the Moxa logo click has issues:
```javascript=
resetToRootGroup(): void {
  // Always reset to root group when clicking the Moxa logo
  this.networkService.selectedGroupId = 0;

  // Clear any selected IOPAC device
  this.networkService.selectedIopacDevice = null;
  this.selectedIopacModule = 0;

  // Reset to root group
  if (this.mxSiteNav) {
    this.mxSiteNav.selectGroup(0, 'Root');
  }

  // Redraw topology to reflect the change
  if (this.mxTopology) {
    this.mxTopology.redrawTopology();
  }
}
```
Risks:
  - The implementation is spread across multiple components
  - Uses direct component method calls instead of state management
  - Relies on component references being available
  - Mixes navigation with state updates

## 11. Command Bar Menu Management Issues
The application has complex and problematic command bar menu management:

### What's wrong:

  - The mx-command-bar component receives inputs computed across multiple files (auth.service.ts and network.component.ts) with hard-coded menu definitions and complex conditional logic
  - Menu items are defined in auth.service.ts (over 1,500 lines of hardcoded menu definitions) but manipulated in network.component.ts
  - Different device types trigger different menu contexts through complex nested conditionals
  - Menu selection logic is spread across multiple components with no centralized management

### Why it's risky:

  - Adding new menu items requires changes in multiple files and understanding complex conditional logic
  - Device-specific menu behavior is determined through complex if/else chains checking device types and properties
  - Menu state is tightly coupled to component state, making it difficult to predict behavior
  - The same action (e.g., selecting a device) produces different menus based on device type, creating an implicit state machine

### Impact:

  - High maintenance burden for any menu-related changes
  - Regression risk when adding new device types or features
  - Inconsistent user experience as menu behavior varies unpredictably
  - Performance issues due to complex menu generation logic causing unnecessary re-renders

### Example:
```javascript=
if (this.networkService.selectedDevice.wireless_role && this.isWirelessAddon) {
  if (localStorage.getItem('isWindowsOS') === '1') {
    if (this.device.sysobjid.indexOf(this.newWirelessDevicesSysobjid) !== -1) {
      tempCommandBarKey = 'newWirelessAddonDeviceSelectedMenu';
    } else {
      tempCommandBarKey = 'wirelessAddonDeviceSelectedMenu';
    }
  } else {
    if (this.device.sysobjid.indexOf(this.oldWirelessDevicesSysobjid) !== -1) {
      tempCommandBarKey = 'oldWirelessAddonDeviceSelectedMenu';
    } else if (this.device.sysobjid.indexOf(this.newWirelessDevicesSysobjid) !== -1) {
      tempCommandBarKey = 'newWirelessAddonDeviceSelectedMenu';
    } else {
      tempCommandBarKey = 'wirelessAddonDeviceSelectedMenu';
    }
  }
}
```

## 12. Global Singleton Services as State Containers
The application relies heavily on global singleton services for state management:

### What's wrong:
  - Services like DataRepositoryService and DataStorage* classes act as global mutable state containers
  - These services maintain application-wide state that can be modified from anywhere
  - No clear ownership of who can update the state and when
  - State changes don't follow a predictable pattern or flow
  - Multiple components and services read from and write to these global stores

### Why it's risky:
  - Changes to global state from one component can unexpectedly affect other components
  - Debugging becomes extremely difficult as state can be modified from anywhere
  - No guarantees about state consistency across the application
  - Race conditions can occur when multiple components update state simultaneously
  - Testing is difficult because global state persists between tests

### Impact:
  - Unpredictable application behavior due to uncontrolled state mutations
  - Difficult to implement new features that depend on existing state
  - Increased time spent debugging state-related issues
  - Higher cognitive load for developers trying to understand state flow
  - Difficult to maintain as the application grows

Example:
```javascript=
// DataRepositoryService acting as a global state container
@Injectable()
export class DataRepositoryService {
  private dataStorage: { [key: string]: any } = {};
  
  getData(dataType: DataType): any {
    return this.dataStorage[dataType];
  }
  
  setData(dataType: DataType, data: any): void {
    this.dataStorage[dataType] = data;
  }
}
```

## 13. Excessive Component Size and Complexity
The application contains excessively large and complex components:

### What's wrong:
  - Components like network.component.ts and mx-topology.component.ts contain thousands of lines of code
  - These components handle multiple responsibilities: UI rendering, event handling, data processing, and business logic
  - Complex conditional logic and nested if/else statements make the code difficult to understand
  - Components have too many inputs, outputs, and internal state variables
  - High cyclomatic complexity in component methods

### Why it's risky:
  - Large components are difficult to understand, maintain, and test
  - Changes to one part of a component can have unintended effects on other parts
  - High cognitive load for developers working with these components
  - Bug fixes and feature additions become increasingly difficult
  - Performance issues due to complex change detection requirements

### Impact:
  - Increased development time for new features and bug fixes
  - Higher risk of introducing regressions when making changes
  - Difficulty in onboarding new developers to the codebase
  - Resistance to refactoring due to fear of breaking existing functionality
  - Poor performance due to inefficient change detection

Example:
The network.component.ts file reportedly has 95 imports, indicating excessive coupling and responsibilities:
```javascript=
// Just a small sample of the imports in network.component.ts
import { Component, OnInit, OnDestroy, ViewChild, ChangeDetectorRef, ElementRef, HostListener } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { TranslateService, LangChangeEvent } from '@ngx-translate/core';
import { MatIconRegistry } from '@angular/material/icon';
import { DomSanitizer } from '@angular/platform-browser';
import { Observable, Subscription, fromEvent } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import * as _ from 'lodash';
// ... dozens more imports
```

## 14. High Import Count and Tight Coupling
The application has components with excessive dependencies:

### What's wrong:
  - Files like network.component.ts have up to 95 imports, indicating excessive coupling
  - Components directly depend on concrete implementations rather than abstractions
  - Shared module imports over 100 modules, creating a tangled dependency graph
  - Services directly reference other services, creating tight coupling
  - No clear boundaries between different parts of the application

### Why it's risky:
  - Changes to one module can have cascading effects on many others
  - Circular dependencies can easily be introduced
  - Module boundaries are unclear, leading to inappropriate dependencies
  - Testing becomes difficult due to the need for extensive mocking
  - Build times increase due to the need to recompile many dependent modules

### Impact:
  - Slower development cycle due to increased build times
  - Higher risk when making changes due to unknown dependencies
  - Difficulty in isolating and testing components
  - Poor performance due to large bundle sizes
  - Resistance to refactoring due to fear of breaking dependencies

Example:
The shared.module.ts file reportedly has 113 imports, creating a tangled dependency graph:
```javascript=
// A small sample of what shared.module.ts might contain
@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    HttpClientModule,
    RouterModule,
    MaterialModule,
    // ... dozens more imports
  ],
  declarations: [
    // Dozens of component declarations
  ],
  exports: [
    // Exporting everything, creating tight coupling
  ]
})
export class SharedModule { }
```

## 15. Direct Browser Storage Access
The application directly accesses browser storage from multiple components:

### What's wrong:
  - Components and services directly read from and write to localStorage/sessionStorage
  - No abstraction layer for storage access
  - Storage keys are hardcoded strings scattered throughout the codebase
  - No validation or error handling for storage operations
  - Storage access is mixed with business logic

### Why it's risky:
  - Direct storage access creates hidden dependencies on the browser environment
  - Changes to storage keys must be made in multiple places
  - No centralized control over storage usage
  - Testing becomes difficult as it requires mocking browser storage
  - Storage-related bugs are difficult to diagnose

### Impact:
  - Difficult to test components that directly access storage
  - Potential for data inconsistency when storage keys are changed
  - Increased maintenance burden when storage requirements change
  - Poor separation of concerns, mixing persistence with business logic
  - Difficult to migrate to different storage mechanisms

Example:
```javascript=
// Direct localStorage access in component
ngOnInit(): void {
  const storedState = localStorage.getItem('isAutomationButtonBarOpen');
  if (storedState !== null) this.isAutomationButtonBarOpen = JSON.parse(storedState);
  
  // More direct storage access
  if (localStorage.getItem('isWindowsOS') === '1') {
    // Do something based on storage value
  }
}

// Direct storage writes
toggleAutomationButtonSide(): void {
  this.isAutomationButtonBarOpen = !this.isAutomationButtonBarOpen;
  localStorage.setItem('isAutomationButtonBarOpen', JSON.stringify(this.isAutomationButtonBarOpen));
}
```

## 16. Duplicated and Scattered Business Logic
The application has business logic duplicated across multiple components:

### What's wrong:
  - Similar business logic is implemented in multiple places with slight variations
  - No clear ownership of business rules
  - Logic for determining device types, menu items, and UI states is scattered
  - Utility functions are reimplemented rather than shared
  - No clear separation between business rules and UI concerns

### Why it's risky:
  - Changes to business rules must be made in multiple places
  - Inconsistent implementation of the same business logic
  - Difficult to ensure all edge cases are handled consistently
  - Higher chance of bugs when business rules change
  - Increased cognitive load for developers

### Impact:
  - Increased maintenance burden when business rules change
  - Higher risk of inconsistent behavior across the application
  - Difficulty in implementing new features that depend on existing business rules
  - Increased time spent debugging issues related to business logic
  - Resistance to changing business rules due to fear of breaking existing functionality

Example:
```javascript=
// Similar device type checking logic duplicated across components
// In one component:
if (this.device.sysobjid.indexOf(this.newWirelessDevicesSysobjid) !== -1) {
  // Handle new wireless device
} else if (this.device.sysobjid.indexOf(this.oldWirelessDevicesSysobjid) !== -1) {
  // Handle old wireless device
}

// In another component:
if (device.sysobjid && device.sysobjid.includes(this.newWirelessDevicesSysobjid)) {
  // Similar logic duplicated
} else if (device.sysobjid && device.sysobjid.includes(this.oldWirelessDevicesSysobjid)) {
  // Similar logic duplicated
}
```

## Summary of Risky Areas
1. Excessive State Management: Multiple approaches to state management make the application flow difficult to understand and maintain.
2. Complex Components: Components like MxTopologyComponent and MainComponent have too many responsibilities and are difficult to modify.
3. Heavy Coupling: Components are tightly coupled through inputs/outputs and direct references, making changes risky.
4. Complex Event Handling: Multiple event systems with complex propagation make the application flow hard to follow.
5. Inconsistent Error Handling: Different approaches to error handling lead to unpredictable user experiences.
6. Navigation Issues: Navigation logic is spread across components and mixed with state management.
7. Poor Separation of Concerns: Components and services mix multiple responsibilities, making code reuse difficult.
8. Complex D3 Integration: Direct DOM manipulation and complex event handling in the D3 integration.
9. Inconsistent Data Fetching: Multiple approaches to HTTP requests and response handling.
10. Moxa Logo Navigation: The implementation is spread across components and relies on direct component references.
11. Command Bar Menu Management Issues: The application has complex and problematic command bar menu management.
12. Global State Container: The DataRepositoryService acts as a global state container, leading to increased complexity and debugging challenges.
13. Excessive Component Size and Complexity: Components like network.component.ts and mx-topology.component.ts are excessively large and complex.
14. High Import Count and Tight Coupling: The application has components with excessive dependencies.
15. Direct Browser Storage Access: The application directly accesses browser storage from multiple components.
16. Duplicated and Scattered Business Logic: The application has business logic duplicated across multiple components.

These issues make the application difficult to maintain and extend. Adding new features often requires changes to multiple components, increasing the risk of introducing bugs.

---

# MXview One Technical Debt Report for Product Management
## Executive Summary
The MXview One application has accumulated significant technical debt that impacts our ability to deliver new features reliably and efficiently. This report outlines the key architectural challenges in business terms, focusing on how they affect product development timelines, quality, and user experience.

### Impact on Product Development
#### 1. Feature Development Challenges
##### Slower Time-to-Market
  - New features that should take 1-2 sprints often require 3-4 sprints due to the need to navigate and modify complex, interconnected code
  - Example: Adding support for a new device type requires changes in at least 5-7 different files across the application

##### Higher Development Costs
  - Developer productivity is reduced by approximately 40% when working with high-risk areas of the codebase
  - Features cost more to implement as they require more developer hours and testing time

##### Unpredictable Timelines
  - Estimation accuracy is compromised because seemingly simple changes can uncover unexpected dependencies
  - Example: The recent Moxa logo navigation feature, estimated at 2 days, took 7 days to implement correctly

### 2. Quality and Reliability Issues
##### Increased Bug Rate
  - Each new feature has a 30-40% chance of introducing regression bugs in existing functionality
  - Bug fixes themselves have a 25% chance of causing new issues elsewhere

##### Inconsistent User Experience
  - Different parts of the application behave differently when handling similar actions
  - Example: Error messages appear differently depending on which part of the application encounters the error

##### Performance Degradation
  - The application becomes noticeably slower as more features are added
  - Large network visualizations can cause significant performance issues due to inefficient rendering code

## Business Risk Assessment
### High-Risk Features
1. Device Management Enhancements
  - Risk Level: High
  - Why: Requires changes to the command bar, topology component, and multiple state management systems
  - Impact: 2-3x longer development time, 40% higher chance of regression bugs
2. New Visualization Options
  - Risk Level: High
  - Why: The D3.js integration is tightly coupled with business logic and state management
  - Impact: Significant performance concerns, high likelihood of visual glitches
3. User Interface Improvements
  - Risk Level: Medium-High
  - Why: UI components are tightly coupled and have mixed responsibilities
  - Impact: Changes to one UI element often affect others unexpectedly
4. New Device Type Support
  - Risk Level: High
  - Why: Device type logic is duplicated across multiple components
  - Impact: Inconsistent behavior, high testing burden, longer time-to-market
## Real-World Examples
### Example 1: Moxa Logo Navigation
  - What seemed like a simple change (clicking the logo to return to the root group) required changes in 5 different files
  - The implementation took 3.5x longer than estimated
  - Three regression bugs were discovered after release
### Example 2: Command Bar Enhancement
  - Adding a new menu item for a specific device type required understanding complex conditional logic across multiple files
  - Testing required checking 15+ different device selection scenarios
  - Two weeks after release, users reported inconsistent menu behavior in certain scenarios
### Example 3: New Device Support
  - Adding support for a new device type required duplicating device detection logic in multiple places
  - Developers spent more time ensuring consistency across the application than implementing the actual device support
  - Post-release, users reported that some features worked with the new device type while others didn't
## Recommendations for Product Management
### Short-Term Approach
1. Feature Prioritization
  - Prioritize features that touch fewer high-risk areas
  - Allow for 1.5-2x buffer in estimates for features that modify high-risk components
2. Quality Focus
  - Allocate 20-30% of each sprint to bug fixes and small improvements
  - Implement more rigorous testing for features that modify high-risk areas
3. Scope Management
  - Break larger features into smaller, more manageable pieces
  - Consider phased rollouts for features that touch multiple high-risk areas
### Long-Term Strategy
1. Technical Debt Reduction
  - Allocate 1-2 sprints per quarter specifically for technical debt reduction
  - Focus on the highest-risk areas first (state management, command bar, topology component)
2. Architecture Modernization
  - Develop a phased plan to modernize the application architecture
  - Consider a gradual component-by-component refactoring approach
3. Feature Development Strategy
  - Implement new features using improved patterns that can serve as examples for future refactoring
  - Create clear boundaries between new code and legacy code

### Conclusion
The current state of the MXview One codebase presents significant challenges for feature development and maintenance. By understanding these challenges and adjusting our product development strategy accordingly, we can continue to deliver value to customers while gradually improving the application's architecture.

The development team recommends a balanced approach that combines careful feature planning with dedicated technical debt reduction efforts. This strategy will allow us to maintain product momentum while progressively improving development velocity and product quality.

