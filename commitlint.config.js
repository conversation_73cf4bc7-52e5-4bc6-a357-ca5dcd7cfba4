module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    'type-enum': [
      2,
      'always',
      ['build', 'chore', 'ci', 'docs', 'feat', 'fix', 'perf', 'refactor', 'revert', 'style', 'test'],
    ],
    'scope-enum': [
      2,
      'always',
      [
        'act-web',
        'awk-web',
        'mrc-quicklink',
        'mxsecurity-web',
        'mx-ros-web',
        'janus',
        'zeus',
        'switch',
        'rks-g4000',
        'rks-g4000-l3',
        'mds-g4000',
        'mds-g4000-l3',
        'mds-g4000-4xgs',
        'mds-g4000-l3-4xgs',
        'eds-g4000',
        'tn-4500b',
        '65m-5000m',
        'lib',
        'project-core',
        'release',
        'wac-web',
        'mxview-web',
      ],
    ],
    'body-max-line-length': [0, 'always', 220],
  },
};
