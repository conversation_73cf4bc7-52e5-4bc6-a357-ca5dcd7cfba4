import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges
} from '@angular/core';
import {
  AbstractControl,
  FormArray,
  FormControl,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators
} from '@angular/forms';
import { MatLegacyAutocomplete as MatAutocomplete } from '@angular/material/legacy-autocomplete';
import { MatLegacySnackBar as MatSnackBar } from '@angular/material/legacy-snack-bar';

import { TranslateService } from '@ngx-translate/core';
import { cloneDeep, forEach, includes, remove } from 'lodash-es';
import { Observable, map, startWith } from 'rxjs';

import { ApiClientName } from '@mrc-quicklink/shared/def/client.def';
import {
  ApiDeviceRule,
  DeviceRuleType,
  DeviceType,
  HealthCheckType,
  ModelType,
  NetworkType
} from '@mrc-quicklink/shared/def/gateway.def';
import { ClientService } from '@mrc-quicklink/shared/service-api/client.service';
import { DeviceRuleItem, GatewayService } from '@mrc-quicklink/shared/service-api/gateway.service';
import { AuthService } from '@mrc-quicklink/shared/service/auth.service';
import { UtilsService } from '@mrc-quicklink/shared/service/utils.service';
import { ValidatorFunction, ValidatorPattern } from '@mrc-quicklink/shared/validators';

export type DeviceFormValueType = Partial<{
  name: string;
  type: DeviceType;
  ip: {
    virtualIp: number;
    ip: string;
    natIp: string;
  };
  mac: string;
  healthCheckType: HealthCheckType;
  pingInterval: number;
  port: number;
  clientList: ClientValueType[]; // only used on submit
}>;

export type DevFormSettingDataType = {
  model: ModelType;
  autoMapping: boolean;
  scenario: NetworkType;
  gwVpnIp: string;
  gwDeviceNames: string[];
  gwDeviceNetwork: number[];
  formData: DeviceFormValueType;
  deviceRules: ApiDeviceRule[];
};

enum DeviceFormtTab {
  Device = 0,
  Rules
}

type ClientValueType = {
  protocolSet: DeviceRuleType;
  protocol: string;
  port: string;
  clientInput: string;
  clients: string[];
};

type ClipboardType = {
  protocolSet: DeviceRuleType;
  protocol: string;
  port: string;
  clients: string[];
};

enum TrProtocol {
  TCP = 'TCP',
  UDP = 'UDP',
  ICMP = 'ICMP'
}

@Component({
  selector: 'app-device-form',
  templateUrl: './device-form.component.html',
  styleUrls: ['./device-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DeviceFormComponent implements OnChanges, OnInit {
  @Input() devFormSettingData: DevFormSettingDataType = {
    model: ModelType.MRC_1002_GW,
    autoMapping: false,
    scenario: NetworkType.Wired,
    gwVpnIp: '',
    gwDeviceNames: [],
    gwDeviceNetwork: [],
    formData: null,
    deviceRules: []
  };
  @Input() markAsTouched = false;
  @Input() gatewayGroupUuid = '';
  @Output() valueChanges: EventEmitter<{ invalid: boolean; value: typeof this.deviceForm.value }> = new EventEmitter();

  deviceForm = new FormGroup({
    name: new FormControl('', [
      Validators.required,
      Validators.maxLength(64),
      Validators.pattern(ValidatorPattern.StrLevel_3),
      ValidatorFunction.nameDuplicate(this.devFormSettingData.gwDeviceNames)
    ]),
    type: new FormControl(DeviceType.IP_Device, Validators.required),
    ip: new FormGroup({
      virtualIp: new FormControl<number>(null, [Validators.required]),
      ip: new FormControl('', [Validators.required, Validators.pattern(ValidatorPattern.Ip)]),
      natIp: new FormControl('', Validators.pattern(ValidatorPattern.Ip))
    }),
    mac: new FormControl('', [Validators.required, Validators.pattern(ValidatorPattern.Mac)]),
    healthCheckType: new FormControl(HealthCheckType.Ping_Check, Validators.required),
    pingInterval: new FormControl(10, [
      Validators.required,
      Validators.pattern(ValidatorPattern.NumberOnly),
      Validators.min(1),
      Validators.max(86400)
    ]),
    port: new FormControl<number>(null, Validators.required),
    clientList: new FormArray([])
  });

  tabSelectedIndex = DeviceFormtTab.Device;
  filterClients: Observable<ApiClientName[]>[] = [];
  ports: number[];
  canUseSerial = false;
  vpnArray: { ip: string; value: number; disable: boolean }[] = [];
  copied = false;

  readonly RuleList: DeviceRuleItem[] = [];

  private _clipboard: ClipboardType = {
    protocolSet: null,
    protocol: '',
    port: '',
    clients: []
  };
  private _ClientsName: ApiClientName[] = [];
  private readonly _MaxDevice = 25;

  private readonly ClientForm = new FormGroup({
    protocolSet: new FormControl(DeviceRuleType.All, Validators.required),
    protocol: new FormControl({ value: '', disabled: true }),
    port: new FormControl('', [
      Validators.required,
      Validators.pattern(ValidatorPattern.NumberWithCommaAndDash),
      this.checkPortValidator()
    ]),
    clientInput: new FormControl(''),
    clients: new FormControl([])
  });

  constructor(
    public utils: UtilsService,
    private snackBar: MatSnackBar,
    private translate: TranslateService,
    private gwService: GatewayService,
    private clientService: ClientService,
    private auth: AuthService,
    private cdr: ChangeDetectorRef
  ) {
    this.RuleList = this.gwService.ruleList;
    this._ClientsName = this.clientService.clientsName;
  }

  get clientListFormArray() {
    return this.deviceForm.get('clientList') as FormArray<typeof this.ClientForm>;
  }
  get DeviceType() {
    return DeviceType;
  }
  get HealthCheckType() {
    return HealthCheckType;
  }
  get TrProtocol() {
    return TrProtocol;
  }
  get ModelType() {
    return ModelType;
  }
  get DeviceRuleType() {
    return DeviceRuleType;
  }

  ngOnInit(): void {
    this.deviceForm.valueChanges.subscribe(() => {
      this.valueChanges.emit({
        invalid: this.deviceForm.invalid,
        value: this.deviceForm.getRawValue()
      });
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (!changes) {
      return;
    }

    if (changes['devFormSettingData']) {
      const data: DevFormSettingDataType = changes['devFormSettingData'].currentValue;

      // vpnArray
      this.vpnArray = [];
      const ip = data.gwVpnIp.split('.');
      for (let i = 1; i <= this._MaxDevice; i++) {
        const used = data.gwDeviceNetwork.find(n => n === i);
        this.vpnArray.push({
          ip: ip[0] + '.' + ip[1] + '.' + ip[2] + '.' + (parseInt(ip[3]) + i),
          value: i,
          disable: used ? true : false
        });
      }

      if (data.formData) {
        // edit
        this.deviceForm.patchValue(data.formData);
        this.vpnArray.find(v => v.value === data.formData.ip.virtualIp).disable = false;
      } else {
        this.deviceForm.get('ip.virtualIp').setValue(this.vpnArray.find(v => !v.disable).value);
      }

      // model
      switch (data.model) {
        case ModelType.OnCell_G4308_GW:
          this.ports = Array.from({ length: 8 }, (_, i) => i + 1);
          break;
        case ModelType.MRC_1002_GW:
          this.ports = data.scenario === NetworkType.Wired ? [2] : Array.from({ length: 2 }, (_, i) => i + 1);
          break;
        case ModelType.OnCell_G4302_GW:
        case ModelType.OnCell_3120_GW:
          this.ports = Array.from({ length: 2 }, (_, i) => i + 1);
          break;
      }

      this.canUseSerial =
        data.model === ModelType.OnCell_G4302_GW ||
        data.model === ModelType.OnCell_G4308_GW ||
        data.model === ModelType.OnCell_3120_GW;

      // gwDeviceNames
      this.deviceForm
        .get('name')
        .setValidators([
          Validators.required,
          Validators.maxLength(64),
          Validators.pattern(ValidatorPattern.StrLevel_3),
          ValidatorFunction.nameDuplicate(data.gwDeviceNames, data.formData?.name)
        ]);
      this.deviceForm.get('name').updateValueAndValidity();

      // rule
      if (data.deviceRules.length > 0) {
        forEach(data.deviceRules, (rule, i) => {
          this.addClientItem();
          const protocolSet = this.getProtocolSet(rule.protocol, rule.port);
          this.onAppProtocolChange(protocolSet, i);
          this.clientListFormArray.at(i).patchValue({
            protocolSet: protocolSet,
            port: rule.port
          });
          forEach(rule.clients, c => {
            this.addClient(c.uuid, i);
          });
        });
      } else {
        this.addClientItem();
      }

      // update
      this.handleDeviceTypeChanges(this.deviceForm.value.type);
      this.onHealthCheckChange(this.deviceForm.get('healthCheckType').value);
      this.valueChanges.emit({
        invalid: this.deviceForm.invalid,
        value: this.deviceForm.getRawValue()
      });

      // clipboard
      const clipboard: ClipboardType = JSON.parse(localStorage.getItem('mrc_rule_client'));
      if (clipboard) {
        this._clipboard = clipboard;
        this.copied = true;
      }
    }
  }

  getClientsName(uuid: string): string {
    return this._ClientsName.find(c => c.uuid === uuid).name;
  }

  handleDeviceTypeChanges(type: DeviceType): void {
    switch (type) {
      case DeviceType.IP_Device:
        this.deviceForm.get('ip').enable();
        this.deviceForm.get('mac').disable();

        if (
          this.devFormSettingData.model === ModelType.MRC_1002_GW &&
          this.devFormSettingData.scenario === NetworkType.Wired
        ) {
          this.deviceForm.get('ip.natIp').enable();
        } else {
          this.deviceForm.get('ip.natIp').disable();
        }

        if (this.devFormSettingData.autoMapping) {
          this.deviceForm.get('ip.virtualIp').enable();
        } else {
          this.deviceForm.get('ip.virtualIp').disable();
        }
        break;
      case DeviceType.L2_Device:
        this.deviceForm.get('ip').disable();
        this.deviceForm.get('mac').enable();
        break;
      case DeviceType.Serial_Device:
        this.deviceForm.get('ip').disable();
        this.deviceForm.get('mac').disable();
        break;
    }

    if (this.devFormSettingData.model === ModelType.OnCell_3120_GW && type === DeviceType.Serial_Device) {
      this.deviceForm.get('healthCheckType').disable();
    } else {
      this.deviceForm.get('healthCheckType').enable();
    }
  }

  updateHealthCheckValues(type: DeviceType): void {
    switch (type) {
      case DeviceType.IP_Device:
        this.deviceForm.get('healthCheckType').setValue(HealthCheckType.Ping_Check);
        this.deviceForm.get('pingInterval').setValue(10);
        break;
      case DeviceType.L2_Device:
        this.deviceForm.get('healthCheckType').setValue(HealthCheckType.Port_Link);
        break;
      case DeviceType.Serial_Device:
        if (this.devFormSettingData.model === ModelType.OnCell_3120_GW) {
          this.deviceForm.get('healthCheckType').setValue(HealthCheckType.Disable);
        } else {
          this.deviceForm.get('healthCheckType').setValue(HealthCheckType.Serial);
        }
        break;
    }
  }

  onTypeChange(type: DeviceType): void {
    this.handleDeviceTypeChanges(type);
    this.updateHealthCheckValues(type);
    this.onHealthCheckChange(this.deviceForm.get('healthCheckType').value);
    this.resetClientList();
  }

  onHealthCheckChange(type: HealthCheckType): void {
    switch (type) {
      case HealthCheckType.Ping_Check:
        this.deviceForm.get('pingInterval').enable();
        this.deviceForm.get('port').disable();
        break;
      case HealthCheckType.Port_Link:
        this.deviceForm.get('pingInterval').disable();
        this.deviceForm.get('port').enable();
        break;
      case HealthCheckType.Serial:
      case HealthCheckType.Disable:
        this.deviceForm.get('pingInterval').disable();
        this.deviceForm.get('port').disable();
        break;
    }
  }

  addClientItem(): void {
    const index = this.clientListFormArray.length;
    this.clientListFormArray.push(cloneDeep(this.ClientForm));
    this.onAppProtocolChange(DeviceRuleType.All, index);
    this.filterClients[index] = this.clientListFormArray
      .at(index)
      .get('clientInput')
      .valueChanges.pipe(
        startWith(null),
        map((value: string | number) => this._unselectedFilterClientList(index, value))
      );
    if (this.deviceForm.get('type').value !== DeviceType.IP_Device) {
      this.clientListFormArray.at(index).get('protocolSet').disable();
    }
  }

  pasteClientItem(): void {
    const index = this.clientListFormArray.length;
    const clientForm = cloneDeep(this.ClientForm);
    clientForm.patchValue(this._clipboard);
    this.clientListFormArray.push(clientForm);
    this.onAppProtocolChange(this._clipboard.protocolSet, index);
    this.filterClients[index] = this.clientListFormArray
      .at(index)
      .get('clientInput')
      .valueChanges.pipe(
        startWith(null),
        map((value: string | number) => this._unselectedFilterClientList(index, value))
      );
  }

  removeClientItem(index: number): void {
    if (this.clientListFormArray.value.length > 1) {
      this.clientListFormArray.removeAt(index);
      this.filterClients.splice(index, 1);
    }
  }

  copyClientItem(index: number): void {
    if (this.clientListFormArray.at(index).invalid) {
      this.clientListFormArray.at(index).markAllAsTouched();
      this.snackBar.open(this.translate.instant('response_handler.res_copy_fail'), '', {
        duration: 3000,
        panelClass: ['error']
      });
      return;
    }
    this.copied = true;
    this._clipboard = {
      protocolSet: this.clientListFormArray.at(index).get('protocolSet').value,
      protocol: this.clientListFormArray.at(index).get('protocol').value,
      port: this.clientListFormArray.at(index).get('port').value,
      clients: this.clientListFormArray.at(index).get('clients').value
    };
    localStorage.setItem('mrc_rule_client', JSON.stringify(this._clipboard));
    this.snackBar.open(this.translate.instant('response_handler.res_copy'), '', {
      duration: 3000
    });
  }

  addClient(clientUuid: string, index: number, clientInput?: HTMLInputElement): void {
    const client = this._ClientsName.find(c => c.uuid === clientUuid);
    const control = this.clientListFormArray.at(index).get('clients');
    const newClientList: string[] = cloneDeep(control.value);

    newClientList.push(client.uuid);
    control.setValue(newClientList);
    this.clientListFormArray.at(index).get('clientInput').setValue('');
    if (clientInput) {
      clientInput.value = '';
    }
  }

  removeClient(clientId: string, index: number): void {
    const control = this.clientListFormArray.at(index).get('clients');
    const newClientList: string[] = cloneDeep(control.value);
    remove(newClientList, uuid => uuid === clientId);
    control.setValue(newClientList);
    this.clientListFormArray.at(index).get('clientInput').setValue('');
  }

  onAppProtocolChange(protocolSetType: DeviceRuleType, controlIdx: number): void {
    const protocolSet = this.RuleList.find(r => r.name === protocolSetType);
    this.clientListFormArray.at(controlIdx).patchValue({
      protocol: protocolSet.protocol,
      port: protocolSet.port
    });
    if (protocolSet.port === '') {
      this.clientListFormArray.at(controlIdx).get('port').enable();
    } else {
      this.clientListFormArray.at(controlIdx).get('port').disable();
    }
  }

  clearClientInput(controlIdx: number, auto: MatAutocomplete, clientInput: HTMLInputElement): void {
    if (auto.isOpen) {
      return;
    }
    this.clientListFormArray.at(controlIdx).get('clientInput').setValue('');
    if (clientInput) {
      clientInput.value = '';
    }
  }

  markAllAsTouched(): Observable<boolean> {
    return new Observable(observe => {
      this.deviceForm.markAllAsTouched();
      this.clientListFormArray.markAllAsTouched();
      if (this.clientListFormArray.invalid) {
        observe.next(this.tabSelectedIndex !== DeviceFormtTab.Rules);
        this.tabSelectedIndex = DeviceFormtTab.Rules;
      } else {
        observe.next(this.tabSelectedIndex !== DeviceFormtTab.Device);
        this.tabSelectedIndex = DeviceFormtTab.Device;
      }
      this.cdr.markForCheck();
    });
  }

  isDisabledAddRule(): boolean {
    return (
      this.clientListFormArray.length >= this.auth.serverResource.deviceMaxRule ||
      this.deviceForm.get('type')?.value === DeviceType.L2_Device
    );
  }

  private _unselectedFilterClientList(index: number, value: string | number): ApiClientName[] {
    const selected: string[] = this.clientListFormArray.at(index).get('clients').value;
    const unselected = this.gatewayGroupUuid
      ? cloneDeep(this._ClientsName).filter(client => client.groupUuid === this.gatewayGroupUuid)
      : cloneDeep(this._ClientsName);
    forEach(selected, selectedClient => {
      remove(unselected, c => c.uuid === selectedClient);
    });
    const _filter = (stringValue: string) => {
      const filterValue = stringValue.toLowerCase();
      return unselected.filter(client => client.name.toLowerCase().includes(filterValue));
    };
    return typeof value === 'string' ? _filter(value) : unselected;
  }

  private getProtocolSet(protocol: string, port: string): DeviceRuleType {
    const protocolSet = this.RuleList.find(rule => rule.protocol === protocol && rule.port === port);
    if (protocolSet) {
      return protocolSet.name;
    } else if (protocol === TrProtocol.TCP) {
      return DeviceRuleType.Custom_TCP;
    } else {
      return DeviceRuleType.Custom_UDP;
    }
  }

  private checkPortValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      // Parse format like '2,3,5-10'
      const portValue = control.value.split(',');
      let error = null;
      // todo
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const errorItem = portValue.find(portItem => {
        if (includes(portItem, '-')) {
          const portRange = portItem.split('-').map(Number);

          if (portRange[0] >= portRange[1]) {
            // range is invalid
            error = { pattern: true };
            return true;
          } else if (portRange[0] < 1024 || portRange[1] > 65535) {
            // range is invalid
            error = { range: true };
            return true;
          }
        } else if (+portItem < 1024 || +portItem > 65535) {
          // range is invalid
          error = { range: true };
          return true;
        }
        return false;
      });

      return error;
    };
  }

  private resetClientList(): void {
    this.clientListFormArray.clear();
    this.addClientItem();
  }
}
