import { Injectable } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SafeHtml } from '@angular/platform-browser';

import { selectProjectState, selectSystemInfo } from '@act-web/stores/reducers/app.reducer';
import { selectAuthInfo } from '@act-web/stores/selectors/auth.selector';
import { selectDeviceProfile } from '@act-web/stores/selectors/deviceProfile.selector';
import { ComponentStore, tapResponse } from '@ngrx/component-store';
import { Store, select } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { Observable, filter, switchMap, take } from 'rxjs';

import { ProjectBaseStore } from '@act-web/pages/project-base/project-base.store';
import { ProjectUtilsService } from '@act-web/pages/project/project-utils.service';
import { ProjectAction } from '@act-web/pages/project/project.def';
import { ProjectStore } from '@act-web/pages/project/project.store';
import {
  ActLicenseProfile,
  AIActionType,
  AIResponse,
  AIResponseAction,
  AIResponseButton,
  AIResponseType
} from '@act-web/shared/act-type.def';
import { CNCService } from '@act-web/shared/service/cnc-services';
import { StatusCode } from '@act-web/shared/service/server-status-code.def';
import { ErrorRes, OpCode, SuccessRes } from '@act-web/shared/service/socket/web-socket.def';
import { WebSocketService } from '@act-web/shared/service/socket/web-socket.service';

export interface Message {
  role: AIResponseType;
  text: string | SafeHtml;
}

export interface ActMessage extends Message {
  icon: string;
  actionList?: ActActionMessage[];
  buttonList: AIResponseButton[];
  toolbar: boolean;
  isFed: boolean;
  id: string;
}

interface ActActionMessage {
  icon: string;
  action: void;
}

export interface ChatRoomState {
  message: (Message | ActMessage)[];
  sessionId: string;
  inputDisabled: boolean;
  nextOffset: string;
  historyMessageAllLoaded: boolean;
  shouldScrollBottom: boolean;
  l2Family: Set<string>;
  modelNameList: string[];
  isLoading: boolean;
}

const defaultState: ChatRoomState = {
  message: [],
  sessionId: '',
  inputDisabled: false,
  nextOffset: '',
  shouldScrollBottom: true,
  historyMessageAllLoaded: false,
  l2Family: null,
  modelNameList: [],
  isLoading: false
};

@Injectable()
export class ChatRoomComponentStore extends ComponentStore<ChatRoomState> {
  intelligentEndpoint: string;
  constructor(
    private _store: Store,
    private _sanitizer: DomSanitizer,
    private cncService: CNCService,
    private projectUtilsService: ProjectUtilsService,
    private projectBaseStore: ProjectBaseStore,
    private projectComponentStore: ProjectStore,
    private _translateService: TranslateService,
    private _websocketService: WebSocketService
  ) {
    super(defaultState);
    this._store.pipe(select(selectAuthInfo)).subscribe(authInfo => {
      this.updateSessionId(authInfo.userId.toString());
    });
    this._store.pipe(select(selectSystemInfo)).subscribe(systemInfo => {
      this.intelligentEndpoint = systemInfo.intelligentEndpoint;
    });

    this._store.pipe(select(selectProjectState)).subscribe(projectState => {
      const projectProfile = projectState.projectProfile;
      if (projectProfile) {
        this.updateModelList(projectProfile);
      }
    });

    this._websocketService
      .receive()
      .pipe(
        filter(
          jsonResult =>
            jsonResult.OpCode === OpCode.START_INTELLIGENT_REQUEST ||
            jsonResult.OpCode === OpCode.START_INTELLIGENT_UPLOAD
        )
      )
      .subscribe({
        next: async (jsonResult: SuccessRes | ErrorRes) => {
          if (jsonResult.StatusCode === StatusCode.RUNNING) {
            const successJsonResult = jsonResult as SuccessRes;
            const data: { response: AIResponse; status: { code: number; message: string } } = successJsonResult.Data;
            this.messageLoaded(data.response);
            this.updateInputDisabled(true);
            this.updateLoading(true);
          } else if (jsonResult.StatusCode === StatusCode.FINISHED) {
            this.updateInputDisabled(false);
            this.updateLoading(false);
          }
        }
      });
  }

  updateModelList(projectProfile: ActLicenseProfile) {
    this._store.pipe(select(selectDeviceProfile)).subscribe(deviceProfiles => {
      const profiles = this.getProfilesByLicenseType(deviceProfiles, projectProfile);
      const l2Family = new Set<string>();
      const modelNameList = [];
      profiles.forEach(deviceProfile => {
        modelNameList.push(deviceProfile.modelName);
        const l2FamilyName =
          'l2Family' in deviceProfile ? deviceProfile.l2Family : deviceProfile.modelName.split('-')[0];
        l2Family.add(l2FamilyName);
      });
      // remove empty string
      l2Family.delete('');
      this.updateL2Family(l2Family);
      this.updateModelNameList(modelNameList);
    });
  }

  getProfilesByLicenseType(deviceProfiles: any, projectProfile: ActLicenseProfile): any[] {
    switch (projectProfile) {
      case ActLicenseProfile.QuickModelSelection:
        return deviceProfiles.generalProfile;
      case ActLicenseProfile.Foxboro:
        return [
          ...deviceProfiles.deviceProfiles.filter(d => d.profiles.includes(ActLicenseProfile.Foxboro)),
          ...deviceProfiles.foxboroProfile
        ];
      case ActLicenseProfile.SelfPlanning:
      default:
        return [
          ...deviceProfiles.deviceProfiles.filter(d => d.profiles.includes(ActLicenseProfile.SelfPlanning)),
          ...deviceProfiles.selfPlanningProfile
        ];
    }
  }

  readonly selectedSessionId$ = this.select(({ sessionId }) => sessionId);

  userInput(text: string, autoSend: boolean, projectMode: string) {
    // disable user input
    this.updateInputDisabled(true);
    let sessionId = '';
    this.selectedSessionId$.pipe(take(1)).subscribe(id => {
      sessionId = id;
    });

    this._websocketService.send(OpCode.START_INTELLIGENT_REQUEST, {
      IntelligentRequest: {
        Text: text,
        SessionId: sessionId,
        AutoSend: autoSend,
        ProjectMode: projectMode
      }
    });
    this.updateLoading(true);
  }

  readonly loadHistoryMessage = this.effect((params$: Observable<{ projectMode: string }>) =>
    params$.pipe(
      switchMap(d => {
        let sessionId = '';
        this.selectedSessionId$.pipe(take(1)).subscribe(id => {
          sessionId = id;
        });
        return this.cncService
          .postIntelligentHistory({ SessionId: sessionId, Offset: this.state().nextOffset, ProjectMode: d.projectMode })
          .pipe(
            tapResponse(
              res => {
                this._updateHistoryOffset(res.response.nextOffset);
                this.historyMessageLoaded(res.response.messages);
              },
              () => {
                this.messageFailed(this._translateService.instant('response_handler.ai_unavailable'));
              }
            )
          );
      })
    )
  );

  readonly userFeedback = this.effect((params$: Observable<{ goodFeedback: boolean; id: string }>) =>
    params$.pipe(
      switchMap(param => {
        return this.cncService.intelligentFeedback({ Id: param.id, Positive: param.goodFeedback }).pipe(
          tapResponse(() => {
            this.feedBackLoaded(param.id);
          }, undefined)
        );
      })
    )
  );

  readonly updateInputDisabled = this.updater((state, inputDisabled: boolean) => {
    return {
      ...state,
      inputDisabled: inputDisabled
    };
  });

  readonly updateL2Family = this.updater((state, l2Family: Set<string>) => {
    return {
      ...state,
      l2Family: l2Family
    };
  });

  readonly updateModelNameList = this.updater((state, modelNameList: string[]) => {
    return {
      ...state,
      modelNameList: modelNameList
    };
  });

  readonly messageLoaded = this.updater((state, input: AIResponse) => {
    let message;
    if (input.role === 'Assistant') {
      input.reply.actions.forEach(action => {
        this._parseActionMessage(action);
      });
      message = {
        buttonList: input.reply.buttons,
        toolbar: input.reply.toolbar,
        isFed: false,
        id: input.id,
        text: this._sanitizer.bypassSecurityTrustHtml(input.reply.reply),
        icon: 'assets/img/icon/main-icon.png',
        role: input.role
      } as ActMessage;
    } else if (input.role === 'User') {
      message = {
        text: input.input,
        role: input.role
      } as Message;
    } else if (input.role === 'System') {
      message = {
        text: this._sanitizer.bypassSecurityTrustHtml(input.input),
        role: input.role
      } as Message;
    }
    return {
      ...state,
      message: [...state.message, message]
    };
  });

  readonly messageFailed = this.updater((state, input: string) => {
    const message: ActMessage = {
      role: 'Assistant',
      buttonList: [],
      toolbar: false,
      isFed: false,
      id: 'error',
      text: this._sanitizer.bypassSecurityTrustHtml(input),
      icon: 'assets/img/icon/main-icon.png'
    };
    return {
      ...state,
      message: [...state.message, message]
    };
  });

  readonly historyMessageLoaded = this.updater((state, messages: AIResponse[]) => {
    const historyMessages = messages.map(message => {
      if (message.role === 'User') {
        // user message
        return {
          text: message.input,
          role: message.role
        } as Message;
      } else if (message.role === 'System') {
        return {
          text: this._sanitizer.bypassSecurityTrustHtml(message.input),
          role: message.role
        } as Message;
      } else {
        // AI message
        return {
          icon: 'assets/img/icon/main-icon.png',
          buttonList: message.reply.buttons,
          toolbar: message.reply.toolbar,
          isFed: false,
          id: message.id,
          text: this._sanitizer.bypassSecurityTrustHtml(message.reply.reply),
          role: message.role
        } as ActMessage;
      }
    });
    historyMessages.reverse();
    return {
      ...state,
      message: [...historyMessages, ...state.message]
    };
  });

  readonly updateLoading = this.updater((state, isLoading: boolean) => {
    return {
      ...state,
      isLoading: isLoading
    };
  });

  readonly feedBackLoaded = this.updater((state, id: string) => {
    const feedBackMessage = state.message.find((message: ActMessage) => message.id === id) as ActMessage;
    feedBackMessage.isFed = true;
    return {
      ...state,
      message: [...state.message]
    };
  });

  readonly updateSessionId = this.updater((state, sessionId: string) => {
    return { ...state, sessionId: sessionId };
  });

  private readonly _updateHistoryOffset = this.updater((state, nextOffset: string) => {
    return { ...state, nextOffset: nextOffset, historyMessageAllLoaded: nextOffset === '' };
  });

  private compute(): void {
    this.projectComponentStore.updateProjectActionStream(ProjectAction.Compute);
  }

  private fitToScreen(): void {
    this.projectComponentStore.updateFitToScreen();
  }

  private _parseActionMessage(input: AIResponseAction) {
    let devices;
    this.projectBaseStore
      .select(state => state.devices)
      .pipe(take(1))
      .subscribe(deviceState => (devices = deviceState));
    switch (input.target) {
      case AIActionType.ExportBOM:
        this.projectUtilsService.generateBOM(devices);
        break;
      case AIActionType.Compute:
        this.compute();
        break;
      case AIActionType.FitToScreen:
        this.fitToScreen();
        break;
    }
  }
}
