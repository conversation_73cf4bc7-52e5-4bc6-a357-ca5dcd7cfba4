import {
  AfterViewChecked,
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
  ViewChild
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, NgForm, ReactiveFormsModule } from '@angular/forms';
import { MatLegacyFormFieldModule as MatFormFieldModule } from '@angular/material/legacy-form-field';
import { MatLegacyInputModule as MatInputModule } from '@angular/material/legacy-input';
import { MatLegacySelectModule as MatSelectModule } from '@angular/material/legacy-select';

import { LetDirective } from '@ngrx/component';
import { TranslateModule } from '@ngx-translate/core';

import { UtilsService } from '@act-web/shared/service/utils.service';

import { LoginPolicyUnit } from '../../models/setting-login-policy.model';

@Component({
  selector: 'app-device-policy-form',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    FormsModule,
    LetDirective,
    MatInputModule,
    MatSelectModule
  ],
  templateUrl: './login-policy-form.component.html',
  styleUrls: ['./login-policy-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LoginPolicyFormComponent implements OnInit, AfterViewChecked {
  @Input() setting: LoginPolicyUnit;
  @Output() changeFormValid = new EventEmitter<boolean>();

  utilsService = inject(UtilsService);
  @ViewChild('form') form!: NgForm;

  ngOnInit() {
    this.setUpForm();
  }

  ngAfterViewChecked(): void {
    this.changeFormValid.emit(!this.form.invalid);
  }

  setUpForm() {
    if (!this.setting.accountLoginFailureLockout) {
      this.setting.accountLoginFailureLockout = false;
    }
    if (this.setting.retryFailureThreshold == null) {
      this.setting.retryFailureThreshold = 5;
    }
    if (this.setting.lockoutDuration == null) {
      this.setting.lockoutDuration = 5;
    }
    if (this.setting.autoLogoutAfter == null) {
      this.setting.autoLogoutAfter = 5;
    }
  }
}
