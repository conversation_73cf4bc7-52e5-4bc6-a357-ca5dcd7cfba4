import {
  ChangeDetectionStrategy,
  Component,
  inject,
  OnInit,
  signal,
  ViewContainerRef,
  WritableSignal
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatLegacyCardModule as MatCardModule } from '@angular/material/legacy-card';
import { MatLegacyDialog as MatDialog } from '@angular/material/legacy-dialog';
import { MatLegacySnackBar as MatSnackBar } from '@angular/material/legacy-snack-bar';
import { MatLegacyTabsModule as MatTabsModule } from '@angular/material/legacy-tabs';
import { MatTabChangeEvent } from '@angular/material/tabs';
import { MatToolbarModule } from '@angular/material/toolbar';

import { GenerateDocService } from '@mx-service/generate-doc.service';
import { FlexLayoutModule } from '@ngbracket/ngx-layout';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { MonitorStore } from '@act-web/pages/monitor/monitor.store';
import { UtilsService } from '@act-web/shared/service/utils.service';

import { SyslogConfigComponent } from '../components/syslog-config/syslog-config.component';
import { SyslogTableComponent } from '../components/syslog-table/syslog-table.component';
import { defaultPageUnit, ExportCsvType, PageUnit, SyslogUnit } from '../models/syslog.model';
import { SyslogStore } from '../stores/syslog.store';

@UntilDestroy()
@Component({
  selector: 'app-snmp-trap',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatTabsModule,
    MatToolbarModule,
    TranslateModule,
    FlexLayoutModule,
    SyslogConfigComponent,
    SyslogTableComponent
  ],
  templateUrl: './syslog.component.html',
  styleUrls: ['./syslog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SyslogComponent implements OnInit {
  store = inject(SyslogStore);
  dialog = inject(MatDialog);
  viewContainerRef = inject(ViewContainerRef);
  monitorStore = inject(MonitorStore);
  translate = inject(TranslateService);
  docService = inject(GenerateDocService);
  utilsService = inject(UtilsService);
  snackBar = inject(MatSnackBar);

  pageUnit: WritableSignal<PageUnit> = signal(defaultPageUnit);
  totalLength: WritableSignal<number> = signal(0);
  syslogList: WritableSignal<SyslogUnit[]> = signal([]);
  allSyslogList: WritableSignal<SyslogUnit[]> = signal([]);

  ngOnInit(): void {
    this.store.getSyslogConfig();
    this.store.getSyslog(this.pageUnit());
    this.store
      .select(state => state.total)
      .pipe(untilDestroyed(this))
      .subscribe(res => {
        this.totalLength.set(res);
      });
    this.store
      .select(state => state.syslogList)
      .pipe(untilDestroyed(this))
      .subscribe(res => {
        this.syslogList.set(res);
      });
    this.store
      .select(state => state.allSyslogList)
      .pipe(untilDestroyed(this))
      .subscribe(res => {
        if (!res || res.length === 0) return;
        this.allSyslogList.set(res);
        this.generateCsv(this.allSyslogList());
        this.store.clearAllSyslogLogList();
      });
  }

  onSelectedTabChange(tabIndex: MatTabChangeEvent) {
    if (tabIndex.index === 1) {
      this.store.getSyslog(this.pageUnit());
    }
  }

  saveSyslogConfig($event: boolean) {
    this.store.saveSyslogConfig($event);
  }

  changePage($event: PageUnit) {
    this.pageUnit.set($event);
    this.store.getSyslog($event);
  }

  filterSyslog($event: PageUnit) {
    this.pageUnit.set($event);
    this.store.getSyslog($event);
  }

  deleteSyslog() {
    this.store.deleteSyslog();
  }

  exportSyslog(payload: { exportCsvType: ExportCsvType; syslogList: SyslogUnit[] }) {
    if (payload.exportCsvType === ExportCsvType.currentPage) {
      this.generateCsv(payload.syslogList);
    } else {
      this.store.exportSyslog({ ...this.pageUnit(), limit: this.totalLength(), offset: 0 });
    }
  }

  generateCsv(logList: SyslogUnit[]) {
    try {
      const tableData = this.createTableData(logList);
      const currentTime = this.utilsService.getDateTimeStringForExport(new Date());
      this.docService.generateDoc('csv', `syslogs_${currentTime}`, tableData);
      this.snackBar.open(this.translate.instant('response_handler.export_success'), '', { duration: 3000 });
    } catch (error) {
      this.snackBar.open(
        this.translate.instant('response_handler.export_fail'),
        this.translate.instant('general.button.close'),
        {
          panelClass: ['error']
        }
      );
    }
  }

  private createTableData(syslogList: SyslogUnit[]) {
    return {
      tableTitle: {
        severity: this.translate.instant('pages.syslog.severity'),
        timestamp: this.translate.instant('pages.syslog.timestamp'),
        syslogTime: this.translate.instant('pages.syslog.syslog_time'),
        ipAddress: this.translate.instant('pages.syslog.ip_address'),
        facility: this.translate.instant('pages.syslog.facility'),
        message: this.translate.instant('pages.syslog.message')
      },
      tableContent: syslogList.map(syslog => ({
        ...syslog,
        severity: syslog.severityDescription,
        timestamp: syslog.timestamp === '' ? '' : this.utilsService.getDateTimeString(new Date(syslog.timestamp)),
        syslogTime:
          syslog.syslogTime === '' ? '' : this.utilsService.getDateTimeString(new Date(syslog.syslogTime)).substring(5),
        ipAddress: syslog.ipaddress,
        facility: syslog.facilityDescription
      }))
    };
  }
}
