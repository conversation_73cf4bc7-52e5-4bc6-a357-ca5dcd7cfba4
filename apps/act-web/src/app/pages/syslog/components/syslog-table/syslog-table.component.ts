import {
  ChangeDetectionStrategy,
  Component,
  Input,
  SimpleChanges,
  ViewChild,
  AfterViewInit,
  OnChanges,
  inject,
  EventEmitter,
  Output,
  WritableSignal,
  signal,
  OnInit
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';

import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { LogService } from '@act-web/shared/service/log.service';
import { UtilsService } from '@act-web/shared/service/utils.service';
import { SharedModule } from '@act-web/shared/shared.module';
import { convertIpToNumber } from '@act-web/shared/util/convertIp';
import { SyslogSeverity } from '@act-web/shared/util/syslog';
import { ValidatorPattern } from '@act-web/shared/validator/validators';

import {
  defaultPageSize,
  defaultPageUnit,
  ExportCsvType,
  PageUnit,
  SyslogPriority,
  SyslogUnit
} from '../../models/syslog.model';

@Component({
  selector: 'app-syslog-table',
  standalone: true,
  imports: [CommonModule, SharedModule, TranslateModule],
  templateUrl: './syslog-table.component.html',
  styleUrls: ['./syslog-table.component.scss'],
  providers: [LogService],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SyslogTableComponent implements OnChanges, OnInit, AfterViewInit {
  @Input() syslogList: SyslogUnit[] = [];
  @Input() total: number;
  @Output() changePage = new EventEmitter<PageUnit>();
  @Output() filterSyslog = new EventEmitter<PageUnit>();
  @Output() exportSyslog = new EventEmitter<{ exportCsvType: ExportCsvType; syslogList: SyslogUnit[] }>();
  @Output() deleteSyslog = new EventEmitter<null>();

  translateService = inject(TranslateService);
  logService = inject(LogService);
  utilsService = inject(UtilsService);
  @ViewChild(MatSort) sort: MatSort;
  @ViewChild('tablePaginator') tablePaginator: MatPaginator;
  dataSource = new MatTableDataSource<SyslogUnit>();
  displayedColumns: string[] = ['severity', 'timestamp', 'syslog-time', 'syslog-ip', 'facility', 'message'];
  pageSize = defaultPageSize;
  pageIndex = 0;
  totalLength: WritableSignal<number> = signal(0);
  ExportCsvType = ExportCsvType;

  fb = inject(FormBuilder);
  queryForm: FormGroup;
  facilityItems = [];
  priorityItems = [];
  severityItems = [];
  HourItems = [];
  MinItems = [];

  constructor() {
    this.queryForm = this.fb.group({
      queryIPAddress: ['', Validators.compose([Validators.pattern(ValidatorPattern.IPADDR_REGEX)])],
      querySeverity: [''],
      queryFacility: [''],
      queryThresholdType: [],
      queryFromTime: [''],
      queryToTime: [''],
      queryFromHour: [''],
      queryFromMin: [''],
      queryToHour: [''],
      queryToMin: ['']
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['syslogList']?.currentValue) {
      this.dataSource.data = changes['syslogList']?.currentValue;
    }

    if (changes['total']?.currentValue !== undefined) {
      this.totalLength.set(this.total);
    }
  }

  ngOnInit(): void {
    this.initAllSelectItems();
  }

  initAllSelectItems(): void {
    this.facilityItems = this.generateSelectItems(24, i => this.utilsService.getSyslogFacility(i));
    this.priorityItems = this.generateSelectItems(3, i => this.getPriority(i));
    this.severityItems = this.generateSelectItems(8, i => this.utilsService.getSyslogSeverity(i));
    this.HourItems = this.generateSimpleNumberArray(24);
    this.MinItems = this.generateSimpleNumberArray(60);
    this.queryForm.controls['queryThresholdType'].setValue(0);
  }

  generateSelectItems(length: number, labelFn: (index: number) => string): { value: number; label: string }[] {
    return Array.from({ length }, (_, index) => ({
      value: index,
      label: labelFn(index)
    }));
  }

  generateSimpleNumberArray(length: number): number[] {
    return Array.from({ length }, (_, index) => index);
  }

  ngAfterViewInit() {
    this.setupTableSetting();
  }

  setupTableSetting() {
    this.dataSource.sort = this.sort;
    this.dataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'syslog-ip':
          return convertIpToNumber(item['ipaddress']);
        case 'syslog-time':
          return item['syslogTime'];
        default:
          return item[property];
      }
    };
    this.dataSource.filterPredicate = (data: SyslogUnit, filter: string): boolean => {
      return (
        String(data.severityDescription).toLowerCase().indexOf(filter) !== -1 ||
        String(data.facilityDescription).toLowerCase().indexOf(filter) !== -1 ||
        String(this.getFormatTime(data.timestamp)).toLowerCase().indexOf(filter) !== -1 ||
        String(this.getFormatTime(data.syslogTime)).toLowerCase().indexOf(filter) !== -1 ||
        String(data.ipaddress).toLowerCase().indexOf(filter) !== -1 ||
        String(data.message).toLowerCase().indexOf(filter) !== -1
      );
    };
  }

  updateFilter(dataSource: MatTableDataSource<any>, filterValue: string): void {
    dataSource.filter = filterValue.trim().toLowerCase();
  }

  onPageChange($event: { previousPageIndex: number; pageIndex: number; pageSize: number; length: number }): void {
    this.pageSize = $event.pageSize;
    this.pageIndex = $event.pageIndex;

    const limit = $event.pageSize;
    const offset = $event.pageIndex * $event.pageSize;
    const query = this.getFilter();
    this.changePage.emit({ ...query, limit, offset });
  }

  onExport(exportCsvType: ExportCsvType) {
    this.exportSyslog.emit({ exportCsvType, syslogList: this.dataSource.filteredData });
  }

  onDelete() {
    this.deleteSyslog.emit();
  }

  onClickReset() {
    this.queryForm.reset();
    this.queryForm.controls['queryThresholdType'].setValue(0);
    this.filterSyslog.emit(defaultPageUnit);
  }

  onClickQueryButton() {
    const query = this.getFilter();
    this.filterSyslog.emit(query);
    this.tablePaginator.firstPage();
  }

  getFilter(): PageUnit {
    const form = this.queryForm.controls;

    const queryFromHour = this.getNumberOrDefault(form['queryFromHour'].value, 0);
    const queryFromMin = this.getNumberOrDefault(form['queryFromMin'].value, 0);
    const queryToHour = this.getNumberOrDefault(form['queryToHour'].value, 23);
    const queryToMin = this.getNumberOrDefault(form['queryToMin'].value, 59);
    const queryStartTime = this.getTimeWithOffset(form['queryFromTime'].value, queryFromHour, queryFromMin);
    const queryEndTime = this.getTimeWithOffset(form['queryToTime'].value, queryToHour, queryToMin, 59);

    const query: PageUnit = {
      limit: this.pageSize,
      offset: this.pageIndex * this.pageSize,
      ipaddress: this.normalizeEmptyValue(form['queryIPAddress'].value),
      facilities: this.normalizeEmptyValue(form['queryFacility'].value),
      severities: this.getSeverity(form['querySeverity'].value, form['queryThresholdType'].value),
      starttime: queryStartTime,
      endtime: queryEndTime
    };

    return query;
  }

  getSeverity(severity: SyslogSeverity, priority: SyslogPriority): number[] {
    if (!severity && severity !== 0) return [-1];

    const severityArray = Array.from({ length: 8 }, (_, i) => i);
    switch (priority) {
      case SyslogPriority.HIGHER:
        return severityArray.filter(i => i <= severity);
      case SyslogPriority.EQUAL:
        return [severity];
      case SyslogPriority.LOWER:
        return severityArray.filter(i => i >= severity);
      default:
        return [-1];
    }
  }

  normalizeEmptyValue(value: any): string {
    return !value ? '-1' : value;
  }

  getNumberOrDefault(value: any, defaultValue: number): number {
    return value?.length === 0 ? defaultValue : Number(value);
  }

  getTimeWithOffset(dateInput: any, hour: number, min: number, sec: number = 0): string {
    if (!dateInput) return '-1';

    const baseDate = new Date(dateInput);
    if (isNaN(baseDate.getTime())) return '-1';

    baseDate.setHours(baseDate.getHours() + hour);
    baseDate.setMinutes(baseDate.getMinutes() + min);
    baseDate.setSeconds(sec);

    const yyyy = baseDate.getFullYear();
    const MM = String(baseDate.getMonth() + 1).padStart(2, '0');
    const dd = String(baseDate.getDate()).padStart(2, '0');
    const HH = String(baseDate.getHours()).padStart(2, '0');
    const mm = String(baseDate.getMinutes()).padStart(2, '0');
    const ss = String(baseDate.getSeconds()).padStart(2, '0');

    const offsetMinutes = -baseDate.getTimezoneOffset();
    const sign = offsetMinutes >= 0 ? '+' : '-';
    const offsetHours = String(Math.floor(Math.abs(offsetMinutes) / 60)).padStart(2, '0');
    const offsetMins = String(Math.abs(offsetMinutes) % 60).padStart(2, '0');

    return `${yyyy}-${MM}-${dd}T${HH}:${mm}:${ss}${sign}${offsetHours}:${offsetMins}`
      .replace(/:/g, '%3A')
      .replace(/\+/g, '%2B');
  }

  getPriority(priority: SyslogPriority): string {
    const priorityMap = {
      [SyslogPriority.HIGHER]: 'pages.syslog.hte',
      [SyslogPriority.EQUAL]: 'pages.syslog.equals',
      [SyslogPriority.LOWER]: 'pages.syslog.lte'
    };

    const translationKey = priorityMap[priority] || '';
    return translationKey ? this.translateService.instant(translationKey) : '';
  }

  getFormatTime(timestamp: string) {
    if (timestamp === '') return '';

    const dt = new Date(timestamp);
    return this.utilsService.getDateTimeString(dt);
  }

  getFormatTimeWithoutYear(timestamp: string) {
    if (timestamp === '') return '';

    const dt = new Date(timestamp);
    return this.utilsService.getDateTimeString(dt).substring(5);
  }
}
