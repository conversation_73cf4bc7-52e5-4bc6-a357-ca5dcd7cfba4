<h3 mat-dialog-title>{{ title | translate }}</h3>
<div mat-dialog-content>
  <form #form="ngForm" id="form-vlan" novalidate *ngrxLet="dialogData.vlan as data">
    <div>
      <mat-form-field>
        <mat-label>{{ 'pages.vlan.vlan_id' | translate }}</mat-label>
        <input
          id="input-vlan-id"
          matInput
          required
          title="vlanId"
          name="vlanId"
          type="number"
          min="1"
          max="4094"
          #vlanId="ngModel"
          [(ngModel)]="data.vlanId"
          (ngModelChange)="onVlanIdChange()"
          [disabled]="dialogData.editMode"
        />
        <mat-hint align="start"
          >{{ 'validators.require_range_between' | translate : { rangeBegin: 1, rangeEnd: 4094 } }}
        </mat-hint>
        <mat-error *ngIf="vlanId.hasError('min') || vlanId.hasError('max')">
          {{ 'validators.invalid_range' | translate : { rangeBegin: 1, rangeEnd: 4094 } }}</mat-error
        >
        <mat-error *ngIf="vlanId.hasError('required')"> {{ 'validators.required' | translate }}</mat-error>
        <mat-error *ngIf="vlanId.hasError('duplicate')"> {{ 'pages.vlan.vlan_exist_hint' | translate }}</mat-error>
      </mat-form-field>
    </div>
    <div>
      <mat-form-field>
        <mat-label>{{ 'pages.vlan.vlan_name' | translate }}</mat-label>
        <input
          id="input-vlan-name"
          matInput
          title="vlanName"
          name="vlanName"
          type="text"
          minlength="1"
          maxlength="32"
          pattern="^\S+$"
          #vlanName="ngModel"
          [(ngModel)]="data.vlanName"
        />
        <mat-hint align="end">{{ vlanName?.control?.value?.length }} / 32</mat-hint>
        <mat-error *ngIf="vlanName.hasError('pattern')"> {{ 'validators.invalid_space' | translate }}</mat-error>
      </mat-form-field>
    </div>

    <div *ngIf="dialogData.deviceInfo.supportVlanTemstid">
      <mat-form-field>
        <mat-select
          required
          id="select-te-mstid"
          placeholder="{{ 'pages.vlan.te_mstid' | translate }}"
          name="teMstid"
          #teMstid="ngModel"
          [(ngModel)]="data.teMstid"
        >
          <mat-option id="option-te-mstid-yes" [value]="true">
            {{ 'general.common.yes' | translate }}
          </mat-option>
          <mat-option id="option-te-mstid-no" [value]="false">
            {{ 'general.common.no' | translate }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <div>
      <mat-form-field>
        <mat-select
          id="select-member-port"
          placeholder="{{ 'pages.vlan.member_port' | translate }}"
          name="memberPort"
          #memberPort="ngModel"
          [(ngModel)]="data.memberPort"
          multiple
        >
          <ng-container *ngFor="let port of dialogData.deviceInfo.portList">
            <mat-option
              id="option-member-port-{{ port.portId }}"
              [value]="port.portId"
              [disabled]="!enableMemberPort(port)"
              >{{ port.portName }}</mat-option
            >
          </ng-container>
        </mat-select>
      </mat-form-field>
    </div>
  </form>
</div>
<div mat-dialog-actions align="end">
  <button id="dialog-button-cancel" mat-button mat-dialog-close color="primary">
    {{ 'general.button.cancel' | translate }}
  </button>
  <button
    id="dialog-button-apply"
    (click)="onSubmit()"
    mat-raised-button
    color="primary"
    [disabled]="form && form.invalid"
  >
    {{ buttonText | translate }}
  </button>
</div>
