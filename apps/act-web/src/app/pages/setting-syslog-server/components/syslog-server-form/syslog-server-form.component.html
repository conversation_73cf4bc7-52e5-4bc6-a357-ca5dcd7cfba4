<div class="section">
  <div class="title">{{ 'general.common.selected_ip' | translate }}</div>
  <div>{{ utilsService.getDeviceIpAlias(setting.deviceIp, setting.deviceAlias) }}</div>
</div>

<div class="section">
  <div class="title">{{ 'pages.syslog_server.syslog_server_setting' | translate }}</div>
  <form #form="ngForm" id="syslog-server-form" novalidate *ngrxLet="setting as data">
    <div>
      <mat-form-field>
        <mat-select
          required
          id="select-enabled"
          placeholder="{{ 'pages.syslog_server.enable' | translate }}"
          name="enabled"
          #enabled="ngModel"
          [(ngModel)]="data.enabled"
        >
          <mat-option id="option-enabled-disabled" [value]="false">
            {{ 'general.common.disabled' | translate }}
          </mat-option>
          <mat-option id="option-enabled-enabled" [value]="true">
            {{ 'general.common.enabled' | translate }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <div>
      <mat-form-field>
        <mat-select
          required
          id="select-syslog-server-1"
          placeholder="{{ 'pages.syslog_server.syslog_server' | translate }} 1"
          name="syslogServer1"
          #syslogServer1="ngModel"
          [(ngModel)]="data.syslogServer1"
        >
          <mat-option id="option-syslog-server-1-disabled" [value]="false">
            {{ 'general.common.disabled' | translate }}
          </mat-option>
          <mat-option id="option-syslog-server-1-enabled" [value]="true">
            {{ 'general.common.enabled' | translate }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <div class="field-row">
      <mat-form-field>
        <mat-label>{{ 'pages.syslog_server.address' | translate }} 1</mat-label>
        <input
          id="input-address-1"
          matInput
          [required]="data.syslogServer1 === true"
          title="address1"
          name="address1"
          #address1="ngModel"
          [(ngModel)]="data.address1"
          [pattern]="ValidatorPattern.IPADDR_REGEX"
        />
        <mat-error *ngIf="address1.hasError('pattern')"> {{ 'validators.invalid_ip_address' | translate }}</mat-error>
        <mat-error *ngIf="address1.hasError('required')"> {{ 'validators.required' | translate }}</mat-error>
      </mat-form-field>

      <mat-form-field>
        <mat-label>{{ 'pages.syslog_server.udp_port' | translate }} 1</mat-label>
        <input
          id="input-udp-port-1"
          matInput
          required
          type="number"
          title="udpPort1"
          name="udpPort1"
          #udpPort1="ngModel"
          min="1"
          max="65535"
          [(ngModel)]="data.udpPort1"
        />
        <mat-hint align="start"
          >{{ 'validators.require_range_between' | translate : { rangeBegin: 1, rangeEnd: 65535 } }}
        </mat-hint>
        <mat-error *ngIf="udpPort1.hasError('min') || udpPort1.hasError('max')">
          {{ 'validators.invalid_range' | translate : { rangeBegin: 1, rangeEnd: 65535 } }}</mat-error
        >
        <mat-error *ngIf="udpPort1.hasError('required')"> {{ 'validators.required' | translate }}</mat-error>
      </mat-form-field>
    </div>

    <div>
      <mat-form-field>
        <mat-select
          required
          id="select-syslog-server-2"
          placeholder="{{ 'pages.syslog_server.syslog_server' | translate }} 2"
          name="syslogServer2"
          #syslogServer2="ngModel"
          [(ngModel)]="data.syslogServer2"
        >
          <mat-option id="option-syslog-server-2-disabled" [value]="false">
            {{ 'general.common.disabled' | translate }}
          </mat-option>
          <mat-option id="option-syslog-server-2-enabled" [value]="true">
            {{ 'general.common.enabled' | translate }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <div class="field-row">
      <mat-form-field>
        <mat-label>{{ 'pages.syslog_server.address' | translate }} 2</mat-label>
        <input
          id="input-address-2"
          matInput
          [required]="data.syslogServer2 === true"
          title="address2"
          name="address2"
          #address2="ngModel"
          [(ngModel)]="data.address2"
          [pattern]="ValidatorPattern.IPADDR_REGEX"
        />
        <mat-error *ngIf="address2.hasError('pattern')"> {{ 'validators.invalid_ip_address' | translate }}</mat-error>
        <mat-error *ngIf="address2.hasError('required')"> {{ 'validators.required' | translate }}</mat-error>
      </mat-form-field>

      <mat-form-field>
        <mat-label>{{ 'pages.syslog_server.udp_port' | translate }} 2</mat-label>
        <input
          id="input-udp-port-2"
          matInput
          required
          type="number"
          title="udpPort2"
          name="udpPort2"
          #udpPort2="ngModel"
          min="1"
          max="65535"
          [(ngModel)]="data.udpPort2"
        />
        <mat-hint align="start"
          >{{ 'validators.require_range_between' | translate : { rangeBegin: 1, rangeEnd: 65535 } }}
        </mat-hint>
        <mat-error *ngIf="udpPort2.hasError('min') || udpPort2.hasError('max')">
          {{ 'validators.invalid_range' | translate : { rangeBegin: 1, rangeEnd: 65535 } }}</mat-error
        >
        <mat-error *ngIf="udpPort2.hasError('required')"> {{ 'validators.required' | translate }}</mat-error>
      </mat-form-field>
    </div>

    <div>
      <mat-form-field>
        <mat-select
          required
          id="select-syslog-server-3"
          placeholder="{{ 'pages.syslog_server.syslog_server' | translate }} 3"
          name="syslogServer3"
          #syslogServer3="ngModel"
          [(ngModel)]="data.syslogServer3"
        >
          <mat-option id="option-syslog-server-3-disabled" [value]="false">
            {{ 'general.common.disabled' | translate }}
          </mat-option>
          <mat-option id="option-syslog-server-3-enabled" [value]="true">
            {{ 'general.common.enabled' | translate }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <div class="field-row">
      <mat-form-field>
        <mat-label>{{ 'pages.syslog_server.address' | translate }} 3</mat-label>
        <input
          id="input-address-3"
          matInput
          [required]="data.syslogServer3 === true"
          title="address3"
          name="address3"
          #address3="ngModel"
          [(ngModel)]="data.address3"
          [pattern]="ValidatorPattern.IPADDR_REGEX"
        />
        <mat-error *ngIf="address3.hasError('pattern')"> {{ 'validators.invalid_ip_address' | translate }}</mat-error>
        <mat-error *ngIf="address3.hasError('required')"> {{ 'validators.required' | translate }}</mat-error>
      </mat-form-field>

      <mat-form-field>
        <mat-label>{{ 'pages.syslog_server.udp_port' | translate }} 3</mat-label>
        <input
          id="input-udp-port-3"
          matInput
          required
          type="number"
          title="udpPort3"
          name="udpPort3"
          #udpPort3="ngModel"
          min="1"
          max="65535"
          [(ngModel)]="data.udpPort3"
        />
        <mat-hint align="start"
          >{{ 'validators.require_range_between' | translate : { rangeBegin: 1, rangeEnd: 65535 } }}
        </mat-hint>
        <mat-error *ngIf="udpPort3.hasError('min') || udpPort3.hasError('max')">
          {{ 'validators.invalid_range' | translate : { rangeBegin: 1, rangeEnd: 65535 } }}</mat-error
        >
        <mat-error *ngIf="udpPort3.hasError('required')"> {{ 'validators.required' | translate }}</mat-error>
      </mat-form-field>
    </div>
  </form>
</div>
