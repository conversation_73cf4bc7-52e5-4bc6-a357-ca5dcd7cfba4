import {
  AfterViewChecked,
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
  ViewChild
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, NgForm, ReactiveFormsModule } from '@angular/forms';
import { MatLegacyFormFieldModule as MatFormFieldModule } from '@angular/material/legacy-form-field';
import { MatLegacyInputModule as MatInputModule } from '@angular/material/legacy-input';
import { MatLegacySelectModule as MatSelectModule } from '@angular/material/legacy-select';

import { LetDirective } from '@ngrx/component';
import { TranslateModule } from '@ngx-translate/core';

import { UtilsService } from '@act-web/shared/service/utils.service';
import { ValidatorPattern } from '@act-web/shared/validator/validators';

import { DeviceInfoUnit } from '../../models/setting-syslog-server.model';

@Component({
  selector: 'app-syslog-server-form',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    FormsModule,
    LetDirective,
    MatInputModule,
    MatSelectModule
  ],
  templateUrl: './syslog-server-form.component.html',
  styleUrls: ['./syslog-server-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SyslogServerFormComponent implements OnInit, AfterViewChecked {
  @Input() setting: DeviceInfoUnit;
  @Output() changeFormValid = new EventEmitter<boolean>();

  utilsService = inject(UtilsService);

  @ViewChild('form') form!: NgForm;

  get ValidatorPattern() {
    return ValidatorPattern;
  }

  ngOnInit() {
    this.setUpForm();
  }

  ngAfterViewChecked(): void {
    this.changeFormValid.emit(!this.form.invalid);
  }

  setUpForm() {
    if (!this.setting.enabled) {
      this.setting.enabled = false;
    }
    if (!this.setting.syslogServer1) {
      this.setting.syslogServer1 = false;
    }
    if (this.setting.udpPort1 == null) {
      this.setting.udpPort1 = 514;
    }
    if (!this.setting.syslogServer2) {
      this.setting.syslogServer2 = false;
    }
    if (this.setting.udpPort2 == null) {
      this.setting.udpPort2 = 514;
    }
    if (!this.setting.syslogServer3) {
      this.setting.syslogServer3 = false;
    }
    if (this.setting.udpPort3 == null) {
      this.setting.udpPort3 = 514;
    }
  }
}
