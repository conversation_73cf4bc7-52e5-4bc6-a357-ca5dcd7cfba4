import {
  AfterViewChecked,
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
  ViewChild
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, NgForm, ReactiveFormsModule } from '@angular/forms';
import { MatLegacyFormFieldModule as MatFormFieldModule } from '@angular/material/legacy-form-field';
import { MatLegacyInputModule as MatInputModule } from '@angular/material/legacy-input';
import { MatLegacySelectModule as MatSelectModule } from '@angular/material/legacy-select';

import { LetDirective } from '@ngrx/component';
import { TranslateModule } from '@ngx-translate/core';

import { UtilsService } from '@act-web/shared/service/utils.service';
import { ValidatorPattern } from '@act-web/shared/validator/validators';

import { DeviceInfoUnit } from '../../models/setting-loop-protection.model';

@Component({
  selector: 'app-loop-protection-form',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    FormsModule,
    LetDirective,
    MatInputModule,
    MatSelectModule
  ],
  templateUrl: './loop-protection-form.component.html',
  styleUrls: ['./loop-protection-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LoopProtectionFormComponent implements OnInit, AfterViewChecked {
  @Input() setting: DeviceInfoUnit;
  @Output() changeFormValid = new EventEmitter<boolean>();

  utilsService = inject(UtilsService);
  @ViewChild('form') form!: NgForm;

  get ValidatorPattern() {
    return ValidatorPattern;
  }

  ngOnInit() {
    this.setUpForm();
  }

  ngAfterViewChecked(): void {
    this.changeFormValid.emit(!this.form.invalid);
  }

  setUpForm() {
    if (!this.setting.networkLoopProtection) {
      this.setting.networkLoopProtection = false;
    }
    if (this.setting.detectInterval == null) {
      this.setting.detectInterval = 10;
    }
  }
}
