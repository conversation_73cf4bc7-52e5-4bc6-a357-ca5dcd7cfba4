import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  ViewChild,
  ViewContainerRef,
  WritableSignal,
  signal
} from '@angular/core';
import { Router } from '@angular/router';
import { FormControl } from '@angular/forms';
import { MatLegacyDialog as MatDialog } from '@angular/material/legacy-dialog';
import { MatLegacySelectChange } from '@angular/material/legacy-select';
import { MatLegacySnackBar as MatSnackBar } from '@angular/material/legacy-snack-bar';
import {
  MatLegacyTabChangeEvent as MatTabChangeEvent,
  MatLegacyTabGroup as MatTabGroup
} from '@angular/material/legacy-tabs';

import { AppState } from '@act-web/app.service';
import { LicenseState } from '@act-web/stores/models/app.models';
import { initialLicense, selectLicense, selectProjectState } from '@act-web/stores/reducers/app.reducer';
import { UntilD<PERSON>roy, untilDestroyed } from '@ngneat/until-destroy';
import { Store, select } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import FileSaver from 'file-saver';
import JSZip from 'jszip';
import { Observable, Subscription, catchError, filter, finalize, forkJoin, from, map, of, switchMap, take } from 'rxjs';

import { ActDeploymentType, ActLicenseProfile, AIResponse } from '@act-web/shared/act-type.def';
import { CalculationDialogComponent } from '@act-web/shared/component/calculation-dialog/calculation-dialog.component';
import { ChatRoomComponentStore } from '@act-web/shared/component/chat-room/chat-room-store';
import { ConnectPlatformDialogComponent } from '@act-web/shared/component/connect-platform-dialog/connect-platform-dialog.component';
import { UploadSpecDialogComponent } from '@act-web/shared/component/upload-spec-dialog/upload-spec-dialog.component';
import { exLargeDialogConfig, mediumDialogConfig, smallDialogConfig } from '@act-web/shared/dialog-config.service';
import { BomBaselineData, BomProjectData } from '@act-web/shared/general-display-type';
import { AuthService } from '@act-web/shared/service/auth.service';
import { CNCService } from '@act-web/shared/service/cnc-services';
import { ErrorService } from '@act-web/shared/service/error.service';
import { StatusCode } from '@act-web/shared/service/server-status-code.def';
import { ErrorRes, OpCode, PatchUpdateActionType, SuccessRes } from '@act-web/shared/service/socket/web-socket.def';
import { WebSocketService } from '@act-web/shared/service/socket/web-socket.service';
import { UtilsService } from '@act-web/shared/service/utils.service';
import {
  ApiBomUnit,
  ComputeStateType,
  defaultTopologyFilter,
  Device,
  DeviceProfile,
  FilterType,
  ProjectTab,
  TopologyFilterType
} from '@act-web/shared/type.def';
import { triggerPageLoading } from '@act-web/store/actions/app.actions';

import { ProjectMode } from '../pages.def';
import { ProjectBaseStore } from '../project-base/project-base.store';
import { CheckEndStationDialogComponent } from './check-end-station-dialog/check-end-station-dialog.component';
import { ProjectUtilsService } from './project-utils.service';
import { CustomViewMode, ProjectAction, UndoRedo, Vendor, ViewMode } from './project.def';
import { ProjectStore } from './project.store';
import { TabComputeResultComponent } from './tab-compute-result/tab-compute-result.component';
import { TabTopologyComponent } from './tab-topology/tab-topology.component';

@UntilDestroy()
@Component({
  templateUrl: './project.component.html',
  styleUrls: ['./project.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ProjectStore, ChatRoomComponentStore]
})
export class ProjectComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('tabGroup') tabGroup!: MatTabGroup;
  @ViewChild('tabTopology') tabTopology!: TabTopologyComponent;
  @ViewChild('tabComputeTopology')
  tabComputeTopology!: TabComputeResultComponent;
  @ViewChild('dialogContainer', { read: ViewContainerRef }) viewContainerRef!: ViewContainerRef;

  devicePanelDisplayItems: DeviceProfile[] = [];
  vendors: Vendor = {};
  computeState = ComputeStateType.CanCompute;
  hasComputedResult = false;
  doAutoLayout = false;
  currentTab: number;
  userPermission = false;
  featureStatus$ = this.projectComponentStore.select(state => state.featureStatus);
  viewMode: ViewMode = ViewMode.StreamDesign;
  isManagementInterface = false;
  topologyFilters: TopologyFilterType = defaultTopologyFilter;
  mode = signal(ProjectMode.Design);
  computedResultStreamControl = new FormControl<number>(null);
  computedResultStreams = this.projectBaseStore.state$.pipe(
    select(state => state.computeResult),
    map(result => result.streamViewResults)
  );
  private deviceProfiles: DeviceProfile[] = [];
  private projectActionSubscription = new Subscription();
  private _projectId: number;
  projectMode: WritableSignal<ProjectMode> = signal(null);
  projectProfile: WritableSignal<ActLicenseProfile> = signal(null);
  licenseState: WritableSignal<LicenseState> = signal(initialLicense);
  hasBaseline: WritableSignal<boolean> = signal(false);
  ActDeploymentType = ActDeploymentType;
  devices: Device[] = [];
  isVerifyingSpec: WritableSignal<boolean> = signal(false);
  newProjectData: SuccessRes;

  get ProjectTab() {
    return ProjectTab;
  }

  get ComputeState() {
    return ComputeStateType;
  }

  get topologyViewMode() {
    return ViewMode;
  }

  get filterType() {
    return FilterType;
  }

  get ProjectMode() {
    return ProjectMode;
  }

  get ActLicenseProfile() {
    return ActLicenseProfile;
  }

  get displayModelSelectionTab() {
    return (
      (this.projectMode() === ProjectMode.Bom && this.projectProfile() === ActLicenseProfile.QuickModelSelection) ||
      this.projectMode() === ProjectMode.Design
    );
  }

  get displayBomListTab() {
    return (
      (this.projectMode() === ProjectMode.Bom && this.projectProfile() === ActLicenseProfile.QuickModelSelection) ||
      this.projectMode() === ProjectMode.Design
    );
  }

  get displayTopologyTab() {
    return (
      (this.projectMode() === ProjectMode.Bom &&
        this.projectProfile() &&
        this.projectProfile() !== ActLicenseProfile.QuickModelSelection) ||
      this.projectMode() === ProjectMode.Design
    );
  }

  constructor(
    private appState: AppState,
    private cncService: CNCService,
    private errorService: ErrorService,
    private projectUtilsService: ProjectUtilsService,
    private snackBar: MatSnackBar,
    private translateService: TranslateService,
    private store: Store,
    private dialog: MatDialog,
    private changeDetectorRef: ChangeDetectorRef,
    private authService: AuthService,
    private projectComponentStore: ProjectStore,
    private projectBaseStore: ProjectBaseStore,
    private webSocketService: WebSocketService,
    private utilsService: UtilsService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.store.pipe(select(selectProjectState), untilDestroyed(this)).subscribe(projectState => {
      // set initial tab
      if (this.projectMode() === null || this.projectProfile() === null) {
        this.currentTab =
          projectState.projectMode === ProjectMode.Bom &&
          projectState.projectProfile === ActLicenseProfile.QuickModelSelection
            ? ProjectTab.ModelSelection
            : ProjectTab.Topology;
      }

      this.projectMode.set(projectState.projectMode);
      this.projectProfile.set(projectState.projectProfile);
    });

    this.store.pipe(select(selectLicense), untilDestroyed(this)).subscribe(license => {
      this.licenseState.set(license);
    });

    this.projectBaseStore
      .select(state => state.devices)
      .pipe(untilDestroyed(this))
      .subscribe(devices => {
        this.devices = devices;
      });

    this.projectBaseStore.getBaselineList();

    this.projectBaseStore
      .select(state => state.baselineList)
      .pipe(untilDestroyed(this))
      .subscribe(baselineList => {
        this.hasBaseline.set(baselineList.length >= 2);
      });

    this.webSocketService
      .receive()
      .pipe(untilDestroyed(this))
      .subscribe({
        next: (jsonResult: SuccessRes | ErrorRes) => {
          if (jsonResult.OpCode === OpCode.START_INTELLIGENT_DOWNLOAD) {
            if (jsonResult.StatusCode === StatusCode.RUNNING && (jsonResult as SuccessRes).Data.status.code === 500) {
              this.snackBar.open(
                this.translateService.instant('response_handler.download_fail'),
                this.translateService.instant('general.button.close'),
                {
                  panelClass: ['error']
                }
              );
            } else if (jsonResult.StatusCode === StatusCode.FINISHED) {
              const fileName = `Questionnaire_${this.appState.projectId}.xlsx`;
              this.cncService.downloadIntelligentSpec(fileName).subscribe({
                error: () =>
                  this.snackBar.open(
                    this.translateService.instant('response_handler.download_fail'),
                    this.translateService.instant('general.button.close'),
                    {
                      panelClass: ['error']
                    }
                  )
              });
            }
          }

          if (jsonResult.OpCode === OpCode.START_INTELLIGENT_UPLOAD && this.isVerifyingSpec() === true) {
            const successJsonResult = jsonResult as SuccessRes;
            const data: { response: AIResponse; status: { code: number; message: string } } = successJsonResult.Data;
            if (
              data.response.role === 'Assistant' &&
              data.response.reply.reply === 'Finished.' &&
              this.newProjectData
            ) {
              this.loadProject(this.newProjectData);
            }
          }

          if (jsonResult.OpCode === OpCode.START_INTELLIGENT_UPLOAD && !this.isVerifyingSpec()) {
            const successJsonResult = jsonResult as SuccessRes;
            const data: { response: AIResponse; status: { code: number; message: string } } = successJsonResult.Data;
            if (data.response.role === 'Assistant' && data.response.reply.reply === 'Finished.') {
              this.projectBaseStore.getBaselineList();
            }
          }

          if (jsonResult.OpCode === OpCode.PROJECT_CHANGE) {
            if (this.isVerifyingSpec() === true && (jsonResult as SuccessRes).Action === PatchUpdateActionType.CREATE) {
              this.newProjectData = jsonResult as SuccessRes;
            }
          }
        },
        error: error => {
          this.errorService.handleError(error);
        }
      });
  }

  loadProject(jsonResult: SuccessRes) {
    const redirectProject = jsonResult.Data;
    this.router.navigate(['pages/project-table'], { queryParams: { projectId: redirectProject.Id } });
  }

  ngAfterViewInit(): void {
    this.userPermission = this.authService.checkRolePermission(this.authService.roleAction.enableTopologyAction);
    // init undo redo deploy status
    this.projectComponentStore.updateFeature();
    this.projectActionSubscription.add(
      this.projectComponentStore
        .select(state => state.projectActionStream.actionCount)
        .pipe(switchMap(() => this.projectComponentStore.select(state => state.projectActionStream.actionType)))
        .subscribe(action => {
          switch (action) {
            case ProjectAction.Compute:
              this.onCompute();
              break;
          }
        })
    );
    this.projectActionSubscription.add(
      this.projectComponentStore
        .select(state => state.projectMode)
        .subscribe({
          next: mode => {
            this.mode.set(mode);
          }
        })
    );
    this.projectActionSubscription.add(
      this.projectBaseStore
        .select(state => state.computeResult)
        .pipe(filter(computeResult => !!computeResult && computeResult.devices.length > 0))
        .subscribe({
          next: () => (this.hasComputedResult = true)
        })
    );
    this.projectActionSubscription.add(
      this.projectComponentStore
        .select(state => state.topologyFilter)
        .subscribe(topologyFilter => {
          this.topologyFilters = topologyFilter.topologyFilter;
        })
    );
    this.projectActionSubscription.add(
      this.projectBaseStore
        .select(state => state.projectId)
        .subscribe({
          next: projectId => {
            this._projectId = projectId;
          }
        })
    );
    this.projectComponentStore
      .select(state => state.computedResultStreamId)
      .subscribe({
        next: streamId => this.computedResultStreamControl.setValue(streamId)
      });
  }

  ngOnDestroy(): void {
    this.projectActionSubscription.unsubscribe();
    this.projectComponentStore.updateProjectActionStream(ProjectAction.None);
  }

  onSelectComputedStream($event): void {
    this.projectComponentStore.updateComputeResultStreamId($event.value);
  }

  onSaveBaseline(): void {
    const payload = { Name: 'Baseline_' + this.getBaselineTime() };
    this.store.dispatch(triggerPageLoading({ isLoading: true }));
    this.cncService.saveBaseline(payload).subscribe(
      () => {
        this.store.dispatch(triggerPageLoading({ isLoading: false }));
        this.snackBar.open(this.translateService.instant('response_handler.save_project_success'), '', {
          duration: 3000
        });
        this.projectBaseStore.getBaselineList();
      },
      error => {
        this.store.dispatch(triggerPageLoading({ isLoading: false }));
        this.errorService.handleError(error);
      }
    );
  }

  getBaselineTime(): string {
    const now = new Date(Date.now());

    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');

    return `${year}${month}${day}${hours}${minutes}${seconds}`;
  }

  onCompute(): void {
    mediumDialogConfig.data = {
      caculationType: OpCode.START_COMPUTE
    };
    mediumDialogConfig.viewContainerRef = this.viewContainerRef;
    const dialogRef = this.dialog.open(CalculationDialogComponent, mediumDialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result === 'complete') {
        this.selectComputeResult();
      }
    });
  }

  onDeploy(): void {
    this.store.dispatch(triggerPageLoading({ isLoading: true }));
    this.cncService.getTopologyLoop().subscribe({
      next: result => {
        exLargeDialogConfig.data = {
          hasLoop: result.loop
        };
        exLargeDialogConfig.viewContainerRef = this.viewContainerRef;
        this.dialog.open(CheckEndStationDialogComponent, exLargeDialogConfig);
        this.changeDetectorRef.markForCheck();
      },
      error: () => {
        this.snackBar.open(
          this.translateService.instant('response_handler.get_data_fail'),
          this.translateService.instant('general.button.close'),
          { panelClass: ['error'] }
        );
        this.store.dispatch(triggerPageLoading({ isLoading: false }));
      }
    });
  }

  onDownloadSpec(): void {
    this.webSocketService.send(OpCode.START_INTELLIGENT_DOWNLOAD);
  }

  onVerifySpec(): void {
    this.isVerifyingSpec.set(false);
    this.newProjectData = null;
    this.projectComponentStore.updateUploadSpec(false);
    smallDialogConfig.data = { uploadVerifiedSpec: false, verify: true };
    smallDialogConfig.viewContainerRef = this.viewContainerRef;
    this.dialog
      .open(UploadSpecDialogComponent, smallDialogConfig)
      .afterClosed()
      .subscribe(result => {
        if (result) {
          this.isVerifyingSpec.set(true);
          this.projectComponentStore.updateUploadSpec(true);
          this.store.dispatch(triggerPageLoading({ isLoading: false }));
        }
      });
  }

  onDownloadProject() {
    this.store.dispatch(triggerPageLoading({ isLoading: true }));
    const isBom = this.projectMode() === ProjectMode.Bom;

    this.performProjectDownload(isBom)
      .pipe(
        finalize(() => {
          this.store.dispatch(triggerPageLoading({ isLoading: false }));
        })
      )
      .subscribe({
        next: () => {
          this.snackBar.open(
            this.translateService.instant('response_handler.download_success'),
            this.translateService.instant('general.button.close'),
            { duration: 3000 }
          );
        },
        error: () => {
          this.snackBar.open(
            this.translateService.instant('response_handler.download_fail'),
            this.translateService.instant('general.button.close'),
            { panelClass: ['error'] }
          );
        }
      });
  }

  private performProjectDownload(isBom: boolean): Observable<void> {
    const requests = [this.cncService.exportProject(this.appState.projectId), this.onDownloadBomList()];

    if (!isBom) {
      requests.push(this.cncService.downloadDeviceConfig(false));
      return from(this.onScreenshotTopology()).pipe(
        switchMap(screenshotBlob =>
          forkJoin(requests).pipe(
            map(([projectData, bomBlob, projectJsonBlob]) =>
              this.createFullProjectZip(projectData, bomBlob, projectJsonBlob, screenshotBlob)
            )
          )
        )
      );
    } else {
      return forkJoin(requests).pipe(map(([projectData, bomBlob]) => this.createBomProjectZip(projectData, bomBlob)));
    }
  }

  private createFullProjectZip(projectData: any, bomBlob: Blob, projectJsonBlob: Blob, screenshotBlob: Blob): void {
    const zip = new JSZip();
    const downloadTime = this.utilsService.getCurrentTime();
    const zipFileName = `${projectData.ProjectName}${downloadTime}.zip`;

    zip.file(`${projectData.ProjectName}.json`, JSON.stringify(projectData));
    zip.file(`BOM list ${downloadTime}.xlsx`, bomBlob);
    zip.file('device_config.zip', projectJsonBlob);
    zip.file('Topology.png', screenshotBlob);

    this.saveZipFile(zip, zipFileName);
    this.tabGroup.selectedIndex = this.projectProfile() === ActLicenseProfile.Foxboro ? 5 : 4;
    this.changeDetectorRef.markForCheck();
  }

  private createBomProjectZip(projectData: any, bomBlob: Blob): void {
    const zip = new JSZip();
    const downloadTime = this.utilsService.getCurrentTime();
    const zipFileName = `${projectData.ProjectName}${downloadTime}.zip`;

    zip.file(`${projectData.ProjectName}.json`, JSON.stringify(projectData));
    zip.file(`BOM list ${downloadTime}.xlsx`, bomBlob);

    this.saveZipFile(zip, zipFileName);
  }

  private saveZipFile(zip: JSZip, fileName: string): void {
    zip.generateAsync({ type: 'blob' }).then(content => {
      FileSaver.saveAs(content, fileName);
    });
  }

  onDownloadBomList(): Observable<Blob> {
    return this.projectBaseStore
      .select(state => state.baselineList)
      .pipe(
        take(1),
        switchMap(baselineList => {
          if (baselineList.length < 2) {
            console.warn('Baseline list too short, returning empty BOM file.');
            return of(new Blob());
          }
          return this.cncService.getBomDetail(baselineList[1].id).pipe(
            switchMap((payload: ApiBomUnit) => {
              const productData: BomProjectData = {
                projectName: this.appState.projectName,
                uuid: this.appState.uuid,
                profileType: this.appState.projectProfile,
                baselineTime: this.utilsService.timeFormat(payload.Date),
                downloadTime: new Date().toISOString().slice(0, 10).replace(/-/g, ''),
                total: payload.TotalPrice
              };

              const deviceContent: BomBaselineData[] = Object.values(payload.SkuQuantitiesMap).map((value, index) => ({
                index: index + 1,
                modelName: value.ModelName,
                description: value.Description,
                qty: value.Quantity,
                unitPrice: value.Price,
                totalPrice: value.TotalPrice.split(' ')[1]
              }));

              const bomlistWorkbook = this.utilsService.parseBomExcelFromBaseline(productData, deviceContent);

              return from(bomlistWorkbook.xlsx.writeBuffer()).pipe(
                map(content => new Blob([content], { type: 'application/vnd.ms-excel;charset=utf-8;' }))
              );
            }),
            catchError(error => {
              console.error('getBomDetail failed:', error);
              return of(new Blob());
            })
          );
        })
      );
  }

  onScreenshotTopology(): Promise<Blob> {
    return new Promise((resolve, reject) => {
      const originTabIndex = this.tabGroup.selectedIndex;
      this.tabGroup.selectedIndex = 0;
      setTimeout(async () => {
        try {
          const dataUrl = await this.tabTopology.onScreenshotTopology(false);
          const screenshotBlob = this.dataURLtoBlob(dataUrl);
          resolve(screenshotBlob);
          this.tabGroup.selectedIndex = originTabIndex;
        } catch (error) {
          reject(error);
          this.tabGroup.selectedIndex = originTabIndex;
        }
      }, 1000);
    });
  }

  dataURLtoBlob(dataUrl: string): Blob {
    const byteString = atob(dataUrl.split(',')[1]);
    const mimeString = dataUrl.split(',')[0].split(':')[1].split(';')[0];
    const arrayBuffer = new ArrayBuffer(byteString.length);
    const uintArray = new Uint8Array(arrayBuffer);
    for (let i = 0; i < byteString.length; i++) {
      uintArray[i] = byteString.charCodeAt(i);
    }
    return new Blob([uintArray], { type: mimeString });
  }

  checkPlatformStatus() {
    this.cncService.checkPlatformStatus().subscribe(
      res => {
        if (res) {
          this.onRegister();
        } else {
          this.openConnectPlatformDialog();
        }
      },
      () => {
        this.store.dispatch(triggerPageLoading({ isLoading: false }));
        this.snackBar.open(
          this.translateService.instant('response_handler.save_project_fail'),
          this.translateService.instant('general.button.close'),
          { panelClass: ['error'] }
        );
      }
    );
  }

  onRegister() {
    this.store.dispatch(triggerPageLoading({ isLoading: true }));
    this.cncService.registerProjectToPlatform().subscribe(
      () => {
        this.store.dispatch(triggerPageLoading({ isLoading: false }));
        this.snackBar.open(this.translateService.instant('response_handler.save_project_success'), '', {
          duration: 3000
        });
      },
      () => {
        this.store.dispatch(triggerPageLoading({ isLoading: false }));
        this.snackBar.open(
          this.translateService.instant('response_handler.save_project_fail'),
          this.translateService.instant('general.button.close'),
          { panelClass: ['error'] }
        );
      }
    );
  }

  openConnectPlatformDialog() {
    smallDialogConfig.viewContainerRef = this.viewContainerRef;
    this.dialog
      .open(ConnectPlatformDialogComponent, smallDialogConfig)
      .afterClosed()
      .subscribe(result => {
        if (result) {
          this.onRegister();
        }
      });
  }

  onChangeViewMode(event: MatLegacySelectChange): void {
    this.projectComponentStore.updateViewMode(event.value);
  }

  onSelectedTabChange(tabIndex: MatTabChangeEvent) {
    const tabMapping = {
      [this.translateService.instant('network.menu.accessory_list')]: ProjectTab.ModelSelection,
      [this.translateService.instant('network.menu.topology')]: ProjectTab.Topology,
      [this.translateService.instant('pages.compute_result.page_title')]: ProjectTab.CalculationResult,
      [this.translateService.instant('pages.compute_result.compute_result_table')]: ProjectTab.CalculationResultTable,
      [this.translateService.instant('pages.vlan.page_title')]: ProjectTab.VLAN,
      [this.translateService.instant('network.menu.bom_list')]: ProjectTab.BomList
    };
    this.currentTab = tabMapping[tabIndex.tab.textLabel];
    this.projectComponentStore.updateProjectTab(this.currentTab);
  }

  onChangeManagementInterface($event): void {
    this.projectComponentStore.updateCustomViewMode(
      $event.checked ? CustomViewMode.ManagementInterface : CustomViewMode.None
    );
  }

  selectComputeResult(): void {
    this.hasComputedResult = true;
    this.tabGroup.selectedIndex = this.projectProfile() === ActLicenseProfile.Foxboro ? 2 : 1;
    this.changeDetectorRef.markForCheck();
  }

  rebuildDevicesPanel(categories?: Set<string>): void {
    if (categories?.size) {
      this.devicePanelDisplayItems = [];
      this.deviceProfiles.forEach(item => {
        if (categories.has(item.deviceType)) {
          this.devicePanelDisplayItems.push(item);
        }
      });
    } else {
      this.devicePanelDisplayItems = this.deviceProfiles;
    }
  }

  undo(): void {
    this.projectComponentStore.undoRedo(UndoRedo.Undo);
  }

  redo(): void {
    this.projectComponentStore.undoRedo(UndoRedo.Redo);
  }

  topologyFilterChange($event, type: FilterType): void {
    const filterMapping = {
      [FilterType.DeviceIp]: () => (this.topologyFilters.device.ip = $event.checked),
      [FilterType.DeviceAlias]: () => (this.topologyFilters.device.alias = $event.checked),
      [FilterType.DeviceName]: () => (this.topologyFilters.device.deviceName = $event.checked),
      [FilterType.ModelName]: () => (this.topologyFilters.device.modelName = $event.checked),
      [FilterType.LinkStatus]: () => (this.topologyFilters.link.status = $event.checked),
      [FilterType.LinkSpeed]: () => (this.topologyFilters.link.speed = $event.checked),
      [FilterType.LinkInterface]: () => (this.topologyFilters.link.interface = $event.checked),
      [FilterType.StreamStatus]: () => (this.topologyFilters.stream.status = $event.checked),
      [FilterType.StreamName]: () => (this.topologyFilters.stream.streamName = $event.checked),
      [FilterType.StreamInterface]: () => (this.topologyFilters.stream.interface = $event.checked)
    };

    if (filterMapping[type]) {
      filterMapping[type]();
      this._changeTopologyFilter(this.topologyFilters);
    }
  }

  private _changeTopologyFilter(topologyFilters) {
    let filter = {};
    if (sessionStorage.getItem('topologyFilters') !== null) {
      try {
        filter = JSON.parse(sessionStorage.getItem('topologyFilters'));
      } catch (e) {
        //filter is not a valid JSON
        filter = {};
      }
    }
    filter[this._projectId.toString()] = topologyFilters;
    sessionStorage.setItem('topologyFilters', JSON.stringify(filter));
    this.projectComponentStore.updateTopologyFilter(topologyFilters);
  }
}
