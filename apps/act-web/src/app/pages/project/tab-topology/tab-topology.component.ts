import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  OnDestroy,
  ViewChild,
  ViewContainerRef,
  WritableSignal,
  signal
} from '@angular/core';
import { MatLegacyDialog as MatDialog } from '@angular/material/legacy-dialog';
import { MatLegacySnackBar as MatSnackBar } from '@angular/material/legacy-snack-bar';
import {
  MXEdgeInterface,
  MXFeature,
  MXNodeInterface,
  MXStreamInterface,
  ScreenShotInclude
} from '@network/mx-ui/mx-topology-feature-model';

import { selectProjectState } from '@act-web/stores/reducers/app.reducer';
import { selectAuthState } from '@act-web/stores/selectors/auth.selector';
import { MxGraph, MxEdgeData, MxEvent, MxPlugin, MxBehavior, MxUserBehavior } from '@moxa/graph';
import { MxCreateEdge, MxCreateEdgeOptions } from '@moxa/graph/lib/behavior/create-edge';
import { Until<PERSON><PERSON><PERSON>, untilDestroyed } from '@ngneat/until-destroy';
import { Store, select } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import domtoimage from 'dom-to-image';
import { find, findIndex, some } from 'lodash-es';
import {
  BehaviorSubject,
  Subscription,
  catchError,
  filter,
  first,
  forkJoin,
  map,
  of,
  skip,
  switchMap,
  take
} from 'rxjs';

import { ProjectMode } from '@act-web/pages/pages.def';
import { TopologyAction } from '@act-web/pages/project-base/project-base.def';
import { ProjectBaseStore } from '@act-web/pages/project-base/project-base.store';
import { DeleteStreamSettingDialogComponent } from '@act-web/pages/traffic-design/traffic-advanced-setting/stream-design/delete-stream-setting-dialog/delete-stream-setting-dialog.component';
import { StreamSettingDialogComponent } from '@act-web/pages/traffic-design/traffic-advanced-setting/stream-design/stream-setting-dialog/stream-setting-dialog.component';
import { ActLicenseProfile } from '@act-web/shared/act-type.def';
import {
  exLargeDialogConfig,
  largeDialogConfig,
  mediumDialogConfig,
  smallDialogConfig
} from '@act-web/shared/dialog-config.service';
import { StreamSetting } from '@act-web/shared/general-display-type';
import { AuthService } from '@act-web/shared/service/auth.service';
import { CNCService } from '@act-web/shared/service/cnc-services';
import { ErrorService } from '@act-web/shared/service/error.service';
import {
  defaultTopologyFilter,
  Device,
  DeviceProfile,
  Link,
  ProjectTab,
  Role,
  Stream,
  TopologyFilterType
} from '@act-web/shared/type.def';
import { triggerPageLoading } from '@act-web/store/actions/app.actions';

import { ProjectUtilsService } from '../project-utils.service';
import { DeviceAction, LinkAction, StreamAction, ViewMode } from '../project.def';
import { ProjectStore } from '../project.store';
import { DeleteLinkDialogComponent } from './delete-link-dialog/delete-link-dialog.component';
import { DeleteDeviceDialogComponent } from './device-setting-dialog/delete-device-dialog/delete-device-dialog.component';
import { DeviceAllSettingDialogComponent } from './device-setting-dialog/device-all-setting-dialog/device-all-setting-dialog.component';
import { ImportTopologyDialogComponent } from './import-topology-dialog/import-topology-dialog.component';
import { LinkSettingDialogComponent } from './link-setting-dialog/link-setting.component';
import { NewStreamSettingDialogComponent } from './new-stream-setting-dialog/new-stream-setting-dialog.component';
import { SaveTopologyDialogComponent } from './save-topology-dialog/save-topology-dialog.component';
import {
  AutoLayoutType,
  DragType,
  ElementMenuItem,
  ForceNode,
  ignoreDeviceIds,
  ignoreEdgeIds,
  TemplateType
} from './tab-topology.def';
import { TabTopologyService } from './tab-topology.service';
import { TopologyDetailComponent } from './topology-detail/topology-detail.component';
import { TopologyTemplateDialogComponent } from './topology-template-dialog/topology-template-dialog.component';

export enum ElementType {
  NODE = 'node',
  EDGE = 'edge',
  STREAM = 'stream'
}

@UntilDestroy()
@Component({
  selector: 'app-tab-topology',
  templateUrl: './tab-topology.component.html',
  styleUrls: ['./tab-topology.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [TabTopologyService]
})
export class TabTopologyComponent implements OnDestroy, AfterViewInit {
  // @ViewChild('mxTopology', { static: true })
  // mxTopology!: MxTopologyComponent;

  @ViewChild('captureElement')
  captureElement!: ElementRef;
  @ViewChild('topology')
  topology: MXFeature;
  @ViewChild('deviceDetail') deviceDetail: TopologyDetailComponent;
  projectMode: WritableSignal<ProjectMode> = signal(null);
  projectProfile: WritableSignal<ActLicenseProfile> = signal(null);

  topologyTextObj = {
    addSwitch: this.translateService.instant('network.device.add_switch'),
    addEndStation: this.translateService.instant('network.device.add_end_station'),
    addBridgedEndStation: this.translateService.instant('network.device.add_br_end_station'),
    addLink: this.translateService.instant('network.link.add_link'),
    addStream: this.translateService.instant('network.stream.add_stream'),
    editDevice: this.translateService.instant('network.device.edit_device'),
    copyDevice: this.translateService.instant('network.device.copy_device'),
    pasteDevice: this.translateService.instant('network.device.paste_device'),
    deleteDevice: this.translateService.instant('network.device.delete_device'),
    editLink: this.translateService.instant('network.link.edit_link'),
    deleteLink: this.translateService.instant('network.link.delete_link'),
    editStream: this.translateService.instant('network.stream.edit_stream'),
    deleteStream: this.translateService.instant('network.stream.delete_stream'),
    timeSlot: this.translateService.instant('network.stream.time_slot_setting'),
    batchUpdate: this.translateService.instant('network.device.batch_update')
  };
  isClickCopy = false;
  selectedDeviceIp = '';
  projectType = '';
  userPermission = this.authService.checkRolePermission(this.authService.roleAction.enableTopologyAction);

  featureNodes = new BehaviorSubject<MXNodeInterface[]>([]);
  featureEdges = new BehaviorSubject<MXEdgeInterface[]>([]);
  featureStreams = new BehaviorSubject<MXStreamInterface[]>([]);

  selectElement$ = new BehaviorSubject<Device | Link | Stream>(null);
  elementMenuItem$ = new BehaviorSubject<ElementMenuItem>(null);
  userRole: Role;
  private initSubscription = new Subscription();
  _topologyFilter: TopologyFilterType;
  private devices: Device[] = [];
  private links: Link[] = [];
  private streams: StreamSetting[] = [];
  private deviceProfiles: DeviceProfile[];
  private projectTab: ProjectTab;
  private _viewMode: ViewMode;
  private originTopologyFilter: TopologyFilterType = defaultTopologyFilter;
  private _translateLang = {
    addLinkSuccess: this.translateService.stream('response_handler.add_link_success'),
    lackDeviceInterface: this.translateService.stream('response_handler.lack_device_interface_fail'),
    buttonClose: this.translateService.stream('general.button.close')
  };
  private _copyDevices: Device[] = [];
  private _isChatRoomFocused = signal(false);
  private _topologyFirstFitCount = 0;
  private _graph: MxGraph;
  private subStreamMapping = {};
  private _managementEdgeIds: string[] = [];
  private _hasFit = signal(false);
  private _defaultBehavior: MxUserBehavior[] = [
    {
      type: MxBehavior.DRAG_ELEMENT
    },
    {
      type: MxBehavior.CLICK_SELECT
    },
    {
      type: MxBehavior.DRAG_CANVAS
    },
    {
      type: MxBehavior.ZOOM_CANVAS
    },
    {
      type: MxBehavior.BRUSH_SELECT
    },
    {
      type: MxBehavior.CREATE_EDGE,
      trigger: 'none'
    }
  ];

  get ProjectTab() {
    return ProjectTab;
  }

  constructor(
    public authService: AuthService,
    private translateService: TranslateService,
    private projectUtilsService: ProjectUtilsService,
    private cncService: CNCService,
    private errorService: ErrorService,
    private dialog: MatDialog,
    private store: Store,
    private snackBar: MatSnackBar,
    private projectBaseStore: ProjectBaseStore,
    private projectComponentStore: ProjectStore,
    private viewContainerRef: ViewContainerRef,
    private changeDetectorRef: ChangeDetectorRef,
    private tabTopologyService: TabTopologyService
  ) {
    this.store.pipe(select(selectProjectState), untilDestroyed(this)).subscribe(projectState => {
      this.projectMode.set(projectState.projectMode);
      this.projectProfile.set(projectState.projectProfile);
      this.updateTopologyFilterByProjectMode();
      this._redrawTopology();
    });
  }

  ngAfterViewInit(): void {
    this._graph = new MxGraph({
      renderer: 'svg',
      plugins: [{ type: MxPlugin.FIXED_TOOLBAR }],
      container: 'vizion-container',
      behaviors: this._defaultBehavior,
      data: {
        nodes: [],
        edges: []
      },
      theme: 'light',
      animation: false,
      zoomRange: [0.5, 2]
    });
    this._graph.render();
    // change device coordinate when drag end
    this._graph.on(MxEvent.NODE_DRAG_MULTI_END, e => {
      const event = e as unknown as string[];
      this._changeDeviceCoordinate(
        event.map((nodeId: string) => {
          const node = this._graph.getNodeData(nodeId);
          return {
            id: this.projectUtilsService.getDeviceIdFromVizion(nodeId),
            x: node.config.point.x,
            y: node.config.point.y
          };
        })
      );
    });
    // detect node click
    this._graph.on(MxEvent.NODE_CLICK, (e: any) => {
      const id = e.target.id as string;
      for (const ignoreDeviceId of ignoreDeviceIds) {
        if (id.includes(ignoreDeviceId)) {
          return;
        }
      }
      const target = this.devices.find(device => device.id == Number(id.split('-')[1]));
      const features = this.projectUtilsService
        .getItemFeatures(target)
        .filter(feature => feature.supportProjectMode.includes(this.projectMode()));
      const visibleLength = features.filter(feature => feature.visible).length;
      const position = {
        x: `${e.client.x - 8 - visibleLength * 20}`,
        y: `${e.client.y - 85}`
      };
      this.elementMenuItem$.next({
        position: position,
        element: target,
        elementFeatures: features
      });
      // update topology detail tab
      this.selectElement$.next(target);
    });
    // detect link click
    this._graph.on(MxEvent.EDGE_CLICK, (e: any) => {
      const id = e.target.id as string;
      for (const ignoreEdgeId of ignoreEdgeIds) {
        if (id.includes(ignoreEdgeId)) {
          return;
        }
      }
      const [itemType, itemId] = id.split('-');
      let target;
      if (itemType === 'link') {
        target = this.links.find(link => link.id == Number(itemId));
      }
      if (itemType === 'stream') {
        target = this.streams.find(stream => stream.id == Number(itemId));
        // active all sub stream
        this._selectSubStream(itemId);
      }
      if (!target) {
        return;
      }
      const features = this.projectUtilsService.getItemFeatures(target);
      const visibleLength = features.filter(feature => feature.visible).length;
      const position = {
        x: `${e.client.x - 8 - visibleLength * 20}`,
        y: `${e.client.y - 85}`
      };
      this.elementMenuItem$.next({
        position: position,
        element: target,
        elementFeatures: features
      });
      // update topology detail tab
      this.selectElement$.next(target);
    });

    // this.initSubscription.add(
    //   this.topology.getZoomPercentage().subscribe({
    //     next: percentage => this.projectComponentStore.updateZoomPercentage(percentage),
    //   })
    // );
    document.getElementById('vizion-container').addEventListener('keyup', (event: KeyboardEvent) => {
      // filter event when chatroom opened
      if (this._isChatRoomFocused()) return;
      if (event.key === 'Delete' || event.key === 'Backspace') {
        const nodes = this._graph.getElementByState('node', 'selected');
        if (nodes.length > 0) {
          const selectedDevices = nodes.map(node =>
            this.devices.find(device => device.id === Number(node.id.split('-')[1]))
          );
          this.onDeleteDevice(selectedDevices);
        }
      }
    });
    document.getElementById('vizion-container').addEventListener('keydown', (event: KeyboardEvent) => {
      // filter event when chatroom opened
      if (this._isChatRoomFocused()) return;
      if ((event.ctrlKey || event.metaKey) && event.key === 'c') {
        // copy
        const nodes = this._graph.getElementByState('node', 'selected');
        if (nodes.length > 0) {
          this._copyDevices = nodes.map(node =>
            this.devices.find(device => device.id === Number(node.id.split('-')[1]))
          );
        }
      }
      if ((event.ctrlKey || event.metaKey) && event.key === 'a') {
        // select all device
        this.topology.selectAllNodes();
      }
      // Ctrl+V or Cmd+V pressed?
      if ((event.ctrlKey || event.metaKey) && event.key == 'v') {
        // paste
        this.cncService.copyDevice(this._copyDevices.map(device => device.id)).subscribe({
          next: value => {
            // select node after it created
            setTimeout(() => {
              const newStates = value.Parameter.DeviceIds.reduce((acc, curr) => {
                acc[`device-${curr}`] = ['selected'];
                return acc;
              }, {});
              this._graph.setElementState(newStates);
            }, 200);
          }
        });
      }
    });
    this.initSubscription.add(
      this.store.pipe(select(selectAuthState), untilDestroyed(this)).subscribe(authState => {
        this.userRole = authState.userRole;
      })
    );
    this._graph.once(MxEvent.GRAPH_AFTER_RENDER, () => {
      this.initSubscription.add(
        this.projectBaseStore
          .select(state => state.deviceProfiles)
          .pipe(untilDestroyed(this))
          .subscribe(deviceProfiles => {
            this.deviceProfiles = deviceProfiles;
          })
      );
      this.initSubscription.add(
        this.projectBaseStore
          .select(state => state.devices)
          .subscribe(devices => {
            this.devices = devices;
          })
      );
      this.initSubscription.add(
        this.projectBaseStore
          .select(state => state.links)
          .subscribe(links => {
            this.links = links;
          })
      );
      this.initSubscription.add(
        this.projectBaseStore
          .select(state => state.streams)
          .subscribe(streams => {
            this.streams = streams;
          })
      );
      this.initSubscription.add(
        this.projectBaseStore
          .select(state => state.topologyAction)
          .pipe(
            skip(1),
            filter(action => !!action)
          )
          .subscribe(topologyAction => {
            this.updateTopology(topologyAction);
          })
      );
      // need to redraw topology when customViewMode change
      this.initSubscription.add(
        this.projectComponentStore
          .select(state => state.topologyFilter)
          .pipe(skip(1))
          .subscribe(topologyFilter => {
            this.originTopologyFilter = topologyFilter.topologyFilter;
            this.updateTopologyFilterByProjectMode();
            this._redrawTopology();
            // auto layout when device in 0,0
            if (this.checkDeviceNeedAutoLayout(this.devices)) {
              this.onAutoLayout(AutoLayoutType.Square);
            }
          })
      );
      this.initSubscription.add(
        this.projectBaseStore
          .select(state => state.projectInitialized)
          .pipe(filter(projectInitialized => projectInitialized !== 0))
          .subscribe(() => {
            this._graph.clear().then(() => {
              // update is empty project
              if (this.devices.length === 0) {
                this.projectComponentStore.updateIsEmptyProject(true);
              }
              this._initGraphData();
              this._graph.render().then(() => {
                // get topology filter from session storage
                this.projectBaseStore
                  .select(state => state.projectId)
                  .pipe(first(projectId => !!projectId))
                  .subscribe({
                    next: result => {
                      this._graph.fitView();
                      const oldFilters = sessionStorage.getItem('topologyFilters');
                      try {
                        if (oldFilters !== null && JSON.parse(oldFilters)[result]) {
                          const topologyFilters: TopologyFilterType = JSON.parse(oldFilters)[result];
                          this.projectComponentStore.updateTopologyFilter(topologyFilters);
                        } else {
                          // no filter
                          this.projectComponentStore.updateTopologyFilter(defaultTopologyFilter);
                        }
                      } catch (error) {
                        //filter is not a valid JSON
                      }
                    }
                  });
              });
            });
          })
      );
      this.initSubscription.add(
        this.projectComponentStore
          .select(state => state.projectTab)
          .subscribe(projectTab => {
            this.projectTab = projectTab;
          })
      );
      this.initSubscription.add(
        this.projectComponentStore
          .select(state => state.updateFitToScreen)
          .pipe(filter(updateFitToScreen => updateFitToScreen !== 0))
          .subscribe(() => {
            setTimeout(() => {
              this._graph.fitView();
            });
          })
      );
      this.initSubscription.add(
        this.projectComponentStore
          .select(state => state.chatRoomFocused)
          .subscribe(chatRoomFocused => {
            this._isChatRoomFocused.set(chatRoomFocused);
          })
      );
    });
  }

  ngOnDestroy(): void {
    this.initSubscription.unsubscribe();
    this.projectComponentStore.updateIsEmptyProject(false);
    // stop monitor
    this.projectComponentStore.projectModeChange(ProjectMode.Design);
  }

  updateTopologyFilterByProjectMode() {
    if (this.projectMode() === ProjectMode.Design) {
      this._topologyFilter = { ...this.originTopologyFilter };
    } else {
      this._topologyFilter = {
        device: { ip: this.originTopologyFilter.device.ip, alias: false, deviceName: false, modelName: false },
        link: {
          status: true,
          interface: false,
          speed: false
        },
        stream: {
          status: true,
          streamName: false,
          interface: false
        },
        managementEndpoint: {
          status: true
        }
      };
    }
  }

  _initGraphData(): void {
    const edges = this.links.map(link => this.projectUtilsService.parseVizionEdge(link, this._topologyFilter));
    const newEdges = this.projectUtilsService.linkPreprocess(edges);
    const streams: MxEdgeData[] = [];
    this.streams.forEach(stream => {
      const streamEdgeData = this.projectUtilsService.parseVizionStream(stream, this.devices, this._topologyFilter);
      this.subStreamMapping[streamEdgeData.streamId.toString()] = streamEdgeData.edges.map(
        (edge: MxEdgeData) => edge.id
      );
      streams.push(...streamEdgeData.edges);
    });
    newEdges.push(...this.projectUtilsService.streamPreprocess(streams));
    this._graph.addNode(
      this.devices.map(device =>
        this.projectUtilsService.parseVizionNode(device, this.deviceProfiles, this._topologyFilter)
      )
    );
    this._graph.addEdge(newEdges);
  }

  updateTopology(topologyAction: TopologyAction): void {
    let element;
    let streamEdgeData;
    const linkPrefixRegex = /^link-/;
    let edgesData;
    this.elementMenuItem$.next(null);
    const newStreams: MxEdgeData[] = [];
    switch (topologyAction.elementType) {
      case 'device':
        element = topologyAction.element as Device;
        switch (topologyAction.type) {
          case 'create':
            this._graph.addNode(
              this.projectUtilsService.parseVizionNode(element, this.deviceProfiles, this._topologyFilter)
            );
            break;
          case 'update':
            this._graph.updateNode(
              this.projectUtilsService.parseVizionNode(element, this.deviceProfiles, this._topologyFilter)
            );
            break;
          case 'delete':
            this._graph.removeNode(topologyAction.id);
            break;
        }
        break;
      case 'link':
        element = topologyAction.element as Link;
        switch (topologyAction.type) {
          case 'create':
            this._graph.addEdge([this.projectUtilsService.parseVizionEdge(element, this._topologyFilter)]);
            break;
          case 'delete':
            if (this._graph.isElementExist(topologyAction.id)) {
              this._graph.removeEdge(topologyAction.id);
            }
            break;
          case 'update':
            this._graph.updateEdge([this.projectUtilsService.parseVizionEdge(element, this._topologyFilter)]);
            break;
        }
        edgesData = this._graph
          .getEdgeData()
          .filter(edge => linkPrefixRegex.test(edge.id as string))
          .map(edge => {
            edge.config.type = 'line-edge';
            return edge;
          });
        this._graph.updateEdge(edgesData);
        this._graph.updateEdge(this.projectUtilsService.linkPreprocess(edgesData));
        break;
      case 'stream':
        element = topologyAction.element as StreamSetting[];
        // remove all old sub stream
        this.subStreamMapping = [];
        this._graph.removeEdge(
          this._graph
            .getEdgeData()
            .filter(item => {
              const id = item.id as string;
              return id.startsWith('stream-');
            })
            .map(item => item.id)
        );
        this._graph.render().then(() => {
          element.forEach(stream => {
            streamEdgeData = this.projectUtilsService.parseVizionStream(stream, this.devices, this._topologyFilter);
            this.subStreamMapping[streamEdgeData.streamId.toString()] = streamEdgeData.edges.map(
              (edge: MxEdgeData) => edge.id
            );
            newStreams.push(...streamEdgeData.edges);
          });
          this._graph.addEdge(this.projectUtilsService.streamPreprocess(newStreams));
          this._graph.render();
        });
        // switch (topologyAction.type) {
        //   case 'create':
        //     streamEdgeData = this.projectUtilsService.parseVizionStream(element, this.devices, this._topologyFilter);
        //     this.subStreamMapping[streamEdgeData.streamId.toString()] = streamEdgeData.edges.map(
        //       (edge: MxEdgeData) => edge.id
        //     );
        //     this._graph.addEdge(streamEdgeData.edges);
        //     break;
        //   case 'delete':
        //     this._graph.removeEdge(this.subStreamMapping[topologyAction.id]);
        //     break;
        //   case 'update':
        //     // remove old sub stream
        //     this._graph.removeEdge(this.subStreamMapping[topologyAction.id]);
        //     // re-add new sub stream
        //     streamEdgeData = this.projectUtilsService.parseVizionStream(element, this._topologyFilter);
        //     this.subStreamMapping[streamEdgeData.streamId.toString()] = streamEdgeData.edges.map(
        //       (edge: MxEdgeData) => edge.id
        //     );
        //     this._graph.addEdge(streamEdgeData.edges);
        //     break;
        // }
        // edgesData = this._graph
        //   .getAllEdgesData()
        //   .filter(edge => streamPrefixRegex.test(edge.id as string))
        //   .map(edge => {
        //     delete edge.config.curvature;
        //     return edge;
        //   });
        // this._graph.updateEdge(edgesData);
        // this._graph.updateEdge(this.projectUtilsService.streamPreprocess(edgesData));
        break;
    }
    this._graph.render().then(() => {
      if (topologyAction.elementType === 'device' && topologyAction.type === 'create') {
        this._graph.fitView();
      }
    });
  }

  onDrop(e: DragEvent): void {
    const info = JSON.parse(e.dataTransfer.getData('info'));
    if (info.type === DragType.Device) {
      this.onAddDevice(info.data, this._graph.viewportToCanvas({ x: e.offsetX, y: e.offsetY }));
    } else if (info.type === DragType.Topology) {
      this.onImportTopology(info.data.id, this._graph.viewportToCanvas({ x: e.offsetX, y: e.offsetY }));
    } else if (info.type === DragType.Template) {
      this.onAddTemplate(info.data, this._graph.viewportToCanvas({ x: e.offsetX, y: e.offsetY }));
    }
  }

  onAutoLayout(type: AutoLayoutType): void {
    if (this.devices.length > 0) {
      this.store.dispatch(triggerPageLoading({ isLoading: true }));
      const topologyContainer = document.getElementById('vizion-container');
      this.tabTopologyService
        .newAutoLayout(this.devices, this.links, topologyContainer.clientWidth, topologyContainer.clientHeight, type)
        .then(forceNodeList => {
          this.afterAutoLayout(forceNodeList);
        });
    }
  }

  onAddDevice(deviceProfile: DeviceProfile, position?: { x: number; y: number }): void {
    const deviceDropPosition = {
      pixiDropX: position ? position.x : null,
      pixiDropY: position ? position.y : null
    };
    const dialogConfig = this.projectMode() === ProjectMode.Bom ? smallDialogConfig : exLargeDialogConfig;
    dialogConfig.data = {
      selectType: deviceProfile.deviceType,
      model: deviceProfile.modelName,
      deviceProfileId: deviceProfile.id,
      ...deviceDropPosition,
      editMode: false,
      projectProfile: this.projectProfile(),
      projectMode: this.projectMode()
    };
    dialogConfig.viewContainerRef = this.viewContainerRef;
    this.dialog.open(DeviceAllSettingDialogComponent, dialogConfig);
  }

  onAddLink(): void {
    const dialogConfig = this.projectMode() === ProjectMode.Bom ? smallDialogConfig : mediumDialogConfig;
    dialogConfig.data = { selectedLink: null, projectMode: this.projectMode() };
    dialogConfig.viewContainerRef = this.viewContainerRef;
    this.dialog.open(LinkSettingDialogComponent, dialogConfig);
  }

  updateDeviceInterfaceUsed(deviceId: number, interfaceId: number): void {
    const deviceIndex = findIndex(this.devices, device => device.id === deviceId);
    const interfaceIndex =
      deviceIndex !== -1
        ? findIndex(
            this.devices[deviceIndex].interfaces,
            deviceInterface => deviceInterface.interfaceId === interfaceId
          )
        : -1;
    if (deviceIndex !== -1 && interfaceIndex !== -1) {
      this.devices[deviceIndex].interfaces[interfaceIndex].used = true;
      this.projectBaseStore.updateDevices(this.devices);
    } else {
      this.snackBar.open(
        this.translateService.instant('general.common.unknown'),
        this.translateService.instant('general.button.close'),
        {
          panelClass: ['error']
        }
      );
    }
  }

  onAddStream(): void {
    largeDialogConfig.data = { editMode: false };
    largeDialogConfig.viewContainerRef = this.viewContainerRef;
    this.dialog.open(NewStreamSettingDialogComponent, largeDialogConfig);
  }

  onDeleteStream(topoStream: StreamSetting) {
    const stream = find(this.streams, stream => stream.id === topoStream.id);
    smallDialogConfig.data = stream.id;
    smallDialogConfig.viewContainerRef = this.viewContainerRef;
    this.dialog.open(DeleteStreamSettingDialogComponent, smallDialogConfig);
  }

  onEditStream(topoStream: StreamSetting): void {
    largeDialogConfig.data = {
      applicationConfig: this.projectBaseStore.selectSignal(state => state.trafficDesign.applicationSetting)(),
      selectedStream: topoStream
    };
    largeDialogConfig.viewContainerRef = this.viewContainerRef;
    this.dialog.open(StreamSettingDialogComponent, largeDialogConfig);
  }

  afterAutoLayout($event: ForceNode[]): void {
    this.cncService.updateDeviceCoordinate($event).subscribe({
      next: () => {
        this.projectBaseStore.initProject(true);
        // trigger fit
        this._hasFit.set(false);
      },
      error: error => {
        this.store.dispatch(triggerPageLoading({ isLoading: false }));
        this.errorService.handleError(error);
      }
    });
  }

  onEditDevice(node: Device): void {
    const deviceForEdit = find(this.devices, device => device.id === node.id);
    const dialogConfig = this.projectMode() === ProjectMode.Bom ? smallDialogConfig : exLargeDialogConfig;
    dialogConfig.data = {
      selectType: deviceForEdit?.deviceType,
      device: deviceForEdit,
      editMode: true,
      projectProfile: this.projectProfile(),
      projectMode: this.projectMode()
    };
    dialogConfig.viewContainerRef = this.viewContainerRef;
    this.dialog.open(DeviceAllSettingDialogComponent, dialogConfig);
  }

  onDeleteDevice(nodes: Device[]): void {
    smallDialogConfig.data = nodes.map(node => find(this.devices, device => device.id === node.id));
    smallDialogConfig.viewContainerRef = this.viewContainerRef;
    this.changeDetectorRef.markForCheck();
    this.dialog.open(DeleteDeviceDialogComponent, smallDialogConfig);
  }

  onDeleteLink(edge: Link): void {
    const link = find(this.links, link => link.id === edge.id);
    smallDialogConfig.data = { selectedLink: link };
    smallDialogConfig.viewContainerRef = this.viewContainerRef;
    this.dialog.open(DeleteLinkDialogComponent, smallDialogConfig);
  }

  onEditLink(edge: Link): void {
    const link = find(this.links, link => link.id === edge.id);
    const dialogConfig = this.projectMode() === ProjectMode.Bom ? smallDialogConfig : mediumDialogConfig;
    dialogConfig.data = { selectedLink: link, projectMode: this.projectMode() };
    dialogConfig.viewContainerRef = this.viewContainerRef;
    this.dialog.open(LinkSettingDialogComponent, dialogConfig);
  }

  /**
   * This function has two methods, one is the original method, and the other is the automatic connection method.
   * @param selectedDevices
   */
  onAddTwoDeviceLink(selectedDevices: string[], id: string): void {
    // original method
    // mediumDialogConfig.viewContainerRef = this.viewContainerRef;
    // mediumDialogConfig.data = {
    //   selectedDevices: selectedDevices.map(node => this.devices.find(device => device.id === node.id)),
    // };
    // this.dialog.open(LinkSettingDialogComponent, mediumDialogConfig);

    // automatic connection
    const source = this.devices.find(device => this._extractDeviceSuffix(selectedDevices[0]) === device.id);
    const target = this.devices.find(device => this._extractDeviceSuffix(selectedDevices[1]) === device.id);
    const sourceUnusedPort = source.interfaces.filter(deviceInterface => !deviceInterface.used);
    const targetUnusedPort = target.interfaces.filter(deviceInterface => !deviceInterface.used);
    if (targetUnusedPort.length === 0) {
      forkJoin([
        this._translateLang.lackDeviceInterface.pipe(
          take(1),
          map(translation => translation.replace('{{ device }}', `${target.ipv4.ipAddress}`))
        ),
        this._translateLang.buttonClose.pipe(take(1))
      ]).subscribe(value => {
        this.snackBar.open(value[0], value[1], {
          panelClass: ['error']
        });
      });
      return;
    }
    const findIntersection = (arr1, arr2) => {
      // 使用 filter 方法檢查每個元素是否存在於兩個陣列中
      const intersection = arr1.filter(function (value) {
        return arr2.indexOf(value) !== -1;
      });
      intersection.sort((a, b) => b - a);
      return intersection;
    };
    this.cncService
      .addLink(
        {
          DestinationDeviceId: target.id,
          DestinationInterfaceId: targetUnusedPort[0].interfaceId,
          SourceDeviceId: source.id,
          SourceInterfaceId: sourceUnusedPort[0].interfaceId,
          Speed: findIntersection(sourceUnusedPort[0].supportSpeeds, targetUnusedPort[0].supportSpeeds)[0]
        },
        false
      )
      .pipe(
        switchMap(() => this._translateLang.addLinkSuccess.pipe(take(1))),
        catchError(err => {
          this.errorService.handleError(err);
          return of();
        })
      )
      .subscribe({
        next: value => {
          this.snackBar.open(value, '', {
            duration: 3000
          });
        },
        error: error => this.errorService.handleError(error),
        complete: () => {
          this._graph.removeEdge(id);
          this._graph.render();
        }
      });
  }

  onAddTwoDeviceStream(selectedDevices: MXNodeInterface[]): void {
    const talker = this.devices.find(device => device.id === selectedDevices[0].id);
    const listener = this.devices.find(device => device.id === selectedDevices[1].id);
    if (
      selectedDevices &&
      (!some(talker.interfaces, deviceInterface => deviceInterface.used) ||
        !some(listener.interfaces, deviceInterface => deviceInterface.used))
    ) {
      this.snackBar.open(
        this.translateService.instant('response_handler.add_stream_to_unlinked_device'),
        this.translateService.instant('general.button.close'),
        { panelClass: ['error'] }
      );
      return;
    }
    largeDialogConfig.data = {
      editMode: false,
      selectedDevices: selectedDevices
    };
    largeDialogConfig.viewContainerRef = this.viewContainerRef;
    this.dialog.open(NewStreamSettingDialogComponent, largeDialogConfig);
  }

  onDetailToggle($event: { type: ElementType; id: number }): void {
    // clear all item state is selected
    const selectedEdge = this._graph.getElementByState('edge', 'selected').reduce((acc, curr) => {
      acc[curr.id] = [];
      return acc;
    }, {});
    this._graph.setElementState(selectedEdge);
    const selectedNode = this._graph.getElementByState('node', 'selected').reduce((acc, curr) => {
      acc[curr.id] = [];
      return acc;
    }, {});
    this._graph.setElementState(selectedNode);
    switch ($event.type) {
      case ElementType.NODE:
        this._graph.setElementState({ [`device-${$event.id}`]: ['selected'] });
        break;
      case ElementType.EDGE:
        this._graph.setElementState({ [`link-${$event.id}`]: ['selected'] });
        break;
      case ElementType.STREAM:
        this._selectSubStream($event.id.toString());
        break;
    }
    this._graph.render();
  }

  onSaveTopology(): void {
    this.topology.screenShot([ScreenShotInclude.NODE, ScreenShotInclude.EDGE]).then(value => {
      largeDialogConfig.data = {
        img: value
      };
      largeDialogConfig.viewContainerRef = this.viewContainerRef;
      this.dialog.open(SaveTopologyDialogComponent, largeDialogConfig);
    });
  }

  onImportTopology(id: number, position?: { x: number; y: number }): void {
    this.cncService.getTopology(id).subscribe({
      next: topology => {
        let [left, right, top, bottom] = [
          topology.Devices[0].Coordinate.X,
          topology.Devices[0].Coordinate.X,
          topology.Devices[0].Coordinate.Y,
          topology.Devices[0].Coordinate.Y
        ];
        topology.Devices.forEach(device => {
          left = Math.min(left, device.Coordinate.X);
          right = Math.max(right, device.Coordinate.X);
          top = Math.min(top, device.Coordinate.Y);
          bottom = Math.max(bottom, device.Coordinate.Y);
        });
        const oldMid = {
          x: (left + right) / 2,
          y: (top + bottom) / 2
        };
        exLargeDialogConfig.data = {
          id: id,
          position: position,
          topology: topology,
          oldMid: oldMid
        };
        exLargeDialogConfig.viewContainerRef = this.viewContainerRef;
        this.dialog.open(ImportTopologyDialogComponent, exLargeDialogConfig);
        this.changeDetectorRef.markForCheck();
      },
      error: error => this.errorService.handleError(error)
    });
  }

  onDetailClose() {
    setTimeout(() => {
      // this.topology.resizePIXI();
    }, 200);
  }

  elementFeatureClick(feature: DeviceAction | LinkAction | StreamAction, element: Device | Link | StreamSetting): void {
    if (element) {
      const selectedDevice = element as Device;
      const createEdgeBehavior = this._defaultBehavior.find(behavior => {
        if (typeof behavior === 'object') {
          return behavior.type === MxBehavior.CREATE_EDGE;
        }
        return behavior === MxBehavior.CREATE_EDGE;
      }) as MxCreateEdgeOptions;
      const instance = this._graph.getBehaviorInstance(MxBehavior.CREATE_EDGE) as MxCreateEdge;
      switch (feature) {
        case DeviceAction.EditDevice:
          this.onEditDevice(element as Device);
          break;
        case DeviceAction.CopyDevice:
          // select node after it created
          this.cncService.copyDevice([selectedDevice.id]).subscribe({
            next: value => {
              setTimeout(() => {
                const copyDevices = value.Parameter.DeviceIds.reduce((acc, curr) => {
                  acc[`device-${curr}`] = ['selected'];
                  return acc;
                }, {});
                this._graph.setElementState(copyDevices);
              }, 200);
            }
          });
          break;
        case DeviceAction.DeleteDevice:
          this.onDeleteDevice([element as Device]);
          break;
        case DeviceAction.AddLink:
          // clear menu
          this.elementMenuItem$.next(null);
          createEdgeBehavior.onEnd = e => {
            if (e.source !== e.target) {
              this.onAddTwoDeviceLink([e.source, e.target], e.id);
            }
            return e;
          };
          this._graph.setBehavior(this._defaultBehavior);
          instance.startCreateEdge(`device-${selectedDevice.id}`);
          break;
        case DeviceAction.AddStream:
          // this.topology
          //   .linkNodeToNode($event.element as MXNodeInterface, 0x008787)
          //   .pipe(take(1))
          //   .subscribe(node => {
          //     const listener = this.devices.find(device => device.id === node.id);
          //     if (
          //       node &&
          //       node.id !== selectedDevice.id &&
          //       (listener.deviceType === DeviceType.END_STATION || listener.deviceType === DeviceType.BR_END_STATION)
          //     ) {
          //       this.onAddTwoDeviceStream([selectedDevice, node] as MXNodeInterface[]);
          //     }
          //   });
          break;
        case DeviceAction.OpenDeviceWeb:
          this.openDeviceWeb(element as Device);
          break;
        case LinkAction.EditLink:
          this.onEditLink(element as Link);
          break;
        case LinkAction.DeleteLink:
          this.onDeleteLink(element as Link);
          break;
        case StreamAction.EditStream:
          this.onEditStream(element as StreamSetting);
          break;
        case StreamAction.DeleteStream:
          this.onDeleteStream(element as StreamSetting);
          break;
      }
    }
  }

  onElementClick($event) {
    this.selectElement$.next($event);
  }

  private _selectSubStream(streamId: string): void {
    const selectedStream = this.subStreamMapping[streamId].reduce((acc, curr) => {
      acc[curr] = ['selected'];
      return acc;
    }, {});
    this._graph.setElementState(selectedStream);
  }

  private _changeDeviceCoordinate(devices: { id: number; x: number; y: number }[]): void {
    this.projectBaseStore.updateDeviceReDraw(false);
    this.cncService.updateDeviceCoordinate(devices).subscribe({
      next: () => {
        devices.forEach(node => {
          // need to change project base store state directly
          const oldDeviceIndex = findIndex(this.devices, device => device.id === node.id);
          if (oldDeviceIndex !== -1) {
            this.devices[oldDeviceIndex].coordinate.x = node.x;
            this.devices[oldDeviceIndex].coordinate.y = node.y;
            this.projectBaseStore.updateDeviceReDraw(false);
            // this.projectBaseStore.updateDevices([...this.devices]);
          }
        });
      },
      error: error => {
        this.errorService.handleError(error);
      }
    });
  }

  private checkDeviceNeedAutoLayout(devices: Device[]): boolean {
    const index = devices.findIndex(device => device.coordinate.x === 0 && device.coordinate.y === 0);
    return index !== -1;
  }

  private openDeviceWeb(node: Device) {
    const device = this.devices.find(device => device.id === node.id);
    window.open(`https://${device.ipv4.ipAddress}`);
  }

  private _redrawTopology(): void {
    if (!this._graph) return;

    this._graph.updateNode(
      this.devices.map(device =>
        this.projectUtilsService.parseVizionNode(device, this.deviceProfiles, this._topologyFilter)
      )
    );
    const links = this.links.map(link => this.projectUtilsService.parseVizionEdge(link, this._topologyFilter));
    const streams = this.streams.flatMap(
      stream => this.projectUtilsService.parseVizionStream(stream, this.devices, this._topologyFilter).edges
    );
    this._graph.updateEdge([...links, ...streams]);
    const linkList = this.links.map(link => `link-${link.id}`);
    const streamEdgeIds = Object.values(this.subStreamMapping).flat() as string[];
    // show or hide link
    if (this._topologyFilter.link.status) {
      this._graph.showElement(linkList);
    } else {
      this._graph.hideElement(linkList);
    }
    // show or hide stream
    if (this._topologyFilter.stream.status) {
      this._graph.showElement(streamEdgeIds);
    } else {
      this._graph.hideElement(streamEdgeIds);
    }
    this._graph.render().then(() => {
      if (!this._hasFit()) {
        this._hasFit.set(true);
        // fit to screen when first load
        this.projectComponentStore.updateFitToScreen();
      }
    });
  }

  private onAddTemplate(templateType: TemplateType, position: { x: number; y: number }): void {
    mediumDialogConfig.data = {
      templateType: templateType,
      position: position
    };
    mediumDialogConfig.viewContainerRef = this.viewContainerRef;
    const dialog = this.dialog.open(TopologyTemplateDialogComponent, mediumDialogConfig);
    dialog.afterClosed().subscribe({
      next: result => {
        if (result) {
          const selectedNode = result.reduce((acc, curr) => {
            acc[`device-${curr}`] = ['selected'];
            return acc;
          }, {});
          this._graph.setElementState(selectedNode);
        }
      }
    });
  }

  private _extractDeviceSuffix(str): number {
    const regex = /device-(\d+)/; // Regular expression to capture the numeric suffix
    const match = str.match(regex);
    if (match) {
      return parseInt(match[1]);
    } else {
      return 0;
    }
  }

  onScreenshotTopology(directDownload: boolean) {
    this._graph.setPlugin([]);
    return this.screenshot(directDownload);
  }

  async screenshot(directDownload: boolean) {
    try {
      const dataUrl = await domtoimage.toPng(this.captureElement.nativeElement);
      this._graph.setPlugin([{ type: MxPlugin.FIXED_TOOLBAR }]);

      if (directDownload) {
        const link = document.createElement('a');
        link.href = dataUrl;
        link.download = `Topology.png`;
        link.click();
      } else {
        return dataUrl;
      }
    } catch {
      this.snackBar.open(
        this.translateService.instant('response_handler.download_fail'),
        this.translateService.instant('general.button.close'),
        { panelClass: ['error'] }
      );
    }
  }
}
