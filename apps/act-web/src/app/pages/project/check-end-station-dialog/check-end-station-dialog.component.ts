import { ChangeDetectionStrategy, Component, Inject, OnInit, ViewChild, WritableSignal, signal } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatIconModule } from '@angular/material/icon';
import { MatLegacyButtonModule as MatButtonModule } from '@angular/material/legacy-button';
import {
  MatLegacyDialogModule as MatDialogModule,
  MatLegacyDialogRef as MatDialogRef,
  MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA
} from '@angular/material/legacy-dialog';
import { MatLegacyProgressBarModule as MatProgressBarModule } from '@angular/material/legacy-progress-bar';
import { MatLegacySnackBar as MatSnackBar } from '@angular/material/legacy-snack-bar';
import { MatLegacyTableDataSource as MatTableDataSource } from '@angular/material/legacy-table';
import { MatLegacyTableModule as MatTableModule } from '@angular/material/legacy-table';
import { MatLegacyTabsModule as MatTabsModule } from '@angular/material/legacy-tabs';
import { MatLegacyTabGroup as MatTabGroup } from '@angular/material/legacy-tabs';
import { MatLegacyTooltipModule as MatTooltipModule } from '@angular/material/legacy-tooltip';

import { AppState } from '@act-web/app.service';
import { loadProject, triggerPageLoading } from '@act-web/stores/actions/app.actions';
import { ProjectState } from '@act-web/stores/models/app.models';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { filter } from 'rxjs';

import { ProjectMode } from '@act-web/pages/pages.def';
import { CNCService } from '@act-web/shared/service/cnc-services';
import { ErrorService } from '@act-web/shared/service/error.service';
import { StatusCode } from '@act-web/shared/service/server-status-code.def';
import { ProjectWebSocketService } from '@act-web/shared/service/socket/project-web-socket.service';
import { ErrorRes, OpCode, SuccessRes } from '@act-web/shared/service/socket/web-socket.def';
import { WebSocketService } from '@act-web/shared/service/socket/web-socket.service';
import { TimeoutService } from '@act-web/shared/service/timeout.service';
import { UtilsService } from '@act-web/shared/service/utils.service';
import { ActDeployDeviceList, DeployDeviceUnit, DeviceConfigUnit } from '@act-web/shared/type.def';

import { DeployData, DeployStep, StatusType, TopologyStatus } from './check-end-station.def';
import { DeployDeviceTableComponent } from './deploy-device-table/deploy-device-table.component';
import { TopologyMappingComponent } from './topology-mapping/topology-mapping.component';

@UntilDestroy()
@Component({
  selector: 'app-check-end-station-dialog',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatDialogModule,
    MatButtonModule,
    MatCheckboxModule,
    MatProgressBarModule,
    MatTabsModule,
    MatTableModule,
    MatIconModule,
    MatTooltipModule,
    RouterModule,
    DeployDeviceTableComponent,
    TopologyMappingComponent
  ],
  templateUrl: './check-end-station-dialog.component.html',
  styleUrls: ['./check-end-station-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ProjectWebSocketService]
})
export class CheckEndStationDialogComponent implements OnInit {
  @ViewChild('tabGroup', { static: true }) tabGroup!: MatTabGroup;
  @ViewChild('topologyMapping') topologyMappingComponent: TopologyMappingComponent;

  dataSource: MatTableDataSource<DeviceConfigUnit> = new MatTableDataSource();
  displayedColumns = ['device-ip', 'model-name', 'mac-address', 'serial-number', 'status'];

  currentStep: WritableSignal<DeployStep> = signal(null);
  deviceInfoList: WritableSignal<DeployDeviceUnit[]> = signal([]);
  deployDevices: WritableSignal<DeployDeviceUnit[]> = signal([]);
  canDeploy = signal(TopologyStatus.None);
  progress = signal(0);
  deviceResult = signal({ success: 0, failed: 0 });
  dialogFinish = signal(false);
  monitorProjectId = signal(-1);
  checked = signal(false);

  get statusType() {
    return StatusType;
  }

  get topologyStatus() {
    return TopologyStatus;
  }

  get DeployStep() {
    return DeployStep;
  }

  constructor(
    private dialogRef: MatDialogRef<CheckEndStationDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public dialogData: { hasLoop: boolean },
    private websocketService: WebSocketService,
    private _errorService: ErrorService,
    private _timeoutService: TimeoutService,
    private _snackBar: MatSnackBar,
    private _translateService: TranslateService,
    private cncService: CNCService,
    private utilsService: UtilsService,
    private appState: AppState,
    private router: Router,
    private store: Store,
    private projectWebSocketService: ProjectWebSocketService
  ) {}

  ngOnInit(): void {
    //TODO - remove this timeout
    setTimeout(() => {
      this.store.dispatch(triggerPageLoading({ isLoading: false }));
    }, 500);

    this.changeCurrentStep(this.dialogData.hasLoop ? DeployStep.DetectLoop : DeployStep.SelectDeployDevice);

    this.getDeployDeviceList();
    this.receiveWebSocekt();
  }

  getDialogTitle(): string {
    switch (this.currentStep()) {
      case DeployStep.SelectDeployDevice:
        return 'pages.check_end_station.select_deploy_device';
      case DeployStep.TopologyMapping:
        return 'pages.check_end_station.topology_mapping';
      default:
        return 'pages.check_end_station.deploy';
    }
  }

  changeCurrentStep(currentStep: DeployStep) {
    this.currentStep.set(currentStep);
    this.tabGroup.selectedIndex = currentStep;
  }

  getDeployDeviceList() {
    this.cncService.getDeployDeviceList().subscribe({
      next: (res: ActDeployDeviceList) => {
        const deviceInfoList: DeployDeviceUnit[] = res.DeviceList.map(d => ({
          deviceAlias: d.DeviceAlias,
          deviceId: d.DeviceId,
          deviceIp: d.DeviceIp,
          firmwareVersion: d.FirmwareVersion,
          location: d.Location,
          macAddress: d.MacAddress,
          modelName: d.ModelName,
          serialNumber: d.SerialNumber,
          support: d.Support
        }));
        this.deviceInfoList.set(deviceInfoList);
      },
      error: () => {
        this._snackBar.open(
          this._translateService.instant('response_handler.get_data_fail'),
          this._translateService.instant('general.button.close'),
          {
            panelClass: ['error']
          }
        );
      }
    });
  }

  receiveWebSocekt() {
    this.websocketService
      .receive()
      .pipe(
        filter(jsonResult => jsonResult.OpCode === OpCode.START_DEPLOY),
        untilDestroyed(this)
      )
      .subscribe({
        next: async (jsonResult: SuccessRes | ErrorRes) => {
          if (jsonResult.OpCode === OpCode.PROJECT_CHANGE) {
            // skip ws response for project change
            return;
          }
          if (jsonResult.StatusCode === StatusCode.RUNNING) {
            const successJsonResult = jsonResult as SuccessRes;
            const data = successJsonResult.Data as DeployData;
            this.progress.set(data.Progress);
            const device = this.deviceInfoList().find(device => device.deviceId === data.Id);
            if (device) {
              // has device result
              this.deviceResult.update(result => {
                if (data.Status === 'Success') {
                  result.success += 1;
                }
                if (data.Status === 'Failed') {
                  result.failed += 1;
                }
                return result;
              });
              this.dataSource.data.forEach(item => {
                if (item.deviceId === device.deviceId) {
                  item.status = data.Status;
                  item.errorMessage = data.ErrorMessage;
                }
              });
            }
          } else if (jsonResult.StatusCode === StatusCode.FINISHED) {
            // re init idle timeout
            this._timeoutService.reInitTimeout();
            this.progress.set(100);
            this.monitorProjectId.set((jsonResult as SuccessRes).Data.MonitorProjectId);
            // finish dialog
            this.dialogFinish.set(true);
          } else {
            // Error
            const errorJsonResult = jsonResult as ErrorRes;
            this._errorService.handleWsError({
              errorCode: errorJsonResult.StatusCode,
              parm: errorJsonResult.Parameter,
              errorMsg: errorJsonResult.ErrorMessage
            });
            // re init idle timeout
            this._timeoutService.reInitTimeout();
            // finish dialog
            this.dialogFinish.set(true);
          }
        },
        error: error => {
          this._errorService.handleError(error);
          // re init idle timeout
          this._timeoutService.reInitTimeout();
        }
      });
  }

  updateSelectedDeviceIdsFromDeployList(selectedDevice: DeployDeviceUnit[]) {
    this.deployDevices.set(selectedDevice);
  }

  onDeployStatusChange($event: TopologyStatus): void {
    this.canDeploy.set($event);
  }

  onCancel(): void {
    if (this.currentStep() === DeployStep.TopologyMapping) {
      this.websocketService.send(OpCode.STOP_TOPOLOGY_MAPPING);
    } else if (this.currentStep() === DeployStep.DeployResult) {
      this.websocketService.send(OpCode.STOP_DEPLOY);
      this._timeoutService.reInitTimeout();
    }
    this.dialogRef.close();
  }

  onSkipLoop() {
    this.changeCurrentStep(DeployStep.SelectDeployDevice);
  }

  onTopologyMapping(): void {
    this.changeCurrentStep(DeployStep.TopologyMapping);
  }

  onRetryMapping(): void {
    this.canDeploy.set(TopologyStatus.CantDeploy);
    this.topologyMappingComponent.retryMapping();
  }

  onDeploy(): void {
    this.monitorProjectId.set(-1);
    this.websocketService.send(OpCode.START_DEPLOY, {
      Id: this.deployDevices().map(device => device.deviceId),
      SkipMappingDevice: this.currentStep() === DeployStep.SelectDeployDevice
    });
    this.dataSource.data = this.deployDevices().map(d => ({
      deviceId: d.deviceId,
      deviceIp: d.deviceIp,
      deviceAlias: d.deviceAlias,
      modelName: d.modelName,
      macAddress: d.macAddress,
      serialNumber: d.serialNumber,
      status: StatusType.pending,
      errorMessage: ''
    }));
    this.changeCurrentStep(DeployStep.DeployResult);
    this._timeoutService.clearTimeout();
  }

  onFinshDeploy() {
    if (this.monitorProjectId() == -1 || !this.monitorProjectId) {
      this.dialogRef.close();
      return;
    }

    this.store.dispatch(triggerPageLoading({ isLoading: true }));
    setTimeout(() => {
      this.projectWebSocketService.close();

      const projectState: ProjectState = {
        projectId: this.monitorProjectId(),
        uuid: this.appState.uuid,
        projectName: this.appState.projectName,
        projectMode: ProjectMode.Monitor,
        projectProfile: this.appState.projectProfile
      };
      this.appState.projectId = this.monitorProjectId();
      this.store.dispatch(loadProject({ projectData: projectState }));
      const path = `/pages/project/monitor/${this.monitorProjectId()}`;
      this.router.navigate([path]);
      this.projectWebSocketService.connect(this.monitorProjectId());
      this.store.dispatch(triggerPageLoading({ isLoading: false }));
      this.dialogRef.close();
      this._snackBar.open(this._translateService.instant('response_handler.deploy_success'), '', { duration: 3000 });
    }, 500);
  }
}
