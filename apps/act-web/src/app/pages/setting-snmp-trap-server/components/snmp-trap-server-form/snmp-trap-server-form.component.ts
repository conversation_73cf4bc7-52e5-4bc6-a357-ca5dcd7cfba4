import {
  AfterViewChecked,
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  inject,
  Input,
  Output,
  ViewChild
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, NgForm, ReactiveFormsModule } from '@angular/forms';
import { MatLegacyFormFieldModule as MatFormFieldModule } from '@angular/material/legacy-form-field';
import { MatLegacyInputModule as MatInputModule } from '@angular/material/legacy-input';
import { MatLegacySelectModule as MatSelectModule } from '@angular/material/legacy-select';

import { LetDirective } from '@ngrx/component';
import { TranslateModule } from '@ngx-translate/core';

import { UtilsService } from '@act-web/shared/service/utils.service';
import { ValidatorPattern } from '@act-web/shared/validator/validators';

import { DeviceInfoUnit, ModeType } from '../../models/setting-snmp-trap-server.model';

@Component({
  selector: 'app-snmp-trap-server-form',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    FormsModule,
    LetDirective,
    MatInputModule,
    MatSelectModule
  ],
  templateUrl: './snmp-trap-server-form.component.html',
  styleUrls: ['./snmp-trap-server-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SnmpTrapServerFormComponent implements AfterViewChecked {
  @Input() setting: DeviceInfoUnit;
  @Output() changeFormValid = new EventEmitter<boolean>();

  utilsService = inject(UtilsService);

  @ViewChild('form') form!: NgForm;

  get ValidatorPattern() {
    return ValidatorPattern;
  }

  get hasHost1() {
    return (
      this.setting.hostName1 !== '' ||
      (this.setting.mode1 !== ModeType.NA && this.setting.mode1 !== ModeType.NotSupport) ||
      this.setting.trapCommunity1 !== ''
    );
  }

  get hasHost2() {
    return (
      this.setting.hostName2 !== '' ||
      (this.setting.mode2 !== ModeType.NA && this.setting.mode2 !== ModeType.NotSupport) ||
      this.setting.trapCommunity2 !== ''
    );
  }

  ModeType = ModeType;
  snmpTrapServerOptions = [
    { value: ModeType.NA, text: 'N/A' },
    { value: ModeType.TrapV1, text: 'Trap V1' },
    { value: ModeType.TrapV2c, text: 'Trap V2C' },
    { value: ModeType.InformV2c, text: 'Inform V2c' }
  ];

  ngAfterViewChecked(): void {
    this.form.form.markAsDirty();
    const isValidHost = (host: boolean, mode: string) => !host || mode !== '';

    const valid =
      !this.form.invalid &&
      isValidHost(this.hasHost1, this.setting.mode1) &&
      isValidHost(this.hasHost2, this.setting.mode2);

    this.changeFormValid.emit(valid);
  }
}
