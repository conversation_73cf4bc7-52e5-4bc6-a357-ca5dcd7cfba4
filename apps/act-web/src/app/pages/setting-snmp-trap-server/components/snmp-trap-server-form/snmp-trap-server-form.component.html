<div class="section">
  <div class="title">{{ 'general.common.selected_ip' | translate }}</div>
  <div>{{ utilsService.getDeviceIpAlias(setting.deviceIp, setting.deviceAlias) }}</div>
</div>

<div class="section">
  <div class="title">{{ 'pages.snmp_trap_server.snmp_trap_server_setting' | translate }}</div>
  <form #form="ngForm" id="snmp-trap-server-form" novalidate *ngrxLet="setting as data">
    <div>
      <mat-form-field>
        <mat-label>{{ 'pages.snmp_trap_server.host_ip' | translate }} 1</mat-label>
        <input
          id="input-host-ip-1"
          [required]="hasHost1"
          matInput
          title="hostIp1"
          name="hostIp1"
          maxlength="15"
          #hostIp1="ngModel"
          [(ngModel)]="data.hostName1"
          [pattern]="ValidatorPattern.IPADDR_REGEX"
        />
        <mat-error *ngIf="hostIp1.hasError('pattern')"> {{ 'validators.invalid_ip_address' | translate }}</mat-error>
        <mat-error *ngIf="hostIp1.hasError('required')"> {{ 'validators.required' | translate }}</mat-error>
        <mat-error *ngIf="hostIp1.value === data.hostName2">
          {{ 'validators.duplicate_ip_address' | translate }}
        </mat-error>
      </mat-form-field>
    </div>

    <div>
      <mat-form-field *ngIf="data.mode1 !== ModeType.NotSupport">
        <mat-select
          [required]="hasHost1"
          id="select-mode-1"
          placeholder="{{ 'pages.snmp_trap_server.mode' | translate }}"
          name="mode1"
          #mode1="ngModel"
          [(ngModel)]="data.mode1"
        >
          <ng-container *ngFor="let option of snmpTrapServerOptions">
            <mat-option id="option-mode-1-{{ option.value }}" [value]="option.value">
              {{ option.text }}
            </mat-option>
          </ng-container>
        </mat-select>

        <mat-error *ngIf="mode1.touched && mode1.invalid">
          {{ 'validators.required' | translate }}
        </mat-error>
      </mat-form-field>
    </div>

    <div>
      <mat-form-field>
        <mat-label>{{ 'pages.snmp_trap_server.trap_community' | translate }}</mat-label>
        <input
          id="input-trap-community-1"
          matInput
          [required]="hasHost1"
          title="trapCommunity1"
          name="trapCommunity1"
          type="text"
          minlength="4"
          maxlength="32"
          #trapCommunity1="ngModel"
          [(ngModel)]="data.trapCommunity1"
        />
        <mat-hint align="end">{{ trapCommunity1?.control?.value?.length }} / 32</mat-hint>
        <mat-hint align="begin">{{ 'validators.require_min_length' | translate : { number: 4 } }}</mat-hint>
        <mat-error align="begin" *ngIf="trapCommunity1.hasError('minlength')"
          >{{ 'validators.require_min_length' | translate : { number: 4 } }}
        </mat-error>
        <mat-error *ngIf="trapCommunity1.hasError('required')"> {{ 'validators.required' | translate }}</mat-error>
      </mat-form-field>
    </div>

    <div>
      <mat-form-field>
        <mat-label>{{ 'pages.snmp_trap_server.host_ip' | translate }} 2</mat-label>
        <input
          id="input-host-ip-2"
          [required]="hasHost2"
          matInput
          title="hostIp2"
          name="hostIp2"
          maxlength="15"
          #hostIp2="ngModel"
          [(ngModel)]="data.hostName2"
          [pattern]="ValidatorPattern.IPADDR_REGEX"
        />
        <mat-error *ngIf="hostIp2.hasError('pattern')"> {{ 'validators.invalid_ip_address' | translate }}</mat-error>
        <mat-error *ngIf="hostIp2.hasError('required')"> {{ 'validators.required' | translate }}</mat-error>
      </mat-form-field>
    </div>

    <div>
      <mat-form-field *ngIf="data.mode2 !== ModeType.NotSupport">
        <mat-select
          [required]="hasHost2"
          id="select-mode-2"
          placeholder="{{ 'pages.snmp_trap_server.mode' | translate }}"
          name="mode2"
          #mode2="ngModel"
          [(ngModel)]="data.mode2"
        >
          <ng-container *ngFor="let option of snmpTrapServerOptions">
            <mat-option id="option-mode-2-{{ option.value }}" [value]="option.value">
              {{ option.text }}
            </mat-option>
          </ng-container>
        </mat-select>

        <mat-error *ngIf="mode2.touched && mode2.invalid">
          {{ 'validators.required' | translate }}
        </mat-error>
      </mat-form-field>
    </div>

    <div>
      <mat-form-field>
        <mat-label>{{ 'pages.snmp_trap_server.trap_community' | translate }}</mat-label>
        <input
          id="input-trap-community-2"
          matInput
          [required]="hasHost2"
          title="trapCommunity2"
          name="trapCommunity2"
          type="text"
          minlength="4"
          maxlength="32"
          #trapCommunity2="ngModel"
          [(ngModel)]="data.trapCommunity2"
        />
        <mat-hint align="end">{{ trapCommunity2?.control?.value?.length }} / 32</mat-hint>
        <mat-hint align="begin">{{ 'validators.require_min_length' | translate : { number: 4 } }}</mat-hint>
        <mat-error align="begin" *ngIf="trapCommunity2.hasError('minlength')"
          >{{ 'validators.require_min_length' | translate : { number: 4 } }}
        </mat-error>
        <mat-error *ngIf="trapCommunity2.hasError('required')"> {{ 'validators.required' | translate }}</mat-error>
      </mat-form-field>
    </div>
  </form>
</div>
