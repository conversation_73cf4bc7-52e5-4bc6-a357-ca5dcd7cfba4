<div class="app-table-wrapper">
  <table id="table-snmp-trap-server-table" #tableSort="matSort" [dataSource]="dataSource" mat-table matSort>
    <ng-container matColumnDef="select">
      <th *matHeaderCellDef mat-header-cell></th>
      <td id="td-select-{{ i }}" *matCellDef="let row; let i = index" mat-cell>
        <mat-radio-button
          [value]="row.deviceId"
          [checked]="selectedDeviceId() === row.deviceId"
          (change)="updateSelectedDevices(row.deviceId)"
          color="primary"
        >
        </mat-radio-button>
      </td>
    </ng-container>

    <ng-container matColumnDef="edit">
      <th *matHeaderCellDef mat-header-cell></th>
      <td id="td-edit-{{ i }}" *matCellDef="let row; let i = index" mat-cell>
        <mat-icon
          class="table-icon-action"
          (click)="onEdit(row)"
          matTooltip="{{ 'general.common_action.edit' | translate }}"
          attr.id="edit_{{ row.id }}"
          >edit</mat-icon
        >
      </td>
    </ng-container>

    <ng-container matColumnDef="device-ip">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        <ng-container>{{ 'general.common.ip_address_alias' | translate }}</ng-container>
      </th>
      <td id="td-device-ip-{{ i }}" *matCellDef="let row; let i = index" mat-cell>
        <ng-container>{{ utilsService.getDeviceIpAlias(row.deviceIp, row.deviceAlias) }}</ng-container>
      </td>
    </ng-container>

    <ng-container matColumnDef="host-ip-1">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        <ng-container>{{ 'pages.snmp_trap_server.host_ip' | translate }} 1</ng-container>
      </th>
      <td id="td-host-ip-1-{{ i }}" *matCellDef="let row; let i = index" mat-cell>
        <ng-container>{{ row.hostName1 }}</ng-container>
      </td>
    </ng-container>

    <ng-container matColumnDef="mode-1">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        <ng-container>{{ 'pages.snmp_trap_server.mode' | translate }}</ng-container>
      </th>
      <td id="td-mode-1-{{ i }}" *matCellDef="let row; let i = index" mat-cell>
        <ng-container *ngIf="row.mode1 !== ModeType.NotSupport; else notSupported">{{ row.mode1 }}</ng-container>
        <ng-template #notSupported>
          <span class="disabled-color">{{ 'general.common.not_support' | translate }}</span>
        </ng-template>
      </td>
    </ng-container>

    <ng-container matColumnDef="trap-community-1">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        <ng-container>{{ 'pages.snmp_trap_server.trap_community' | translate }}</ng-container>
      </th>
      <td id="td-trap-community-1-{{ i }}" *matCellDef="let row; let i = index" mat-cell>
        <ng-container>{{ row.trapCommunity1 }}</ng-container>
      </td>
    </ng-container>

    <ng-container matColumnDef="host-ip-2">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        <ng-container>{{ 'pages.snmp_trap_server.host_ip' | translate }} 2</ng-container>
      </th>
      <td id="td-host-ip-2-{{ i }}" *matCellDef="let row; let i = index" mat-cell>
        <ng-container>{{ row.hostName2 }}</ng-container>
      </td>
    </ng-container>

    <ng-container matColumnDef="mode-2">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        <ng-container>{{ 'pages.snmp_trap_server.mode' | translate }}</ng-container>
      </th>
      <td id="td-mode-2-{{ i }}" *matCellDef="let row; let i = index" mat-cell>
        <ng-container *ngIf="row.mode2 !== ModeType.NotSupport; else notSupported">{{ row.mode2 }}</ng-container>
        <ng-template #notSupported>
          <span class="disabled-color">{{ 'general.common.not_support' | translate }}</span>
        </ng-template>
      </td>
    </ng-container>

    <ng-container matColumnDef="trap-community-2">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        <ng-container>{{ 'pages.snmp_trap_server.trap_community' | translate }}</ng-container>
      </th>
      <td id="td-trap-community-2-{{ i }}" *matCellDef="let row; let i = index" mat-cell>
        <ng-container>{{ row.trapCommunity2 }}</ng-container>
      </td>
    </ng-container>

    <tr *matHeaderRowDef="displayedColumns; sticky: true" mat-header-row></tr>
    <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
  </table>
</div>
<mat-paginator
  #tablePaginator
  class="disableChangePage"
  [hidePageSize]="true"
  [showFirstLastButtons]="false"
></mat-paginator>
