import {
  ChangeDetectionStrategy,
  Component,
  Output,
  EventEmitter,
  Input,
  SimpleChanges,
  ViewChild,
  AfterViewInit,
  OnChanges,
  inject,
  WritableSignal,
  signal
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';

import { TranslateModule } from '@ngx-translate/core';

import { UtilsService } from '@act-web/shared/service/utils.service';
import { SharedModule } from '@act-web/shared/shared.module';
import { convertIpToNumber } from '@act-web/shared/util/convertIp';

import { DeviceInfoUnit, ModeType } from '../../models/setting-snmp-trap-server.model';

@Component({
  selector: 'app-snmp-trap-server-table',
  standalone: true,
  imports: [CommonModule, SharedModule, TranslateModule],
  templateUrl: './snmp-trap-server-table.component.html',
  styleUrls: ['./snmp-trap-server-table.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SnmpTrapServerTableComponent implements OnChanges, AfterViewInit {
  @Input() selectDeviceIds: number[] = [];
  @Input() deviceInfoList: DeviceInfoUnit[] = [];
  @Output() editDevice = new EventEmitter<DeviceInfoUnit>();
  @Output() updateSelectedDeviceIdsFromTable = new EventEmitter<number[]>();

  utilsService = inject(UtilsService);
  @ViewChild(MatSort) sort: MatSort;
  @ViewChild('tablePaginator') tablePaginator: MatPaginator;
  dataSource = new MatTableDataSource<DeviceInfoUnit>();
  displayedColumns: string[] = [
    'select',
    'edit',
    'device-ip',
    'host-ip-1',
    'mode-1',
    'trap-community-1',
    'host-ip-2',
    'mode-2',
    'trap-community-2'
  ];
  selectedDeviceId: WritableSignal<number | null> = signal(null);
  ModeType = ModeType;

  ngAfterViewInit() {
    this.setupTableSetting();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['selectDeviceIds']?.currentValue) {
      this.updateSeletedFromTopology();
    }

    if (changes['deviceInfoList']?.currentValue) {
      this.dataSource.data = changes['deviceInfoList']?.currentValue;
    }
  }

  setupTableSetting() {
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.tablePaginator;
    this.dataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'device-ip':
          return convertIpToNumber(item['deviceIp']);
        case 'host-ip':
          return item['hostName'];
        case 'trap-communit':
          return item['trapCommunity'];
        default:
          return item[property];
      }
    };
  }

  updateSelectedDevices(id: number) {
    this.selectedDeviceId.set(id);
    this.updateSelectedDeviceIdsFromTable.emit([id]);
  }

  updateSeletedFromTopology() {
    this.selectedDeviceId.set(this.selectDeviceIds[0]);
  }

  onEdit(data: DeviceInfoUnit) {
    this.editDevice.emit(data);
  }
}
