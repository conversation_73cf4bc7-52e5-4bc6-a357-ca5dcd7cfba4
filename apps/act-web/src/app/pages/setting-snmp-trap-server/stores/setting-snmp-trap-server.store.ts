import { Injectable } from '@angular/core';
import { MatLegacySnackBar as MatSnackBar } from '@angular/material/legacy-snack-bar';
import { ComponentStore } from '@ngrx/component-store';
import { TranslateService } from '@ngx-translate/core';
import { catchError, EMPTY, Observable, switchMap, tap } from 'rxjs';
import { SnmpTrapServerState, ApiDeviceInfoUnit, DeviceInfoUnit, ModeType } from '../models/setting-snmp-trap-server.model';
import { SnmpTrapServerService } from '../services/setting-snmp-trap-server.service';
import { sortByIp } from '@act-web/shared/util/convertIp';
import { ApiStatusStateEnum } from '@act-web/shared/general-display-type';
import { ProjectMode } from '@act-web/pages/pages.def';
import { Router } from '@angular/router';
import { WebSocketService } from '@act-web/shared/service/socket/web-socket.service';

const SnmpTrapServerState: SnmpTrapServerState = {
  projectMode:ProjectMode.Design,
  getDataApiStatus: ApiStatusStateEnum.none,
  editDataApiStatus: ApiStatusStateEnum.none,
  deviceInfoList: [],
};

@Injectable({
  providedIn: 'any'
})
export class SnmpTrapServerStore extends ComponentStore<SnmpTrapServerState> {
  constructor(
    private service: SnmpTrapServerService,
    private snackBar: MatSnackBar,
    private translate: TranslateService,
    private router:Router    ,private webSocketService :WebSocketService

  ) {
    super(SnmpTrapServerState);
  }

  updateDeviceInfoList = this.updater((state, payload: ApiDeviceInfoUnit[]) => {
    const deviceInfoList = payload.map(d => ({
      deviceId: d.DeviceId,
      deviceIp: d.DeviceIp,
      deviceAlias: d.DeviceAlias,
      support: d.Support,
      hostName1: d.HostList[0]?.HostName || '',
      mode1: d.HostList[0]?.Mode || ModeType.NA,
      trapCommunity1: d.HostList[0]?.TrapCommunity || '',
      hostName2: d.HostList[1]?.HostName || '',
      mode2: d.HostList[1]?.Mode || ModeType.NA,
      trapCommunity2: d.HostList[1]?.TrapCommunity || '',
    })).filter(d => d.support);
    return { ...state, deviceInfoList: sortByIp(deviceInfoList) };
  });

  updateProjectMode = this.updater((state, projectMode: ProjectMode) => ({...state,projectMode}))
  updateGetDataApiStatus = this.updater((state, apiStatus: ApiStatusStateEnum) => ({...state,getDataApiStatus:apiStatus}))
  updateEditDataApiStatus = this.updater((state, apiStatus: ApiStatusStateEnum) => ({...state,editDataApiStatus:apiStatus}))

  getProjectMode  = ()=>{
    const projectMode = this.router.url.includes('monitor') ? ProjectMode.Monitor : ProjectMode.Design;
    this.updateProjectMode(projectMode)
  }

  getSnmpTrapSettings = this.effect((origin$: Observable<void>) =>
    origin$
      .pipe(switchMap(() =>  this.service.getSnmpTrapSettings().pipe(
        tap((d) => {
          this.updateGetDataApiStatus(ApiStatusStateEnum.success)
          this.updateDeviceInfoList(d)}),
        catchError(() => {
          this.handleSnackBar('response_handler.get_data_fail', false);
          this.updateGetDataApiStatus(ApiStatusStateEnum.fail)
          return EMPTY;
        })
      )))
  );

  editSnmpTrapSettings = this.effect((origin$: Observable<DeviceInfoUnit[]>) =>
    origin$.pipe(switchMap((payload) => {
      this.updateEditDataApiStatus(ApiStatusStateEnum.loading)
      const data = payload.map(d => ({
        DeviceId: d.deviceId,
        DeviceIp: d.deviceIp,
        DeviceAlias: d.deviceAlias,
        Support:d.support,
        HostList:[{HostName:d.hostName1,Mode:d.mode1,TrapCommunity:d.trapCommunity1},{HostName:d.hostName2,Mode:d.mode2,TrapCommunity:d.trapCommunity2}].filter(h=>h.HostName)
      }))
      return this.service.editSnmpTrapSettings({DeviceSnmpTrapSettingList:data}).pipe(
        tap(() => {
          this.updateEditDataApiStatus(ApiStatusStateEnum.success)

          if(this.state().projectMode === ProjectMode.Design){
            this.getSnmpTrapSettings()
            this.handleSnackBar('response_handler.global_success', true)
          }
        }),
        catchError(() => {
          this.handleSnackBar('response_handler.global_fail', false);
          if(this.state().projectMode === ProjectMode.Monitor){
            this.webSocketService.startMonior();
          }
          return EMPTY;
        })
      )
    }))
  );

  handleSnackBar = (message: string, isSuccess: boolean) => {
    const translatedMessage = this.translate.instant(message);
    if (isSuccess) {
      this.snackBar.open(translatedMessage, '', { duration: 3000 });
    } else {
      this.snackBar.open(translatedMessage, this.translate.instant('general.button.close'), {
        panelClass: ['error']
      });
    }
  };
}
