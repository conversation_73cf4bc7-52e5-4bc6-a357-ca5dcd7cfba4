import { ProjectMode } from '@act-web/pages/pages.def';
import { ApiStatusStateEnum } from '@act-web/shared/general-display-type';

export interface SnmpTrapServerState {
  projectMode: ProjectMode;
  getDataApiStatus: ApiStatusStateEnum;
  editDataApiStatus: ApiStatusStateEnum;
  deviceInfoList: DeviceInfoUnit[];
}

export interface DeviceInfoUnit {
  deviceId: number;
  deviceIp: string;
  deviceAlias: string;
  support: boolean;
  hostName1: string;
  mode1: ModeType;
  trapCommunity1: string;
  hostName2: string;
  mode2: ModeType;
  trapCommunity2: string;
}

export interface ApiDeviceInfo {
  DeviceSnmpTrapSettingList: ApiDeviceInfoUnit[];
}

export interface ApiDeviceInfoUnit {
  DeviceId: number;
  DeviceIp: string;
  DeviceAlias: string;
  Support: boolean;
  HostList: HostList[];
}

export interface HostList {
  HostName: string;
  Mode: ModeType;
  TrapCommunity: string;
}

export enum ModeType {
  NA = '',
  TrapV1 = 'TrapV1',
  TrapV2c = 'TrapV2c',
  InformV2c = 'InformV2c',
  NotSupport = 'NotSupport'
}
