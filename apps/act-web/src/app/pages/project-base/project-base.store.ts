import { Injectable, On<PERSON><PERSON>roy } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { MatLegacySnackBar as MatSnackBar } from '@angular/material/legacy-snack-bar';

import { triggerPageLoading, updateProfile, updateUUID } from '@act-web/stores/actions/app.actions';
import { updateDeviceProfile } from '@act-web/stores/actions/deviceProfile.actions';
import { CycleSetting, ProjectSetting, TrafficDesign } from '@act-web/stores/models/project.state';
import { selectProjectState } from '@act-web/stores/reducers/app.reducer';
import { selectDeviceProfile } from '@act-web/stores/selectors/deviceProfile.selector';
import { ComponentStore, tapResponse } from '@ngrx/component-store';
import { Store, select } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { Observable, Subscription, forkJoin, map, merge, switchMap, take } from 'rxjs';

import {
  ActCycleSetting,
  ActDevice,
  ActLicenseProfile,
  ActLink,
  ActProject,
  ActProjectSetting,
  ActSimpleTopology,
  ActTrafficDesign
} from '@act-web/shared/act-type.def';
import { StreamSetting, projectTypeBase } from '@act-web/shared/general-display-type';
import { CNCService } from '@act-web/shared/service/cnc-services';
import { ErrorService } from '@act-web/shared/service/error.service';
import { ProjectWebSocketService } from '@act-web/shared/service/socket/project-web-socket.service';
import { OpCode, PatchUpdateActionType, SuccessRes } from '@act-web/shared/service/socket/web-socket.def';
import { WebSocketService } from '@act-web/shared/service/socket/web-socket.service';
import { UtilsService } from '@act-web/shared/service/utils.service';
import {
  ApiListUnit,
  ApiSkuUnit,
  BaselineUnit,
  ComputedResult,
  Device,
  DeviceProfile,
  Link,
  SimpleTopology
} from '@act-web/shared/type.def';

import { ProjectUtilsService } from '../project/project-utils.service';
import { TopologyAction } from './project-base.def';

interface ProjectBaseState {
  projectId: number;
  profile: ActLicenseProfile;
  applicationMode: projectTypeBase;
  devices: Device[];
  deviceReDraw: boolean;
  links: Link[];
  streams: StreamSetting[];
  deviceProfiles: DeviceProfile[];
  // use to do something after project init
  projectInitialized: number;
  projectSetting: ProjectSetting;
  trafficDesign: TrafficDesign;
  deviceConfig: object;
  cycleSetting: CycleSetting;
  simpleTopologies: SimpleTopology[];
  managementInterfaceIcon: string;
  computeResult: ComputedResult;
  topologyAction: TopologyAction;
  baselineList: BaselineUnit[];
  skuQuantitiesMap: { [key: string]: ApiSkuUnit };
}

@Injectable()
export class ProjectBaseStore extends ComponentStore<ProjectBaseState> implements OnDestroy {
  private _initSubscription = new Subscription();
  constructor(
    private cncService: CNCService,
    private errorService: ErrorService,
    private projectUtilsService: ProjectUtilsService,
    private utilsService: UtilsService,
    private domSanitizer: DomSanitizer,
    private projectWebSocketService: ProjectWebSocketService,
    private webSocketService: WebSocketService,
    private store: Store,
    private _snackBar: MatSnackBar,
    private _translateService: TranslateService
  ) {
    super({
      projectId: null,
      profile: null,
      applicationMode: projectTypeBase.NEW_CLASS,
      devices: [],
      deviceReDraw: true,
      links: [],
      streams: [],
      deviceProfiles: [],
      projectInitialized: 0,
      projectSetting: null,
      trafficDesign: null,
      deviceConfig: null,
      cycleSetting: null,
      simpleTopologies: [],
      managementInterfaceIcon: '',
      computeResult: null,
      topologyAction: null,
      baselineList: [],
      skuQuantitiesMap: {}
    });
    this._initSubscription.add(
      this.store.pipe(select(selectProjectState)).subscribe(projectState => {
        this.updateProjectId(projectState.projectId);
        this.updateProjectProfile(projectState.projectProfile);
      })
    );
    // topology change will use global websocket
    this._initSubscription.add(
      merge(this.projectWebSocketService.receive(), this.webSocketService.receive()).subscribe({
        next: (jsonResult: SuccessRes) => {
          switch (jsonResult.OpCode) {
            case OpCode.PROJECT_CHANGE:
              this._projectChange(jsonResult.Path, jsonResult.Data, jsonResult.Action);
              break;
          }
        },
        error: error => {
          this.errorService.handleError(error);
        }
      })
    );
    this._initSubscription.add(
      this.store.select(selectDeviceProfile).subscribe(result => {
        this.updateDeviceProfiles(result.deviceProfiles);
      })
    );
  }

  ngOnDestroy(): void {
    this._initSubscription.unsubscribe();
  }

  readonly updateProjectId = this.updater((state, projectId: number) => {
    return {
      ...state,
      projectId: projectId
    };
  });

  readonly updateProjectProfile = this.updater((state, profile: ActLicenseProfile) => ({ ...state, profile }));

  readonly _projectChange = (path: string, data, patchActionType: PatchUpdateActionType): void => {
    this.state$.pipe(take(1)).subscribe(state => {
      const projectId = state.projectId;
      const destinationPathList = path.replace(`Projects/${projectId}/`, '').split('/');
      switch (destinationPathList[0]) {
        case 'Projects':
          this._refreshProject(data);
          break;
        case 'Devices':
          switch (patchActionType) {
            case PatchUpdateActionType.CREATE:
              this._createDeviceFromSocket(data);
              break;
            case PatchUpdateActionType.DELETE:
              this._deleteDeviceFromSocket(Number(destinationPathList[1]));
              break;
            case PatchUpdateActionType.UPDATE:
              this._updateDeviceFromSocket(data);
              break;
          }
          break;
        case 'Links':
          switch (patchActionType) {
            case PatchUpdateActionType.CREATE:
              this._createLinkFromSocket(data);
              break;
            case PatchUpdateActionType.DELETE:
              this._deleteLinkFromSocket(Number(destinationPathList[1]));
              break;
            case PatchUpdateActionType.UPDATE:
              this._updateLinkFromSocket(data);
              break;
          }
          break;
        case 'Streams':
          // switch (patchActionType) {
          //   case PatchUpdateActionType.CREATE:
          //     this._createStreamFromSocket(data);
          //     break;
          //   case PatchUpdateActionType.DELETE:
          //     this._deleteStreamFromSocket(Number(destinationPathList[1]));
          //     break;
          //   case PatchUpdateActionType.UPDATE:
          //     this._updateStreamFromSocket(data);
          //     break;
          // }
          break;
        case 'ProjectSetting':
          this._updateProjectSettingFromSocket(data);
          break;
        case 'DeviceConfig':
          this._updateDeviceConfigFromSocket(data);
          break;
        case 'CycleSetting':
          this._updateCycleSettingFromSocket(data);
          break;
        case 'Topologies':
          switch (patchActionType) {
            case PatchUpdateActionType.CREATE:
              this._createTopologyFromSocket(data);
              break;
          }
          break;
        case 'TrafficDesign':
          switch (patchActionType) {
            case PatchUpdateActionType.CREATE:
            case PatchUpdateActionType.UPDATE:
            case PatchUpdateActionType.DELETE:
              this._trafficDesignFromSocket(data);
              break;
          }
          break;
        case 'DeviceProfiles':
          // create device profile from auto scan -> auto probe
          switch (patchActionType) {
            case PatchUpdateActionType.CREATE:
              this._createDeviceProfileFromSocket(data);
              break;
            case PatchUpdateActionType.DELETE:
              this._deleteDeviceProfileFromSocket(Number(destinationPathList[1]));
              break;
          }
          break;
      }
    });
  };

  readonly initDeviceProfile = this.effect((param$: Observable<boolean>) =>
    param$.pipe(
      switchMap(() =>
        forkJoin([this.cncService.getSimpleTopologies(), this.cncService.getActIcon()]).pipe(
          tapResponse(
            result => {
              // init act icon for management Interface
              this._updateManagementInterfaceIcon(result[1]);
              this.initProject(true);
              // init simple topology and act icon
              forkJoin(result[0].map(simpleTopology => this.cncService.getTopologyIcon(simpleTopology.Id))).subscribe({
                next: icons => {
                  const simpleTopologies = result[0].map(
                    (simpleTopology, index) =>
                      new SimpleTopology({
                        simpleTopology,
                        icon: icons[index]
                      })
                  );
                  this.updateSimpleTopologies(simpleTopologies);
                },
                error: error => this.errorService.handleError(error)
              });
            },
            error => {
              this.store.dispatch(triggerPageLoading({ isLoading: false }));
              this.errorService.handleError(error);
            }
          )
        )
      )
    )
  );

  readonly initProject = this.effect((param$: Observable<boolean>) =>
    param$.pipe(
      switchMap(() =>
        this.cncService.getProject().pipe(
          tapResponse(
            (project: ActProject) => {
              this.store.dispatch(triggerPageLoading({ isLoading: false }));
              this.updateProjectProfile(project.Profile);
              this.store.dispatch(updateProfile({ profile: project.Profile }));
              this.store.dispatch(updateUUID({ uuid: project.UUID }));

              let deviceProfiles = [];
              this.state$.pipe(take(1)).subscribe(state => (deviceProfiles = state.deviceProfiles));
              const deviceData = this.projectUtilsService.parseDevice(project.Devices, deviceProfiles);
              this.updateDevices(deviceData);
              const linksData = this.projectUtilsService.parseLink(project.Links, deviceData);
              this.updateLinks(linksData);
              // const streamsData = this.projectUtilsService.parseStream(project.Streams);
              // this.updateStreams(streamsData);
              this.updateApplicationMode(project.ProjectType);
              // update project device config
              this.updateDeviceConfig(project.DeviceConfig);
              // update project setting
              this.updateProjectSetting(project.ProjectSetting);
              // update project cycleSetting
              this.updateCycleSetting(project.CycleSetting);
              // update compute result
              this.updateComputeResult(
                this.projectUtilsService.parseComputeResult(project.ComputedResult, deviceProfiles)
              );
              this.updateSkuQuantitiesMap(project.SkuQuantitiesMap);
              // update traffic design
              this.initTrafficDesign(project.TrafficDesign);
              this.updateProjectInitialized(this.state().projectInitialized + 1);
              this.getBaselineList();
            },
            error => {
              this.store.dispatch(triggerPageLoading({ isLoading: false }));
              this.errorService.handleError(error);
            }
          )
        )
      )
    )
  );

  readonly updateProjectInitialized = this.updater((state, projectInitialized: number) => {
    return {
      ...state,
      projectInitialized: projectInitialized
    };
  });

  readonly updateApplicationMode = this.updater((state, applicationMode: projectTypeBase) => {
    return {
      ...state,
      applicationMode: applicationMode
    };
  });

  readonly updateDevices = this.updater((state, devices: Device[]) => {
    devices.sort((a, b) => this.utilsService.ipSortCallback(a.ipv4.ipAddress, b.ipv4.ipAddress, 'asc'));
    return {
      ...state,
      devices: devices
    };
  });

  readonly updateDeviceReDraw = this.updater((state, deviceReDraw: boolean) => {
    return {
      ...state,
      deviceReDraw: deviceReDraw
    };
  });

  readonly updateLinks = this.updater((state, links: Link[]) => {
    return {
      ...state,
      links: links
    };
  });

  readonly updateStreams = this.updater((state, streams: StreamSetting[]) => {
    return {
      ...state,
      streams: streams
    };
  });

  readonly updateSimpleTopologies = this.updater((state, simpleTopologies: SimpleTopology[]) => {
    return {
      ...state,
      simpleTopologies: simpleTopologies
    };
  });

  readonly updateDeviceProfiles = this.updater((state, deviceProfiles: DeviceProfile[]) => {
    return {
      ...state,
      deviceProfiles: deviceProfiles
    };
  });

  readonly updateTopologyAction = this.updater((state, topologyAction: TopologyAction) => {
    return {
      ...state,
      topologyAction: topologyAction
    };
  });

  readonly updateDeviceConfig = this.updater((state, deviceConfig: object) => {
    return {
      ...state,
      deviceConfig: deviceConfig
    };
  });

  readonly updateProjectSetting = this.updater((state, projectSetting: ActProjectSetting) => {
    return {
      ...state,
      projectSetting: new ProjectSetting(projectSetting)
    };
  });

  readonly updateSkuQuantitiesMap = this.updater((state, skuQuantitiesMap: { [key: string]: ApiSkuUnit }) => {
    return {
      ...state,
      skuQuantitiesMap
    };
  });

  getBaselineList() {
    this.cncService.getBaselineList().subscribe({
      next: d => {
        this.updateBaselineList(d.NetworkBaselineList);
      }
    });
  }

  readonly updateBaselineList = this.updater((state, payload: ApiListUnit[]) => {
    const DefaultBaselineName = 'CURRENT';
    const baselineList: BaselineUnit[] = payload.map(d => {
      return {
        id: d.Id,
        name: d.Name,
        date: d.Date,
        projectId: d.ProjectId,
        createdUser: d.CreatedUser,
        totalPrice: '',
        devices: [],
        skuQuantities: []
      };
    });
    const defaultBaseline = baselineList.find(d => d.name === DefaultBaselineName);
    const sortedBaselineList = baselineList.filter(d => d.name !== DefaultBaselineName).sort((a, b) => b.date - a.date);
    return { ...state, baselineList: [defaultBaseline, ...sortedBaselineList] };
  });

  readonly initTrafficDesign = this.updater((state, trafficDesign: ActTrafficDesign) => {
    const traffic = new TrafficDesign(trafficDesign);
    return {
      ...state,
      trafficDesign: traffic,
      streams: traffic.mode === 'Advanced' ? traffic.streamSetting : []
    };
  });

  readonly updateTrafficDesign = this.updater((state, trafficDesign: ActTrafficDesign) => {
    const traffic = new TrafficDesign(trafficDesign);
    // update stream when traffic mode is advanced
    if (traffic.mode === 'Advanced') {
      this._updateStreamFromSocket(traffic.streamSetting);
    } else {
      this._updateStreamFromSocket([]);
    }
    return {
      ...state,
      trafficDesign: traffic,
      streams: traffic.streamSetting
    };
  });

  readonly updateCycleSetting = this.updater((state, cycleSetting: ActCycleSetting) => {
    return {
      ...state,
      cycleSetting: new CycleSetting(cycleSetting)
    };
  });

  readonly updateComputeResult = this.updater((state, computeResult: ComputedResult) => {
    computeResult.streamViewResults.sort((a, b) => a.StreamName.localeCompare(b.StreamName));
    return {
      ...state,
      computeResult: computeResult
    };
  });

  private _refreshProject = (project: ActProject): void => {
    this.state$.pipe(take(1)).subscribe(state => {
      const deviceProfiles = state.deviceProfiles;
      const deviceData = this.projectUtilsService.parseDevice(project.Devices, deviceProfiles);
      this.updateDevices(deviceData);
      this.updateLinks(this.projectUtilsService.parseLink(project.Links, deviceData));
      // this.updateStreams(this.projectUtilsService.parseStream(project.Streams));
      this.updateApplicationMode(project.ProjectType);
      // update project setting
      this.updateProjectSetting(project.ProjectSetting);
      // update traffic design
      this.initTrafficDesign(project.TrafficDesign);
      // re init project
      this.updateProjectInitialized(this.state().projectInitialized + 1);
      this.updateSkuQuantitiesMap(project.SkuQuantitiesMap);
    });
  };

  private _createDeviceFromSocket = (data: ActDevice): void => {
    let deviceProfiles;
    this.state$.pipe(take(1)).subscribe(state => (deviceProfiles = state.deviceProfiles));
    let devices: Device[];
    this.state$.pipe(take(1)).subscribe(state => (devices = [...state.devices]));
    const device = this.projectUtilsService.parseDevice([data], deviceProfiles)[0];
    // trigger topology action for create device
    this.updateTopologyAction({
      type: 'create',
      elementType: 'device',
      element: device
    });
    this.updateDevices([...devices, device]);
  };

  private _deleteDeviceFromSocket = (id: number): void => {
    let devices: Device[];
    this.state$.pipe(take(1)).subscribe(state => (devices = [...state.devices]));
    const index = devices.findIndex(device => device.id === id);
    if (index === -1) {
      // cant find target device
      this._snackBar.open(
        this._translateService.instant('request_handler.entity_not_found', { target: `device(${id})` }),
        this._translateService.instant('general.button.close'),
        { panelClass: ['error'] }
      );
      this.store.dispatch(triggerPageLoading({ isLoading: false }));
      return;
    }
    devices.splice(index, 1);
    // trigger topology action for delete device
    this.updateTopologyAction({
      type: 'delete',
      elementType: 'device',
      element: null,
      id: `device-${id.toString()}`
    });
    this.updateDevices(devices);
  };

  private _updateDeviceFromSocket = (data: ActDevice): void => {
    let deviceProfiles;
    this.state$.pipe(take(1)).subscribe(state => (deviceProfiles = state.deviceProfiles));
    const device = this.projectUtilsService.parseDevice([data], deviceProfiles)[0];
    // trigger topology action for update device
    this.updateTopologyAction({
      type: 'update',
      elementType: 'device',
      element: device
    });
    let devices: Device[];
    this.state$.pipe(take(1)).subscribe(state => (devices = [...state.devices]));
    const index = devices.findIndex(oldDevice => oldDevice.id === device.id);
    if (index === -1) {
      // cant find target device
      this._snackBar.open(
        this._translateService.instant('request_handler.entity_not_found', { target: `device(${device.id})` }),
        this._translateService.instant('general.button.close'),
        { panelClass: ['error'] }
      );
      this.store.dispatch(triggerPageLoading({ isLoading: false }));
      return;
    }
    devices.splice(index, 1, device);
    this.updateDevices(devices);
  };

  private _createLinkFromSocket = (data: ActLink): void => {
    let devices: Device[];
    this.state$.pipe(take(1)).subscribe(state => (devices = [...state.devices]));
    let links: Link[];
    this.state$.pipe(take(1)).subscribe(state => (links = [...state.links]));
    const link = this.projectUtilsService.parseLink([data], devices)[0];
    // trigger topology action for update link
    this.updateTopologyAction({
      type: 'create',
      elementType: 'link',
      element: link
    });
    this.updateLinks([...links, link]);
  };

  private _deleteLinkFromSocket = (id: number): void => {
    let links: Link[];
    this.state$.pipe(take(1)).subscribe(state => (links = [...state.links]));
    const index = links.findIndex(link => link.id === id);
    if (index === -1) {
      // cant find target link
      this._snackBar.open(
        this._translateService.instant('request_handler.entity_not_found', { target: `link(${id})` }),
        this._translateService.instant('general.button.close'),
        { panelClass: ['error'] }
      );
      this.store.dispatch(triggerPageLoading({ isLoading: false }));
      return;
    }
    links.splice(index, 1);
    // trigger topology action for delete link
    this.updateTopologyAction({
      type: 'delete',
      elementType: 'link',
      element: null,
      id: `link-${id.toString()}`
    });
    this.updateLinks(links);
  };

  private _updateLinkFromSocket = (data: ActLink): void => {
    let devices: Device[];
    this.state$.pipe(take(1)).subscribe(state => (devices = [...state.devices]));
    let links: Link[];
    this.state$.pipe(take(1)).subscribe(state => (links = [...state.links]));
    const link = this.projectUtilsService.parseLink([data], devices)[0];
    const index = links.findIndex(oldLink => oldLink.id === link.id);
    if (index === -1) {
      // cant find target link
      this._snackBar.open(
        this._translateService.instant('request_handler.entity_not_found', { target: `link(${link.id})` }),
        this._translateService.instant('general.button.close'),
        { panelClass: ['error'] }
      );
      this.store.dispatch(triggerPageLoading({ isLoading: false }));
      return;
    }
    links.splice(index, 1, link);
    // trigger topology action for update link
    this.updateTopologyAction({
      type: 'update',
      elementType: 'link',
      element: link
    });
    this.updateLinks(links);
  };

  private _updateStreamFromSocket = (data: StreamSetting[]): void => {
    // let streams: Stream[];
    // this.state$.pipe(take(1)).subscribe(state => (streams = [...state.streams]));
    // const stream = this.projectUtilsService.parseStream([data])[0];
    // const index = streams.findIndex(oldStream => oldStream.id === stream.id);
    // if (index === -1) {
    //   // cant find target stream
    //   this._snackBar.open(
    //     this._translateService.instant('request_handler.entity_not_found', { target: `stream(${stream.id})` }),
    //     this._translateService.instant('general.button.close'),
    //     { panelClass: ['error'] }
    //   );
    //   this.store.dispatch(triggerPageLoading({ isLoading: false }));
    //   return;
    // }
    // streams.splice(index, 1, stream);
    this.updateStreams(data);
    // trigger topology action for update streams
    this.updateTopologyAction({
      type: 'update',
      elementType: 'stream',
      element: data
    });
  };

  private _updateProjectSettingFromSocket = (data: ActProjectSetting): void => {
    this.updateProjectSetting(data);
  };

  private _updateDeviceConfigFromSocket = (data: object): void => {
    this.updateDeviceConfig(data);
  };

  private _updateCycleSettingFromSocket = (data: ActCycleSetting): void => {
    this.updateCycleSetting(data);
  };

  private _createTopologyFromSocket = (data: ActSimpleTopology): void => {
    forkJoin([this.cncService.getTopologyIcon(data.Id), this.state$.pipe(take(1))]).subscribe({
      next: results => {
        const simpleTopologies = results[1].simpleTopologies;
        this.updateSimpleTopologies([
          ...simpleTopologies,
          new SimpleTopology({
            simpleTopology: data,
            icon: results[0]
          })
        ]);
        this.store.dispatch(triggerPageLoading({ isLoading: false }));
      },
      error: error => {
        this.errorService.handleError(error);
      }
    });
  };

  private _trafficDesignFromSocket = (data: ActTrafficDesign): void => {
    this.updateTrafficDesign(data);
  };

  private _createDeviceProfileFromSocket = (data: any): void => {
    this.cncService
      .getDeviceIcon(data)
      .pipe(
        map(value => {
          const profile = new DeviceProfile(data);
          profile.loadImage(value, this.domSanitizer.bypassSecurityTrustResourceUrl(value) as string);
          return profile;
        })
      )
      .subscribe({
        next: deviceProfile => {
          this.store.dispatch(updateDeviceProfile({ deviceProfiles: [...this.state().deviceProfiles, deviceProfile] }));
        },
        error: error => this.errorService.handleError(error)
      });
  };

  private _deleteDeviceProfileFromSocket = (id: number): void => {
    const deviceProfiles = [...this.state().deviceProfiles];
    const index = deviceProfiles.findIndex(deviceProfile => deviceProfile.id === id);
    deviceProfiles.splice(index, 1);
    this.updateDeviceProfiles(deviceProfiles);
  };

  private readonly _updateManagementInterfaceIcon = this.updater((state, icon: string) => {
    return {
      ...state,
      managementInterfaceIcon: icon
    };
  });
}
