{"__readme__": {"__1__": "All key naming is snake_case, in addition to using uri key, e.g.'ospfRRDStatic'", "__2__": "In 'common', words are used repeatedly. It can be reclassified and named 'common_xxx', e.g.'common_port', 'common_account'...", "__3__": "In 'dialog', words are used repeatedly in the dialog.", "__4__": "In 'button', put all button words in and must be capitalized"}, "login": {"model_name": "<PERSON>", "session_expired_title": "Session Expired", "session_expired_desc": "This session has expired. The system will return to the login page.", "server_disconnect_title": "Server Disconnect", "server_disconnect_desc": "Server is Disconnected. The system will return to the login page."}, "general": {"top_nav": {"greeting": "Hi, {{ username }}", "logout": {"title": "Log Out", "desc": "Are you sure you want to log out?"}, "system_settings": "System Settings"}, "page_state": {"page_not_found": "Page Not Found :(", "page_not_found_desc": "The request URL was not found on this server.", "back_link": "Back to Index Page", "application_error": "Application Error :(", "application_error_desc": "An error occurred while processing this request."}, "common": {"all": "All", "max": "<PERSON>.", "unknown": "Unknown", "alias": "<PERSON><PERSON>", "tier": "Tier", "name": "Name", "settings": "Settings", "preference": "Preference", "status": "Status", "complete": "Complete", "failed": "Failed", "success": "Success", "skip": "<PERSON><PERSON>", "configuring": "Configuring", "username": "Username", "password": "Password", "enter_new_password": "Enter New Password", "device_name": "Device Name", "model_name": "Model Name", "fw_version": "Firmware Version", "upload_icon": "Upload Icon", "icon": "Icon", "ip_address": "IP Address", "ip_address_alias": "IP(Alias)", "new_ip": "New IP", "first_ip": "First IP Address", "last_ip": "Last IP Address", "connection_parameters": "Connection Parameters", "dest_ip_address": "New IP Address", "subnet_mask": "Subnet Mask", "gateway": "Gateway", "default_gateway": "Default Gateway", "dns_server_1": "DNS Server 1", "dns_server_2": "DNS Server 2", "mac_address": "MAC Address", "port": "Port", "protocol": "Protocol", "http": "HTTP", "https": "HTTPS", "port_name": "Port Name", "enabled": "Enabled", "disabled": "Disabled", "yes": "Yes", "no": "No", "none": "None", "source": "Source", "destination": "Destination", "distance": "Distance", "fail_results": "Fail Results", "serial_number": "Serial Number", "module_name": "Module Name", "location": "Location", "design": "Design", "monitor": "Monitor", "bom": "BOM", "manufacture": "Manufacture", "menu_search": "Search for function", "default": "<PERSON><PERSON><PERSON>", "custom": "Custom", "unicast": "Unicast", "multicast": "Multicast", "quantity": "Quantity", "topology_name": "Topology Name", "root": "Root", "backup_root": "Backup Root", "circle": "Circle", "square": "Square", "description": "Description", "id": "ID", "device_mac": "Device MAC", "split": "Split", "device_type": "Device Type", "queue": "Queue", "active": "Active", "line": "Line", "ring": "Ring", "star": "Star", "tagged": "Tagged", "untagged": "Untagged", "management_endpoint": "Management Endpoint", "tx": "TX", "rx": "RX", "temperature": "Temp.", "voltage": "Volt.", "selected_ip": "Selected IP", "select_devices": "Select Devices", "other": "other", "others": "others", "copy_setting_to_device": "Copy Settings to Selected Devices", "copy_setting_to_port": "Copy Settings to Selected Ports", "upload_hint": "Browse file or drop it to upload", "not_support": "Not support", "slot": "Slot", "ethernet": "Ethernet", "power": "Power"}, "common_action": {"add": "Add", "edit": "Edit", "delete": "Delete", "compute": "COMPUTE", "copy": "Copy", "compare": "COMPARE", "deploy": "DEPLOY", "filter": "Filter", "preview": "Preview", "search": "Search", "load": "Load", "import": "Import", "generate": "Generate", "drag": "Drag", "export": "Export", "export_pdf": "Export PDF", "export_csv": "Export CSV", "export_csv_all": " Export All to CSV", "download": "Download", "refresh": "Refresh", "show_detail": "Show Detail", "clear_all_events": "Clear All Events"}, "tooltip": {"unlock": "Unlock", "locked": "Locked", "select_first": "Please select the devices first."}, "button": {"add": "ADD", "auto_assign": "Auto Assign", "create": "CREATE", "connect": "CONNECT", "edit": "EDIT", "delete": "DELETE", "apply": "APPLY", "cancel": "CANCEL", "close": "CLOSE", "change_pwd": "CHANGE PASSWORD", "finish": "FINISH", "next": "NEXT", "back": "BACK", "skip": "SKIP", "retry": "RETRY", "continue": "CONTINUE", "confirm": "CONFIRM", "proceed": "PROCEED", "save": "SAVE", "reset": "RESET", "import": "IMPORT", "export": "EXPORT", "upload": "UPLOAD", "upgrade": "UPGRADE", "scan": "SCAN", "scan_generate": "SCAN and GENERATE", "auto_scan_topology": "AUTO SCAN TOPOLOGY", "redo_auto_scan": "Re-Auto Scan", "detect": "DETECT", "locked": "LOCKED", "unlock": "UNLOCK", "compare": "COMPARE", "deploy": "DEPLOY", "download": "DOWNLOAD", "duplicate": "DUPLICATE", "browse_topology": "BROWSE TOPOLOGY", "go_to": "GO TO", "log_in": "LOG IN", "log_out": "LOG OUT", "re_configure": "RECONFIGURE", "topology_mapping": "TOPOLOGY MAPPING", "verify": "VERIFY", "export_excel": "EXPORT EXCEL", "start_manufacture": "START MANUFACTURE", "remanufacture": "REMANUFACTURE", "next_round": "NEXT ROUND", "register": "REGISTER", "save_baseline": "SAVE BASELINE"}, "unit": {"times": "times", "min_without_dot": "min", "min": "min.", "sec": "sec.", "sec_without_dot": "sec", "byte": "byte", "m": "M", "mbps": "Mbps", "gbps": "Gbps", "ms": "ms", "microsecond": "µs", "ns": "ns", "ns_byte": "ns/byte", "dbm": "dBm", "temp_c": "°C", "voltage": "V"}}, "network": {"menu": {"view": "view", "physical": "Physical", "topology": "Topology", "accessory_list": "Accessory List", "bom_list": "BOM List", "auto_layout": "Auto Layout", "category": "Category", "template": "Template", "tsn_switch": "TSN Switch", "switch": "Switch", "br_end_station": "Bridged End Station", "end_station": "End Station", "icmp": "ICMP", "unknown": "Unknown", "moxa": "Moxa", "network_design": "Network Design", "tsn_design": "TSN Design", "status_view": "Status View", "swift_view": "Swift View", "scan_topology": "Scan Topology", "sfp_list": "SFP List", "system_intelligent_setting": "System Intelligent Settings", "fundamental_setting": "Fundamental Settings", "network_fundamental_setting": "Network Fundamental Settings", "event_logs": "Event Logs", "manufacture": "Manufacture", "stage_production_results": "Stage Production Results", "troubleshooting": "Troubleshooting"}, "view_mode": {"stream_design": "Stream Design", "physical_view": "Physical View", "management_interface": "Network Management Endpoint"}, "chat": {"title": "AI Assistant", "feedback_success_hint": "Thank you for your feedback.", "help_hint": "Helpful?"}, "upload_design_spec": {"upload_design_spec": "Upload Design Questionnaire", "upload_verified_spec": "Upload Verified Questionnaire", "upload_design_spec_and_create": "Upload Questionnaire & Create Project", "upload_verified_spec_and_create": "Upload Verified Questionnaire & Create Project", "upload_design_spec_content": "Create topology by uploading the questionnaire.", "upload_verified_spec_content": "Create topology by uploading the verified questionnaire.", "verify_spec_content": "Verify topology by uploading the questionnaire.", "upload_hint": "Browse file or drop it to upload", "upload_warning": "Uploading a new questionnaire will replace existing data in this project.", "download_spec": "Download Questionnaire Template", "upload_verify_hint": "Upload Questionnaire & Verify"}, "command_bar": {"show_detail": "View details", "hide_detail": "Hide details", "list_view": "List view", "save_project": "Save Project", "save_topology": "Save Topology", "project_setting": "Project Settings", "scheduling_setting": "TSN Scheduling Settings", "base_ip_setting": "Base IP Settings", "communication_interface": "Communication Default Interface", "topology_view": "Topology view", "settings": "Settings", "device_setting": "<PERSON><PERSON>s"}, "stream": {"title": "Stream", "add_stream": "Add Stream", "edit_stream": "Edit Stream", "untag_stream": "Untagged Stream", "tag_stream": "Tagged Stream", "vlan_setting": "VLAN Settings", "traffic_setting": "Traffic Settings", "add_stream_result": "Add Stream Result", "stream_table": "Stream Table", "time_slot": "Time Slot", "time_slot_interval": "Time Slot Interval (µs)", "transmission_interval": "Transmission Interval (µs)", "transmission_start": "Transmission Start (µs)", "transmission_end": "Transmission End (µs)", "transmission": "Transmission (µs)", "frame_offsets": "Frame Offsets (µs)", "stream_adding": "Stream Adding", "stream_setting": "Stream Settings", "traffic_type": "Traffic Type", "zone_path": "Zone Path", "talker_listener": "Talker and Listener", "talker": "Talker", "listener_list": "Listener List", "listener": "Listener", "total_listeners": "Total Listeners", "sender": "Sender", "receiver": "Receiver", "untag": "VLAN Untag", "listener_settings": "Listener Settings", "add_listener": "Add Listener", "delete_listener": "Delete Listener", "bounded_latency": "Bounded Latency", "deadline": "Deadline", "qos_type": "QoS Type", "max_latency": "<PERSON><PERSON> Latency", "accumulated_latency": "Accumulated Latency (µs)", "min_receive_offset": "<PERSON>. Receive Offset", "max_receive_offset": "<PERSON>. Receive Offset", "traffic_specification": "Traffic Specification", "max_frame_size": "<PERSON><PERSON>", "max_bytes_per_interval": "Max. Bytes per Interval", "interval": "Interval", "interval_ns": "Interval (ns)", "delete_stream": "Delete Stream", "delete_stream_desc": "Are you sure you want to delete the selected stream?", "user_defined_vlan": "User Defined VLAN", "sys_assigned_vlan": "System Assigned VLAN", "user_defined_vlan_tag": "User Defined VLAN Tag", "sys_assigned_vlan_tag": "System Assigned VLAN Tag", "vlan_id": "VLAN ID", "priority_code_point": "Priority Code Point", "earliest_transmit_offset": "Earliest Transmit Offset", "latest_transmit_offset": "Latest Transmit Offset", "multicast_ip": "Multicast IP Address", "multicast_mac": "Multicast MAC Address", "destination_mac": "Destination MAC Address", "jitter": "Jitter", "stream_name": "Stream Name", "stream_type": "Stream Type", "stream_parameter": "Stream Parameters", "device_alias": "<PERSON><PERSON>", "interface": "Interface", "ingress_interface": "Ingress Interface", "egress_interface": "Egress Interface", "frer_type": "FRER Type", "egress_port": "Egress Port", "max_frame_per_interval": "<PERSON><PERSON> per Interval", "interface_name": "Interface Name", "frer": "802.1CB (FRER)", "frame_type_class": "Frame Type Class", "ether_type": "EtherType", "subtype": "Subtype", "tag_method": "Tag Method", "pvid": "PVID", "port_priority": "Port Priority", "per_stream_priority": "Per-stream Priority", "per_stream_priority_hint": "\"Per-stream Priority\" function enables non VLAN streams to be tagged with System Assigned VLAN Tags at the first TSN Bridge receiving the traffic from End Stations.", "cyclic": "Cyclic", "best_effort": "Best Effort", "multiple_path": "Multiple Path", "bandwidth": "Bandwidth", "cannot_delete_all_listeners": "Cannot delete all listeners", "pcp_hint_1": "\"Priority Code Point\" is a means of classifying and managing network traffic by providing quality of service.", "pcp_hint_2": "The available values of priority code point are the \"Cyclic\" queues defined in the \"Priority Queue Setting\" on \"Project Setting Page\"", "gcl": "Gate Control List", "stream_properties": "Stream Properties", "stream_info": "Stream Information", "time_slot_setting": "Time Slot Settings", "cycle_time": "Cycle Time", "period": "Period", "slot": "Slot", "cycle_base_time": "Cycle Base Time", "time_slot_index": "Time Slot Index", "queue_id": "Queue ID", "date": "Date", "time": "Time", "nano_second": "Nano Second", "stream_frame_type_setting": "Stream & Frame Type Settings", "frame_type": "Frame Type", "deploy_preview": "Deployment Preview", "stream_fail": "stream fail", "total_slots": "Total Slots", "receive_offset": "Receive Offset"}, "accessory_list": {"device_model": "Device Model", "quantity": "Quantity", "select_model": "Select Model", "edit_model": "Edit Model", "delete_model": "Delete Model", "delete_model_desc": "Are you sure you want to delete the selected model?"}, "bom_list": {"baseline_time": "Baseline Time", "total_price": "Total Price", "unit_price": "Unit Price", "data_sync_hint": "Data sync requires platform connection."}, "service_platform": {"service_platform": "Service Platform", "connect_service_platform": "Connect to Service Platform", "login_success": "Successfully connected ", "login_failed": "Connected failed"}, "device": {"title": "<PERSON><PERSON>", "add_device_topology": "Add Device & Topology", "device_table": "Device Table", "brand": "Brand", "search_device": "Search Device", "add_switch": "Add Switch", "edit_switch": "Edit Switch", "add_br_end_station": "Add Bridged End Station", "edit_br_end_station": "Edit Bridged End Station", "add_device": "Add <PERSON>", "edit_device": "<PERSON>", "copy_device": "<PERSON><PERSON>", "paste_device": "<PERSON><PERSON>", "zone": "Zone", "dependent_delay": "Dependent Delay", "independent_delay": "Independent Delay", "add_end_station": "Add End Station", "edit_end_station": "Edit End Station", "interface_name": "Interface Name", "interface_setting": "Interface Setting", "add_interface": "Add Interface", "delete_interface": "Delete Interface", "cannot_delete_all_if": "Cannot delete all interfaces", "if_Setting_warning_hint": "If you don't set any interface, it will be set by default.", "mac_addr_opt_warning_hint": "If you don't set MAC on interface, it may have problem with unicast stream.", "snmp": "SNMP", "snmp_version": "SNMP Version", "snmp_port": "SNMP Port", "snmp_auth_type": "Authentication Type", "snmp_auth_password": "Authentication Password", "snmp_encrypt_type": "Encryption Type", "snmp_encrypt_key": "Encryption Key", "read_community": "Enter New Read Community", "write_community": "Enter New Write Community", "netconf": "NETCONF", "netconf_info": "NETCONF Information", "netconf_port": "NETCONF Port", "restful": "RESTful", "restful_info": "RESTful Information", "restful_port": "RESTful Port", "delete_device": "Delete Device", "delete_device_desc": "Are you sure you want to delete the selected devices?", "batch_update": "Batch Update", "setting_Change": "Setting Change", "device_Change": "Device Change", "device_properties": "Device Properties", "basic_dev_property": "Basic Device Properties", "interfaces": "Interfaces", "deploy_msg": "Deploy Message", "error_message": "Error Message", "access_port": "Access", "hybrid_port": "Hybrid", "trunk_port": "Trunk", "open_device_web": "Open Device Web"}, "link": {"title": "Link", "add_link": "Add Link", "edit_link": "Edit Link", "link_capability": "Link Capability", "speed": "Speed", "propagation_delay": "Propagation Delay", "propagation_delay_ns": "Propagation Delay", "cable_type": "Cable Type", "fiber": "Fiber", "copper": "Copper", "cable_length": "Cable Length", "cable_length_m": "Cable Length (M)", "propagation_delay_hint": "Propagation delay will be generated based on cable type and cable length:", "calc_rj45_propag_delay": "˙ RJ45 propagation delay = 5.7 * cable length.", "calc_fiber_propag_delay": "˙ Fiber propagation delay = 5.0 * cable length.", "delete_link": "Delete Link", "delete_link_desc": "Are you sure you want to delete the selected link?", "link_properties": "Link Properties", "link_info": "Link Information", "source_ip": "Source IP", "source_interface": "Source Interface", "target_ip": "Target IP", "target_interface": "Target Interface"}, "group": {"add_group": "Add Group", "edit_group": "Edit Group", "delete_group": "Delete Group", "group_name": "Group Name"}, "calculation": {"title": "Calculation", "scan_device": "<PERSON>an <PERSON>ces", "discovery_device": "Discover Device", "retry_unlock_device": "Retry Unlock Device", "link_distance_detect": "Link Distance Detect", "parameter": "Parameter", "stream_path": "Stream Path", "max_frame_duration": "<PERSON><PERSON>", "remain_time": "Remain Time {{ remainMin }}", "progress": {"identify_devices": "Identify Devices", "get_devices_info": "Get Devices Info", "scan_links": "<PERSON><PERSON>s", "update_topology": "Update Topology"}}}, "pages": {"global_setting": {"auto_save_project": "Auto Save Project", "auto_save": "Auto Save", "auto_save_hint": "Enable auto save will automatically save project content whenever users make changes.", "idle_timeout": "Idle Timeout", "idle_timeout_hint": "0 meaning no automatic log out due to inactivity.", "auto_logout_after": "Auto Logout After", "device_display_setting": "<PERSON><PERSON> Display Setting", "device_display_type": "Device Display Type", "operation": "Operation"}, "port_setting": {"page_title": "Port Setting", "disable_all_unused_ports": "Disable all unused port", "disable_unused_ports_content": "Ports that are not linked during the design phase and ports that are not currently linked on the device will be disabled."}, "login_policy": {"page_title": "Login Policy", "login_policy_setting": "Login Policy Settings", "login_message": "Login Message", "login_authentication_failure_message": "Login Authentication Failure Message", "account_login_failure_lockout": "Account Login Failure Lockout", "retry_failure_threshold": "Retry Failure Threshold", "lockout_duration": "Lockout Duration", "auto_lockout_after": "Auto Logout After"}, "ip_setting": {"restart_hint": "Devices need to be restarted to apply settings."}, "device_information": {"page_title": "Information", "device_information_setting": "Information Settings", "device_name": "Device Name", "location": "Location", "description": "Description", "contact_information": "Contact Information"}, "import_export": {"page_title": "Import/Export Configuration", "import_title": "Import Configuration", "export_title": "Export Configuration", "file_path": "File Path"}, "snmp_trap_server": {"page_title": "SNMP Trap Server", "snmp_trap_server_setting": "SNMP Trap Server Settings", "host_ip": "Host IP", "mode": "Mode", "trap_community": "Trap Community"}, "syslog_server": {"page_title": "Syslog Server", "syslog_server_setting": "Syslog Server Settings", "enable": "Enable", "syslog_server": "Syslog Server", "address": "Address", "udp_port": "UDP Port"}, "loop_protection": {"page_title": "Loop Protection", "loop_protection_setting": "Loop Protection Settings", "network_loop_protection": "Network Loop Protection", "detect_interval": "Detect Interval"}, "snmp_trap": {"page_title": "SNMP Trap", "event_id": "Event Id", "timestamp": "Timestamp", "source_ip_address_alias": "Source IP (Alias)"}, "syslog": {"page_title": "Syslog", "syslog_server_settings": "Syslog Server Settings", "enable_built_in_syslog_server": "Enable Built-in Syslog Server", "port": "Port", "syslog_viewer": "Syslog Viewer", "severity": "Severity", "syslog_time": "Syslog Time", "timestamp": "Timestamp", "ip_address": " IP Address", "facility": "Facility", "message": "Message", "priority": "Priority", "hte": "Higher than or equal to", "equals": "Equals", "lte": "Lower than or equal to", "emergency": " Emergency", "severity_type": {"alert": "<PERSON><PERSON>", "critical": "Critical", "debug": "Debug", "emergency": "Emergency", "error": "Error", "information": "Information", "notice": "Notice", "title": "Severity", "warning": "Warning"}, "facility_type": {"kernel": "kernel", "user": "user", "mail": "mail", "system": "daemon", "auth": "auth", "syslog": "syslog", "line_printer": "LPR", "network_news": "news", "uucp": "uucp", "clock": "clock", "auth_note1": "AuthPriv", "ftp": "ftp", "ntp": "ntp", "log_audit": "log audit", "log_alert": "log alert", "cron": "cron", "local0": "local0", "local1": "local1", "local2": "local2", "local3": "local3", "local4": "local4", "local5": "local5", "local6": "local6", "local7": "local7"}, "filter": {"start_date": "Start Date", "end_date": "End Date", "hour": "Hour", "min": "Minute"}}, "time": {"page_title": "Time", "time_setting": "Time Settings", "clock_source": "Clock Source", "time_server": "Time Server", "time_zone": "Time Zone", "daylight_saving_time": "Daylight Saving Time", "offset": "Offset", "start": "Start", "end": "End", "month": "Month", "week": "Week", "day": "Day", "hour": "Hour", "minute": "Minute"}, "port": {"page_title": "Port", "admin_status": "Admin Status"}, "redundancy": {"redundancy": "Redundancy", "stp_rstp": "STP/RSTP", "stp_rstp_setting": "STP/RSTP Setting", "compatibility": "Compatibility", "stp": "STP", "rstp": "RSTP", "not_support": "Not Supported", "bridge_priority": "Bridge Priority", "forward_delay_time": "Forward Delay Time", "hello_time": "Hello Time", "max_age": "Max. Age", "error_recovery_time": "Error Recovery Time", "port_index": "Port Index", "enable": "Enable", "edge": "Edge", "priority": "Priority", "path_cost": "Path Cost", "link_type": "Link Type", "auto": "Auto", "bpdu_guard": "BPDU Guard", "root_guard": "Root Guard", "loop_guard": "Loop Guard", "bpdu_filter": "BPDU Filter", "swift_enable": "Swift Enable", "swift_precedence": "Swift Precedence", "enable_guard_hint": "\"Root Guard\" and \"Loop Guard\" cannot be enabled at the same time.", "enable_swift_and_guard_hint": "Enabling either \"Guard\" or \"BPDU Filter\" will automatically disable \"Swift\" if it is currently enabled."}, "account": {"page_title": "User Accounts", "new_password": "New Password", "confirm_password": "Confirm Password", "role": "Role", "admin": "Admin", "supervisor": "Supervisor", "user": "User", "add_account_title": "Create a New Account", "edit_account_title": "Edit This Account", "edit_account_password_title": "Edit the Account Password", "delete_account_title": "Delete Account", "delete_account_desc": "Are you sure you want to delete the selected account?", "size_limitation": "The maximum number of projects is {{ size }}."}, "project_table": {"projects": "Project Management", "project_list": "Project List", "project_name": "Project Name", "uuid": "UUID", "project_type": "Project Type", "last_edit_time": "Last Edit Time", "delete_project": "Delete Project", "delete_project_desc": "Are you sure you want to delete the selected project?", "size_limitation": "The maximum number of projects is {{ size }}.", "import_project": "Import Project", "project_file": "Project File", "device_configuration": "Device Configuration", "history_project_list": "History of project list", "class_base_config": "Class Based Configuration", "new_class_base_config": "New Class Based Configuration", "stream_base_config": "Stream Based Configuration", "config_mode": "Configuration Mode", "create_project": "Create Project", "edit_project": "Edit Project", "search_history": "Search history project list", "overwrite_project": "Overwrite the existing project: {{ projectName }} ", "select_profile": "Select Profile", "self_planning": "Self-planning", "quick_model_selection_profile": "Quick Model Selection", "quick_model_selection_profile_desc": "Includes 5000+ models. Covers most product models with common features and flexibility.", "foxboro": "Foxboro", "foxboro_specific_design": "Foxboro (Specific design)", "foxboro_profile_desc": "Includes 6 models. Focuses on a particular model with exact specifications.", "project_delete_desc": "The project will be automatically deleted 24 hours after the last edit time.", "has_no_profile_desc": "No supported profiles available.", "upload_not_support_profile_project": "Upload failed. This project profile is not supported."}, "project_setting": {"hint": "This page is used to configure settings related to 802.1Qbv.", "scheduling_param": "Scheduling Parameter", "scheduling_hint": "This provides settings related to scheduling.", "base_ip_setting_hint": "Specify the network's starting IP address.", "media_specific_overhead": "Media Specific Overhead", "media_specific_overhead_hint": "Fixed media-specific framing bytes.", "best_effort_bandwidth": "<PERSON><PERSON><PERSON><PERSON>", "time_sync_delay": "Time Sync. Delay", "time_sync_delay_hint": "This specifies the possible inter-device delay for time synchronization.", "calculation_timeout": "Calculation Timeout", "calculation_timeout_hint": "This represents the maximum time for scheduling calculations; exceeding it results in a timeout.", "keep_previous_result": "Keep Previous Result", "keep_previous_result_hint": "This retains the offset of the talker's stream from the previous scheduling, preventing modifications from affecting the original talker's configuration.", "tsn_vlan_range": "System Assigned VLAN Range", "tsn_vlan_range_hint": "This configuration allows the system to automatically assign VLAN IDs for each system-assigned VLAN stream. If the user manually specifies a VLAN ID, it will not be affected by this range.", "tsn_vlan_range_conflict_title": "System Assigned VLAN Conflict", "tsn_vlan_range_conflict_hint": "This operation will affect the current \"System Assigned VLAN\" on streams and regenerate a new set in the range. \"Confirm\" to proceed or modify the input and retry.", "ptp_packet_size": "PTP Packet Size", "ptp_interval": "PTP Interval", "ca_cert": "CA Certificate", "private_key": "Private key", "client_cert": "Client Certificate", "netconf_cert_mgmt": "NETCONF Certification Management", "priority_queue_setting": "Priority Queue Settings", "priority_queue_setting_hint": "This is used to assign supported traffic types to each priority queue, serving as the basis for scheduling.", "time_sync": "Cyclic/Time Sync.", "na": "N/A", "zone_display": "Zone Display", "communication_interface_hint": "This page is used to configure the default connection information between devices.", "account_setting_title": "De<PERSON><PERSON> Account <PERSON><PERSON>", "connection_account_title": "Connection Account", "base_ip": "Base IP Address", "communication_interface_title": "Communication Default Interface", "ethernet_module": "<PERSON><PERSON><PERSON> Module", "power_module": "Power Module", "power_module_hint": "At least one power module is required.", "power_module_support_poe_hint": "Only power modules with PoE power input are accepted.", "download_device_config": "Download Device Configuration", "download_device_config_hint": "Configuration is generated only after computing. Please compute first before downloading the configuration.", "download_bom_list_hint": "BOM List is generated only after saving baseline. Please save baseline first before downloading the BOM List.", "download_bom_list": "Download BOM List", "download_topology": "Download Topology PNG", "bom_list": "BOM List", "index": "Index", "item": "<PERSON><PERSON>", "qty": "Qty"}, "stream_table": {"page_title": "Stream Table", "size_limitation": "The maximum number of TSN Stream for this network is {{ size }}."}, "compute_result": {"page_title": "Compute Result", "compute_result_table": "Compute Result Table", "result_view": "Result View", "device_view": "Device View", "stream_view": "Stream View", "gate_open": "Gate Open (µs)", "gate_close": "Gate Close (µs)", "frame_offsets": "Frame Offsets", "gcl_view": "Gate Control List View", "gate_states": "Gate states", "remind_compute_title": "Backup Project", "remind_compute_hint": "The previous computation result will be erased if you are to proceed with the current operations.You may export the project as a backup if necessary.", "remind_compute_not_show": "I am aware of the above."}, "vlan": {"page_title": "VLAN", "vlan_setting": "VLAN Settings", "management_vlan": "Management VLAN", "vlan_id": "VLAN ID", "vlan_name": "VLAN Name", "vlan_panel": "VLAN Panel", "name": "Name", "te_mstid": "TE-MSTID", "member_port": "Member Port", "interface": "Interface", "mode": "Mode", "pvid": "PVID", "priority_code_point": "Priority Code Point(PCP)", "untagged_vlan": "Untagged VLAN", "tagged_vlan": "Tagged VLAN", "add_vlan": "Add VLAN", "edit_vlan": "Edit VLAN", "delete_vlan": "Delete VLAN", "vlan_exist_hint": "VLAN already exists", "delete_vlan_desc": "Are you sure you want to delete the selected VLAN?", "delete_management_vlan_hint": "Management VLAN cannot be deleted.", "delete_vlan_hint": "VLAN {{ vlanId }} cannot be deleted.", "edit_port": "Edit Port"}, "scan_range": {"page_title": "Scan Range", "auto_scan_topology": "IP Scan", "hint": "This feature allows users to input IP ranges and connection information to scan devices within the topology using IP scanning.", "auto_scan_mode": "Auto Scan Mode", "net_ip_range": "Network IP Range", "discovery_result": "Discovery Result", "new_topology_title": "New Topology", "new_topology_desc": "To create a new topology based on the current connected network. Existing devices and links will be deleted.", "update_topology_title": "Update Topology", "update_topology_desc": "To update the topology based on the current connected network. Existing devices will be kept while new devices and links being added.", "no_device_desc": "No devices has been discovered.", "update_topology_no_device_desc": "No new devices has been discovered.", "add_ip_range": "Add IP Range", "edit_ip_range": "Edit IP Range", "delete_ip_range": "Delete IP Range", "delete_ip_range_desc": "Are you sure you want to delete the selected IP Range?", "connection_setting_title": "Connection Settings", "snmp_setting_title": "Default SNMP Settings", "enable_snmp_setting_title": "SNMP Settings on Devices", "enable_snmp_setting": "Automatically Enable SNMP function on compatible Moxa devices", "enable_snmp_setting_hint": "Please note that the function will only enable SNMP function without changing the SNMP setting on devices.", "netconf_setting_title": "Default NETCONF Settings", "http_setting_title": "Default HTTP(S) Settings", "update_parameters": "UPDATE PARAMETERS"}, "vlan_setting": {"title": "End to End VLAN Setting", "hint": "This feature allows users to specify which VLAN ID to use for communication between end devices and configures the corresponding VLAN settings on intermediary network devices.\nNote that in this version, the system only assists in adding the user-specified VLAN to the corresponding ports; it does not automatically add VLAN tags to traffic.", "add_vlan_setting_title": "Add End to End VLAN Setting", "selected_devices": "Selected End Devices", "selected_devices_hint": "Select the linked end station for this VLAN group.", "end_station_ip": "End Station IP Address", "delete_vlan": "Delete End to End VLAN Setting", "delete_vlan_desc": "Are you sure you want to delete the selected setting?"}, "rstp_setting": {"title": "Redundancy Settings", "hint": "This feature configures network redundancy mechanisms - RSTP.\nUsers only need to specify the master (root) and secondary (backup root) devices, and the software will automatically convert the relevant settings to the network devices.", "hello_time": "Hello Time", "redundancy_protocol": "Redundancy Protocol", "selected_devices": "Selected Devices", "one_group_hint": "We currently only support one group at this stage.", "selected_devices_hint": "Select network devices for this redundancy group.", "delete_rstp": "Delete Redundancy Setting", "delete_rstp_desc": "Are you sure you want to delete the selected redundancy setting group?", "no_rstp_candidate_hint": {"desc_1": "The \"swift\" feature cannot be enabled due to the environment must meet the following conditions:", "condition_1": "1 At least two devices are required, with two connections between them (root and backup root).", "condition_2": "2 Both devices must support the 'swift' feature.", "desc_2": "Please ensure your environment meets the above requirements before enabling the feature."}}, "management_interface": {"title": "Network Management Endpoint", "hint": "This feature allows users to specify the access point, preventing control packet blocking caused by certain network configurations (e.g., VLAN).", "selected_interfaces": "Selected Endpoints", "add_interface": "Add Endpoint", "delete_interface": "Delete Endpoint", "one_endpoint_hint": "We currently only support one endpoint at this stage.", "delete_all_interface_hint": "Cannot delete all endpoints.", "delete_management_interface": "Delete Network Management Endpoint", "delete_management_interface_desc": "Are you sure you want to delete the selected Network Management Endpoint?"}, "broadcast_search": {"page_title": "Broadcast Search & IP Setting", "hint": "This feature is designed to perform broadcast searches on specific models of devices within a topology.\nUsers can modify the IP addresses of these devices, even if there are conflicting IP addresses.", "skip_setting": "Skip this step with the saved settings.", "define_device_type": {"title": "Define Device Type", "desc": "Please selected which kinds of devices you would like to search.", "moxa_ind_eth_switch": "Moxa Industrial Ethernet Switches", "moxa_ind_wireeless": "Moxa Industrial Wireless"}, "define_net_if": {"title": "Define Network Interface", "all_net_if": "All network interfaces", "desc": "Please selected network interface for broadcast search."}, "unlock_device": {"title": "Unlock Devices", "link_seq_detect_hint": "Link Sequence Detection allows the distances to be considered in the following steps.This would take a few minutes.", "snmp_unreachable": "Found SNMP unreachable", "snmp_unreachable_hint": "The device could not be reached with the current SNMP settings, which might cause wrong link detection results in the following steps.", "http_unreachable": "Found HTTP(S) unreachable", "http_unreachable_hint": "The device could not be reached with the current HTTP(S) settings.", "netconf_unreachable": "Found NETCONF unreachable", "netconf_unreachable_hint": "The device could not be reached with the current NETCONF settings, which might result in \"Deploy\" failed.", "remove_lock_dev": "<PERSON><PERSON>ve Locked <PERSON>", "remove_lock_dev_desc": "The locked devices will be remove if you are to proceed with the current operations.", "moxa_service_unreachable": "Found MOXA service unreachable", "moxa_service_unreachable_hint": "The device could not be reached with the current Protocol settings."}, "define_config_scope": {"title": "Define Configuration Scope", "dev_select_rule": "Device Selecting Rules", "all_dev_upon_search": "All Devices upon Search", "dev_of_spec_ip": "Devices of Specific IPs", "dev_ex_spec_ip": "Devices Excluding Specific IPs", "dev_select_manually": "Devices Selected Manually", "spec_ip_hint": "Specific IPs (Please use \",\" to separate multiple IPs )"}, "define_ip_assign": {"title": "Define IP Assignment", "ip_assign_rule": "IP Assigning Rules", "ip_range": "IP Range", "ip_rule": "IP Rule", "start_ip": "Start IP", "increment": "Increment", "ip_assign_seq": "IP Assigning Sequence", "ls_far_to_near": "By Link Sequence (From Far to Near)", "ls_near_to_far": "By Link Sequence (From Near to Far)", "by_mac_asc": "By MAC Ascending", "by_mac_desc": "By MAC Descending", "keyin_manually": "Key-in Manually"}, "define_ip_config_seq": {"title": "Define IP Configuration Sequence", "ip_confit_seq": "IP Configuration Sequence", "sequence": "Sequence", "ls_far_to_near": "By Link Sequence (From Far to Near)", "ls_near_to_far": "By Link Sequence (From Near to Far)", "manually_def_seq": "Manually Defined Sequence"}, "config_device": {"title": "Configure <PERSON><PERSON>", "config_setting": "Configure Setting", "config_success": "{{ number }} configure success", "config_fail": "{{ number }} configure fail"}, "auto_generate_scan": {"title": "Auto Generate Scan Range List", "content": "This feature will delete all the existing scan ranges."}}, "traffic_design": {"title": "Traffic Design", "title_hint": "hint", "advanced_mode_title": "Provide Stream Details", "advanced_mode_hint": "You need to select the traffic type and specify the stream parameters, talker and listeners.\nThe algorithm will help calculate the appropriate time slot setting.", "simple_mode_title": "Unable to Provide Stream Details", "simple_mode_hint": "You can still set time slot setting and per-stream priority setting.", "time_slot_title": "Time Slot Setting", "per_stream_priority": "Per-stream Priority", "selected_queue": "Selected Queue", "add_slot": "Add Slot", "delete_slot": "Delete Slot", "cannot_delete_all_slot": "Cannot delete all slots", "select_ip_title": "Selected IP and corresponding ports", "total_devices": "Total Devices", "delete_time_slot_setting": "Delete Time Slot Setting", "delete_time_slot_setting_desc": "Are you sure you want to delete the selected time slot setting?", "delete_per_stream_priority": "Delete Per-stream Priority Setting", "delete_per_stream_priority_desc": "Are you sure you want to delete the selected per-stream priority setting?", "delete_application_setting": "Delete Application Setting", "delete_application_setting_desc": "Are you sure you want to delete the selected application setting?", "add_device": "Add <PERSON>", "delete_device": "Delete Device", "cannot_delete_all_devices": "Cannot delete all devices", "traffic_type_title": "Traffic Type Configuration", "application_setting_title": "Application Setting", "traffic_class": "Traffic Class", "traffic_type": "Traffic Type", "reserved_time": "Reserved Time", "application_name": "Application Name", "edit_application_setting": "Edit Application Setting", "add_application_setting": "Add Application Setting", "enable_tsn": "Enable TSN", "stream_design_title": "Stream Design", "add_stream": "Add Stream", "edit_stream": "Edit Stream", "reserverd_time_hint": "Algorithm will allocate sufficient time in each cycle to accommodate every type at least once.", "change_mode_warning": "Warning: Changing this option will render the previous settings ineffective.", "stream_active_hint": "Active to participation in algorithm", "lack_end_station_hint": "This operation requires two or more connected end stations.", "enable_tsn_CB": "Enabled (enable CB)", "vlan_type": "VLAN Type", "tcp_port": "TCP Port", "udp_port": "UDP Port", "per_stream_priority_hint": "Untagged streams will be processed based on the per-stream priority if they match any user-defined rules. If the streams do not match any rule, they will be processed based on the default port priority."}, "config_device": {"page_title": "Device Management", "hint": "This feature is designed to modify the configuration of devices and immediately apply the changes to the specified device(s).", "selected": "Selected", "select_devices": {"title": "Select Devices", "notice": "Please select the devices to be configured to the desired settings", "ip_address": "IP Address", "model_name": "Model Name"}, "configure_device_settings": {"title": "Configure <PERSON><PERSON>", "model_name_not_same": "The selected devices cannot be configured together for this feature.", "select_a_function": "Select a function", "system": "System", "firmware_upgrade": "Firmware Upgrade", "reboot": "Reboot", "factory_default": "Factory Default", "locator": "Locator", "run_cli_script": "Run CLI Script", "cli_script": "CLI Script", "result": "Result", "network_configuration": "Network Configuration", "ip_configuration": "IP Configuration", "vlan_configuration": "VLAN Configuration", "unicast_static_forward": "Static Forwarding Unicast", "unicast_table": "Static Forwarding Unicast Table", "multicast_static_forward": "Static Forwarding Multicast", "multicast_table": "Static Forwarding Multicast Table"}, "configuring_sequence": {"title": "Configuring Sequence", "notice": "After the description is calculated, the setting order is as follows, and the order can be adjusted if necessary.", "index": "Index", "ip_address": "IP Address", "model_name": "Model Name"}, "scan_devices": {"title": "Result", "result": "Result: {{successCount}} Success, {{failCount}} Failure", "ip_address": "IP Address", "new_ip_address": "New IP Address", "setting_result": "Setting Result"}, "firmware_upgrade": {"title": "Firmware Upgrade", "method": "Method", "local": "Local", "select_file": "Select File"}, "ip_configuration": {"title": "IP", "ip_configuration_settings": "IP Settings", "subnet_mask": "Subnet Mask", "default_gateway": "Default Gateway", "ip_address": "IP Address", "model_name": "Model Name", "new_ip_address": "New IP Address"}, "vlan_configuration": {"title": "VLAN Configuration"}, "factory_default": {"title": "Factory Default", "sub_title": "There is no parameters input required.", "hint": "Are you sure you want to reset the system configurations to factory default?", "content": "Please note the following:\n 1. \"Factory Default\" will be configured to devices in the far to near sequence.  \n 2. The network might be temporarily lost during the \"Factory Default\" operations on devices."}, "reboot": {"title": "Reboot", "sub_title": "There is no parameters input required.", "content": "Please note the following:\n 1. \"Reboot\" will be configured to devices in the far to near sequence.  \n 2. The network might be temporarily lost during the \"Reboot\" operations on devices."}}, "profile_mgmt": {"page_title": "Device Profile Management", "hint": "The Device Profile Management page is designed to store the current list of devices supported by the software.\nUsers can add or delete profiles for specific devices on this page.\nBuilt-in profiles, which cannot be deleted, are also listed to provide users with a comprehensive overview of available device configurations.", "import_profile": "Import Profile", "upload_icon": "Upload Icon", "add_profile_hint": "Add IP range to scan the topology for profile generation.", "generate_profile": "Generate Profile", "generate_profile_result": "Generate Profile - Result", "profile_result": "Generated Profiles", "profile_result_hint": "Total: {{ profileCount }}", "success_generated": "Successfully Generated", "detail_information": "Detail Information", "source_ip": "Source IP Address", "select_picture": "Select a Device Picture", "select_profile": "Select a Profile", "delete_profile": "Delete Profile", "delete_project_desc": "Are you sure you want to delete the selected Profiles?", "size_limitation": "The maximum number of plugin is {{ size }}.", "icon_size_range": "Suggested icon size is no larger than {{ size }} * {{ size }}."}, "topology_mgmt": {"title": "Topology Management", "hint": "The Topology Management Page allows users to view and delete saved topologies.", "delete_topology": "Delete Topology", "delete_topology_desc": "Are you sure you want to delete the selected Topology?", "import_topology": "Import Topology", "import_template": "Import Template", "template_type": "Template Type", "ring_quantity_invalid_hint": "The devices in the ring must be more than 2."}, "about": {"page_title": "About", "sw_version": "Software Version"}, "time_slot": {"page_title": "Time Slot Settings"}, "stream_settings": {"page_title": "Stream Settings", "hint": "This page allows users to configure TSN streams (802.1Qcc)."}, "check_end_station": {"description": "Check end station status.", "check_end_station": "Check End Station Status", "topology_mapping": "Topology Mapping", "select_deploy_device": "Select Devices to Deploy", "deploy": "Deploy", "hint": "This feature allows users to confirm the existence of an end station and attempts to retrieve and replace the default MAC address provided by the offline design with the device's actual MAC address.", "remove_loop_desc": "<PERSON> has detected a loop in the network design topology. To ensure a successful deployment, please disconnect the physical loop in the field before proceeding with deployment. This step is necessary to avoid deployment errors.", "need_management_endpoint_desc": "Before deployment, it is necessary to set up the  network management endpoint to ensure proper connectivity after deployment.", "offline_ip": "Offline IP Address", "offline_model": "Offline Model Name", "online_ip": "Online IP Address", "online_mac": "Online MAC Address", "online_model": "Online Model Name", "topology_mapping_hint": "This feature maps the offline design's topology to the devices in the actual environment, ensuring that the configured environment matches the project."}, "monitor": {"cpu_usage": "CPU Usage", "memory_usage": "Memory Usage", "sysyem_uptime": "System Uptime", "product_revision": "Product Revision", "redundant_protocol": "Redundant Protocol", "polling_setting": "Polling Setting", "monitor_type": "Monitor Type", "from_offline_project": "From Offline Project", "from_ip_scan": "From <PERSON><PERSON> List", "polling_interval": "Polling interval", "sync_project": "Sync Project", "sync_project_desc": "Would you like to sync the monitor project with the original project?", "auto_refresh_hint": "Automatically refreshes every 60 seconds."}, "network_baseline_check": {"network_baseline_check": "Network Baseline Check", "baseline_check": "Baseline Check", "name": "Name", "date": "Date", "create_by": "Create By", "add_baseline": "Add Baseline", "edit_baseline": "<PERSON>", "baseline_detail": "Baseline Detail", "compare_baseline": "Compare Baselines", "compare_hint": "Please select two baselines to compare.", "delete_baseline": "Delete Baseline", "delete_baseline_desc": "Are you sure you want to delete the selected baseline?", "compare_baselines": "Compare Baselines", "no_diff_data": "No differences found.", "delete_default_baseline_hint": "The default baseline cannot be deleted.", "duplicate_baseline_hint": "The baseline name must be unique.", "default_baseline_name_hint": "The baseline name cannot be \"CURRENT\"."}, "manufacture": {"start_scan_devices": "<PERSON>an <PERSON>ces", "manufacture_step_order": "Step 1. Please enter the order.", "manufacture_step_scan": "Step 2. Please scan the devices.", "scan_progress": "Scan Progress", "manufacture_progress": "Manufacture Progress", "progress": "Progress", "order": "Order", "scan": "SCAN", "stop_scan": "STOP SCAN", "stop_manufacture": "STOP MANUFACTURE", "scan_pairing_result": "Scan Pairing Result", "scan_pairing_success_result": "Scan Pairing Success Result", "scan_pairing_failure_result": "Scan Pairing Failure Result", "scan_device_list": "<PERSON><PERSON> List", "pairing_result": "Pairing Result", "single_production_result": "Single Production Result", "stage_production_results": "Stage Production Results", "pending_production_equipment_list": "Pending Production Equipment List", "prduction_batch": "Production Batch", "timestamp": "Timestamp", "index": "Index", "sn_txt": "S/N TXT01", "device_ip_txt": "Device_IP DEFAULP_IP02", "device_name_txt": "Device Name TXT02", "slot": "Slot", "type": "Type", "sn": "S/N"}}, "request_handler": {"action_saving": "Saving...", "initialing": "Initialing...", "running": "Running...", "canceling": "Canceling", "finished": "Finished", "close_dialog": "This dialog will close in {{ time }} seconds...", "discovering": "Discovering devices"}, "response_handler": {"global_success": "Successfully Updated.", "global_fail": "Update Failed.", "upload_success": "Upload Successfully.", "upload_fail": "Upload Failed.", "delete_success": "Delete Successfully.", "delete_fail": "Delete Failed.", "download_fail": "Download Failed.", "download_success": "Download Successfully.", "export_fail": "Export Failed.", "export_success": "Export Successfully.", "import_fail": "Import Failed.", "import_success": "Import Successfully.", "scan_fail": "Scan Failed.", "scan_success": "<PERSON><PERSON> Successfully.", "manufacture_fail": "Manufacture Failed.", "auto_save_enable_success": "Enable Auto Save Successfully.", "auto_save_disable_success": "Disable Auto Save Successfully.", "timeout_setting_success": "Timeout Setting Successfully.", "device_display_setting_success": "<PERSON><PERSON> Display Setting Successfully.", "add_device_success": "Add <PERSON>ce Successfully.", "update_device_success": "Device Updated Successfully.", "add_stream_success": "Add Stream Successfully.", "copy_stream_success": "Copy Stream Successfully.", "update_stream_success": "Update a stream rule successfully.", "delete_stream_success": "Delete stream successfully.", "transmit_offset_invalid": "Latest transmit offset is smaller than earliest transmit offset.", "add_link_success": "Add Link Successfully.", "update_link_success": "Update Link Successfully.", "add_stream_to_unlinked_device": "Cannot add stream to unlinked device.", "delete_device_Success": "<PERSON><PERSON> successfully deleted.", "batch_update_success": "Batch Update Successfully.", "discovery_device_success": "Discover Device Successfully.", "retry_protocal_success": "<PERSON><PERSON> Unlock Device Successfully.", "link_distance_detect_success": "Link Distance Detect Successfully.", "compute_success": "Compute Successfully.", "compare_success": "Compare Successfully.", "deploy_success": "Deploy Successfully.", "scan_device_success": "<PERSON><PERSON> Devices Successfully.", "delete_link_success": "<PERSON> Successfully Deleted.", "delete_project_success": "Delete Projects Successfully.", "import_project_success": "Import Project Successfully.", "copy_project_success": "Copy Project Successfully.", "save_project_success": "Save Project Successfully.", "save_project_fail": "Save Project Failed.", "edit_project_error": "You should close the opening project page to edit the project name.", "delete_project_error": "You should close the opening project page to delete the project.", "add_profile_success": "Import Profile Successfully.", "upload_icon_success": "Upload Icon Successfully", "delete_profile_success": "Delete Profile Successfully.", "set_time_slot_success": "Set Time Slot Settings Successfully.", "deploy_vlan_fail": "The VLAN Deployment Failed.", "deploy_gcl_fail": "The Gate Control List deployment failed.", "deploy_routing_fail": "The routing deployment failed.", "deploy_stream_adapter_fail": "The steam adapter deployment failed.", "discovery_finish": "Device discovery is finished.", "change_pwd_success": "Change Password Successfully.", "update_account_success": "Update Account Successfully.", "create_account_success": "Create Account Successfully.", "delete_account_success": "Delete Account Successfully.", "import_topology_out_boundary": "The topology to be added has already exceeded the boundaries.", "save_topology_success": "Save Topology Successfully", "delete_topology_success": "Delete Topology Successfully", "import_topology_success": "Import Topology({{ topologyName }}) Successfully", "add_system_vlan_success": "Add End to End VLAN Setting Successfully", "update_system_vlan_success": "Update End to End VLAN Setting Successfully", "delete_vlan_success": "Delete End to End VLAN Setting Successfully.", "add_management_interface_success": "Add Network Management Endpoint Successfully", "update_management_interface_success": "Update Network Management Endpoint Successfully", "add_rstp_group_success": "Add Redundancy Setting Group Successfully", "update_rstp_group_success": "Update Redundancy Setting Group Successfully", "delete_rstp_success": "Delete Redundancy Setting Group Successfully.", "delete_management_interface_success": "Delete Network Management Endpoint Successfully", "lack_device_interface_fail": "{{ device }} lacks sufficient interfaces.", "entity_not_found": "{{ target }} not found.", "ai_unavailable": "AI Assistant service is unavailable, please restart the system and try again later.", "latest_offset_invalid": "Latest transmit offset can't greater than interval.", "add_time_slot_setting_success": "Add Time Slot Setting Successfully.", "update_time_slot_setting_success": "Update Time Slot Setting Successfully.", "delete_time_slot_setting_success": "Delete Time Slot Setting Successfully.", "add_per_stream_priority_success": "Add Per-stream Priority Successfully.", "update_per_stream_priority_success": "Update Per-stream Priority Successfully.", "delete_per_stream_priority_success": "Delete Per-stream Priority Successfully.", "add_application_setting_success": "Add Application Setting Successfully.", "update_application_setting_success": "Update Application Setting Successfully.", "copy_application_setting_success": "Copy Application Setting Successfully.", "delete_application_setting_success": "Delete Application Setting Successfully.", "add_topology_template_success": "Add Topology Template Successfully.", "save_monitor_setting_success": "Save Monitor Setting Successfully.", "get_data_fail": "Failed to retrieve data", "monitor_is_running": "Monitor process is running"}, "validators": {"required": "Required", "require_min_length": "Minimum {{ number }} characters", "require_range_between": "{{ rangeBegin }} - {{ rangeEnd }}", "not_support": "Not supported", "duplicate_ip_address": "Duplicate IP", "invalid_format": "Invalid Format", "invalid_space": "The value cannot contain space characters.", "invalid_single_range": "Range is {{ singleNum }} or {{ rangeBegin }} to {{ rangeEnd }}", "invalid_range": "The valid range is from {{ rangeBegin }} to {{ rangeEnd }}", "invalid_min": "The minimum value is {{ min }}", "invalid_positive_integer": "Invalid positive integer", "invalid_ip_address": "Invalid IP Address", "invalid_mac_address": "Invalid MAC Address", "invalid_multicast_mac_address": "Invalid Multicast MAC Address", "invalid_unicast_mac_address": "Invalid Unicast MAC Address", "invalid_netmask": "Invalid <PERSON>", "invalid_username_pwd": "Invalid username or password", "invalid_password_mismatch": "Does not match the password", "invalid_duplicate_username": "The username is already in use", "invalid_root_username": "'root' is forbidden", "invalid_best_effort": "Invalid Best Effort <PERSON>", "invalid_cyclic": "Invalid Cyclic Queue", "invalid_file_type": "Invalid File Type", "invalid_project_name_len": "Project name length must less then 64", "invalid_png_format": "Only supports PNG formats", "invalid_json_format": "Only supports JSON formats", "invalid_file_ext": "File extension must be {{ fileExtension }}", "invalid_time_slot_order": "NA Time Slot should set after non NA Time Slot", "invalid_require_best_effort": "There should be one Best Effort traffic type in the slots", "invalid_require_time_sync": "There should be one Time Sync. traffic type in the slots", "invalid_ip_higher_last_ip": "First IP cannot higher then last IP", "invalid_reserved_time": "Reserved time should be greater than 0", "duplicate_ip": "Duplicate IP", "duplicate_name": "Duplicate Name", "duplicate_listener_ip": "Listener IP is duplicated", "duplicate_taker_listener": "Talker and Listener Can't be the same", "duplicate_ptp_queue": "Duplicate with PTP queue", "duplicate_src_ip_dest_ip": "Source IP and Destination IP shall be different", "vlan_used": "This VLAN ID is used", "multiples_of_number": "Multiples of {{ number }}", "required_priority_multiple": "The priority must be a multiple of {{ number }}"}, "bk_api_handler": {"Success": "Success", "unauthorized": "Unauthorized", "forbidden": "Access Denied", "not_found": "\"{{ item }}\" is not found", "conflict": "Conflict", "unProcessable": "UnProcessable", "internal_error": "Internal error in \"{{ module }}\" module", "skip": "<PERSON><PERSON>", "running": "Running", "stop": "Stop", "finished": "Finished", "failed": "Failed", "duplicated": "\"{{ item }}\" is duplicated", "full": "Full", "license_content_failed": "License Content Failed", "license_size_failed": "The \"{{ item }}\" exceeds the limit: \"{{ size }}\"", "license_not_support": "The license does not support \"{{item}}\"", "routing_dest_unreachable": "\"{{ streamName }}\" - Cannot reach to \"{{ destIp }}\"", "pcp_ins_for_time_slot": "The pcp is insufficient for time slot \"{{ timeSlotIndex }}\"", "time_sync_pcp_device_not_consistent": "The time sync PCP {{timeSyncPCP}} in the device {{deviceIp}} is not consistent with project setting", "routing_device_type_incapable": "\"{{streamName}}\" - Has the incapable routing device at the path \"{{deviceIp}}\"", "scheduling_failed": "Scheduling Failed:\n{{ errorMessage }}", "feasibility_check_failed": "Feasibility Check Failed - {{ reason }}", "calculate_timeout": "Calculating timeout", "set_config_failed": "Set \"{{ item }}\" configuration failed.\nDevice: \"{{ device }}\"", "get_config_failed": "Get \"{{ item }}\" configuration failed.\nDevice: \"{{ device }}\"", "get_device_data_failed": "Get \"{{ item }}\" failed.\nDevice: \"{{ device }}\"", "compare_failed": "Compare \"{{ item }}\" failed.\n\"{{ tItemType }}\": \"{{ tItems }}\"", "compare_topology_failed": "Compare topology consistent failed.\nNot found links: \"{{ notFoundLinks }}\";\nExtra links: \"{{ extraLinks }}\"", "deploy_failed": "Deploy failed. Failed Devices: \"{{failedDevices}}\"", "southbound_failed": "Southbound Failed - {{ errorMessage }}", "auto_scan_failed": "Auto Scan Failed", "device_profile_conflict": "The device profile is used by:\n", "update_project_topology_failed": "Update Project topology failed.\nFailed Devices: \"{{ devices }}\".\nFailed Links: \"{{ links }}\""}, "log": {"trap": {"unknown_event": "Unknown event", "received": "A trap is received.", "port_link_down": "Port {{ x }} link down.", "port_link_up": "Port {{ x }} link up.", "device_cold_start": "Device cold start.", "device_warm_start": "<PERSON>ce warm start.", "user_login_failed": "User login failed.", "di_up": "DI {{ x }} is up.", "di_down": "DI {{ x }} is down.", "redundancy_topology_changed": "Redundancy topology changed.", "turbo_ring_coupling_port_changed": "Turbo Ring Coupling port is changed.", "turbo_ring_master_changed": "Turbo Ring Master is changed.", "device_configuration_changed": "Device configuration is changed.", "device_firmware_upgraded": "Device firmware is upgraded.", "port_traffic_utilization_over_threshold": "The port traffic utilization of port {{ x }} is over {{ y }}%.", "lldp_table_changed": "The LLDP table of a device is changed.", "poe_device_current_over_threshold": "PoE device current over threshold.", "poe_device_check_failed": "PoE device check failed warning.", "poe_device_on": "A PoE device is on.", "poe_device_off": "A PoE device is off.", "pse_over_threshold": "The PSE is over threshold.", "pse_mosfet_failed": "MOSFet on a PSE is failed.", "pse_temperature_over_threshold": "The temperature of the PSE is over threshold.", "pse_input_voltage_too_low": "The input voltage of the PSE is too low.", "abc02_warning": "ABC02 warning.", "turboring_master_mismatch": "TurboRing Master mismatch.", "user_login_success": "A user is logged in the device.", "too_many_users_login": "Too many users have logged in the device.", "device_user_settings_changed": "The user settings of the device is changed.", "new_config_imported": "A new config is imported to the device.", "phr_function_failed": "The PHR function of the device is failed.", "firewall_rules_violated": "(EDR) The firewall rules are violated.", "mac_sticky_rules_violated": "The MAC sticky rules are violated.", "remote_login_success": "A remote login (through TACACS+ or RADIUS) is successful.", "remote_login_failed": "A remote login (through TACACS+ or RADIUS) is failed.", "user_locked_due_to_login_failures": "{{ username }} locked due to {{ param }} failed login attempts.", "power_module_fan_malfunction": "Power module fan malfunction.", "system_temperature_exceeds_threshold": "System temperature exceeds threshold.", "thermal_sensor_overheat": "Thermal sensor component overheat detected.", "power_cut_due_to_overheating": "Power has been cut due to overheating.", "power_module_overheat_protection": "Overheat protection now active for power module {{ x }}.", "vrrp_master_changed": "VRRP Master changed.", "mstp_topology_changed": "MSTP topology changed.", "new_root_bridge_selected": "New root bridge selected in topology.", "new_port_role_selected": "New port role selected.", "ospf_designated_router_changed": "OSPF designated router changed.", "ospf_designated_router_interface_adjacency_changed": "OSPF designated router interface and adjacency changed.", "interface_set_as_ospf_designated_router": "Interface set as OSPF designated router.", "phr_ab_port_timediff": "PHR AB Port Timediff.", "phr_ab_port_wrong_lan": "PHR AB Port Wrong LAN.", "mrp_multiple_managers_event": "MRP multiple managers event occurred.", "mrp_ring_open_event": "MRP ring open event occurred.", "port_non_pd_or_pd_short_circuited": "Port {{ x }} Non-PD or PD short circuited."}}}