{"ABOUT": {"debug_log": "调试日志", "debug_log_desc": "点击下载按钮，生成并下载调试日志。通过MOXA支持频道发送下载的日志文件，以便进一步分析。", "desc": "Copyright Moxa, Inc. All Rights Reserved.", "eula": "检视使用者授权合约 (EULA)", "gateway": "网关版本", "title": "关于", "web": "Web 版本"}, "ACCOUNT_MANAGEMENT": {"access_site": "可存取服务器", "add_account_dialog": "添加用户帐户", "add_user_fail": "无法建立新用户", "add_user_success": "新用户已经建立成功", "admin": "监工", "all_user": "所有用户", "authority": "权限", "change_password": "更新密码", "delete_user_fail": "无法删除用户", "delete_user_success": "用户已经删除成功", "demo_user": "演示用户", "filter_account": "键入以筛选用户帐号", "modify_account_dialog": "修改用户帐户", "new_password": "新密码", "old_password": "旧密码", "password": "密码", "password_policy_mismatch": "密码不符合密码政策", "superuser": "管理员", "title": "用户管理", "ui_profile": "UI 描述档", "update_user_fail": "无法更新新用户", "update_user_success": "用户已经更新成功", "user": "用户", "user_account": "用户帐号", "user_exist": "用户已经存在", "username": "用户名称"}, "account_password": {"1st_email": "第一个电子邮件收件人", "2nd_email": "第二个电子邮件收件人", "account_audit": "账户审计", "account_info": "账户信息", "account_info_content": "点击『更新』按钮以检索所有设备的账户信息。这可能需要一些时间。", "account_management": "账户管理", "account_password_management_automation": "账户和密码管理自动化", "account_status_audit": "账戶审计", "account_status_baseline": "账户基线", "account_status_baseline_content": "此操作将建立一个新的基线并覆盖现有的", "accounts": "账户", "activate": "已生效", "add_account": "新增账户", "add_temporary_account": "添加临时账户", "added_account": "添加账户", "admin": "管理员", "apply_accounts": "套用的账户", "audit_automation": "审计自动化", "authority": "权限", "baseline_account": "账户基线", "baseline_auto_check_failed": "无法创建基线", "change_admin_name": "更改默认「管理员」名称", "change_admin_name_content": "MXview One 将使用更新后的账户凭证访问以下设备。其他设备不受影响。", "change_admin_name_contents": "MXview One 将使用更新后的账户密码访问以下设备。其他设备不受影响。", "check_default_account_failed": "检查默认账户失败。", "check_password_length": "请确保密码长度在设备允许的最大密码长度范围内。", "compability": "兼容的", "create_baseline_failed": "无法创建基线", "create_baseline_failed_no_devices": "无法创建基线。未检测到任何设备。", "days": "天", "default_account": "默认用户名/密码", "default_password_audit": "默认密码审计", "default_password_audit_info": "扫描默认账户密码可能需要一些时间，并且会导致画面暂时不可用。您确定要继续吗？", "delete_account": "删除账户", "delete_temporary_account": "删除临时账户", "delete_temporary_account_info": "您确实要删除该临时账户吗？", "delete_temporary_accounts_info": "您确定要删除这些临时账户吗？", "deleted_account": "已删除的账户", "device_alias": "设备别名", "device_ip": "设备 IP", "edit_account": "编辑账户", "edit_temporary_account": "编辑临时账户", "email_server_configuration": "Email 服务器设定", "email_server_hint": "如果您没有收到包含验证码的电子邮件，请检查您的垃圾邮件文件夹或验证", "email_verified": "电子邮件地址已验证。", "end_date": "失效日期", "end_time": "结束时间", "fatiled_to_audit_account_due": "无法完成账户审核。无法从 {{ ip }} 检索设备账户信息。", "fatiled_to_create_baseline_due": "无法创建基线。无法从 {{ ip }} 检索设备账户信息。", "get_baseline_failed": "无法取得基线", "get_device_account_failed": "查询设备账户信息失败，其他请求正在进行中。请稍后重试。", "incorrect_verification_code": "验证码不正确。", "last_audit_time": "上次审计", "last_execution_time": "上次执行", "max_char": "最多 {{num}} 个字符", "model": "型号", "mxa_char": "最多 {{num}} 个字符", "new_password": "新密码", "new_username": "新用户名称", "next_audit_time": "下次审计", "next_schdeule_start_time": "下次預定時間", "no_data": "N/A", "not_activate": "尚未生效", "not_started": "待执行", "not_started_hint": "由于系统关闭，此任务未执行。单击「重新生成密码」按钮执行该任务。", "now": "立即", "operation": "操作", "password": "密码", "password_automation": "密码自动化", "password_automation_schedule": "密码自动化排程", "password_automation_settings": "密码自动化向导", "password_email_receiver": "密码电子邮件收件人", "password_regenerated_info": "MXview One 将使用以下设置为每台设备生成新密码。", "password_resend_info": "您确定要将设备密码重新发送给以下收件人吗？", "password_resend_result_info": "MXview One 已将设备账户和密码文件发送至以下电子邮件地址：", "password_strength": "密码强度", "random_password_complexity": "设置密码复杂性", "random_password_length": "随机密码长度", "random_password_length_info": "MXview One 将为选定的设备生成随机密码。", "randomized_password_failed": "失败（设备账户与数据库账户不匹配。）", "refresh_hint": "在继续此操作之前，请按「更新」按钮来检索设备账户。", "regenerate_password": "重新生成密码", "resend_password_email": "重新发送密码电子邮件", "retrieve_data": "检索数据", "retry_failed_devices": "重试失败的设备", "schedule": "排程", "schedule_interval": "间隔", "script_error": "此字段不得包含以下任何字符：#%&amp;*{}|:\\&quot;&lt;&gt;?/\\\\", "select_device": "选择设备", "select_device_random_password": "选择要生成随机密码的设备。", "send_password_email": "发送密码电子邮件", "send_password_email_success": "MXview One 已将设备账户、密码和执行结果发送至以下电子邮件收件人：", "set_password_to_device": "将密码应用于设备", "set_schedule_interval_failed": "无法设置排程间隔。", "set_schedule_interval_success": "排程间隔设置成功。", "start_date": "生效日期", "start_over": "重新开始", "start_time": "开始时间", "start_wizard": "启动向导", "status": {"cli_session_timeout": "CLI 会话超时", "failed": "失败", "failed_account_exist": "失败（该账户已存在）", "failed_account_password_incorrect": "失败（账户或密码错误）", "failed_limit_reached": "失败（已达到设备账户限制）", "failed_not_support_role": "失败（设备不支持）", "failed_other_request": "失败（其他请求正在进行中）", "failed_retrieve_account_info": "失败（无法检索账户信息）", "finished": "已完成", "in_progress": "进行中 ...", "waiting": "等待中"}, "supervisor": "监工", "temporary_account": "临时账户", "test_eamil_recipient": "测试电子邮件收件人", "title": "账户和密码", "unable_to_get_accounts": "无法检索账户", "user": "用戶", "username": "用户名称", "verififcation_code": "验证码", "verift_title": "验证您的 MXview One 账户", "verify_code_expiration": "验证码过期时间", "verify_email_not_allowed": "请等待至少一分钟后再发送另一封电子邮件。", "verify_email_password_receiver": "验证账户和密码电子邮件收件人", "verify_email_receiver": "验证电子邮件收件人", "verify_email_server_failed": "邮件服务器配置无效。无法发送电子邮件。", "verify_user_failed": "无效的用户名或密码"}, "ADD_DEVICE": {"add_device_fail": "无法新增设备", "add_device_fail_error_message": {"device_has_existed": "具有该IP的设备已存在", "license_limitation_reached": "已达到许可限制", "model_not_exist": "该型号不存在。"}, "add_device_success": "添加设备成功", "assign_group": "指派群组", "assign_model": "指定型号", "authentication": "认证", "auto_detect_model": "自动侦测", "data_encryption": "数据加密", "encryption_password": "加密密码", "encryption_type": "加密方法", "field_required": "此栏位为必填", "snmp_setting": "SNMP 设置", "snmp_version": "SNMP 版本", "title": "添加设备"}, "ADD_LINK": {"alias": "别名", "device": "设备", "fail": "无法增加连线", "from": "从", "ip_address": "IP 地址", "model": "型号", "only_number": "仅支援数字。", "port": "端口", "success": "增加连线成功", "title": "增加连线", "to": "到"}, "API_MANAGEMENT": {"access_count": "存取次数", "add_failed": "添加 API 密钥失败", "add_success": "添加 API 密钥成功", "add_title": "添加 token", "api_key": "API 密钥", "application_name": "应用程序名称", "create_time": "建立时间", "delete_failed": "删除 API 密钥失败", "delete_success": "删除 API 密钥成功", "edit_title": "编辑 token", "filter": "键入以筛选 API 密钥", "regenerate_api_key": "重新生成 API 密钥", "regenerate_failed": "重新生成 API 密钥失败", "regenerate_success": "重新生成 API 密钥成功", "title": "API 管理", "update_failed": "更新 API 密钥失败", "update_success": "更新 API 密钥成功"}, "ASSIGN_MODEL": {"apply_to_all": "应用此图示至相同型号的所有设备", "assign_model_fail": "无法指定设备型号", "assign_model_success": "指定设备型号成功", "ip_address": "IP地址", "model": "型号", "model_icon": "型号图示", "select_model": "选择型号"}, "AVAILABILITY_REPORT": {"alias": "设备别名", "average": "平均可用性", "days": "天", "end_date": "结束日期", "filter": "键入以筛选可用性报告", "from_date": "开始日期", "query_date": "查询日期", "report_generate_day": "报表生成日期: ", "site_name": "站点名称", "title": "可用性报告", "worst": "最差可用性"}, "BASIC_INFORMATION": {"apply_fail": "无法储存设备系统资讯", "apply_success": "设备系统资讯储存成功", "contact": "联系", "location": "位置", "model": "型号", "name": "名称", "title": "基本信息"}, "BUTTON": {"add": "添加", "add_to_scheduler": "添加到排程", "agree": "同意", "apply": "启用", "audit": "审计", "back": "上一步", "cancel": "取消", "change": "更改", "check": "检查", "checkNow": "立即检查", "clear": "清除", "clear_fail_record": "清除错误登入记录", "close": "关闭", "compare": "比较", "confirm": "确认", "connected": "已连线", "continue": "继续", "copy": "复制", "create": "创建", "deactivate": "停用", "decline": "衰退", "delete": "删除", "disable_new_version_notifications": "禁用新版本通知", "disconnected": "未连线", "download": "下载", "download_all_logs": "下载所有日志", "download_filter_logs": "下载过滤日志", "edit": "编辑", "enable_new_version_notifications": "启用新版本通知", "execute": "运行", "faqs": "FAQs", "got_it": "知道了", "ignore": "忽略", "leave": "离开", "next": "下一步", "ok": "确认", "query": "查询", "reboot": "重启", "redirect": "重定向", "refresh": "刷新", "regenerate": "重新产生", "resend": "重新发送", "reset": "重设", "retry_failed_devices": "重试失败的设备", "run": "执行", "save": "储存", "scan": "扫描", "search": "搜索", "security_patch_available": "可用安全补丁", "select": "选择", "select_firmware_version": "选择固件版本", "send": "发送", "send_test_eamil": "发送测试电子邮件", "set": "设置", "upgrade": "升级", "upgrade_firmware": "更新固件", "upload": "上传", "verify": "验证", "verify_email": "验证电子邮件"}, "cli_object_database": {"add_cli_fail": "无法创建 CLI 脚本", "add_cli_object": "添加 CLI 脚本", "add_cli_success": "新的 CLI 脚本已成功创建", "before_date": "日期", "before_time": "时间", "cli_objects": "CLI 脚本", "cli_script": "CLI脚本", "delete_all_execution_results": "删除所有 CLI 脚本执行结果", "delete_all_execution_results_before_time": "删除之前的脚本执行结果", "delete_all_execution_results_before_time_desc": "您确定要删除 {{param}} 之前的所有脚本执行结果吗？", "delete_all_execution_results_desc": "您确定要删除所有 CLI 脚本执行结果吗？", "delete_cli_fail": "无法删除 CLI 脚本", "delete_cli_object": "删除 CLI 脚本", "delete_cli_object_desc": "您确定要删除此 CLI 脚本吗？", "delete_cli_object_disabled": "该脚本无法删除，因为它已链接到排程任务或脚本自动化。", "delete_cli_objects_desc": "您确定要删除这些 CLI 脚本吗？", "delete_cli_success": "CLI 脚本删除成功", "delete_execution_result_fail": "无法删除执行结果", "delete_execution_result_success": "脚本执行结果删除成功", "delete_results_before_time": "删除 CLI 脚本执行结果", "description": "描述", "download_all_execution_results": "下载所有执行结果", "download_all_execution_results_fail": "无法下载执行结果", "download_execution_results_failed_hint": "没有可供下载的执行结果。", "edit_cli_fail": "无法更新 CLI 脚本", "edit_cli_object": "编辑 CLI 脚本", "edit_cli_success": "CLI 脚本更新成功", "execution_results": "执行结果", "get_cli_fail": "无法取得 CLI 脚本", "linked_scheduled_task": "链接的排程任务", "linked_script_automation": "链接的脚本自动化", "name": "名称", "non_ascii": "仅接受 ASCII 字符。", "scheduled_execution_cli_object_desc": "您可以从管理 > 维护计划页面创建排程任务以在指定日期和时间运行 CLI 脚本。", "scheduled_execution_cli_object_info": "排程脚本", "scheduled_task": "排程任务", "title": "保存的 CLI 脚本"}, "COMBO_BOX": {"disabled": "未启用", "enabled": "已启用", "export_all_event_csv": "将所有事件导出到 CSV", "export_all_syslog_csv": "将所有 Syslog 汇出到 CSV", "export_csv": "导出 CSV", "export_pdf": "导出 PDF", "sequential": "严格依序", "smart_concurrent": "智能依序"}, "COMMAND_BAR": {"hide_automation_button": "隐藏按钮部件", "hide_button_panel": "隐藏按钮面板", "hide_detail": "隐藏详细信息", "hide_group": "隐藏群组", "list_view": "列表检视", "show_automation_button": "显示按钮部件", "show_button_panel": "显示按钮面板", "show_detail": "查看详细信息", "show_group": "查看群组", "topology_view": "拓扑检视"}, "CONFIG_CENTER": {"alias_name": "别名", "backup_config": "备份配置", "backup_message": "系统会将档案压缩成一个zip档", "backup_tab": "备份", "backup_tab_hint": "请先转到备份分页并导出设备配置", "compare_config_basement": "比较基准: {{compareConfigFileName}}", "compare_config_dialog_title": "比较配置", "compare_tab": "记录", "compare_target": "比较目标", "configuration_file": "配置文件", "configuration_name": "配置名称", "create_time": "创造时间", "delete_config_dialog_title": "删除配置", "delete_config_failed": "删除设备配置失败", "delete_config_success": "删除设备配置成功", "delete_config_warning_message": "您确定要删除选定的配置吗？", "device_list": "设备列表", "export_failed": "导出失败", "export_success": "导出成功", "from_date": "开始日期", "group_name": "群组", "ip_address": "IP 地址", "last_check_time": "上次检查时间", "local_file": "本地文件", "restore_config": "还原配置", "restore_device": "还原设备 - {{selectedDeviceIP}}", "restore_tab": "还原", "site_name": "站点", "time": "时间", "title": "设备配置中心", "to_date": "结束日期"}, "DASHBOARD": {"adpDestIp": "最常发生 ADP 规则事件的前 5 名目的地 IP", "adpSrcIp": "最常发生 ADP 规则事件的前 5 名来源 IP", "ap_devices": "AP 设备", "ap_traffic_load": "AP 流量负载", "baseline": "基本", "client_devices": "Client 设备", "critial_devices": "严重", "device_availability": "设备可用性", "device_availability_intro": "MXview One 使用以下的公式计算最近 2​​4 小时的可用性。可用性 = (运行时间 / (运行时间+停机时间)) * 100", "device_summary": "设备摘要", "devices": "设备", "disk_space_utilization": "磁盘空间使用率", "dpiDestIp": "最常发生协议过滤规则事件的前 5 名目的地 IP", "dpiSrcIp": "最常发生协议过滤规则事件的前 5 名来源 IP", "event_highlight": "事件摘要", "healthy_devices": "正常", "icmp_unreachable": "ICMP unreachable", "iec_level_1": "中", "iec_level_2": "高", "ipsDestIp": "最常发生 IPS 规则事件的前 5 名目的地 IP", "ipsSrcIp": "最常发生 IPS 规则事件的前 5 名来源 IP", "l3DestIp": "最常发生第 3-7 层规则事件的前 5 名目的地 IP", "l3SrcIp": "最常发生第 3-7 层规则事件的前 5 名来源 IP", "last_1_day": "最近1天", "last_1_hours": "最近1小時", "last_1_weeks": "最近1周", "last_2_weeks": "最近2周", "last_24_hours": "最近24小時", "last_3_days": "最近3天", "last_3_hours": "最近3小时", "last_30_days": "最近30天", "last_30_minutes": "最近30分钟", "last_7_days": "最近7天", "last_update": "最后更新:", "link_down": "断线", "link_up": "连接", "linkButton": "显示事件日志", "not_pass": "没有通过", "now": "即时", "open": "未达安全层级", "pass": "通过", "reboot_times": "Cold/Warm Start trap", "refresh_all": "全部刷新", "security_level": "安全层级", "security_summary": "安全摘要", "selecting_visible_item": "选择要显示的项目", "set_default_tab": "设置为默认选项卡", "tabs": {"cybersecurity": "网络安全", "general": "一般", "wireless": "无线"}, "title": "仪表板", "total_availability": "Device availability is below {{param}}%", "total_devices": "所有设备", "unknown": "未知", "view_network_topology": "查看网络拓扑", "warning_devices": "警告", "wireless_device_summary": "无线设备摘要"}, "DATABASE_BACKUP": {"database_name": "名称", "fail": "数据库备份失败", "success": "数据库备份成功 {{param1}}", "title": "数据库备份"}, "DEVICE_LOCATOR": {"stop_trigger_locator": "结束", "title": "定位设备", "trigger_locator": "开始", "trigger_locator_fail": "无法触发设备定位", "trigger_locator_off": "关闭设备定位成功", "trigger_locator_on": "触发设备定位成功"}, "device_management": {"built_in_command_execution_process_is_running": "无法发送命令。 另一个命令正在运行。 稍后再试。", "execute_fail": "执行失败", "limited_support": "有限度支援，请查看用户手册。", "select_device": "选择设备", "select_operation": "选择操作"}, "DEVICE_PANEL": {"panel_status": "面板状态", "panel_zoom_size": "设备面板缩放尺寸", "port": "端口"}, "DEVICE_POLLING_SETTING": {"consecutive_icmp_fail_trigger": "触发ICMP无响应事件的连续轮询失败次数", "consecutive_snmp_fail_trigger": "触发SNMP无响应事件的连续轮询失败次数", "icmp_polling_interval": "ICMP轮询间隔 ", "snmp_polling_interval": "SNMP轮询间隔", "title": "轮询间隔设置"}, "DEVICE_PROPERTIES": {"basic_property": {"alias": "别名", "availability": "可用性", "bios_version": "BIOS/引导加载程序版本", "cpu_loading": "CPU加载 (%)", "cpu_loading_30_seconds": "CPU加载30秒 (%)", "cpu_loading_300_seconds": "CPU加载300秒 (%)", "cpu_loading_5_seconds": "CPU加载5秒 (%)", "cpu_utilization_300_seconds": "近300秒CPU利用率(%)", "cpu_utilization_60_seconds": "近60秒CPU利用率(%)", "cpu_utilization_900_seconds": "近900秒CPU利用率(%)", "disk_utilization_unit": "磁盘利用率 (%)", "fw_system_version": "固件/系统映像版本", "fw_version": "固件/系统映像版本", "mac_address": "MAC地址", "memory_usage": "内存使用情况", "memory_usage_unit": "内存使用情况 (%)", "model_name": "型号名称", "os_type": "操作系统", "power_comsumption": "耗电量 (W)", "serial_number": "序列号", "system_contact": "系统联系", "system_description": "系统描述", "system_location": "系统位置", "system_name": "系统名称", "system_object_id": "Sysobjectid", "system_up_time": "系统运行时间", "title": "基本设备属性"}, "cellular": {"cellular_carrier": "行动网路业者", "cellular_ip_address": "行动网路 IP 地址", "cellular_mode": "行动网路模式", "cellular_signal": "行动网路讯号", "imei": "IMEI", "imsi": "IMSI", "title": "行动网路资讯"}, "goose_table": {"app_id": "应用程序_ID", "gocb_name": "GOCB名称", "goose_address": "GOOSE地址", "ied_name": "IED名称", "port": "入站端口", "rx_counter": "RX计数器", "status": "状态", "tampered_port": "被篡改的端口", "tampered_port_status": "篡改的端口 {{port}}", "title": "GOOSE检查", "type": "类型", "vid": "VID"}, "ipsec": {"l2tp_status": "IPSec L2TP状态", "local_gateway": "本地网关", "local_subnet": "本地子网", "name": "IPSec名称", "phase_1_status": "IPSec第1阶段状态", "phase_2_status": "IPSec第2阶段状态", "remote_gateway": "远程网关", "remote_subnet": "远程子网", "title": "IPsec状态"}, "link": {"from": "从", "port": "端口", "sfpTitle": "SFP 信息", "speed": "链结速度", "title": "链结信息", "to": "至"}, "management_interfaces": {"http_port": "HTTP端口", "https_port": "HTTPS端口", "profinet_enabled": "PROFINET启用", "ssh_port": "SSH端口", "telnet_port": "Telnet端口", "title": "管理界面"}, "mms": {"title": "MMS资讯"}, "modbus_device_property": {"model": "模型", "revision": "修订", "title": "Modbus 设备属性", "vendor": "供应商"}, "not_selected": "选择一个模块以显示设备详情", "other_device_properties": {"active_redundancy_protocol": "已启用的冗余协议", "auto_ip_config": "自动IP配置", "default_gateway": "默认网关", "dns_1_ip_address": "DNS 1 IP地址", "dns_2_ip_address": "DNS 2 IP地址", "ip_ad_ent_addr": "ipAdEntAddr", "ip_address": "IP地址 (mib)", "mac_address": "MAC地址 (mib)", "model_name": "型号名称", "monitor_current_mode": "监控当前模式", "monitor_down_stream_rate": "监控下行速率", "monitor_snr": "监控信噪比", "monitor_up_stream_rate": "监控上行速率", "netmask": "子网掩码", "title": "其他设备属性"}, "port": {"if_number": "ifNumber", "interface": "接口", "number_of_ports": "端口数量", "poe_port_class": "PoE端口类", "poe_power_legacy_pd_detect": "PoE电源旧版PD检测", "poe_power_output_mode": "PoE电源输出模式", "title": "端口信息"}, "power": {"power_1_status": "电源 1 状态", "power_2_status": "电源 2 状态", "title": "电源状态"}, "redundancy": {"active_redundancy_protocol": "已启用的冗余协议", "dh": "Dual Homing", "iec_624393_redundancy_protocol": "IEC 62439-3 冗余协议", "rstp": "RSTP", "tc": "Turbo Chain", "title": "冗余", "trv2": "Turbo Ring V2"}, "selected_module": "选定模块", "snmp": {"1st_trap_community": "第一组 Trap Community", "2nd_trap_server_community": "第二组 Trap Community", "inform_enabled": "通知启用", "inform_retries": "通知重试", "inform_timeout": "通知超时", "read_community": "Read Community", "title": "SNMP信息", "trap_server_address_1": "第一组 Trap 服务器地址", "trap_server_address_2": "第二组 Trap 服务器地址"}, "title": "设备属性", "vpn": {"from_ip": "VPN来自IP", "local_connection_name": "本地VPN连接名称", "remote_connection_name": "远程VPN连接名称", "to_ip": "VPN到IP"}, "wireless": {"channel_width": "通道宽度", "client_ip": "客户端 IP", "client_mac": "客户端 MAC", "client_RSSI": "客户端场强 (dBm)", "rf_type": "RF Type", "ssid_index": "SSID Index", "title": "无线信息", "vap_mgmt_encryption": "VAP 管理加密", "vap_wpa_encrypt": "VAP WPA 加密", "vapAuthType": "VAP 认证类型"}}, "DEVICE_SETTING": {"advanced": "进阶", "alias": "别名", "alias_input_invalid": "请输入有效的设备别名", "apply_fail": "无法设置设备访问密码", "apply_success": "已成功设置设备参数", "availability_time_frame": "妥善率计算时间长度", "get_parameter_fail": "无法取得设备参数", "input_error_message": "输入错误，请检查您的设置，然后重试", "modify_device_alias": "修改设备别名", "password": "密码", "password_input_invalid": "请输入有效密码", "polling_interval": "轮询间隔", "polling_ip": "轮询 IP", "snmp_configuration": "SNMP 配置", "snmp_port_invalid": "请输入有效SNMP端口", "title": "设备设置", "use_global": "使用全局用户名称和访问密码", "use_global_device_settings": "使用全局设备设置", "username": "用户名称", "username_input_invalid": "请输入有效的用户名"}, "DEVICE": {"device_properties": "设备属性", "device_role": "设备角色", "filter_device": "键入以筛选设备", "filter_register_device": "键入以筛选注册设备", "na": "不明", "properties": {"availability": "可用性", "device_alias": "设备别名", "device_ip": "设备 IP", "firmware_version": "固件版本", "location": "位置", "mac_address": "MAC 地址", "model_name": "型号名称", "mxsec_flag": "安全模块", "severity": "严重性"}, "registered_devices": "已注册", "site_name": "站点名称", "title": "设备列表", "unregistered_devices": "未註冊"}, "DeviceDashboard": {"avg_erase_count": "Avg. <PERSON><PERSON>", "change_disk_hint": "请更换磁盘", "chartTitle": {"60s_cpu": "CPU 利用率（过去 60 秒）", "connection": "连线状态", "cpu": "CPU使用情况", "disk": "分区使用", "memory": "内存使用情况", "noiseFloor": "Noise Floor", "raid_mode": "RAID模式", "signalStrength": "场强", "smart": "S.M.A.R.T.​", "snr": "信噪比", "traffic": "流量负载"}, "connected": "已连接", "current_status": "实时状态：", "cycle_limitation": "Cycle Limitation", "icmp_not_support": "ICMP 设备不支援此功能", "link_down_port": "断线端口", "link_up_port": "连接端口", "managed": "管理中", "migrating_data": "迁移资料", "no_raid": "没有 RAID", "normal": "正常", "raid": "RAID", "rebuild": "重建", "smart_hint": "(Self-Monitoring Analysis and Reporting Technology) 代表磁盘的健康状态和寿命信息", "unreachable": "无法使用{{warningWording}}访问该设备，MXview One 可能无法检索完整的设备信息。"}, "DIALOG": {"add_wifi_ssid": {"aes": "AES", "clear_all_existing_ssid": "清除所有现有 SSID", "eapol_version": "EAPOL 版本", "encryption": "加密", "open": "Open", "passphrase": "密码", "personal": "Personal", "protected_management_frame": "受保护的管理讯框", "rf_band": "RF 频段", "security": "安全", "ssid": "SSID", "title": "添加 Wi-Fi SSID", "tkip_aes_mixed": "TKIP / AES 混合", "wpa_mode": "WPA 模式"}, "auto_layout": {"desc": "您确定要自动排列拓扑? (目前的拓扑将会被覆盖)", "title": "自动布置"}, "auto_topology": {"advanced": "进阶拓扑分析", "advanced_desc": "*将花费较长时间", "advanced_hint": "会在支持LLDP或转发表的ICMP设备之间增加链结", "fail": "自动拓扑失败", "link_check": "严格链接验证模式", "link_check_hint": "如果启用，设备之间的链接只有在两端的设备在其 LLDP 表中有其他设备的信息时才会显示在拓扑结构上。", "new_topology": "创建全新拓扑", "new_topology_desc": "现有链结将会被删除", "success": "自动拓扑成功", "title": "自动拓扑", "update_topology": "更新现有拓扑", "update_topology_desc": "保留现有链结, 新链结将会被增加"}, "background_dialog": {"content": "请先设置背景图\n 背景图可为楼层平面图或可显示其覆盖区的图片", "set_now": "现在设置", "title": "设置背景"}, "change_group": {"assign_to_group": "分配到群组", "change_group_fail": "更改群组失败", "change_group_success": "更改群组成功", "current_group": "群组", "ip": "IP 地址", "title": "更改群组"}, "change_wifi_channel": {"channel": "通道", "channel_width": "通道宽度", "execute_button": "更改", "title": "更改 Wi-Fi 通道"}, "create_group": {"assign_group": "分配到群组", "create_group_fail": "创建群组失败", "create_group_success": "创建群组成功", "current_group": "群组", "empty_group_name": "您必须输入群组名称", "group_desc": "群组描述", "group_name": "群组名称", "parent_group": "上层群组", "title": "创建群组"}, "create_snapshot": {"execute_button": "创建", "title": "创建快照"}, "data_not_ready": {"content": "设备资料还没有准备好，请稍后再试。", "title": "请稍后再试"}, "delete_account": {"delete_confirm_message": "是否确实要删除此帐户?", "title": "删除帐户"}, "delete_background": {"desc": "确定要删除背景?", "failed": "删除背景失败", "success": "删除背景成功", "title": "删除背景"}, "delete_custom_opc": {"delete_confirm_message": "您确定要删除这个自定义OPC标签吗？", "title": "删除自定义OPC标签"}, "delete_device": {"delete_wireless_client_alert": "被删除的无线客户端设备的历史数据也将被清除，并影响所列功能的可追溯性。您想继续吗？", "delete_wireless_client_alert_title": "确认设备的删除", "desc": "确定要删除设备吗?", "desc_multi": "确定要删除这些设备吗?", "failed": "删除设备失败", "success": "删除设备成功", "title": "删除设备"}, "delete_group": {"desc": "确定要删除群组吗?", "desc_multi": "确定要删除这些群组吗?", "failed": "删除群组失败", "success": "删除群组成功", "title": "删除群组"}, "delete_link": {"desc": "确定要删除连线吗?", "desc_multi": "确定要删除这些连线吗?", "failed": "删除连线失败", "success": "删除连线成功", "title": "删除连线"}, "delete_objects": {"desc": "确定要删除所有选定的物件吗?", "failed": "删除物件失败", "success": "删除物件成功", "title": "删除物件"}, "delete_site": {"desc": "确定要删除站点吗?", "failed": "删除站点失败", "success": "删除站点成功", "title": "删除站点"}, "device_settings": {"fail": "设定链路预算参数(个别设备)失败", "rx_antenna_gain": "接收天线增益", "rx_cable_loss": "接收线缆损耗", "success": "设定链路预算参数(个别设备)成功", "title": "链路预算参数(个别设备)", "tx_antenna_gain": "发送天线增益", "tx_cable_loss": "发送线缆损耗"}, "disable_unsecured": {"execute_button": "禁用 HTTP 和 Telnet", "title": "禁用不安全的 HTTP 和 Telnet 终端"}, "disable_unused": {"execute_button": "禁用未使用的端口", "keep_port_available": "确保使用中的端口不会因为暂时断线而被关闭", "title": "禁用未使用的以太网和光纤端口"}, "discovery_device": {"another_discovery_error": "另一个搜索正在处理", "discovering": "正在发现设备", "discovery_finish": "设备发现完毕", "error": "无法发现设备", "title": "设备发现"}, "dynamic_mac_sticky": {"address_limit": "位址限制", "alias": "别名", "mac_sticky": "Sticky MAC", "mac_sticky_settings": "Sticky MAC 设置", "packet_drop": "丢弃数据包", "port": "端口", "port_duplicated": "该端口已被配置。", "port_format_not_equal": "型号必须具有相同的端口格式和端口数量。", "port_selection_guide": "端口对照表", "port_shutdown": "关闭端口", "security_action": "安全行动", "title": "动态 Sticky MAC"}, "export_config": {"config_center": "设备配置中心", "config_file": "组态档", "export": "导出", "fail": "导出设备设置文件失败", "hint": "* 请确认设备的使用者名称和密码有在进阶设定里设定正确", "success": "导出设备设置文件成功", "title": "导出配置"}, "goose": {"how_to_resolve": "如何解决？", "import_scd_tooltip": "请导入一个SCD文件以查看GOOSE信息。点击Power > Import SCD。", "ip_port": "{{ ip }}的{{ port }}端口", "open_web_console": "打开Web控制台", "port_tampered_msg": "GOOSE端口被篡改的原因是", "port_tampered_title": "解决GOOSE端口被篡改的问题", "reset_goose": "重置被篡改的GOOSE信息", "reset_goose_desc": "GOOSE信息：{{ cbName }}/{{ appId }}/{{ mac }}。", "reset_goose_title": "您确定要重置这个被篡改的GOOSE信息的所有实例吗？", "resolve_goose_tampered_desc_1": "请尝试以下步骤来解决GOOSE端口被篡改的问题", "resolve_goose_tampered_desc_2": "1. 检查 IED 的设置", "resolve_goose_tampered_desc_3": "确保IED的GOOSE发布/订阅信息被正确设置。", "resolve_goose_tampered_desc_4": "2. 检查端口状态", "resolve_goose_tampered_desc_5": "请检查{{ ip }}的端口{{ port }}状态。", "resolve_goose_tampered_desc_6": "2. 检查以确保所有设备都被授权", "resolve_goose_tampered_desc_7": "请检查网络上是否有任何未经授权的设备。", "resolve_goose_tampered_desc_8": "请尝试以下步骤解决GOOSE SA篡改的问题", "resolve_goose_timeout_desc_1": "尝试以下步骤来解决GOOSE超时问题。", "resolve_goose_timeout_desc_10": "还是不能运作？", "resolve_goose_timeout_desc_11": "移除SFP模块并重新安装。", "resolve_goose_timeout_desc_12_1": "如果有任何技术问题，请先联系您的", "resolve_goose_timeout_desc_12_2": "渠道合作伙伴", "resolve_goose_timeout_desc_12_3": "。", "resolve_goose_timeout_desc_13_1": "联系", "resolve_goose_timeout_desc_13_2": "Moxa技术支持", "resolve_goose_timeout_desc_13_3": "如果您仍然需要额外的支持。", "resolve_goose_timeout_desc_2": "1. 检查IED的设置", "resolve_goose_timeout_desc_3": "确保IED的发布/订阅GOOSE信息的设置正确。", "resolve_goose_timeout_desc_4": "2. 确保端口不是断线状态", "resolve_goose_timeout_desc_5": "检查 GOOSE 流({{ cbName }}/{{ appId }}/{{ mac }})上的每个设备的端口不是断线状态。", "resolve_goose_timeout_desc_6": "3. 确保端口没有任何 TX/RX 错误。", "resolve_goose_timeout_desc_7": "点击一个连线，选择\"连线流量\"，查看\"封包错误率\"部分。确保该端口没有任何错误。", "resolve_goose_timeout_desc_8": "4. 检查光纤端口是否超过一定的阈值", "resolve_goose_timeout_desc_9": "选择 \"SFP\" > \"SFP列表\" 按钮。确保端口没有超过一定的阈值。", "sa_tampered_msg": "GOOSE SA被篡改", "sa_tampered_name_msg": "GOOSE信息（{{ cbName }}/{{ appId }}/{{ mac }}）与另一个GOOSE源地址冲突。", "sa_tampered_title": "解决GOOSE SA被篡改的问题", "tampered": "被篡改", "timeout": "超时", "timeout_msg": "GOOSE超时的原因是", "timeout_title": "解决GOOSE超时问题"}, "import_config": {"config_center": "设备配置中心", "config_file": "组态档", "config_file_error": "Mxview只支持ini档案格式", "config_file_size_error": "最大文件大小为3MB", "fail": "导入设备配置文件失败", "hint": "* 请确认设备的使用者名称和密码有在进阶设定里设定正确", "import": "导入", "success": "导入设备设置文件成功", "title": "导入配置"}, "link_traffic": {"date": "日期", "from": "从", "packet_error_rate_title": "数据包错误率", "port_traffic_title": "带宽使用率", "time": "时间", "to": "到", "utilization": "带宽利用率", "value": "值"}, "mac_sticky_switch": {"mac_sticky": "Sticky MAC", "title": "Sticky MAC 开/关"}, "maintain_group": {"change_icon": "更改群组图示", "create": "创建", "create_group_fail": "创建群组失败", "create_group_success": "创建群组成功", "delete": "删除", "delete_group_fail": "删除群组失败", "delete_group_success": "删除群组成功", "empty_group_name": "您必须输入群组名称", "group_desc": "群组描述", "group_name": "群组名称", "modify_group_fail": "修改群组失败", "modify_group_success": "修改群组成功", "reset_icon": "重置为默认图示", "title": "群组维护"}, "ping": {"failed": "<PERSON> 失败", "title": "<PERSON>"}, "policy_profile": {"delete_msg": "您确实要删除选定的配置文件吗？", "delete_title": "删除配置文件"}, "reboot": {"execute_button": "重启", "reboot_sequence": "重启顺序", "title": "重启", "unable_determine_reboot_sequence_hint": "无法決定重新启动顺序。 确保拓扑包含一台装有 MXview One 的计算机，然后重试。"}, "relearn_dynamic_mac_sticky": {"execute_button": "重新学习", "title": "重新学习动态 Sticky MAC"}, "restore_to_create_snapshot": {"execute_button": "还原", "title": "还原快照"}, "scd": {"import_failed_desc": "请看下面的问题列表，请更正SCD文件并重新尝试导入。", "import_failed_title": "导入SCD的问题", "import_succeed_desc": "GOOSE信息和流程设计已成功地建立在网络拓扑结构中。", "import_succeed_title": "导入完成!", "missing": "缺少", "missing_device": "找不到以下设备", "missing_device_desc": "请看下面的问题列表。", "scd_file_error": "MXview One只支持.scd格式", "scd_file_size_error": "文件最大尺寸为100 MB", "select_file": "选择档", "tag": "标签", "topology_change_desc_1": "尝试以下这些步骤来解决问题。", "topology_change_desc_2": "1. 添加丢失的设备", "topology_change_desc_3": "选择 \"编辑\" > \"添加设备\" 按钮。", "topology_change_desc_4": "2. 再次导入SCD档", "topology_change_desc_5": "选择 \"电源\" 按钮 > \"导入SCD\"。", "topology_change_title": "改变拓扑的问题", "visualize_goose_messages": "要在IED之间实现GOOSE信息的可视化，必须先导入一个SCD档。", "visualize_goose_messages_title": "可视化GOOSE信息"}, "set_background": {"browse": "浏览图档", "desc": "拖曳一个图档到这里或是", "desc1": "来设置背景", "failed": "设置背景失败", "image": "图档", "image_alpha": "透明度", "image_error": "选择的文件不是图片", "image_position": "位置", "image_saturation": "饱和度", "image_x": "X", "image_y": "Y", "preview": "预览", "size_error": "图片大小必须在1KB ~ 20MB之间。", "success": "设置背景成功", "title": "设置背景", "topology_size": "拓扑大小"}, "set_document": {"current_filename": "当前文件名", "delete": "删除文件", "error": "Mxview只支持PDF档案格式", "failed": "设置文件失败", "file_size_error": "最大文件大小为20MB.仅限PDF。", "open": "打开文件", "set": "设置文件", "success": "设置文件成功", "title": "设置文件", "upload": "选择上传文件"}, "set_port_label": {"error": "错误", "failed": "设定联机标签失败", "from": "从:", "success": "设定联机标签成功", "title": "设定联机标签", "to": "到:", "use_custom_label": "自定义标签", "vpn_link_desc": "不能修改VPN联机标签"}, "set_scale": {"error": "参数错误", "fail": "设定比例失败", "success": "设定比例成功", "title": "设定比例"}, "severity_threshold": {"bandwidth_input_invalid": "请输入一个0-100的整数", "bandwidth_utilization": "带宽利用率", "critical": "紧要", "failed": "设定严重性阈值失败", "information": "信息", "over": "高于", "packet_error_rate": "封包错误率", "sfp_rx_over": "SFP RX 高于", "sfp_rx_under": "SFP RX 低于", "sfp_temp_over": "SFP 温度高于", "sfp_threshold": "SFP 阈值", "sfp_tx_over": "SFP TX 高于", "sfp_tx_under": "SFP TX 低于", "sfp_vol_over": "SFP 电压高于", "sfp_vol_under": "SFP 电压低于", "success": "设定严重性阈值成功", "title": "严重性阈值", "under": "低于", "warning": "警告"}, "sfp_info": {"date": "日期", "dateError": "日期区间错误", "from": "从", "port": "端口", "sfpRxPower": "SFP RX", "sfpRxPower_label": "RX", "sfpRxPower_scale": " dBm", "sfpTemperature": "SFP 温度", "sfpTemperature_label": "温度", "sfpTemperature_scale": " °C", "sfpTxPower": "SFP TX", "sfpTxPower_label": "TX", "sfpTxPower_scale": " dBm", "sfpVoltage": "SFP 电压", "sfpVoltage_label": "电压", "sfpVoltage_scale": " V", "time": "时间", "title": "SFP 信息", "to": "到", "value": "值"}, "sfp_sync": {"confirm_desc": "您确定您要从设备上同步SFP阈值吗？", "content": "您可以从MOXA交换机上同步SFP 阈值。同步后，光纤检查的温度、Tx Power和Rx Power将被同步到每个连结的SFP 阈值。", "failed": "同步SFP阈值失败", "hint": "* 要检查SFP阈值，您可以点击一个连结，选择严重程度阈值>SFP阈值。", "success": "同步SFP阈值成功", "title": "从设备同步SFP阈值"}, "wireless_settings": {"fail": "设定链路预算参数(通用)失败", "rxSensitivityHigh": "接收感度(高)", "rxSensitivityLow": "接收感度(低)", "rxSensitivityMedium": "接收感度(中)", "sr": "预留安全系数", "success": "设定链路预算参数(通用)成功", "title": "链路预算参数(通用)"}}, "EMBED_WIDGET": {"click_preview": "点击预览", "copied_to_clipboard": "链接已复制到剪贴板", "copy_link": "复制链接", "custom": "自定义", "desc": "将以下内容粘贴到任何 HTML 页面中", "embed": "嵌入", "height": "高度", "layout_1": "版型 1", "layout_2": "版型 2", "layout_3": "版型 3", "layout_4": "版型 4", "link": "链接", "no_api_key": "您必须要先建立 API 密钥", "preview": "预览", "recent_event": "近来事件", "select_api_key": "选择 API 密钥", "select_layout": "选择版型", "title": "网页嵌入微件", "topology": "拓扑", "topology_recent_event": "拓扑与近来事件", "width": "宽度"}, "error_handler": {"error_session_expired_dialog": "登入凭证已失效。系统将会自动帮您转址回到登入页面。"}, "ERROR_MESSAGE": {"get_data_fail": "无法检索数据", "input_invalid_char": "请输入有效的名称", "input_invalid_characters": "该字段不能包含以下任何字符 #%&*:<>?|{}\\\"/", "input_invalid_contact": "请输入有效的联系", "input_invalid_email": "请输入一个有效的电子邮件", "input_invalid_location": "请输入有效的地点", "input_invalid_mac": "MAC 地址无效", "input_invalid_password_characters": "这个字段不能包含以下任何字符 '\\\"/`。", "input_invalid_script": "此字段不得包含以下任何字符：#%&amp;*{}|:&quot;&lt;&gt;?/\\", "input_ip_invalid": "请输入有效的IP地址", "input_required": "此值为必填", "non_restricted_ascii": "ASCII 字符，除了 ' \\\" ` \\\\"}, "errors": {"A001": "缺少必填栏位", "A002": "缺少查询字符串", "A003": "格式不正确", "D001": "已达到最大授权数量", "D002": "找不到该设备", "D003": "设备必须在线", "D004": "设备已被删除", "F001": "找不到固件", "F002": "该固件已存在", "F003": "已达到固件文件的最大数量", "G001": "该群组已存在", "G002": "找不到该群组", "G003": "默认组无法修改", "G004": "管理员用户无法分配到组", "I001": "该接口已经存在", "I002": "找不到接口", "I003": "默认组无法修改", "I004": "此接口由安全配置文件引用。", "L001": "无效的授权码", "L002": "授权已过期", "L003": "激活码重复", "L004": "已达到最大节点数", "L005": "无法激活或停用该设备", "L006": "开始时间无效", "L007": "新型授权的开始时间无效", "O001": "此物件已存在", "O002": "找不到物件", "O003": "该物件由安全配置文件引用。", "P001": "找不到该安装包", "P002": "此安装包已存在", "P003": "已达到安装包数量上限", "P004": "不受支持的版本", "S001": "更新数据库错误", "SP001": "此配置文件已存在", "SP002": "找不到配置文件", "T001": "未授权的token", "T002": "Token 已过期", "T003": "token 无效", "U001": "没有权限", "U002": "此用户名已存在", "U003": "找不到用户", "U004": "找不到该角色", "U005": "无效的用户名或密码", "U006": "密码不符合最小长度", "U007": "密码超出最大长度", "U008": "密码不能与用户名相同", "U009": "必须至少包含一个大写字符", "U010": "必须至少包含一个小写字符", "U011": "必须至少包含一位数字", "U012": "必须至少包含一个非字母数字字符", "U013": "密码不能与原密码相同", "U014": "用户名无效", "Unknown": "未知错误"}, "EULA": {"agree_hint": "请同意使用MXview One的协议。", "eula_title": "EULA"}, "EVENT_MENU": {"ack": "确认", "ack_all": "全部确认", "filter_event": "筛选条件"}, "EVENT": {"ack_all_events": {"ack_all_event_success": "全部事件已被确认", "button_ack_hint": "确认选择的事件", "button_hint": "确认全部事件", "confirm_message": "所有事件将全部确认，您确定要继续?", "unable_ack_all_event": "无法确认全部事件"}, "ack": {"ack_fail": "无法确认事件", "acked": "已确认", "any": "任何", "unacked": "未确认"}, "all_event": "所有事件", "all_group": "所有群组", "all_site": "所有站点", "clear_all_events": {"button_hint": "清除全部事件", "clear_all_event_success": "事件已全部清除", "confirm_message": "所有事件全部清除, 您确定要继续?", "confirm_message_network": "所有网络和设备事件都将被清除。您确定要继续吗？", "confirm_message_system": "所有系统事件都将被清除。您确定要继续吗？", "unable_clear_all_event": "不能清除所有事件"}, "custom_events": {"activate": "启用事件", "add_custom_event": "添加自定义事件", "all": "所有事件", "all_devices": "所有设备", "apply_fail": "无法设置自定义事件", "apply_success": "自定义事件已成功添加", "below": "低于", "condition": "条件", "condition_operator": "条件", "condition_value": "条件值", "consecutive_updates": "连续轮询次数", "delete_fail": "无法删除自定义事件", "delete_success": "自定义事件已成功删除", "description": "描述", "device_properties": "设备属性", "devices": "设备", "duration": "触发需时", "equal": "等于", "event_name": "事件名称", "filter_custom_event": "键入以筛选自定义事件", "get_fail": "无法获取自定义事件", "helper": "您可以自定事件并且将这些事件注册到某些设备中，当设备某个属性符合事件的条件时就会发出事件告警", "not_equal": "不等于", "over": "高于", "recovery_description": "恢复说明", "register": "注册", "register_devices": "注册设备", "search": "搜索", "severity": "严重性", "title": "自定义事件", "update_custom_event": "更新自定义事件", "update_fail": "无法更新自定义事件", "update_success": "自定义事件已成功更新"}, "event_description": {"abc_attache_warning": "插入USB设备", "abc_auto_import_warning": "自动汇入失败", "abc_config_warning": "组态汇出失败", "abc_detache_warning": "移除USB设备", "abc_log_warning": "事件汇出失败", "abc_space_warning": "空间不足", "abc_unauthorized_warning": "侦测到未经授权的媒体", "abc_unknow_warning": "未知", "abc02_warning": "USB 事件: {{param}}", "account_audit_baseline_failed": "无法完成账户审计。无法检索所有设备数据。", "account_audit_baseline_match": "账户审计已成功完成。结果与基线相符。", "account_audit_failed": "无法完成账户审计。无法从 {{ip}} 的设备检索数据。", "account_audit_match": "账户审计已成功完成。结果与基线相符。", "account_audit_mismatch": "账户审计已成功完成。结果与基线不相符。", "account_audit_unable_retrieve_device": "无法完成账户审核。无法检索所有设备数据。", "accountAudit1": "账号审计与基线不相符", "accountAudit2": "未能完成账户审計", "all_event_clear": "所有事件已被清除", "auth_fail": "验证设备登录失败。", "availability_down": "设备可用性低于阈值", "availability_down_recovery": "设备可用性高于阈值", "background_scan_found": "发现新设备（IP：{{ip}}）。", "cli_button_event_all_failed": "{{user}} 自 IP: {{sourceIP}} 执行了按钮：{{cliName}}。执行结果为全部失败。", "cli_button_event_all_finished": "{{user}} 自 IP: {{sourceIP}} 执行了按钮：{{cliName}}。执行结果为全部完成。", "cli_button_event_all_partially_finished": "{{user}} 自 IP: {{sourceIP}} 执行了按钮：{{cliName}}。执行结果为部分完成。", "cli_button_event_start": "{{user}} 自 IP：{{sourceIP}} 执行了按钮：{{cliName}}。", "cli_saved_script_event": "{{user}} 自 IP：{{sourceIP}} 执行了 CLI 命令：{{cliName}}", "cli_script_event": "{{user}} 自 IP：{{sourceIP}} 开始执行 CLI。", "cold_start": "冷启动", "custom_event_detail": "{{param1}}. 阈值={{param2}}, 值={{param3}}. {{param4}}", "custom_event_recovery": "自定义事件已恢复", "custom_event_recovery_detail": "{{param1}} 已复原. 阈值={{param2}}, 值={{param3}}. {{param4}}", "custom_event_trigger": "自定义事件被触发", "cybersecurity_event_trigger": "网络安全事件", "ddos_under_attack": "安全路由器遭受DoS攻击", "ddos_under_attack_recovery": "安全路由器DoS攻击恢复正常", "device_configuration_change": "设备配置已更改。", "device_firmware_upgrade": "设备固件更新", "device_infom_receive": "收到设备通知", "device_lockdown_violation": "设备锁定违规", "device_power_down": "电源 {{param}} 断电", "device_power_down_off_to_on": "PWR {{param}} 关 > 开。", "device_power_on": "电源 {{param}} 通电", "device_power_on_to_off": "PWR {{param}} 开 > 关。", "device_snmp_reachable": "设备恢复通过SNMP访问", "device_snmp_unreachable": "设备无法通过SNMP访问", "di_off": "数字输入 {{param}}} 关闭", "di_on": "数字输入 {{param}}} 开启", "disk_space_not_enough": "硬盘剩余空间低于阈值", "event_config_import": "已导入新的配置文件", "event_ddos_attack": "检测到 DoS 攻击", "event_ddos_attack_recovery": "安全路由器DoS攻击恢复正常", "event_dying_gasp": "系统电源已关闭。设备由其电容器供电", "event_eps_is_off": "PoE外部电源已关闭", "event_eps_is_on": "PoE外部电源已打开", "event_firewall_attack": "违反防火牆规则", "event_firewall_attack_recovery": "违反防火牆规则恢复正常", "event_ieee_lldp_table_change": "{{trapDetail}}", "event_ieee_rstp_root_change": "RSTP 拓扑中新的 root 已被选择", "event_ieee_rstp_topology_change": "拓扑已被改为RSTP", "event_linux_account_setting_change": "帐户设定 {{username}} 已被更改", "event_linux_config_change": "{{modules}} 配置已被 {{username}} 修改。", "event_linux_config_import": "{{username}} 设定汇入{{successOrFail}}", "event_linux_config_import_failed": "失败", "event_linux_config_import_succeed": "成功", "event_linux_coupling_change": "Turbo Ring v2 coupling 路径状态已被改变", "event_linux_di_off": "DI {{index}} 已被关闭", "event_linux_di_on": "DI {{index}} 已被开启", "event_linux_dotlx_auth_fail": "802.1X 认证失败在端口{{portIndex}} 因 {{reason}}", "event_linux_dual_homing_change": "Dual Homing 路径已被转换", "event_linux_log_capacity_threshold": "事件纪录数量 {{value}} 已超过阈值", "event_linux_low_input_voltage": "输入电压低于阈值", "event_linux_master_change": "Ring {{index}} master 已被改变", "event_linux_master_mismatch": "Ring {{index}} master 设定并不匹配", "event_linux_over_power_budget_limit": "所有PDs的消耗电源 {{value}} 已超过最大输入电源值 {{threshold}}", "event_linux_password_change": "用户 {{username}} 密码已被更改", "event_linux_pd_no_response": "PoE 端口 {{portIndex}} 对PD failure check 无反应", "event_linux_pd_over_current": "PoE 端口 {{portIndex}} 电源已超过安全标准", "event_linux_pd_power_off": "PoE 端口 {{portIndex}} PD 电源关闭", "event_linux_pd_power_on": "PoE 端口 {{portIndex}} PD 电源开启", "event_linux_port_recovery_by_ratelimit": "端口 {{portIndex}} 因限速已回复功能", "event_linux_port_shutdown_by_ratelimit": "由于流量超过速率限制，端口 {{portIndex}} 被阻断。", "event_linux_port_shutdown_by_security": "端口 {{portIndex}} 因 port security 被关闭", "event_linux_power_detection_fail": "PoE 端口 {{portIndex}} 显示设备状态为{{devicetype}}，建议 : {{suggestion}}.", "event_linux_power_detection_fail_devietype_na": "不明", "event_linux_power_detection_fail_devietype_noPresent": "不显示", "event_linux_power_detection_fail_devietype_unknown": "未知", "event_linux_power_detection_fail_suggestion_disable_POE": "关闭 POE", "event_linux_power_detection_fail_suggestion_enable_legacy": "开启 legacy", "event_linux_power_detection_fail_suggestion_enable_POE": "打开 POE", "event_linux_power_detection_fail_suggestion_no": "无", "event_linux_power_detection_fail_suggestion_rais_EPS_voltage": "升起 Eps 电压", "event_linux_power_detection_fail_suggestion_select_auto": "选择自动", "event_linux_power_detection_fail_suggestion_select_force": "选择 force", "event_linux_power_detection_fail_suggestion_select_high_power": "选择 high power", "event_linux_power_off": "电源 {{index}} 已被关闭", "event_linux_power_on": "电源 {{index}} 已被开启", "event_linux_redundant_port_health_check": "备用端口 {{portIndex}} health check 失败", "event_linux_RMON_trap_is_falling": "RMON trap 已结束", "event_linux_RMON_trap_is_raising": "RMON trap 已触发", "event_linux_rstp_invalid_bpdu": "RSTP 端口 {{portIndex}} 收到不正确的BPDU (型态: {{type}}, 值:{{value}})", "event_linux_rstp_migration": "端口 {{portIndex}} 已从 {{originTopology} 被改成 {{changeTopology}}", "event_linux_rstp_new_port_role": "RSTP 端口 {{portIndex}} 角色从 {{originalRole}} 被改为 {{newRole}}", "event_linux_ssl_cer_change": "SSL 认证已被更新", "event_linux_topology_change": "拓扑结构已经改变。", "event_linux_topology_change_by_type": "拓扑已被更改为 {{topologyType}}", "event_linux_user_login_lockout": "用户 {{username}} 因 {{param}} 次登入失败帐户被锁", "event_linux_user_login_success": "用户 {{username}} 从 {{interface}} 成功登入", "event_log_cleared_trap_event_info": "Event logs 已被清除 (使用者: {{ user }}, IP: {{ ip }}, 介面: {{ interface }})", "event_message_serial_device_port_any_recovery": "串行端口 {{portnum}} 已恢复正常运行。", "event_message_serial_device_port_break": "串行端口 {{portnum}} 收到错误：中断错误计数。", "event_message_serial_device_port_frame": "串行端口 {{portnum}} 收到错误：帧错误计数。", "event_message_serial_device_port_overrun": "串行端口 {{portnum}} 收到错误：溢出错误计数。", "event_message_serial_device_port_parity": "串行端口 {{portnum}} 收到错误：奇偶校验错误计数。", "event_message_serial_device_port_rx": "串行端口 {{portnum}} 的 RX 在过去 {{min}} 分钟内未接收到任何数据。", "event_message_serial_device_port_rx_recovery": "串行端口 {{portnum}} 的 RX 已恢复接收数据。", "event_message_serial_device_port_rxtx": "串行端口 {{portnum}} 的 RX 和 TX 在过去 {{min}} 分钟内未接收到任何数据。", "event_message_serial_device_port_rxtx_recovery": "串行端口 {{portnum}} 的 RX 和 TX 已恢复接收数据。", "event_message_serial_device_port_tx": "串行端口 {{portnum}} 的 TX 在过去 {{min}} 分钟内未接收到任何数据。", "event_message_serial_device_port_tx_recovery": "串行端口 {{portnum}} 的 TX 已恢复接收数据。", "event_not_found_type": "Unknown event type: {{ eventType }}", "event_pd_check_fail": "PD 失效检查 (无回应)", "event_pd_over_current": "PoE 端口 {{portIndex}} 电流超标/短路", "event_pd_power_off": "PoE 端口 {{portIndex}} 电源关闭", "event_pd_power_on": "PoE 端口 {{portIndex}} 电源开启", "event_prp_function_fail": "PRP功能失败", "event_serial_device_port_break": "串行端口收到错误：中断错误计数", "event_serial_device_port_frame": "串行端口收到错误：帧错误计数", "event_serial_device_port_overrun": "串行端口收到错误：溢出错误计数", "event_serial_device_port_parity": "串行端口收到错误：奇偶校验错误计数", "event_serial_device_port_rx": "串口 RX 没有接收到任何数据", "event_serial_device_port_rxtx": "串口 RX 和 TX 没有接收到任何数据", "event_serial_device_port_tx": "串口 TX 没有接收到任何数据", "event_sfp_rx_below": "SFP 端口 {{portIndex}} 的 RX 功率 ({{currentdB}} dBm) 低于阈值 ({{thresholddB}} dBm)。", "event_sfp_rx_below_recovery": "SFP 端口 {{portIndex}} Rx {{recoverydB}}dBm 已恢复正常", "event_sfp_temp_over": "SFP 端口 {{portIndex}} 的模块温度 ({{currentTemp}}ºc) 超过了阈值 ({{currentTemp}}ºc)。", "event_sfp_temp_over_recovery": "SFP 端口 {{portIndex}} 温度 {{recoveryTemp}}ºc 已恢复正常", "event_sfp_tx_below": "SFP 端口 {{portIndex}} 的 TX 射功率 ({{currentdB}} dBm) 低于阈值 ({{thresholddB}} dBm)。", "event_sfp_tx_below_recovery": "SFP 端口 {{portIndex}} Tx {{recoverydB}}dBm 已恢复正常", "event_sfp_voltage_below": "SFP 端口 {{portIndex}} 的模块电压 ({{currentVoltage}} V) 低于阈值 ({{thresholdVoltage}} V)。", "event_sfp_voltage_below_recovery": "SFP 端口 {{portIndex}} voltage {{recoveryVoltage}}v 已恢复正常", "event_sfp_voltage_over": "SFP 端口 {{portIndex}} 的模块电压 ({{currentVoltage}} V) 超过了阈值 ({{thresholdVoltage}} V)。", "event_sfp_voltage_over_recovery": "SFP 端口 {{portIndex}} voltage {{recoveryVoltage}}v 已恢复正常", "event_too_many_login_failure": "登录失败太多，暂时无法以Web存取。", "event_too_many_login_failure_recovery": "登录失败太多事件结束。已恢复以Web存取。", "event_tracking_port_enabled_status": "与端口启用相关的 Tracking entries 已更改", "event_tracking_static_route_status_changed": "与静态路由相关的 Tracking entries 已更改", "event_tracking_status_changed": "Tracking entries 的状态已更改", "event_tracking_vrrp_status_changed": "与 VRRP 相关的 Tracking entries 已更改", "event_trusted_access_attack": "违反设备信任访问规则", "event_trusted_access_attack_recovery": "违反信任存取规则恢复正常", "event_user_info_change": "用户信息异动: {{trapoid}}", "event_v3_trap_parse_error": "V3 trap 封包解析错误", "event_v3_trap_parse_error_recovery": "V3 trap parse 封包解析错误事件已清除", "exceed_poe_threshold": "超过PoE系统阈值", "fan_module_malfunction": "风扇模块故障。", "fiber_warning": "{{portIndex}} 光纤警告( {{warningType}} )", "firewall_policy_violation": "防火墙与DoS攻击规则: {{trapoid}}", "firewall_under_attack": "违反防火牆规则", "firewall_under_attack_recovery": "违反防火牆规则恢复正常", "firmware_upgraded": "固件升级", "firmware_version_release": "有固件更新可用。 查看固件管理页面了解详细信息。", "goose_healthy": "GOOSE状态：健康的 \nGOOSE信息 {{ display }}}", "goose_healthy_with_value": "GOOSE status: Healthy \nGOOSE message {{ display }}", "goose_tampered": "GOOSE状态：被篡改", "goose_tampered_with_value": "GOOSE状态：被篡改 \nGOOSE 消息 {{ display }}.", "goose_timeout": "GOOSE狀態：超時", "goose_timeout_with_value": "GOOSE状态： 超时 \nGOOSE 消息 {{ display }}。", "high_cpu_loading": "CPU 负载连续 10 分钟超过 85%。", "icmp_packet_loss_over_critical_threhold": "设备丢包率到达{{param1}}(超过紧要阈值{{param2}})", "icmp_packet_loss_over_critical_threhold_recovery": "设备丢包率恢复到{{param1}}(超过紧要阈值{{param2}})", "icmp_packet_loss_over_threhold": "设备丢包率到达{{param1}}, 超过阈值{{param2}} (基于ICMP)", "icmp_packet_loss_over_threhold_recovery": "设备丢包率恢复到{{param1}}, 超过阈值{{param2}} (基于ICMP)", "icmp_reachable": "设备恢复访问", "icmp_unreachable": "设备无法访问", "iei_fiber_warning": "触发了光纤警告", "input_bandwidth_over_threshold": "输入带宽利用率超过了阈值。", "input_bandwidth_over_threshold_disabled": "端口{{portIndex}}的输入带宽利用率没有设置阈值。", "input_bandwidth_over_threshold_recovery": "端口{{portIndex}}输入带宽利用率恢复，数值为{{currentValue}}。", "input_bandwidth_over_threshold_with_port": "端口 {{portIndex}} 的输入带宽利用率 ({{currentValue}}) 已超过阈值 ({{threshold}})。", "input_bandwidth_under_threshold": "输入带宽利用率低于阈值。", "input_bandwidth_under_threshold_disabled": "端口{{portIndex}}的输入带宽利用率没有设置阈值。", "input_bandwidth_under_threshold_recovery": "端口{{portIndex}}输入带宽利用率恢复，数值为{{currentValue}}。", "input_bandwidth_under_threshold_with_port": "端口 {{portIndex}} 的输入带宽利用率 ({{currentValue}}) 低于阈值 ({{threshold}})。", "input_packet_error_over_threshold": "输入数据包错误率超过了阈值。", "input_packet_error_over_threshold_disabled": "端口{{portIndex}}的输入封包错误率没有阈值设置。", "input_packet_error_over_threshold_recovery": "端口{{portIndex}}输入封包错误恢复，数值为{{currentValue}}。", "input_packet_error_over_threshold_with_port": "端口 {{portIndex}} 的输入数据包错误率 ({{currentValue}}) 超过了阈值 ({{threshold}})。", "insufficient_disk_space": "可用磁盘空间小于 5 GB。", "interface_set_as_ospf_designated_router": "接口设置为 OSPF 指定路由器。", "ip_conflict_detected": "检测到 {{ip}} 的 IP 冲突，MAC 冲突：", "ip_conflict_detected_failed": "无法运行 IP 冲突检测，因为未找到 Npcap/WinPcap/Libpcap", "ip_conflict_recovery": "IP 冲突已解决。", "iw_client_joined": "客户端 加入: {{param}}", "iw_client_left": "客户端 离开: {{param}}", "l3_firewall_policy_violation": "违反防火墙规则（NAT系列）", "license_limitation_reached": "达到最大授权限制。", "license_not_enough": "节点超过授权数量。请删除一些节点，或者添加另一个授权", "license_over": "节点超过授权数量。请删除一些节点，或者添加另一个授权", "lldp_change": "LLDP 数据变更", "logging_capacity": "事件超出容量限制", "login_radius_fail": "通过RADIUS服务器登录认证失败", "login_radius_success": "通过RADIUS服务器录认证成功", "login_tacas_fail": "通过TACACS+服务器登录认证失败", "login_tacas_success": "通过TACACS+服务器登录认证成功", "mac_sticky_violation": "违反 MAC sticky", "mrp_multiple_event": "发生多 MRP 管理员事件。", "mrp_ring_open_event": "发生 MRP 环打开事件。", "mstp_topology_changed": "MSTP 拓扑发生变化", "mxview_autopology_finish": "自动拓扑完成", "mxview_autopology_start": "自动拓扑开始", "mxview_db_backup_fail": "数据库备份失败", "mxview_db_backup_sucess": "数据库备份完成, 存储于 \\%%MXviewPRO_Data\\%%\\db_backup\\{{param1}} {{param2}}", "mxview_job_done": "作业: {{jobname}} 完成了", "mxview_job_start": "作业：{{jobname}}开始", "mxview_server_license_limit": "MXview One Central Manager 没有足够的授权", "mxview_server_start": "MXview One服务器启动", "mxview_sms_fail": "无法发送短信通知", "mxview_sms_success": "MXview One成功发送短信通知", "mxview_user_lockout": "{{param}} 用户暂时停用", "mxview_user_login_fail": "登录 MXview One 失败。", "mxview_user_login_sucess": "使用者登入成功： {{param}}", "mxview_user_logout": "使用者注销: {{param}}", "network_latency": "MXview One Central Manager 的网络延迟超过 100 毫秒", "new_port_role_selected": "选择新端口角色。", "new_root_bridge_selected_in_topology": "在拓扑中选择新的根桥。", "notification_sfp_rx_below": "SFP Rx 低于阈值", "notification_sfp_temp_over": "SFP 温度超过阈值", "notification_sfp_tx_below": "SFP Tx 低于阈值", "notification_sfp_voltage_below": "SFP 电压低于阈值", "notification_sfp_voltage_over": "SFP 电压高于阈值", "nport_syslog_over_threshold": "NPort 的系统日志超过阈值", "opcua_server_start": "MXview One OPC UA 服务器已启动。", "opcua_server_stop": "MXview One OPC UA 服务器已停止。", "ospf_designated_router_changed": "OSPF 指定路由器发生变化。", "ospf_designated_router_interface_and_adjacency_changed": "OSPF  指定路由器接口和邻接关系发生变化。", "out_of_memory": "可用内存不足 20%。", "output_bandwidth_over_threshold": "输出带宽利用率超过了阈值。", "output_bandwidth_over_threshold_disabled": "端口{{portIndex}}的输出带宽利用率没有设置阈值。", "output_bandwidth_over_threshold_recovery": "端口{{portIndex}}输出带宽利用率恢复，数值为{{currentValue}}。", "output_bandwidth_over_threshold_with_port": "端口 {{portIndex}} 的输出带宽利用率 ({{currentValue}}) 超过了阈值 ({{threshold}})。", "output_bandwidth_under_threshold": "输出带宽利用率低于阈值。", "output_bandwidth_under_threshold_disabled": "端口{{portIndex}}的输出带宽利用率没有设置阈值。", "output_bandwidth_under_threshold_recovery": "端口{{portIndex}}输出带宽利用率恢复，数值为{{currentValue}}。", "output_bandwidth_under_threshold_with_port": "端口 {{portIndex}} 的输出带宽利用率 ({{currentValue}}) 低于阈值 ({{threshold}})。", "output_packet_error_over_threshold": "输出数据包错误率超过了阈值。", "output_packet_error_over_threshold_disabled": "端口{{portIndex}}的输出封包错误率没有设置阈值。", "output_packet_error_over_threshold_recovery": "端口{{portIndex}}输出封包错误率恢复，数值为{{currentValue}}。", "output_packet_error_over_threshold_with_port": "端口 {{portIndex}} 的输出数据包错误率 ({{currentValue}}) 已超过阈值 ({{threshold}})。", "overheat_protection_now_active_for_power_module": "电源模块 {{ x }} 的过热保护现已激活。", "password_automatically_changed_failed": "无法自动更改设备密码。", "password_automatically_changed_success": "设备密码自动更改成功。", "password_automation_scheduled": "密码自动化已达到排程时间并将开始执行。", "phr_port_timediff": "PHR AB 端口时间差。", "phr_port_wrong_lan": "PHR AB 端口 LAN 错误。", "poe_off_info": "设备未通过 PoE 供电", "poe_on_info": "设备由 PoE 供电", "port_linkdown_event": "端口 {{portindex}} 断线", "port_linkdown_recovery": "端口 {{portindex}} 恢复连接", "port_linkup_event": "端口 {{portindex}} 连接", "port_linkup_recovery": "端口 {{portindex}} 恢复连接", "port_loop_detect": "已检测到端口 {{portnum}} 循环", "port_loop_detect_resolved": "端口 {{portnum}} 上的端口循环问题已解决。", "port_loop_detected": "端口循环", "port_pd_short_circuited": "端口 {{portnum}} 非 PD 或 PD 短路。", "port_traffic_overload": "端口{{portIndex}}宽利用率到达{{percent}}%", "power_danger_recovery": "电源 {{param}} 以交流电(AC)供电中", "power_has_been_cut_due_to_overheating": "由于过热，电源已被切断。", "power_module_fan_malfunction": "电源模块风扇故障。", "power_type_danger": "电源 {{param}} 状态异常, 请尽速排除(直流电(DC)供电中)", "pse_fet_bad": "PoE 端口 {{portIndex}} 外部 FET 失效", "pse_over_temp": "PSE 芯片 温度超标", "pse_veeuvlo": "PSE 芯片 VEE 欠压锁定", "ptp_grandmaster_changed": "PTP Grandmaster变更", "ptp_sync_status_changed": "PTP 同步状态已更改", "rateLimit_off": "端口 {{portindex}} 流量限制关闭", "rateLimit_on": "端口 {{portindex}} 上的速率限制已激活。", "recorved_device_lockdown_violation": "设备锁定违规恢复正常", "recorved_l3_firewall_policy_violation": "违反防火牆规则恢复正常（NAT系列）\n", "redundancy_topology_change": "冗余拓扑已更改。", "syslog_server_start": "MXview One syslog 服务器已启动。", "syslog_server_stop": "MXview One syslog 服务器已停止。", "system_temperature_exceeds_threshold": "系统温度超过阈值。", "temporary_account_activate_success": "{{ip}}的临时账户已成功激活。", "temporary_account_deactivate_success": "{{ip}} 的临时账户已成功停用。", "thermal_sensor_component_overheat_detected": "检测到热传感器组件过热。", "trunk_port_link_down": "聚合端口 {{portindex}} 断线 (物理端口 {{param}})", "trunk_port_link_down_recovery": "聚合端口 {{portindex}} 恢复连接 (物理端口 {{param}})", "trunk_port_link_up": "聚合端口 {{portindex}} 连接 (物理端口 {{param}})", "trust_access_under_attack": "违反设备信任访问规则", "trust_access_under_attack_recovery": "违反信任存取规则恢复正常", "turbo_ring_master_match": "Turbo Ring master 无误", "turbo_ring_master_mismatch": "Turbo Ring master 发生错误", "turbo_ring_master_unknow": "Turbo Ring master 事件, 状态未知", "turbochain_topology_change": "Turbo Chain 拓扑改变", "turboring_coupling_port_change": "Turbo Ring Coupling 端口改变", "turboring_master_change": "Turbo Ring Master 改变", "unknown_device_detected": "检测到未知设备", "user_login_fail": "用户登入失败", "user_login_success": "用户登入成功: {{username}} ", "usercode_revoke": "使用者重新生成 usercode。", "vpn_link_recovery": "VPN tunnel {{param}} 恢复连接", "vpn_linkdown": "VPN tunnel {{param}} 断开", "vpn_linkup": "VPN tunnel {{param}} 连接", "vrrp_master_changed": "VRRP Master变更", "warn_start": "热启动"}, "event_detail_title": "事件细节信息 ID: {{eventId}}", "filter": "筛选条件", "filter_end_time": "结束日期", "filter_event": "键入以筛选事件", "filter_event_type": "快速筛选事件", "filter_from_time": "开始日期", "filter_hour": "小时", "filter_min": "分钟", "filter_sec": "秒", "filter_type": {"last_fifty_events": "最新五十笔事件", "last_twenty_events": "最新二十笔事件", "unack_events": "未确认事件", "unack_last_fifty_events": "最新五十笔未确认事件", "unack_last_twenty_events": "最新二十笔未确认事件"}, "group": "群组", "recent_event": "近来事件", "severity": {"any": "任何", "critical": "紧要", "information": "信息", "network_device": "网络与设备", "system_information": "系统信息", "warning": "警告"}, "show_event_detail": "显示详细信息", "source": {"any": "任何", "mxview": "MXview One", "security_sensing": "安全检测", "trap": "Trap"}, "table_title": {"ack": "确认", "ack_time": "确认时间", "always_show": "启动时总是显示最近的事件", "description": "描述", "detail_information": "细节信息", "event_id": "ID", "event_properties": "事件属性", "event_source": "来源", "event_time": "发出时间", "hide_recent_event": "隐藏最近的事件", "severity": "严重性", "show_recent_event": "显示最近的事件", "site_name": "站点名称", "source_ip": "来源 IP"}, "tabs": {"network_device_title": "网络与设备", "security_event": "网络安全事件", "security_title": "网络安全", "system_title": "系统"}}, "execute_cli_object": {"add_cli_object": "添加 CLI 脚本", "alias": "别名", "cli_error": {"connection_failure": "连线失败", "handshake_failure": "交握失败", "login_failure": "登入失败", "port_limit": "端口安全位址记录的最大数量：{{param}}", "reach_maximum_ssid": "SSID 最大数量：{{param}}", "smmp_configuration_mismatch": "SNMP 配置不匹配", "ssh_not_supported": "无法连接到 SSH 客户端", "unable_to_set_port": "无法设置端口 {{port}}。", "unknown_error": "未知错误"}, "cli_Script": "CLI脚本", "cli_session_timeout": "CLI 会话超时", "confirm_selected_devices": "选定的设备", "description": "描述", "execute_cli_fail": "无法运行 CLI 脚本", "execute_cli_object": "运行 CLI 脚本", "execute_cli_result_hint": "如果离开此屏幕，您可以从“已保存的 CLI 脚本”>“执行结果”下载执行结果。", "execute_cli_results": "CLI 执行结果", "execute_cli_script": "运行 CLI 脚本", "failed": "失败", "finished": "已完成", "in_progress": "进行中 ...", "ip": "IP", "model": "型号", "name": "CLI 脚本名称", "no_cli_object_hint": "未找到 CLI 脚本。 首先从保存的 CLI 脚本中添加 CLI 脚本", "not_sent": "未发送", "result": "结果", "save_as_cli_object": "另存为 CLI 脚本", "save_cli_object": "保存 CLI 脚本", "select_cli_object": "选择 CLI 脚本", "ssh_connection_timeout": "SSH 连接超时", "status": "状态"}, "firmware-management": {"action": "动作", "add-task": "添加任务", "add-to-schedule": "排程更新", "api-message": {"add-schedule-fail": "无法排程任务", "add-schedule-success": "任务已排程", "delete-schedule-fail": "无法删除检查间隔排程", "delete-schedule-success": "检查间隔排程删除成功", "fm-downloaded": "固件档下载成功", "fm-downloading": "下载固件文件", "fm-ready": "固件文件准备就绪", "get-data-failed": "无法取得数据", "get-download-fm-failed": "无法下载固件文件", "get-release-note-failed": "无法取得发行说明", "get-srs-status-failed": "无法查询 Moxa 固件服务器状态", "ignored-model-fail": "无法将型号添加到“忽略的型号”列表中"}, "check-Firmware-status": "检查固件状态", "check-interval": "检查间隔", "check-now": "立即检查", "connected": "已连线", "description": "描述", "disconnected": "未连线", "download-csv-report": "下载 CSV 报告", "download-pdf-report": "下载 PDF 报告", "execution-time": "时间", "firmware-upgrade-sequential": "固件更新（严格依序）", "firmware-upgrade-smart-concurrent": "固件更新（智能依序）", "ignore-report": "忽略报告", "ignore-report-desc1": "您确定要跳过下载报告吗？", "ignore-report-desc2": "如果您离开此页面，该报告将不再可供下载。", "ignored-models": "忽略的型号", "last-update": "最后检查", "models": "型号", "moxa-firmware-server-status": "Moxa 固件服务器状态", "no-information": "无资料", "none": "無", "offline-desc": "无资料。 之前没有连接到固件更新服务器。 确保设备已连接到互联网，然后重试。", "proceeding-firmware-upgrade": "固件更新状态", "proceeding-upgrade-result": "固件更新结果", "release-note": "发行说明", "repeat-execution": "重复", "retry-Failed-devices": "重试失败的设备", "select-devices": "选择设备", "select-firmware": "选择固件", "set-upgrade-sequence": "升级顺序", "sign-here": "签名", "start-date": "日期", "status": {"failed": "失败的", "finished": "完成的", "in-progress": "进行中", "waiting": "等待中"}, "table-header": {"alias": "别名", "current-version": "当前版本", "device-status": "设备状态", "ip": "IP", "latest-version-on-firmware-server": "固件服务器上的最新版本", "model-series": "型号系列", "order": "順序", "selected-firmware-ready": "所选固件下载状态", "selected-version": "选定版本", "status": "状态"}, "task-name": "任务名称", "title": "固件管理", "turn-off-check-interval": "禁用检查间隔", "turn-on-check-interval": "启用检查间隔", "unable-to-download-firmware": "下载固件失败", "update-mode": "更新模式", "upgrade-desc1": "无法決定更新顺序。 请先将运行 MXview One 的计算机添加到拓扑中。", "upgrade-desc2": "\n当前设置仅支援设备固件同时更新。要使用严格依序或智能依序更新方法，您必须首先将运行 MXview One 的计算机添加到拓扑中。", "upgrade-firmware-report": "固件更新报告", "upgrade-now": "立即更新", "upgrade-state-desc": "固件更新可能需要一些时间。 请等待更新过程完成。", "version": "版本"}, "general": {"common": {"action": "行动", "allow": "允许", "any": "任何", "deny": "拒绝", "description": "描述", "deviceInUse": "设备正在使用", "deviceName": "设备名称", "disabled": "停用", "enabled": "启用", "endDate": "结束日期", "filters": "筛选", "firmwareVersion": "固件版本", "group": "群组", "index": "序号", "ipAddress": "IP 地址", "location": "位置", "mac": "MAC 地址", "name": "名称", "online": "在线", "options": "选项", "productModel": "产品型号", "profileInUse": "使用中的配置文件", "refCount": "引用", "serialNumber": "序列号", "startDate": "开始日期", "status": "状态", "title": "面板名称"}, "dialog": {"deleteMsg": "您确定要删除选定的 {{ item }} 吗？", "deleteTitle": "删除 {{ item }}", "isSelected": "已选择 {{ number }} 个项目", "title_system_message": "系统讯息", "unsaved_hint_content": "您确定要离开此页面吗？\n您所做的任何更改都不会被保存。", "unsaved_hint_title": "离开且不保存", "warning": "警告"}, "fileDrop": {"browse": "浏览", "dropText": "将文件拖放到此处，或者"}, "item_selected": "已选择 {{ number }} 个项目", "log": {"localStorage": "本地存储", "logDestination": "日志存取于", "snmpTrapServer": "SNMP Trap 服务器", "syslogServer": "Syslog 服务器", "title": "事件日志"}, "menu": {"jump_page_placeholder": "Alt+J 快速切换页面"}, "page_state": {"application_error": "应用程序错误", "application_error_desc": "Code有问题，救我...", "back_link": "回到首页", "page_not_found": "找不到页面 :(", "page_not_found_desc": "目前在服务器上无法处理这个网址。"}, "severity": {"alert": "警告", "critical": "紧要", "debug": "调试", "emergency": "紧急", "error": "错误", "high": "高", "information": "信息", "informational": "信息", "low": "低", "medium": "中", "notice": "通知", "title": "严重性", "warning": "警告"}, "shortWeekday": {"fri": "星期五", "mon": "星期一", "sat": "星期六", "sun": "星期日", "thu": "星期四", "tue": "星期二", "wed": "星期三"}, "table": {"add": "添加", "delete": "删除", "download": "下载", "downloadAllLogs": "下载所有日志", "edit": "编辑", "filter": "筛选", "info": "信息", "more": "更多的", "permissionDenied": "没有权限", "reboot": "重启", "refresh": "刷新", "reorderFinish": "完成重新排序", "reorderPriority": "重新排序优先级", "search": "搜索", "transfer": "转移", "upgrade": "升级"}, "top_nav": {"api_doc": {"title": "API 文件"}, "hide_recent_event": "隐藏导航菜单", "notifications": {"message_content": "事件叙述在这里", "message_readall": "浏览所有通知讯息", "message_title": "事件标题在这里", "notification_header": "通知讯息"}, "show_recent_event": "显示导航菜单", "user_profile": {"advanced_mode": "进阶模式", "change_pwd": "变更密码", "greeting": "嗨", "logout": "注销", "manage_account": "管理帐号", "reset_factory_default": "重置出厂设置", "restart_machine": "重新启动机器", "search": "键入要搜索的关键字"}}, "topNav": {"caseInsensitive": "不区分大小写", "changePwd": "变更密码", "changeSuccess": "您的密码已成功更新。请重新登录。", "confirmNewPwd": "确认新密码", "currentPwd": "当前密码", "invalidKey": "以下名称是保留的：admin、operator、viewer、root、administrator、auditor", "logout": "登出", "logoutMsg": "您确定要登出吗？", "newPwd": "新密码", "subject": "主题", "troubleshoot": "故障排除", "troubleshootMsg": "您可以将调试日志导出到本地主机以进行故障排除。", "updateAuthority": "更新权限", "updateSuccess": "您的账户权限已改变，请重新登录。", "username": "用户名称"}, "unit": {"days": "天", "entries": "条目", "minute": "分钟", "minutes": "分钟", "months": "月", "percent": "％", "pkts": "包/秒", "sec": "秒", "seconds": "秒", "thousand": "千"}, "weekday": {"friday": "星期五", "monday": "星期一", "saturday": "星期六", "sunday": "星期日", "thursday": "星期四", "tuesday": "星期二", "wednesday": "星期三"}}, "GLOBAL_MESSAGE": {"update_fail": "更新失败", "update_success": "更新成功"}, "GROUP_PROPERTIES": {"description": "描述", "devices": "设备 (正常 / 警告 / 紧要)", "information": "信息", "name": "名称", "title": "群组属性"}, "IMAGE": {"deviceSizeError": "最大图片大小为100KB", "error": "Mxview 只支持 jpg, gif, png 档案格式", "sizeError": "图片大小上限为1MB"}, "inventory_management": {"active": "有效", "alias": "别名", "assets_list": "资产列表", "available": "可用的", "channel_extended_end_date": "渠道延长保修截止日期", "channel_extended_warranty_end_date_hint": "如果您与 Moxa 渠道供应商有延长保修协议，请在此处手动输入延长到期日期。", "check_warranty_manually": "手动检查保修", "check_warranty_status": "检查保修状态", "days": "天前", "email_example": "<EMAIL>", "email_to": "发电子邮件给", "expire_soon": "即将过期", "expired": "已过期", "firmware_version": "固件版本", "invalid_email_desc": "无效的电子邮件地址", "ip": "IP", "last_update": "上次更新", "mac_address": "MAC 地址", "model": "型号", "multiple_email_hint": "您可以添加多个收件人电子邮件地址，以逗号分隔。", "no_data": "N/A", "notify_before": "发送提醒", "retrieve_data": "检索数据", "select": "选择", "serial_number": "序列号", "type": "搜寻依据", "unable_query_warranty_server_status": "无法连接 Moxa 保修服务器。", "unable_retrieve_warranty_information": "无法检索保修信息。", "unavailable": "不可用的", "warranty_end_date": "保修截止日期", "warranty_end_date_notification": "保修到期通知", "warranty_management": "保修管理", "warranty_notification": "保修通知", "warranty_period": "保修期限", "warranty_server_status": "Moxa 保修服务器状态", "warranty_start_date": "保修开始日期", "warranty_status": "保修状态"}, "INVENTORY_REPORT": {"alias": "别名", "filter": "键入以筛选库存报告", "fw_version": "固件版本", "ip_address": "IP 地址", "mac": "MAC 地址", "model": "型号", "property": "属性", "report_generate_day": "报告生成日: ", "site_name": "站点名称", "system_desc": "系统描述", "title": "库存报告", "value": "数值"}, "IP_CONFIGURATION": {"auto_ip": "自动 IP", "change_ip_fail": "无法设置IP配置", "change_ip_success": "成功设置IP配置", "gateway": "网关", "hint": "此功能不适用于第3层设备。", "ip_address": "IP地址", "netmask": "子网掩码", "title": "IP 配置"}, "ip_conflict_detected_notification": "检测到 IP 冲突", "ip_conflict_recovery_notification": "IP 冲突已解决", "ips_configuration": {"dialog-title": "IPS配置", "execute_fail": "Failed to execute", "input-ips": "IPS", "option-detection-mode": "检测模式", "option-prevention-mode": "预防模式", "selet-ips-operation-mode": "IPS操作模式", "th-execution-status": "状态"}, "IPSEC": {"connection_name": "连接名称", "ipsec_status_phase1": "IPSec第1阶段状态", "ipsec_status_phase2": "IPSec第2阶段状态", "local_gateway": "本地网关", "local_subnet": "本地子网", "remote_gateway": "遠端网关", "remote_subnet": "遠端子网"}, "IW": {"Message": {"CONNECT_TIMEOUT": "联机超时", "ERROR_OCCURRED": "服务器联机错误,请稍等", "FAILED": "失败", "LOAD_DATA_FAILED": "读取数据失败", "RSSI_SNR_ONLY": "(场强与信噪比)", "SET_SUCCESS": "设定成功", "SUCCESSED": "成功", "UPDATE_FAILED": "更新数据错误"}, "Title": {"ap": "AP", "AUTO_REFREASH": "自动刷新: ", "AUTO_REFRESH": "自动刷新", "BSSID": "BSSID", "channel": "Channel", "client": "客户端", "client_count": "客户端数量", "client_router": "Client-Router", "CLOSE": "关闭", "COLOR": "颜色", "COLUMN": "字段", "CONDITIONS": "条件", "CONN_TIME": "连线时间 (秒)", "connected": "连接时间", "DEVICE_NAME": "设备名称", "disable": "Disable", "ENABLE": "启用", "FILTER_TABLE_VIEW": "筛选表格", "hint": "此页面将在以后的产品版本中删除。请改用MXview One中的Wireless Add-on。", "IP_ADDR": "IP 地址", "link_speed": "连线速度", "MAC": "MAC 地址", "master": "Master", "MODULATION": "调变", "noise_floor": "Noise Floor", "noise_floor_unit": "Noise Floor (dBm)", "OK": "确认", "ONLINE": "在线", "operation_mode": "运作模式", "RSSI": "场强 (dBm)", "security_mode": "安全模式", "signal_level": "Signal Level", "slave": "Slave", "SNR": "信噪比 (dB)", "SNR_A": "信噪比-A (dB)", "SNR_B": "信噪比-B (dB)", "ssid": "SSID", "TOTAL_AP": "接入点数量: ", "TOTAL_CLIENT": "客户端数量: ", "tx_power": "TX 功率", "tx_power_unit": "TX 功率 (dBm)", "tx_rate": "TX 速率", "tx_rate_unit": "TX 速率 (Mb/s)", "uptime": "运行时间", "VALUE": "数值", "WIRELESS_TABLE_VIEW": "无线状态列表"}}, "JOB_SCHEDULER": {"add_failed": "添加作业失败", "add_success": "添加作业成功", "add_title": "添加作业", "alias": "别名", "auto_topology": "自动拓扑", "cli_object_name": "CLI 脚本名称", "config_file": "配置档案", "config_file_error": "Mxview只支持ini档案格式", "config_file_size_error": "最大文件大小为1MB", "current_filename": "当前文件名", "current_version": "当前版本", "daily": "每日", "database_backup": "数据库维护", "delete_failed": "删除作业失败", "delete_success": "删除作业成功", "description": "描述", "edit_title": "编辑作业", "excute_cli_object": "运行保存的脚本", "execution_time": "执行时间", "export_configuration": "导出配置", "filter": "键入以筛选作业调度", "fm_sequential": "严格依序", "fm_smart": "智能依序", "friday": "星期五", "import_configuration": "导入配置", "ip": "IP", "job_action": "动作", "job_log": "作业日志", "job_name": "作业名称", "model_series": "型号系列", "monday": "星期一", "monthly": "每月", "on": "周期", "once": "一次", "order": "顺序", "registered_devices": "注册的设备", "repeat_execution": "重复执行", "saturday": "星期六", "schedule_time": "作业时间", "selected_version": "选定版本", "show_log_fail": "还没有日志可以显示", "show_log_not_found": "无法显示日志", "start_date": "开始日期", "sunday": "星期日", "thursday": "星期四", "title": "维护计划", "tuesday": "星期二", "update_failed": "更新作业失败", "update_success": "更新作业成功", "wednesday": "星期三", "weekly": "每周"}, "LANG": {"de-DE": "De<PERSON>ch", "en-US": "English", "es-ES": "Español", "fr-FR": "Français", "ja-JP": "日本語", "ru-RU": "Русский язык", "zh-CN": "中文(简体)", "zh-TW": "中文(繁體)"}, "LICENSE": {"activation_code": "授权码", "activation_code_error": "无效的授权码", "activation_title": "启用", "active": "启用", "active_desc_1": "造访", "active_desc_2": "http://license.moxa.com/", "active_desc_3": "并填写 Product Code 和 User Code 以获得您的 Active Code", "add_fail": "增加授权失败", "add_license": "增加授权", "add_new_license_desc": "您可以在这里增加新的授权", "add_new_license": {"activate": "启用", "activate_intro_link": "Moxa授权网站", "activate_intro_pre": "从", "activate_intro_suf": "下载授权，并在此处贴上授权码。", "copy_user_code": "复制User Code", "copy_user_code_intro_link": "Moxa授权网站", "copy_user_code_intro_pre": "复制User Code至", "license_site_step_1_link": "Moxa授权网站", "license_site_step_1_pre": "1. 登入", "license_site_step_2": "2. 在此网站选择「Activate Your License」和「MXview One」。", "license_site_step_3": "3. 注册码", "license_site_step_3_Free_step": "前往下一步。", "license_site_step_3_Free_title": "免费版使用者：", "license_site_step_3_Full_step": "键入注册码及User Code于Moxa授权网站，User Code将在后续的步骤中取得。", "license_site_step_3_Full_title": "完整版使用者", "login_license_site": "登入Moxa授权网站", "select_network_adapter": "选择网卡", "title": "增加新授权"}, "add_success": "增加授权成功", "copied_to_clipboard": "已复制到剪贴簿", "copy_deactivation_code": "复制解除授权码", "copy_older_license_code": "复制2.x授权码", "current_nodes": "管理的设备数量:", "deactivate": "停用", "deactivate_fail": "停用授权失败", "deactivate_success": "停用授权成功", "deactivated_licenses": "已停用的授权", "deactivating": "停用中...", "deactivation_code": "解除授权码", "deactivation_desc": "停用后授权将会失效，确定要停用授权吗？", "deactivation_title": "停用授权", "disabled": "未启用", "duration": "有效天数", "enabled": "已启用", "expired_license": "过期授权", "free_trial": "免费试用", "free_trial_desc": "开始试用免费的 MXview One", "import_license_file": "输入授权档", "license": "授权:", "license_authorized": "已授权", "license_free": "免费授权", "license_none": "无", "license_site": "Moxa授权网站", "license_start": "授权开始时间", "license_title": "授权", "license_trial": "试用", "license_type": {"node_base_intro": "提供MXview One在网络中可以监控的设备数量。", "node_base_title": "基于节点的授权", "power_addon": "电力附加授权", "power_intro": "为用户提供更多与电力相关的功能。", "security_addon": "安全附加授权", "security_intro": "提供用户访问更多的安全相关功能。", "title": "授权类型", "trial_intro": "您可以在90天内体验到MXview One的强大功能。", "trial_title": "试用授权", "wireless_addon": "无线附加授权", "wireless_intro": "提供用户访问更多的无线相关功能。"}, "licensed_node": "授权点数", "licensed_nodes": "授权总数量:", "licenses": "授权", "managed_by_central": "授权由 MXview One Central 管理", "managed_by_central_licenses_invalidated": "无有效授权，请在 Control Panel 中确认状态", "mxview": "MXview One", "network_adapter": {"button": "选择网卡", "change_network_adapter": "更改网卡", "change_network_adapter_alert_1": "您确定要更改网卡吗？", "change_network_adapter_alert_2": "如果更改网卡，将终止所有当前授权。 在使用新网卡注册新授权之前，您将无法使用MXview One。", "intro": "MXview One将授权绑定到一个网卡，请选择要绑定的网卡。 重新选择网卡将终止所有授权，您必须重新注册所有授权。", "select_adapters": "选择网卡", "select_adapters_desc": "请选择一张网卡。我们将使用这张网卡来产生 User Code", "title": "网卡"}, "node": "管理的设备数量 / 授权总数量:", "nodes": "授权数量", "older_license": "2.x 授权", "older_license_nodes": "2.x 授权数量", "over_nodes_desc": "因为监控的节点已超过授权的数量，您已被注销", "over_nodes_title": "警告", "power_addon_trial": "开始体验MXview One中的电力附加功能。", "reactivate_license": {"activate": "启用", "activate_intro_link": "Moxa授权网站", "activate_intro_pre": "从", "activate_intro_suf": "下载授权，并在此处贴上授权码。", "copy_deactivate_code": "复制解除授权码", "copy_deactivate_code_intro_link": "Moxa授权网站", "copy_deactivate_code_intro_pre": "复制解除授权码，在", "copy_deactivate_code_intro_suf": "中贴上。", "copy_user_code": "复制User Code", "copy_user_code_intro_link": "Moxa授权网站", "copy_user_code_intro_pre": "复制User Code至", "intro": "使用解除授权码及User Code来重新启用您的授权", "license_site_step_1_link": "Moxa授权网站", "license_site_step_1_pre": "1. 登入", "license_site_step_2": "2. 在此网站点击「MXview One Deactivation」和「Transfer to another device」。", "license_site_step_3": "3. 在Software Product中选择MXview One。", "login_license_site": "登入Moxa授权网站", "title": "重新启用授权", "title_abbr": "重新启用"}, "reason": "状态", "relaunch": {"activating": "启用中...", "active_note": "操作将在10秒内完成。"}, "remain": "剩余", "security_addon_trial": "开始体验 MXview One 中的安全附加功能。", "select_network_interface": "选择网卡", "site_license_invalid": "在某些站点中有无效的授权", "site_license_invalid_title": "授权无效", "start_free_trial": "开始试用", "start_free_trial_fail": "开始免费试用失败", "start_free_trial_success": "开始免费试用成功", "state": "状态:", "state_all_licenses_invalidated": "无有效授权", "state_cannot_add_free_license": "无法增加免费授权", "state_cannot_add_multiple_free_licenses": "无法增加多个免费授权", "state_format_incorrect": "授权的格式不正确", "state_general_error": "一般错误", "state_license_deactivated": "此授权已停用", "state_license_expired": "授权过期", "state_license_is_registered": "此授权已注册", "state_license_not_found": "找不到授权", "state_license_over_2000": "总授权点数超过2000，但MXview One最多支持2000点", "state_license_upgrade_error": "无法增加Upgrade License， MXview One需要至少一个完整授权", "state_license_upgrade_no_full_license ": "无法删除此授权，请先移除所有Upgrade License", "state_no_full_license": "无 MXview One 授权", "state_no_usercode": "没有 User Code", "state_over_nodes": "监控的节点已超过授权的数量", "state_trial_expired": "试用过期", "state_trial_is_began": "试用已开始", "state_trial_not_activated": "试用还没有激活", "state_usercode_deactivated": "User Code 停用", "state_usercode_exists": "User Code 已存在", "state_usercode_not_match": "授权中的 User Code 与系统中的不相符", "state_usercode_not_match_adapter": "未找到绑定到此授权的User Code", "title": "授权管理", "trial_button": "试用", "trial_day": "天", "trial_expired": "试用期满", "trial_over_nodes": "如果您不删除超过的节点，MXview One 将在 10 分钟后自动注销", "trial_remaining": "试用剩余", "user_code": "User Code:", "valid": "有效", "wireless_addon_trial": "开始试用 MXview One 上的无线附加功能"}, "link_list": {"rx": "RX (%)", "tx": "TX (%)"}, "LINK_PROPERTIES": "连线属性", "LOGIN": {"account_reach_limit": "帐号超过授权数量(10)", "all_sites_offline": "所有站点都离线", "default_password_warning": "请改变默认密码以得到较高的安全性", "error": "无效的用户名或密码", "ie_not_supported": "MXview One不支持IE, 请使用Chrome以获得最佳体验", "last_login_fail": "最新登入失败记录如下(s)", "last_login_succeed": "您最后成功登入的时间是", "login_fail_time": "{{loginTime}} 来自 {{loginIp}}", "login_succeed_time": "{{loginTime}} 来自 {{loginIp}}", "logout": "注销", "password": "密码", "password_policy_mismatch": "密码不符合密码政策", "sign_in": "登入", "username": "用户名称", "welcome": "欢迎"}, "max_char": "最多 {{num}} 个字符", "min_char": "最少 {{num}} 个字符", "model-port-mapping": {"port": "端口", "web-ui": "设备上的 Web UI"}, "MXVIEW_WIZARD": {"complete_page_title": "完成", "navigate_to_wizard_page": "您想使用MXview One安装向导吗？", "step_add_scan_range": "新增扫瞄范围", "step_auto_topology": "产生网路拓扑 (具有 LLDP 的设备)", "step_create_group": "创建群组", "step_select_site": "请选择要设置的站点", "step_set_snmp": "设置SNMP设定", "step_set_trap_server": "设定 Trap 服务器", "title": "设置向导", "welcom_page_title": "欢迎使用初始化向导"}, "NETWORK_MENU": {"add_link": "增加链结", "add_wifi_ssid": "添加 Wi-Fi SSID", "alignment": {"bottom": "下对齐", "left": "左对齐", "right": "右对齐", "title": "对齐", "top": "上对齐"}, "copy_device_list": "复制设备列表", "create_a_snapshot": "创建快照", "cybersecurity_control": "网络安全控制", "delete": "删除", "device_configuration": "设备配置", "device_control": "设备控制", "device_dashboard": "<PERSON>ce Dashboard", "device_login_account": "设备帐户", "device_panel": "设备面板", "device_wireless_settings": "个别设备参数", "disable_unsecured_http_and_telnet_console": "禁用不安全的 HTTP 和 Telnet 终端", "disable_unused_ethernet_and_fiber_ports": "禁用未使用的以太网和光纤端口", "document": {"menu": {"open": "打开", "set": "设置"}, "title": "文件"}, "dynamic_mac_sticky": "动态 Sticky MAC", "edit": {"menu": {"add_device": "添加设备", "delete_background": "删除背景", "export_device_list": "导出设备列表", "export_topology": "导出拓扑", "import_device_list": "汇入设备", "set_background": "设置背景"}, "title": "编辑"}, "execute_cli": {"menu": {"execute_cli_object": "运行保存的脚本", "execute_cli_script": "CLI 脚本"}, "title": "运行脚本"}, "grid": {"menu": {"import_scd": "导入SCD"}, "title": "电源"}, "group": {"menu": {"change_group": "更改群组", "create_group": "创建群组", "group_maintenance": "群组维护"}, "title": "群组"}, "grouping": "組成群組", "ips_configuration": "IPS配置", "ipsec_status": "IPSec 状态", "link_traffic": {"menu": {"packet_error_rate": "封包错误率", "port_traffic": "带宽使用率"}, "title": "链接流量"}, "locator": "设备定位", "mac_sticky_on_off": "Sticky MAC 开/关", "maintenance": {"menu": {"advance_settings": "进阶设定", "assign_model": "指定型号", "basic_information": "基本信息", "change_device_icon": "更改图示", "device_identification_settings": "设备识别设置", "eip_enable": "EtherNet/IP 启用", "eip_tcp_port": "以太网/IP TCP 端口", "eip_udp_port": "EtherNet/IP UDP 端口", "export_config": "导出配置", "generate_qr_code": "产生QR Code", "import_config": "导入配置", "ip_configuration": "IP配置", "modbus_enable": "Modbus 启用", "modbus_port": "Modbus 端口", "modbus_tcp_configuration": "Modbus TCP 设置", "polling_ip_setting": "轮询 IP", "polling_settings": "MXview One 轮询间隔", "port_settings": "以太网/光纤端口设置", "s7_port": "Siemens S7comm 端口", "s7_status": "Siemens S7comm 启用", "serial_port_monitoring": "串行端口监控", "snmp_settings": "SNMP 通讯协议", "trap_server": "Trap 服务器", "upgrade_firmware": "升级固件"}, "title": "维护"}, "modify_device_alias": "设备别名", "policy_profile_deployment": "规则配置文件部署", "reboot": "重启", "refresh": "刷新", "restart_mac_sticky_learning": "重新学习动态 Sticky MAC", "restore_to_created_snapshot": "还原快照", "scale": "设定比例", "security_package_deployment": "网安防护包部署", "set_port_label": "设定联机标签", "severity_threshold": "严重性阈值", "sfp": "SFP", "sfp_Info": "SFP 信息", "sfp_list": "SFP 列表", "sfp_sync": "从设备同步阈值", "tools": {"menu": {"device_panel": "设备面板", "mib_browser": "MIB库浏览器", "ping": "<PERSON>", "telnet": "Telnet", "web_console": "网页控制台"}, "title": "工具"}, "topology": {"menu": {"auto_layout": "自动布置", "auto_topology": "自动拓扑", "embed": "嵌入微件", "scan_range": "设备发现"}, "title": "拓扑"}, "ungrouping": "取消群組", "upgrade_patch": "执行系统更新", "visualization": {"menu": {"igmp": "IGMP", "security_view": "安全检视", "traffic_view": "流量检视", "vlan": "VLAN", "vlan_view": "Vlan 检视"}, "title": "可视化"}, "wifi_channel_change": "更改 Wi-Fi 通道", "wireless_settings": "通用参数", "wireless": {"menu": {"wireless_planner_view": "无线网路覆盖", "wireless_playback_view": "无线漫游回放", "wireless_table_view": "无线设备列表"}, "title": "无线"}}, "NETWORK": {"current_status": {"no_event": "无事件", "title": "实时状态", "v3_trap_event_clear_fail": "V3 trap 事件清除失败", "v3_trap_event_suggestion": "请确认 snmp v3 设定"}, "not_selected": "选择一个模块以显示设备详情"}, "NORTHBOUND_INTERFACE": {"custom_opc": {"add_custom_opc": "添加自定义OPC标签", "all": "所有OPC", "apply_fail": "无法设置自定义OPC标签", "apply_success": "自定义OPC标签已成功添加", "delete_fail": "无法删除自定义OPC标签", "delete_success": "自定义OPC标签已删除", "device_properties": "设备属性", "enable": "启用自定义OPC标签", "filter_custom_opc": "键入以筛选自定义OPC标签", "get_fail": "无法获取自定义OPC标签", "property_name": "属性名称", "register_devices": "注册设备", "title": "自定义OPC标签", "update_custom_opc": "更新自定义OPC标签", "update_fail": "无法更新自定义OPC标签", "update_success": "自定义OPC标签已成功更新"}}, "NOTIFICATION_SETTINGS": {"action": "通知输出", "action_cant_deleted": "尚有通知包含此动作。请先清除相关通知，然后再删除这个动作", "action_information": "动作资讯", "action_name": "动作名称", "action_tab_hint": "请转到通知输出分页并首先添加动作", "action_type": "类型", "add_action": "添加通知输出", "add_action_fail": "增加动作失败", "add_action_success": "增加动作成功", "add_notification": "添加通知", "add_notification_fail": "增加通知配置失败", "add_notification_success": "增加通知配置成功", "check_security_tab": "检查网络安全分頁", "content": "内容", "delete_action_fail": "无法删除动作", "delete_action_success": "动作已经删除成功", "delete_notification_fail": "无法删除通知配置", "delete_notification_success": "删除通知配置成功", "edit_action": "编辑通知输出", "edit_notification": "编辑通知", "email": "电子邮件", "email_content_hint": "此处的内容将加到默认通知邮件的内容中", "event_type": "种类", "file_size_error": "最大文件大小为1MB", "file_type_error": "声音档只支持WAV格式", "filter_action": "键入以筛选通知操作配置", "filter_notification": "键入以筛选通知配置", "messagebox": "讯息框", "mobile": "MXview ToGo", "mobile_number": "手机号码", "notification": "通知", "notification_name": "通知名称", "notification_name_exist": "这个名字已经被另一个通知所使用", "receiver_email": "收件人电子邮件", "register_devices": "注册设备", "register_subscribers": "订户", "slack": "<PERSON><PERSON>ck", "sms": "SMS", "snmptrap": "SNMP Trap", "sound": "声音档", "teams": "Microsoft Teams", "testConnection": "测试连接", "title": "通知配置", "update_action_fail": "无法更新动作", "update_action_success": "动作已经更新成功", "update_notification_fail": "无法更新通知配置", "update_notification_success": "通知配置已经更新成功", "webhook": "Webhook", "webhook_fail": "无法执行Webhook", "webhook_success": "Webhook已发送"}, "OPC_UA_SERVER": {"add_opc_tags": "添加 OPC 标签", "anonymous": "匿名的", "auth_setting": "验证设置", "certificate": "证书", "certificate_link": "从以下位置下载和管理证书", "change_authentication_password": "更改认证密码", "change_password": "更改密码", "control_panel": "MXview One 控制面板", "create_tags_fail": "无法添加标签", "create_tags_success": "标签创建成功", "delete_tag_content": "您确定要删除该 OPC 标签吗？", "delete_tags": "删除 OPC 标签", "delete_tags_content": "您确定要删除这些 OPC 标签吗？", "delete_tags_fail": "删除标签失败", "delete_tags_success": "标签删除成功", "device_property": "设备属性", "disabled": "未启用", "edit_opc_tags": "编辑 OPC 标签", "edit_tags_fail": "更新标签失败", "edit_tags_success": "标签更新成功", "enable_opc_server": "启用 OPC UA 服务器", "enabled": "启用", "exceed_server_performance": "已达到注册设备的最大数量（4000）。", "get_tags_list_fail": "无法检索标签列表", "ip_domain_name": "IP/域名", "method": "方法", "opc_tags": "OPC 标签", "property_name": "设备属性", "registered_device": "注册的设备", "security": "安全模式", "security_placeholder": "允许不安全", "server_settings": "服务器设置", "status": "状态", "support_security_policy": "支持的安全政策", "tag_name": "标签名称", "tag_name_duplicate": "此标签名称已存在", "tags_exceed_limit": "已达到最大标签数（2000）。", "title": "OPC UA 服务器", "update_fail": "无法更新设置", "update_server_setting_fail": "无法更新服务器设置", "update_server_setting_fail_no_up": "更新设置失败。指定的 IP 不存在。", "update_server_setting_success": "服务器设置更新成功", "username": "账户和密码"}, "PAGES_MENU": {"about": "关于", "administration": {"account_management": "账户管理", "device_settings_template": "默认设备模板", "global_device_settings": "全局设备设置", "license_management": "授权管理", "maintenance_scheduler": "维护计划", "preferences": "首选项", "system_settings": "系统设置", "title": "管理", "troubleshooting": "故障排除"}, "alert": {"custom_events": "自订事件", "device_threshold": "设备阈值", "event_settings": "事件设置", "link_threshold": "连线阈值", "notifications": "通知管理", "title": "警告"}, "cli_object_database": {"title": "保存的 CLI 脚本"}, "dashboard": "仪表板", "device_management": {"account_password": "账户和密码", "configuration_control": "配置和控制", "title": "设备管理"}, "devices": {"device_configurations": "设备配置", "list_of_devices": "设备列表"}, "event": {"all_events": "历史事件", "custom_events_management": "自定义事件", "notification_management": "通知管理", "syslog_settings": "Syslog 设置", "syslog_viewer": "Syslog 浏览器", "title": "事件管理"}, "firewall_policy_management": {"dos_descr": "配置 DoS 规则", "ips_descr": "配置 IPS 规则", "layer3to7_descr": "配置第 3-7 层防火墙规则", "policy_profile_deployment": "规则配置文件部署", "policy_profile_management": "规则配置文件管理", "security_package_deployment": "网安防护包部署", "security_package_management": "网安防护包管理", "sessionControl_descr": "配置会话控制规则", "title": "防火墙规则管理"}, "firmware_management": {"title": "固件管理"}, "help": {"about_mxview": "关于MXview One", "api_documentation": "API文檔", "title": "帮助", "user_manual": "用户手册"}, "license": "授权", "links": {"list_of_rj45_links": "RJ45 连线列表", "list_of_sfp_links": "SFP 连线列表", "list_of_wifi_links": "Wi-Fi 连线列表", "title": "连线"}, "migrations": {"configuration_center": "设备配置中心", "database_backup": "数据库备份", "job_scheduler": "维护计划", "title": "迁移"}, "network": {"scan_range": "扫描范围", "title": "网络", "topology": "拓扑", "wizard": "向导"}, "northbound_interface": {"custom_opc_tags": "自定义OPC标签", "opc_ua_server": "OPC UA Server", "restful_api_management": "RESTful API 管理", "title": "整合", "web_widget_embedded": "网页嵌入微件"}, "preferences": "首选项", "report": {"assets_and_warranty": "资产和保修", "availability_report": "可用性报告", "inventory_report": "库存报告", "rogue_device_detection": "非法设备侦测", "title": "报告", "vlan": "VLAN"}, "scan_range_wizard": {"title": "扫描范围精灵"}, "security": {"account_management": "账户管理", "security_analyser": "安全分析", "title": "安全"}}, "pages": {"deviceDeployment": {"alreadySentSms": "本月已发送 {{ smsNumber }}/{{ max }} 条短信", "applied": "应用", "atLeastSelectOne": "选择至少一条短信控制命令", "cellularModuleDisable": "蜂窝模块已禁用，不允许发送短信命令。", "cellularStartConnecting": "蜂窝网络开始连接", "cellularStopConnecting": "蜂窝网络停止连接", "configSync": "所选设备的配置将被同步。", "daily": "每日", "date": "日期", "deleteMsg": "您确实要删除选定的设备吗？", "deleteSchedule": "删除调度\n", "deleteScheduleSuccess": "设备调度删除成功", "deleteSuccess": "删除设备成功", "deleteTitle": "删除设备", "device_ip": "设备 IP", "deviceConfiguration": "设备配置", "deviceDetail": "设备详情", "deviceDisableHint": "设备端已禁用该功能", "deviceSelected": "选定的设备", "endTime": "结束日期", "firmware": "固件", "firmwareUpgrade": "选定设备的固件将被升级。", "firmwareVersion": "固件版本", "general": "一般", "groupName": "群组名称", "groupSelected": "选定的组", "invalidDate": "无效日期", "invalidPeriod": "无效期间", "lastRebootTime": "上次重启时间", "lastUpdate": "最后检查", "lastUpdateTime": "最后更新时间", "location": "位置", "mac": "MAC", "manually": "手动", "maxSms": "您已达到每月短信限额 (最大. {{ max }})", "noConfigAvailable": "没有可用的配置", "noConfigMsg": "检查管理/设备配置页面上的配置。", "noFirmwareMsg": "检查管理/固件页面上的固件文件。", "noPackageMsg": "在网安防护包管理页面，查看网安防护包。", "noProfileAvailable": "没有可用的配置文件", "noProfileMsg": "在「规则配置文件管理」页面查看配置文件。", "notSupportModel": "不支持型号", "notSync": "未同步", "noVersionAvailable": "没有可用版本", "oneTime": "一次", "outOfSync": "不同步", "package": "防护包", "packageUpgrade": "所选设备的网安防护包将被升级。", "packageVersion": "防护包版本", "period": "期间", "policyProfile": "规则配置文件", "processing": "处理中", "profileName": "配置文件名称", "profileSync": "所选设备的配置文件将被同步。", "reboot": "设备将重新启动。", "rebootDisabled": "仅可重启在线设备。", "rebootMsg": "您确实要重新启动选定的设备吗？", "rebootTitle": "重启设备", "remoteSmsControl": "远程短信控制", "restoreConfigDisabled": "仅可同步相同型号的在线设备。", "sameVersionWarning": "一个或多个选定的设备已应用版本 {{ version }}。", "schedule": "调度", "scheduleDisabled": "仅可调度相同型号的设备。", "scheduleOverlapMsg": "无法选择已经分配重启或固件升级的时间段。", "scheduleSettings": "调度设置", "scheduling": "调度", "schedulingMode": "调度模式", "schedulingPeriod": "调度周期", "schedulingReboot": "调度重启", "selectConfigFile": "选择配置文件", "selectFile": "选择档", "sendSms": "发简讯", "sendSmsControl": "发送短信控制", "sendSmsOnCell": "选择一个 OnCell 设备发送短信控制", "sendSmsSuccess": "短信发送成功", "serialNumber": "序列号", "setDoOff": "设置 DO 关闭", "setDoOn": "设置 DO 打开", "shouldBeSameVersion": "所选设备的软件包版本应该相同。", "shouldHaveJanus": "选定的一个或多个设备未安装网安防护包。", "shouldSyncOnline": "仅可同步在线设备。", "showAll": "显示所有组和设备", "showSelected": "显示选定的组和设备", "smsCountDownHint": "60 秒后发送下一条短信", "softwarePackage": "网安防护包", "startIpsecTunnel": "启动 IPsec 隧道", "startTime": "开始日期", "status": "状态", "statusProfileName": "状态/配置文件名称", "stopIpsecTunnel": "停止 IPsec 隧道", "switchSim": "切换SIM卡", "sync": "已同步", "syncConfig": "同步配置", "syncConfigTitle": "将配置同步到设备", "syncModified": "已同步（已修改）", "syncProfile": "同步配置文件", "syncProfileTitle": "将配置文件同步至设备", "systemRestart": "系统重启", "time": "时间", "updateScheduleSuccess": "设备调度更新成功。", "upgradeDisabled": "仅在线的设备才可以升级。", "upgradePackageError": "2.5.0 以上和 2.4.x 以下的固件版本不能共存。", "upgradePackageNotSameDisabled": "仅可选择相同型号的设备", "upToDate": "最新", "version": "版本", "weekly": "每周", "weeklyDay": "每周某日"}, "logging": {"eventLog": {"adp": "ADP", "audit": "审计", "device": "设备", "dos": "DoS规则", "dpi": "协议筛选规则", "endDate": "结束日期", "endTime": "结束时间", "event": "事件", "firewall": "防火墙", "ips": "IPS", "l2Policy": "第 2 层规则", "l3Policy": "第 3-7 层规则", "malformed": "格式错误的数据包", "sc": "会话控制", "setting": "设置", "severity": "严重性", "startDate": "开始日期", "startTime": "开始时间", "tab": {"audit": {"deviceName": "设备名称", "event": "事件", "groupName": "群组名称", "message": "信息", "severity": "严重性", "time": "时间", "username": "用户名称"}, "device": {"deviceName": "设备名称", "event": "事件", "groupName": "群组名称", "mac": "MAC 地址", "message": "信息", "severity": "严重性", "time": "时间", "username": "用户名称"}, "firewall": {"action": "动作", "adp": "ADP", "all": "所有事件", "appProtocol": "应用协议", "category": "类别", "deviceName": "设备名称", "dos": "DoS规则", "dpi": "协议筛选规则", "dstIp": "目的IP", "dstMac": "目的地MAC地址", "dstPort": "目的端口", "etherType": "以太类型", "event": "事件", "fromInterface": "传入接口", "groupName": "群组名称", "icmpCode": "ICMP 代码", "icmpType": "ICMP 类型", "id": "序号", "ips": "IPS", "ipsCategory": "IPS类别", "ipsSeverity": "IPS 严重性", "l3Policy": "第 3-7 层规则", "malformed": "格式错误的数据包", "message": "附加信息", "policyId": "规则 ID", "policyName": "规则名称", "protocol": "IP 协议", "security": "安全", "sessionControl": "会话控制", "severity": "严重性", "srcIp": "来源 IP", "srcMac": "源MAC", "srcPort": "源端口", "subCategory": "子类别", "tcpFlag": "TCP 标志", "time": "时间", "toInterface": "传出接口", "trustAccess": "可信访问", "username": "用户名称", "vlanId": "VLAN ID"}, "vpn": {"deviceName": "设备名称", "event": "事件", "groupName": "群组名称", "message": "附加信息", "severity": "严重性", "time": "时间", "username": "用户名称"}}, "trustAccess": "可信访问", "vpn": "VPN"}, "notification": {"advancedSettingMsg": "一旦在一段时间内达到最大通知数量，则在下一个时间段之前不会再发送任何通知。", "advancedSettings": "进阶设定", "appProtocol": "应用协议", "arpFlood": "ARP-Flood", "atLeastOneReceiver": "至少一个接收者", "bufferOverflow": "缓冲区溢出", "chooseDevices": "选择设备", "createdBy": "创建自", "createNotification": "添加网络安全事件", "createSuccess": "网络安全事件创建成功", "deleteFailed": "此事件用于通知，无法删除。", "deleteKey": "网络安全事件", "deleteNotification": "删除通知", "deleteSuccess": "已成功删除网络安全事件", "deviceCount": "设备数量", "deviceName": "设备名称", "DNP3": "DNP3", "dosAttacks": "DoS 攻击", "dstIp": "目的IP", "dstMac": "目的地MAC地址", "editNotification": "编辑网络安全事件", "EIP": "EIP", "email": "电子邮件", "emailContent": "电子邮件内容", "emailContentDefault": "事件 ${event} 在设备 ${productModel}, ${deviceName} 上触发，发生时间为 ${eventTime}。\n", "emailHeader": "[MXsecurity] 通知 ${notificationName}\n从${deviceName} 生成", "emailMsgAutoSentFrom": "此通知已自动自 MXsecurity发送。", "emailMsgCheck": "请在 MXsecurity 上查看详细信息。", "emailMsgGreeting": "尊敬的先生/女士，", "emailMsgSignOff": "此致，\nMXsecurity", "eq": "等于", "event": "事件", "event_used": "此事件已在通知设置中使用，无法修改。", "eventFilter": "选择事件和过滤条件", "eventFilterRule": "事件过滤规则", "eventTime": "事件时间", "exploits": "漏洞", "fileVulnerabilities": "文件漏洞", "filterRule": "过滤规则", "filterRuleDetail": "过滤规则详情", "finScan": "FIN Scan", "floodingScan": "泛洪和扫描", "GOOSE": "GOOSE", "gt": "低于", "gte": "低于或等于", "icmpDeath": "ICMP-Flood", "IEC-104": "IEC-104", "ipAddress": "IP 地址", "ipRangeHint": "您可以使用 * 来表示 /8/16/24 子网掩码的结果，例如 192.168.*.* 不能用在 IP 地址的开头或中间 *", "ipsCate": "IPS类别", "ipSpoofing": "IP欺骗", "ipsSeverity": "IPS 严重性", "location": "位置", "lt": "高于", "lte": "高于或等于", "macAddress": "MAC 地址", "macRangeHint": "您可以使用 * 来表示一系列 \\n MAC 地址，例如 00:90:E8:*:*:* \\n 不能用在 \\n MAC 地址的开头或中间。", "malwareTraffic": "恶意软件流量", "maxEnableSize": "最大启用通知数为 {{num}}。", "maxNotification": "最大通知", "maxPerUserSize": "每个用户的最大通知数量为 {{num}}。", "MMS": "MMS", "Modbus/TCP": "Modbus/TCP", "newTcpWithoutSynScan": "TCP Sessions Without SYN", "nmapIdScan": "NMAP-ID Scan", "nmapXmasScan": "NMAP-<PERSON><PERSON>", "notificationActions": "通知操作", "notificationEvent": "通知事件", "notificationInfo": "通知信息", "notificationLimit": "通知限制", "notificationName": "事件名称", "nullScan": "<PERSON><PERSON>", "OmronFINS": "Omron FINS", "periodTime": "时间段", "policyName": "规则名称", "productModel": "产品型号", "protocolAttackProtection": "协议攻击防护", "receiverEmailAddress": "收件人电子邮件", "receiverSetting": "收件者设置", "reconnaissance": "侦察", "resetToDefault": "重置为默认", "serialNumber": "序列号", "severity": "严重性", "severityMode": "严重性模式", "severityRule": "严重性规则", "showAllDevices": "显示所有设备", "showSelectedDevices": "显示选定的设备", "srcIp": "来源 IP", "srcMac": "源MAC", "Step7Comm": "Step7Comm", "subCate": "子类别", "synFinScan": "SYN/FIN Scan", "synFlood": "SYN-Flood", "synRstScan": "SYN/RST Scan", "syslog": "Syslog", "syslogContent": "Syslog 内容", "syslogContentDefault": "通知 ${notificationName} 已在设备上触发。${productModel}，${deviceName}，发生在 ${eventTime}。详细信息请查看 MXsecurity。", "udpFlood": "UDP-Flood", "updateSuccess": "通知配置已经更新成功", "webThreats": "网路威胁", "xmasScan": "<PERSON><PERSON>"}}, "management": {"deviceConfiguration": {"configModel": "配置模型", "configName": "配置名称", "createSuccess": "设备配置创建成功。", "deleteKey": "设备配置", "deleteSuccess": "设备配置已成功删除", "editConfig": "编辑配置", "enterConfigInfo": "输入配置文件信息", "firmwareVersion": "固件版本", "group": "群组", "isReferenced": "参考了一个或多个选定的配置。", "lastModifiedTime": "上次修改时间", "location": "位置", "mac": "MAC 地址", "maxTableSize": "最大配置数为 {{num}}。", "noModelMsg": "没有配置模型", "offlineWarning": "设备离线", "onlyAcceptIni": "仅接受' .ini '格式的配置文件。", "onlyOneFilePerTime": "每次只能上传一个文件。", "selectConfigFile": "选择配置文件", "selectWarning": "仅允许一台设备进行配置备份", "serialNumber": "序列号", "updateSuccess": "设备设置更新成功", "uploadConfigFile": "上传配置文件 (.ini)", "uploadConfigMethod": "上传配置方法", "uploadConfigTitle": "上传设备配置文件", "uploadDeviceConfig": "从设备上传配置", "uploadLocalConfig": "从本地上传配置"}, "deviceGroup": {"accessPermission": "存取权限", "addDevices": "添加设备", "adminPermission": "管理员用户拥有所有群组的权限", "createGroup": "创建群组", "createSuccess": "设备群组创建成功", "deleteKey": "删除设备群组", "deleteSuccess": "设备群组刪除成功", "description": "描述", "deviceCount": "设备数量", "editGroup": "编辑设备群组", "enterGroupInfo": "输入群组信息", "firmwareVersion": "固件版本", "grantAccessPermission": "授予存取权限", "group": "群组", "groupName": "群组名称", "location": "位置", "mac": "MAC", "role": "角色", "serialNumber": "序列号", "showAllDevices": "显示所有设备", "showSelectedDevices": "显示选定的设备", "status": "状态", "updateSuccess": "设备群组更新成功。", "username": "用户名称"}, "firmware": {"buildTime": "构建时间", "deleteKey": "固件", "deleteSuccess": "固件删除成功。", "description": "描述", "dropZoneTitle": "上传固件文件 (.rom)", "isReferenced": "引用了一个或多个选定固件。", "maxRowMsg": "固件文件的数量最多为 {{ max }}。", "maxSize": "允许的最大文件大小为 1 GB。", "modelSeries": "型号系列", "onlyAcceptRom": "仅接受 '.rom' 格式的固件文件。", "onlyOneFilePerTime": "一次只能上传一个文件。", "uploadFirmware": "上传固件", "uploadSuccess": "固件上传成功。", "version": "版本"}, "inUse": "是", "object": {"filter": {"address": "IP 地址和子网", "code": "代码", "createObject": "创建物件", "createSuccess": "物件创建成功。", "customIpProtocol": "自定义IP协议", "decimal": "（十进制）", "deleteKey": "物件", "deleteSuccess": "物件已成功删除。", "detail": "详细信息", "editObject": "编辑物件", "endPort": "端口：结束", "icmp": "ICMP", "icmpCode": "ICMP 代码", "icmpType": "ICMP 类型", "industrialAppService": "行业应用服务", "ipAddress": "IP 地址", "ipEnd": "IP 地址： 结束", "ipProtocol": "IP协议", "ipRange": "IP范围", "ipStart": "IP 地址： 开始", "ipType": "IP类型", "isReferenced": "引用一个或多个选定物件", "leaveAsAny": "留空代表任意", "maxRowMsg": "物件的最大数量为 {{ max }}。", "name": "筛选", "needSelectedMsg": "至少选择一项", "networkName": "网络名称", "networkService": "网络服务", "objectName": "名称", "objectReference": "物件引用", "objectReferenceMsg": "该物件由以下配置文件中的规则索引引用：", "objectType": "物件类型", "port": "端口", "portRange": "TCP 和 UDP 端口范围", "selectIndustrialAppService": "选择工业应用服务*", "selectNetworkService": "选择网络服务*", "servicePortType": "服务端口类型", "singleIp": "单一IP", "singlePort": "TCP 和 UDP 端口", "startPort": "端口： 启动", "subnet": "子网", "subnetMask": "子网掩码", "tcp": "TCP", "tcpUdp": "TCP 和 UDP", "type": "类型", "udp": "UDP", "updateSuccess": "物件更新成功。", "userDefinedService": "自定义服务"}, "interface": {"bridge": "桥接", "createInterface": "创建网络接口物件", "createSuccess": "接口创建成功。", "deleteKey": "接口", "deleteSuccess": "接口已成功删除。", "editInterface": "编辑网络接口物件", "interfaceName": "接口名称", "interfaceReference": "接口引用", "interfaceReferenceMsg": "以下配置文件中的规则索引引用了此接口：", "invalidKey": "以下名称是保留的：任何", "isReferenced": "引用了一个或多个选定的接口。", "maxRowMsg": "最大接口数量为 {{ max }}。", "mode": "模式", "name": "接口", "port": "基于端口", "updateSuccess": "接口更新成功。", "vlan": "VLAN", "vlanIdBridgeType": "VLAN ID / 桥接模式", "zone": "基于分区"}}, "policyProfile": {"createProfile": "创建规则配置文件", "createSuccess": "配置文件已成功建立。", "deleteKey": "配置文件", "deleteSuccess": "配置文件已成功删除。", "deployment": {"profile_title": "规则配置文件部署", "security_title": "网安防护包部署", "title": "规则配置文件部署"}, "dos": {"all_protection_types": "启用所有保护类型", "dosLogSetting": "DoS 日志设置", "dosSetting": "DoS 设置", "floodProtection": "防洪", "limit": "限制", "portScanProtection": "端口扫描保护", "sessionSYNProtection": "会话 SYN 保护", "stat1": "<PERSON><PERSON>", "stat10": "SYN-Flood", "stat11": "ARP-Flood", "stat12": "UDP-Flood", "stat2": "<PERSON><PERSON>", "stat3": "NMAP-<PERSON><PERSON>", "stat4": "SYN/FIN Scan", "stat5": "FIN Scan", "stat6": "NMAP-ID Scan", "stat7": "SYN/RST Scan", "stat8": "TCP Sessions Without SYN", "stat8Tooltip": "限制：对于非对称网络架构并且当启用 NAT 时，强烈建议不要禁用“无 SYN 的 TCP 会话”，以避免意外断开连接。", "stat9": "ICMP-Flood", "title": "DoS"}, "editProfile": "编辑规则配置文件", "ips": {"accept": "接受", "category": "类别", "custom": "（客制）", "id": "ID", "impact": "影响", "monitor": "监视", "noPackageMsg": "在网安防护包管理页面，查看网安防护包。", "noVersionAvailable": "没有可用版本", "packageVersion": "防护包版本", "reference": "参考", "reset": "重置", "ruleSetting": "规则设置", "title": "IPS", "updateSuccess": "规则更新成功。", "warningMsg": "在配置任何规则之前，请确保已在设备 Web 界面的防火墙 > 進階防护 > 配置功能頁启用了入侵防御系统 (IPS) 功能。"}, "isReferenced": "引用了一个或多个选定的配置文件。", "layer3to7": {"allowAll": "允许全部", "createPolicy": "创建第 3-7 层规则", "createSuccess": "第 3-7 层规则已成功创建。", "default_action_log": "事件日志", "default_action_log_destination": "日志目的地", "default_action_severity": "严重性", "defaultAction": "动作", "deleteKey": "第 3-7 层规则", "deleteSuccess": "第 3-7 层规则已成功删除。", "deleteTitle": "删除 3-7 层规则", "denyAll": "全部拒绝", "destinationAddress": "目的地地址", "destinationPort": "目标端口或协议", "destIpAddress": "目的IP地址", "destService": "目的地服务", "editPolicy": "编辑第 3-7 层规则", "enforce": "状态", "enforcement": "状态", "event": "事件", "eventSetting": "默认规则设置", "filterMode": "过滤模式", "globalSetting": "全域防火墙设置", "incomingInterface": "传入接口", "ipAndPortFiltering": "IP 和端口过滤", "ipAndSourceMacBinding": "IP与源MAC绑定", "ipTypeError": "源端口 IP 协议 ({{ source }}) 与目标端口 IP 协议 ({{ dest }}) 不同", "maxRowMsg": "物件的最大数量为 {{ max }}。", "outgoingInterface": "传出接口", "policyName": "名称", "protocolService": "协议与服务", "sourceAddress": "源地址", "sourceIpAddress": "源IP地址", "sourceMacAddress": "源MAC地址", "sourceMacFiltering": "源MAC过滤", "sourcePort": "源端口", "title": "第 3-7 层", "updateSuccess": "第 3-7 层规则更新成功。"}, "maxRowMsg": "配置文件的最大数量为 {{ max }}。", "profileName": "配置文件名称", "profileReference": "配置文件參考", "profileReferenceMsg": "以下设备引用了此配置文件：", "sessionControl": {"concurrentTcp": "并行 TCP 连接数", "connectionsRequestUnit": "连接数/秒", "connectionsUnit": "连接", "createPolicy": "创建会话控制规则", "createSuccess": "会话控制规则创建成功。", "deleteKey": "会话控制规则", "deleteSuccess": "会话控制规则删除成功。", "destinationIp": "目的IP", "destinationPort": "目的端口", "destIpAddress": "IP 地址", "destPort": "端口", "drop": "丢弃", "editPolicy": "编辑会话控制规则", "enforcement": "状态", "maxRowMsg": "该设备的最大规则数量为 {{ max }}。", "monitor": "监视", "sub_title": "网络主机和服务资源保护器", "tcpConnectionLimit": "TCP 连接限制", "tcpDestError": "IP 地址和端口不能同时为 \"任何\"", "tcpDestination": "TCP 目的地", "tcpLimitError": "您必须至少配置一项限制", "tcpLimitMsg": "至少需要一项限制", "title": "会话控制", "totalTcp": "TCP 连接总数", "updateSuccess": "会话控制规则更新成功。"}, "tabInspection": "检查条件物件", "tabInterface": "网络接口物件", "tabPolicyProfile": "规则配置文件", "title": "规则配置文件管理", "updateSuccess": "配置文件已成功更新。"}, "scheduleInUse": "使用中的计划", "scheduling": "調度", "softwarePackage": {"applicationProducts": "适用产品", "auto-download": "自动下载", "bugsFixed": "错误修复", "buildTime": "构建时间", "changes": "变更", "checkConnection": "检查与 Moxa 更新服务器的连接状态。", "checkNewPackage": "检查 MOXA 服务器上的新网安防护包版本。", "checkSoftwarePackage": "检查网安防护包更新", "daily": "每日", "deleteKey": "网安防护包", "deleteSuccess": "网安防护包已成功删除。", "description": "描述", "detailInfo": "详细信息", "dropZoneTitle": "上传防護包 (.pkg)", "endDate": "结束日期", "endTime": "结束时间", "enhancements": "优化项目", "event": "事件", "isReferenced": "引用一个或多个选定防护包", "janus": "网安防护包", "lastConnectionCheck": "最后连接检查", "lastSoftwarePackageUpdateResult": "最后网安防护包更新结果", "licenseActivationReminder": "授权激活提醒", "licenseActivationReminderContent": "为了确保增强的安全机制，请激活授权以启用此功能。", "licenseTransferReminder": "授权转移提醒", "licenseTransferReminderContent": "为了确保增强的安全机制，请在上传网络安全包之前转移您的 MXsecurity 授权。", "local": "本地", "log": "事件日志", "maxPackageMsg": "最大同时下载数量：{{ max }} 个文件。", "maxRowMsg": "防护包的最大数量为 {{ max }}。", "maxSize": "允许的最大文件大小为 1 GB。", "message": "信息", "newFeatures": "新功能", "notes": "备注", "onlyAcceptPkg": "仅接受“.pkg”格式的文件。", "onlyOneFilePerTime": "每次只能上传一个文件。", "packageDownloading": "安装包下载器正在运作", "packageReference": "防护包参考", "packageReferenceMsg": "下列配置文件引用了该防护包：", "period": "期间", "productModel": "产品型号", "releaseDate": "发布日期", "releaseNote": "发行说明", "scheduling": "定期更新检查", "schedulingMode": "调度模式", "server": "Moxa 更新服务器状态", "serverDisconnected": "仅当服务器连接时才可以检查网安防护包。", "severity": "严重性", "softwarePackageAlreadyLatest": "网安防护包已是最新版本。", "softwarePackageCheck": "网安防护包检查", "softwarePackagesFile": "网安防护包档案", "softwarePackagesUpdateCheck": "更新网安防护包", "startDate": "开始日期", "startTime": "开始时间", "supportedFunctions": "支持的功能", "supportedOperatingSystems": "支持的操作系统", "supportModel": "支持的型号", "supportSeries": "支持的系列", "syncSettingNotSet": "完成同步设置以检查网安防护包。", "syncSettings": "调度更新检查", "syncSettingUpdateSuccess": "设置更新成功", "syncSoftwarePackageBySchedule": "根据用户指定的计划自动检查指定型号的网安防护包更新。", "syncSoftwarePackageByScheduleTooltip": "配置检查 Moxa 服务器网安防护包更新的频率。", "time": "时间", "title": "网安防护包管理", "updateCheckTooltip": "检查 Moxa 服务器的网安防护包更新，以确保您使用的是最新版本。", "uploadBy": "上传自", "uploadSoftwarePackage": "上传防護包", "uploadSuccess": "网安防护包上传成功。", "username": "用户名称", "version": "版本", "weekday": "天", "weekly": "每周", "zeus": "MXsecurity 代理程序包"}}}, "PORT_SETTING": {"another_port_setting_error": "另一个设定正在处理", "apply_another_port": "将设置应用到另一个端口", "disable_port_warning": "警告：禁用此端口将断开连接到此端口的设备。", "enable": "启用", "get_port_setting_fail": "无法获取端口设置", "hint": "* 如果设置失败，请确认所选端口可以设定", "media_type": "Media Type", "port": "端口", "port_description": "端口描述", "port_name": "端口名称", "port_select": "您选择了", "set_fail": "部分端口设置失败，请稍后再试", "set_success": "所有的端口设置成功", "title": "以太网/光纤端口设置"}, "PREFERENCES": {"advanced": "进阶", "appearance": "外观", "default_view": {"choose_start_page": "选择一个起始页", "dashboard": "仪表板", "title": "默认检视", "topology": "拓扑"}, "device_appearance": {"alias": "别名", "bottom_hint": "如果您改变了别名设置，请删除拓扑上的设备，然后重新扫描或添加设备以完成'别名'设置。", "bottom_label": "底部标签", "bottom_label_items": {"alias": "别名", "location": "位置", "mac": "MAC", "model_name": "型号名称", "none": "无", "sysname": "SysName"}, "get_fail": "无法取得设备外观设定", "ip_address": "IP 地址", "set_fail": "无法储存设备外观", "set_success": "设备外观储存成功", "title": "设备"}, "device": {"login": "登录", "title": "设备"}, "dialog": {"desc": "清除所有\"不再显示此讯息\"设定，然后再次显示所有隐藏的对话框", "title": "对话框"}, "display": "外观", "email_config": {"allow_selfsigned_cert": "允许自签名证书", "apply_fail": "无法储存EMail server 配置", "apply_success": "EMail server 配置储存成功", "encryption": "加密", "password": "密码", "port_number": "通讯端口号", "sender_address": "发件人地址", "server_domain_name": "服务器域名/IP", "title": "EMail Server 设定", "username": "用户名称"}, "events": {"apply_fail": "无法储存事件阈值配置", "apply_success": "事件阈值配置储存成功", "availability_under": "可用性低于", "bandwidth_utilization_over": "带宽利用率超出", "bandwidth_utilization_under": "带宽利用率低于", "link_down": "断线", "link_up": "连线", "packet_error_rate_over": "封包错误率超出", "port_looping": "端口循环", "sfp_rx_below_threshold": "SFP RX 低于", "sfp_temp_over_threshold": "SFP 温度超出", "sfp_tx_below_threshold": "SFP TX 低于", "sfp_volt_below_threshold": "SFP 电压低于", "sfp_volt_over_threshold": "SFP 电压超出", "title": "事件"}, "labs": {"colored_link_desc": "启用后，所有的无线链路都会按信噪比来显示颜色", "desc": "切换到实验室会增加以下实验性功能，它们处于早期阶段，但不会对您的系统造成伤害。", "dialog_title": "启用实验室功能", "title": "MXview One实验室"}, "language": {"default_language": "语言", "en_US": "English", "fail": "无法储存默认语言", "success": "默认语言储存成功", "title": "语言", "zh_CN": "简体中文", "zh_TW": "繁體中文"}, "login_authentication": {"apply_fail": "无法储存登录认证", "apply_success": "登录认证储存成功", "authentication_protocol": "认证协议", "local": "本地", "tacacs": "TACACS+", "tacacs_local": "TACACS+, 本地", "title": "登录认证"}, "login_notification": {"fail": "无法储存登入讯息", "login_authentication_failure_message": "登入认证错误讯息", "login_message": "登入讯息", "show_default_password_notification": "显示默认密码讯息", "show_login_failure_records": "显示失败记录", "success": "登入讯息储存成功", "title": "登入讯息"}, "management_interface": {"help": " 此设定页面用来设定MXview One如何与开关通讯，包括http，https，telnet端口的设定", "http_port": "HTTP Port", "htts_port": "HTTPS Port", "invalid_port": "端口号码必须是 1-65535", "set_fail": "无法设置管理接口", "set_success": "设定管理接口成功", "telnet_port": "Telnet Port", "title": "管理接口", "web_console_protocol": "网页控制台传输协议"}, "modbus_tcp_configuration": {"port": "port"}, "opc_server_config": {"apply_fail": "无法储存OPC server 配置", "apply_success": "OPC server 配置储存成功", "enable_opc_server": "启用", "title": "OPC Server 服务器配置"}, "password_policy": {"fail": "无法储存密码规则", "has_digits": "最少一个数字 (0~9)", "has_special_chars": "最少一个特殊字符 (~!@#$%^&*-_|;:,.<>[]{}())", "min_password_length": "最短长度 (4 - 16)", "min_password_length_error": "请输入有效的值", "mixed_case": "混合大小写 (A~Z, a~z)", "password_strength_check": "密码强度检查", "success": "密码规则储存成功", "title": "密码规则"}, "search": "搜索", "security_view": {"all": "全部", "awk_device_credentials_hint": "要支持安全检视，必须设置此设备的用户名和密码", "basic": "基本", "basic_text": "基本", "built_in_profile": "内建设定档", "check_item": "检查项目", "colors_for_check_result": "颜色定义", "current_setting": "当前设置:", "custom": "自订", "custom_profile": "选择一个设定档", "device_security_level": "设备安全级别:", "failed": "设定安全检视失败", "filter_result": "筛选结果为", "high": "高", "high_text": "高", "medium": "中", "medium_text": "中", "new_profile": "新配置文件", "not_pass": "未通过", "open": "未达安全层级", "pass": "通过", "profile": "设定档", "profile_details": "内建设定档详细资讯", "success": "设定安全检视成功", "title": "安全检视", "unknown": "未支持", "user_defined": "使用者自订"}, "Server": "服务器", "site_name_configuration": "站点名称配置", "sms_config": {"apply_fail": "无法储存 SMS 配置", "apply_success": "SMS 配置储存成功", "baud_rate": "Baud <PERSON>", "com_port": "COM 端口", "mode": "模式", "title": "SMS COM Port 设定"}, "snmp_configuration": {"help": "设置SNMP配置以访问网络设备", "title": "SNMP配置"}, "SNMP_TRAP": {"apply_fail": "无法进行 SNMP Trap 设定", "apply_success": "SNMP Trap Server 设置已被设定成功", "community1": "Trap Server 1 的 Community", "community2": "Trap Server 2 的 Community", "device_list": "装置列表", "device_trap": "装置的 Trap Server", "forward_trap_control1": "转发 Trap 到 Server 1", "forward_trap_control2": "转发 Trap 到 Server 2", "ip1": "Trap Server 1 的 IP 地址", "ip2": "Trap Server 2 的 IP 地址", "mxview_trap": "MXview One 的 SNMP Trap Server", "version": "SNMP 版本", "version_1": "SNMP 版本 1", "version_2": "SNMP 版本 2c"}, "syslog_config": {"already_running": "配置文件不正确。 请打开 %MXviewPRO_Data%\\nms-platform\\runtime\\generic-processor\\configs 中的 config.json 文件，并将 enable_syslog_server 设置为“true”。", "apply_fail": "无法储存syslog server 配置", "apply_success": "Syslog server 配置储存成功", "enable_syslog_server": "启用内置 syslog 服务器", "invalid_port": "端口号码必须是 1-65535", "syslog_server_port": "Syslog 服务器端口", "title": "Syslog 服务器配置"}, "system_configuration": {"apply_fail": "无法储存系统配置", "apply_success": "系统配置储存成功", "background_discovery": "背景发现", "disk_hint": "设置为0表示停用磁盘空间报警", "playback": "回放", "playback_hint_1": "* 当\"回放\"功能被启用，MXview One将记录事件发生时设备和连线的状态，您可以进入播放模式观看详细过程。", "playback_hint_2": "* 启用\"回放\"功能需要额外的磁盘空间。", "threshold_disk_space": "磁盘空间阈值 (MB)", "title": "系统配置"}, "table": {"default": "默认", "dense": "紧密", "fail": "无法存储表格设定", "success": "表格设定储存成功", "table_row_height": "行高", "title": "表格"}, "tacacs": {"apply_fail": "无法储存 TACACS 服务器配置", "apply_success": "TACACS+ 服务器配置储存成功", "auth_type": "认证类型", "auth_type_asc_two": "ASCII", "auth_type_chap": "CHAP", "auth_type_ms_chap": "MS-CHAP", "auth_type_pap": "PAP", "server_address": "服务器地址", "share_key": "共享密钥", "tcp_port": "TCP 端口号", "timeout": "超时", "title": "TACACS+ 服务器"}, "title": "参数", "topology_appearance": {"access_port": "Access Port", "background": "背景", "background_color": "背景颜色", "directed_line_style": "直线样式", "edit_igmp_visualization_color": "编辑 IGMP 可视化颜色", "edit_traffic_load_color": "编辑流量负载颜色", "edit_vlan_visualization_color": "编辑 VLAN 可视化颜色", "elbow_line_style": "曲折线样式", "fail": "无法储存拓扑外观", "hsr_ring": "HSR Ring", "igmp_visualization": "IGMP 可视化", "link_down": "断线", "link_up": "连接", "member": "Member", "poe": "PoE", "poe_color": "PoE链接颜色", "prp_lan_a": "PRP LAN A", "prp_lan_b": "PRP LAN B", "querier": "<PERSON><PERSON>", "rstp": "RSTP", "show_poe": "在网络拓扑上显示PoE信息", "status_color": "状态颜色", "success": "拓扑外观储存成功", "text_size": "字体大小", "text_size_large": "大", "text_size_medium": "中", "text_size_small": "小", "title": "拓扑", "topology_style": "拓扑连线样式", "traffic_load": "流量负载", "trunk_port": "Trunk Port", "turbo_chain": "Turbo Chain", "turbo_ring_v1": "Turbo Ring V1", "turbo_ring_v2": "Turbo Ring V2", "vlan_visualization": "VLAN 可视化"}, "user": "使用者"}, "rogue_device_detection": {"add_device_for_rogue_device_detection": "将此设备添加到设备基线以进行非法设备侦测", "add_device_to_baseline": "添加设备到基线", "add_device_to_baseline_content": "您确定要将该设备添加到基线吗？", "add_devices_to_baseline_content": "您确定要将这些设备添加到基线吗？", "add_scan_device_for_rogue_device_detection": "将扫描到的设备添加到设备基线以进行非法设备侦测", "clear_all_rogue_device_history": "清除非法设备历史记录", "clear_all_rogue_device_history_hint": "所有非法设备历史记录都将被清除。您确定要继续吗？", "connected_switch_port": "连接的交换机/端口", "creation_time": "创建于", "current_rogue_device": "当前的非法设备", "delete_device_from_baseline": "从基线删除设备", "delete_device_from_baseline_content": "该设备将从基线中移除并添加为非法设备。", "delete_devices_from_baseline_content": "这些设备将从基线中删除，并添加为非法设备。", "device_baseline": "设备基线", "device_baseline_content": "此操作将创建新基线并覆盖现有基线。", "download_all_history_data_to_csv": "将所有历史数据导出到 CSV", "download_current_page_to_csv": "将当前页面导出至 CSV", "first_seen": "首次看到", "ip": "IP 地址", "last_seen": "最后看到", "mac": "MAC 地址", "must_create_a_baseline_first": "无设备基线。请先建立一个基线。", "no_devices_can_add": "未侦测到设备。请先将设备添加至 MXview One。", "port": "端口", "rogue_device_history": "非法设备历史记录", "rogue_device_settings": "非法设备设置", "sequence_no": "序号", "unknown": "未知的", "vendor": "网卡供应商"}, "SCAN_RANGE": {"add_scan_range": "新增扫描范围", "button": {"back": "上一步", "browse_topology": "浏览拓扑", "cancel": "取消", "discovery": "确认并运行发现程序", "next": "下一步", "recover": "恢复", "scan_new_network": "扫描新的网络"}, "cidr_address_range": "CIDR 位置范围", "duplicate_range": "扫描范围与存在范围重迭", "edit_scan_range": "编辑扫描范围", "firstIp_higher_lastIp": "Ip 范围无效 (首个 IP > 最后 IP)", "subnet_mask": "子网掩码", "table_title": {"active": "有效", "background_scan": "背景扫描", "conflict_scan": "IP 冲突检测", "edit": "编辑", "end_ip": "最后 IP 地址", "group": "群组", "name": "名称", "site_name": "站点名称", "start_ip": "首个 IP 地址"}, "wizard": {"complete": "完成", "complete_message": "有{{discoveryDevices}}台已被添加到MXview One", "discovery_result": "发现结果", "network_range": "网络范围", "save_hint": "设备发现后，扫描的范围将被保存。", "title": "扫描范围向导"}}, "script_automation": {"add_a_script_automation": "添加脚本自动化", "add_script_button_hint": "脚本自动化的最大数量为 200。", "add_script_first_hint": "未找到脚本自动化。请转至脚本自动化屏幕以添加脚本自动化。", "add_script_sutomation": "添加脚本自动化", "adjustable_buttons": "重新排序按钮", "affected_devices": "受影响的设备", "affected_devices_info": "此操作将影响 {{ affectedDevices }} 台设备。", "affected_devices_info_2": "此操作将影响以下 {{ affectedDevices }} 台设备", "align_buttons": "将所有群组对齐在一栏中", "all_devices": "所有设备", "automation_button": "自动化按钮", "background_color": "背景颜色", "button_name": "按钮名称", "button": {"panel": "按钮面板", "privilege": "需要管理员权限验证", "state": {"hint": "在任何时间，一个组中只能有一个按钮处于“开启”状态。", "off": "关闭", "on": "开启", "title": "按钮状态"}, "style": "显示模式", "widget": "按钮部件"}, "cli_id_duplicated": "此 CLI 已被选定。", "cli_script_and_target_device": "CLI 脚本和目标设备", "cli_script_hint": "脚本的最大数量为 50。", "color": "颜色", "confirm_proceed": "您想继续吗？", "create_new_group": "创建并添加到新群组", "delete_automation": "您确定要删除此脚本自动化吗？", "delete_multiple_automation": "您确定要删除这些脚本自动化吗？", "delete_script_automation": "删除脚本自动化", "description": "描述", "device_missing": "无法找到以下设备", "drag_button": "将按钮拖至此处以创建新群组。", "edit_button": "编辑按钮", "edit_group": "编辑群组", "edit_panel": "编辑按钮面板名称", "edit_script_button": "编辑脚本自动化", "execute_button": "执行按钮", "execute_button_hint": "执行脚本自动化", "execute_button_info": "请等待该过程完成以查看结果。如果离开此屏幕，您可以转到「已保存的 CLI 脚本」 > 「执行结果」来下载结果。", "extra_large": "特大", "group": "群组", "group_already_exist": "此群组名称已存在", "group_name": "群组名称", "invalid_account": "帐户权限无效", "ip_duplicated": "该设备已被选定。", "large": "大", "last_executed": "上次执行", "leave_page_hint": "您确定要离开此页面吗？", "leave_without_saving": "离开且不保存", "medium": "中", "more": "更多资讯", "name": "名称", "not_change_group": "使用当前群组", "not_saved_hint": "您所做的任何更改都不会被保存。", "script_automation": "脚本自动化", "select_all": "全选", "select_existing_group": "移至另一群组", "select_saved_cli_script": "选择已保存的 CLI 脚本", "small": "小", "start_preview": "启用拓扑预览", "stop_preview": "停止预览", "target_device": "目标设备", "text_color": "文字颜色", "widget_size": "小部件尺寸"}, "SECURITY_ITEM": {"ACCESSIBLE_IP_LIST": "启用Trusted Access的功能", "ACCOUNT_LOCKOUT": "启用帐号锁定的功能", "ACCOUNT_VALIDITY": "帐号与密码规则验证", "AUTO_LOGOUT": "启用自动注销的功能", "AWK_SERIES": "无线设备", "BROAD_CAST_STORM": "启用 DDoS Protection 的功能", "changed": "已变更", "disabled": "未启用", "enabled": "已启用", "ENCRYPT_CONSOLE": "关闭未加密的TCP/UDP连接端口", "ENCRYPTED_CONFIG": "启用加密设定档的功能", "HIGH_SECURE_MODE": "高安全性配置", "LOGIN_NOTIFICATION": "设定登入讯息", "MGATE": "网关", "non-changed": "未变更", "not_set": "未设定", "NPORT": "终端服务器", "NPORT5000": "串口服务器", "NTP_SERVER": "设定NTP Client", "PASSWORD_CHANGED": "变更默认密码 / SNMP Community 字符串", "PASSWORD_POLICY": "启用检测密码强度的功能", "read_fail": "读取失败", "set": "已设定", "SWITCH": "交换机", "SYSLOG": "设定Syslog Server", "TRAPSYSLOG": "设定SNMP Trap/Inform或Syslog Server", "unknown": "未支持", "WEB_CERTIFICATE": "汇入网页凭证"}, "SERIAL_PORT_MONITORING": {"all_ports": "所有端口", "any_serial_error_count": "任何串行错误计数", "break_error_count": "中断错误计数", "copy_configuration_device": "将配置复制到设备", "count_threshold": "触发阈值", "counts": "次", "critical": "紧要", "error_status": "串行端口警告", "event_condition_rule": "事件触发规则", "frame_error_count": "帧错误计数", "hint_any": "请按照下方步骤尝试解决以下问题：串行端口 {{portnum}} 已超过溢出错误 ({{overrun error count}})、奇偶校验错误 ({{parity error count}})、帧错误 ({{frame error count}}) 或中断错误 ({{break error count}}) 计数阈值。", "hint_break": "请按照下方步骤尝试解决以下问题：串行端口 {{portnum}} 已超过中断错误计数阈值 ({{count}})。\n串行中断信号表示所连接的串行设备出现特殊情况，例如接线问题、设备故障、设备重置或同步过程。", "hint_frame": "请按照下方步骤尝试解决以下问题：串行端口 {{portnum}} 已超过帧错误计数阈值 ({{count}})。\n当 Moxa 设备接收串口数据时，会检查帧格式是否与串口参数匹配，如果不匹配，则计为帧错误。", "hint_general_1": "如果建议的解决方案不起作用或者您还有其他问题，请先联系您的", "hint_general_2": "。", "hint_general_3": "联系", "hint_general_4": "如果您仍然需要额外的支持。", "hint_overrun": "请按照下方步骤尝试解决以下问题：串行端口 {{portnum}} 已超过溢出错误计数阈值 ({{count}})。\n如果连接的串行设备发送数据的速度太快，以至于 Moxa 设备来不及读取，将导致数据丢失，从而引发溢出错误。", "hint_parity": "请按照下方步骤尝试解决以下问题：串行端口 {{portnum}} 已超过奇偶校验错误计数阈值 ({{count}})。\n奇偶校验错误表示接收到的数据字符与配置的奇偶校验不匹配。", "hint_rx": "请按照下方步骤尝试解决以下问题：串行端口 {{portnum}} 在过去 {{min}} 分钟内未收到任何数据。", "hint_rxtx": "请按照下方步骤尝试解决以下问题：串行端口 {{portnum}} 在过去 {{min}} 分钟内未传输或收到任何数据。", "hint_tx": "请按照下方步骤尝试解决以下问题：串行端口 {{portnum}} 在过去 {{min}} 分钟内未传输任何数据。", "how_to_resolve": "如何解决？", "minutes": "分钟", "no_data_period": "无数据期间", "overrun__error_count": "溢出错误计数", "parity__error_count": "奇偶校验错误计数", "port_duplicated": "此端口/触发规则组合已配置", "port_properties": "事件类型", "resolve_title": "解决串行端口 {{portnum}} 问题", "rx": "RX 闲置", "serial_port": "串行端口", "severity": "严重性", "step1_break": "检查连接的串行设备是否正常運行。", "step1_frame": "检查 Moxa 设备上的串行介面设置（RS-232, RS-422, RS485）和通信参数（例如：115200, 8, n, 1）是否与连接的串行设备设置匹配。", "step1_ovrrun": "检查 Moxa 设备和所连接的串行设备，串行硬件和/或软件流控制是否皆设置正确。", "step1_parity": "检查 Moxa 设备和所连接的串行设备，兩者的奇偶校验和波特率设置是否匹配。", "step1_txrx": "检查 Moxa 设备和串行终端设备之间的串行电缆是否连接正确。", "step2_ovrrun": "检查是否需要在 Moxa 设备上启用 FIFO。", "step2_parity": "在高度干扰地区，请检查串行通信系统是否受到良好的保护。", "step2_txrx": "检查连接的串行设备是否正常运行。", "step3_parity": "检查所连接的串行设备是否存在接线问题或硬件故障。", "still_not_working": "还是不能运作？", "title": "串行端口事件", "tx": "TX 闲置", "tx_rx": "TX 和 RX 闲置", "warning": "警告"}, "SEVERITY": {"critical": "紧要", "information": "信息", "title": "严重性", "warning": "警告"}, "sfpList": {"rx": "RX (dBm)", "temperature": "温度 (°C)", "title": "SFP 列表", "tx": "TX (dBm)", "voltage": "电压 (V)"}, "SITE_MANAGEMENT": {"desc": "描述", "fail": "无法更新站点", "name": "名称", "offline": "站点断线 ({{siteName}})", "online": "站点上线 ({{siteName}})", "success": "更新站点成功", "title": "站点管理"}, "SITE_MENU": {"management": "管理"}, "SITE_PROPERTIES": {"description": "描述", "devices": "设备 (正常 / 警告 / 紧要)", "information": "信息", "name": "名称", "title": "站点属性"}, "SNACK_BAR": {"acking": "确认中...", "copied": "已复制", "deleting": "删除中...", "saving": "储存中..."}, "SYSLOG_SETTINGS": {"all_severity": "所有的", "authentication": "认证", "enable_syslog_forward": "Syslog 转发", "enable_tcp": "启用（仅限 TCP）", "enable_udp": "启用（仅限 UDP）", "enable_udp_tcp": "启用（UDP 和 TCP）", "failed_to_get_syslog_forward_settings": "无法获取 syslog 转送设置", "failed_to_get_syslog_settings": "无法获取 syslog 设置", "filter_settings_hint": "您可以输入多个来源 IP 地址，以逗号分隔。", "forward_ip1": "远程 IP/域名 1", "forward_ip2": "远程 IP/域名 2", "port_1": "端口 1", "port_2": "端口 2", "protocol": "协议", "source_ip": "来源 IP", "syslog_built_in": "内置 Syslog 服务器", "syslog_filter_settings": "Syslog 筛选", "syslog_forwarding": "Syslog 转发", "syslog_server_settings": "Syslog 服务器设置", "tcp": "TCP", "tcp_port": "TCP 端口", "title": "Syslog 设置", "tls_cert": "TLS + 证书", "tls_only": "仅 TLS", "udp": "UDP", "udp_port": "UDP 端口", "update_failed": "无法更新设置", "update_success": "设置更新成功"}, "SYSLOG_VIEWER": {"device_ip": "IP 地址", "facility": "程序", "filter_syslog_event": "键入以筛选 syslog 事件", "ip_error": "请输入有效的IP地址", "message": "讯息", "priority": {"equals": "等于", "high_than": "高于等于", "lower_than": "低于等于", "title": "级别"}, "severity": {"alert": "警告", "critical": "危急", "debug": "调试", "emergency": "紧急事件", "error": "错误", "information": "信息", "notice": "注意", "title": "级别", "warning": "警告"}, "site_name": "站点名称", "timestamp": "时间", "title": "Syslog 浏览器"}, "SYSTEM": {"request_timeout_title": "请求超时", "trigger_disconnected_desc1": "与 MXview One 服务器的连接中断", "trigger_disconnected_desc2": "5 秒后开始重新连接...", "unauthorized_desc": "访问由于凭据无效被拒绝", "unauthorized_title": "未经授权"}, "TABLE": {"add": "添加", "adjustable_columns": "可调整栏位", "compare": "比较", "delete": "删除", "edit": "编辑", "edit_columns": "编辑栏位", "enable": "启用", "export": "导出", "exporting": "输出中...", "filter": "筛选", "import": "导入", "limit_count": "最大", "list_collaspe": "合起", "list_expand": "展开以获取更多信息", "locate": "定位", "no_data": "没有数据显示", "not_support": "不支援此设备版本", "save": "储存", "search": "搜索", "selected_count": "已选取", "show_log": "显示记录", "sync": "同步", "total": "总数", "waiting_data": "等待数据中"}, "TOPOLOGY": {"add_tag": "新增标签...", "add_tag_fail": "无法添加标签", "add_tag_success": "成功添加标签", "choose_goose_publisher": "选择一个GOOSE发布者", "colored_link": "按信噪比着色的连线", "delete_tag_failed": "无法删除标签", "delete_tag_success": "成功删除标签", "device_not_found": "找不到设备", "display_opt": "显示选项", "dynamic_wireless_client_position": "动态客户端位置", "dynamic_wireless_client_position_desc": "启用此选项将显示每个AP周围的每个客户端", "editTagTooltip": "按 Enter 储存。\n按 ESC 取消。", "goose": "GOOSE", "goose_publisher": "GOOSE发布者", "goose_tampered": "GOOSE 被篡改", "goose_timeout": "GOOSE 超时", "grouping_failed": "组成群組失敗。", "grouping_success": "组成群组成功。", "legend": "图例", "new_tag": "新标签", "no_subscriber": "没有订阅者", "prp_hsr": "PRP/HSR", "prp_hsr_tags": "PRP/HSR 标签", "publisher": "发布者", "publisher_hint": "GOOSE 控制块名称\nAPPID / 地址", "search_topology": "搜索拓扑", "set_tag_fail": "无法设置标签", "set_tag_success": "设置标签成功", "show_all_wireless_clients": "显示无线客户端", "site_management_not_supported": "漫游播放仅支持单个站点检视", "subscriber": "订阅者", "subscriber_hint": "IED 名称 / GOOSE 控制块名称", "tag": "标签", "traffic_view": "流量负载(%)", "ungrouping_failed": "取消群组失败。", "ungrouping_success": "取消群组成功。", "wireless_display_opt": "无线显示选项", "zoom_in": "放大", "zoom_out": "缩小", "zoom_to_actual_size": "缩放到实际尺寸", "zoom_to_fit": "缩放到最适大小"}, "TRAP_CONFIGURATION": {"apply_fail": "无法设置Trap Server配置", "apply_success": "成功设置Trap Server配置", "community_name1": "Community Name 1", "community_name2": "Community Name 2", "destination_ip1": "目的 IP1", "destination_ip2": "目的 IP2", "title": "Trap Server"}, "UNIT": {"dB": "dB", "dBi": "dBi", "dBm": "dBm", "hours": "小时", "mb": "Mb", "mbps": "Mbps", "meter": "公尺", "min": "分钟", "sec": "秒", "times": "次数"}, "UPGRADE_FIRMWARE": {"file_type_error": "固件档必须采用 .rom、.tar 和 .gz 文件格式。", "upgrade_firmware_fail": "固件升级失败", "upgrade_firmware_success": "固件升级成功", "upgrading": "上载固件档案, 这一行动可能需要几分钟, 不要断电或断开网络, 请稍等片刻"}, "validators": {"duplicateEmail": "有重复的电子邮件地址", "excludeLastPassword": "新密码不能与上次密码相同", "excludeUserName": "不能包含用户名", "invalid": "无效字符", "invalid_date": "无效日期", "invalid_format_allow_space": "字符串中最多允许有 {{count}} 个空格", "invalidEmail": "无效的电子邮件", "invalidIpAddress": "无效的 IP 地址", "invalidIpAddressOrDomainName": "无效的 IP 地址或域名", "invalidLocation": "允许特殊字元（-_@!#$%^&*(.,/)", "invalidMacAddress": "无效的 MAC 地址", "invalidMacAddressAllZero": "MAC 地址 00:00:00:00:00:00 已保留", "invalidSeverity": "无效严重性", "ipRangeError": "结束 IP 地址需要大于开始 IP 地址", "isExist": "已经存在", "isExistOrUsedByOtherUser": "已经存在或者被其他用户使用", "maxReceiverSize": "最大接收者数量为 {{num}}。", "needDigit": "必须至少包含一位数字 (0 - 9)", "needGreaterThan": "{{ largeItem }} 需要大于 {{smallItem }}", "needLowerCase": "必须至少包含一个小写字符 (a - z)", "needSpecialCharacter": "必须至少包含一个特殊字符（~!@#$%^&amp;*_-+=`|\\(){}[]:;”&#39;&lt;&gt;,.?/）", "needUpperCase": "必须至少包含一个大写字符 (A - Z)", "notMeetPolicy": "不符合密码策略要求", "portRangeError": "结束端口需要大于开始端口", "pwdNotMatch": "密码不匹配", "range": "无效范围 ({{ min }} ~ {{ max }})", "required": "必填", "requireMaxLength": "长度不得超过 {{ number }} 个字符", "requireMinLength": "长度必须至少为 {{ number }} 个字符"}, "Validators": {"require_range_between": "{{ rangeBegin }} - {{ rangeEnd }}"}, "VLAN_TABLE": {"access_ports": "Access 端口", "device_ip": "设备 IP", "empty": "空", "export_csv": "输出 CSV 檔", "filter_vlan": "键入以筛选 VLAN", "hybrid_ports": "Hybrid 端口", "location": "位置", "management_vlan": "管理网络 ID", "model": "型号", "no": "非", "site_name": "站点名称", "title": "VLAN", "trunk_ports": "Trunk 端口", "vlan_id": "VLAN ID", "yes": "是"}, "wirelessPlayback": {"decreaseSpeed": "减低速度", "increaseSpeed": "加快速度", "noData": "在所选择的日期范围内没有数据", "range": "范围", "startTime": "开始时间", "timeRange": "时间范围"}}