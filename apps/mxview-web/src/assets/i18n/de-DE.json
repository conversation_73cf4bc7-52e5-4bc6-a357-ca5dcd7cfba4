{"ABOUT": {"debug_log": "Debug-Protokolle", "debug_log_desc": "Klicken Sie auf die Schaltfläche Download, um Debug-Protokolle zu erstellen und herunterzuladen. Senden Sie die heruntergeladene Protokolldatei über den MOXA-Supportkanal zur weiteren Analyse.", "desc": "Copyright Moxa, Inc. Alle Rechte vorbehalten.", "eula": "View the end user license agreement (EULA)", "gateway": "Gateway-Version", "title": "Info", "web": "Webversion"}, "ACCOUNT_MANAGEMENT": {"access_site": "Zugängliche Standorte", "add_account_dialog": "Benutzerkonto hinzufügen", "add_user_fail": "Das neue Konto kann nicht erstellt werden.", "add_user_success": "Ein neues Konto wird erstellt.", "admin": "Supervisor", "all_user": "<PERSON><PERSON>", "authority": "Berechtigung", "change_password": "Passwort ändern", "delete_user_fail": "Das Konto kann nicht gelöscht werden.", "delete_user_success": "Das Benutzerkonto wird gelöscht.", "demo_user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filter_account": "<PERSON>p zum Filtern von <PERSON>", "modify_account_dialog": "Benutzerkonto ändern", "new_password": "Neues Passwort", "old_password": "Altes Passwort", "password": "Passwort", "password_policy_mismatch": "Das Passwort entspricht nicht der Passwortrichtlinie.", "superuser": "Administrator", "title": "Kontomanagement", "ui_profile": "UI-Profil", "update_user_fail": "Das Konto kann nicht aktualisiert werden.", "update_user_success": "Das Benutzerkonto wird aktualisiert.", "user": "<PERSON><PERSON><PERSON>", "user_account": "User Account", "user_exist": "Der Nutzer ist bereits vorhanden", "username": "<PERSON><PERSON><PERSON><PERSON>"}, "account_password": {"1st_email": "1. E-Mail-Empfänger", "2nd_email": "2. E-Mail-Empf<PERSON><PERSON>", "account_audit": "Kontoprüfung", "account_info": "Kontoinformationen", "account_info_content": "Klicken Sie auf die Schaltfläche ' Aktualisieren ' um die Kontoinformationen für alle Geräte abzurufen. Dies kann einige Zeit dauern.", "account_management": "Kontomanagement", "account_password_management_automation": "Automatisierung der Konto- und Passwortverwaltung", "account_status_audit": "Kontenprüfung", "account_status_baseline": "Konten-Baseline", "account_status_baseline_content": "Dieser Vorgang erstellt eine neue Basislinie und überschreibt die vorhandene.", "accounts": "Buchhaltung", "activate": "Aktivieren", "add_account": "<PERSON><PERSON> hi<PERSON>uf<PERSON>", "add_temporary_account": "Temporäres Konto hinzufügen", "added_account": "Hinzugefügte Konten", "admin": "Admin", "apply_accounts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "audit_automation": "Audit-Automatisierung", "authority": "Berechtigung", "baseline_account": "Basiskonten", "baseline_auto_check_failed": "Die Baseline konnte nicht erstellt werden", "change_admin_name": "Standardmäßigen „Admin“-Namen ändern", "change_admin_name_content": "MXview One verwendet die aktualisierten Kontoanmeldeinformationen, um auf die folgenden Geräte zuzugreifen. Andere Geräte sind nicht betroffen.", "change_admin_name_contents": "MXview One verwendet die aktualisierten Kontoanmeldeinformationen, um auf die folgenden Geräte zuzugreifen. Andere Geräte sind nicht betroffen.", "check_default_account_failed": "Überprüfung des Standardkontos fehlgeschlagen.", "check_password_length": "<PERSON><PERSON><PERSON>, dass die Kennwortlänge die auf dem/den Gerät(en) maximal zulässige Kennwortlänge nicht überschreitet.", "compability": "kompatibel", "create_baseline_failed": "Die Baseline konnte nicht erstellt werden", "create_baseline_failed_no_devices": "Erstellen der Baseline fehlgeschlagen. Keine Geräte erkannt.", "days": "Tage", "default_account": "Standard-Benutzername/Passwort", "default_password_audit": "Standardkennwort-Audit", "default_password_audit_info": "Das Scannen nach Standardkontoanmeldeinformationen kann einige Zeit dauern und führt dazu, dass die Schnittstelle vorübergehend nicht verfügbar ist. Möchten Sie wirklich fortfahren?", "delete_account": "Konto löschen", "delete_temporary_account": "Temporäres Konto löschen", "delete_temporary_account_info": "<PERSON>öchten Sie dieses temporäre Konto wirklich löschen?", "delete_temporary_accounts_info": "Möchten Sie diese temporären Konten wirklich löschen?", "deleted_account": "Gelöschte Konten", "device_alias": "Ger<PERSON><PERSON><PERSON><PERSON><PERSON>", "device_ip": "Geräte-IP", "edit_account": "<PERSON><PERSON> bearbeiten", "edit_temporary_account": "Temporäres Ko<PERSON> bearbeiten", "email_server_configuration": "E-Mail-Server-Setup", "email_server_hint": "Wenn Sie die E-Mail mit dem Bestätigungscode nicht erhalten haben, überprüfen Sie Ihren Spam-Ordner oder überprüfen Sie die", "email_verified": "Die E-Mail-Adresse wurde verifiziert.", "end_date": "Gültig bis", "end_time": "Endzeit", "fatiled_to_audit_account_due": "Die Kontoprüfung konnte nicht abgeschlossen werden. Gerätekontoinformationen konnten nicht von {{ ip }} abgerufen werden.", "fatiled_to_create_baseline_due": "Erstellen der Baseline fehlgeschlagen. Gerätekontoinformationen konnten nicht von {{ ip }} abgerufen werden.", "get_baseline_failed": "Die Baseline konnte nicht abgerufen werden", "get_device_account_failed": "Die Abfrage der Gerätekontoinformationen ist fehlgeschlagen. Andere Anfragen sind in Bearbeitung. Versuchen Sie es später erneut.", "incorrect_verification_code": "Falscher verifikationscode.", "last_audit_time": "Zuletzt geprüft", "last_execution_time": "Zuletzt ausgeführt", "max_char": "Maximal {{num}} <PERSON><PERSON><PERSON>", "model": "<PERSON><PERSON>", "mxa_char": "Maximal {{num}} <PERSON><PERSON><PERSON>", "new_password": "Neues Passwort", "new_username": "<PERSON><PERSON><PERSON>", "next_audit_time": "Nächstes Audit", "next_schdeule_start_time": "Nächster Termin für", "no_data": "nicht verfügbar", "not_activate": "Inaktivieren", "not_started": "<PERSON><PERSON><PERSON><PERSON>", "not_started_hint": "Diese Aufgabe wurde aufgrund eines Systemabsturzes nicht ausgeführt. Klicken Sie auf die Schaltfläche „Passwort neu generieren“, um die Aufgabe manuell auszuführen.", "now": "Sofort", "operation": "Aktion", "password": "Kennwort", "password_automation": "Passwort-Automatisierung", "password_automation_schedule": "Zeitplan für die Passwortautomatisierung", "password_automation_settings": "Assistent zur Kennwortautomatisierung", "password_email_receiver": "Passwort E-Mail Empfänger", "password_regenerated_info": "MXview One verwendet die folgenden Einstellungen, um für jedes Gerät ein neues Passwort zu generieren.", "password_resend_info": "Möchten Sie das Gerätekennwort wirklich an die folgenden Empfänger erneut senden?", "password_resend_result_info": "MXview One hat das Gerätekonto und die Kennwortdatei an die folgende(n) E-Mail-Adresse(n) gesendet:", "password_strength": "Passwortstärke", "random_password_complexity": "Festlegen der Kennwortkomplexität", "random_password_length": "Zufällige Passwortlänge", "random_password_length_info": "MXview One generiert ein zufälliges Passwort für die ausgewählten Geräte.", "randomized_password_failed": "Fehlgeschlagen (Das Gerätekonto stimmt nicht mit dem Datenbankkonto überein.)", "refresh_hint": "Bitte drücken Sie die Schaltfläche ' Aktualisieren ' , um die Gerätekonten abzurufen, bevor <PERSON> mit dieser Aktion fortfahren.", "regenerate_password": "Passwort neu generieren", "resend_password_email": "E-Mail mit Passwort erneut senden", "retrieve_data": "Daten ab<PERSON><PERSON>", "retry_failed_devices": "Ausgefallene Geräte erneut versuchen", "schedule": "<PERSON><PERSON><PERSON>", "schedule_interval": "Interval", "script_error": "Dieses Feld darf keines der folgenden Zeichen enthalten: #%&amp;*{}|:\\&quot;&lt;&gt;?/\\\\", "select_device": "Wählen Sie Geräte aus", "select_device_random_password": "<PERSON><PERSON><PERSON>en Sie die Geräte aus, für die ein zufälliges Passwort generiert werden soll.", "send_password_email": "Passwort-E-Mail senden", "send_password_email_success": "MXview One hat das Gerätekonto, das Passwort und das Ausführungsergebnis an die folgenden E-Mail-Empfänger gesendet:", "set_password_to_device": "Kennwörter auf Geräte anwenden", "set_schedule_interval_failed": "Das Planungsintervall konnte nicht festgelegt werden.", "set_schedule_interval_success": "Zeitplanintervall erfolgreich festgelegt.", "start_date": "Aktiv ab", "start_over": "Start Over", "start_time": "Startzeit", "start_wizard": "Assistent starten", "status": {"cli_session_timeout": "CLI-Sitzungstimeout", "failed": "Gescheiterte", "failed_account_exist": "Fehlgeschlagen (Dieses Konto existiert bereits)", "failed_account_password_incorrect": "Fehlgeschlagen (falsches Konto oder Passwort)", "failed_limit_reached": "Fehlgeschlagen (Gerätekontolimit erreicht)", "failed_not_support_role": "Fehlgeschlagen (Gerät wird nicht unterstützt)", "failed_other_request": "Fehlgeschlagen (Andere Anfragen sind in Bearbeitung)", "failed_retrieve_account_info": "Fehlgeschlagen (Kontoinformationen konnten nicht abgerufen werden)", "finished": "Fertiggestellt", "in_progress": "<PERSON><PERSON> ...", "waiting": "<PERSON><PERSON>"}, "supervisor": "Supervisor", "temporary_account": "Temporä<PERSON>", "test_eamil_recipient": "Test-E-Mail-Empf<PERSON>nger", "title": "Konten und Passwörter", "unable_to_get_accounts": "Konten können nicht abgerufen werden", "user": "<PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON>", "verififcation_code": "Bestätigungs-Code", "verift_title": "Verifizieren Sie Ihr MXview One-Konto", "verify_code_expiration": "Der Bestätigungscode läuft ab in", "verify_email_not_allowed": "Bitte warten Sie mindestens eine Minute, bevor Sie eine weitere E-Mail senden.", "verify_email_password_receiver": "Konto und Passwort bestätigen E-Mail-Empfänger", "verify_email_receiver": "E-Mail-Empfänger bestätigen", "verify_email_server_failed": "Die E-Mail-Serverkonfiguration ist ungültig. E-Mails können nicht gesendet werden.", "verify_user_failed": "Ungültiger Benutzername/ungültiges Passwort"}, "ADD_DEVICE": {"add_device_fail": "Gerät kann nicht hinzugefügt werden.", "add_device_fail_error_message": {"device_has_existed": "Ein Gerät mit dieser IP existiert bereits", "license_limitation_reached": "Lizenzlimit erreicht", "model_not_exist": "Das Modell existiert nicht."}, "add_device_success": "Das Gerät wird erfolgreich hinzugefügt.", "assign_group": "Zur Gruppe zu<PERSON>sen", "assign_model": "<PERSON><PERSON>", "authentication": "Authentifizierung", "auto_detect_model": "Automatische Erkennung", "data_encryption": "Datenverschlüsselung", "encryption_password": "Verschlüsselungspasswort", "encryption_type": "Verschlüsselungsprotokoll", "field_required": "<PERSON>s ist ein Pflichtfeld.", "snmp_setting": "SNMP-Einstellung", "snmp_version": "SNMP-Version", "title": "Ger<PERSON> hinzufügen"}, "ADD_LINK": {"alias": "<PERSON><PERSON>", "device": "G<PERSON><PERSON>", "fail": "Die Verbindung kann nicht hinzugefügt werden.", "from": "<PERSON>", "ip_address": "IP-Adresse", "model": "<PERSON><PERSON>", "only_number": "Erlaubt nur Zahlen", "port": "Hafen", "success": "Die Verbindung wird erfolgreich hinzugefügt.", "title": "Verbindung hinzufügen", "to": "Bis"}, "API_MANAGEMENT": {"access_count": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "add_failed": "Das Hinzufügen eines API-Key ist fehlgeschlagen.", "add_success": "Ein API-Key wird hinzugefügt.", "add_title": "Neues Token hinzufügen", "api_key": "API-Key", "application_name": "Anwendungsname", "create_time": "Erstellungszeit", "delete_failed": "Das Löschen des API-Key ist fehlgeschlagen.", "delete_success": "Das API-Key wird gel<PERSON>t.", "edit_title": "Token bearbeiten", "filter": "Typ zum Filtern von API-Key", "regenerate_api_key": "API-Key neu generieren", "regenerate_failed": "Die Neugenerierung des API-Key ist fehlgeschlagen.", "regenerate_success": "Das API-Key wird neu generiert.", "title": "API-Key-Verwaltung", "update_failed": "Die Aktualisierung des API-Key ist fehlgeschlagen.", "update_success": "Der API-Key wird aktualisiert."}, "ASSIGN_MODEL": {"apply_to_all": "Dieses Symbol auf alle Geräte mit dem gleichen Modell anwenden.", "assign_model_fail": "Das Modell kann nicht zugeordnet werden.", "assign_model_success": "Das Gerätemodell wird aktualisiert.", "ip_address": "IP-Adresse", "model": "<PERSON><PERSON>", "model_icon": "Modellsymbol", "select_model": "<PERSON><PERSON> auswählen"}, "AVAILABILITY_REPORT": {"alias": "Ger<PERSON><PERSON><PERSON><PERSON><PERSON>", "average": "Durchschnittliche Verfügbarkeit", "days": "Tage", "end_date": "Enddatum", "filter": "Typ zum Filtern von Verfügbarkeitsberichten", "from_date": "Anfangsdatum", "query_date": "Abfragedatum", "report_generate_day": "Datum der Berichtserstellung:", "site_name": "Standortname", "title": "Verfügbarkeitsbericht", "worst": "Schlechteste Verfügbarkeit"}, "BASIC_INFORMATION": {"apply_fail": "Die Gerätesystemdaten können nicht eingestellt werden.", "apply_success": "Die Gerätesystemdaten werden aktualisiert.", "contact": "Kontakt", "location": "Ort", "model": "<PERSON><PERSON>", "name": "Name", "title": "Basisinformationen"}, "BUTTON": {"add": "Hinzufügen", "add_to_scheduler": "Zum Planer hinzufügen", "agree": "Zustimmen", "apply": "<PERSON><PERSON><PERSON>", "audit": "Prüfung", "back": "Zurück", "cancel": "Abbrechen", "change": "Ändern", "check": "<PERSON><PERSON><PERSON> An", "checkNow": "Jetzt prüfen", "clear": "Löschen", "clear_fail_record": "Fehlerprotokolle löschen", "close": "Schließen", "compare": "Vergleichen", "confirm": "Bestätigen", "connected": "Verbunden", "continue": "Fortsetzen", "copy": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON><PERSON>", "deactivate": "Deaktivieren", "decline": "Abfall", "delete": "Löschen", "disable_new_version_notifications": "Deaktivieren Sie Benachrichtigungen über neue Versionen", "disconnected": "Getrennt", "download": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "download_all_logs": "Alle Protokolle <PERSON>", "download_filter_logs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "enable_new_version_notifications": "Aktivieren Sie Benachrichtigungen über neue Versionen", "execute": "<PERSON><PERSON>", "faqs": "FAQs", "got_it": "Ich habs", "ignore": "Ignorieren", "leave": "Verlassen", "next": "<PERSON><PERSON>", "ok": "OK", "query": "Abfragen", "reboot": "Starten Sie neu", "redirect": "UMLEITEN", "refresh": "<PERSON><PERSON><PERSON> laden", "regenerate": "<PERSON><PERSON><PERSON><PERSON>", "resend": "<PERSON><PERSON><PERSON> senden", "reset": "Z<PERSON>ücksetzen", "retry_failed_devices": "Ausgefallene Geräte erneut versuchen", "run": "Run", "save": "Speichern", "scan": "<PERSON><PERSON>", "search": "<PERSON><PERSON>", "security_patch_available": "Verfügbare Sicherheitspatches", "select": "Auswählen", "select_firmware_version": "Wählen Sie die Firmware-Version aus", "send": "Senden", "send_test_eamil": "Test-E-Mail senden", "set": "Einstellen", "upgrade": "aktualisieren", "upgrade_firmware": "Firmware upgraden", "upload": "Hochladen", "verify": "Überprüfen", "verify_email": "E-Mail bestätigen"}, "cli_object_database": {"add_cli_fail": "Das CLI-<PERSON>k<PERSON>t kann nicht erstellt werden", "add_cli_object": "CLI-Skript hinzufügen", "add_cli_success": "Neues CLI-Skript erfolgreich erstellt", "before_date": "Datum", "before_time": "Zeit", "cli_objects": "CLI-Skripte", "cli_script": "CLI-Skript", "delete_all_execution_results": "Alle Ergebnisse der CLI-Skriptausführung löschen", "delete_all_execution_results_before_time": "Skriptausführungsergebnisse vorher löschen", "delete_all_execution_results_before_time_desc": "<PERSON>d <PERSON> sic<PERSON>, dass Sie alle Ergebnisse der Skriptausführung vor {{param}} löschen möchten?", "delete_all_execution_results_desc": "Sind <PERSON> sicher, dass Sie alle Ergebnisse der CLI-Skriptausführung löschen möchten?", "delete_cli_fail": "Das CLI-<PERSON>k<PERSON><PERSON> kann nicht gelöscht werden", "delete_cli_object": "CLI-Skript löschen", "delete_cli_object_desc": "Sind <PERSON> sicher, dass Sie dieses CLI-Skript löschen möchten?", "delete_cli_object_disabled": "<PERSON><PERSON> kann nicht gel<PERSON>t werden, da es mit einer geplanten Aufgabe verknüpft ist. Um dieses Skript zu löschen, ändern oder löschen Si<PERSON> zunächst die geplante Aufgabe auf der Seite „Administration > Wartungsplaner“.", "delete_cli_objects_desc": "Sind <PERSON> sicher, dass Sie diese CLI-Skripte löschen möchten?", "delete_cli_success": "CLI-Skript erfolgreich gelöscht", "delete_execution_result_fail": "Ausführungsergebnisse können nicht gelöscht werden", "delete_execution_result_success": "Die Ergebnisse der Skriptausführung wurden erfolgreich gelöscht", "delete_results_before_time": "Ergebnisse der CLI-Skriptausführung löschen", "description": "Beschreibung", "download_all_execution_results": "Laden Sie alle Ausführungsergebnisse herunter", "download_all_execution_results_fail": "Ausführungsergebnisse konnten nicht heruntergeladen werden", "download_execution_results_failed_hint": "<PERSON>s stehen keine Ausführungsergebnisse zum Download zur Verfügung.", "edit_cli_fail": "Das CLI-<PERSON>k<PERSON><PERSON> kann nicht aktualisiert werden", "edit_cli_object": "Bearbeiten Sie das CLI-Skript", "edit_cli_success": "CLI-Skript erfolgreich aktualisiert", "execution_results": "Ausführungsergebnisse", "get_cli_fail": "CLI-Skripte können nicht abgerufen werden", "linked_scheduled_task": "Verknüpfte geplante Aufgaben", "linked_script_automation": "Verknüpfte Skriptautomatisierungen", "name": "Name", "non_ascii": "Es werden nur ASCII-Zeichen akzeptiert.", "scheduled_execution_cli_object_desc": "Sie können auf der Seite „Administration > Wartungsplaner“ geplante Aufgaben erstellen, um CLI-Skripte zu einem bestimmten Datum und einer bestimmten Uhrzeit auszuführen.", "scheduled_execution_cli_object_info": "Geplantes Skript", "scheduled_task": "Geplante Aufgabe", "title": "Gespeicherte CLI-Skripte"}, "COMBO_BOX": {"disabled": "<PERSON><PERSON><PERSON><PERSON>", "enabled": "Freigegeben", "export_all_event_csv": "Exportieren Sie alle Ereignisse in CSV", "export_all_syslog_csv": "Exporter tout syslog au format CSV", "export_csv": "CSV exportieren", "export_pdf": "PDF exportieren", "sequential": "strenge Ordnung", "smart_concurrent": "<PERSON>e Reihenfolge"}, "COMMAND_BAR": {"hide_automation_button": "Automatisierungsschaltflächen ausblenden", "hide_button_panel": "Schaltflächenleiste ausblenden", "hide_detail": "Details ausblenden", "hide_group": "Gruppen verbergen", "list_view": "Listenansicht", "show_automation_button": "Automatisierungsschaltflächen anzeigen", "show_button_panel": "Schaltflächenfeld anzeigen", "show_detail": "Details anzeigen", "show_group": "Gruppen anzeigen", "topology_view": "Topologieansicht"}, "CONFIG_CENTER": {"alias_name": "<PERSON><PERSON><PERSON>", "backup_config": "Backup der Konfiguration erstellen", "backup_message": "MXview One archiviert diese Konfigurationsdateien.", "backup_tab": "<PERSON>up erstellen", "backup_tab_hint": "Gehen Sie bitte zur Registerkarte Backup und exportieren Sie zuerst die Gerätekonfiguration", "compare_config_basement": "Vergleichsbasis: {{compareConfigFileName}}", "compare_config_dialog_title": "Konfigurationen vergleichen", "compare_tab": "Aufzeichnungen", "compare_target": "<PERSON><PERSON>", "configuration_file": "Konfigurationsdatei", "configuration_name": "Konfigurationsname", "create_time": "Uhrzeit der Erstellung", "delete_config_dialog_title": "Konfiguration löschen", "delete_config_failed": "Das Löschen der Gerätekonfiguration ist fehlgeschlagen.", "delete_config_success": "Die Gerätekonfiguration wird gelöscht.", "delete_config_warning_message": "Möchten Sie die ausgewählte Konfiguration wirklich löschen?", "device_list": "Geräteli<PERSON>", "export_failed": "Export fehlgeschlagen", "export_success": "Export erfolgreich", "from_date": "Anfangsdatum", "group_name": "Gruppe", "ip_address": "IP-Adresse", "last_check_time": "Uhrzeit der letzten Prüfung", "local_file": "Lokale Datei", "restore_config": "Konfiguration wiederherstellen", "restore_device": "<PERSON><PERSON><PERSON> wiederherstellen - {{selectedDeviceIP}}", "restore_tab": "Wiederherstellen", "site_name": "<PERSON><PERSON>", "time": "Zeit", "title": "Gerätekonfigurationszentrum", "to_date": "Enddatum"}, "DASHBOARD": {"adpDestIp": "Top 5 ADP-Richtlinienereignisse nach Ziel-IP", "adpSrcIp": "Top 5 ADP-Richtlinienereignisse nach Quell-IP", "ap_devices": "AP-Geräte", "ap_traffic_load": "AP-Traffic-Last", "baseline": "Einfache", "client_devices": "Client-Geräte", "critial_devices": "Kritische Geräte", "device_availability": "Geräteverfügbarkeit", "device_summary": "Geräteübersicht", "devices": "<PERSON><PERSON><PERSON><PERSON>", "disk_space_utilization": "Auslastung des Festplattenspeichers", "dpiDestIp": "Top 5 Protokollfilterrichtlinienereignisse nach Ziel-IP", "dpiSrcIp": "Top 5 Protokollfilterrichtlinienereignisse nach Quell-IP", "event_highlight": "Ereignis-Highlights", "healthy_devices": "Geräte ohne Störung", "icmp_unreachable": "ICMP unerreichbar", "iec_level_1": "<PERSON><PERSON><PERSON>", "iec_level_2": "Hoch", "ipsDestIp": "Top 5 IPS-Richtlinienereignisse nach Ziel-IP", "ipsSrcIp": "Top 5 IPS-Richtlinienereignisse nach Quell-IP", "l3DestIp": "Top 5 Layer 3-7-Richtlinienereignisse nach Ziel-IP", "l3SrcIp": "Top 5 Layer 3-7-Richtlinienereignisse nach Quell-IP", "last_1_day": "Letzte 1 Tage", "last_1_hours": "Letzte 1 Stunden", "last_1_weeks": "Letzte 1 Wochen", "last_2_weeks": "Letzte 2 Wochen", "last_24_hours": "Letzte 24 Stunden", "last_3_days": "Letzte 3 Tage", "last_3_hours": "Letzte 3 Stunden", "last_30_days": "Letzte 30 Tage", "last_30_minutes": "Letzte 30 Minuten", "last_7_days": "Letzte 7 Tage", "last_update": "Letzte Aktualisierung:", "link_down": "Verbindung trennen", "link_up": "Verbindung herstellen", "linkButton": "Ereignisprotokoll anzeigen", "not_pass": "<PERSON>cht bestanden", "now": "<PERSON>b <PERSON>zt", "open": "<PERSON>en", "pass": "<PERSON><PERSON><PERSON>", "reboot_times": "Kalt-/Warmstart-Trap", "refresh_all": "Alle aktualisieren", "security_level": "Sicherheitsstufe", "security_summary": "Sicherheitsübersicht", "selecting_visible_item": "Auswahl der sichtbaren Elemente", "set_default_tab": "Als Standardregisterkarte festlegen", "tabs": {"cybersecurity": "Cybersicherheit", "general": "Allgemein", "wireless": "Wireless"}, "title": "Dashboard", "total_availability": "Device availability is below {{param}}%", "total_devices": "Gesamtzahl der Geräte", "unknown": "Unbekannt", "view_network_topology": "Netzwerktopologie anzeigen", "warning_devices": "Geräte mit Warnungen", "wireless_device_summary": "Überblick über kabellose Geräte"}, "DATABASE_BACKUP": {"database_name": "Name", "fail": "Das Backup der Datenbank ist fehlgeschlagen.", "success": "Ein Backup der Datenbank wird durchgeführt am: {{param1}}", "title": "Datenbanksicherung"}, "DEVICE_LOCATOR": {"stop_trigger_locator": "Stopp", "title": "Gerätelokalisierer", "trigger_locator": "Start", "trigger_locator_fail": "Der Gerätelokalisierer konnte nicht ausgelöst werden.", "trigger_locator_off": "Gerätelokalisierer ist ausgeschaltet", "trigger_locator_on": "Gerätelokalisierer ist eingeschaltet"}, "device_management": {"built_in_command_execution_process_is_running": "Be<PERSON><PERSON> kann nicht gesendet werden. Ein anderer Befehl wird ausgeführt. Versuchen Sie es später erneut.", "execute_fail": "Ausführung fehlgeschlagen", "limited_support": "Der Support ist begrenzt. Bitte überprüfen Sie das Benutzerhandbuch.", "select_device": "Gerät auswählen", "select_operation": "Operation auswählen"}, "DEVICE_PANEL": {"panel_status": "Panel Status", "panel_zoom_size": "Zoom für das Gerätepanel", "port": "Hafen"}, "DEVICE_POLLING_SETTING": {"consecutive_icmp_fail_trigger": "Die Auslösung des ICMP-Ereignisses \"nicht erreichbar\" ist fortlaufend fehlgeschlagen.", "consecutive_snmp_fail_trigger": "Die Auslösung des SNMP-Ereignisses \"nicht erreichbar\" ist fortlaufend fehlgeschlagen.", "icmp_polling_interval": "ICMP-Polling-Intervall", "snmp_polling_interval": "SNMP-Polling-Intervall", "title": "Polling-Einstellungen"}, "DEVICE_PROPERTIES": {"basic_property": {"alias": "<PERSON><PERSON>", "availability": "Verfügbarkeit", "bios_version": "BIOS-/Bootloader-Version", "cpu_loading": "CPU lädt gerade (%)", "cpu_loading_30_seconds": "CPU lädt 30 Sekunden (%)", "cpu_loading_300_seconds": "CPU lädt 300 Sekunden (%)", "cpu_loading_5_seconds": "CPU lädt 5 Sekunden (%)", "cpu_utilization_300_seconds": "CPU-Auslastung in den letzten 300 Sekunden (%)", "cpu_utilization_60_seconds": "CPU-Auslastung in den letzten 60 Sekunden (%)", "cpu_utilization_900_seconds": "CPU-Auslastung in den letzten 900 Sekunden (%)", "disk_utilization_unit": "Festplattenauslastung (%)", "fw_system_version": "Firmware-/System-Image-Version", "fw_version": "Firmware/System-Image-Version", "mac_address": "MAC-Adresse", "memory_usage": "Speichernutzung", "memory_usage_unit": "Speichernutzung (%)", "model_name": "Name des Modells", "os_type": "Betriebssystem", "power_comsumption": "Stromverbrauch (W)", "serial_number": "Seriennummer", "system_contact": "Systemkontakt", "system_description": "Systembeschreibung", "system_location": "Systemstandort", "system_name": "Systemname", "system_object_id": "Sysobjectid", "system_up_time": "System Up Time", "title": "Grundlegende Geräteeigenschaften"}, "cellular": {"cellular_carrier": "Cellular Carrier", "cellular_ip_address": "Cellular IP Address", "cellular_mode": "Cellular Mode", "cellular_signal": "Cellular Signal", "imei": "IMEI", "imsi": "IMSI", "title": "Cellular Information"}, "goose_table": {"app_id": "app_id", "gocb_name": "GoCB Name", "goose_address": "GOOSE-<PERSON><PERSON><PERSON>", "ied_name": "IED-Name", "port": "Ingress Port", "rx_counter": "RX-Zähler", "status": "Status", "tampered_port": "Manipulierter Port", "tampered_port_status": "Manipuliert am Anschluss {{ port }}", "title": "GOOSE-Prüfung", "type": "<PERSON><PERSON>", "vid": "VID"}, "ipsec": {"l2tp_status": "L2TP Status", "local_gateway": "Lokales Gateway", "local_subnet": "Lokales Subnetz", "name": "IPSec-Name", "phase_1_status": "Phase 1 Status", "phase_2_status": "Phase 2 Status", "remote_gateway": "Entferntes Gateway", "remote_subnet": "Entferntes Subnetz", "title": "IPsec-Status"}, "link": {"from": "<PERSON>", "port": "Hafen", "sfpTitle": "SFP Information", "speed": "Verbindungsgeschwindigkeit", "title": "Verbindungsinformationen", "to": "Nach"}, "management_interfaces": {"http_port": "HTTP-Port", "https_port": "HTTPS-Port", "profinet_enabled": "PROFINET aktiviert", "ssh_port": "SSH Port", "telnet_port": "Telnet Port", "title": "Management-Schnittstellen"}, "mms": {"title": "MMS-Eigenschaften"}, "modbus_device_property": {"model": "<PERSON><PERSON>", "revision": "Revision", "title": "Modbus-Geräteeigenschaften", "vendor": "Verkäufer"}, "not_selected": "<PERSON>ählen Si<PERSON> ein Modul aus, um Gerätedetails anzuzeigen", "other_device_properties": {"active_redundancy_protocol": "Aktives Redundanzprotokoll", "auto_ip_config": "Automatische IP-Konfiguration", "default_gateway": "Standard-Gateway", "dns_1_ip_address": "DNS 1 IP-Adresse", "dns_2_ip_address": "DNS 2 IP-Adresse", "ip_ad_ent_addr": "ipAdEntAddr", "ip_address": "IP-Adresse (mib)", "mac_address": "MAC-Adresse (mib)", "model_name": "Model Name", "monitor_current_mode": "Aktuellen Modus überwachen", "monitor_down_stream_rate": "Downstream-Rate überwachen", "monitor_snr": "SNR überwachen", "monitor_up_stream_rate": "Upstream-Rate überwachen", "netmask": "Netzmaske", "title": "Andere Geräteeigenschaften"}, "port": {"if_number": "ifNumber", "interface": "Bedieneroberfläche", "number_of_ports": "Anzahl der Anschlüsse", "poe_port_class": "PoE Port-Klasse", "poe_power_legacy_pd_detect": "PoE Leistung Legacy PD erkennen", "poe_power_output_mode": "PoE Stromausgabemodus", "title": "Port-Informationen"}, "power": {"power_1_status": "Energiestatus 1", "power_2_status": "Energiestatus 2", "title": "Energiestatus"}, "redundancy": {"active_redundancy_protocol": "Aktives Redundanzprotokoll", "dh": "Dual Homing", "iec_624393_redundancy_protocol": "IEC 62439-3 Redundanz-Protokoll", "rstp": "RSTP", "tc": "Turbo Chain", "title": "Redundanz", "trv2": "Turbo Ring V2"}, "selected_module": "Ausgewähltes Modul", "snmp": {"1st_trap_community": "1. Trap-Zugriffsberechtigung", "2nd_trap_server_community": "2. Trap-Zugriffsberechtigung", "inform_enabled": "Inform aktiviert", "inform_retries": "Inform erneuter Versuch", "inform_timeout": "Inform Timeout", "read_community": "Berechtigung für lesenden Zugriff", "title": "SNMP Informationen", "trap_server_address_1": "Trap-Serveradresse 1", "trap_server_address_2": "Trap-Serveradresse 2"}, "title": "Geräteeigenschaften", "vpn": {"from_ip": "VPN von IP", "local_connection_name": "Name der lokalen VPN-Verbindung", "remote_connection_name": "Name der Remote-VPN-Verbindung", "to_ip": "VPN zu IP"}, "wireless": {"channel_width": "Kanalbreite", "client_ip": "Client IP", "client_mac": "Client MAC", "client_RSSI": "Client-Signalstärke (dBm)", "rf_type": "RF-Typ", "ssid_index": "SSID-Index", "title": "Kabellos-Informationen", "vap_mgmt_encryption": "VAP-Management-Verschlüsselung", "vap_wpa_encrypt": "VAP WPA-Verschlüsselung", "vapAuthType": "VAP-Authentifizierungstyp"}}, "DEVICE_SETTING": {"advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alias": "<PERSON><PERSON>", "alias_input_invalid": "Bitte geben Si<PERSON> einen gültigen Gerätealias ein.", "apply_fail": "Geräteparameter können nicht eingestellt werden.", "apply_success": "Geräteparameter werden aktualisiert.", "availability_time_frame": "Zeitspanne für die Verfügbarkeitsberechnung", "get_parameter_fail": "Die Geräteparameter können nicht abgerufen werden.", "input_error_message": "Der neue Name muss im folgenden Bereich liegen:", "modify_device_alias": "Geräte-<PERSON><PERSON>", "password": "Passwort", "password_input_invalid": "Bitte geben Si<PERSON> ein gültiges Passwort ein.", "polling_interval": "Polling-Intervall", "polling_ip": "Polling-IP", "snmp_configuration": "SNMP-Konfiguration", "snmp_port_invalid": "Geben Sie bitte einen gültigen SNMP-Port ein", "title": "Geräteeinstellung", "use_global": "Benutzername und Passwort von globalen Zugriffsbereich verwenden", "use_global_device_settings": "Globale Geräteeinstellungen verwenden", "username": "<PERSON><PERSON><PERSON><PERSON>", "username_input_invalid": "Bitte geben Si<PERSON> einen gültigen Benutzernamen ein."}, "DEVICE": {"device_properties": "Geräteeigenschaften", "device_role": "Geräte-Rolle", "filter_device": "<PERSON><PERSON> zum Filtern von Gerät<PERSON>", "filter_register_device": "<PERSON><PERSON> zum Filtern von <PERSON>", "na": "N / A", "properties": {"availability": "Verfügbarkeit", "device_alias": "Ger<PERSON><PERSON><PERSON><PERSON><PERSON>", "device_ip": "Geräte-IP", "firmware_version": "Firmwareversion", "location": "Ort", "mac_address": "MAC-Adresse", "model_name": "<PERSON><PERSON><PERSON>", "mxsec_flag": "Sicherheits-Add-On", "severity": "Schwere"}, "registered_devices": "Eingetragen", "site_name": "Standortname", "title": "Geräteli<PERSON>", "unregistered_devices": "<PERSON>cht registriert"}, "DeviceDashboard": {"avg_erase_count": "Avg. <PERSON><PERSON>", "change_disk_hint": "Please change your disk", "chartTitle": {"60s_cpu": "CPU-Auslastung (letzte 60 Sekunden)", "connection": "Connection Status", "cpu": "CPU-Nutzung", "disk": "Disk Utilization", "memory": "Speichernutzung", "noiseFloor": "Noise Floor", "raid_mode": "RAID Mode", "signalStrength": "Signalstärke", "smart": "S.M.A.R.T.​", "snr": "SNR", "traffic": "Traffic-Last"}, "connected": "Verbunden", "current_status": "Aktueller Status:", "cycle_limitation": "Cycle Limitation", "icmp_not_support": "Das ICMP-Gerät unterstützt diese Funktion nicht.", "link_down_port": "Link Down Ports", "link_up_port": "Link Up Ports", "managed": "Verwaltet", "migrating_data": "Migrating Data", "no_raid": "No RAID", "normal": "Normal", "raid": "RAID", "rebuild": "Rebuild", "smart_hint": "(Self-Monitoring Analysis and Reporting Technology) represents device health status and lifespans information.", "unreachable": "{{ warningWording }} unerreichbar. MXview One erhält möglicherweise keine vollständigen Daten vom Gerät."}, "DIALOG": {"add_wifi_ssid": {"aes": "AES", "clear_all_existing_ssid": "Löschen Sie alle vorhandenen SSIDs", "eapol_version": "EAPOL-Version", "encryption": "Verschlüsselung", "open": "Open", "passphrase": "Passwort", "personal": "Personal", "protected_management_frame": "geschütztes Management-Framework", "rf_band": "RF-Band", "security": "Sicherheit", "ssid": "SSID", "title": "WLAN-SSID hinzufügen", "tkip_aes_mixed": "TKIP/AES-Hybrid", "wpa_mode": "WPA-Modus"}, "auto_layout": {"desc": "Möchten Sie das Layout wirklich automatisch erstellen lassen? (Das aktuelle Layout wird überschrieben.)", "title": "Automatisches Layout"}, "auto_topology": {"advanced": "Erweiterte Topologieanalyse", "advanced_desc": "*Erfordert zusätzliche Zeit.", "advanced_hint": "Verknüpfungslinks zwischen einem ICMP-Gerät und einem Gerät unterstützen entweder LLDP- oder Forwarding-Tabelle", "fail": "Die automatische Topologie ist fehlgeschlagen.", "link_check": "Strenger Link-Verifizierungsmodus", "link_check_hint": "<PERSON><PERSON> diese Option aktiviert ist, werden Verbindungen zwischen Geräten nur dann in der Topologie angezeigt, wenn die Geräte an beiden Enden die Informationen des anderen Geräts in ihrer LLDP-Tabelle haben.", "new_topology": "Neue Topologie", "new_topology_desc": "Vorhandene Verbindungen werden gelöscht.", "success": "Die automatische Topologie wird erfolgreich ausgeführt.", "title": "Automatische Topologie", "update_topology": "Topologie aktualisieren", "update_topology_desc": "Bestehende Verbindungen werden beibehalten, während neue Verbindungen hinzugefügt werden."}, "background_dialog": {"content": "Please set a background image first.\n The background image can be a floor plan or other image that represents the coverage area.", "set_now": "Jetzt einstellen", "title": "Hintergrundbild einstellen"}, "change_group": {"assign_to_group": "Zur Gruppe zu<PERSON>sen", "change_group_fail": "Die Geräte konnten nicht in die neue Gruppe verschoben werden.", "change_group_success": "Die Geräte werden in die neue Gruppe verschoben.", "current_group": "Aktuelle Gruppe", "ip": "IP-Adresse", "title": "Gruppe ändern"}, "change_wifi_channel": {"channel": "<PERSON><PERSON>", "channel_width": "Kanalbreite", "execute_button": "Ändern", "title": "WLAN-Kanal ändern"}, "create_group": {"assign_group": "Zur Gruppe zu<PERSON>sen", "create_group_fail": "Die Gruppe konnte nicht erstellt werden.", "create_group_success": "Die Gruppe wird erstellt.", "current_group": "Aktuelle Gruppe", "empty_group_name": "<PERSON><PERSON> müssen einen Namen für die Gruppe eingeben.", "group_desc": "Gruppenbeschreibung", "group_name": "Gruppenname", "parent_group": "Übergeordnete Gruppe", "title": "Gruppe er<PERSON>llen"}, "create_snapshot": {"execute_button": "erstellen", "title": "Schnappschuss er<PERSON>llen"}, "data_not_ready": {"content": "Die Gerätedaten sind noch nicht fertig, bitte versuchen Si<PERSON> es später noch einmal.", "title": "Bitte versuchen Sie es später noch einmal"}, "delete_account": {"delete_confirm_message": "Möchten Sie dieses Konto wirklich löschen?", "title": "Konto löschen"}, "delete_background": {"desc": "Möchten Sie das Hintergrundbild wirklich löschen?", "failed": "Der Hintergrund konnte nicht gelöscht werden.", "success": "Der Hintergrund wird gelöscht.", "title": "Hintergrund löschen"}, "delete_custom_opc": {"delete_confirm_message": "Sind <PERSON> sic<PERSON>, dass Sie dieses benutzerdefinierte OPC-Tag löschen möchten?", "title": "Benutzerdefiniertes OPC-Tag löschen"}, "delete_device": {"delete_wireless_client_alert": "Historische Daten von gelöschten drahtlosen Client-Geräten werden ebenfalls gelöscht und beeinträchtigen die Rückverfolgbarkeit der aufgeführten Funktionen. Möchten Si<PERSON> fortfahren?", "delete_wireless_client_alert_title": "Bestätigung der Löschung des/der Geräte(s)", "desc": "Möchten Sie dieses Gerät wirklich löschen?", "desc_multi": "Möchten Sie diese Geräte wirklich löschen?", "failed": "Das Löschen der Geräte ist fehlgeschlagen.", "success": "Das Gerät bzw. die Geräte werden erfolgreich gelöscht.", "title": "Gerät löschen"}, "delete_group": {"desc": "Möchten Sie diese Gruppe wirklich löschen?", "desc_multi": "Möchten Sie diese Gruppen wirklich löschen?", "failed": "Das Löschen der Gruppe ist fehlgeschlagen.", "success": "Die Gruppe(n) werden erfolgreich gelöscht.", "title": "Gruppe löschen"}, "delete_link": {"desc": "<PERSON>öchten Sie diese Verbindung wirklich löschen?", "desc_multi": "Möchten Sie diese Verbindungen wirklich löschen?", "failed": "Das Löschen der Verbindung ist fehlgeschlagen.", "success": "Die Verbindung(en) werden erfolgreich gelöscht.", "title": "Verbindung löschen"}, "delete_objects": {"desc": "Möchten Sie alle ausgewählten Objekte wirklich löschen?", "failed": "Die ausgewählten Objekte konnten nicht gelöscht werden.", "success": "Die ausgewählten Objekte werden erfolgreich gelöscht.", "title": "Objekte löschen"}, "delete_site": {"desc": "Möchten Sie diesen Standort wirklich löschen?", "failed": "Das Löschen des Standorts ist fehlgeschlagen.", "success": "Der Standort wird erfolgreich gelöscht.", "title": "Standort löschen"}, "device_settings": {"fail": "Setting Link Budget Parameters (Per-device) Failed", "rx_antenna_gain": "RX Antenna Gain", "rx_cable_loss": "RX Cable Loss", "success": "Setting Link Budget Parameters (Per-device) Success", "title": "Link Budget Parameters (Per-device)", "tx_antenna_gain": "TX Antenna Gain", "tx_cable_loss": "TX Cable Loss"}, "disable_unsecured": {"execute_button": "Deaktivieren Sie HTTP und Telnet", "title": "Deaktivieren Sie unsichere HTTP- und Telnet-Konsolen"}, "disable_unused": {"execute_button": "Deaktivieren Sie nicht verwendete Ports", "keep_port_available": "Halten Sie einen vorübergehend unterbrochenen Port aktiv", "title": "Deaktivieren Sie nicht verwendete Ethernet- und Glasfaser-Ports"}, "discovery_device": {"another_discovery_error": "Eine weitere Suche ist im Gang.", "discovering": "Geräte werden erkannt.", "discovery_finish": "Die Erkennung der Geräte ist abgeschlossen.", "error": "Geräte können nicht erkannt werden.", "title": "Geräteentdeckung"}, "dynamic_mac_sticky": {"address_limit": "Adressbeschränkungen", "alias": "<PERSON><PERSON>", "mac_sticky": "Sticky MAC", "mac_sticky_settings": "Sticky MAC-Einstellungen", "packet_drop": "Pakete verwerfen", "port": "Port", "port_duplicated": "Der Port wurde bereits konfiguriert.", "port_format_not_equal": "Die Modelle müssen das gleiche Portformat und die gleiche Portanzahl haben.", "port_selection_guide": "Portnamenschlüssel", "port_shutdown": "Port schließen", "security_action": "<PERSON><PERSON><PERSON>", "title": "Dynamischer Sticky MAC"}, "export_config": {"config_center": "Konfigurationszentrum", "config_file": "Konfigurationsdatei", "export": "Exportieren", "fail": "Die Gerätekonfiguration kann nicht exportiert werden.", "hint": "* Please make sure the username and password for this device are correctly set in \"Advanced Settings\"", "success": "Die Gerätekonfiguration wird erfolgreich exportiert.", "title": "Konfiguration exportieren"}, "goose": {"how_to_resolve": "Wie kann man das <PERSON> beheben?", "import_scd_tooltip": "Bitte importieren Sie eine SCD-Datei, um GOOSE-Meldungen anzuzeigen. Klicken Sie auf Energie > SCD importieren.", "ip_port": "Anschluss {{ port }} von {{ ip }}", "open_web_console": "Web-<PERSON><PERSON><PERSON>", "port_tampered_msg": "GOOSE Port Tampered verursacht durch", "port_tampered_title": "GOOSE Port Manipulation Problem beheben", "reset_goose": "Manipulierte GOOSE-Meldung zurücksetzen", "reset_goose_desc": "GOOSE-Nachricht: {{ cbName }}/{{ appId }}/{{ mac }}", "reset_goose_title": "Sind <PERSON> sicher, dass Sie alle Instanzen dieser manipulierten GOOSE-Meldung zurücksetzen möchten?", "resolve_goose_tampered_desc_1": "Versuchen Sie folgende Schritte, um das Problem GOOSE Port Tampered zu beheben", "resolve_goose_tampered_desc_2": "1. Überprüfen Sie die Einstellungen des/der IED(s)", "resolve_goose_tampered_desc_3": "Vergewissern <PERSON> sich, dass die GOOSE Publish/Subscribe-Nachrichten des IEDs korrekt eingestellt sind.", "resolve_goose_tampered_desc_4": "2. Prüfen Sie den Port-Status", "resolve_goose_tampered_desc_5": "Bitte prüfen Sie den Port {{ port }}-Status von {{ ip }}.", "resolve_goose_tampered_desc_6": "2. Überpr<PERSON><PERSON>, ob alle Geräte autorisiert sind.", "resolve_goose_tampered_desc_7": "Überprüfen Sie, ob es im Netzwerk nicht autorisierte Geräte gibt.", "resolve_goose_tampered_desc_8": "Versuchen Sie die folgenden Schritte, um das Problem der manipulierten GOOSE SA zu beheben", "resolve_goose_timeout_desc_1": "Versuchen Sie diese Schritte, um GOOSE-Timeout-Probleme zu beheben.", "resolve_goose_timeout_desc_10": "Funktioniert es immer noch nicht?", "resolve_goose_timeout_desc_11": "Entfernen Sie das SFP-Modul und installieren Sie es erneut.", "resolve_goose_timeout_desc_12_1": "Wenn Sie technische Fragen haben, wenden Sie sich bitte zu<PERSON>t an Ihren Vertriebspartner.", "resolve_goose_timeout_desc_12_2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "resolve_goose_timeout_desc_12_3": "first", "resolve_goose_timeout_desc_13_1": "Kontakt", "resolve_goose_timeout_desc_13_2": "Moxa Technischer Support", "resolve_goose_timeout_desc_13_3": "wenn Sie noch weitere Unterstützung benötigen.", "resolve_goose_timeout_desc_2": "1. Überprüfen Sie die IED(s)-Einstellungen", "resolve_goose_timeout_desc_3": "Vergewissern <PERSON> sich, dass die Einstellungen für das Veröffentlichen/Abonnieren von GOOSE-Nachrichten des IED korrekt sind.", "resolve_goose_timeout_desc_4": "2. <PERSON><PERSON><PERSON>, dass der Port NICHT unterbrochen ist", "resolve_goose_timeout_desc_5": "<PERSON><PERSON><PERSON><PERSON>, ob der Port eines jeden Geräts im GOOSE-Fluss ({{ cbName }}) NICHT ausgefallen ist.", "resolve_goose_timeout_desc_6": "3. <PERSON><PERSON><PERSON>, dass der Port keinen Tx/Rx-<PERSON><PERSON> hat", "resolve_goose_timeout_desc_7": "<PERSON>licken Sie auf einen Link, wählen Sie Link Traffic, um den Abschnitt Packet Error Rate zu sehen. <PERSON><PERSON><PERSON>, dass der Port keine Fehler aufweist.", "resolve_goose_timeout_desc_8": "4. <PERSON><PERSON><PERSON><PERSON>, ob die Glasfaser-Ports einen bestimmten Schwellenwert überschreiten", "resolve_goose_timeout_desc_9": "Wählen Sie die Schaltfläche \"SFP\" > \"SFP-Liste\". Vergewissern Si<PERSON> sich, dass die Ports einen bestimmten Schwellenwert NICHT überschreiten.", "sa_tampered_msg": "GOOSE SA verfälscht", "sa_tampered_name_msg": "GOOSE-Nachricht ({{ cbName }}/{{ appId }}/{{ mac }}) steht im Konflikt mit einer anderen GOOSE-Quelladresse.", "sa_tampered_title": "GOOSE SA Manipulationsproblem beheben", "tampered": "<PERSON><PERSON><PERSON><PERSON>", "timeout": "Zeitüberschreitung", "timeout_msg": "GOOSE-Timeout verursacht durch", "timeout_title": "GOOSE-Timeout-<PERSON> beheben"}, "import_config": {"config_center": "Konfigurationszentrum", "config_file": "Konfigurationsdatei", "config_file_error": "MXview One unterstützt nur Dateien im .ini-Format.", "config_file_size_error": "Maximale Dateigröße ist 3 MB", "fail": "Die Gerätekonfiguration kann nicht importiert werden.", "hint": "* Please make sure the username and password for this device are correctly set in \"Advanced Settings\"", "import": "Importieren", "success": "Die Gerätekonfiguration wird erfolgreich importiert.", "title": "Konfiguration importieren"}, "link_traffic": {"date": "Datum", "from": "<PERSON>", "packet_error_rate_title": "Paketfehlerrate", "port_traffic_title": "Port-Datenverkehr", "time": "Time", "to": "Bis", "utilization": "Auslastung", "value": "Value"}, "mac_sticky_switch": {"mac_sticky": "Sticky MAC", "title": "Sticky MAC ein/aus"}, "maintain_group": {"change_icon": "Change group icon", "create": "<PERSON><PERSON><PERSON><PERSON>", "create_group_fail": "Die Gruppe konnte nicht erstellt werden.", "create_group_success": "Die Gruppe wird erstellt.", "delete": "Löschen", "delete_group_fail": "Die Gruppe konnte nicht gelöscht werden.", "delete_group_success": "Die Gruppe wird gelöscht.", "empty_group_name": "<PERSON><PERSON> müssen einen Namen für die Gruppe eingeben.", "group_desc": "Gruppenbeschreibung", "group_name": "Gruppenname", "modify_group_fail": "Die Gruppe konnte nicht geändert werden.", "modify_group_success": "Die Gruppe wird geändert.", "reset_icon": "Reset to default image", "title": "Gruppenpflege"}, "ping": {"failed": "Pingen fehlgeschlagen", "title": "<PERSON><PERSON>"}, "policy_profile": {"delete_msg": "Möchten Sie die ausgewählten Profile wirklich löschen?", "delete_title": "Profil(e) löschen"}, "reboot": {"execute_button": "<PERSON>eu starten", "reboot_sequence": "Sequenz neu starten", "title": "<PERSON>eu starten", "unable_determine_reboot_sequence_hint": "Die Neustartreihenfolge kann nicht ermittelt werden. <PERSON><PERSON><PERSON>, dass die Topologie einen Computer enthält, auf dem MXview One installiert ist, und versuchen Sie es erneut."}, "relearn_dynamic_mac_sticky": {"execute_button": "nochmal studieren", "title": "Lernen Sie den dynamischen Sticky MAC neu"}, "restore_to_create_snapshot": {"execute_button": "genesen", "title": "Aus Snapshot wiederherstellen"}, "scd": {"import_failed_desc": "Bitte korrigieren Sie die SCD-Datei und versuchen Sie erneut zu importieren.", "import_failed_title": "Probleme beim Importieren von SCD", "import_succeed_desc": "GOOSE-Nachrichten und Flussdesign wurden erfolgreich in die Netzwerktopologie eingebaut.", "import_succeed_title": "Import abgeschlossen!", "missing": "<PERSON><PERSON><PERSON><PERSON>", "missing_device": "Folgende(s) Gerät(e) kann/können nicht gefunden werden", "missing_device_desc": "<PERSON><PERSON><PERSON> Si<PERSON> sich die Liste der Probleme unten an.", "scd_file_error": "MXview One unterstützt nur das .scd-Format", "scd_file_size_error": "Maximale Dateigröße ist 100 MB", "select_file": "<PERSON>i ausw<PERSON>hlen", "tag": "Tag", "topology_change_desc_1": "Versuchen Sie die folgenden Schritte, um Probleme zu beheben.", "topology_change_desc_2": "1. <PERSON><PERSON><PERSON> Si<PERSON> das/die fehlende(n) Gerät(e) hinzu", "topology_change_desc_3": "<PERSON><PERSON>hlen Sie die Schaltfläche \"Bearbeiten\" > \"Ger<PERSON> hinzufügen\".", "topology_change_desc_4": "2. Importieren Sie die SCD-Datei erneut", "topology_change_desc_5": "Wählen Sie die Schaltfläche \"Power\" > \"SCD importieren\".", "topology_change_title": "Probleme beim Ändern der Topologie", "visualize_goose_messages": "Um GOOSE-Nachrichten zwischen IEDs zu visualisieren, müssen Si<PERSON> zunächst eine SCD-Datei importieren.", "visualize_goose_messages_title": "Visualisieren von GOOSE-Meldungen"}, "set_background": {"browse": "durchsuchen", "desc": "<PERSON>ild hierher ziehen oder ", "desc1": " zur Einstellung des Hintergrunds", "failed": "Der Hintergrund konnte nicht eingestellt werden.", "image": "Bild", "image_alpha": "Alpha", "image_error": "Die ausgewählte Datei war kein Bild", "image_position": "Position", "image_saturation": "Sättigung", "image_x": "X", "image_y": "Y", "preview": "Vorschau", "size_error": "Die Bildgröße muss zwischen 1 KB und 20 MB liegen.", "success": "Der Hintergrund wird aktualisiert.", "title": "Hintergrund einstellen", "topology_size": "Topologie Größe"}, "set_document": {"current_filename": "Aktueller Dateiname", "delete": "Dokument löschen", "error": "MXview One unterstützt nur das PDF-Format.", "failed": "Das Dokument konnte nicht eingestellt werden.", "file_size_error": "Die maximale Größe des Dokuments beträgt 20MB. Nur PDF.", "open": "Dokument öffnen", "set": "Dokument einstellen", "success": "Gerätedokument ist aktualisiert.", "title": "Dokument einstellen", "upload": "Eine Datei zum Hochladen auswählen"}, "set_port_label": {"error": "<PERSON><PERSON>", "failed": "Die Port-Bezeichnung konnte nicht eingestellt werden.", "from": "Von:", "success": "Die Port-Bezeichnung wird aktualisiert.", "title": "Port-Bezeichnung einstellen", "to": "Bis:", "use_custom_label": "Benutzerdefinierte Bezeichnung verwenden", "vpn_link_desc": "Die Bezeichnung einer VPN-Verbindung ist nicht konfigurierbar."}, "set_scale": {"error": "Parameter error", "fail": "Setting Scale Failed", "success": "Setting Scale Success", "title": "Set Scale"}, "severity_threshold": {"bandwidth_input_invalid": "<PERSON>te geben Sie eine ganze <PERSON>ahl von 0-100 ein.", "bandwidth_utilization": "Bandbreitenauslastung", "critical": "<PERSON><PERSON><PERSON>", "failed": "Die Einstellung des Schweregrad-Schwellenwerts ist fehlgeschlagen.", "information": "Information", "over": "Überschreitung", "packet_error_rate": "Paketfehlerrate", "sfp_rx_over": "SFP RX Über", "sfp_rx_under": "SFP RX Unter", "sfp_temp_over": "SFP Übertemperatur", "sfp_threshold": "SFP Schwellenwert", "sfp_tx_over": "SFP TX Über", "sfp_tx_under": "SFP TX Unter", "sfp_vol_over": "SFP Überspannung", "sfp_vol_under": "SFP Unterspannung", "success": "Der Schweregrad-Schwellenwert wird aktualisiert.", "title": "Schweregrad-Schwellenwert", "under": "Unterschreitung", "warning": "<PERSON><PERSON><PERSON>"}, "sfp_info": {"date": "Datum", "dateError": "Ungültiger Datumsbereich", "from": "<PERSON>", "port": "Hafen", "sfpRxPower": "SFP RX", "sfpRxPower_label": "RX", "sfpRxPower_scale": " dBm", "sfpTemperature": "SFP Temperatur", "sfpTemperature_label": "Temp.", "sfpTemperature_scale": " °C", "sfpTxPower": "SFP TX", "sfpTxPower_label": "TX", "sfpTxPower_scale": " dBm", "sfpVoltage": "SFP Spannung", "sfpVoltage_label": "Spannung", "sfpVoltage_scale": " V", "time": "Zeit", "title": "SFP Information", "to": "Bis", "value": "Wert"}, "sfp_sync": {"confirm_desc": "Sind <PERSON> sicher, dass Sie den SFP-Schwellenwert mit dem Gerät synchronisieren möchten?", "content": "Sie können den SFP-Schwellenwert mit dem MOXA-Switch synchronisieren. Nach der Synchronisierung werden die Temperatur, Sendeleistung und Empfangsleistung der Glasfaserprüfung mit dem SFP-Schwellenwert der einzelnen Links synchronisiert.", "failed": "Synchronisierung des SFP-Schwellenwerts fehlgeschlagen", "hint": "* Um den SFP-Schwellenwert zu überprüfen, klicken Sie auf eine Verbindung und wählen Sie Schweregrad-Schwellenwert > SFP-Schwellenwert.", "success": "Sync SFP-Schwellenwert Erfolg", "title": "SFP-Schwellenwert vom Gerät synchronisieren"}, "wireless_settings": {"fail": "Setting Link Budget Parameters (General) Failed", "rxSensitivityHigh": "RX Sensitivity High", "rxSensitivityLow": "RX Sensitivity Low", "rxSensitivityMedium": "RX Sensitivity Medium", "sr": "Reserved Safety Factor", "success": "Setting Link Budget Parameters (General) Success", "title": "Link Budget Parameters (General)"}}, "EMBED_WIDGET": {"click_preview": "<PERSON><PERSON><PERSON>", "copied_to_clipboard": "Link in die Zwischenablage kopiert", "copy_link": "<PERSON>", "custom": "Anpassung", "desc": "In eine beliebige HTML-Seite einfügen", "embed": "Einbetten", "height": "<PERSON><PERSON><PERSON>", "layout_1": "Layout 1", "layout_2": "Layout 2", "layout_3": "Layout 3", "layout_4": "Layout 4", "link": "Verbindung", "no_api_key": "Sie müssen zuerst einen API-Key erstellen.", "preview": "Vorschau", "recent_event": "<PERSON><PERSON><PERSON><PERSON>", "select_api_key": "API-Key auswählen", "select_layout": "Layout auswählen", "title": "Eingebettetes Web-Widget", "topology": "Topologie", "topology_recent_event": "Topologie und neuestes Ereignis", "width": "Breite"}, "error_handler": {"error_session_expired_dialog": "Die Sitzung ist abgelaufen. Das System leitet Sie zur Login-Seite weiter."}, "ERROR_MESSAGE": {"get_data_fail": "Datenabruf feh<PERSON>schlagen", "input_invalid_char": "Bitte geben Si<PERSON> einen gültigen Namen ein.", "input_invalid_characters": "Dieses Feld darf keines der folgenden Zeichen enthalten: #%&*:<>?|{}\\\"/", "input_invalid_contact": "<PERSON><PERSON><PERSON> Si<PERSON> bitte einen gültigen Kontakt ein", "input_invalid_email": "Bitte geben Si<PERSON> eine gültige E-Mail ein", "input_invalid_location": "<PERSON><PERSON><PERSON> Si<PERSON> bitte einen gültigen Standort ein", "input_invalid_mac": "Ungültige MAC-Adresse", "input_invalid_password_characters": "Dieses Feld darf keines der folgenden Zeichen enthalten '\\\"/`", "input_invalid_script": "Dieses Feld darf keines der folgenden Zeichen enthalten: #%&amp;*{}|:&quot;&lt;&gt;?/\\", "input_ip_invalid": "Bitte geben Si<PERSON> eine gültige IP-Adresse ein.", "input_required": "Dieser Wert ist erforderlich", "non_restricted_ascii": "ASCII-<PERSON><PERSON><PERSON>, außer ' \\\" ` \\\\"}, "errors": {"A001": "Fehlendes Pflich<PERSON>feld", "A002": "Fehlende Abfragezeichenfolge", "A003": "Falsches Format", "D001": "Maximale Anzahl an Lizenzen erreicht", "D002": "Das Gerät kann nicht gefunden werden", "D003": "Das Gerät muss online sein", "D004": "Das Gerät wurde gelöscht", "F001": "Die Firmware kann nicht gefunden werden", "F002": "Diese Firmware existiert bereits", "F003": "Maximale Anzahl an Firmwaredateien erreicht", "G001": "Diese Gruppe existiert bereits", "G002": "Die Gruppe kann nicht gefunden werden", "G003": "Die Standardgruppe kann nicht geändert werden", "G004": "Administratorbenutzer können keinen Gruppen zugewiesen werden", "I001": "<PERSON><PERSON> existiert bereits", "I002": "Die Schnittstelle kann nicht gefunden werden", "I003": "Die Standardgruppe kann nicht geändert werden", "I004": "<PERSON><PERSON> diese Schnittstelle wird durch ein Sicherheitsprofil verwiesen.", "L001": "Ungültiger Aktivierungscode", "L002": "Die Lizenz ist abgelaufen", "L003": "Doppelter Aktivierungscode", "L004": "Maximale Anzahl an Knoten erreicht", "L005": "Das Gerät kann nicht aktiviert oder deaktiviert werden", "L006": "Ungültige Startzeit", "L007": "Ungültige Startzeit für die neue Typlizenz", "O001": "Dieses Objekt existiert bereits", "O002": "Das Objekt kann nicht gefunden werden", "O003": "<PERSON><PERSON> dieses Objekt wird durch ein Sicherheitsprofil verwiesen.", "P001": "Das Paket kann nicht gefunden werden", "P002": "Dieses Paket existiert bereits", "P003": "Maximale Anzahl Pakete erreicht", "P004": "Nicht unterstützte Version", "S001": "DB-Update-<PERSON><PERSON>", "SP001": "Dieses Profil existiert bereits", "SP002": "Das Profil kann nicht gefunden werden", "T001": "Nicht autorisiertes Token", "T002": "Token abgelaufen", "T003": "Ungültiger Token", "U001": "Erlau<PERSON><PERSON> verweigert", "U002": "Dieser Benutzername existiert bereits", "U003": "Der Benutzer kann nicht gefunden werden", "U004": "Die Rolle kann nicht gefunden werden", "U005": "ungültiger Benutzername oder Passwort", "U006": "Das Passwort erfüllt nicht die Mindestlänge", "U007": "Das Passwort überschreitet die maximale Länge", "U008": "Das Passwort darf nicht mit dem Benutzernamen identisch sein.", "U009": "Muss mindestens einen Großbuchstaben enthalten", "U010": "Muss mindestens einen Kleinbuchstaben enthalten", "U011": "Muss mindestens eine Ziffer enthalten", "U012": "Muss mindestens ein nicht-alphanumerisches Zeichen enthalten", "U013": "Das Passwort darf nicht mit dem vorherigen Passwort identisch sein.", "U014": "ungültiger Benutzername", "Unknown": "Unbekannt"}, "EULA": {"agree_hint": "Bitte stimmen Sie der Vereinbarung zur Nutzung von MXview One zu.", "eula_title": "EULA"}, "EVENT_MENU": {"ack": "<PERSON><PERSON><PERSON><PERSON>", "ack_all": "<PERSON>e quittieren", "filter_event": "Filterbedingungen"}, "EVENT": {"ack_all_events": {"ack_all_event_success": "<PERSON>s wurden alle Ereignisse quittiert.", "button_ack_hint": "Ack Selected Event(s)", "button_hint": "Quttieren Sie alle Ereignisse", "confirm_message": "Alle Ereignisse werden quittiert! Möchten Sie diesen Prozess fortsetzen?", "unable_ack_all_event": "<PERSON>s können nicht alle Ereignisse quittiert werden"}, "ack": {"ack_fail": "Das Ereignis kann nicht quittiert werden.", "acked": "<PERSON><PERSON><PERSON><PERSON>", "any": "<PERSON><PERSON><PERSON>", "unacked": "<PERSON><PERSON>"}, "all_event": "Alle Ereignisse", "all_group": "Alle Gruppen", "all_site": "<PERSON><PERSON> Standorte", "clear_all_events": {"button_hint": "Alle Ereignisse löschen", "clear_all_event_success": "Alle Ereignisse wurden gelöscht.", "confirm_message": "Alle Ereignisse werden gelöscht! Möchten Sie diesen Vorgang fortsetzen?", "confirm_message_network": "Alle Netzwerk- und Geräteereignisse werden gelöscht. Möchten Sie diesen Vorgang wirklich fortsetzen?", "confirm_message_system": "Alle Systemereignisse werden gelöscht. Möchten Sie diesen Vorgang wirklich fortsetzen?", "unable_clear_all_event": "<PERSON>s können nicht alle Ereignisse gelöscht werden."}, "custom_events": {"activate": "Benutzerdefiniertes Ereignis aktivieren", "add_custom_event": "Benutzerdefiniertes Ereignis hi<PERSON>", "all": "Alle", "all_devices": "Alle Geräte", "apply_fail": "Das benutzerdefinierte Ereignis kann nicht eingestellt werden.", "apply_success": "Das benutzerdefinierte Ereignis wird hinzugefügt.", "below": "Unter", "condition": "Bedingung", "condition_operator": "Bedingungsoperator", "condition_value": "Bedingungswert", "consecutive_updates": "aufeinanderfolgende Abfragen", "delete_fail": "Das benutzerdefinierte Ereignis kann nicht gelöscht werden.", "delete_success": "<PERSON><PERSON> nicht mehr anzeigen", "description": "Beschreibung", "device_properties": "Geräteeigenschaften", "devices": "<PERSON><PERSON><PERSON><PERSON>", "duration": "<PERSON><PERSON>", "equal": "<PERSON><PERSON><PERSON>", "event_name": "Ereignisname", "filter_custom_event": "Typ zum Filtern benutzerdefinierter Ereignisse", "get_fail": "Benutzerdefinierte Ereignisse können nicht abgerufen werden", "helper": "Benutzerdefinierte Ereignisse hinzufügen und diesen Geräte zuordnen", "not_equal": "<PERSON><PERSON> gleich", "over": "<PERSON><PERSON>", "recovery_description": "Beschreibung der Wiederherstellung", "register": "Registrieren", "register_devices": "Geräte registrieren", "search": "<PERSON><PERSON>", "severity": "Schwere", "title": "Benutzerdefiniertes <PERSON>ignis", "update_custom_event": "Benutzerdefiniertes Ereignis aktualisieren", "update_fail": "Das benutzerdefinierte Ereignis kann nicht aktualisiert werden.", "update_success": "Das benutzerdefinierte Ereignis wird aktualisiert."}, "event_description": {"abc_attache_warning": "Das ABC-Gerät ist angeschlossen.", "abc_auto_import_warning": "Der automatische Import der Konfiguration ist fehlgeschlagen.", "abc_config_warning": "Der Export der Konfigurationsdatei ist fehlgeschlagen.", "abc_detache_warning": "Das ABC-Gerät wird getrennt.", "abc_log_warning": "Der Export der Protokolldatei ist fehlgeschlagen.", "abc_space_warning": "<PERSON><PERSON> wenig Speicherplatz", "abc_unauthorized_warning": "Nicht autorisierte Medien werden erkannt.", "abc_unknow_warning": "Unbekannt", "abc02_warning": "USB-Ereignis: {{param}}", "account_audit_baseline_failed": "Die Kontoprüfung konnte nicht abgeschlossen werden. Daten konnten nicht von allen Geräten abgerufen werden.", "account_audit_baseline_match": "Die Kontoprüfung wurde erfolgreich abgeschlossen. Das Ergebnis entspricht dem Ausgangswert.", "account_audit_failed": "Die Kontoprüfung konnte nicht abgeschlossen werden. Daten von Geräten unter {{ip}} konnten nicht abgerufen werden.", "account_audit_match": "Die Kontoprüfung wurde erfolgreich abgeschlossen. Das Ergebnis entspricht dem Ausgangswert.", "account_audit_mismatch": "Die Kontenprüfung wurde erfolgreich abgeschlossen. Das Ergebnis entspricht nicht der Baseline.", "account_audit_unable_retrieve_device": "Die Kontoprüfung konnte nicht abgeschlossen werden. Daten konnten nicht von allen Geräten abgerufen werden.", "accountAudit1": "Die Kontoprüfung entspricht nicht der Baseline", "accountAudit2": "Die Kontoprüfung konnte nicht abgeschlossen werden", "all_event_clear": "Alle Ereignisse werden gelöscht.", "auth_fail": "Die Authentifizierung der Geräteanmeldung ist fehlgeschlagen.", "availability_down": "Geräteverfügbarkeit unter Schwellenwert", "availability_down_recovery": "Geräteverfügbarkeit unter Schwellenwert \nWiederherstellung", "background_scan_found": "Neues Gerät gefunden (IP: {{ip}}).", "cli_button_event_all_failed": "{{user}} von IP: {{sourceIP}} hat die Schaltfläche {{cliName}} ausgeführt. Das Ausführungsergebnis ist „Alle fehlgeschlagen“.", "cli_button_event_all_finished": "{{user}} von IP: {{sourceIP}} hat die Schaltfläche {{cliName}} ausgeführt. Das Ausführungsergebnis ist „Alles fertig“.", "cli_button_event_all_partially_finished": "{{user}} von IP: {{sourceIP}} hat die Schaltfläche ausgeführt: {{cliName}}. Das Ausführungsergebnis ist „Teilweise abgeschlossen“.", "cli_button_event_start": "{{user}} von IP: {{sourceIP}} hat die Schaltfläche ausgeführt: {{cliName}}.", "cli_saved_script_event": "{{user}} von IP: {{sourceIP}} hat den CLI-Befehl ausgeführt: {{cliName}}", "cli_script_event": "{{user}} von IP: {{sourceIP}} startet mit der Ausführung der CLI.", "cold_start": "Kaltstart", "custom_event_detail": "{{param1}}. <PERSON>hwellenwert={{param2}}, Wert={{param3}}. {{param4}}", "custom_event_recovery": "Benutzerdefiniertes Ereignis wiederhergestellt", "custom_event_recovery_detail": "{{param1}} wird wiederhergestellt. Schwellenwert={{param2}}, Wert={{param3}}. {{param4}}", "custom_event_trigger": "Benutzerdefiniertes Ereignis au<PERSON>", "cybersecurity_event_trigger": "Veranstaltung zur Cybersicherheit", "ddos_under_attack": "Ein sicherer Router ist unter DDoS-Attacke", "ddos_under_attack_recovery": "Ein sicherer Router ist nicht mehr unter DDoS-Attacke", "device_configuration_change": "Die Gerätekonfiguration hat sich geändert.", "device_firmware_upgrade": "Die Geräte-Firmware wird aktualisiert.", "device_infom_receive": "Geräteinformation empfangen", "device_lockdown_violation": "Gerätesperrungsverletzung", "device_power_down": "{{param}} ausschalten", "device_power_down_off_to_on": "PWR {{param}} Aus > Ein.", "device_power_on": "{{param}} e<PERSON><PERSON><PERSON>", "device_power_on_to_off": "PWR {{param}} Ein > Aus.", "device_snmp_reachable": "Geräte-SNMP erreichbar", "device_snmp_unreachable": "Geräte-SNMP unerreichbar", "di_off": "DI {{param}}} aus", "di_on": "DI {{param}}} an", "disk_space_not_enough": "Der verfügbare Festplattenspeicher ist kleiner als der Schwellenwert.", "event_config_import": "Eine neue Konfigurationsdatei wird importiert", "event_ddos_attack": "DoS-<PERSON><PERSON> er<PERSON>", "event_ddos_attack_recovery": "Wiederherstellung sicherer Router nach DDoS-Attacke", "event_dying_gasp": "Die Stromversorgung des Systems ist unterbrochen. Das Gerät wird über seinen Kondensator versorgt", "event_eps_is_off": "Externe PoE-Stromquelle ist ausgeschaltet", "event_eps_is_on": "Externe PoE-Stromquelle ist eingeschaltet", "event_firewall_attack": "Sicherer Router unter Firewall-Attacke", "event_firewall_attack_recovery": "Wiederherstellung sicherer Router nach Firewall-Attacke", "event_ieee_lldp_table_change": "{{trapDetail}}", "event_ieee_rstp_root_change": "Die neue RSTP-<PERSON><PERSON><PERSON> wurde in der Topologie ausgewählt", "event_ieee_rstp_topology_change": "Die Topologie wurde von RSTP geändert", "event_linux_account_setting_change": "Kontoeinstellungen des {{username}} wurden geändert", "event_linux_config_change": "Der {{modules}} Konfiguration wurde geändert von {{username}}.", "event_linux_config_import": "Die Konfiguration wurde {{successOrFail}} von {{username}} importiert", "event_linux_config_import_failed": "Misserfolg", "event_linux_config_import_succeed": "Erfolg", "event_linux_coupling_change": "Der Status des Turbo Ring v2-Kopplungspfads hat sich geändert", "event_linux_di_off": "Der Digitaleingang {{index}} wurde ausgeschaltet", "event_linux_di_on": "Der Digitaleingang {{index}} wurde eingeschaltet", "event_linux_dotlx_auth_fail": "Die 802.1X-Authentifizierung an Port {{portIndex}} mit {{reason}} ist fehlgeschlagen", "event_linux_dual_homing_change": "Der Dual Homing Path hat umgeschaltet", "event_linux_log_capacity_threshold": "Die Anzahl der Ereignisprotokolleinträge {{value)}} hat den Schwellenwert erreicht", "event_linux_low_input_voltage": "Die Eingangsspannung des Netzteils liegt unter der Schwelle", "event_linux_master_change": "Ring-Master {{Index}} hat sich geändert", "event_linux_master_mismatch": "Die Ring-Mastereinstellung {{Index}} stimmt nicht überein", "event_linux_over_power_budget_limit": "Die verbrauchte Leistung {{value}} aller PDs hat die maximale Eingangsleistung {{threshold}} überschritten", "event_linux_password_change": "Das Passwort des {{username}} wurde geändert", "event_linux_pd_no_response": "Das PoE-Port-Gerät {{portIndex}} reagiert nicht auf die PD-Fehlerprüfung.", "event_linux_pd_over_current": "Der Strom des PoE-Port {{portIndex}} hat das Sicherheitslimit überschritten.", "event_linux_pd_power_off": "PoE-Port {{portIndex}} PD ausschalten", "event_linux_pd_power_on": "PoE-Port {{portIndex}} PD einschalten", "event_linux_port_recovery_by_ratelimit": "Der Port {{portIndex}} hat sich durch das Ratenlimit erholt", "event_linux_port_shutdown_by_ratelimit": "Hafen {{portIndex}} ist ges<PERSON><PERSON>, da der Datenverkehr die Geschwindigkeitsbegrenzung überschreitet.", "event_linux_port_shutdown_by_security": "Der Port {{portIndex}} wur<PERSON> von der Port-Sicherheit heruntergefahren", "event_linux_power_detection_fail": "Das PoE-Port-Gerät{{portIndex}} ist ein {{devicetype}}. Bitte {{suggestion}}.", "event_linux_power_detection_fail_devietype_na": "nicht verfügbar", "event_linux_power_detection_fail_devietype_noPresent": "keine vorhanden", "event_linux_power_detection_fail_devietype_unknown": "unbekannt", "event_linux_power_detection_fail_suggestion_disable_POE": "POE deaktivieren", "event_linux_power_detection_fail_suggestion_enable_legacy": "Altgerät aktivieren", "event_linux_power_detection_fail_suggestion_enable_POE": "POE aktivieren", "event_linux_power_detection_fail_suggestion_no": "kein Vorschlag", "event_linux_power_detection_fail_suggestion_rais_EPS_voltage": "EPS-Spannung erhöhen", "event_linux_power_detection_fail_suggestion_select_auto": "Auto auswählen", "event_linux_power_detection_fail_suggestion_select_force": "Kraft auswählen", "event_linux_power_detection_fail_suggestion_select_high_power": "Hochleistung auswählen", "event_linux_power_off": "Die Stromversorgung {{index}} wurde ausgeschaltet", "event_linux_power_on": "Die Stromversorgung {{index}} wurde eingeschaltet", "event_linux_redundant_port_health_check": "Fehler beim Überprüfen des redundanten Port {{portIndex}}", "event_linux_RMON_trap_is_falling": "RMON-Trap fällt", "event_linux_RMON_trap_is_raising": "RMON-T<PERSON> ste<PERSON>t", "event_linux_rstp_invalid_bpdu": "Der RSTP-Port {{portIndex}} hat eine ungültige BPDU (Typ: {{type}}, Wert:{{value}})", "event_linux_rstp_migration": "Port {{portIndex}} changed from {{originTopology}} to {{changeTopology}}  Der Port {{portIndex}} wurde von {{originTopology}} in {{changeTopology}} ge<PERSON><PERSON>t", "event_linux_rstp_new_port_role": "Die RSTP-Port-Rolle {{portIndex}} wurde von {{originalRole}} in {{newRole}} ge<PERSON>ndert", "event_linux_ssl_cer_change": "Das SSL-Zertifikat wurde geändert ", "event_linux_topology_change": "Die Topologie hat sich geändert.", "event_linux_topology_change_by_type": "Der Status des Turbo Ring v2-Kopplungspfads hat sich geändert", "event_linux_user_login_lockout": "{{username}} wurde aufgrund von fehlgeschlagenen Anmeldeversuchen {{param}} blockiert", "event_linux_user_login_success": "{{username}} erfolgreich über {{interface}} angemeldet", "event_log_cleared_trap_event_info": "The event logs were cleared (User: {{ user }}, IP: {{ ip }}, Interface: {{ interface }})", "event_message_serial_device_port_any_recovery": "Der serielle Port {{portnum}} hat den Normalbetrieb wieder aufgenommen.", "event_message_serial_device_port_break": "Der serielle Port {{portnum}} hat einen Fehler erhalten: Break Error Count.", "event_message_serial_device_port_frame": "Der serielle Port {{portnum}} hat einen Fehler empfangen: <PERSON><PERSON>.", "event_message_serial_device_port_overrun": "Der serielle Port {{portnum}} hat einen Fehler empfangen: Fehleranzahl überschritten.", "event_message_serial_device_port_parity": "Der serielle Port {{portnum}} hat einen Fehler empfangen: <PERSON><PERSON><PERSON> der Paritätsfehler.", "event_message_serial_device_port_rx": "Der RX des seriellen Ports {{portnum}} hat in den letzten {{min}} Minute(n) keine Daten empfangen.", "event_message_serial_device_port_rx_recovery": "Der RX des seriellen Ports {{portnum}} hat den Datenempfang wieder aufgenommen.", "event_message_serial_device_port_rxtx": "RX und TX des seriellen Ports {{portnum}} haben in den letzten {{min}} Minute(n) keine Daten empfangen.", "event_message_serial_device_port_rxtx_recovery": "RX und TX des seriellen Ports {{portnum}} haben den Datenempfang wieder aufgenommen.", "event_message_serial_device_port_tx": "Der TX des seriellen Ports {{portnum}} hat in den letzten {{min}} Minute(n) keine Daten empfangen.", "event_message_serial_device_port_tx_recovery": "Der TX des seriellen Ports {{portnum}} hat den Datenempfang wieder aufgenommen.", "event_not_found_type": "Unknown event type: {{ eventType }}", "event_pd_check_fail": "PD-Fehlerprüfung (keine Antwort)", "event_pd_over_current": "PoE-Port {{portIndex}} Überstrom/Kurzschluss", "event_pd_power_off": "PoE-Port {{portIndex}} Stromversorgung aus", "event_pd_power_on": "PoE-Port {{portIndex}} Stromversorgung ein", "event_prp_function_fail": "PRP-Funktion ist ausgefallen", "event_serial_device_port_break": "Der serielle Port hat einen Fehler empfangen: Break Error Count", "event_serial_device_port_frame": "Der serielle Port hat einen Fehler empfangen: <PERSON><PERSON>", "event_serial_device_port_overrun": "Der serielle Port hat einen Fehler empfangen: Fehleranzahl überschritten", "event_serial_device_port_parity": "Der serielle Port hat einen Fehler empfangen: <PERSON>zahl der Paritätsfehler", "event_serial_device_port_rx": "Der RX des seriellen Ports hat keine Daten empfangen", "event_serial_device_port_rxtx": "Der RX & TX des seriellen Ports hat keine Daten empfangen", "event_serial_device_port_tx": "Der TX des seriellen Ports hat keine Daten empfangen", "event_sfp_rx_below": "Die RX-Leistung ({{currentdB}} dBm) des SFP-Ports {{portIndex}} liegt unter dem Schwellenwert ({{thresholddB}} dBm).", "event_sfp_rx_below_recovery": "SFP Port {{portIndex}} RX {{recoverydB}}dBm wird zurückgewonnen", "event_sfp_temp_over": "Die Modultemperatur ({{currentTemp}}ºc) des SFP-Ports {{portIndex}} hat den Schwellenwert überschritten ({{currentTemp}}ºc).", "event_sfp_temp_over_recovery": "SFP Port {{portIndex}} Temperatur {{recoveryTemp}}ºc wird zurückgewonnen", "event_sfp_tx_below": "Die Sendeleistung ({{currentdB}} dBm) des SFP-Ports {{portIndex}} liegt unter dem Schwellenwert ({{thresholddB}} dBm).", "event_sfp_tx_below_recovery": "SFP Port {{portIndex}} TX {{recoverydB}}dBm wird zurückgewonnen", "event_sfp_voltage_below": "Die Modulspannung ({{currentVoltage}} V) des SFP-Ports {{portIndex}} liegt unter dem Schwellenwert ({{thresholdVoltage}} V).", "event_sfp_voltage_below_recovery": "SFP Port {{portIndex}} Spannung {{recoveryVoltage}}v wird zurückgewonnen", "event_sfp_voltage_over": "Die Modulspannung ({{currentVoltage}} V) des SFP-Ports {{portIndex}} hat den Schwellenwert überschritten ({{thresholdVoltage}} V).", "event_sfp_voltage_over_recovery": "SFP Port {{portIndex}} Spannung {{recoveryVoltage}}v wird zurückgewonnen", "event_too_many_login_failure": "Der Zugriff auf die Weboberfläche wurde aufgrund zu vieler Anmeldefehler vorübergehend blockiert.", "event_too_many_login_failure_recovery": "Zu viele Anmeldefehler Der Web-Zugriff wird wiederhergestellt.", "event_tracking_port_enabled_status": "Einer der Port-bezogenen Tracking-Einträge wird geändert", "event_tracking_static_route_status_changed": "Einer der statischen routenbezogenen Tracking-Einträge wird geändert", "event_tracking_status_changed": "Einer der Stati von Tracking-Einträgen wird geändert", "event_tracking_vrrp_status_changed": "Einer der VRRP-bezogenen Tracking-Einträge wird geändert", "event_trusted_access_attack": "Sicherer Router unter Trusted-Access-Attacke", "event_trusted_access_attack_recovery": "Wiederherstellung sicherer Router nach Trusted-Access-Attacke", "event_user_info_change": "Die Kontoinformationen wurden geändert: {{trapoid}}", "event_v3_trap_parse_error": "V3-<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>", "event_v3_trap_parse_error_recovery": "Das V3-Trap-<PERSON><PERSON><PERSON>ehlerereignis wird gel<PERSON>t", "exceed_poe_threshold": "PoE-Systemschwellenwert überschritten", "fan_module_malfunction": "Das Lüftermodul ist defekt.", "fiber_warning": "{{portIndex}} Faserwarnung ({{warningType}})", "firewall_policy_violation": "Firewall-Richtlinie und DoS-Regel: {{trapoid}}", "firewall_under_attack": "Die Firewall eines sicheren Routers ist unter Attacke", "firewall_under_attack_recovery": "Die Firewall eines sicheren Routers ist nicht mehr unter Attacke", "firmware_upgraded": "Firmware-Upgrade", "firmware_version_release": "Ein Firmware-Update ist verfügbar. Weitere Informationen finden Sie auf der Seite „Firmware-Verwaltung“.", "goose_healthy": "GOOSE-Status:  Gesund GOOSE-Meldung {{ display }}", "goose_healthy_with_value": "GOOSE-Status:  Healthy GOOSE message {{ display }}", "goose_tampered": "GOOSE-Status:  <PERSON><PERSON><PERSON><PERSON>", "goose_tampered_with_value": "GOOSE-Status:  <PERSON><PERSON><PERSON><PERSON> GOOSE-Meldung {{ display }}", "goose_timeout": "GOOSE-Status:  Zeitüberschreitung", "goose_timeout_with_value": "GOOSE-Status:  Zeitüberschreitung GOOSE-Meldung {{ display }}", "high_cpu_loading": "Die CPU-Auslastung hat 10 Minuten hintereinander 85 % überschritten.", "icmp_packet_loss_over_critical_threhold": "ICMP-Paketfehlerrate des Geräts bis {{param1}} (kritischer oberer Schwellenwert {{param2}})", "icmp_packet_loss_over_critical_threhold_recovery": "ICMP-Paketfehlerrate des Geräts liegt unter{{param1}} (kritischer oberer Schwellenwert {{param2}})", "icmp_packet_loss_over_threhold": "ICMP-Paketfehlerrate des Geräts bis {{param1}} (ober<PERSON> {{param2}})", "icmp_packet_loss_over_threhold_recovery": "ICMP-Paketfehlerrate des Geräts liegt unter {{param1}} (ober<PERSON>well<PERSON>t {{param2}})", "icmp_reachable": "Geräte-ICMP er<PERSON>bar", "icmp_unreachable": "Geräte-ICMP unerreichbar", "iei_fiber_warning": "<PERSON>e Faserwarnung wird ausgelöst", "input_bandwidth_over_threshold": "Die Auslastung der Eingangsbandbreite hat den Schwellenwert überschritten.", "input_bandwidth_over_threshold_disabled": "<PERSON><PERSON>wellenwerteinstellung für Eingangsbandbreitenauslastung für Port {{portIndex}}.", "input_bandwidth_over_threshold_recovery": "Port {{portIndex}} Eingangsbandbreitenauslastung wiederhergestellt, Wert ist {{currentValue}}.", "input_bandwidth_over_threshold_with_port": "Die Nutzung der Eingangsbandbreite ({{currentValue}}) des Hafens {{portIndex}} hat den Schwellenwert überschritten ({{threshold}}).", "input_bandwidth_under_threshold": "Die Auslastung der Eingangsbandbreite liegt unter dem Schwellenwert.", "input_bandwidth_under_threshold_disabled": "<PERSON><PERSON>wellenwerteinstellung für Eingangsbandbreitenauslastung für Port {{portIndex}}.", "input_bandwidth_under_threshold_recovery": "Port {{portIndex}} Eingangs-Bandbreitenauslastung wiederhergestellt, Wert ist {{currentValue}}.", "input_bandwidth_under_threshold_with_port": "Die Nutzung der Eingangsbandbreite ({{currentValue}}) des Hafens {{portIndex}} liegt unter dem Schwellenwert ({{threshold}}).", "input_packet_error_over_threshold": "Die Fehlerrate der Eingabepakete hat den Schwellenwert überschritten.", "input_packet_error_over_threshold_disabled": "<PERSON><PERSON>wellenwerteinstellung für die Eingangs-Paketfehlerrate für Port {{portIndex}}.", "input_packet_error_over_threshold_recovery": "Port {{portIndex}} Eingangs-Paketfehlerrate wiederhergestellt, Wert ist {{currentValue}}.", "input_packet_error_over_threshold_with_port": "Die Fehlerrate der Eingabepakete ({{currentValue}}) des Hafens {{portIndex}} hat den Schwellenwert überschritten ({{threshold}}).", "insufficient_disk_space": "Weniger als 5 GB verfügbarer Speicherplatz.", "interface_set_as_ospf_designated_router": "Die Schnittstelle ist als OSPF-designierter Router eingerichtet.", "ip_conflict_detected": "IP-Konflikt für {{ip}} erkannt, widersprüchliche MACs:", "ip_conflict_detected_failed": "Die IP-Konflikterkennung kann nicht ausgeführt werden, da Npcap/WinPcap/Libpcap nicht gefunden wird", "ip_conflict_recovery": "IP-Konflikt behoben.", "iw_client_joined": "Client verbunden: {{param}}", "iw_client_left": "Client getrennt: {{param}}", "l3_firewall_policy_violation": "Verstoß gegen Firewall-Richtlinien (NAT-Serie)", "license_limitation_reached": "Maximale Lizenzgrenze erreicht.", "license_not_enough": "Die Knotengrenze ist überschritten. Bitte löschen Sie einige Knoten oder upgraden Sie Ihre MXview One-Lizenz.", "license_over": "Die Knotengrenze ist überschritten. Bitte löschen Sie einige Knoten oder aktualisieren Sie Ihre MXview One-Lizenz.", "lldp_change": "Die LLDP-Tabelle hat sich geändert.", "logging_capacity": "Ereignisprotokoll liegt über dem Kapazitätsschwellenwert.", "login_radius_fail": "Die Login-Authentifizierung vom RADIUS+-Server ist fehlgeschlagen.", "login_radius_success": "Die Login-Authentifizierung vom RADIUS+-Server ist erfolgreich.", "login_tacas_fail": "Die Login-Authentifizierung vom TACACS+-Server ist fehlgeschlagen.", "login_tacas_success": "Die Login-Authentifizierung vom TACACS+-Server ist erfolgreich.", "mac_sticky_violation": "MAC-Sticky-<PERSON><PERSON><PERSON><PERSON>", "mrp_multiple_event": "Es ist ein MRP-Ereignis für mehrere Manager aufgetreten.", "mrp_ring_open_event": "Es ist ein MRP-Ring-Öffnungsereignis aufgetreten.", "mstp_topology_changed": "Änderungen der MSTP-Topologie", "mxview_autopology_finish": "Automatische Topologie abgeschlossen", "mxview_autopology_start": "Automatische Topologie gestartet", "mxview_db_backup_fail": "Die Datenbanksicherung ist fehlgeschlagen.", "mxview_db_backup_sucess": "Die Datenbanksicherung ist abgeschlossen, Speicherort %MXviewPRO_Data%\\db_backup\\{{param1}} {{param2}}", "mxview_job_done": "Job: {{jobname}} ist erledigt", "mxview_job_start": "Job: {{jobname}} Start", "mxview_server_license_limit": "<PERSON>cht genügend <PERSON> von MXview One Central Manager", "mxview_server_start": "Der MXview One-Server wird gestartet.", "mxview_sms_fail": "Die SMS-Benachrichtigung kann nicht gesendet werden.", "mxview_sms_success": "MXview One hat eine SMS-Benachrichtigung erfolgreich gesendet.", "mxview_user_lockout": "Das Konto {{param}} wurde vorübergehend gesperrt.", "mxview_user_login_fail": "Die Anmeldung bei MXview One ist fehlgeschlagen.", "mxview_user_login_sucess": "Benutzeranmeldung: {{param}}", "mxview_user_logout": "Benutzerabmeldung: {{param}}", "network_latency": "Die Netzwerklatenz zum MXview One Central Manager hat 100 ms überschritten", "new_port_role_selected": "<PERSON>ählen Sie die neue Portrolle aus.", "new_root_bridge_selected_in_topology": "Wählen Sie die neue Root-Bridge in der Topologie aus.", "notification_sfp_rx_below": "SFP RX unter Schwellenwert", "notification_sfp_temp_over": "SFP Temperatur über Schwellenwert", "notification_sfp_tx_below": "SFP TX unter Schwellenwert", "notification_sfp_voltage_below": "SFP Spannung unter Schwellenwert", "notification_sfp_voltage_over": "SFP Spannung über Schwellenwert", "nport_syslog_over_threshold": "NPort liegt über dem Schwellenwert des Systemprotokolls.", "opcua_server_start": "MXview One OPC UA-Server wird gestartet.", "opcua_server_stop": "Der MXview One OPC UA-Server wurde gestoppt.", "ospf_designated_router_changed": "Der von OSPF festgelegte Router hat sich geändert.", "ospf_designated_router_interface_and_adjacency_changed": "OSPF-designierte Router-Schnittstellen und Umgebungen ändern sich.", "out_of_memory": "Weniger als 20 % Speicher verfügbar.", "output_bandwidth_over_threshold": "Die Auslastung der Ausgangsbandbreite hat den Schwellenwert überschritten.", "output_bandwidth_over_threshold_disabled": "<PERSON><PERSON>wellenwerteinstellung der Ausgangsbandbreitenauslastung für Port {{portIndex}}.", "output_bandwidth_over_threshold_recovery": "Port {{portIndex}} Ausgangsbandbreitenauslastung wiederhergestellt, Wert ist {{currentValue}}.", "output_bandwidth_over_threshold_with_port": "Die Auslastung der Ausgangsbandbreite ({{currentValue}}) des Hafens {{portIndex}} hat den Schwellenwert überschritten ({{threshold}}).", "output_bandwidth_under_threshold": "Die Auslastung der Ausgangsbandbreite liegt unter dem Schwellenwert.", "output_bandwidth_under_threshold_disabled": "<PERSON><PERSON>wellenwerteinstellung für Ausgangsbandbreitenauslastung für Port {{portIndex}}.", "output_bandwidth_under_threshold_recovery": "Port {{portIndex}} Ausgangsbandbreitenauslastung wiederhergestellt, Wert ist {{currentValue}}.", "output_bandwidth_under_threshold_with_port": "Die Auslastung der Ausgangsbandbreite ({{currentValue}}) des Hafens {{portIndex}} liegt unter dem Schwellenwert ({{threshold}}).", "output_packet_error_over_threshold": "Die Fehlerrate des Ausgabepakets hat den Schwellenwert überschritten.", "output_packet_error_over_threshold_disabled": "<PERSON><PERSON>wellenwerteinstellung für Ausgangspaketfehler für Port {{portIndex}}.", "output_packet_error_over_threshold_recovery": "Port {{portIndex}} Ausgangspaketfehler wiederhergestellt, Wert ist {{currentValue}}.", "output_packet_error_over_threshold_with_port": "Die Fehlerrate des Ausgabepakets ({{currentValue}}) des Hafens {{portIndex}} hat den Schwellenwert überschritten ({{threshold}}).", "overheat_protection_now_active_for_power_module": "Der Übertemperaturschutz des Netzteilmoduls {{ x }} ist jetzt aktiviert.", "password_automatically_changed_failed": "Das automatische Ändern des Gerätekennworts ist fehlgeschlagen.", "password_automatically_changed_success": "Das Gerätepasswort wurde erfolgreich automatisch geändert.", "password_automation_scheduled": "Die Kennwortautomatisierung hat die geplante Zeit erreicht und wird mit der Ausführung begonnen.", "phr_port_timediff": "PHR AB Port Zeitdiff.", "phr_port_wrong_lan": "PHR AB-Port Falsches LAN.", "poe_off_info": "The device is not powered by PoE", "poe_on_info": "The device is powered by PoE", "port_linkdown_event": "Port {{portindex}} Verbindung trennen", "port_linkdown_recovery": "Port {{portindex}} Verbindung herstellen", "port_linkup_event": "Port {{portindex}} Verbindung herstellen", "port_linkup_recovery": "Port {{portindex}} Verbindung trennen", "port_loop_detect": "Port Looping auf Port {{portnum}} wurde erkannt", "port_loop_detect_resolved": "Port-Looping auf Port {{portnum}} wurde behoben.", "port_loop_detected": "Port-Looping", "port_pd_short_circuited": "Port {{portnum}} Nicht-PD oder PD kurzgeschlossen.", "port_traffic_overload": "Datenverkehr von Port {{portIndex}} überlastet {{percent}}%", "power_danger_recovery": "Stromversorgung {{param}} geändert auf AC", "power_has_been_cut_due_to_overheating": "Die Stromversorgung wurde wegen Überhitzung unterbrochen.", "power_module_fan_malfunction": "Der Lüfter des Leistungsmoduls ist defekt.", "power_type_danger": "Stromversorgung {{param}} geändert auf DC", "pse_fet_bad": "PoE-Port {{portIndex}} externer FET ist ausgefallen", "pse_over_temp": "Temperaturüberschreitung PSE-Chip", "pse_veeuvlo": "VEE-Unterspannungssperre PSE-Chip", "ptp_grandmaster_changed": "PTP Grandmaster geändert", "ptp_sync_status_changed": "PTP-Synchronisierungsstatus geändert", "rateLimit_off": "Port {{portindex}} Ratengrenze ausgeschaltet", "rateLimit_on": "Die Ratenbegrenzung ist am Port aktiv {{portindex}}.", "recorved_device_lockdown_violation": "Wiederhergestellt nach Gerätesperrungsverletzung", "recorved_l3_firewall_policy_violation": "Wiederhergestellt nach Verstoß gegen Firewall-Richtlinie (NAT-Serie)", "redundancy_topology_change": "Redundante Topologie geändert.", "syslog_server_start": "Der MXview One-Syslog-Server wird gestartet.", "syslog_server_stop": "Der Syslog-Server von MXview One wurde gestoppt.", "system_temperature_exceeds_threshold": "Die Systemtemperatur überschreitet den Schwellenwert.", "temporary_account_activate_success": "Das temporäre Konto unter {{ip}} wurde erfolgreich aktiviert.", "temporary_account_deactivate_success": "Das temporäre Konto bei {{ip}} wurde erfolgreich deaktiviert.", "thermal_sensor_component_overheat_detected": "Es wurde eine Überhitzung der Thermosensorbaugruppe festgestellt.", "trunk_port_link_down": "Bündelungs-Port{{portindex}} getrennt (physikalischer Port:{{param}})", "trunk_port_link_down_recovery": "Bündelungs-Port {{portindex}} verbunden (physikalischer Port:{{param}})", "trunk_port_link_up": "Bündelungs-Port{{portindex}} verbunden (physikalischer Port:{{param}})", "trust_access_under_attack": "Verletzung des vertrauenswürdigen Zugriff eines sicheren Routers", "trust_access_under_attack_recovery": "Es liegt keine Verletzung des vertrauenswürdige Zugriff eines sicheren Routers mehr vor.", "turbo_ring_master_match": "<PERSON> Turboring-Master sti<PERSON><PERSON> ü<PERSON>.", "turbo_ring_master_mismatch": "<PERSON> Turboring-Master stimmt nicht überein.", "turbo_ring_master_unknow": "Turboring-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Status ist unbekannt", "turbochain_topology_change": "Die Turboketten-Topologie hat sich geändert.", "turboring_coupling_port_change": "Der Turboring-Kopplungs-Port hat sich geändert.", "turboring_master_change": "Die Turboring-Master hat sich geändert.", "unknown_device_detected": "Ein unbekanntes Gerät wurde erkannt", "user_login_fail": "Kontoauthentifizierung fehlgeschlagen", "user_login_success": "Erfolgreiche Kontoauthentifizierung: {{username}}", "usercode_revoke": "Der Usercode wird vom Benutzer neu generiert.", "vpn_link_recovery": "VPN-Tunnel {{param}} wird wiederhergestellt", "vpn_linkdown": "VPN-Tunnel {{param}} wird getrennt", "vpn_linkup": "VPN-Tunnel {{param}} wird verbunden", "vrrp_master_changed": "VRRP-<PERSON> g<PERSON><PERSON><PERSON>", "warn_start": "Warmstart"}, "event_detail_title": "Event Detail ID: {{eventId}}", "filter": "Filterbedingungen", "filter_end_time": "Enddatum", "filter_event": "<PERSON><PERSON> zum Filtern von E<PERSON>n", "filter_event_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filter_from_time": "Anfangsdatum", "filter_hour": "Stunde", "filter_min": "Minute", "filter_sec": "Sekunde", "filter_type": {"last_fifty_events": "Letzte fünfzig Ereignisse", "last_twenty_events": "Letzte zwanzig Ereignisse", "unack_events": "Nicht quittierte Ereignisse", "unack_last_fifty_events": "Nicht quittierte letzte 50 Ereignisse", "unack_last_twenty_events": "Nicht quittierte letzte zwanzig Ereignisse"}, "group": "Gruppe", "recent_event": "Neueste Ereignisse", "severity": {"any": "<PERSON><PERSON><PERSON>", "critical": "<PERSON><PERSON><PERSON>", "information": "Information", "network_device": "Netzwerk und Gerät", "system_information": "Systeminformationen", "warning": "<PERSON><PERSON><PERSON>"}, "show_event_detail": "Show Details", "source": {"any": "<PERSON><PERSON><PERSON>", "mxview": "MXview One", "security_sensing": "Sicherheitserkennung", "trap": "Trap"}, "table_title": {"ack": "<PERSON><PERSON><PERSON><PERSON>", "ack_time": "Quittier-Zeit", "always_show": "Beim <PERSON> immer das letzte Ereignis anzeigen", "description": "Beschreibung", "detail_information": "Detailinformationen", "event_id": "ID", "event_properties": "Ereigniseigenschaften", "event_source": "<PERSON><PERSON>", "event_time": "Uhrzeit ausgegeben", "hide_recent_event": "Kürzliche Ereignisse ausblenden", "severity": "Schwere", "show_recent_event": "Letzte Ereignisse anzeigen", "site_name": "Standortname", "source_ip": "Quellen-IP"}, "tabs": {"network_device_title": "Netzwerk und Gerät", "security_event": "Veranstaltung zur Cybersicherheit", "security_title": "Cybersicherheit", "system_title": "System"}}, "execute_cli_object": {"add_cli_object": "Fügen Sie ein CLI-Skript hinzu", "alias": "<PERSON><PERSON>", "cli_error": {"connection_failure": "Verbindungsfehler", "handshake_failure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "login_failure": "Anmeldungsfehler", "port_limit": "Maximale Anzahl von Port-Sicherheitsadresseinträgen: {{param}}", "reach_maximum_ssid": "Maximale Anzahl SSIDs: {{param}}", "smmp_configuration_mismatch": "Nicht übereinstimmende SNMP-Konfiguration", "ssh_not_supported": "<PERSON>s konnte keine Verbindung zum SSH-Client hergestellt werden", "unable_to_set_port": "Port {{port}} konnte nicht festgelegt werden", "unknown_error": "Unbekannter Fehler"}, "cli_Script": "CLI-Skript", "cli_session_timeout": "Zeitüberschreitung der CLI-Sitzung", "confirm_selected_devices": "Ausgewählte Geräte", "description": "Beschreibung", "execute_cli_fail": "Das CLI-<PERSON>k<PERSON>t kann nicht ausgeführt werden", "execute_cli_object": "Führen Sie ein CLI-Skript aus", "execute_cli_result_hint": "Wenn Sie diesen Bildschirm verlassen, können Sie die Ausführungsergebnisse unter „Gespeicherte CLI-Skripte > Ausführungsergebnisse“ herunterladen.", "execute_cli_results": "CLI-Ausführungsergebnisse", "execute_cli_script": "Führen Sie das CLI-Skript aus", "failed": "scheitern", "finished": "<PERSON><PERSON><PERSON>", "in_progress": "<PERSON><PERSON> ...", "ip": "IP", "model": "<PERSON><PERSON>", "name": "CLI-Skriptname", "no_cli_object_hint": "Keine CLI-Skripte gefunden. Fügen Sie zunächst ein CLI-Skript aus „Gespeicherte CLI-Skripte“ hinzu", "not_sent": "<PERSON>cht gesendet", "result": "<PERSON><PERSON><PERSON><PERSON>", "save_as_cli_object": "Als CLI-Skript speichern", "save_cli_object": "CLI-Skript speichern", "select_cli_object": "Wählen Sie ein CLI-Skript aus", "ssh_connection_timeout": "Timeout der SSH-Verbindung", "status": "Status"}, "firmware-management": {"action": "Aktion", "add-task": "Aufgabe hinzufügen", "add-to-schedule": "Geplantes Upgrade", "api-message": {"add-schedule-fail": "Aufgabe kann nicht geplant werden", "add-schedule-success": "Aufgabe geplant", "delete-schedule-fail": "Prüfintervallplan konnte nicht gelöscht werden", "delete-schedule-success": "Prüfintervallplan erfolgreich gelöscht", "fm-downloaded": "Firmware-<PERSON><PERSON> erfolgreich heruntergeladen", "fm-downloading": "Firmware-<PERSON><PERSON> wird her<PERSON><PERSON><PERSON><PERSON><PERSON>", "fm-ready": "Firmware-<PERSON><PERSON> bereit", "get-data-failed": "Daten können nicht abgerufen werden", "get-download-fm-failed": "Die Firmware-<PERSON><PERSON> konnte nicht heruntergeladen werden", "get-release-note-failed": "Versionshinweise können nicht abgerufen werden", "get-srs-status-failed": "Der Status des Moxa-Firmware-Servers kann nicht abgefragt werden", "ignored-model-fail": "Das Modell konnte nicht zur Liste „Ignorierte Modelle“ hinzugefügt werden"}, "check-Firmware-status": "Überprüfen Sie den Firmware-Status", "check-interval": "Überprüfen Sie das Intervall", "check-now": "Jetzt prüfen", "connected": "In Verbindung gebracht", "description": "Beschreibung", "disconnected": "Getrennt", "download-csv-report": "Laden Sie den CSV-<PERSON><PERSON><PERSON> herunter", "download-pdf-report": "PDF-Be<PERSON>t herunterladen", "execution-time": "Zeit", "firmware-upgrade-sequential": "Firmware-Upgrade (strikt sequenziell)", "firmware-upgrade-smart-concurrent": "Firmware aktualisieren (Smart Sequential)", "ignore-report": "Bericht ignorieren", "ignore-report-desc1": "Möchten Sie das Herunterladen des Berichts wirklich überspringen?", "ignore-report-desc2": "<PERSON>n Si<PERSON> diese Seite verlassen, steht der Bericht nicht mehr zum Download zur Verfügung.", "ignored-models": "Ignorierte Modelle", "last-update": "Zuletzt überprüft", "models": "<PERSON><PERSON>", "moxa-firmware-server-status": "Status des Moxa-Firmware-Servers", "no-information": "Keine Information verfügbar", "none": "<PERSON><PERSON>", "offline-desc": "Keine Information verfügbar. Keine vorherige Verbindung zum Firmware-Update-Server. <PERSON><PERSON><PERSON>, dass das Gerät mit dem Internet verbunden ist, und versuchen Sie es erneut.", "proceeding-firmware-upgrade": "Firmware-Upgrade-Status", "proceeding-upgrade-result": "Ergebnis des Firmware-Upgrades", "release-note": "Versionshinweise", "repeat-execution": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retry-Failed-devices": "Ausgefallene Geräte erneut versuchen", "select-devices": "Wählen Sie Geräte aus", "select-firmware": "Wählen Sie Firmware", "set-upgrade-sequence": "Aktualisierungssequenz", "sign-here": "Unterschrift", "start-date": "Datum", "status": {"failed": "Fehlgeschlagen", "finished": "<PERSON><PERSON><PERSON>", "in-progress": "<PERSON><PERSON>", "waiting": "<PERSON><PERSON>"}, "table-header": {"alias": "<PERSON><PERSON>", "current-version": "Aktuelle Version", "device-status": "G<PERSON><PERSON><PERSON><PERSON>", "ip": "IP", "latest-version-on-firmware-server": "Neueste Version auf dem Firmware-Server", "model-series": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "order": "<PERSON><PERSON><PERSON>", "selected-firmware-ready": "Ausgewählter Firmware-Download-Status", "selected-version": "Ausgewählte Version", "status": "Status"}, "task-name": "Aufga<PERSON><PERSON><PERSON>", "title": "Firmware-Management", "turn-off-check-interval": "Prüfintervall deaktivieren", "turn-on-check-interval": "Aktivieren Sie das Prüfintervall", "unable-to-download-firmware": "Das Herunterladen der Firmware ist fehlgeschlagen", "update-mode": "Update-Modus", "upgrade-desc1": "Die Update-Reihenfolge kann nicht ermittelt werden. Bitte fügen Sie zuerst den Computer, auf dem MXview One läuft, zur Topologie hinzu.", "upgrade-desc2": "Das aktuelle Setup unterstützt nur gleichzeitige Geräte-Firmware-Upgrades. Um die Upgrade-Methoden Strict Sequential oder Smart Sequential zu verwenden, müssen Si<PERSON> zuerst den Computer, auf dem MXview One ausgeführt wird, zur Topologie hinzufügen.", "upgrade-firmware-report": "Firmware-Upgrade-Bericht", "upgrade-now": "Jetzt upgraden", "upgrade-state-desc": "Das Firmware-Upgrade kann einige Zeit dauern. Bitte warten Sie, bis der Upgrade-Vorgang abgeschlossen ist.", "version": "Ausführung"}, "general": {"common": {"action": "Handlung", "allow": "<PERSON><PERSON><PERSON>", "any": "<PERSON><PERSON><PERSON>", "deny": "Verweigern", "description": "Beschreibung", "deviceInUse": "Verwendetes Gerät", "deviceName": "G<PERSON><PERSON><PERSON><PERSON>", "disabled": "<PERSON><PERSON>", "enabled": "Aktiviert", "endDate": "<PERSON><PERSON><PERSON>", "filters": "Filter", "firmwareVersion": "Firmware Version", "group": "Gruppe", "index": "Index", "ipAddress": "IP-Adresse", "location": "Stelle", "mac": "MAC-Adresse", "name": "Name", "online": "Online", "options": "Optionen", "productModel": "Produktmodell", "profileInUse": "Verwendetes Profil", "refCount": "Referenzen", "serialNumber": "SN", "startDate": "Anfangsdatum", "status": "Status", "title": "Titel"}, "dialog": {"deleteMsg": "Möchten Sie das ausgewählte {{ item }} wirklich löschen?", "deleteTitle": "{{ item }} löschen", "isSelected": "{{ number }} Artikel ausgewählt", "title_system_message": "Systemmeldung", "unsaved_hint_content": "Möchten Sie diese Seite wirklich verlassen?\nVon I<PERSON>en vorgenommene Änderungen werden nicht gespeichert.", "unsaved_hint_title": "Verlassen ohne zu speichern", "warning": "<PERSON><PERSON><PERSON>"}, "fileDrop": {"browse": "DURCHSUCHE", "dropText": "<PERSON><PERSON><PERSON> e<PERSON> Date<PERSON> hi<PERSON>her, oder"}, "item_selected": "{{ number }} Artikel ausgewählt", "log": {"localStorage": "Lokaler Speicher", "logDestination": "Protokollziel", "snmpTrapServer": "SNMP-Trap-Server", "syslogServer": "Syslog-Server", "title": "<PERSON><PERSON>ignisp<PERSON><PERSON>ll"}, "menu": {"jump_page_placeholder": "Für Seitensprung Alt+J drücken"}, "page_state": {"application_error": "Anwendungsfehler :(", "application_error_desc": "Bei der Bearbeitung dieser Anfrage ist ein Fehler aufgetreten.", "back_link": "Zurück zur Indexseite", "page_not_found": "Seite nicht gefunden :(", "page_not_found_desc": "Die angeforderte URL wurde auf diesem Server nicht gefunden."}, "severity": {"alert": "Alarm", "critical": "<PERSON><PERSON><PERSON>", "debug": "Debugg<PERSON> ", "emergency": "Notfall", "error": "<PERSON><PERSON>", "high": "Hoch", "information": "Information", "informational": "Informativ", "low": "<PERSON><PERSON><PERSON>", "medium": "<PERSON><PERSON><PERSON>", "notice": "<PERSON><PERSON><PERSON><PERSON>", "title": "Schwere", "warning": "<PERSON><PERSON><PERSON>"}, "shortWeekday": {"fri": "<PERSON>.", "mon": "Mo.", "sat": "So.", "sun": "Sonne.", "thu": "Do.", "tue": "Di.", "wed": "<PERSON><PERSON><PERSON>."}, "table": {"add": "HINZUFÜGEN", "delete": "LÖSCHEN", "download": "HERUNTERLADEN", "downloadAllLogs": "Alle Protokolle <PERSON>", "edit": "BEARBEITEN", "filter": "Filter", "info": "Infos", "more": "<PERSON><PERSON>", "permissionDenied": "Erlau<PERSON><PERSON> verweigert", "reboot": "Starten Sie neu", "refresh": "AKTUALISIERUNG", "reorderFinish": "Neuordnung abschließen", "reorderPriority": "Prioritäten neu anordnen", "search": "<PERSON><PERSON>", "transfer": "Übertragung", "upgrade": "AUFRÜSTEN"}, "top_nav": {"api_doc": {"title": "API-Referenz"}, "hide_recent_event": "Navigationsm<PERSON><PERSON><PERSON> au<PERSON>", "notifications": {"message_content": "Ereignisinhalt", "message_readall": "Mehr Benachrichtigungen", "message_title": "Ereignistitel", "notification_header": "Benachrichtigungen"}, "show_recent_event": "Navigationsm<PERSON><PERSON><PERSON> anzeigen", "user_profile": {"advanced_mode": "Erweiterter Modus", "change_pwd": "Passwort ändern", "greeting": "Hall<PERSON>", "logout": "Abmelden", "manage_account": "<PERSON><PERSON> ver<PERSON>ten", "reset_factory_default": "Auf Werkseinstellungen zurücksetzen", "restart_machine": "<PERSON><PERSON><PERSON> neu starten", "search": "<PERSON><PERSON>en Si<PERSON> ein Schlüsselwort ein, um zu suchen"}}, "topNav": {"caseInsensitive": "G<PERSON>ß-/Kleinschreibung wird nicht beachtet", "changePwd": "KENNWORT ÄNDERN", "changeSuccess": "Ihr Passwort wurde erfolgreich aktualisiert. Bitte melden Si<PERSON> sich erneut an.", "confirmNewPwd": "Neues Passwort bestätigen", "currentPwd": "derzeitiges Passwort", "invalidKey": "Die folgenden Namen sind reserviert: <PERSON><PERSON>, Operator, <PERSON>er, <PERSON>, Administrator, Auditor", "logout": "Ausloggen", "logoutMsg": "Sind Sie sicher, dass Sie sich abmelden?", "newPwd": "Neues Passwort", "subject": "<PERSON>a", "troubleshoot": "Fehlersuche", "troubleshootMsg": "Sie können die Debug-Protokolle zur Fehlerbehebung auf den lokalen Host exportieren.", "updateAuthority": "Autorität aktualisieren", "updateSuccess": "Ihre Kontoberechtigung wurde geändert. Bitte melden Si<PERSON> sich erneut an.", "username": "<PERSON><PERSON><PERSON><PERSON>"}, "unit": {"days": "Tage)", "entries": "Einträge", "minute": "Minute", "minutes": "Minute(n)", "months": "<PERSON><PERSON>(e)", "percent": "%", "pkts": "Pkt/s", "sec": "Sek.", "seconds": "Sekunde(n)", "thousand": "tausend"}, "weekday": {"friday": "Freitag", "monday": "Montag", "saturday": "Samstag", "sunday": "Sonntag", "thursday": "Don<PERSON><PERSON>", "tuesday": "Dienstag", "wednesday": "Mittwoch"}}, "GLOBAL_MESSAGE": {"update_fail": "Aktualisierung fehlgeschlagen", "update_success": "Erfolgreich geu<PERSON>"}, "GROUP_PROPERTIES": {"description": "Beschreibung", "devices": "Ger<PERSON><PERSON> (normal, Warnung, kritisch)", "information": "Informationen", "name": "Name", "title": "Gruppeneigenschaften"}, "IMAGE": {"deviceSizeError": "Maximum image size is 100KB", "error": "MXview One unterstützt nur die Formate jpg, gif und png.", "sizeError": "Maximale Bildgröße beträgt 1MB"}, "inventory_management": {"active": "<PERSON><PERSON><PERSON><PERSON>", "alias": "<PERSON><PERSON>", "assets_list": "Asset-Liste", "available": "Verfügbar", "channel_extended_end_date": "Enddatum der erweiterten Kanalgarantie", "channel_extended_warranty_end_date_hint": "Wenn Si<PERSON> mit Ihrem Mo<PERSON>-Channel-Provider über eine erweiterte Garantievereinbarung verfügen, geben Sie das erweiterte Ablaufdatum hier manuell ein.", "check_warranty_manually": "<PERSON><PERSON><PERSON> manuell prü<PERSON>", "check_warranty_status": "Garantiestatus prüfen", "days": "<PERSON><PERSON> zuvor", "email_example": "<EMAIL>", "email_to": "E-Mail an", "expire_soon": "<PERSON><PERSON><PERSON><PERSON> bald ab", "expired": "Abgelaufen", "firmware_version": "Firmware-Version", "invalid_email_desc": "Ungültige E-Mail-Adresse", "ip": "IP", "last_update": "Letztes Update", "mac_address": "MAC-Adresse", "model": "<PERSON><PERSON>", "multiple_email_hint": "<PERSON>e können mehrere Empfänger-E-Mail-Adressen hinzufügen, getrennt durch Kommas.", "no_data": "nicht verfügbar", "notify_before": "Eine Erinnerung senden", "retrieve_data": "Daten ab<PERSON><PERSON>", "select": "Auswählen", "serial_number": "Ordnungsnummer", "type": "<PERSON><PERSON> nach", "unable_query_warranty_server_status": "Der Moxa-Garantieserver kann nicht erreicht werden.", "unable_retrieve_warranty_information": "Garantieinformationen können nicht abgerufen werden.", "unavailable": "Nicht verfügbar", "warranty_end_date": "Garantie-Enddatum", "warranty_end_date_notification": "Benachrichtigungen zum Ablauf der Garantie", "warranty_management": "Garantiemanagement", "warranty_notification": "Garantiebenachrichtigungen", "warranty_period": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warranty_server_status": "<PERSON><PERSON>-Server<PERSON>tus", "warranty_start_date": "Garantiebeginn", "warranty_status": "G<PERSON><PERSON><PERSON><PERSON>"}, "INVENTORY_REPORT": {"alias": "<PERSON><PERSON>", "filter": "Typ zum Filtern von Inventurberichten", "fw_version": "Firmwareversion", "ip_address": "IP-Adresse", "mac": "MAC-Adresse", "model": "<PERSON><PERSON>", "property": "Eigenschaft", "report_generate_day": "Datum der Berichtserstellung:", "site_name": "Standortname", "system_desc": "Systembeschreibung", "title": "Lagerbestandsbericht", "value": "Wert"}, "IP_CONFIGURATION": {"auto_ip": "Automatische IP", "change_ip_fail": "Die IP-Konfiguration kann nicht eingestellt werden.", "change_ip_success": "Die Geräte-IP-Konfiguration wird aktualisiert.", "gateway": "Gateway", "hint": "Diese Funktion ist für Layer-3-Geräte nicht verfügbar.", "ip_address": "IP-Adresse", "netmask": "Netzmaske", "title": "IP-Konfiguration"}, "ip_conflict_detected_notification": "IP-Konflikt erkannt", "ip_conflict_recovery_notification": "IP-Konflikt behoben", "ips_configuration": {"dialog-title": "IPS-Konfiguration", "execute_fail": "Ausführung fehlgeschlagen", "input-ips": "IPS", "option-detection-mode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "option-prevention-mode": "Präventionsmodus", "selet-ips-operation-mode": "IPS-Betriebsmodus", "th-execution-status": "Ausführungsstatus"}, "IPSEC": {"connection_name": "Verbindungsname", "ipsec_status_phase1": "IPSec Status Phase1", "ipsec_status_phase2": "IPSec Status Phase2", "local_gateway": "Lokales Gateway", "local_subnet": "Lokales Subnetz", "remote_gateway": "Entferntes Gateway", "remote_subnet": "Entferntes Subnetz"}, "IW": {"Message": {"CONNECT_TIMEOUT": "Verbindungszeitüberschreitung", "ERROR_OCCURRED": "Server-<PERSON><PERSON>. Bitte versuchen Sie es später erneut.", "FAILED": "Fehlgeschlagen", "LOAD_DATA_FAILED": "Fehler beim Laden der Daten!", "RSSI_SNR_ONLY": "(Nur Signalstärke und SNR)", "SET_SUCCESS": "Einstellung erfolgreich geändert", "SUCCESSED": "Erfolgreich", "UPDATE_FAILED": "Fehler bei der Datenaktualisierung!"}, "Title": {"ap": "AP", "AUTO_REFREASH": "Automatische Aktualisierung: ", "AUTO_REFRESH": "Automatische Aktualisierung", "BSSID": "BSSID", "channel": "Channel", "client": "Client", "client_count": "Client<PERSON><PERSON><PERSON><PERSON>", "client_router": "Client-Router", "CLOSE": "Schließen", "COLOR": "Farbe", "COLUMN": "Parameterspalte", "CONDITIONS": "Bedingung", "CONN_TIME": "Verbindungszeit (Sek.)", "connected": "Verbunden", "DEVICE_NAME": "G<PERSON><PERSON><PERSON><PERSON>", "disable": "Disable", "ENABLE": "Freigeben", "FILTER_TABLE_VIEW": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hint": "This page will be removed in a future product release. Please use Wireless Add-on in MXview One instead.", "IP_ADDR": "IP-Adresse", "link_speed": "Verbindungsgeschwindigkeit", "MAC": "MAC-Adresse", "master": "<PERSON><PERSON>", "MODULATION": "Modulation", "noise_floor": "Noise Floor", "noise_floor_unit": "Noise Floor (dBm)", "OK": "OK", "ONLINE": "Online", "operation_mode": "Betriebsmodus", "RSSI": "Signalstärke (dBm)", "security_mode": "Sicherheitsmodus", "signal_level": "Signal Level", "slave": "Sklave", "SNR": "SNR (dB)", "SNR_A": "SNR-A (dB)", "SNR_B": "SNR-B (dB)", "ssid": "SSID", "TOTAL_AP": "<PERSON>zahl APs: ", "TOTAL_CLIENT": "Anzahl Clients: ", "tx_power": "TX-Leistung", "tx_power_unit": "TX-Leistung (dBm)", "tx_rate": "TX-Rate", "tx_rate_unit": "TX-Rate (Mb/s)", "uptime": "Betriebszeit", "VALUE": "Wert", "WIRELESS_TABLE_VIEW": "Wireless-Tabellenansicht"}}, "JOB_SCHEDULER": {"add_failed": "Hinzufügen des Auftrags fehlgeschlagen.", "add_success": "Ein neuer Auftrag wird hinzugefügt.", "add_title": "Neuen Auftrag hinzufügen", "alias": "<PERSON><PERSON>", "auto_topology": "Automatische Topologie", "cli_object_name": "CLI-Skriptname", "config_file": "Konfigurationsdatei", "config_file_error": "MXview One unterstützt nur das .ini-Format.", "config_file_size_error": "Maximale Dateigröße ist 1 MB", "current_filename": "Aktueller Dateiname", "current_version": "Aktuelle Version", "daily": "Tä<PERSON><PERSON>", "database_backup": "Datenbanksicherung", "delete_failed": "Löschen des Auftrags fehlgeschlagen", "delete_success": "Der Auftrag wird gelöscht.", "description": "Beschreibung", "edit_title": "Auftrag bearbeiten", "excute_cli_object": "Führen Sie das gespeicherte Skript aus", "execution_time": "Ausführungszeit", "export_configuration": "Konfiguration exportieren", "filter": "<PERSON><PERSON> zum Filtern von Aufträgen", "fm_sequential": "Strenge Reihenfolge", "fm_smart": "Intelligente Sequenzierung", "friday": "Freitag", "import_configuration": "Konfiguration importieren", "ip": "IP", "job_action": "Aktion", "job_log": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "job_name": "Auftragsname", "model_series": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monday": "Montag", "monthly": "<PERSON><PERSON><PERSON>", "on": "Am", "once": "Einmal", "order": "<PERSON><PERSON><PERSON>", "registered_devices": "Registrierte Geräte", "repeat_execution": "Ausführung wiederholen", "saturday": "Samstag", "schedule_time": "Zeit planen", "selected_version": "Ausgewählte Version", "show_log_fail": "Protokollfehler anzeigen", "show_log_not_found": "Protokoll kann nicht angezeigt werden", "start_date": "Anfangsdatum", "sunday": "Sonntag", "thursday": "Don<PERSON><PERSON>", "title": "Wartungsplaner", "tuesday": "Dienstag", "update_failed": "Aktualisierung des Auftrags fehlgeschlagen.", "update_success": "Der Auftrag wird aktualisiert.", "wednesday": "Mittwoch", "weekly": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "LANG": {"de-DE": "De<PERSON>ch", "en-US": "English", "es-ES": "Español", "fr-FR": "Français", "ja-JP": "日本語", "ru-RU": "Русский язык", "zh-CN": "中文(简体)", "zh-TW": "中文(繁體)"}, "LICENSE": {"activation_code": "Aktivierungscode", "activation_code_error": "Ungültiger Aktivierungscode", "activation_title": "Aktivierung", "active": "Aktivieren", "active_desc_1": "<PERSON><PERSON><PERSON>", "active_desc_2": "http://license.moxa.com/", "active_desc_3": "und geben Sie den Produktcode und den Benutzercode ein, um Ihren Aktivierungscode zu erhalten.", "add_fail": "Die Lizenz kann nicht hinzugefügt werden.", "add_license": "<PERSON><PERSON><PERSON>", "add_new_license_desc": "<PERSON>e können hier eine Lizenz hinzufügen.", "add_new_license": {"activate": "Aktivieren", "activate_intro_link": "Moxa-Lizenzseite", "activate_intro_pre": "Laden Sie die Lizenz von der ", "activate_intro_suf": " herunter und fügen Sie den Aktivierungscode hier ein.", "copy_user_code": "Benutzercode kopieren", "copy_user_code_intro_link": "Moxa-Lizenzseite", "copy_user_code_intro_pre": "Kopieren Sie den Benutzercode auf die ", "license_site_step_1_link": "Moxa-Lizenzseite", "license_site_step_1_pre": "1. <PERSON><PERSON> auf der ", "license_site_step_1_suf": " anmelden", "license_site_step_2": "2. <PERSON><PERSON><PERSON><PERSON> sie auf der Seite \"Activate Your License\" und \"MXview One\"", "license_site_step_3": "3. Registrierungscode", "license_site_step_3_Free_step": "Setzen Sie fort mit dem nächsten Schritt", "license_site_step_3_Free_title": "Benutzer einer kostenlosen Version: ", "license_site_step_3_Full_step": "Geben Sie den Registrierungscode und den Benutzercode auf der Moxa-Lizenzseite ein. Der Benutzercode wird in den nächsten Schritten angezeigt", "license_site_step_3_Full_title": "Benutzer einer Volllizenz: ", "login_license_site": "<PERSON>ch auf der Moxa-Lizenzseite anmelden", "select_network_adapter": "Netzwerkadapter wählen", "title": "Neue Lizenz hinzufügen"}, "add_success": "<PERSON><PERSON><PERSON> wird erfolgreich hinzugefügt", "copied_to_clipboard": "Ko<PERSON>rt in die Zwischenablage", "copy_deactivation_code": "Deaktivierungscode kopieren", "copy_older_license_code": "Den Lizenzcode 2 x kopieren", "current_nodes": "Aktuelle Knoten:", "deactivate": "Deaktivieren", "deactivate_fail": "Die Lizenz kann nicht deaktiviert werden.", "deactivate_success": "<PERSON><PERSON>z wird erfolg<PERSON>ich deaktiviert", "deactivated_licenses": "Deaktivierte Lizenzen", "deactivating": "Deaktivierung wird ausgeführt ...", "deactivation_code": "Deaktivierungscode", "deactivation_desc": "Die Lizenz wird nach der Deaktivierung ungültig; sind <PERSON> sicher, dass Sie die Lizenz deaktivieren wollen?", "deactivation_title": "Deaktivierung", "disabled": "<PERSON><PERSON>", "duration": "<PERSON><PERSON>", "enabled": "Aktiviert", "expired_license": "Abgelaufene <PERSON>", "free_trial": "Kostenloser Test", "free_trial_desc": "Beginnen Sie und erleben Sie die Leistungsfähigkeit von MXview One.", "import_license_file": "Lizenzdatei importieren", "license": "Lizenz:", "license_authorized": "Autorisiert", "license_free": "<PERSON><PERSON><PERSON>", "license_none": "<PERSON><PERSON>", "license_site": "Moxa-Lizenzseite", "license_start": "Lizenzbeginn", "license_title": "<PERSON><PERSON><PERSON>", "license_trial": "Test", "license_type": {"node_base_intro": "Bietet die Anzahl der Geräte, die MXview One im Netzwerk überwachen kann.", "node_base_title": "Knotenbasierte <PERSON>z", "power_addon": "Strom-Add-on-Lizenz", "power_intro": "Ermöglicht den Benutzern den Zugriff auf mehr strombezogene Funktionen.", "security_addon": "Sicherheits-Add-on-Lizenz", "security_intro": "Ermöglicht Benutzern den Zugriff auf zusätzliche sicherheitsrelevante Funktionen.", "title": "Lizenztyp", "trial_intro": "Sie können die Leistungsfähigkeit von MXview One innerhalb von 90 Tagen testen.", "trial_title": "Testlizenz", "wireless_addon": "Wireless Add-on Lizenz", "wireless_intro": "Ermöglicht Benutzern den Zugriff auf weitere Wireless-bezogene Funktionen."}, "licensed_node": "Lizenzierter Knoten", "licensed_nodes": "lizenzierte Knoten:", "licenses": "<PERSON><PERSON><PERSON>", "managed_by_central": "The License is managed by MXview One Central", "managed_by_central_licenses_invalidated": "No valid licenses, please check the status in Control Panel.", "mxview": "MXview One", "network_adapter": {"button": "Netzwerkadapter wählen", "change_network_adapter": "Netzwerkadapter ändern", "change_network_adapter_alert_1": "Sind <PERSON> sic<PERSON>, dass Sie den Netzwerkadapter ändern wollen?", "change_network_adapter_alert_2": "Nachdem <PERSON><PERSON> \"Bestätigen\" gew<PERSON><PERSON>t haben, werden alle Lizenzen deaktiviert. Sie können MXview One dann erst wieder verwenden, wenn Sie die neue Lizenz mit dem neuen Netzwerkadapter registriert haben.", "intro": "MXview One bindet die Lizenz an einen Netzwerkadapter. Wählen Sie den Adapter aus, den Sie binden möchten. Wenn Sie einen Netzwerkadapter erneut auswählen, werden alle Ihre Lizenzen automatisch deaktiviert. Sie müssen sie erneut registrieren.", "select_adapters": "<PERSON><PERSON><PERSON> wählen", "select_adapters_desc": "<PERSON><PERSON><PERSON>en Sie bitte einen Netzwerkadapter', MXview One verwendet ihn, um Ihren Benutzercode zu generieren.", "title": "Netzwerkadapter"}, "node": "Aktuelle Knoten / lizenzierte Knoten:", "nodes": "Knoten", "older_license": "2.x <PERSON><PERSON><PERSON>", "older_license_nodes": "2.x Knoten", "over_nodes_desc": "Sie werden abgemeldet, weil die Anzahl der überwachten Knoten die Anzahl der unterstützten Lizenzen überschreitet.", "over_nodes_title": "<PERSON><PERSON><PERSON>", "power_addon_trial": "<PERSON><PERSON>ben Sie jetzt das Power Add-on in MXview One", "reactivate_license": {"activate": "Aktivieren", "activate_intro_link": "Moxa-Lizenzseite", "activate_intro_pre": "Laden Sie die Lizenz von der ", "activate_intro_suf": " herunter und fügen Sie den Aktivierungscode hier ein.", "copy_deactivate_code": "Deaktivierungscode kopieren", "copy_deactivate_code_intro_link": "Moxa-Lizenzseite", "copy_deactivate_code_intro_pre": "Kopieren Sie den Deaktivierungscode und fügen Sie ihn auf der ", "copy_deactivate_code_intro_suf": " ein", "copy_user_code": "Benutzercode kopieren", "copy_user_code_intro_link": "Moxa-Lizenzseite", "copy_user_code_intro_pre": "Kopieren Sie den Benutzercode auf die ", "intro": "Verwenden Sie sowohl den Deaktivierungscode als auch den Benutzercode, um Ihre Lizenz erneut zu aktivieren.", "license_site_step_1_link": "Moxa-Lizenzseite", "license_site_step_1_pre": "1. <PERSON><PERSON> auf der ", "license_site_step_1_suf": " anmelden", "license_site_step_2": "2. <PERSON><PERSON><PERSON> Sie auf der Seite auf \"MXview One Deactivation\" und \"Transfer to another Device\".", "license_site_step_3": "3. <PERSON><PERSON><PERSON><PERSON> Sie unter Softwareprodukt die Option MXview One aus", "login_license_site": "<PERSON>ch auf der Moxa-Lizenzseite anmelden", "title": "Lizenz erneut aktivieren", "title_abbr": "Erneut aktivieren"}, "reason": "Zustand", "relaunch": {"activating": "Aktivierung...", "active_note": "Der Vorgang wird in 10 Sekunden beendet."}, "remain": "Restliche", "security_addon_trial": "Erleben Sie das MXview One Security Add-on", "select_network_interface": "Netzwerkschnittstelle auswählen", "site_license_invalid": "Sie haben ab einigen Stellen eine ungültige Lizenz.", "site_license_invalid_title": "<PERSON><PERSON><PERSON>", "start_free_trial": "Testversion starten", "start_free_trial_fail": "Start des kostenlosen Tests fehlgeschlagen", "start_free_trial_success": "Kostenloser Test erfolgreich gestartet", "state": "Zustand:", "state_all_licenses_invalidated": "<PERSON><PERSON> gültigen Liz<PERSON>", "state_cannot_add_free_license": "Eine kostenlose Lizenz kann nicht hinzugefügt werden, wenn Si<PERSON> vollständige Lizenzen haben.", "state_cannot_add_multiple_free_licenses": "Mehrere kostenlose Lizenzen können nicht hinzugefügt werden.", "state_format_incorrect": "Das Format einer Lizenzdatei ist falsch.", "state_general_error": "Allgemeiner Fehler", "state_license_deactivated": "Die Lizenz ist bereits deaktiviert.", "state_license_expired": "Lizenz ist abgelaufen", "state_license_is_registered": "Die Lizenz ist bereits im System registriert.", "state_license_not_found": "<PERSON><PERSON>z nicht gefunden", "state_license_over_2000": "Ihre Lizenz hat 2000 Knoten überschritten, was die maximal verfügbare Anzahl für MXview One ist.", "state_license_upgrade_error": "Upgrade-<PERSON><PERSON><PERSON> kann nicht hinzuge<PERSON><PERSON><PERSON> werden, MXview One benötigt mindestens eine Volllizenz.", "state_license_upgrade_no_full_license ": "<PERSON><PERSON><PERSON> kann nicht entfernt werden. Sie müssen zu<PERSON>t alle Upgrade-Lizenzen entfernen.", "state_no_full_license": "<PERSON>ine MXview One-<PERSON><PERSON><PERSON>", "state_no_usercode": "<PERSON><PERSON>", "state_over_nodes": "Bitte kaufen Sie zusätzliche Knoten, da nicht genügend Knoten für die Anzahl der Geräte, die Sie bereitstellen möchten, verfügbar sind. ", "state_trial_expired": "Testzeit abgelaufen", "state_trial_is_began": "Testzeit hat bereits begonnen", "state_trial_not_activated": "Test ist noch nicht aktiviert", "state_usercode_deactivated": "Benutzercode ist deaktiviert", "state_usercode_exists": "Ein Benutzercode ist bereits vorhanden.", "state_usercode_not_match": "Der Benutzercode in einer Lizenz stimmt nicht mit dem Benutzercode des Systems überein.", "state_usercode_not_match_adapter": "Der an den Benutzercode gebundene Netzwerkadapter wurde nicht gefunden.", "title": "<PERSON><PERSON><PERSON><PERSON>", "trial_button": "TEST", "trial_day": "Tage", "trial_expired": "Testzeit abgelaufen", "trial_over_nodes": "MXview One wird nach 30 Minuten gesperrt, wenn Sie nicht genügend Lizenzen hinzufügen oder übermäßig genutzte Knoten entfernen.", "trial_remaining": "Verbleibende Testzeit", "user_code": "Benutzercode:", "valid": "<PERSON><PERSON><PERSON><PERSON>", "wireless_addon_trial": "<PERSON><PERSON><PERSON>, das kabellose Add-on in MXview One zu erleben"}, "link_list": {"rx": "RX (%)", "tx": "TX (%)"}, "LINK_PROPERTIES": "Verbindungseigenschaften", "LOGIN": {"account_reach_limit": "Die Grenze angemeldeter Konten (10) ist überschritten.", "all_sites_offline": "Alle Standorte sind offline", "default_password_warning": "Bitte ändern Sie das Standardpasswort unter Berücksichtigung der höheren Sicherheitsstufe.", "error": "Ungültiger Benutzername/ungültiges Passwort", "ie_not_supported": "MXview One unterstützt kein IE, verwenden Sie bitte Google Chrome, um optimale Ergebnisse zu erzielen.", "last_login_fail": "Die zuletzt vermerkte/n, fehlgeschlagene/n Anmeldeversuch/e", "last_login_succeed": "Die letzte erfolgreiche Anmeldung erfolgte zur folgenden Uhrzeit", "login_fail_time": "{{loginTime}} Von {{loginIp}}", "login_succeed_time": "{{loginTime}} Von {{loginIp}}", "logout": "Abmelden", "password": "Passwort", "password_policy_mismatch": "Passwort stimmt nicht mit der Passwortrichtlinie überein", "sign_in": "Anmelden", "username": "<PERSON><PERSON><PERSON><PERSON>", "welcome": "Willkommen!"}, "max_char": "Maximal {{num}} <PERSON><PERSON><PERSON>", "min_char": "Mindestens {{num}} Z<PERSON><PERSON>", "model-port-mapping": {"port": "Port", "web-ui": "Web-Benutzeroberfläche auf dem Gerät"}, "MXVIEW_WIZARD": {"complete_page_title": "<PERSON>den", "navigate_to_wizard_page": "Möchten Sie den MXview One-Setup-Assistenten verwenden?", "step_add_scan_range": "Scan-<PERSON><PERSON><PERSON>", "step_auto_topology": "Topologie zeichnen (für Geräte, die LLDP unterstützen)", "step_create_group": "Gruppe er<PERSON>llen", "step_select_site": "<PERSON>ä<PERSON>en Si<PERSON> bitte eine Seite, um eine Einstellung vorzunehmen", "step_set_snmp": "SNMP-Einstellung festlegen", "step_set_trap_server": "SNMP-Trap-Server einstellen", "title": "Setup-Assistent", "welcom_page_title": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>-Assistent"}, "NETWORK_MENU": {"add_link": "Verbindung hinzufügen", "add_wifi_ssid": "WLAN-SSID hinzufügen", "alignment": {"bottom": "Unten ausrichten", "left": "<PERSON><PERSON> au<PERSON>", "right": "Rechts ausrichten", "title": "Ausrichtung", "top": "<PERSON><PERSON> aus<PERSON>"}, "copy_device_list": "Geräteliste kopieren", "create_a_snapshot": "Schnappschuss er<PERSON>llen", "cybersecurity_control": "Netzwerksicherheitskontrollen", "delete": "Löschen", "device_configuration": "Gerätekonfiguration", "device_control": "Gerätekontrolle", "device_dashboard": "Geräte-Dashboard", "device_login_account": "Gerätekonto", "device_panel": "Geräte-Panel", "device_wireless_settings": "Per-device Parameters", "disable_unsecured_http_and_telnet_console": "Deaktivieren Sie unsichere HTTP- und Telnet-Konsolen", "disable_unused_ethernet_and_fiber_ports": "Deaktivieren Sie nicht verwendete Ethernet- und Glasfaser-Ports", "document": {"menu": {"open": "<PERSON>en", "set": "Einstellen"}, "title": "Dokument"}, "dynamic_mac_sticky": "Dynamischer Sticky MAC", "edit": {"menu": {"add_device": "Ger<PERSON> hinzufügen", "delete_background": "Hintergrund löschen", "export_device_list": "Geräteliste exportieren", "export_topology": "Topologie exportieren", "import_device_list": "Geräteliste importieren", "set_background": "Hintergrund einstellen"}, "title": "<PERSON><PERSON><PERSON>"}, "execute_cli": {"menu": {"execute_cli_object": "Führen Sie das gespeicherte Skript aus", "execute_cli_script": "CLI-Skripte"}, "title": "Skript ausführen"}, "grid": {"menu": {"import_scd": "SCD importieren"}, "title": "Le<PERSON><PERSON>"}, "group": {"menu": {"change_group": "Gruppe ändern", "create_group": "Gruppe er<PERSON>llen", "group_maintenance": "Gruppenpflege"}, "title": "Gruppe"}, "grouping": "Gruppe", "ips_configuration": "IPS-Konfiguration", "ipsec_status": "IPsec-Status", "link_traffic": {"menu": {"packet_error_rate": "Paketfehlerrate", "port_traffic": "Port-Datenverkehr"}, "title": "Verbindungsdatenverkehr"}, "locator": "Lok<PERSON><PERSON><PERSON>", "mac_sticky_on_off": "Sticky MAC ein/aus", "maintenance": {"menu": {"advance_settings": "Erweiterte Einstellungen", "assign_model": "<PERSON><PERSON>", "basic_information": "Basisinformationen", "change_device_icon": "Gerätesymbol ändern", "device_identification_settings": "Einstellungen zur Geräteidentifikation", "eip_enable": "EtherNet/IP aktivieren", "eip_tcp_port": "EtherNet/IP TCP-Port", "eip_udp_port": "EtherNet/IP UDP-Port", "export_config": "Konfiguration exportieren", "generate_qr_code": "QR-<PERSON> generieren", "import_config": "Konfiguration importieren", "ip_configuration": "IP-Konfiguration", "modbus_enable": "Modbus aktivieren", "modbus_port": "Modbus-Anschluss", "modbus_tcp_configuration": "Modbus TCP-Einstellungen", "polling_ip_setting": "Polling-IP-Einstellungen", "polling_settings": "MXview One Abfrageintervall", "port_settings": "Einstellungen für Ethernet-/Glasfaser-Ports", "s7_port": "Siemens S7comm Port", "s7_status": "Siemens S7comm Aktivieren", "serial_port_monitoring": "Überwachung der seriellen Schnittstelle", "snmp_settings": "SNMP-Kommunikationsprotokoll", "trap_server": "Trap-Server", "upgrade_firmware": "Firmware aktualisieren"}, "title": "Wartung"}, "modify_device_alias": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "policy_profile_deployment": "Bereitstellung von Richtlinienprofilen", "reboot": "<PERSON>eu starten", "refresh": "<PERSON><PERSON><PERSON> laden", "restart_mac_sticky_learning": "Lernen Sie den dynamischen Sticky MAC neu", "restore_to_created_snapshot": "Aus Snapshot wiederherstellen", "scale": "Scale", "security_package_deployment": "Bereitstellung von Sicherheitspaketen", "set_port_label": "Port-Bezeichnung einstellen", "severity_threshold": "Schweregrad-Schwellenwert", "sfp": "SFP", "sfp_Info": "SFP Information", "sfp_list": "SFP Liste", "sfp_sync": "Schwellenwert vom Gerät synchronisieren", "tools": {"menu": {"device_panel": "Geräte-Bedienfeld", "mib_browser": "MIB<PERSON><PERSON><PERSON><PERSON>", "ping": "<PERSON><PERSON>", "telnet": "Telnet", "web_console": "Web-<PERSON><PERSON><PERSON>"}, "title": "Werkzeuge"}, "topology": {"menu": {"auto_layout": "Automatisches Layout", "auto_topology": "Automatische Topologie", "embed": "Widget e<PERSON><PERSON>ten", "scan_range": "Geräteentdeckung"}, "title": "Topologie"}, "ungrouping": "Gruppierung aufheben", "upgrade_patch": "Systemaktualisierungen anwenden", "visualization": {"menu": {"igmp": "IGMP", "security_view": "Sicherheitsansicht", "traffic_view": "Datenverkehrsansicht", "vlan": "VLAN", "vlan_view": "VLAN-Ansi<PERSON>"}, "title": "Visualisierung"}, "wifi_channel_change": "WLAN-Kanal ändern", "wireless_settings": "General Parameters", "wireless": {"menu": {"wireless_planner_view": "<PERSON><PERSON><PERSON>", "wireless_playback_view": "Kabellose Roaming-Wiedergabe", "wireless_table_view": "Überblick über kabellose Geräte"}, "title": "Wireless"}}, "NETWORK": {"current_status": {"no_event": "<PERSON><PERSON>", "title": "Aktueller Status", "v3_trap_event_clear_fail": "<PERSON><PERSON><PERSON> von V3 Trap-Ereignis fehlgeschlagen", "v3_trap_event_suggestion": "Prüfen Sie bitte die Snmp v3-Konfiguration"}, "not_selected": "<PERSON>ählen Si<PERSON> ein Modul aus, um Gerätedetails anzuzeigen"}, "NORTHBOUND_INTERFACE": {"custom_opc": {"add_custom_opc": "Benutzerdefinierte OPC-Tags hinzufügen", "all": "Alle", "apply_fail": "Benutzerdefinierte OPC-Tags können nicht hinzugefügt werden.", "apply_success": "Neue benutzerdefinierte OPC-Tags werden hinzugefügt.", "delete_fail": "Das OPC-Tag kann nicht gelöscht werden.", "delete_success": "OPC-Tag wurde gelöscht", "device_properties": "Geräteeigenschaften", "enable": "Benutzerdefinierte OPC-Tags wird aktiviert", "filter_custom_opc": "Typ zum Filtern benutzerdefinierter OPC-Tags", "get_fail": "Benutzerdefinierte OPC-Tags können nicht abgerufen werden", "property_name": "Objektname", "register_devices": "Geräte registrieren", "title": "Benutzerdefinierte OPC-Tags", "update_custom_opc": "Benutzerdefinierte OPC-Tags aktualisieren", "update_fail": "Das OPC-Tag kann nicht aktualisiert werden.", "update_success": "Benutzerdefinierte OPC-Tags werden aktualisiert."}}, "NOTIFICATION_SETTINGS": {"action": "Aktion", "action_cant_deleted": "Diese Aktion wird von einer oder mehreren Benachrichtigungen verwendet. Bitte deaktivieren Sie die Aktion aus diesen Benachrichtigungen und löschen Si<PERSON> sie erneut.", "action_information": "Aktionsinformation", "action_name": "Aktionsname", "action_tab_hint": "Wechseln Sie zur Registerkarte Aktion und füge Sie zu<PERSON>t eine Aktion hinzu", "action_type": "Art", "add_action": "Benachrichtigungsaktion hinzufügen", "add_action_fail": "Die Aktion kann nicht hinzugefügt werden.", "add_action_success": "Eine neue Aktion wird hinzugefügt.", "add_notification": "Benachrichtigung hinzufügen", "add_notification_fail": "Die Benachrichtigung kann nicht hinzugefügt werden.", "add_notification_success": "Eine neue Benachrichtigung wird hinzugefügt.", "check_security_tab": "Überprüfen Sie die Registerkarte „Cybersicherheit“", "content": "Inhalt", "delete_action_fail": "Die Aktion kann nicht gelöscht werden.", "delete_action_success": "Die Aktion wird gelöscht.", "delete_notification_fail": "Die Benachrichtigung kann nicht gelöscht werden.", "delete_notification_success": "Die Benachrichtigung wird gelöscht.", "edit_action": "Benachrichtigungsaktion bearbeiten", "edit_notification": "Benachrichtigung bearbeiten", "email": "E-Mail", "email_content_hint": "Der Inhalt hier wird an den Hauptteil der standardmäßigen Benachrichtigungs-E-Mail angehängt", "event_type": "<PERSON><PERSON>", "file_size_error": "Die maximale Dateigröße beträgt 1 MB.", "file_type_error": "MXview One unterstützt nur .wav-Dateien.", "filter_action": "<PERSON><PERSON> zum Filtern von Aktionen", "filter_notification": "Typ zum Filtern von Benachrichtigungen", "messagebox": "Meldungsfeld", "mobile": "MXview ToGo", "mobile_number": "Mobilttelefonnummer", "notification": "Benachrichtigung", "notification_name": "Benachrichtigungsname", "notification_name_exist": "Dieser Name wird bereits von einer anderen Meldung verwendet", "receiver_email": "Empfänger-E-Mail-Adresse", "register_devices": "Registrierte Geräte", "register_subscribers": "Registrierte Aktionen", "slack": "<PERSON><PERSON>ck", "sms": "SMS", "snmptrap": "SNMP Trap", "sound": "Tonda<PERSON>i", "teams": "Microsoft Teams", "testConnection": "Testverbindung", "title": "Benachrichtigungseinstellungen", "update_action_fail": "Die Aktion kann nicht aktualisiert werden.", "update_action_success": "Die Aktion wird aktualisiert.", "update_notification_fail": "Die Benachrichtigung kann nicht aktualisiert werden.", "update_notification_success": "Die Benachrichtigung wird aktualisiert.", "webhook": "Webhook", "webhook_fail": "Ausführung von Webhook nicht möglich", "webhook_success": "Webhook wurde gesendet"}, "OPC_UA_SERVER": {"add_opc_tags": "OPC-Tag hinzufügen", "anonymous": "Anonym", "auth_setting": "Authentifizierungseinstellungen", "certificate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "certificate_link": "<PERSON><PERSON><PERSON><PERSON><PERSON> und Verwalten von Zertifikaten aus dem", "change_authentication_password": "Authentifizierungskennwort ändern", "change_password": "Ändere das Passwort", "control_panel": "MXview One-Systemsteuerung", "create_tags_fail": "Unable to add tag", "create_tags_success": "Tag erfolgreich erstellt", "delete_tag_content": "Möchten Sie dieses OPC-Tag wirklich löschen?", "delete_tags": "OPC-Tag löschen", "delete_tags_content": "Möchten Sie diese OPC-Tags wirklich löschen?", "delete_tags_fail": "Tags konnten nicht gelöscht werden", "delete_tags_success": "Tags erfolgreich gelöscht", "device_property": "Device Property", "disabled": "<PERSON><PERSON><PERSON><PERSON>", "edit_opc_tags": "OPC-Tag bearbeiten", "edit_tags_fail": "Tag konnte nicht aktualisiert werden", "edit_tags_success": "Tag erfolgreich aktualisiert", "enable_opc_server": "OPC UA-Server aktivieren", "enabled": "Freigegeben", "exceed_server_performance": "Maximale Anzahl registrierter Geräte (4000) erreicht.", "get_tags_list_fail": "Die Tag-Liste konnte nicht abgerufen werden.", "ip_domain_name": "IP/Domänenname", "method": "Verfahren", "opc_tags": "OPC Tags", "property_name": "Device Property", "registered_device": "Registered Device", "security": "Sicherheitsmodus", "security_placeholder": "<PERSON><PERSON> Si<PERSON>he<PERSON> zulassen", "server_settings": "Server Settings", "status": "status", "support_security_policy": "Unterstützte Sicherheitsrichtlinien", "tag_name": "Tag Name", "tag_name_duplicate": "Dieser Tag-Name existiert bereits", "tags_exceed_limit": "Maximale Anzahl an Tags (2000) erreicht.", "title": "OPC UA Server", "update_fail": "Die Einstellungen konnten nicht aktualisiert werden", "update_server_setting_fail": "Die Servereinstellungen konnten nicht aktualisiert werden.", "update_server_setting_fail_no_up": "Die Einstellungen konnten nicht aktualisiert werden. Die angegebene IP existiert nicht.", "update_server_setting_success": "Servereinstellungen erfolgreich aktualisiert", "username": "<PERSON><PERSON><PERSON><PERSON>"}, "PAGES_MENU": {"about": "Info", "administration": {"account_management": "Kontoverwaltung", "device_settings_template": "Standardgerätevorlage", "global_device_settings": "Globale Geräteeinstellungen", "license_management": "Lizenz-Verwaltung", "maintenance_scheduler": "Wartungsplaner", "preferences": "Voreinstellungen", "system_settings": "System-Einstellungen", "title": "Verwaltung", "troubleshooting": "Fehlersuche"}, "alert": {"custom_events": "Custom Events", "device_threshold": "<PERSON><PERSON>", "event_settings": "Ereigniseinstellungen", "link_threshold": "<PERSON>", "notifications": "<PERSON>er<PERSON><PERSON><PERSON> von Benachrichtigungen", "title": "Alarm"}, "cli_object_database": {"title": "Gespeicherte CLI-Skripte"}, "dashboard": "Dashboard", "device_management": {"account_password": "Konten und Passwörter", "configuration_control": "Konfiguration und Steuerung", "title": "Geräteverwaltung"}, "devices": {"device_configurations": "Device Configurations", "list_of_devices": "List of Devices"}, "event": {"all_events": "Ereignishistorie", "custom_events_management": "<PERSON><PERSON><PERSON><PERSON>", "notification_management": "Benachrichtigungsverwaltung", "syslog_settings": "Syslog-Einstellungen", "syslog_viewer": "Syslog Viewer", "title": "<PERSON><PERSON>ignis-Management"}, "firewall_policy_management": {"dos_descr": "Konfigurieren der DoS-Richtlinie", "ips_descr": "IPS-Richtlinie konfigurieren", "layer3to7_descr": "Konfigurieren der Layer 3-7-Firewall-Rich<PERSON><PERSON>e", "policy_profile_deployment": "Bereitstellung von Richtlinienprofilen", "policy_profile_management": "Richtlinienprofilverwaltung", "security_package_deployment": "Bereitstellung von Sicherheitspaketen", "security_package_management": "Sicherheitspaketverwaltung", "sessionControl_descr": "Konfigurieren der Sitzungssteuerungsrichtlinie", "title": "Firewall-Richtlinienverwaltung"}, "firmware_management": {"title": "Firmware-Management"}, "help": {"about_mxview": "Über MXview One", "api_documentation": "API-Dokumentation", "title": "<PERSON><PERSON><PERSON>", "user_manual": "Benutzerhandbuch"}, "license": "<PERSON><PERSON><PERSON>", "links": {"list_of_rj45_links": "List of RJ45 Links", "list_of_sfp_links": "List of SFP Links", "list_of_wifi_links": "List of Wi-Fi Links", "title": "Links"}, "migrations": {"configuration_center": "Gerätekonfigurationszentrum", "database_backup": "Datenbanksicherung", "job_scheduler": "Wartungsplaner", "title": "<PERSON><PERSON><PERSON>"}, "network": {"scan_range": "Scan-Be<PERSON>ich", "title": "Netzwerk", "topology": "Topologie", "wizard": "Assistent"}, "northbound_interface": {"custom_opc_tags": "Benutzerdefinierte OPC-Tags", "opc_ua_server": "OPC UA Server", "restful_api_management": "RESTful API Management", "title": "Integration", "web_widget_embedded": "Eingebettetes Web-Widget"}, "preferences": "Voreinstellungen", "report": {"assets_and_warranty": "Vermögenswerte und Garantie", "availability_report": "Verfügbarkeitsbericht", "inventory_report": "Lagerbestandsbericht", "rogue_device_detection": "Erkennung nicht autorisierter Geräte", "title": "Berichte", "vlan": "VLAN-Bericht"}, "scan_range_wizard": {"title": "Geräteentdeckung"}, "security": {"account_management": "Kontoverwaltung", "security_analyser": "Sicherheitsanalysator", "title": "Sicherheit:"}}, "pages": {"deviceDeployment": {"alreadySentSms": "In diesem Monat bereits {{ smsNumber }}/{{ max }} SMS versendet", "applied": "Ang<PERSON><PERSON><PERSON>", "atLeastSelectOne": "Wählen Sie mindestens einen SMS-Steuerbefehl aus", "cellularModuleDisable": "Das Mobilfunkmodul ist deaktiviert, das Senden von SMS-Befehlen ist nicht zulässig.", "cellularStartConnecting": "Beginnen Sie mit der Mobilfunkverbindung", "cellularStopConnecting": "Mobilfunkverbindung unterbrochen", "configSync": "Die Konfiguration des/der ausgewählten Geräte(s) wird synchronisiert.", "daily": "Tä<PERSON><PERSON>", "date": "Datum", "deleteMsg": "Möchten Sie die ausgewählten Geräte wirklich löschen?", "deleteSchedule": "Zeitplan löschen", "deleteScheduleSuccess": "Gerätezeitplan erfolgreich gelöscht", "deleteSuccess": "Gerät(e) erfolgreich <PERSON>t", "deleteTitle": "Gerät(e) löschen", "device_ip": "Geräte-IP", "deviceConfiguration": "Gerätekonfiguration", "deviceDetail": "G<PERSON><PERSON><PERSON><PERSON>", "deviceDisableHint": "Funktion ist geräteseitig deaktiviert", "deviceSelected": "ausgewählte(s) Gerät(e)", "endTime": "<PERSON><PERSON><PERSON>", "firmware": "Firmware", "firmwareUpgrade": "Die Firmware des/der ausgewählten Geräte(s) wird aktualisiert.", "firmwareVersion": "Firmware Version", "general": "General", "groupName": "Gruppenname", "groupSelected": "ausgewählte Gruppe(n)", "invalidDate": "Ungültiges Datum", "invalidPeriod": "Ungültiger Zeitraum", "lastRebootTime": "Letzter Neustart", "lastUpdate": "Letztes Update", "lastUpdateTime": "Letzte Aktualisierung", "location": "Stelle", "mac": "MAC", "manually": "<PERSON><PERSON>", "maxSms": "Sie haben das monatliche SMS-Limit erreicht (MAX. {{ max }})", "noConfigAvailable": "<PERSON><PERSON> Konfigu<PERSON>en verfügbar", "noConfigMsg": "Überprüfen Sie die Konfigurationen auf der Seite „Verwaltung/Gerätekonfiguration“.", "noFirmwareMsg": "Überprüfen Sie die Firmware-Dateien auf der Seite „Verwaltung/Firmware“.", "noPackageMsg": "Überprüfen Sie die Sicherheitspakete auf der Seite „Sicherheitspaketverwaltung“.", "noProfileAvailable": "Keine Profile verfügbar", "noProfileMsg": "Überprüfen Sie die Profile auf der Seite „Richtlinienprofilverwaltung“.", "notSupportModel": "<PERSON><PERSON> un<PERSON>tütz<PERSON> Modell", "notSync": "Nicht synchronisiert", "noVersionAvailable": "Keine Version verfügbar", "oneTime": "Ein<PERSON>ig", "outOfSync": "<PERSON>cht synchron", "package": "<PERSON><PERSON>", "packageUpgrade": "Das Sicherheitspaket des/der ausgewählten Geräte(s) wird aktualisiert.", "packageVersion": "Paketversion", "period": "Zeit", "policyProfile": "Richtlinienprofile", "processing": "Verarbeitung", "profileName": "Profilname", "profileSync": "Das Profil des/der ausgewählten Geräte(s) wird synchronisiert.", "reboot": "Das/die Gerät(e) werden neugestartet.", "rebootDisabled": "Nur Online-Geräte können neu gestartet werden.", "rebootMsg": "<PERSON>öchten Sie die ausgewählten Geräte wirklich neu starten?", "rebootTitle": "Ger<PERSON>(e) neu starten", "remoteSmsControl": "Fernsteuerung per SMS", "restoreConfigDisabled": "Es können nur Online-Geräte des gleichen Modelltyps synchronisiert werden.", "sameVersionWarning": "Auf einem oder mehreren der ausgewählten Geräte ist bereits die Version {{ version }} angewendet.", "schedule": "Zeitplan", "scheduleDisabled": "Es können nur Geräte des gleichen Modelltyps eingeplant werden.", "scheduleOverlapMsg": "Der Zeitschlitz, der bereits den Neustart oder das Firmware-Upgrade zugewiesen hat, kann nicht ausgewählt werden.", "scheduleSettings": "Zeitplaneinstellungen", "scheduling": "Terminplanung", "schedulingMode": "Planungsmodus", "schedulingPeriod": "Planungszeitraum", "schedulingReboot": "Neustart planen", "selectConfigFile": "Konfigurationsdatei auswählen", "selectFile": "<PERSON><PERSON> aussuchen", "sendSms": "SMS senden", "sendSmsControl": "SMS-Steuerung senden", "sendSmsOnCell": "<PERSON>ä<PERSON>en Sie ein OnCell-Gerät zum Senden der SMS-Steuerung aus", "sendSmsSuccess": "SMS erfolgreich versenden", "serialNumber": "SN", "setDoOff": "DO ausschalten", "setDoOn": "DO einschalten", "shouldBeSameVersion": "Die Paketversion der ausgewählten Geräte sollte identisch sein.", "shouldHaveJanus": "Auf einem oder mehreren der ausgewählten Geräte ist kein Sicherheitspaket installiert.", "shouldSyncOnline": "Es können nur Online-Geräte synchronisiert werden.", "showAll": "Alle Gruppen und Geräte anzeigen", "showSelected": "Ausgewählte Gruppen und Geräte anzeigen", "smsCountDownHint": "Nächste SMS nach 60 Sekunden senden", "softwarePackage": "Sicherheitspakete", "startIpsecTunnel": "IPsec-Tunnel starten", "startTime": "Anfangsdatum", "status": "Status", "statusProfileName": "Status/Profilname", "stopIpsecTunnel": "IPsec-Tunnel stoppen", "switchSim": "SIM wechseln", "sync": "Synchronisiert", "syncConfig": "Synchronisierungskonfiguration", "syncConfigTitle": "Konfiguration mit Gerät(en) synchronisieren", "syncModified": "Synchronisiert (geändert)", "syncProfile": "Profil synchroni<PERSON>", "syncProfileTitle": "Profil mit Gerät(en) synchronisieren", "systemRestart": "Systemneustart", "time": "Zeit", "updateScheduleSuccess": "Gerätezeitplan erfolgreich aktualisiert.", "upgradeDisabled": "Nur Online-Geräte können aktualisiert werden.", "upgradePackageError": "Firmware-Versionen über 2.5.0 und unter 2.4.x können nicht koexistieren.", "upgradePackageNotSameDisabled": "Es können nur Geräte des gleichen Modelltyps ausgewählt werden", "upToDate": "<PERSON><PERSON> <PERSON>", "version": "Version", "weekly": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "weeklyDay": "Wochentag"}, "logging": {"eventLog": {"adp": "ADP", "audit": "Prüfung", "device": "G<PERSON><PERSON>", "dos": "DoS-Richtl<PERSON><PERSON>", "dpi": "Protokollfilterrichtlinie", "endDate": "<PERSON><PERSON><PERSON>", "endTime": "Endzeit", "event": "Veranstaltung", "firewall": "Firewall", "ips": "IPS", "l2Policy": "Layer 2-<PERSON><PERSON><PERSON><PERSON>", "l3Policy": "Layer 3-7-<PERSON><PERSON><PERSON><PERSON>", "malformed": "Fehlerhafte Pakete", "sc": "Sitzungssteuerung", "setting": "Einstellung", "severity": "Schwere", "startDate": "Anfangsdatum", "startTime": "Startzeit", "tab": {"audit": {"deviceName": "G<PERSON><PERSON><PERSON><PERSON>", "event": "Veranstaltung", "groupName": "Gruppenname", "message": "<PERSON><PERSON><PERSON>", "severity": "Schwere", "time": "Zeit", "username": "<PERSON><PERSON><PERSON><PERSON>"}, "device": {"deviceName": "G<PERSON><PERSON><PERSON><PERSON>", "event": "Veranstaltung", "groupName": "Gruppenname", "mac": "MAC-Adresse", "message": "<PERSON><PERSON><PERSON>", "severity": "Schwere", "time": "Zeit", "username": "<PERSON><PERSON><PERSON><PERSON>"}, "firewall": {"action": "Handlung", "adp": "ADP", "all": "Alles", "appProtocol": "Anwendungsprotokoll", "category": "<PERSON><PERSON><PERSON>", "deviceName": "G<PERSON><PERSON><PERSON><PERSON>", "dos": "DoS-Richtl<PERSON><PERSON>", "dpi": "Protokollfilterrichtlinie", "dstIp": "Ziel-IP", "dstMac": "Ziel-MAC", "dstPort": "Zielport", "etherType": "EtherType", "event": "Veranstaltung", "fromInterface": "Eingehende Schnittstelle", "groupName": "Gruppenname", "icmpCode": "ICMP-Code", "icmpType": "ICMP-Typ", "id": "Index", "ips": "IPS", "ipsCategory": "IPS-<PERSON><PERSON><PERSON>", "ipsSeverity": "IPS-Schweregrad", "l3Policy": "Layer 3-7-<PERSON><PERSON><PERSON><PERSON>", "malformed": "Fehlerhafte Pakete", "message": "Zusätzliche Nachricht", "policyId": "Richtlinien-ID", "policyName": "Versicherungsname", "protocol": "IP-Protokoll", "security": "Sicherheit", "sessionControl": "Sitzungssteuerung", "severity": "Schwere", "srcIp": "Quell-IP", "srcMac": "Quell-MAC", "srcPort": "Quellport", "subCategory": "Unterkategorie", "tcpFlag": "TCP-Flags", "time": "Zeit", "toInterface": "Ausgehende Schnittstelle", "trustAccess": "Vertrauenswürdiger Zugriff", "username": "<PERSON><PERSON><PERSON><PERSON>", "vlanId": "VLAN-ID"}, "vpn": {"deviceName": "G<PERSON><PERSON><PERSON><PERSON>", "event": "Veranstaltung", "groupName": "Gruppenname", "message": "Zusätzliche Nachricht", "severity": "Schwere", "time": "Zeit", "username": "<PERSON><PERSON><PERSON><PERSON>"}}, "trustAccess": "Vertrauenswürdiger Zugriff", "vpn": "VPN"}, "notification": {"advancedSettingMsg": "<PERSON>bald die maximale Anzahl an Benachrichtigungen in einem Zeitraum erreicht wurde, werden bis zum nächsten Zeitraum keine weiteren Benachrichtigungen gesendet.", "advancedSettings": "Erweiterte Einstellungen", "appProtocol": "Anwendungsprotokoll", "arpFlood": "ARP-Flood", "atLeastOneReceiver": "Mindestens ein Empfänger", "bufferOverflow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chooseDevices": "Geräte auswählen", "createdBy": "<PERSON><PERSON><PERSON><PERSON> von", "createNotification": "Cybersecurity-<PERSON><PERSON><PERSON><PERSON>", "createSuccess": "Cybersecurity-Event erfolgreich erstellt", "deleteFailed": "Die<PERSON> E<PERSON>ign<PERSON> wird für Benachrichtigungen verwendet und kann nicht gelöscht werden.", "deleteKey": "Cybersicherheitsereignis(se)", "deleteNotification": "Benachrichtigung löschen", "deleteSuccess": "Cybersicherheitsereignis(se) erfolgreich gelöscht", "deviceCount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deviceName": "G<PERSON><PERSON><PERSON><PERSON>", "DNP3": "DNP3", "dosAttacks": "DoS<PERSON><PERSON><PERSON><PERSON>", "dstIp": "Ziel-IP", "dstMac": "Ziel-MAC", "editNotification": "Cybersicherheitsereign<PERSON> bearbeiten", "EIP": "EIP", "email": "Email", "emailContent": "E-Mail-Inhalt", "emailContentDefault": "Das Ereignis ${event}, das auf dem Gerät ${productModel}, ${deviceName}, ausgelöst wurde, ist um ${eventTime} eingetreten.", "emailHeader": "[MXsecurity] Benachrichtigung ${notificationName}\ngeneriert von ${deviceName}", "emailMsgAutoSentFrom": "Diese Benachrichtigung wurde automatisch gesendet\nvon MXsecurity.", "emailMsgCheck": "Bitte prüfen Sie die Detailinformationen\nauf MXsecurity.", "emailMsgGreeting": "<PERSON><PERSON> geehrte Damen und Herren,", "emailMsgSignOff": "Beste grüße,\nMX-Sicherheit", "eq": "<PERSON><PERSON><PERSON>", "event": "Veranstaltung", "event_used": "Die<PERSON> wird bereits in den Benachrichtigungseinstellungen verwendet und kann nicht geändert werden.", "eventFilter": "<PERSON><PERSON><PERSON><PERSON> und Filter auswählen", "eventFilterRule": "Ereignisfilterregel", "eventTime": "Ereigniszeit", "exploits": "Exploits", "fileVulnerabilities": "Dateischwachstellen", "filterRule": "<PERSON><PERSON><PERSON><PERSON>", "filterRuleDetail": "Filterregel(n) Detail", "finScan": "FIN Scan", "floodingScan": "<PERSON>n und Scannen", "GOOSE": "GOOSE", "gt": "<PERSON><PERSON><PERSON><PERSON> als", "gte": "<PERSON>er oder gleich", "icmpDeath": "ICMP-Flood", "IEC-104": "IEC-104", "ipAddress": "IP-Adresse", "ipRangeHint": "<PERSON><PERSON> können * verwen<PERSON>, um das Ergebnis darzustellen\nder Subnetzmaske /8/16/24, zB.\n192.168.*.*\nKonnte nicht verwendet werden zu Beginn\neine IP-Adresse oder alleine in der Mitte *", "ipsCate": "IPS-<PERSON><PERSON><PERSON>", "ipSpoofing": "IP-Spoofing", "ipsSeverity": "IPS-Schwere", "location": "Stelle", "lt": "<PERSON><PERSON><PERSON> als", "lte": "<PERSON><PERSON><PERSON><PERSON><PERSON> oder gleich", "macAddress": "MAC-Adresse", "macRangeHint": "<PERSON><PERSON> können * ver<PERSON><PERSON>, um einen Bereich darzustellen von\nMAC-Adresse, zB. 00:90:E8:*:*:*\nKonnte nicht am Anfang eines\nMAC-Adresse oder alleine in der Mitte.", "malwareTraffic": "Malware-Verkehr", "maxEnableSize": "Die maximale Anzahl aktivierter Benachrichtigungen beträgt {{num}}.", "maxNotification": "MAX. Benachrichtigung", "maxPerUserSize": "Die MAXIMALE Anzahl an Benachrichtigungen pro Benutzer beträgt {{num}}.", "MMS": "MMS", "Modbus/TCP": "Modbus/TCP", "newTcpWithoutSynScan": "TCP Sessions Without SYN", "nmapIdScan": "NMAP-ID Scan", "nmapXmasScan": "NMAP-<PERSON><PERSON>", "notificationActions": "Benachrichtigungsaktionen", "notificationEvent": "Benachrichtigungsereign<PERSON>", "notificationInfo": "Benachrichtigungsinformationen", "notificationLimit": "Benachrichtigungslimit", "notificationName": "Veranstaltungsname", "nullScan": "<PERSON><PERSON>", "OmronFINS": "Omron FINS", "periodTime": "Zeitspanne", "policyName": "Versicherungsname", "productModel": "Produktmodell", "protocolAttackProtection": "Schutz vor Protokollangriffen", "receiverEmailAddress": "E-Mail-Adresse des Empfängers", "receiverSetting": "Empfängereinstellungen", "reconnaissance": "Aufklärung", "resetToDefault": "Auf Werkseinstellungen zurück setzen", "serialNumber": "SN", "severity": "Schwere", "severityMode": "Schweregradmodus", "severityRule": "Schweregradregel", "showAllDevices": "Alle Geräte anzeigen", "showSelectedDevices": "Ausgewählte Geräte anzeigen", "srcIp": "Quell-IP", "srcMac": "Quell-MAC", "Step7Comm": "Step7Comm", "subCate": "Unterkategorie", "synFinScan": "SYN/FIN Scan", "synFlood": "SYN-Flood", "synRstScan": "SYN/RST Scan", "syslog": "Syslog", "syslogContent": "Syslog-Inhalt", "syslogContentDefault": "Benachrichtigung ${notificationName} wurde auf Gerät ausgelöst. ${productModel}, ${deviceName}, geschah um ${eventTime}. Bitte prüfen Sie die Detailinformationen bei MXsecurity.", "udpFlood": "UDP-Flood", "updateSuccess": "Cybersecurity-Event erfolgreich aktualisiert", "webThreats": "Bedrohungen aus dem Internet", "xmasScan": "<PERSON><PERSON>"}}, "management": {"deviceConfiguration": {"configModel": "Konfigurationsmodell", "configName": "Name konfigurieren", "createSuccess": "Gerätekonfiguration erfolgreich erstellt.", "deleteKey": "Gerätekonfiguration(en)", "deleteSuccess": "Gerätekonfiguration(en) erfolgreich gelöscht", "editConfig": "Konfiguration bearbeiten", "enterConfigInfo": "Geben Sie die Informationen zur Konfigurationsdatei ein", "firmwareVersion": "Firmware Version", "group": "Gruppe", "isReferenced": "Auf eine oder mehrere der ausgewählten Konfigurationen wird verwiesen.", "lastModifiedTime": "Letzte Änderung", "location": "Stelle", "mac": "MAC-Adresse", "maxTableSize": "Die maximale Konfiguration beträgt {{num}}.", "noModelMsg": "<PERSON><PERSON> gibt kein Modell für die Konfiguration", "offlineWarning": "Gerät offline", "onlyAcceptIni": "Es werden nur Konfigurationsdateien im ' .ini ' -Format akzeptiert.", "onlyOneFilePerTime": "<PERSON>s kann immer nur eine Datei hochgeladen werden.", "selectConfigFile": "Konfigurationsdatei auswählen", "selectWarning": "Ermöglicht nur ein Gerät zur Konfigurationssicherung", "serialNumber": "SN", "updateSuccess": "Geräteeinstellungen erfolgreich aktualisiert", "uploadConfigFile": "Konfigurationsdatei (.ini) hochladen", "uploadConfigMethod": "Upload-Konfigurationsmethode", "uploadConfigTitle": "Gerätekonfigurationsdatei hochladen", "uploadDeviceConfig": "Konfiguration vom Gerät hochladen", "uploadLocalConfig": "Konfiguration vom lokalen Gerät hochladen"}, "deviceGroup": {"accessPermission": "Zugriffsberechtigung", "addDevices": "G<PERSON><PERSON><PERSON> hinzufü<PERSON>", "adminPermission": "Administratorbenutzer haben Zugriff auf alle Gruppen", "createGroup": "Gruppe er<PERSON>llen", "createSuccess": "Gerätegruppe erfolgreich erstellt.", "deleteKey": "Gerätegruppe(n) löschen", "deleteSuccess": "Gerätegruppe(n) erfolgreich gelöscht.", "description": "Beschreibung", "deviceCount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "editGroup": "Gerätegruppe bearbeiten", "enterGroupInfo": "Gruppeninformationen eingeben", "firmwareVersion": "Firmware Version", "grantAccessPermission": "Zugriffsberechtigung erteilen", "group": "Gruppe", "groupName": "Gruppenname", "location": "Stelle", "mac": "MAC", "role": "<PERSON><PERSON>", "serialNumber": "SN", "showAllDevices": "Alle Geräte anzeigen", "showSelectedDevices": "Ausgewählte Geräte anzeigen", "status": "Status", "updateSuccess": "Gerätegruppe erfolgreich aktualisiert.", "username": "<PERSON><PERSON><PERSON><PERSON>"}, "firmware": {"buildTime": "Bauzeit", "deleteKey": "Firmware", "deleteSuccess": "Firmware erfolgreich gelöscht.", "description": "Beschreibung", "dropZoneTitle": "Laden Sie eine Firmware-Datei (.rom) hoch.", "isReferenced": "Es wird auf eine oder mehrere der ausgewählten Firmwares verwiesen.", "maxRowMsg": "Die maximale Anzahl an Firmware-<PERSON><PERSON> beträgt {{ max }}.", "maxSize": "Die maximal zulässige Dateigröße beträgt 1 GB.", "modelSeries": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onlyAcceptRom": "Es werden nur Firmwaredateien im ' .rom ' -Format akzeptiert.", "onlyOneFilePerTime": "<PERSON>s kann immer nur eine Datei hochgeladen werden.", "uploadFirmware": "Firmware hochladen", "uploadSuccess": "Firmware erfolgreich hochgeladen.", "version": "Version"}, "inUse": "<PERSON>a", "object": {"filter": {"address": "IP-Adresse und Subnetz", "code": "Code", "createObject": "Objekt erstellen", "createSuccess": "Objekt erfolgreich erstellt", "customIpProtocol": "Benutzerdefiniertes IP-Protokoll", "decimal": "(Dezimal)", "deleteKey": "Objekt(e)", "deleteSuccess": "Objekt(e) erfolgreich gelöscht.", "detail": "Details", "editObject": "Objekt bearbeiten", "endPort": "Hafen: <PERSON><PERSON>", "icmp": "ICMP", "icmpCode": "ICMP-Code", "icmpType": "ICMP-Typ", "industrialAppService": "Industrieller Anwendungsservice", "ipAddress": "IP-Adresse", "ipEnd": "IP-Adresse: <PERSON><PERSON>", "ipProtocol": "IP-Protokoll", "ipRange": "IP-Bereich", "ipStart": "IP-Adresse: Start", "ipType": "IP-Typ", "isReferenced": "Ein oder mehrere der ausgewählten Objekte werden referenziert", "leaveAsAny": "<PERSON><PERSON> das Feld leer, um „Beliebig“ darzustellen.", "maxRowMsg": "Die maximale An<PERSON><PERSON> von Objekten beträgt {{ max }}.", "name": "Filter", "needSelectedMsg": "<PERSON><PERSON><PERSON>en Sie mindestens ein Element aus", "networkName": "Netzwerkname", "networkService": "Netzwerkdienst", "objectName": "Name", "objectReference": "Objektreferenzen", "objectReferenceMsg": "<PERSON><PERSON> dieses Objekt wird durch einen Richtlinienindex in den folgenden Profilen verwiesen:", "objectType": "Objekttyp", "port": "Port", "portRange": "TCP- und UDP-Portbereich", "selectIndustrialAppService": "Wählen Sie industrielle Anwendungsdienstleistungen aus *", "selectNetworkService": "Netzwerkdienst(e) auswählen *", "servicePortType": "Service-Port-Typ", "singleIp": "Einzelne IP", "singlePort": "TCP- und UDP-Port", "startPort": "Hafen: Start", "subnet": "Subnetz", "subnetMask": "Subnetzmaske", "tcp": "TCP", "tcpUdp": "TCP und UDP", "type": "Art", "udp": "UDP", "updateSuccess": "Objekt erfolgreich aktualisiert", "userDefinedService": "Benutzerdefinierter Dienst"}, "interface": {"bridge": "<PERSON><PERSON><PERSON><PERSON>", "createInterface": "Schnittstellenobjekt erstellen", "createSuccess": "Schnittstelle erfolgreich erstellt.", "deleteKey": "Schnittstelle(n)", "deleteSuccess": "Schnittstelle(n) erfolgreich gelöscht.", "editInterface": "Schnittstellenobjekt bearbeiten", "interfaceName": "Schnittstellenname", "interfaceReference": "Schnittstellenverweise", "interfaceReferenceMsg": "Au<PERSON> diese Schnittstelle wird durch einen Richtlinienindex in den folgenden Profilen verwiesen:", "invalidKey": "Die folgenden Namen sind reserviert: Any", "isReferenced": "Auf eine oder mehrere der ausgewählten Schnittstellen wird verwiesen.", "maxRowMsg": "Die maximale Anzahl an Schnittstellen beträgt {{ max }}.", "mode": "Modus", "name": "Schnittstelle", "port": "Portbasiert", "updateSuccess": "Schnittstelle erfolgreich aktualisiert.", "vlan": "VLAN", "vlanIdBridgeType": "VLAN-ID/Bridge-Modus", "zone": "Zonenbasiert"}}, "policyProfile": {"createProfile": "Richtlinienprofil erstellen", "createSuccess": "<PERSON>il er<PERSON><PERSON><PERSON><PERSON>ich erstellt.", "deleteKey": "Profil(e)", "deleteSuccess": "Profil(e) erfolgreich <PERSON>t.", "deployment": {"profile_title": "Bereitstellung von Richtlinienprofilen", "security_title": "Bereitstellung von Sicherheitspaketen", "title": "Bereitstellung von Richtlinienprofilen"}, "dos": {"all_protection_types": "Alle Schutztypen aktivieren", "dosLogSetting": "DoS-Log-Einstellungen", "dosSetting": "DoS-Einstellungen", "floodProtection": "Hochwasserschutz", "limit": "<PERSON><PERSON><PERSON>", "portScanProtection": "Port-Scan-Schutz", "sessionSYNProtection": "Session-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat1": "<PERSON><PERSON>", "stat10": "SYN-Flood", "stat11": "ARP-Flood", "stat12": "UDP-Flood", "stat2": "<PERSON><PERSON>", "stat3": "NMAP-<PERSON><PERSON>", "stat4": "SYN/FIN Scan", "stat5": "FIN Scan", "stat6": "NMAP-ID Scan", "stat7": "SYN/RST Scan", "stat8": "TCP Sessions Without SYN", "stat8Tooltip": "Einschränkung: Bei asymmetrischen Netzwerkarchitekturen und aktiviertem NAT wird dringend empfohlen, „TCP-Sitzungen ohne SYN“ nicht zu deaktivieren, um unerwartete Verbindungsabbrüche zu vermeiden.", "stat9": "ICMP-Flood", "title": "DOS"}, "editProfile": "Richtlinienprofil bearbeiten", "ips": {"accept": "Akzeptieren", "category": "<PERSON><PERSON><PERSON>", "custom": "(<PERSON><PERSON><PERSON>)", "id": "Identifikation", "impact": "Einfluss", "monitor": "Monitor", "noPackageMsg": "Überprüfen Sie die Sicherheitspakete auf der Seite „Sicherheitspaketverwaltung“.", "noVersionAvailable": "Keine Version verfügbar", "packageVersion": "Paketversion", "reference": "<PERSON><PERSON><PERSON><PERSON>", "reset": "RESET", "ruleSetting": "Regeleinstellungen", "title": "IPS", "updateSuccess": "Regel(n) erfolgreich aktualisiert.", "warningMsg": "<PERSON><PERSON><PERSON> vor der Konfiguration von Richtlinien sicher, dass die Funktion Intrusion Prevention System (IPS) auf dem Bildschirm „Firewall“ > „Erweiter<PERSON> Schutz“ > „Konfiguration“ der Weboberfläche des Geräts aktiviert ist."}, "isReferenced": "Auf ein oder mehrere der ausgewählten Profile wird verwiesen.", "layer3to7": {"allowAll": "Alles erlauben", "createPolicy": "<PERSON>er 3-7-<PERSON><PERSON><PERSON><PERSON>", "createSuccess": "Layer 3-7-<PERSON><PERSON><PERSON><PERSON> erfolg<PERSON>ich erstellt.", "default_action_log": "<PERSON><PERSON>ignisp<PERSON><PERSON>ll", "default_action_log_destination": "Protokollziel", "default_action_severity": "Schwere", "defaultAction": "Handlung", "deleteKey": "Layer 3-7-<PERSON><PERSON><PERSON><PERSON>(n)", "deleteSuccess": "Layer 3-7-<PERSON><PERSON><PERSON><PERSON>(n) erfolgreich gel<PERSON>.", "deleteTitle": "Layer 3-7-<PERSON><PERSON><PERSON><PERSON>", "denyAll": "<PERSON><PERSON>", "destinationAddress": "Zieladresse", "destinationPort": "Zielport oder Protokoll", "destIpAddress": "Ziel-IP-Adresse", "destService": "Zielortservice", "editPolicy": "Layer 3-7-<PERSON><PERSON><PERSON><PERSON> bear<PERSON>", "enforce": "Status", "enforcement": "Status", "event": "Veranstaltung", "eventSetting": "Standardrichtlinieneinstellungen", "filterMode": "Filtermodus", "globalSetting": "Globale Firewall-Einstellungen", "incomingInterface": "Eingehende Schnittstelle", "ipAndPortFiltering": "IP- und Port-Filterung", "ipAndSourceMacBinding": "IP- und Quell-MAC-Bindung", "ipTypeError": "Das Quellport-IP-Protokoll ({{ source }}) unterscheidet sich vom Zielport-IP-Protokoll ({{ dest }}).", "maxRowMsg": "Die maximale An<PERSON><PERSON> von Objekten beträgt {{ max }}.", "outgoingInterface": "Ausgehende Schnittstelle", "policyName": "Name", "protocolService": "Protokoll und Dienst", "sourceAddress": "Quelladresse", "sourceIpAddress": "Quell-IP-Adresse", "sourceMacAddress": "Quell-MAC-Adresse", "sourceMacFiltering": "Quell-MAC-Filterung", "sourcePort": "Quellport", "title": "Schicht 3-7", "updateSuccess": "Layer 3-7-<PERSON><PERSON><PERSON><PERSON> erfolgreich aktualisiert."}, "maxRowMsg": "Die maximale Anzahl an Profilen beträgt {{ max }}.", "profileName": "Profilname", "profileReference": "Profilreferenzen", "profileReferenceMsg": "<PERSON><PERSON> dieses Profil wird von den folgenden Geräten verwiesen:", "sessionControl": {"concurrentTcp": "Gleichzeitige TCP-Verbindungen", "connectionsRequestUnit": "Anschlüsse", "connectionsUnit": "Anschlüsse", "createPolicy": "Sitzungssteuerungsrichtlinie erstellen", "createSuccess": "Sitzungssteuerungsrichtlinie erfolgreich erstellt.", "deleteKey": "Sitzungssteuerungsrichtlinie(n)", "deleteSuccess": "Sitzungssteuerungsrichtlinie(n) erfolgreich gelöscht.", "destinationIp": "Ziel-IP", "destinationPort": "Zielport", "destIpAddress": "IP-Adresse", "destPort": "Port", "drop": "Tropfen", "editPolicy": "Sitzungssteuerungsrichtlinie bearbeiten", "enforcement": "Status", "maxRowMsg": "Die maximale Anzahl an Richtlinien für dieses Gerät beträgt {{ max }}.", "monitor": "Monitor", "sub_title": "Netzwerkhost- und Serviceressourcenschutz", "tcpConnectionLimit": "TCP-Verbindungslimit", "tcpDestError": "IP-Adresse und Port können nicht gleichzeitig beliebig sein", "tcpDestination": "TCP-Ziel", "tcpLimitError": "Sie müssen mindestens eine Einschränkung konfigurieren", "tcpLimitMsg": "Mindestens eine Einschränkung ist erforderlich", "title": "Sitzungssteuerung", "totalTcp": "Gesamtzahl der TCP-Verbindungen", "updateSuccess": "Sitzungssteuerungsrichtlinie erfolgreich aktualisiert."}, "tabInspection": "Prüfobjekte", "tabInterface": "Schnittstellenobjekte", "tabPolicyProfile": "Richtlinienprofile", "title": "Richtlinienprofilverwaltung", "updateSuccess": "Profil er<PERSON><PERSON>g<PERSON>ich aktualisiert."}, "scheduleInUse": "Zeitplan in Verwendung", "scheduling": "Terminplanung", "softwarePackage": {"applicationProducts": "Anwendbare Produkte", "auto-download": "Automatischer Download", "bugsFixed": "<PERSON><PERSON><PERSON><PERSON>", "buildTime": "Bauzeit", "changes": "Änderungen", "checkConnection": "Überprüfen Sie den Verbindungsstatus zum Moxa-Updateserver.", "checkNewPackage": "Sucht auf dem MOXA-Server nach neuen Paketversionen.", "checkSoftwarePackage": "Suchen Sie nach Paketaktualisierungen", "daily": "Tä<PERSON><PERSON>", "deleteKey": "Sicherheitspaket(e)", "deleteSuccess": "Sicherheitspaket(e) erfolgreich gelöscht.", "description": "Beschreibung", "detailInfo": "Detaillierte Informationen", "dropZoneTitle": "<PERSON><PERSON><PERSON><PERSON> einer Paketdatei (.pkg)", "endDate": "<PERSON><PERSON><PERSON>", "endTime": "Endzeit", "enhancements": "Verbesserungen", "event": "Veranstaltung", "isReferenced": "Auf ein oder mehrere der ausgewählten Pakete wird verwiesen.", "janus": "Netzwerksicherheitspakete", "lastConnectionCheck": "Letzte Verbindungsprüfung", "lastSoftwarePackageUpdateResult": "Ergebnis der letzten Aktualisierung des Sicherheitspakets", "licenseActivationReminder": "Erinnerung zur Lizenzaktivierung", "licenseActivationReminderContent": "Um erweiterte Sicherheitsmechanismen zu gewährleisten, aktivieren Si<PERSON> bitte die Lizenz, um diese Funktion zu aktivieren.", "licenseTransferReminder": "Erinnerung zur Lizenzübertragung", "licenseTransferReminderContent": "Um verbesserte Sicherheitsmechanismen zu gewährleisten, übertragen Sie bitte Ihre MXsecurity-<PERSON><PERSON>z, bevor Sie die Netzwerksicherheitspakete hochladen.", "local": "<PERSON><PERSON>", "log": "Ereignisprotokolle", "maxPackageMsg": "Maximale gleichzeitige Downloads: {{ max }} <PERSON><PERSON>.", "maxRowMsg": "Die maximale Anzahl an Softwarepaketen beträgt {{ max }}.", "maxSize": "Die maximal zulässige Dateigröße beträgt 1 GB.", "message": "<PERSON><PERSON><PERSON>", "newFeatures": "Neue Features", "notes": "Aufzeichnungen", "onlyAcceptPkg": "Es werden nur Dateien im ' .pkg ' -Format akzeptiert.", "onlyOneFilePerTime": "<PERSON>s kann immer nur eine Datei hochgeladen werden.", "packageDownloading": "Der Paket-Downloader funktioniert für andere", "packageReference": "Paketreferenzen", "packageReferenceMsg": "<PERSON><PERSON> dieses <PERSON>et wird von den folgenden Profilen verwiesen:", "period": "Zeit", "productModel": "Produktmodell", "releaseDate": "Veröffentlichungsdatum", "releaseNote": "Versionshinweise", "scheduling": "Geplante Update-Prüfung", "schedulingMode": "Planungsmodus", "server": "Moxa Update Server Status", "serverDisconnected": "Eine Überprüfung des Sicherheitspaketes ist nur bei bestehender Verbindung zum Server möglich.", "severity": "Schwere", "softwarePackageAlreadyLatest": "Sicherheitspaket ist auf dem neuesten Stand.", "softwarePackageCheck": "Sicherheitspaketprüfung", "softwarePackagesFile": "Sicherheitspaketdatei", "softwarePackagesUpdateCheck": "Sicherheitspaket aktualisieren", "startDate": "Anfangsdatum", "startTime": "Startzeit", "supportedFunctions": "Unterstützte Funktionen", "supportedOperatingSystems": "Unterstützte Betriebssysteme", "supportModel": "Unterstützte Modelle", "supportSeries": "Unterstützte Serien", "syncSettingNotSet": "Vervollständigen Sie die Synchronisierungseinstellungen, um das Sicherheitspaket zu überprüfen.", "syncSettings": "Geplante Update-Prüfung", "syncSettingUpdateSuccess": "Einstellungen erfolgreich aktualisiert.", "syncSoftwarePackageBySchedule": "Automatische Suche nach Sicherheitspaketaktualisierungen für die angegebenen Modelle basierend auf einem benutzerdefinierten Zeitplan.", "syncSoftwarePackageByScheduleTooltip": "Konfigurieren Sie die Häufigkeit, mit der der Moxa-Server auf Sicherheitspaketaktualisierungen überprüft werden soll.", "time": "Zeit", "title": "Sicherheitspaketverwaltung", "updateCheckTooltip": "Überprüfen Sie den Moxa-Server auf Sicherheitspaketaktualisierungen, um sicherzustellen, dass Sie die neueste Version verwenden.", "uploadBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "uploadSoftwarePackage": "<PERSON><PERSON> ho<PERSON>n", "uploadSuccess": "Sicherheitspaket erfolgreich hochgeladen.", "username": "<PERSON><PERSON><PERSON><PERSON>", "version": "Version", "weekday": "Tag", "weekly": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zeus": "MXsecurity Agent-Pak<PERSON>"}}}, "PORT_SETTING": {"another_port_setting_error": "Eine andere Einstellung wird gerade verarbeitet.", "apply_another_port": "Einstellungen auf einen anderen Port anwenden", "disable_port_warning": "Warnung: Durch Deaktivieren dieses Port werden die an diesen Port angeschlossenen Geräte getrennt.", "enable": "Freigeben", "get_port_setting_fail": "Die Port-Einstellungen können nicht abgerufen werden.", "hint": "* If the settings fail, please confirm that the selected port can be configured.", "media_type": "Media Type", "port": "Hafen", "port_description": "Port-Beschreibung", "port_name": "Port-Name", "port_select": "<PERSON>e haben gewählt", "set_fail": "Einige Ports können nicht aktualisiert werden. Bitte versuchen Sie es später noch einmal.", "set_success": "Alle Port-Einstellungen werden aktualisiert.", "title": "Einstellungen für Ethernet-/Glasfaser-Ports"}, "PREFERENCES": {"advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "appearance": "Darstellung", "default_view": {"choose_start_page": "Wählen Sie eine Startseite", "dashboard": "Dashboard", "title": "Standardansicht", "topology": "Topologie"}, "device_appearance": {"alias": "<PERSON><PERSON>", "bottom_hint": "Wenn Sie die Alias-Einstellung ändern, löschen Sie das Gerät in der Topologie und scannen Sie es erneut oder fügen Si<PERSON> ein Gerät hinzu, um die Alias-Einstellung abzuschließen.", "bottom_label": "<PERSON><PERSON><PERSON> Etikett", "bottom_label_items": {"alias": "<PERSON><PERSON>", "location": "Ort", "mac": "MAC", "model_name": "<PERSON><PERSON><PERSON>", "none": "<PERSON><PERSON>", "sysname": "SysName"}, "get_fail": "Die Gerätedarstellung kann nicht abgerufen werden.", "ip_address": "IP-Adresse", "set_fail": "Die Gerätedarstellung kann nicht eingestellt werden.", "set_success": "Die Gerätedarstellung wird aktualisiert.", "title": "<PERSON><PERSON>"}, "device": {"login": "Anmelden", "title": "G<PERSON><PERSON>"}, "dialog": {"desc": "Löschen Sie alle 'Die<PERSON> Nachricht nicht mehr anzeigen'-Einstellungen und zeigen Sie alle versteckten Dialoge wieder an", "title": "Dialog"}, "display": "<PERSON><PERSON>", "email_config": {"allow_selfsigned_cert": "Selbstsigniertes Zertifikat zulassen", "apply_fail": "Die E-Mail-Server-Konfiguration kann nicht eingestellt werden.", "apply_success": "Die E-Mail-Server-Konfiguration wird aktualisiert.", "encryption": "Verschlüsselung", "password": "Passwort", "port_number": "Port-Nummer", "sender_address": "Absenderad<PERSON>e", "server_domain_name": "Server-Domainname/IP", "title": "E-Mail-Server-Setup", "username": "<PERSON><PERSON><PERSON><PERSON>"}, "events": {"apply_fail": "Der Ereignis-Schwellenwert kann nicht eingestellt werden.", "apply_success": "Der Ereignis-Schwellenwert wird aktualisiert.", "availability_under": "Verfügbarkeit unterschritten", "bandwidth_utilization_over": "Bandbreitenauslastung überschritten", "bandwidth_utilization_under": "Bandbreitenauslastung unterschritten", "link_down": "Verbindung trennen", "link_up": "Verbindung herstellen", "packet_error_rate_over": "Paketfehlerrate überschritten", "port_looping": "Port-Looping", "sfp_rx_below_threshold": "SFP RX Unter", "sfp_temp_over_threshold": "SFP Temperature Over", "sfp_tx_below_threshold": "SFP TX Unter", "sfp_volt_below_threshold": "SFP Voltage Under", "sfp_volt_over_threshold": "SFP Voltage Over", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "labs": {"colored_link_desc": "<PERSON><PERSON> akt<PERSON>, werden alle drahtlosen Verbindungen nach SNR gefärbt.", "desc": "<PERSON>n Sie die Labs einschalten, werden experimentelle Funktionen hinzugefügt, die sich noch in einem frühen Stadium befinden, aber Ihrem System nicht schaden.", "dialog_title": "Aktivieren von Labs-Funktionen", "title": "MXview One Laboratorien"}, "language": {"default_language": "Standardsprache", "en_US": "<PERSON><PERSON><PERSON>", "fail": "Die Standardsprache kann nicht eingestellt werden.", "success": "Die Standardsprache wird aktualisiert.", "title": "<PERSON><PERSON><PERSON>", "zh_CN": "简体中文", "zh_TW": "繁體中文"}, "login_authentication": {"apply_fail": "Authentifizierung Anmeldung konnte nicht aktualisiert werden", "apply_success": "Authentifizierung Anmeldung wurde aktualisiert", "authentication_protocol": "Authentifizierungsprotokoll", "local": "<PERSON><PERSON>", "tacacs": "TACACS+", "tacacs_local": "TACACS+, Lokal", "title": "Authentifizierung Anmeldung"}, "login_notification": {"fail": "Die Login-Benachrichtigung kann nicht eingestellt werden.", "login_authentication_failure_message": "Nachricht über Scheitern der Anmeldungsauthentifizierung", "login_message": "Login-<PERSON><PERSON>ng", "show_default_password_notification": "Standardpasswortbenachrichtigung anzeigen", "show_login_failure_records": "Login-Fehlerprotokolle anzeigen", "success": "Die Login-Benachrichtigung wird aktualisiert.", "title": "Login-Benachrichtigung"}, "management_interface": {"help": "Diese Seite dient zum Einstellen der Verbindungsschnittstellen von MXview One zu Geräten, einsch<PERSON>ßlich http-, https- und Telnet-Ports.", "http_port": "HTTP-Port", "htts_port": "HTTPS-Port", "invalid_port": "Die Port-Nummer muss zwischen 1 und 65535 liegen.", "set_fail": "Die Management-Schnittstelle kann nicht eingestellt werden.", "set_success": "Die Management-Schnittstelle wird aktualisiert.", "telnet_port": "Telnet-Port", "title": "Management-Schnittstelle", "web_console_protocol": "Web-Konsolenprotokoll"}, "modbus_tcp_configuration": {"port": "port"}, "opc_server_config": {"apply_fail": "Die OPC-Server-Konfiguration kann nicht eingestellt werden.", "apply_success": "Die OPC-Server-Konfiguration wird aktualisiert.", "enable_opc_server": "Freigeben", "title": "OPC-Server-Konfiguration"}, "password_policy": {"fail": "Die Passwortrichtlinie kann nicht eingestellt werden.", "has_digits": "Mindestens eine Zahl (0 - 9)", "has_special_chars": "Mindestens ein Sonderzeichen (~!@#$%^&*-_|;:,.<>[]{}())", "min_password_length": "<PERSON><PERSON><PERSON><PERSON><PERSON> (4 - 16)", "min_password_length_error": "Bitte geben Si<PERSON> einen gültigen Wert ein.", "mixed_case": "Groß- und Kleinbuchstaben gemischt (A bis Z, a bis z)", "password_strength_check": "Überprüfung der Passwortstärke", "success": "Die Passwortrichtlinie wird aktualisiert.", "title": "Passwortrichtlinie"}, "search": "<PERSON><PERSON>", "security_view": {"all": "Alle", "awk_device_credentials_hint": "Um die Sicherheitsansicht zu unterstützen, müssen Sie den Benutzernamen und das Kennwort dieses Geräts festlegen", "basic": "Einfache", "basic_text": "<PERSON><PERSON><PERSON>", "built_in_profile": "Eingebautes Profil", "check_item": "Element prüfen", "colors_for_check_result": "Farben für das Prüfergebnis", "current_setting": "Aktuelle Einstellung:", "custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "custom_profile": "Eine Basis für das benutzerdefinierte Profil auswählen", "device_security_level": "Gerätesicherheitsstufe:", "failed": "Die Sicherheitsansicht konnte nicht eingestellt werden.", "filter_result": "Ergebnis des Filters", "high": "Hoch", "high_text": "Hoch", "medium": "<PERSON><PERSON><PERSON>", "medium_text": "<PERSON><PERSON><PERSON>", "new_profile": "Neues Profil", "not_pass": "<PERSON>cht bestanden", "open": "<PERSON>en", "pass": "<PERSON><PERSON><PERSON>", "profile": "Profil", "profile_details": "Profildetails", "success": "Die Sicherheitsansicht wird aktualisiert.", "title": "Sicherheitsansicht", "unknown": "Unbekannt", "user_defined": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "Server": "Server", "site_name_configuration": "Site-Name-Konfiguration", "sms_config": {"apply_fail": "Die SMS-Konfiguration kann nicht eingestellt werden.", "apply_success": "Die SMS-Konfiguration wird aktualisiert.", "baud_rate": "Baudrate", "com_port": "COM-Port", "mode": "Modus", "title": "SMS-Einstellung"}, "snmp_configuration": {"help": "SNMP-Konfiguration für den Zugriff auf Netzwerkgeräte festlegen", "title": "SNMP-Konfiguration"}, "SNMP_TRAP": {"apply_fail": "Die SNMP-Trap-Server-Konfiguration kann nicht eingestellt werden.", "apply_success": "Die SNMP-Trap-Server-Konfiguration wird aktualisiert.", "community1": "Verwaltungsbeziehung von Trap-Server 1", "community2": "Verwaltungsbeziehung von Trap-Server 2", "device_list": "Geräteli<PERSON>", "device_trap": "SNMP-Trap-Server des Geräts", "forward_trap_control1": "Empfangenen Trap an Server 1 weiterleiten", "forward_trap_control2": "Empfangenen Trap an Server 2 weiterleiten", "ip1": "IP-Adresse des Trap-Servers 1", "ip2": "IP-Adresse des Trap-Servers 2", "mxview_trap": "SNMP-Trap-Server von MXview One", "version": "SNMP-Version", "version_1": "SNMP-Version 1", "version_2": "SNMP-Version 2c"}, "syslog_config": {"already_running": "Der Server ist bereits in Betrieb oder abgeschaltet.", "apply_fail": "Die Syslog-Server-Konfiguration kann nicht eingestellt werden.", "apply_success": "Die Syslog-Server-Konfiguration wird aktualisiert.", "enable_syslog_server": "Eingebauten Syslog-Server aktivieren", "invalid_port": "Die Port-Nummer muss zwischen 1 und 65535 liegen.", "syslog_server_port": "Syslog-Server Port", "title": "Syslog-Server-Konfiguration"}, "system_configuration": {"apply_fail": "Die Systemkonfiguration kann nicht eingestellt werden.", "apply_success": "Die Systemkonfiguration wird aktualisiert.", "background_discovery": "Hintergrunderkennung", "disk_hint": "Alarm ist deaktiviert, wenn er auf 0 festgelegt wird", "playback": "Wiedergabe", "playback_hint_1": "* Wenn die \"Wiedergabe-Funktion\" aktiviert ist, zeichnet MXview One den Status von Geräten und Verbindungen auf, während ein Ereignis eintritt, und <PERSON><PERSON> können später in den Wiedergabemodus wechseln, um den Detailprozess zu verfolgen.", "playback_hint_2": "* Zusätzlicher Speicherplatz ist erforderlich, wenn die Funktion \"Wiedergabe\" aktiviert ist", "threshold_disk_space": "Schwellenwert des Festplattenspeichers (MB)", "title": "Systemkonfiguration"}, "table": {"default": "Standardwert", "dense": "<PERSON><PERSON> Dichte", "fail": "<PERSON><PERSON><PERSON> von Tabelleneinstellungen nicht möglich", "success": "Tabelleneinstellungen wurden aktualisiert", "table_row_height": "Zeilenhöhe", "title": "<PERSON><PERSON><PERSON>"}, "tacacs": {"apply_fail": "Serverkonfiguration TACACS lässt sich nicht einrichten", "apply_success": "TACACS+ Serverkonfiguration wurde aktualisiert", "auth_type": "Auth.-Typ", "auth_type_asc_two": "ASCII", "auth_type_chap": "CHAP", "auth_type_ms_chap": "MS-CHAP", "auth_type_pap": "PAP", "server_address": "Server-<PERSON><PERSON><PERSON>", "share_key": "Teilungsschlüssel", "tcp_port": "TCP Port", "timeout": "Zeitüberschreitung", "title": "TACACS+ Server"}, "title": "Einstellungen", "topology_appearance": {"access_port": "Zugangs-Port", "background": "Hi<PERSON>grund", "background_color": "Hintergrundfarbe", "directed_line_style": "Gericht<PERSON>til", "edit_igmp_visualization_color": "Farbe der IGMP-Visualisierung bearbeiten", "edit_traffic_load_color": "Farbe für Datenverkehrslast bearbeiten", "edit_vlan_visualization_color": "Farbe der VLAN-Visualisierung bearbeiten", "elbow_line_style": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fail": "Die Topologiedarstellung kann nicht eingestellt werden.", "hsr_ring": "HSR-Ring", "igmp_visualization": "IGMP-Visualisierung", "link_down": "Verbindung trennen", "link_up": "Verbindung herstellen", "member": "<PERSON><PERSON><PERSON><PERSON>", "poe": "PoE", "poe_color": "PoE-Verbindungsfarbe", "prp_lan_a": "PRP LAN A", "prp_lan_b": "PRP LAN B", "querier": "<PERSON><PERSON><PERSON><PERSON>", "rstp": "RSTP", "show_poe": "PoE-Status in der Topologie anzeigen", "status_color": "Statusfarbe", "success": "Die Topologiedarstellung wird aktualisiert.", "text_size": "Schriftgröße", "text_size_large": "<PERSON><PERSON><PERSON>", "text_size_medium": "<PERSON><PERSON><PERSON>", "text_size_small": "<PERSON>", "title": "Topologie", "topology_style": "Topologielinienstil", "traffic_load": "Datenverkehrslast", "trunk_port": "Bündelungs-Port", "turbo_chain": "Turbokette", "turbo_ring_v1": "Turboring V1", "turbo_ring_v2": "Turboring V2", "vlan_visualization": "VLAN-Visualisierung"}, "user": "<PERSON><PERSON><PERSON>"}, "rogue_device_detection": {"add_device_for_rogue_device_detection": "Fügen Sie dieses Gerät zur Geräte-Baseline für die Erkennung nicht autorisierter Geräte hinzu", "add_device_to_baseline": "Gerät zur Baseline hinzufügen", "add_device_to_baseline_content": "Möchten Sie dieses Gerät wirklich zur Baseline hinzufügen?", "add_devices_to_baseline_content": "Möchten Sie diese Geräte wirklich zur Baseline hinzufügen?", "add_scan_device_for_rogue_device_detection": "Fügen Sie die gescannten Geräte zur Geräte-Baseline für die Erkennung nicht autorisierter Geräte hinzu", "clear_all_rogue_device_history": "<PERSON><PERSON><PERSON><PERSON> nicht autorisierter Geräte löschen", "clear_all_rogue_device_history_hint": "Der gesamte Verlauf nicht autorisierter Geräte wird gelöscht. Möchten Sie wirklich fortfahren?", "connected_switch_port": "Verbundener Switch/Port", "creation_time": "Erstellt am", "current_rogue_device": "Aktuelle Rogue-Geräte", "delete_device_from_baseline": "Gerät aus Baseline löschen", "delete_device_from_baseline_content": "Dieses Gerät wird aus der Basislinie entfernt und als Rogue-Gerät hinzugefügt.", "delete_devices_from_baseline_content": "Diese Geräte werden aus der Basislinie entfernt und als Rogue-Geräte hinzugefügt.", "device_baseline": "Geräte-Baseline", "device_baseline_content": "Dieser Vorgang erstellt eine neue Basislinie und überschreibt die vorhandene.", "download_all_history_data_to_csv": "Alle Verlaufsdaten nach CSV exportieren", "download_current_page_to_csv": "Aktuelle Seite als CSV exportieren", "first_seen": "Zum ersten Mal g<PERSON>hen", "ip": "IP-Adresse", "last_seen": "Zuletzt g<PERSON>hen", "mac": "MAC-Adresse", "must_create_a_baseline_first": "<PERSON>s ist keine Geräte-Baseline vorhanden. <PERSON>rstellen Si<PERSON> zu<PERSON>t eine Baseline.", "no_devices_can_add": "<PERSON><PERSON> Geräte erkannt. Fügen Sie zuerst Geräte zu MXview One hinzu.", "port": "Hafen", "rogue_device_history": "<PERSON><PERSON><PERSON><PERSON> nicht autorisierter Geräte", "rogue_device_settings": "Einstellungen für nicht autorisierte Geräte", "sequence_no": "Sequenznummer", "unknown": "Unbekannt", "vendor": "NIC-Anbieter"}, "SCAN_RANGE": {"add_scan_range": "Scan-<PERSON><PERSON><PERSON>", "button": {"back": "Zurück", "browse_topology": "Topologie durchsuchen", "cancel": "Abbrechen", "discovery": "OK und Erkennung ausführen", "next": "<PERSON><PERSON>", "recover": "Wiederherstellung", "scan_new_network": "Neues Netzwerk scannen"}, "cidr_address_range": "CIDR-Adressbereich", "duplicate_range": "Der neue Bereich überschneidet sich mit bestehenden Bereichen.", "edit_scan_range": "Scan-<PERSON><PERSON><PERSON> bear<PERSON>", "firstIp_higher_lastIp": "IP Bereich ist ungültig (Erste IP > Letzte IP)", "subnet_mask": "Subnet Mask", "table_title": {"active": "Scan-Bereich aktivieren", "background_scan": "Hintergrundscan", "conflict_scan": "IP-Konflikt erkennen", "edit": "<PERSON><PERSON><PERSON>", "end_ip": "Letzte IP-Adresse", "group": "Gruppe", "name": "Name", "site_name": "Standortname", "start_ip": "Erste IP-Adresse"}, "wizard": {"complete": "<PERSON>den", "complete_message": "{{discoveryDevices}} werden zu MXview One hinzugefügt.", "discovery_result": "Erkennungsergebnis", "network_range": "Netzwerkbereich", "save_hint": "Der/die gescannte(n) Bereich(e) wird/werden nach der Geräteerkennung gespeichert.", "title": "Geräteentdeckung"}}, "script_automation": {"add_a_script_automation": "Fügen Sie eine Skriptautomatisierung hinzu", "add_script_button_hint": "Die maximale Anzahl an Skriptautomatisierungen beträgt 200.", "add_script_first_hint": "<PERSON>ine Skriptautomatisierungen gefunden. Gehen Sie zum Bildschirm „Skriptautomatisierung“, um eine Skriptautomatisierung hinzuzufügen.", "add_script_sutomation": "Skriptautomatisierung hinzufügen", "adjustable_buttons": "Schaltflächen neu anordnen", "affected_devices": "Betroffene Geräte", "affected_devices_info": "Diese Aktion betrifft {{ affectedDevices }} Geräte.", "affected_devices_info_2": "Diese Aktion betrifft die folgenden {{ affectedDevices }} Geräte.", "align_buttons": "Alle Gruppen in einer einzigen Spalte ausrichten", "all_devices": "Alle Geräte", "automation_button": "Automatisierungsschaltflächen", "background_color": "Hintergrundfarbe", "button_name": "Schaltflächenname", "button": {"panel": "<PERSON><PERSON><PERSON>", "privilege": "Administrator-Authentifizierung erforderlich", "state": {"hint": "Nur eine Schaltfläche in einer Gruppe kann sich im Status ' on ' befinden", "off": "Ausgeschaltet", "on": "<PERSON><PERSON>", "title": "Schaltflächenstatus"}, "style": "Schaltflächenstil", "widget": "Schaltflächen-Widget"}, "cli_id_duplicated": "Diese CLI wurde bereits ausgewählt.", "cli_script_and_target_device": "CLI-Skripte und Zielgeräte", "cli_script_hint": "Die maximale Anzahl an Skripten beträgt 50.", "color": "Farbe", "confirm_proceed": "M<PERSON>chten Si<PERSON> fortfahren?", "create_new_group": "Erstellen und Hinzufügen einer neuen Gruppe", "delete_automation": "Möchten Sie diese Skriptautomatisierung wirklich löschen?", "delete_multiple_automation": "Möchten Sie diese Skriptautomatisierungen wirklich löschen?", "delete_script_automation": "Skriptautomatisierung löschen", "description": "Beschreibung", "device_missing": "Die folgenden Geräte konnten nicht gefunden werden", "drag_button": "<PERSON><PERSON>hen Si<PERSON> eine Schaltfläche hierher, um eine neue Gruppe zu erstellen.", "edit_button": "Schaltfläche „Bearbeiten“", "edit_group": "Edit Group", "edit_panel": "Titel des Schaltflächenfelds bearbeiten", "edit_script_button": "Schaltfläche „Automatisierung bearbeiten“", "execute_button": "Schaltfläche „Ausführen“", "execute_button_hint": "Ausführen der Skriptautomatisierung", "execute_button_info": "Bitte warten Si<PERSON>, bis der Vorgang abgeschlossen ist, um die Ergebnisse anzuzeigen. Wenn Si<PERSON> diesen Bildschirm verlassen, können Si<PERSON> zu „Gespeicherte CLI-Skripte > Ausführungsergebnisse“ gehen, um die Ergebnisse herunterzuladen.", "extra_large": "Extra groß", "group": "Gruppe", "group_already_exist": "Dieser Gruppenname existiert bereits", "group_name": "Gruppenname", "invalid_account": "Ungültige Kontoberechtigung", "ip_duplicated": "Dieses Gerät ist bereits ausgewählt.", "large": "<PERSON><PERSON><PERSON>", "last_executed": "Zuletzt ausgeführt", "leave_page_hint": "<PERSON><PERSON>chten Sie diese Seite wirklich verlassen?", "leave_without_saving": "Verlassen ohne zu speichern", "medium": "<PERSON><PERSON><PERSON>", "more": "<PERSON><PERSON>", "name": "Name", "not_change_group": "Aktuelle Gruppe verwenden", "not_saved_hint": "Von Ihnen vorgenommene Änderungen werden nicht gespeichert.", "script_automation": "Skriptautomatisierung", "select_all": "Wählen Sie Alle", "select_existing_group": "In eine andere Gruppe verschieben", "select_saved_cli_script": "Gespeichertes CLI-Skript auswählen", "small": "<PERSON>", "start_preview": "Vorschau in Topologie starten", "stop_preview": "Stoppvorschau in Topologie", "target_device": "Zielgeräte", "text_color": "Textfarbe", "widget_size": "Widget-Größe"}, "SECURITY_ITEM": {"ACCESSIBLE_IP_LIST": "Vertrauenswürdigen Zugriff aktivieren", "ACCOUNT_LOCKOUT": "Sperrung bei Konto-Login-Fehler aktivieren", "ACCOUNT_VALIDITY": "Gültigkeit der Richtlinien für Konten und Passwörter", "AUTO_LOGOUT": "Automatische Abmeldung aktivieren", "AWK_SERIES": "Wireless", "BROAD_CAST_STORM": "Enable DDoS Protection", "changed": "G<PERSON><PERSON>ndert", "disabled": "<PERSON><PERSON><PERSON><PERSON>", "enabled": "Freigegeben", "ENCRYPT_CONSOLE": "Nicht verschlüsselte TCP/UDP-Ports deaktivieren", "ENCRYPTED_CONFIG": "Verschlüsselung der Konfigurationsdatei aktivieren", "HIGH_SECURE_MODE": "Hochsicherer Modus", "LOGIN_NOTIFICATION": "Login-<PERSON><PERSON>ng einstellen", "MGATE": "Gateway", "non-changed": "<PERSON>cht geändert", "not_set": "<PERSON>cht e<PERSON>llt", "NPORT": "Terminal-Server", "NPORT5000": "Geräteserver", "NTP_SERVER": "NTP-Client einstellen", "PASSWORD_CHANGED": "Standardpasswort / SNMP-Community-String ändern", "PASSWORD_POLICY": "Überprüfung der Passwortstärke aktivieren", "read_fail": "<PERSON><PERSON><PERSON><PERSON>", "set": "Eingestellt", "SWITCH": "Switch", "SYSLOG": "Syslog-Server einstellen", "TRAPSYSLOG": "SNMP-Trap/Inform oder Syslog-Server einstellen", "unknown": "Unbekannt", "WEB_CERTIFICATE": "Web-Zertifikat importieren"}, "SERIAL_PORT_MONITORING": {"all_ports": "Alle Häfen", "any_serial_error_count": "<PERSON><PERSON><PERSON> beliebiger se<PERSON><PERSON>", "break_error_count": "Anzahl der Unterbrechungsfehler", "copy_configuration_device": "Konfiguration auf Geräte kopieren", "count_threshold": "Triggersch<PERSON><PERSON>", "counts": "<PERSON><PERSON><PERSON>(en)", "critical": "Critical", "error_status": "Warnungen zur seriellen Schnittstelle", "event_condition_rule": "Regeln für Ereignisauslöser", "frame_error_count": "Frame<PERSON><PERSON><PERSON>", "hint_any": "Versuchen Sie die folgenden Schritte, um das folgende Problem zu beheben: Der serielle Port {{portnum}} hat den Schwellenwert für die Anzahl von Über<PERSON>n ({{overrun error count}}), Paritätsfehlern ({{parity error count}}), Frame-<PERSON>hlern ({{frame error count}}) oder Unterbrechungsfehlern ({{break error count}}) überschritten.", "hint_break": "Versuchen Sie die folgenden Schritte, um das folgende Problem zu beheben: Der serielle Port {{portnum}} hat den Schwellenwert für die Anzahl der Unterbrechungsfehler ({{count}}) überschritten.\nEin serielles Unterbrechungssignal weist auf einen besonderen Zustand des angeschlossenen seriellen Geräts hin, beispielsweise ein Verdrahtungsproblem, eine Gerätefehlfunktion, ein Gerätereset oder ein Synchronisierungsvorgang.", "hint_frame": "Versuchen Sie die folgenden Schritte, um das folgende Problem zu beheben: Der serielle Port {{portnum}} hat den Schwellenwert für die Anzahl der Frame-Fehler ({{count}}) überschritten.\nWenn das Moxa-Gerät serielle Daten empfängt, prüft es, ob das Frame-Format mit den seriellen Parametern übereinstimmt. Wenn dies nicht der Fall ist, wird dies als Frame-Fehler gewertet.", "hint_general_1": "Wenn die vorgeschlagene Lösung nicht funktioniert hat oder Sie weitere Fragen haben, wenden Sie sich bitte an Ihren", "hint_general_2": "first", "hint_general_3": "Kontakt", "hint_general_4": "wenn Sie noch weitere Unterstützung benötigen.", "hint_overrun": "Versuchen Sie die folgenden Schritte, um das folgende Problem zu beheben: Der serielle Port {{portnum}} hat den Schwellenwert für die Anzahl der Überlauffehler ({{count}}) überschritten.\nWenn die angeschlossenen seriellen Geräte Daten zu schnell senden, als dass das Moxa-Gerät sie lesen könnte, gehen Daten verloren, was zu einem Überlauffehler führt.", "hint_parity": "Versuchen Sie die folgenden Schritte, um das folgende Problem zu beheben: Der serielle Port {{portnum}} hat den Schwellenwert für die Anzahl der Paritätsfehler ({{count}}) überschritten.\nEin Paritätsfehler zeigt an, dass das empfangene Datenzeichen nicht mit der konfigurierten Parität übereinstimmt.", "hint_rx": "Versuchen Sie die folgenden Schritte, um das folgende Problem zu beheben: Der serielle Port {{portnum}} hat in den letzten {{min}} Minute(n) keine Daten empfangen.", "hint_rxtx": "Versuchen Sie die folgenden Schritte, um das folgende Problem zu beheben: Der serielle Port {{portnum}} hat in den letzten {{min}} Minuten keine Daten gesendet oder empfangen.", "hint_tx": "Versuchen Sie die folgenden Schritte, um das folgende Problem zu beheben: Der serielle Port {{portnum}} hat in den letzten {{min}} Minuten keine Daten übertragen.", "how_to_resolve": "Wie kann man das <PERSON> beheben?", "minutes": "Protokoll", "no_data_period": "<PERSON><PERSON>", "overrun__error_count": "Fehleranzahl überschritten", "parity__error_count": "Anzahl der Paritätsfehler", "port_duplicated": "Diese Port-/Triggerregelkombination ist bereits konfiguriert.", "port_properties": "Ereignistyp", "resolve_title": "Problem am seriellen Port {{portnum}} beheben", "rx": "RX-Inaktivität", "serial_port": "Serielle Schnittstelle", "severity": "Schwere", "step1_break": "Überprüfen Si<PERSON>, ob die angeschlossenen seriellen Geräte ordnungsgemäß funktionieren.", "step1_frame": "<PERSON>berpr<PERSON><PERSON>, ob die Einstellungen der seriellen Schnittstelle (RS-232, RS-422, RS485) und die Kommunikationsparameter (z. B. 115200, 8, n, 1) auf dem Moxa-Gerät mit denen auf den angeschlossenen seriellen Geräten übereinstimmen.", "step1_ovrrun": "Überprüfen Si<PERSON>, ob die serielle Hardware- und/oder Software-Flusssteuerung sowohl auf dem Moxa-Gerät als auch auf den angeschlossenen seriellen Geräten richtig eingestellt ist", "step1_parity": "Überprüfen Si<PERSON>, ob die Paritäts- und Baudrateneinstellungen auf dem Moxa-Gerät und den angeschlossenen seriellen Geräten übereinstimmen.", "step1_txrx": "Überprüfen Sie, ob das serielle Kabel zwischen dem Moxa-Gerät und den seriellen Endgeräten richtig angeschlossen ist.", "step2_ovrrun": "Überprüfen Sie, ob das FIFO auf dem Moxa-Gerät aktiviert werden muss.", "step2_parity": "Überprüfen Sie in Bereichen mit starken Störungen, ob das serielle Kommunikationssystem ausreichend geschützt ist.", "step2_txrx": "Überprüfen Si<PERSON>, ob die angeschlossenen seriellen Geräte ordnungsgemäß funktionieren.", "step3_parity": "Überprüfen Sie die angeschlossenen seriellen Geräte auf Verdrahtungsprobleme oder Hardwarefehler.", "still_not_working": "Funktioniert es immer noch nicht?", "title": "Serielle Port-Ereignisse", "tx": "TX-Inaktivität", "tx_rx": "TX- und RX-Inaktivität", "warning": "<PERSON><PERSON><PERSON>"}, "SEVERITY": {"critical": "<PERSON><PERSON><PERSON>", "information": "Information", "title": "Schweregrad", "warning": "<PERSON><PERSON><PERSON>"}, "sfpList": {"rx": "RX (dBm)", "temperature": "Temp. (°C)", "title": "SFP Liste", "tx": "TX (dBm)", "voltage": "Spannung (V)"}, "SITE_MANAGEMENT": {"desc": "Beschreibung", "fail": "Die Standortinformationen können nicht aktualisiert werden.", "name": "Name", "offline": "Eine Seite ist offline ({{siteName}})", "online": "Eine Seite ist online ({{siteName}})", "success": "Die Standortinformationen werden aktualisiert.", "title": "Standortverwaltung"}, "SITE_MENU": {"management": "Management"}, "SITE_PROPERTIES": {"description": "Beschreibung", "devices": "Ger<PERSON><PERSON> (normal, Warnung, kritisch)", "information": "Informationen", "name": "Name", "title": "Standorteigenschaften"}, "SNACK_BAR": {"acking": "Q<PERSON>tieren...", "copied": "<PERSON><PERSON><PERSON>", "deleting": "<PERSON><PERSON><PERSON>", "saving": "Wird g<PERSON><PERSON>"}, "SYSLOG_SETTINGS": {"all_severity": "Alle", "authentication": "Authentifizierung", "enable_syslog_forward": "Syslog-Weiterleitung", "enable_tcp": "Aktivieren (nur TCP)", "enable_udp": "Aktivieren (nur UDP)", "enable_udp_tcp": "Aktivieren (UDP und TCP)", "failed_to_get_syslog_forward_settings": "Die Syslog-Weiterleitungseinstellungen konnten nicht abgerufen werden.", "failed_to_get_syslog_settings": "Syslog-Einstellungen konnten nicht abgerufen werden.", "filter_settings_hint": "<PERSON>e können mehrere Quell-IP-Adressen e<PERSON>ben, getrennt durch <PERSON>.", "forward_ip1": "Remote-IP/Domänenname 1", "forward_ip2": "Remote-IP/Domänenname 2", "port_1": "Anschluss 1", "port_2": "Anschluss 2", "protocol": "Protokoll", "source_ip": "Quellen-IP", "syslog_built_in": "Integrierter Syslog-Server", "syslog_filter_settings": "Syslog-Filter", "syslog_forwarding": "Syslog-Weiterleitung", "syslog_server_settings": "Syslog-Servereinstellungen", "tcp": "TCP", "tcp_port": "TCP Port", "title": "Syslog-Einstellungen", "tls_cert": "TLS + Zertifikat", "tls_only": "Nur TLS", "udp": "UDP", "udp_port": "Udp-hafen", "update_failed": "Die Einstellungen konnten nicht aktualisiert werden", "update_success": "Einstellungen erfolgreich aktualisiert"}, "SYSLOG_VIEWER": {"device_ip": "IP-Adresse", "facility": "Einrichtung", "filter_syslog_event": "<PERSON>p zum Filtern von Syslog-Ereignissen", "ip_error": "<PERSON><PERSON><PERSON> Si<PERSON> bitte eine gültige IP-Adresse ein", "message": "<PERSON><PERSON><PERSON>", "priority": {"equals": "<PERSON><PERSON><PERSON>", "high_than": "<PERSON><PERSON><PERSON><PERSON><PERSON> oder gleich", "lower_than": "<PERSON>er oder gleich", "title": "Priorität"}, "severity": {"alert": "Alarm", "critical": "<PERSON><PERSON><PERSON>", "debug": "Debug", "emergency": "Notfall", "error": "<PERSON><PERSON>", "information": "Information", "notice": "<PERSON><PERSON><PERSON><PERSON>", "title": "Schwere", "warning": "<PERSON><PERSON><PERSON>"}, "site_name": "Standortname", "timestamp": "Zeitstempel", "title": "Syslog Viewer"}, "SYSTEM": {"request_timeout_title": "Anfrage-Timeout", "trigger_disconnected_desc1": "Vom MXview One-Server getrennt", "trigger_disconnected_desc2": "Erneute Verbindung nach 5 Sekunden...", "unauthorized_desc": "Zugriff wird aufgrund ungültiger Zugangsdaten verweigert.", "unauthorized_title": "Nicht autorisiert"}, "TABLE": {"add": "Hinzufügen", "adjustable_columns": "Einstellbare Säulen", "compare": "Vergleichen", "delete": "Löschen", "edit": "<PERSON><PERSON><PERSON>", "edit_columns": "Spalten bearbeiten", "enable": "Aktiviert/Deaktiviert", "export": "Exportieren", "exporting": "Wird jetzt exportiert...", "filter": "Filter", "import": "Importieren", "limit_count": "<PERSON>.", "list_collaspe": "Reduzieren", "list_expand": "Erweitern für weitere Informationen", "locate": "Lokalisieren", "no_data": "<PERSON><PERSON>", "not_support": "Die Firmware-Version des Geräts wird nicht unterstützt", "save": "Speichern", "search": "<PERSON><PERSON>", "selected_count": "Ausgewählt", "show_log": "Protokoll anzeigen", "sync": "Sync", "total": "Gesamt", "waiting_data": "<PERSON><PERSON> auf Daten"}, "TOPOLOGY": {"add_tag": "Add Tag...", "add_tag_fail": "Unable to add tag", "add_tag_success": "Add a tag successfully", "choose_goose_publisher": "Wählen Sie einen GOOSE-Publisher", "colored_link": "Colored link by SNR", "delete_tag_failed": "Unable to delete tag", "delete_tag_success": "Delete a tag successfully", "device_not_found": "Gerät nicht gefunden", "display_opt": "Anzeigeoption", "dynamic_wireless_client_position": "Clients um angeschlossenen AP anzeigen", "dynamic_wireless_client_position_desc": "Aktivieren Sie diese Option, wird jede Client um jeden verbundenen AP angezeigt", "editTagTooltip": "Press Enter to save.\nPress ESC key to cancel.", "goose": "GOOSE", "goose_publisher": "GOOSE-<PERSON><PERSON><PERSON><PERSON>", "goose_tampered": "GOOSE <PERSON>", "goose_timeout": "GOOSE Zeitüberschreitung", "grouping_failed": "Gruppierung fehlgeschlagen", "grouping_success": "Erfolgreich gruppiert", "legend": "Legend", "new_tag": "New tag", "no_subscriber": "<PERSON><PERSON>", "prp_hsr": "PRP/HSR", "prp_hsr_tags": "PRP/HSR Tags", "publisher": "Herausgeber", "publisher_hint": "GOOSE control block name\nAPPID / Address", "search_topology": "Topologie durchsuchen", "set_tag_fail": "Unable to set a tag", "set_tag_success": "Set a tag successfully", "show_all_wireless_clients": "Alle Clients anzeigen", "site_management_not_supported": "Bitte geben Si<PERSON> einen Standort an, um die Funktion zur Wiedergabe des Roaming-Verlaufs zu nutzen", "subscriber": "Abonnent", "subscriber_hint": "IED Name / GOOSE control block name", "tag": "Tag", "traffic_view": "Datenverkehrslast (%)", "ungrouping_failed": "Entgruppierung fehlgeschlagen", "ungrouping_success": "Erfolgreich entgruppiert", "wireless_display_opt": "Wireless Anzeigeoption", "zoom_in": "Heranzoomen", "zoom_out": "Herauszoomen", "zoom_to_actual_size": "Auf Standardgröße zoomen", "zoom_to_fit": "<PERSON><PERSON> Zoom anpassen"}, "TRAP_CONFIGURATION": {"apply_fail": "Der Trap-Server des Geräts kann nicht eingestellt werden.", "apply_success": "Die Trap-Server-Einstellungen des Geräts werden aktualisiert.", "community_name1": "Zugriff Name1", "community_name2": "Zugriff Name2", "destination_ip1": "Ziel IP1", "destination_ip2": "Ziel IP2", "title": "Trap-Server"}, "UNIT": {"dB": "dB", "dBi": "dBi", "dBm": "dBm", "hours": "h", "mb": "MB", "mbps": "Mbps", "meter": "m", "min": "min", "sec": "Sek.", "times": "Zeiten"}, "UPGRADE_FIRMWARE": {"file_type_error": "Firmware-<PERSON><PERSON> mü<PERSON> im .rom-, .tar- und .gz-Dateiformat vorliegen.", "upgrade_firmware_fail": "Firmware kann nicht hochgeladen werden.", "upgrade_firmware_success": "Ein Upgrade der Firmware wird ausgeführt.", "upgrading": "Die Firmware-<PERSON><PERSON> wird herunt<PERSON>n; das kann ein paar <PERSON>uten dauern. Schalten Sie die Stromversorgung nicht aus und trennen Sie das Netzwerk nicht, bis der Prozess abgeschlossen ist."}, "validators": {"duplicateEmail": "<PERSON>s gibt doppelte E-Mails", "excludeLastPassword": "Das neue Passwort darf nicht mit dem letzten Passwort identisch sein.", "excludeUserName": "Der Benutzername kann nicht enthalten sein", "invalid": "Ungültige(s) Zeichen", "invalid_date": "Ungültiges Datum", "invalid_format_allow_space": "Im String sind nur maximal {{count}} Leerzeichen zulässig", "invalidEmail": "Ungültige E-Mail", "invalidIpAddress": "Ungültige IP-Adresse", "invalidIpAddressOrDomainName": "Ungültige IP-Adresse oder ungültiger Domänenname", "invalidLocation": "<PERSON><PERSON><PERSON><PERSON> (-_@!#$%^&*().,/)", "invalidMacAddress": "Ungültige MAC-Adresse", "invalidMacAddressAllZero": "MAC-Adresse 00:00:00:00:00:00 ist reserviert", "invalidSeverity": "Ungültiger Schwere", "ipRangeError": "IP-Adresse: <PERSON><PERSON> muss größer sein als IP-Adresse: Start", "isExist": "existiert bereits", "isExistOrUsedByOtherUser": "existiert bereits oder wird von einem anderen Benutzer verwendet", "maxReceiverSize": "Die MAXIMALE Anzahl Empfänger beträgt {{num}}.", "needDigit": "<PERSON>ss mindestens eine Ziffer enthal<PERSON> (0 - 9)", "needGreaterThan": "{{ largeItem }} muss gr<PERSON>ßer sein als {{ smallItem }}", "needLowerCase": "Muss mindestens einen Kleinbuchstaben enthalten (a - z)", "needSpecialCharacter": "Muss mindestens ein Sonderzeichen enthalten (~!@#$%^&amp;*_-+=`|\\(){}[]:;”&#39;&lt;&gt;,.?/)", "needUpperCase": "Muss mindestens einen Großbuchstaben (A - Z) enthalten", "notMeetPolicy": "Erfüllt nicht die Anforderungen der Kennwortrichtlinie", "portRangeError": "Port: <PERSON>e muss größer sein als Port: Start", "pwdNotMatch": "Das Passwort stimmt nicht überein", "range": "Ungültiger Bereich ({{ min }} ~ {{ max }})", "required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requireMaxLength": "<PERSON><PERSON> nicht länger als {{ number }} <PERSON><PERSON><PERSON> sein", "requireMinLength": "Muss mindestens {{ number }} <PERSON><PERSON><PERSON> lang sein"}, "Validators": {"require_range_between": "{{ rangeBegin }} - {{ rangeEnd }}"}, "VLAN_TABLE": {"access_ports": "Zugangs-Ports", "device_ip": "Geräte-IP", "empty": "<PERSON><PERSON>", "export_csv": "CSV exportieren", "filter_vlan": "Typ zum Filtern der Geräte-VLANs", "hybrid_ports": "Hybrid Ports", "location": "Ort", "management_vlan": "VLAN-Management", "model": "<PERSON><PERSON>", "no": " <PERSON><PERSON>", "site_name": "Standortname", "title": "VLAN", "trunk_ports": "Bündelungs-Port", "vlan_id": "VLAN-ID", "yes": " <PERSON>a"}, "wirelessPlayback": {"decreaseSpeed": "Geschwindigkeit verringern", "increaseSpeed": "Geschwindigkeit", "noData": "<PERSON>s gibt keine Daten im ausgewählten Datumsbereich", "range": "<PERSON><PERSON>", "startTime": "Startzeit", "timeRange": "Zeitbereich"}}