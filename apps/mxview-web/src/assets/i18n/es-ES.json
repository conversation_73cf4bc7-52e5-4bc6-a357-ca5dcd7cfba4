{"ABOUT": {"debug_log": "Registros de depuración", "debug_log_desc": "Haga clic en \"Descargar\" para generar y descargar los registros de depuración. Si tiene problemas, envíe el archivo de registro descargado a través del canal de soporte de Moxa para su análisis.", "desc": "Copyright Moxa, Inc. Todos los derechos reservados.", "eula": "Consultar el Contrato de licencia de usuario final (EULA)", "gateway": "Versión de la puerta de enlace", "title": "Acerca de", "web": "Versión web"}, "ACCOUNT_MANAGEMENT": {"access_site": "<PERSON><PERSON> accesibles", "add_account_dialog": "Añadir cuenta de usuario", "add_user_fail": "No se pudo crear una cuenta nueva", "add_user_success": "Se creó una cuenta nueva correctamente", "admin": "Supervisor", "all_user": "Todos los usuarios", "authority": "Autoridad", "change_password": "Cambiar contraseña", "delete_user_fail": "No se pudo eliminar la cuenta de usuario", "delete_user_success": "La cuenta de usuario se eliminó correctamente", "demo_user": "Usuario de demostración", "filter_account": "Escriba para filtrar cuentas de usuario", "modify_account_dialog": "Modificar cuenta de usuario", "new_password": "Nueva contraseña", "old_password": "Contraseña anterior", "password": "Contraseña", "password_policy_mismatch": "La contraseña no se ajusta a la política de contraseñas", "superuser": "Administrador", "title": "Gestión de cuentas", "ui_profile": "Perfil de la UI", "update_user_fail": "No se pudo actualizar la cuenta de usuario", "update_user_success": "La cuenta de usuario se actualizó correctamente", "user": "Usuario", "user_account": "Cuenta de usuario", "user_exist": "El usuario ya existe", "username": "Nombre de usuario"}, "account_password": {"1st_email": "Primer destinatar<PERSON> de correo electrónico", "2nd_email": "<PERSON><PERSON>do destinatar<PERSON> de correo electrónico", "account_audit": "Auditoría de cuentas", "account_info": "Información de la cuenta", "account_info_content": "Haga clic en el botón ' Actualizar ' para recuperar la información de la cuenta para todos los dispositivos. Esto puede tomar algo de tiempo.", "account_management": "Gestión de cuentas", "account_password_management_automation": "Automatización de la gestión de cuentas y contraseñas", "account_status_audit": "Auditoría de Cuentas", "account_status_baseline": "Línea base de cuentas", "account_status_baseline_content": "Esta operación creará una nueva línea base y sobrescribirá la existente.", "accounts": "cuentas", "activate": "Activar", "add_account": "Agregar cuenta", "add_temporary_account": "Agregar cuenta temporal", "added_account": "Cuentas agregadas", "admin": "Administración", "apply_accounts": "Cuentas aplicables", "audit_automation": "Automatización de auditoría", "authority": "Autoridad", "baseline_account": "Cuentas de referencia", "baseline_auto_check_failed": "No se pudo crear la línea base", "change_admin_name": "Cambiar el nombre predeterminado de \"Administrador\"", "change_admin_name_content": "MXview One utilizará las credenciales de cuenta actualizadas para acceder a los siguientes dispositivos. Otros dispositivos no se ven afectados.", "change_admin_name_contents": "MXview One utilizará las credenciales de cuenta actualizadas para acceder a los siguientes dispositivos. Otros dispositivos no se ven afectados.", "check_default_account_failed": "No se pudo verificar la cuenta predeterminada.", "check_password_length": "Asegúrese de que la longitud de la contraseña esté dentro de la longitud máxima permitida de la contraseña en los dispositivos.", "compability": "Compatible", "create_baseline_failed": "No se pudo crear la línea base", "create_baseline_failed_no_devices": "No se pudo crear la línea base. No se detectaron dispositivos.", "days": "días", "default_account": "Nombre de usuario/contraseña predeterminados", "default_password_audit": "Auditoría de contraseña predeterminada", "default_password_audit_info": "La búsqueda de credenciales de cuenta predeterminadas puede llevar algún tiempo y dejará la interfaz no disponible temporalmente. Estás seguro de que quieres continuar?", "delete_account": "Eliminar la cuenta", "delete_temporary_account": "Eliminar cuenta temporal", "delete_temporary_account_info": "¿Estás seguro de que deseas eliminar esta cuenta temporal?", "delete_temporary_accounts_info": "¿Estás seguro de que deseas eliminar estas cuentas temporales?", "deleted_account": "Cuentas eliminadas", "device_alias": "Alias ​​del dispositivo", "device_ip": "IP del dispositivo", "edit_account": "<PERSON><PERSON>", "edit_temporary_account": "Editar cuenta temporal", "email_server_configuration": "Configuración del servidor de correo electrónico", "email_server_hint": "Si no has recibido el correo electrónico con el código de verificación, revisa tu carpeta de spam o verifica el", "email_verified": "La dirección de correo electrónico ha sido verificada.", "end_date": "<PERSON><PERSON><PERSON><PERSON> hasta", "end_time": "Hora de finalización", "fatiled_to_audit_account_due": "No se pudo completar la auditoría de la cuenta. No se puede recuperar la información de la cuenta del dispositivo de {{ ip }}.", "fatiled_to_create_baseline_due": "No se pudo crear la línea base. No se puede recuperar la información de la cuenta del dispositivo de {{ ip }}.", "get_baseline_failed": "No se pudo obtener la línea de base", "get_device_account_failed": "No se pudo consultar la información de la cuenta del dispositivo. Otras solicitudes están en curso. Vuelve a intentarlo más tarde.", "incorrect_verification_code": "Código de verificación incorrecto.", "last_audit_time": "Última auditoría", "last_execution_time": "Última ejecución", "max_char": "Máximo {{num}} caracteres", "model": "<PERSON><PERSON>", "mxa_char": "Máximo {{num}} caracteres", "new_password": "Nueva contraseña", "new_username": "Nuevo nombre de usuario", "next_audit_time": "Próxima auditoría", "next_schdeule_start_time": "Próximo programado para", "no_data": "N/A", "not_activate": "inactivar", "not_started": "Pendiente", "not_started_hint": "Esta tarea no se ejecutó debido a un apagado del sistema. Haga clic en el botón \"Regenerar contraseña\" para ejecutar manualmente la tarea.", "now": "Inmediato", "operation": "Acción", "password": "Contraseña", "password_automation": "Automatización de contraseñas", "password_automation_schedule": "Programación de automatización de contraseñas", "password_automation_settings": "Asistente de automatización de contraseñas", "password_email_receiver": "Destinatario del correo electrónico de contraseña", "password_regenerated_info": "MXview One utilizará las siguientes configuraciones para generar una nueva contraseña para cada dispositivo.", "password_resend_info": "¿Está seguro de que desea reenviar la contraseña del dispositivo a los siguientes destinatarios?", "password_resend_result_info": "MXview One ha enviado el archivo de cuenta y contraseña del dispositivo a las siguientes direcciones de correo electrónico:", "password_strength": "seguridad de la contraseña", "random_password_complexity": "Establecer la complejidad de la contraseña", "random_password_length": "Longitud aleatoria de la contraseña", "random_password_length_info": "MXview One generará una contraseña aleatoria para los dispositivos seleccionados.", "randomized_password_failed": "Error (la cuenta del dispositivo no coincide con la cuenta de la base de datos).", "refresh_hint": "Presione el botón ' Actualizar ' para recuperar las cuentas del dispositivo antes de continuar con esta acción.", "regenerate_password": "Regenerar contraseña", "resend_password_email": "Reenviar correo electrónico de contraseña", "retrieve_data": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>", "retry_failed_devices": "Reintentar dispositivos fallidos", "schedule": "Programado", "schedule_interval": "Intervalo", "script_error": "Este campo no puede contener ninguno de los siguientes caracteres: #%&amp;*{}|:\\&quot;&lt;&gt;?/\\\\", "select_device": "Seleccionar dispositivos", "select_device_random_password": "Seleccione los dispositivos para los que generar una contraseña aleatoria.", "send_password_email": "Enviar contraseña por correo electrónico", "send_password_email_success": "MXview One ha enviado la cuenta del dispositivo, la contraseña y el resultado de la ejecución a los siguientes destinatarios de correo electrónico:", "set_password_to_device": "Aplicar contraseña a los dispositivos", "set_schedule_interval_failed": "No se pudo establecer el intervalo de programación.", "set_schedule_interval_success": "Intervalo de programación establecido correctamente.", "start_date": "Activo desde", "start_over": "Comenzar de nuevo", "start_time": "Hora de inicio", "start_wizard": "<PERSON><PERSON>", "status": {"cli_session_timeout": "Tiempo de espera de sesión CLI", "failed": "Ha fallado", "failed_account_exist": "Error (Esta cuenta ya existe)", "failed_account_password_incorrect": "Error (cuenta o contraseña incorrecta)", "failed_limit_reached": "Error (se alcanzó el límite de la cuenta del dispositivo)", "failed_not_support_role": "Error (dispositivo no compatible)", "failed_other_request": "Error (hay otras solicitudes en curso)", "failed_retrieve_account_info": "Error (no se puede recuperar la información de la cuenta)", "finished": "Terminado", "in_progress": "En curso ...", "waiting": "Espera"}, "supervisor": "Supervisor", "temporary_account": "Cuentas Temporales", "test_eamil_recipient": "Destinatario de correo electrónico de prueba", "title": "Cuentas y Contraseñas", "unable_to_get_accounts": "No se pueden recuperar cuentas", "user": "Usuario", "username": "Nombre de usuario", "verififcation_code": "Código de verificación", "verift_title": "Verifique su cuenta MXview One", "verify_code_expiration": "El código de verificación caduca en", "verify_email_not_allowed": "<PERSON><PERSON>e al menos un minuto antes de enviar otro correo electrónico.", "verify_email_password_receiver": "Verificar cuenta y contraseña Destinatarios de correo electrónico", "verify_email_receiver": "Verificar destinatario de correo electrónico", "verify_email_server_failed": "La configuración del servidor de correo electrónico no es válida. No se pueden enviar correos electrónicos.", "verify_user_failed": "Nombre de usuario/contraseña no válidos"}, "ADD_DEVICE": {"add_device_fail": "No se pudo agregar el dispositivo", "add_device_fail_error_message": {"device_has_existed": "Ya existe un dispositivo con esta IP", "license_limitation_reached": "Límite de licencia alcanzado", "model_not_exist": "El modelo no existe."}, "add_device_success": "El dispositivo se agregó correctamente", "assign_group": "Asignar al grupo", "assign_model": "<PERSON><PERSON><PERSON>o", "authentication": "Autenticación", "auto_detect_model": "Detección automática", "data_encryption": "<PERSON><PERSON><PERSON>", "encryption_password": "Contraseña de cifrado", "encryption_type": "Protocolo de cifrado", "field_required": "Este campo es obligatorio", "snmp_setting": "Configuración de SNMP", "snmp_version": "Versión de SNMP", "title": "<PERSON><PERSON>dir dispositivo"}, "ADD_LINK": {"alias": "<PERSON><PERSON>", "device": "Dispositivo", "fail": "No se pudo agregar el enlace", "from": "<PERSON><PERSON>", "ip_address": "Dirección IP", "model": "<PERSON><PERSON>", "only_number": "Permite solo números", "port": "Puerto", "success": "El enlace se añadió correctamente", "title": "<PERSON><PERSON><PERSON> enlace", "to": "A"}, "API_MANAGEMENT": {"access_count": "Número de accesos", "add_failed": "No se pudo agregar la clave de API", "add_success": "La clave de API se agregó correctamente", "add_title": "Añadir un token nuevo", "api_key": "Clave de API", "application_name": "Nombre de la aplicación", "create_time": "Tiempo de creación", "delete_failed": "No se pudo eliminar la clave de API", "delete_success": "La clave de API se eliminó correctamente", "edit_title": "Editar token", "filter": "Escriba para filtrar claves de API", "regenerate_api_key": "Volver a generar la clave de API", "regenerate_failed": "No se pudo volver a generar la clave de API", "regenerate_success": "La clave de API se volvió a generar correctamente", "title": "Gestión de claves de API", "update_failed": "No se pudo actualizar la clave de API", "update_success": "La clave de API se actualizó correctamente"}, "ASSIGN_MODEL": {"apply_to_all": "Aplicar este icono a todos los dispositivos con el mismo modelo", "assign_model_fail": "No se pudo asignar el modelo", "assign_model_success": "El modelo de dispositivo se asignó correctamente", "ip_address": "Dirección IP", "model": "<PERSON><PERSON>", "model_icon": "Icono de modelo", "select_model": "Seleccionar modelo"}, "AVAILABILITY_REPORT": {"alias": "Alias ​​del dispositivo", "average": "Disponibilidad media", "days": "Días", "end_date": "Fecha de finalización", "filter": "Escriba para filtrar informes de disponibilidad", "from_date": "Fecha de inicio", "query_date": "<PERSON><PERSON>", "report_generate_day": "Fecha de generación del informe: ", "site_name": "Nombre del sitio", "title": "Informe de disponibilidad", "worst": "Peor disponibilidad"}, "BASIC_INFORMATION": {"apply_fail": "No se pudo establecer la información básica del dispositivo", "apply_success": "La información básica del dispositivo se actualizó correctamente", "contact": "Contactar", "location": "Ubicación", "model": "<PERSON><PERSON>", "name": "Nombre", "title": "Información básica"}, "BUTTON": {"add": "Agregar", "add_to_scheduler": "Agregar al programador", "agree": "Aceptar", "apply": "Aplicar", "audit": "Auditoría", "back": "Regresar", "cancel": "<PERSON><PERSON><PERSON>", "change": "Cambiar", "check": "Comprobar", "checkNow": "<PERSON><PERSON><PERSON>", "clear": "Bo<PERSON>r", "clear_fail_record": "Borrar los registros de fallas", "close": "<PERSON><PERSON><PERSON>", "compare": "Comparar", "confirm": "Confirmar", "connected": "Conectado", "continue": "<PERSON><PERSON><PERSON><PERSON>", "copy": "Copia", "create": "<PERSON><PERSON><PERSON>", "deactivate": "Desactivar", "decline": "<PERSON><PERSON><PERSON>", "delete": "Eliminar", "disable_new_version_notifications": "Deshabilitar notificaciones de nueva versión", "disconnected": "Desconectado", "download": "<PERSON><PERSON><PERSON>", "download_all_logs": "<PERSON><PERSON><PERSON> todos los registros", "download_filter_logs": "Descargar registros de filtros", "edit": "<PERSON><PERSON>", "enable_new_version_notifications": "Habilitar notificaciones de nueva versión", "execute": "<PERSON><PERSON>", "faqs": "Preguntas frecuentes", "got_it": "Entendido", "ignore": "<PERSON><PERSON><PERSON>", "leave": "<PERSON><PERSON>", "next": "Siguient<PERSON>", "ok": "Aceptar", "query": "Consultar", "reboot": "<PERSON><PERSON><PERSON>", "redirect": "REDIRECCIÓN", "refresh": "Actualizar", "regenerate": "regenerado", "resend": "Reenviar", "reset": "Reiniciar", "retry_failed_devices": "Reintentar dispositivos fallidos", "run": "correr", "save": "Guardar", "scan": "Escanear", "search": "Buscar", "security_patch_available": "Parches de seguridad disponibles", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "select_firmware_version": "Seleccione la versión del firmware", "send": "Enviar", "send_test_eamil": "Enviar correo electrónico de prueba", "set": "<PERSON><PERSON><PERSON>", "upgrade": "Actualizar", "upgrade_firmware": "Actualización de firmware", "upload": "Subir", "verify": "Verificar", "verify_email": "Verificar correo electrónico"}, "cli_object_database": {"add_cli_fail": "No se puede crear el script CLI", "add_cli_object": "Agregar secuencia de comandos CLI", "add_cli_success": "Nuevo script CLI creado correctamente", "before_date": "<PERSON><PERSON>", "before_time": "Tiempo", "cli_objects": "Secuencias de comandos CLI", "cli_script": "Secuencia de comandos CLI", "delete_all_execution_results": "Eliminar todos los resultados de ejecución del script CLI", "delete_all_execution_results_before_time": "Eliminar los resultados de la ejecución del script antes de", "delete_all_execution_results_before_time_desc": "¿Está seguro de que desea eliminar todos los resultados de la ejecución del script antes de {{param}}?", "delete_all_execution_results_desc": "¿Está seguro de que desea eliminar todos los resultados de ejecución del script CLI?", "delete_cli_fail": "No se puede eliminar el script CLI", "delete_cli_object": "Eliminar secuencia de comandos CLI", "delete_cli_object_desc": "¿Está seguro de que desea eliminar este script CLI?", "delete_cli_object_disabled": "Este script no se puede eliminar porque está vinculado a una tarea programada. Para eliminar este script, primero modifique o elimine la tarea programada en la página Administración > Programador de mantenimiento.", "delete_cli_objects_desc": "¿Está seguro de que desea eliminar estos scripts CLI?", "delete_cli_success": "El script CLI se eliminó correctamente", "delete_execution_result_fail": "No se pueden eliminar los resultados de la ejecución", "delete_execution_result_success": "Los resultados de la ejecución del script se eliminaron correctamente", "delete_results_before_time": "Eliminar los resultados de la ejecución del script CLI", "description": "Descripción", "download_all_execution_results": "<PERSON><PERSON><PERSON> todos los resultados de la ejecución", "download_all_execution_results_fail": "No se pueden descargar los resultados de la ejecución", "download_execution_results_failed_hint": "No hay resultados de ejecución disponibles para descargar.", "edit_cli_fail": "No se puede actualizar el script CLI", "edit_cli_object": "Editar secuencia de comandos CLI", "edit_cli_success": "Script CLI actualizado correctamente", "execution_results": "Resultados de ejecución", "get_cli_fail": "No se pueden recuperar los scripts CLI", "linked_scheduled_task": "Tareas programadas vinculadas", "linked_script_automation": "Automatizaciones de scripts vinculados", "name": "Nombre", "non_ascii": "Sólo se aceptan caracteres ASCII.", "scheduled_execution_cli_object_desc": "<PERSON>uede crear tareas programadas para ejecutar scripts CLI en una fecha y hora específicas desde la página Administración > Programador de mantenimiento.", "scheduled_execution_cli_object_info": "Guión programado", "scheduled_task": "Tarea programada", "title": "Scripts CLI guardados"}, "COMBO_BOX": {"disabled": "Desactivado", "enabled": "Activado", "export_all_event_csv": "Exportar todos los eventos a CSV", "export_all_syslog_csv": "Exportar todos los Syslog a CSV", "export_csv": "Exportar CSV", "export_pdf": "Exportar PDF", "sequential": "orden estricto", "smart_concurrent": "secuencia inteligente"}, "COMMAND_BAR": {"hide_automation_button": "Ocultar botones de automatización", "hide_button_panel": "Ocultar el panel de botones", "hide_detail": "<PERSON><PERSON><PERSON><PERSON> de<PERSON><PERSON>", "hide_group": "Ocultar grupos", "list_view": "Vista de lista", "show_automation_button": "Ver botones de automatización", "show_button_panel": "Ver panel de botones", "show_detail": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "show_group": "Ver grupos", "topology_view": "Vista de topología"}, "CONFIG_CENTER": {"alias_name": "Nombre de alias", "backup_config": "Copia de seguridad de la configuración", "backup_message": "MXview One archivará estos archivos de configuración", "backup_tab": "Copia de seguridad", "backup_tab_hint": "Vaya a la pestaña Copia de seguridad y exporte una configuración del dispositivo primero", "compare_config_basement": "Comparar s<PERSON>: {{compareConfigFileName}}", "compare_config_dialog_title": "Comparar configuraciones", "compare_tab": "Registros", "compare_target": "Compara<PERSON>", "configuration_file": "Archivo de configuración", "configuration_name": "Nombre de la configuración", "create_time": "Hora de creación", "delete_config_dialog_title": "Eliminar configuración", "delete_config_failed": "No se pudo eliminar la configuración del dispositivo", "delete_config_success": "La configuración del dispositivo se eliminó correctamente", "delete_config_warning_message": "¿Está seguro de que desea eliminar la configuración seleccionada?", "device_list": "Lista de dispositivos", "export_failed": "No se pudo exportar", "export_success": "Se exportó correctamente", "from_date": "Fecha de inicio", "group_name": "Grupo", "ip_address": "Dirección IP", "last_check_time": "Hora de la última comprobación", "local_file": "Archivo local", "restore_config": "Restaurar la configuración", "restore_device": "Restaurar dispositivo: {{selectedDeviceIP}}", "restore_tab": "Restaurar", "site_name": "Sitio", "time": "<PERSON><PERSON>", "title": "Centro de configuración de dispositivos", "to_date": "Fecha de finalización"}, "DASHBOARD": {"adpDestIp": "Los 5 principales eventos de políticas de ADP por IP de destino", "adpSrcIp": "Los 5 principales eventos de políticas de ADP por IP de origen", "ap_devices": "Dispositivos de PA", "ap_traffic_load": "Carga de tráfico de PA", "baseline": "Básico", "client_devices": "Dispositivos cliente", "critial_devices": "Dispositivos críticos", "device_availability": "Disponibilidad del dispositivo", "device_summary": "Resumen de dispositivos", "devices": "Dispositivos", "disk_space_utilization": "Utilización del espacio en disco", "dpiDestIp": "Los 5 principales eventos de políticas de filtrado de protocolos por IP de destino", "dpiSrcIp": "Los 5 principales eventos de políticas de filtrado de protocolos por IP de origen", "event_highlight": "Lo más destacado el evento", "healthy_devices": "Dispositivos en buenas condiciones", "icmp_unreachable": "ICMP inaccesible", "iec_level_1": "Mediano", "iec_level_2": "Alto", "ipsDestIp": "Los 5 principales eventos de políticas de IPS por IP de destino", "ipsSrcIp": "Los 5 principales eventos de políticas de IPS por IP de origen", "l3DestIp": "Los 5 principales eventos de políticas de capa 3 a 7 por IP de destino", "l3SrcIp": "Los 5 principales eventos de políticas de capa 3 a 7 por IP de origen", "last_1_day": "Último día", "last_1_hours": "Última hora", "last_1_weeks": "Última semana", "last_2_weeks": "Últimas 2 semanas", "last_24_hours": "Últimas 24 horas", "last_3_days": "Últimos 3 días", "last_3_hours": "Últimas 3 horas", "last_30_days": "Últimos 30 días", "last_30_minutes": "Últimos 30 minutos", "last_7_days": "Últimos 7 días", "last_update": "Última actualización:", "link_down": "Inactivo", "link_up": "Activo", "linkButton": "Mostrar registro de eventos", "not_pass": "No superado", "now": "<PERSON><PERSON>", "open": "Abrir", "pass": "Superado", "reboot_times": "Captura de arranque en frío/caliente", "refresh_all": "Actualizar todo", "security_level": "<PERSON><PERSON> de seguridad", "security_summary": "Resumen de seguridad", "selecting_visible_item": "Selección de elementos visibles", "set_default_tab": "Establecer como pestaña predeterminada", "tabs": {"cybersecurity": "Ciberseguridad", "general": "General", "wireless": "Inalámbrico"}, "title": "Panel de control", "total_availability": "La disponibilidad del dispositivo es inferior a {{param}}%.", "total_devices": "Total de dispositivos", "unknown": "Desconocido", "view_network_topology": "Ver la topología de la red", "warning_devices": "Dispositivos con advertencias", "wireless_device_summary": "Resumen de dispositivos inalámbricos"}, "DATABASE_BACKUP": {"database_name": "Nombre", "fail": "No se pudo realizar una copia de seguridad de la base de datos", "success": "Se realizó una copia de seguridad de la base de datos en {{param1}}", "title": "Copia de seguridad de base de datos"}, "DEVICE_LOCATOR": {"stop_trigger_locator": "Detener", "title": "Localizador de dispositivos", "trigger_locator": "Iniciar", "trigger_locator_fail": "No se pudo activar el localizador de dispositivos", "trigger_locator_off": "Localizador de dispositivos desactivado", "trigger_locator_on": "Localizador de dispositivos activado"}, "device_management": {"built_in_command_execution_process_is_running": "No se puede enviar el comando. Se está ejecutando otro comando. Vuelve a intentarlo más tarde.", "execute_fail": "Error al ejecutar", "limited_support": "El soporte es limitado, consulte el manual del usuario.", "select_device": "Seleccionar dispositivo", "select_operation": "Seleccionar operación"}, "DEVICE_PANEL": {"panel_status": "Estado del panel", "panel_zoom_size": "Tamaño de zoom del panel del dispositivo", "port": "Puerto"}, "DEVICE_POLLING_SETTING": {"consecutive_icmp_fail_trigger": "Fallos consecutivos antes de desencadenar un evento ICMP inaccesible", "consecutive_snmp_fail_trigger": "Fallos consecutivos antes de desencadenar un evento SNMP inaccesible", "icmp_polling_interval": "Intervalo de sondeo de ICMP", "snmp_polling_interval": "Intervalo de sondeo de SNMP", "title": "Configuración de sondeo"}, "DEVICE_PROPERTIES": {"basic_property": {"alias": "<PERSON><PERSON>", "availability": "Disponibilidad", "bios_version": "Versión de BIOS/cargador de arranque", "cpu_loading": "Carga de la CPU (%)", "cpu_loading_30_seconds": "Promedio Carga de la CPU: 30 segundos (%)", "cpu_loading_300_seconds": "Promedio Carga de la CPU: 300 segundos (%)", "cpu_loading_5_seconds": "Promedio Carga de la CPU: 5 segundos (%)", "cpu_utilization_300_seconds": "Utilización de CPU en los últimos 300 segundos (%)", "cpu_utilization_60_seconds": "Utilización de CPU en los últimos 60 segundos (%)", "cpu_utilization_900_seconds": "Utilización de CPU en los últimos 900 segundos (%)", "disk_utilization_unit": "Utilización de disco (%)", "fw_system_version": "Versión de firmware/imagen del sistema", "fw_version": "Versión del firmware/imagen del sistema", "mac_address": "Dirección MAC", "memory_usage": "Uso de la memoria", "memory_usage_unit": "Us<PERSON> de la memoria (%)", "model_name": "Nombre del modelo", "os_type": "Sistema operativo", "power_comsumption": "Consumo de energía (W)", "serial_number": "Número de serie", "system_contact": "Contacto del sistema", "system_description": "Descripción del sistema", "system_location": "Ubicación del sistema", "system_name": "Nombre del sistema", "system_object_id": "ID de objeto del sistema", "system_up_time": "System Up Time", "title": "Propiedades básicas del dispositivo"}, "cellular": {"cellular_carrier": "Cellular Carrier", "cellular_ip_address": "Cellular IP Address", "cellular_mode": "Cellular Mode", "cellular_signal": "Cellular Signal", "imei": "IMEI", "imsi": "IMSI", "title": "Cellular Information"}, "goose_table": {"app_id": "ID DE LA APLICACIÓN", "gocb_name": "Nombre del GoCB", "goose_address": "Dirección de GOOSE", "ied_name": "Nombre del IED", "port": "Puerto de entrada", "rx_counter": "Contador de recepción", "status": "Estado", "tampered_port": "Puerto alterado", "tampered_port_status": "El puerto {{ port }} se alteró", "title": "Comprobación de GOOSE", "type": "Tipo", "vid": "VID"}, "ipsec": {"l2tp_status": "Estado de L2TP", "local_gateway": "Puerta de enlace local", "local_subnet": "Subred local", "name": "Nombre de IPSec", "phase_1_status": "Estado de la fase 1", "phase_2_status": "Estado de la fase 2", "remote_gateway": "<PERSON><PERSON><PERSON> de enlace remota", "remote_subnet": "<PERSON><PERSON> remota", "title": "Estado de IPsec"}, "link": {"from": "<PERSON><PERSON>", "port": "Puerto", "sfpTitle": "Información de SFP", "speed": "Velocidad de vínculo", "title": "Información de vínculo", "to": "A"}, "management_interfaces": {"http_port": "Puerto HTTP", "https_port": "Puerto HTTPS", "profinet_enabled": "PROFINET habilitado", "ssh_port": "Puerto SSH", "telnet_port": "Puerto Telnet", "title": "Interfaces de gestión"}, "mms": {"title": "Propiedades de MMS"}, "modbus_device_property": {"model": "<PERSON><PERSON>", "revision": "Revisión", "title": "Propiedades del dispositivo Modbus", "vendor": "<PERSON><PERSON><PERSON><PERSON>"}, "not_selected": "Seleccione un módulo para mostrar los detalles del dispositivo", "other_device_properties": {"active_redundancy_protocol": "Protocolo de redundancia activa", "auto_ip_config": "IP Config automático", "default_gateway": "<PERSON><PERSON><PERSON> de enlace predeterminada", "dns_1_ip_address": "Dirección IP de DNS 1", "dns_2_ip_address": "Dirección IP de DNS 2", "ip_ad_ent_addr": "ipAdEntAddr", "ip_address": "Dirección IP (mib)", "mac_address": "Dirección MAC (mib)", "model_name": "Nombre del modelo", "monitor_current_mode": "Monitorizar el modo actual", "monitor_down_stream_rate": "Monitoreo de la velocidad de descarga", "monitor_snr": "Monitorizar la relación señal/ruido", "monitor_up_stream_rate": "Monitorizar la velocidad de subida", "netmask": "Máscara de red", "title": "Otras propiedades del dispositivo"}, "port": {"if_number": "ifNumber", "interface": "interfaz", "number_of_ports": "Número de puertos", "poe_port_class": "Clase de puerto PoE", "poe_power_legacy_pd_detect": "Detección de dispositivos alimentados por PoE heredados", "poe_power_output_mode": "Modo de salida de corriente PoE", "title": "Información del puerto"}, "power": {"power_1_status": "Estado de energía 1", "power_2_status": "Estado de energía 2", "title": "Estado de energía"}, "redundancy": {"active_redundancy_protocol": "Protocolo de redundancia activa", "dh": "Dual Homing", "iec_624393_redundancy_protocol": "Protocolo de redundancia de IEC 62439-3", "rstp": "RSTP", "tc": "Turbo Chain", "title": "Redundancia", "trv2": "Turbo Ring V2"}, "selected_module": "<PERSON><PERSON><PERSON><PERSON>", "snmp": {"1st_trap_community": "1ª comunidad de captura", "2nd_trap_server_community": "2ª comunidad de captura", "inform_enabled": "Inform habilitado", "inform_retries": "Reintentos de Inform", "inform_timeout": "Tiempo de espera de Inform", "read_community": "Comunidad de lectura", "title": "Información de SNMP", "trap_server_address_1": "Dirección del servidor de captura 1", "trap_server_address_2": "Dirección del servidor de captura 2"}, "title": "Propiedades del dispositivo", "vpn": {"from_ip": "VPN de la IP", "local_connection_name": "Nombre de la conexión VPN local", "remote_connection_name": "Nombre de la conexión VPN remota", "to_ip": "VPN a la IP"}, "wireless": {"channel_width": "Ancho de canal", "client_ip": "IP del cliente", "client_mac": "MAC del cliente", "client_RSSI": "Intensidad de la señal del cliente (dBm)", "rf_type": "Tipo de RF", "ssid_index": "Índice SSID", "title": "Información inalámbrica", "vap_mgmt_encryption": "Cifrado de gestión de VAP", "vap_wpa_encrypt": "Cifrado WPA de VAP", "vapAuthType": "Tipo de autenticación de VAP"}}, "DEVICE_SETTING": {"advanced": "<PERSON><PERSON><PERSON>", "alias": "<PERSON><PERSON>", "alias_input_invalid": "Ingrese un alias de dispositivo válido", "apply_fail": "No se pudieron actualizar los parámetros del dispositivo", "apply_success": "Los parámetros del dispositivo se actualizaron correctamente", "availability_time_frame": "Plazo para el cálculo de disponibilidad", "get_parameter_fail": "No se pudieron obtener los parámetros del dispositivo", "input_error_message": "Ingrese un valor válido", "modify_device_alias": "Modificar el alias ​​del dispositivo", "password": "Contraseña", "password_input_invalid": "Ingrese una contraseña de válida", "polling_interval": "Intervalo de sondeo", "polling_ip": "IP de sondeo", "snmp_configuration": "Configuración de SNMP", "snmp_port_invalid": "Ingrese un puerto SNMP válido", "title": "Configuración del dispositivo", "use_global": "Usar nombre de usuario y contraseña de acceso global", "use_global_device_settings": "Usar plantilla de configuración de dispositivo", "username": "Nombre de usuario", "username_input_invalid": "Ingrese un nombre de usuario válido"}, "DEVICE": {"device_properties": "Propiedades del dispositivo", "device_role": "Función del dispositivo", "filter_device": "Escriba para filtrar dispositivos", "filter_register_device": "Escriba para filtrar dispositivos registrados", "na": "N/A", "properties": {"availability": "Disponibilidad", "device_alias": "Alias ​​del dispositivo", "device_ip": "IP del dispositivo", "firmware_version": "Versión de firmware", "location": "Ubicación", "mac_address": "Dirección MAC", "model_name": "Nombre del modelo", "mxsec_flag": "Complemento de seguridad", "severity": "Gravedad"}, "registered_devices": "Registrado", "site_name": "Nombre del sitio", "title": "Lista de dispositivos", "unregistered_devices": "No registrado"}, "DeviceDashboard": {"avg_erase_count": "Promedio Eliminar cuenta", "change_disk_hint": "Cambie su disco", "chartTitle": {"60s_cpu": "Utilización de la CPU (últimos 60 segundos)", "connection": "Estado de la conexión", "cpu": "Utilización de la CPU", "disk": "Uso de disco", "memory": "Utilización de la memoria", "noiseFloor": "Ruido de fondo", "raid_mode": "Modo RAID", "signalStrength": "Intensidad de la señal", "smart": "S.M.A.R.T.", "snr": "SNR", "traffic": "Carga de tráfico"}, "connected": "Conectado", "current_status": "Estado actual:", "cycle_limitation": "Limitación del ciclo", "icmp_not_support": "Los dispositivos ICMP no admiten esta función", "link_down_port": "Puertos inactivos", "link_up_port": "Puertos activos", "managed": "Administrado", "migrating_data": "<PERSON><PERSON><PERSON> da<PERSON>", "no_raid": "Sin RAID", "normal": "Normal", "raid": "RAID", "rebuild": "Volver a generar", "smart_hint": "(Tecnología de análisis e informes de autocontrol) representa el estado del disco y la información sobre su vida útil", "unreachable": "{{ warningWording }} inaccesible. Es posible que MXview One no obtenga datos completos del dispositivo."}, "DIALOG": {"add_wifi_ssid": {"aes": "AES", "clear_all_existing_ssid": "Borrar todos los SSID existentes", "eapol_version": "Versión EAPOL", "encryption": "cifrado", "open": "Open", "passphrase": "contraseña", "personal": "Personal", "protected_management_frame": "marco de gestión protegido", "rf_band": "Banda de RF", "security": "Seguridad", "ssid": "SSID", "title": "Agregar SSID de Wi-Fi", "tkip_aes_mixed": "Híbrido TKIP/AES", "wpa_mode": "modo WPA"}, "auto_layout": {"desc": "¿Está seguro de que desea utilizar el diseño automático? (el diseño actual se anulará)", "title": "Diseño automático"}, "auto_topology": {"advanced": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> de la topología", "advanced_desc": "*El tiempo adicional es obligatorio.", "advanced_hint": "Establezca enlaces entre un dispositivo ICMP y un dispositivo que admita LLDP o tablas de reenvío", "fail": "Topología automática incorrecta", "link_check": "Modo de verificación de enlaces estrictos", "link_check_hint": "Si está habilitado, los enlaces entre dispositivos solo se mostrarán en la topología si los dispositivos en ambos extremos tienen la información del otro dispositivo en su tabla LLDP.", "new_topology": "Topología nueva", "new_topology_desc": "Los enlaces existentes se van a eliminar", "success": "Topología automática correcta", "title": "Topología automática", "update_topology": "Actualizar la topología", "update_topology_desc": "Los enlaces existentes se mantendrán al añadir nuevos"}, "background_dialog": {"content": "Establezca una imagen de fondo primero.\n La imagen de fondo puede ser un plano u otra imagen que represente el área de cobertura.", "set_now": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON> imagen de fondo"}, "change_group": {"assign_to_group": "Asignar al grupo", "change_group_fail": "No se pudieron mover los dispositivos al grupo nuevo", "change_group_success": "Los dispositivos se movieron al grupo nuevo correctamente", "current_group": "Grupo actual", "ip": "Dirección IP", "title": "Cambiar grupo"}, "change_wifi_channel": {"channel": "canal", "channel_width": "<PERSON><PERSON> de banda", "execute_button": "Cambiar", "title": "Cambiar canal wifi"}, "create_group": {"assign_group": "Asignar al grupo", "create_group_fail": "No se pudo crear el grupo", "create_group_success": "El grupo se creó correctamente", "current_group": "Grupo actual", "empty_group_name": "Debe ingresar un nombre para el grupo", "group_desc": "Descripción del grupo", "group_name": "Nombre del grupo", "parent_group": "Grupo principal", "title": "Crear grupo"}, "create_snapshot": {"execute_button": "crear", "title": "<PERSON><PERSON><PERSON>"}, "data_not_ready": {"content": "Los datos del dispositivo todavía no están listos, vuelva a intentarlo más tarde.", "title": "Vuelva a intentarlo más tarde"}, "delete_account": {"delete_confirm_message": "¿Está seguro de que desea eliminar esta cuenta?", "title": "Eliminar la cuenta"}, "delete_background": {"desc": "¿Está seguro de que desea eliminar la imagen de fondo?", "failed": "No se pudo eliminar la imagen de fondo", "success": "La imagen de fondo se eliminó correctamente", "title": "Eliminar fondo"}, "delete_custom_opc": {"delete_confirm_message": "¿Está seguro de que desea eliminar esta etiqueta OPC personalizada?", "title": "Eliminar la etiqueta OPC personalizada"}, "delete_device": {"delete_wireless_client_alert": "Los datos históricos de los dispositivos de los clientes inalámbricos eliminados también se eliminarán. Esto afectará a la rastreabilidad de las funciones enumeradas. ¿Desea continuar?", "delete_wireless_client_alert_title": "Confirmación de la eliminación del o los dispositivos", "desc": "¿Está seguro de que desea eliminar este dispositivo?", "desc_multi": "¿Está seguro de que desea eliminar estos dispositivos?", "failed": "No se pudieron eliminar el o los dispositivos", "success": "El o los dispositivos se eliminaron correctamente", "title": "Eliminar dispositivo"}, "delete_group": {"desc": "¿Está seguro de que desea eliminar este grupo?", "desc_multi": "¿Está seguro de que desea eliminar estos grupos?", "failed": "No se pudieron eliminar el o los grupos", "success": "El o los grupos se eliminaron correctamente", "title": "Eliminar grupo"}, "delete_link": {"desc": "¿Está seguro de que desea eliminar este enlace?", "desc_multi": "¿Está seguro de que desea eliminar estos enlaces?", "failed": "No se pudieron eliminar el o los enlaces", "success": "El o los enlaces se eliminaron correctamente", "title": "Eliminar enlace"}, "delete_objects": {"desc": "¿Está seguro de que desea eliminar todos los objetos seleccionados?", "failed": "No se pudieron eliminar los objetos seleccionados", "success": "Los objetos seleccionados se eliminaron correctamente", "title": "Eliminar objetos"}, "delete_site": {"desc": "¿Está seguro de que desea eliminar este sitio?", "failed": "No se pudo eliminar el sitio", "success": "El sitio se eliminó correctamente", "title": "Eliminar sitio"}, "device_settings": {"fail": "No se pudieron actualizar los parámetros de balance de enlace (por dispositivo)", "rx_antenna_gain": "Ganancia de la antena de recepción", "rx_cable_loss": "Pérdida de cable de recepción", "success": "Los parámetros de balance de enlace (por dispositivo) se actualizaron correctamente", "title": "Parámetros de balance de enlace (por dispositivo)", "tx_antenna_gain": "Ganancia de la antena de transmisión", "tx_cable_loss": "Pérdida de cable de transmisión"}, "disable_unsecured": {"execute_button": "Deshabilitar HTTP y Telnet", "title": "Deshabilite las consolas HTTP y Telnet inseguras"}, "disable_unused": {"execute_button": "Deshabilitar puertos no utilizados", "keep_port_available": "Mantener activo un puerto temporalmente interrumpido en uso", "title": "Deshabilite los puertos Ethernet y de fibra óptica no utilizados"}, "discovery_device": {"another_discovery_error": "Hay otra detección de dispositivos en curso", "discovering": "Detectando dispositivos", "discovery_finish": "Se detectaron dispositivos correctamente", "error": "No se pudieron detectar dispositivos", "title": "Detección de dispositivos"}, "dynamic_mac_sticky": {"address_limit": "Restricciones de dirección", "alias": "<PERSON><PERSON>", "mac_sticky": "Sticky MAC", "mac_sticky_settings": "Configuración Sticky MAC", "packet_drop": "soltar paquetes", "port": "puerto", "port_duplicated": "El puerto ya ha sido configurado.", "port_format_not_equal": "Los modelos deben tener el mismo formato de puerto y número de puertos.", "port_selection_guide": "clave de nombre de puerto", "port_shutdown": "cerrar puerto", "security_action": "acción segura", "title": "Sticky MAC dinámico"}, "export_config": {"config_center": "Centro de configuración", "config_file": "Archivo de configuración", "export": "Exportar", "fail": "No se pudo exportar la configuración del dispositivo", "hint": "* Please make sure the username and password for this device are correctly set in \"Advanced Settings\"", "success": "La configuración del dispositivo se exportó correctamente", "title": "Exportar configuración"}, "goose": {"how_to_resolve": "¿Cómo resolverlo?", "import_scd_tooltip": "Importe un archivo SCD para ver los mensajes de GOOSE. Haga clic en \"Potencia\" > \"Importar SCD\".", "ip_port": "Puerto {{ port }} de {{ ip }}", "open_web_console": "Abra la consola web", "port_tampered_msg": "El problema de alteración del puerto de GOOSE se provocó debido a lo siguiente:", "port_tampered_title": "Resolver el problema de alteración del puerto de GOOSE", "reset_goose": "Restablecer el mensaje de GOOSE alterado", "reset_goose_desc": "Mensaje de GOOSE: {{ cbName }}/{{ appId }}/{{ mac }}", "reset_goose_title": "¿Está seguro de que desea restablecer todas las instancias de este mensaje de GOOSE alterado?", "resolve_goose_tampered_desc_1": "Intente realizar estos pasos para resolver el problema de alteración del puerto de GOOSE", "resolve_goose_tampered_desc_2": "1. Compruebe la configuración del o los IED", "resolve_goose_tampered_desc_3": "Asegúrese de que los mensajes de publicación/suscripción de GOOSE del IED estén configurados correctamente.", "resolve_goose_tampered_desc_4": "2. Compruebe el estado del puerto", "resolve_goose_tampered_desc_5": "Compruebe el estado del puerto {{ port }} de {{ ip }}.", "resolve_goose_tampered_desc_6": "2. <PERSON>mp<PERSON><PERSON> que todos los dispositivos estén autorizados", "resolve_goose_tampered_desc_7": "Compruebe si hay algún dispositivo no autorizado en la red.", "resolve_goose_tampered_desc_8": "Intente realizar estos pasos para resolver el problema de alteración de GOOSE SA", "resolve_goose_timeout_desc_1": "Intente realizar estos pasos para resolver los problemas de tiempo de espera de GOOSE.", "resolve_goose_timeout_desc_10": "¿Sigue sin funcionar?", "resolve_goose_timeout_desc_11": "Retire el módulo de SFP y vuelva a instalarlo.", "resolve_goose_timeout_desc_12_1": "Si tiene más preguntas, póngase en contacto con su", "resolve_goose_timeout_desc_12_2": "socio de canal", "resolve_goose_timeout_desc_12_3": "primero.", "resolve_goose_timeout_desc_13_1": "Contactar", "resolve_goose_timeout_desc_13_2": "Soporte técnico de Moxa", "resolve_goose_timeout_desc_13_3": "si necesita asistencia adicional.", "resolve_goose_timeout_desc_2": "1. Compruebe la configuración del o los IED", "resolve_goose_timeout_desc_3": "Asegúrese de que los mensajes de publicación/suscripción de GOOSE del IED estén configurados correctamente.", "resolve_goose_timeout_desc_4": "2. Asegú<PERSON><PERSON> de que el puerto no esté en estado de vínculo inactivo", "resolve_goose_timeout_desc_5": "Compruebe que el puerto de ningún dispositivo del flujo de GOOSE ({{ cbName }}/{{ appId }}/{{ mac }}) esté en estado de vínculo caído.", "resolve_goose_timeout_desc_6": "3. Asegúrese de que el puerto no tenga ningún error de transmisión/recepción.", "resolve_goose_timeout_desc_7": "Haga clic en un enlace y seleccione \"Tráfico de enlaces\" para ver la sección \"Tasa de errores de paquetes\". Asegúrese de que el puerto no tenga ningún error.", "resolve_goose_timeout_desc_8": "4. Compruebe que los puertos de fibra no superen los umbrales", "resolve_goose_timeout_desc_9": "Haga clic en \"SFP\" ➔ \"Lista de SFP\". Asegúrese de que los puertos no superen los umbrales.", "sa_tampered_msg": "GOOSE SA alterado", "sa_tampered_name_msg": "El mensaje de GOOSE ({{ cbName }}/{{ appId }}/{{ mac }}) entra en conflicto con otra dirección de origen de GOOSE.", "sa_tampered_title": "Resolver el problema de la alteración de GOOSE SA", "tampered": "Alterado", "timeout": "Tiempo de espera", "timeout_msg": "La causa del problema del tiempo de espera de GOOSE fue la siguiente:", "timeout_title": "Resolver el problema del tiempo de espera de GOOSE"}, "import_config": {"config_center": "Centro de configuración", "config_file": "Archivo de configuración", "config_file_error": "MXview One solo admite archivos de configuración .ini", "config_file_size_error": "El archivo debe tener un tamaño inferior a 3 MB.", "fail": "No se pudo importar la configuración del dispositivo", "hint": "* Please make sure the username and password for this device are correctly set in \"Advanced Settings\"", "import": "Importar", "success": "La configuración del dispositivo se importó correctamente", "title": "Importar configuración"}, "link_traffic": {"date": "<PERSON><PERSON>", "from": "<PERSON><PERSON>", "packet_error_rate_title": "Tasa de errores de paquetes", "port_traffic_title": "Tráfico de puertos", "time": "Time", "to": "<PERSON><PERSON>", "utilization": "Utilización", "value": "Value"}, "mac_sticky_switch": {"mac_sticky": "Sticky MAC", "title": "Activar/desactivar Sticky MAC"}, "maintain_group": {"change_icon": "Cambiar el icono del grupo", "create": "<PERSON><PERSON><PERSON>", "create_group_fail": "No se pudo crear el grupo", "create_group_success": "El grupo se creó correctamente", "delete": "Eliminar", "delete_group_fail": "No se pudo eliminar el grupo", "delete_group_success": "El grupo se eliminó correctamente", "empty_group_name": "Debe ingresar un nombre para el grupo", "group_desc": "Descripción del grupo", "group_name": "Nombre del grupo", "modify_group_fail": "No se pudo modificar el grupo", "modify_group_success": "El grupo se modificó correctamente", "reset_icon": "Restablecer a la imagen predeterminada", "title": "Mantenimiento de grupos"}, "ping": {"failed": "Error de ping", "title": "<PERSON>"}, "policy_profile": {"delete_msg": "¿Está seguro de que desea eliminar los perfiles seleccionados?", "delete_title": "Eliminar perfil(es)"}, "reboot": {"execute_button": "<PERSON><PERSON><PERSON>", "reboot_sequence": "Secuencia de reinicio", "title": "<PERSON><PERSON><PERSON>", "unable_determine_reboot_sequence_hint": "No se puede determinar la secuencia de reinicio. Asegúrese de que la topología contenga una computadora con MXview One instalado e inténtelo nuevamente."}, "relearn_dynamic_mac_sticky": {"execute_button": "estudiar de nuevo", "title": "Vuelva a aprender Sticky MAC dinámico"}, "restore_to_create_snapshot": {"execute_button": "recuperar", "title": "Restaurar desde una instantánea"}, "scd": {"import_failed_desc": "Consulte la lista de problemas a continuación, corrija el archivo SCD e intente volver a realizar la importación.", "import_failed_title": "No se pudo importar el archivo SCD", "import_succeed_desc": "Los mensajes de GOOSE y el diseño del flujo se incorporaron correctamente a la topología de la red.", "import_succeed_title": "Los archivos SCD se importaron correctamente", "missing": "<PERSON><PERSON><PERSON><PERSON>", "missing_device": "No se pueden encontrar los siguientes dispositivos:", "missing_device_desc": "Consulte la siguiente lista de problemas.", "scd_file_error": "MXview One solo admite archivos .scd", "scd_file_size_error": "El archivo debe tener un tamaño inferior a 100 MB", "select_file": "Seleccionar archivo", "tag": "etiqueta", "topology_change_desc_1": "Intente realizar estos pasos para resolver los problemas.", "topology_change_desc_2": "1. Añada el o los dispositivos que faltan", "topology_change_desc_3": "Haga clic en \"Editar\" ➔ \"Añadir dispositivo\".", "topology_change_desc_4": "2. Vuelva a importar el archivo SCD", "topology_change_desc_5": "Haga clic en \"Potencia\" ➔ \"Importar SCD\".", "topology_change_title": "Problemas al cambiar la topología", "visualize_goose_messages": "Para visualizar los mensajes de GOOSE entre los IED, primero, debe importar un archivo SCD", "visualize_goose_messages_title": "Visualización de mensajes de GOOSE"}, "set_background": {"browse": "examine", "desc": "Arrastre una imagen aquí o ", "desc1": " para establecer el fondo", "failed": "No se pudo establecer la imagen de fondo", "image": "Imagen", "image_alpha": "Alfa", "image_error": "El archivo seleccionado no es una imagen", "image_position": "Posición", "image_saturation": "Saturación", "image_x": "X", "image_y": "Y", "preview": "Vista previa", "size_error": "El tamaño de la imagen debe ser de 1 KB a 20 MB", "success": "La imagen de fondo se configuró correctamente", "title": "<PERSON><PERSON><PERSON>o", "topology_size": "Tamaño de la topología"}, "set_document": {"current_filename": "Nombre de archivo actual", "delete": "Eliminar documento", "error": "MXview One solo admite documentos PDF", "failed": "No se pudo establecer el documento", "file_size_error": "El documento debe ser un archivo PDF de 20 MB como máximo.", "open": "Abrir documento", "set": "Establecer documento", "success": "El documento se estableció correctamente", "title": "Establecer documento", "upload": "Seleccione un archivo para cargar"}, "set_port_label": {"error": "Error", "failed": "No se pudo establecer la etiqueta del puerto", "from": "De:", "success": "La etiqueta del puerto se estableció correctamente", "title": "Establecer etiqueta de puerto", "to": "A:", "use_custom_label": "Usar una etiqueta personalizada", "vpn_link_desc": "Las etiquetas de los enlaces de la VPN no se pueden configurar."}, "set_scale": {"error": "Error de parámetro", "fail": "No se pudo establecer la escala", "success": "La escala se estableció correctamente", "title": "<PERSON><PERSON><PERSON> escala"}, "severity_threshold": {"bandwidth_input_invalid": "Ingrese un número entero de 0 a 100", "bandwidth_utilization": "Utilización del ancho de banda", "critical": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "failed": "No se pudo actualizar el umbral de gravedad", "information": "Información", "over": "Por encima de", "packet_error_rate": "Tasa de errores de paquetes", "sfp_rx_over": "Recepción de SFP por encima de", "sfp_rx_under": "Recepción de SFP por debajo de", "sfp_temp_over": "Temperatura de SFP por encima de", "sfp_threshold": "Umbral de SFP", "sfp_tx_over": "Transmisión de SFP por encima de", "sfp_tx_under": "Transmisión de SFP por debajo de", "sfp_vol_over": "Voltaje de SFP por encima de", "sfp_vol_under": "Voltaje de SFP por debajo de", "success": "El umbral de gravedad se actualizó correctamente", "title": "Umbral de gravedad", "under": "Por debajo de", "warning": "Advertencia"}, "sfp_info": {"date": "<PERSON><PERSON>", "dateError": "Intervalo de fechas no válido", "from": "<PERSON><PERSON>", "port": "Puerto", "sfpRxPower": "Recepción de SFP", "sfpRxPower_label": "Recepción", "sfpRxPower_scale": " dBm", "sfpTemperature": "Temperatura de SFP", "sfpTemperature_label": "Temp.", "sfpTemperature_scale": " °C", "sfpTxPower": "Transmisión de SFP", "sfpTxPower_label": "Transmisión", "sfpTxPower_scale": " dBm", "sfpVoltage": "Voltaje de SFP", "sfpVoltage_label": "Voltaje", "sfpVoltage_scale": " V", "time": "<PERSON><PERSON>", "title": "Información de SFP", "to": "A", "value": "Valor"}, "sfp_sync": {"confirm_desc": "¿Está seguro de que desea sincronizar el umbral de SFP desde el dispositivo?", "content": "Puede sincronizar el umbral de SFP desde un conmutador Moxa. Después de la sincronización, la temperatura y la potencia de transmisión y recepción para la comprobación de la fibra se sincronizarán con el umbral de SFP de cada enlace.", "failed": "No se pudo sincronizar el umbral de SFP", "hint": "*Para comprobar el umbral de SFP, puede hacer clic en un enlace y, luego, seleccionar Umbral de gravedad ➔ Umbral de SFP.", "success": "El umbral de SFP se sincronizó correctamente", "title": "Sincronizar el umbral de SFP desde los dispositivos"}, "wireless_settings": {"fail": "No se pudieron actualizar los parámetros de balance de enlace (general)", "rxSensitivityHigh": "Sensibilidad de recepción alta", "rxSensitivityLow": "Sensibilidad de recepción baja", "rxSensitivityMedium": "Sensibilidad de recepción media", "sr": "Factor de seguridad reservado", "success": "Los parámetros de balance de enlace (general) se actualizaron correctamente", "title": "Parámetros de balance de enlace (general)"}}, "EMBED_WIDGET": {"click_preview": "Haga clic en la vista previa", "copied_to_clipboard": "El enlace se copió al portapapeles", "copy_link": "<PERSON><PERSON><PERSON> enlace", "custom": "Personalización", "desc": "Pegue esto en cualquier página HTML", "embed": "Insertar", "height": "Alto", "layout_1": "Diseño 1", "layout_2": "Diseño 2", "layout_3": "Diseño 3", "layout_4": "Diseño 4", "link": "Enlace", "no_api_key": "Debe crear una clave de API primero", "preview": "Vista previa", "recent_event": "Eventos recientes", "select_api_key": "Seleccione la clave de API", "select_layout": "Seleccione el diseño", "title": "Widget web integrado", "topology": "Topología", "topology_recent_event": "Topología y eventos recientes", "width": "<PERSON><PERSON>"}, "error_handler": {"error_session_expired_dialog": "La sesión caducó. El sistema lo redirigirá a la página de inicio de sesión."}, "ERROR_MESSAGE": {"get_data_fail": "Error al recuperar datos", "input_invalid_char": "Ingrese un nombre válido", "input_invalid_characters": "Este campo no puede contener ninguno de los siguientes caracteres: #%&*:<>?|{}\\\"/", "input_invalid_contact": "Ingrese un contacto válido", "input_invalid_email": "Ingrese una dirección de correo electrónico válida", "input_invalid_location": "Ingrese una ubicación válida", "input_invalid_mac": "Dirección MAC no válida", "input_invalid_password_characters": "Este campo no puede contener ninguno de los siguientes caracteres: '\\\"/`", "input_invalid_script": "Este campo no puede contener ninguno de los siguientes caracteres: #%&amp;*{}|:&quot;&lt;&gt;?/\\", "input_ip_invalid": "Ingrese una dirección IP válida", "input_required": "Este valor es obligatorio", "non_restricted_ascii": "Caracteres ASCII, excepto ' \\\" ` \\\\"}, "errors": {"A001": "Faltan campos requeridos", "A002": "Cadena de consulta faltante", "A003": "Formato incorrecto", "D001": "Se alcanzó el número máximo de licencias", "D002": "No se puede encontrar el dispositivo", "D003": "El dispositivo debe estar en línea", "D004": "El dispositivo fue eliminado", "F001": "No se puede encontrar el firmware", "F002": "Este firmware ya existe", "F003": "Se alcanzó el número máximo de archivos de firmware", "G001": "Este grupo ya existe", "G002": "No se puede encontrar el grupo", "G003": "El grupo predeterminado no se puede modificar", "G004": "Los usuarios administradores no pueden asignarse a grupos", "I001": "Esta interfaz ya existe", "I002": "No se puede encontrar la interfaz", "I003": "El grupo predeterminado no se puede modificar", "I004": "Esta interfaz está referenciada por un perfil de seguridad.", "L001": "El código de activación no es válido", "L002": "La licencia ha expirado", "L003": "Código de activación duplicado", "L004": "Se alcanzó el número máximo de nodos", "L005": "El dispositivo no se puede activar ni desactivar", "L006": "Hora de inicio no válida", "L007": "Hora de inicio no válida para el nuevo tipo de licencia", "O001": "Este objeto ya existe", "O002": "No se puede encontrar el objeto", "O003": "Este objeto está referenciado por un perfil de seguridad.", "P001": "No se puede encontrar el paquete", "P002": "Este paquete ya existe", "P003": "Se alcanzó el número máximo de paquetes", "P004": "Versión no compatible", "S001": "Error de actualización de base de datos", "SP001": "Este perfil ya existe", "SP002": "No se puede encontrar el perfil", "T001": "Token no autorizado", "T002": "<PERSON><PERSON> caducada", "T003": "Simbolo no valido", "U001": "<PERSON><PERSON><PERSON> den<PERSON>ado", "U002": "Este nombre de usuario ya existe", "U003": "No se puede encontrar el usuario", "U004": "No se puede encontrar el rol", "U005": "Nombre de usuario/contraseña no válidos", "U006": "La contraseña no cumple con la longitud mínima", "U007": "La contraseña excede la longitud máxima", "U008": "La contraseña no puede ser la misma que el nombre de usuario", "U009": "Debe incluir al menos un carácter en mayúscula", "U010": "Debe incluir al menos un carácter en minúscula", "U011": "<PERSON>be incluir al menos un dígito", "U012": "Debe incluir al menos un carácter no alfanumérico", "U013": "La contraseña no puede ser la misma que la contraseña anterior", "U014": "nombre de usuario inválido", "Unknown": "Error descon<PERSON>"}, "EULA": {"agree_hint": "Acepte el acuerdo para usar MXview One.", "eula_title": "EULA"}, "EVENT_MENU": {"ack": "Reconocer", "ack_all": "Reconocer todo", "filter_event": "Condiciones de filtro"}, "EVENT": {"ack_all_events": {"ack_all_event_success": "Todos los eventos se reconocieron correctamente", "button_ack_hint": "Ack Selected Event(s)", "button_hint": "Reconocer todos los eventos", "confirm_message": "Se reconocerán todos los eventos. ¿Está seguro de que desea continuar con este proceso?", "unable_ack_all_event": "No se pudieron reconocer todos los eventos"}, "ack": {"ack_fail": "No se pudo reconocer el evento", "acked": "Reconocidos", "any": "<PERSON><PERSON><PERSON><PERSON>", "unacked": "No reconocidos"}, "all_event": "Historial de eventos", "all_group": "Todos los gruposs", "all_site": "Todos los sitios", "clear_all_events": {"button_hint": "Borrar todos los eventos", "clear_all_event_success": "Todos los eventos se borraron correctamente", "confirm_message": "Todos los eventos se borrarán. ¿Está seguro de que desea continuar con este proceso?", "confirm_message_network": "Se borrarán todos los eventos de red y de dispositivos. ¿Está seguro de que desea continuar con este proceso?", "confirm_message_system": "Se borrarán todos los eventos del sistema. ¿Está seguro de que desea continuar con este proceso?", "unable_clear_all_event": "No se pudieron borrar todos los eventos"}, "custom_events": {"activate": "Habilitar evento personalizado", "add_custom_event": "Agregar un evento personalizado", "all": "Todos", "all_devices": "Todos los dispositivos", "apply_fail": "No se pudo agregar el evento personalizado", "apply_success": "El evento personalizado se agregó correctamente", "below": "Por debajo de", "condition": "Condición", "condition_operator": "Operador de condición", "condition_value": "Valor de condición", "consecutive_updates": "Sondeos consecutivos", "delete_fail": "No se pudieron eliminar el o los eventos personalizados", "delete_success": "El o los eventos personalizados se eliminaron correctamente", "description": "Descripción", "device_properties": "Propiedades del dispositivo", "devices": "Dispositivos", "duration": "Duración", "equal": "Igual a", "event_name": "Nombre del evento", "filter_custom_event": "Escriba para filtrar eventos personalizados", "get_fail": "No se pudieron obtener los eventos personalizados", "helper": "Agregue eventos personalizados y asígneles dispositivos", "not_equal": "Dist<PERSON><PERSON> de", "over": "Por encima de", "recovery_description": "Descripción de la recuperación", "register": "Registrar", "register_devices": "Dispositivos registrados", "search": "Buscar", "severity": "Gravedad", "title": "Evento personalizado", "update_custom_event": "Actualizar el evento personalizado", "update_fail": "No se pudo actualizar el evento personalizado", "update_success": "El evento personalizado se actualizó correctamente"}, "event_description": {"abc_attache_warning": "Dispositivo ABC conectado", "abc_auto_import_warning": "No se pudo importar la configuración automáticamente", "abc_config_warning": "No se pudo exportar el archivo de configuración", "abc_detache_warning": "Dispositivo ABC desconectado", "abc_log_warning": "No se pudo exportar el archivo de registro", "abc_space_warning": "No hay suficiente espacio en el dispositivo ABC", "abc_unauthorized_warning": "Se detectó un medio no autorizado", "abc_unknow_warning": "Error descon<PERSON>", "abc02_warning": "Evento de USB: {{param}}", "account_audit_baseline_failed": "No se pudo completar la auditoría de la cuenta. No se pueden recuperar datos de todos los dispositivos.", "account_audit_baseline_match": "La auditoría de cuenta se completó con éxito. El resultado coincide con la línea de base.", "account_audit_failed": "No se pudo completar la auditoría de la cuenta. No se pueden recuperar datos de los dispositivos en {{ip}}.", "account_audit_match": "La auditoría de cuenta se completó con éxito. El resultado coincide con la línea de base.", "account_audit_mismatch": "La auditoría de cuenta se completó con éxito. El resultado no coincide con la línea de base.", "account_audit_unable_retrieve_device": "No se pudo completar la auditoría de la cuenta. No se pueden recuperar datos de todos los dispositivos.", "accountAudit1": "La auditoría de cuentas no coincide con la línea de base", "accountAudit2": "No se pudo completar la auditoría de cuenta", "all_event_clear": "Se borraron todos los eventos", "auth_fail": "No se pudo autenticar el inicio de sesión del dispositivo.", "availability_down": "Disponibilidad del dispositivo por debajo del umbral", "availability_down_recovery": "Se recuperó de la condición de disponibilidad del dispositivo por debajo del umbral", "background_scan_found": "Nuevo dispositivo encontrado (IP: {{ip}}).", "cli_button_event_all_failed": "{{user}} de IP: {{sourceIP}} ejecutó el botón: {{cliName}}. El resultado de la ejecución es Todo fallido.", "cli_button_event_all_finished": "{{user}} de IP: {{sourceIP}} ejecutó el botón: {{cliName}}. El resultado de la ejecución es Todo terminado.", "cli_button_event_all_partially_finished": "{{user}} de IP: {{sourceIP}} ejecutó el botón: {{cliName}}. El resultado de la ejecución es Parcialmente finalizado.", "cli_button_event_start": "{{user}} de IP: {{sourceIP}} ejecutó el botón: {{cliName}}.", "cli_saved_script_event": "{{user}} de IP: {{sourceIP}} ejecutó el comando CLI: {{cliName}}", "cli_script_event": "{{user}} de IP: {{sourceIP}} comienza a ejecutar la CLI.", "cold_start": "Arranque en frío", "custom_event_detail": "{{param1}}. Threshold={{param2}}, value={{param3}}. {{param4}}", "custom_event_recovery": "Evento personalizado recuperado", "custom_event_recovery_detail": "{{param1}} recuperado. Threshold={{param2}}, value={{param3}}. {{param4}}", "custom_event_trigger": "Evento personalizado activado", "cybersecurity_event_trigger": "Evento de ciberseguridad", "ddos_under_attack": "Se detectó un ataque DoS", "ddos_under_attack_recovery": "Se recuperó de un ataque DoS", "device_configuration_change": "La configuración del dispositivo ha cambiado.", "device_firmware_upgrade": "Se actualizó el firmware del dispositivo", "device_infom_receive": "Se recibió Inform del dispositivo", "device_lockdown_violation": "Violación del bloqueo del dispositivo", "device_power_down": "Activar {{param}}", "device_power_down_off_to_on": "potencia {{param}} Desactivado > Activado.", "device_power_on": "Desactivar {{param}}", "device_power_on_to_off": "potencia {{param}} Activado > Desactivado.", "device_snmp_reachable": "Dispositivo SNMP accesible", "device_snmp_unreachable": "Dispositivo SNMP inaccesible", "di_off": "DI {{param}} desactivado", "di_on": "DI {{param}} activado", "disk_space_not_enough": "El espacio de disco disponible está por debajo del umbral", "event_config_import": "Se importó un archivo de configuración nuevo", "event_ddos_attack": "Ataque DoS detectado", "event_ddos_attack_recovery": "Se recuperó de un ataque DoS", "event_dying_gasp": "<PERSON><PERSON><PERSON> del sistema, dispositivo alimentado por condensador", "event_eps_is_off": "Fuente de alimentación externa PoE apagada", "event_eps_is_on": "Fuente de alimentación externa PoE encendida", "event_firewall_attack": "Infracción de la política de firewall", "event_firewall_attack_recovery": "Se recuperó de la infracción de la política de firewall", "event_ieee_lldp_table_change": "{{trapDetail}}", "event_ieee_rstp_root_change": "Se seleccionó una raíz nueva de RSTP en la topología", "event_ieee_rstp_topology_change": "Topología modificada por RSTP", "event_linux_account_setting_change": "Se modificó la configuración de la cuenta de {{username}}", "event_linux_config_change": "El {{modules}} la configuración fue modificada por {{username}}.", "event_linux_config_import": "{{Username}} modificó la importación de configuración {{successOrFail}}", "event_linux_config_import_failed": "<PERSON>o", "event_linux_config_import_succeed": "correcto", "event_linux_coupling_change": "Se modificó el estado de la ruta de acoplamiento de Turbo Ring v2", "event_linux_di_off": "Entrada digital {{index}} desactivada", "event_linux_di_on": "Entrada digital {{index}} activada", "event_linux_dotlx_auth_fail": "La autenticación 802.1X falló en el puerto {{portIndex}} debido a {{reason}}", "event_linux_dual_homing_change": "Se modificó la ruta de dual homing", "event_linux_log_capacity_threshold": "El número de entradas del registro de eventos {{value}} alcanzó el umbral", "event_linux_low_input_voltage": "El voltaje de entrada de la fuente de alimentación está por debajo del umbral", "event_linux_master_change": "Se modificó el master de Ring {{index}}", "event_linux_master_mismatch": "La configuración maestra de Ring {{index}} no concuerda", "event_linux_over_power_budget_limit": "La potencia consumida {{value}} de todos los dispositivos alimentados superó la potencia de entrada máxima {{threshold}}", "event_linux_password_change": "Se modificó la contraseña de {{username}}", "event_linux_pd_no_response": "El dispositivo del puerto PoE {{portIndex}} no responde a la verificación de fallas del dispositivo alimentado", "event_linux_pd_over_current": "La corriente del puerto PoE {{portIndex}} superó el límite de seguridad", "event_linux_pd_power_off": "Dispositivo alimentado por puerto PoE {{portIndex}} apagado", "event_linux_pd_power_on": "Dispositivo alimentado por puerto PoE {{portIndex}} encendido", "event_linux_port_recovery_by_ratelimit": "El puerto {{portIndex}} se recuperó del apagado debido a que el tráfico había superado el límite de velocidad", "event_linux_port_shutdown_by_ratelimit": "Puerto {{portIndex}} está bloqueado debido a que el tráfico excede el límite de velocidad.", "event_linux_port_shutdown_by_security": "El puerto {{portIndex}} se apagó por la seguridad del puerto", "event_linux_power_detection_fail": "El dispositivo del puerto PoE {{portIndex}} es {{devicetype}}, {{suggestion}}", "event_linux_power_detection_fail_devietype_na": "N/A", "event_linux_power_detection_fail_devietype_noPresent": "no presente", "event_linux_power_detection_fail_devietype_unknown": "desconocido", "event_linux_power_detection_fail_suggestion_disable_POE": "desactivar PoE", "event_linux_power_detection_fail_suggestion_enable_legacy": "habilitar heredado", "event_linux_power_detection_fail_suggestion_enable_POE": "activar PoE", "event_linux_power_detection_fail_suggestion_no": "revise su conexión", "event_linux_power_detection_fail_suggestion_rais_EPS_voltage": "Aumentar el voltaje de EPS", "event_linux_power_detection_fail_suggestion_select_auto": "seleccionar automático", "event_linux_power_detection_fail_suggestion_select_force": "seleccionar fuerza", "event_linux_power_detection_fail_suggestion_select_high_power": "seleccionar alta potencia", "event_linux_power_off": "Alimentación {{index}} apagada", "event_linux_power_on": "Alimentación {{index}} encendida", "event_linux_redundant_port_health_check": "Error en la comprobación de estado del puerto redundante {{portIndex}}", "event_linux_RMON_trap_is_falling": "La captura de RMON está cayendo", "event_linux_RMON_trap_is_raising": "La captura de RMON está subiendo", "event_linux_rstp_invalid_bpdu": "El puerto RSTP {{portIndex}} recibió una BPDU no válida (escriba: {{type}}, value:{{value}})", "event_linux_rstp_migration": "El puerto {{portIndex}} cambió de {{originTopology}} a {{changeTopology}}", "event_linux_rstp_new_port_role": "El rol del puerto RSTP {{portIndex}} cambió de {{originalRole}} a {{newRole}}", "event_linux_ssl_cer_change": "Se modificó el certificado SSL", "event_linux_topology_change": "La topología ha cambiado.", "event_linux_topology_change_by_type": "Topología modificada por {{topologyType}}", "event_linux_user_login_lockout": "{{username}} se bloqueó debido a {{param}} intentos fallidos de inicio de sesión", "event_linux_user_login_success": "{{username}} inició sesión correctamente a través de {{interface}}", "event_log_cleared_trap_event_info": "The event logs were cleared (User: {{ user }}, IP: {{ ip }}, Interface: {{ interface }})", "event_message_serial_device_port_any_recovery": "El puerto serie {{portnum}} ha reanudado su funcionamiento normal.", "event_message_serial_device_port_break": "El puerto serie {{portnum}} recibió un error: recuento de errores de interrupción.", "event_message_serial_device_port_frame": "El puerto serie {{portnum}} recibió un error: recuento de errores de trama.", "event_message_serial_device_port_overrun": "El puerto serie {{portnum}} recibió un error: Recuento de errores de desbordamiento.", "event_message_serial_device_port_parity": "El puerto serie {{portnum}} recibió un error: Recuento de errores de paridad.", "event_message_serial_device_port_rx": "El RX del puerto serie {{portnum}} no recibió ningún dato en los últimos {{min}} minuto(s).", "event_message_serial_device_port_rx_recovery": "El RX del puerto serie {{portnum}} ha reanudado la recepción de datos.", "event_message_serial_device_port_rxtx": "El RX y TX del puerto serial {{portnum}} no recibieron ningún dato en los últimos {{min}} minuto(s).", "event_message_serial_device_port_rxtx_recovery": "El RX y TX del puerto serie {{portnum}} ha reanudado la recepción de datos.", "event_message_serial_device_port_tx": "La transmisión del puerto serie {{portnum}} no recibió ningún dato en los últimos {{min}} minuto(s).", "event_message_serial_device_port_tx_recovery": "El TX del puerto serie {{portnum}} ha reanudado la recepción de datos.", "event_not_found_type": "Unknown event type: {{ eventType }}", "event_pd_check_fail": "Comprobación de fallas del dispositivo alimentado (sin respuesta)", "event_pd_over_current": "Sobrecarga/cortocircuito del puerto PoE {{portIndex}}", "event_pd_power_off": "Puerto PoE {{portIndex}} apagado", "event_pd_power_on": "Puerto PoE {{portIndex}} encendido", "event_prp_function_fail": "Error en la función PHR", "event_serial_device_port_break": "El puerto serie recibió un error: recuento de errores de interrupción", "event_serial_device_port_frame": "El puerto serie recibió un error: recuento de errores de trama", "event_serial_device_port_overrun": "El puerto serie recibió un error: recuento de errores de desbordamiento", "event_serial_device_port_parity": "El puerto serie recibió un error: recuento de errores de paridad", "event_serial_device_port_rx": "El RX del puerto serial no recibió ningún dato", "event_serial_device_port_rxtx": "El RX y TX del puerto serie no recibieron ningún dato", "event_serial_device_port_tx": "El TX del puerto serial no recibió ningún dato", "event_sfp_rx_below": "La potencia RX ({{currentdB}} dBm) del puerto SFP {{portIndex}} está por debajo del umbral ({{thresholddB}} dBm).", "event_sfp_rx_below_recovery": "Se recuperó la recepción del puerto SFP {{portIndex}} de {{recoverydB}} dBm", "event_sfp_temp_over": "La temperatura del módulo ({{currentTemp}}ºc) del puerto SFP {{portIndex}} ha superado el umbral ({{currentTemp}}ºc).", "event_sfp_temp_over_recovery": "Se recuperó la temperatura del puerto SFP {{portIndex}} de {{recoveryTemp}} ºC", "event_sfp_tx_below": "La potencia de transmisión ({{currentdB}} dBm) del puerto SFP {{portIndex}} está por debajo del umbral ({{thresholddB}} dBm).", "event_sfp_tx_below_recovery": "Se recuperó la transmisión del puerto SFP {{portIndex}} de {{recoverydB}} dBm", "event_sfp_voltage_below": "El voltaje del módulo ({{currentVoltage}} V) del puerto SFP {{portIndex}} está por debajo del umbral ({{thresholdVoltage}} V).", "event_sfp_voltage_below_recovery": "Se recuperó el voltaje del puerto SFP {{portIndex}} de {{recoveryVoltage}} V", "event_sfp_voltage_over": "El voltaje del módulo ({{currentVoltage}} V) del puerto SFP {{portIndex}} ha superado el umbral ({{thresholdVoltage}} V).", "event_sfp_voltage_over_recovery": "Se recuperó el voltaje del puerto SFP {{portIndex}} de {{recoveryVoltage}} V", "event_too_many_login_failure": "El acceso a la interfaz web se bloqueó temporalmente debido a demasiadas fallas de inicio de sesión.", "event_too_many_login_failure_recovery": "Se recuperó de demasiados errores de inicio de sesión, se reanudó el acceso web", "event_tracking_port_enabled_status": "Se modificó una entrada de seguimiento relacionada con un puerto habilitado", "event_tracking_static_route_status_changed": "Se modificó una entrada de seguimiento relacionada con una ruta estática", "event_tracking_status_changed": "Se modificó el estado de una entrada de seguimiento", "event_tracking_vrrp_status_changed": "Se modificó una entrada de seguimiento relacionada con VRRP", "event_trusted_access_attack": "Infracción de la política de acceso de confianza del dispositivo", "event_trusted_access_attack_recovery": "Se recuperó de la infracción de la política de acceso de confianza del dispositivo", "event_user_info_change": "Se modificó la información de la cuenta: {{trapoid}}", "event_v3_trap_parse_error": "Error de análisis de captura V3", "event_v3_trap_parse_error_recovery": "Se borró el evento de error de análisis de captura V3", "exceed_poe_threshold": "Se superó el umbral del sistema PoE", "fan_module_malfunction": "El módulo del ventilador está defectuoso.", "fiber_warning": "{{portIndex}} Advertencia de fibra ( {{warningType}} )", "firewall_policy_violation": "Política de firewall y regla de DoS: {{trapoid}}", "firewall_under_attack": "Infracción de la política de firewall", "firewall_under_attack_recovery": "Se recuperó de la infracción de la política de firewall", "firmware_upgraded": "Actualización de firmware", "firmware_version_release": "Hay una actualización de firmware disponible. Consulte la página de Gestión de firmware para obtener más detalles.", "goose_healthy": "Estado de GOOSE: Buen estado \nMensaje de GOOSE {{ display }}", "goose_healthy_with_value": "Estado de GOOSE: Buen estado \nMensaje de GOOSE {{ display }}", "goose_tampered": "Estado de GOOSE: Alterado", "goose_tampered_with_value": "Estado de GOOSE: Alteración \nMensaje de GOOSE {{ display }}", "goose_timeout": "Estado de GOOSE: Tiempo de espera", "goose_timeout_with_value": "Estado de GOOSE: Tiempo de espera \nMensaje de GOOSE {{ display }}", "high_cpu_loading": "La carga de la CPU superó el 85 % durante 10 minutos consecutivos.", "icmp_packet_loss_over_critical_threhold": "Tasa de pérdida de paquetes de ICMP del dispositivo hasta {{param1}} (por encima del umbral crítico {{param2}})", "icmp_packet_loss_over_critical_threhold_recovery": "La pérdida de paquetes de ICMP del dispositivo está por debajo de {{param1}} (por encima del umbral crítico {{param2}})", "icmp_packet_loss_over_threhold": "Tasa de pérdida de paquetes de ICMP del dispositivo hasta {{param1}} (por encima del umbral {{param2}})", "icmp_packet_loss_over_threhold_recovery": "La tasa de pérdida de paquetes de ICMP del dispositivo está por debajo de {{param1}} (por encima del umbral {{param2}})", "icmp_reachable": "Dispositivo ICMP accesible", "icmp_unreachable": "Dispositivo ICMP inaccesible", "iei_fiber_warning": "Advertencia de fibra activada", "input_bandwidth_over_threshold": "La utilización del ancho de banda de entrada ha excedido el umbral.", "input_bandwidth_over_threshold_disabled": "No hay ningún umbral establecido para la utilización del ancho de banda de entrada del puerto {{portIndex}}.", "input_bandwidth_over_threshold_recovery": "La utilización del ancho de banda de entrada del puerto {{portIndex}} se recuperó, el valor es {{currentValue}}.", "input_bandwidth_over_threshold_with_port": "La utilización del ancho de banda de entrada ({{currentValue}}) de puerto {{portIndex}} ha superado el umbral ({{threshold}}).", "input_bandwidth_under_threshold": "La utilización del ancho de banda de entrada está por debajo del umbral.", "input_bandwidth_under_threshold_disabled": "No hay ningún umbral establecido para la utilización del ancho de banda de entrada del puerto {{portIndex}}.", "input_bandwidth_under_threshold_recovery": "La utilización del ancho de banda de entrada del puerto {{portIndex}} se recuperó, el valor es {{currentValue}}.", "input_bandwidth_under_threshold_with_port": "La utilización del ancho de banda de entrada ({{currentValue}}) de puerto {{portIndex}} está por debajo del umbral ({{threshold}}).", "input_packet_error_over_threshold": "La tasa de error del paquete de entrada ha excedido el umbral.", "input_packet_error_over_threshold_disabled": "No hay ningún umbral establecido para la tasa de errores de paquetes de entrada del puerto {{portIndex}}.", "input_packet_error_over_threshold_recovery": "La tasa de errores de paquetes de entrada del puerto {{portIndex}} se recuperó, el valor es {{currentValue}}.", "input_packet_error_over_threshold_with_port": "La tasa de error del paquete de entrada ({{currentValue}}) de puerto {{portIndex}} ha superado el umbral ({{threshold}}).", "insufficient_disk_space": "Menos de 5 GB de espacio en disco disponible.", "interface_set_as_ospf_designated_router": "La interfaz está configurada como un enrutador designado por OSPF.", "ip_conflict_detected": "Conflicto de IP detectado para {{ip}}, MAC en conflicto:", "ip_conflict_detected_failed": "La detección de conflictos de IP no se puede ejecutar porque no se encuentra Npcap/WinPcap/Libpcap", "ip_conflict_recovery": "Conflicto de IP resuelto.", "iw_client_joined": "El cliente se unió: {{param}}", "iw_client_left": "El cliente salió: {{param}}", "l3_firewall_policy_violation": "Violación de la política de firewall (serie NAT)", "license_limitation_reached": "Se alcanzó el límite máximo de licencias.", "license_not_enough": "Se superó el límite de nodos, elimine algunos nodos o actualice su licencia de MXview One", "license_over": "Se superó el límite de nodos, elimine algunos nodos o actualice su licencia de MXview One", "lldp_change": "Se modificó la tabla de LLDP", "logging_capacity": "El registro de eventos supera el umbral de capacidad", "login_radius_fail": "La autenticación de inicio de sesión no se pudo establecer correctamente desde el servidor RADIUS+", "login_radius_success": "La autenticación de inicio de sesión se estableció correctamente desde el servidor RADIUS+", "login_tacas_fail": "La autenticación de inicio de sesión no se pudo establecer correctamente desde el servidor TACACS+", "login_tacas_success": "La autenticación de inicio de sesión se estableció correctamente desde el servidor TACACS+", "mac_sticky_violation": "Infracción de MAC persistente", "mrp_multiple_event": "Se produjo un evento de múltiples gerentes de MRP.", "mrp_ring_open_event": "Se produjo un evento de apertura de anillo MRP.", "mstp_topology_changed": "Cambios de topología MSTP", "mxview_autopology_finish": "Topología automática finalizada", "mxview_autopology_start": "Topología automática iniciada", "mxview_db_backup_fail": "No se pudo realizar la copia de seguridad de la base de datos", "mxview_db_backup_sucess": "La copia de seguridad de la base de datos se completó y se almacenó en %MXviewPRO_Data%\\db_backup\\{{param1}} {{param2}}", "mxview_job_done": "Trabajo: {{jobname}} completado", "mxview_job_start": "Trabajo: {{jobname}} inicio", "mxview_server_license_limit": "Pas assez de licences de MXview One Central Manager", "mxview_server_start": "Servidor de MXview One iniciado", "mxview_sms_fail": "No se pudo enviar una notificación por SMS", "mxview_sms_success": "MXview One envió una notificación por SMS correctamente", "mxview_user_lockout": "La cuenta {{param}} se bloqueó temporalmente", "mxview_user_login_fail": "No se pudo iniciar sesión en MXview One.", "mxview_user_login_sucess": "Inicio de sesión de usuario: {{param}}", "mxview_user_logout": "Cierre de sesión del usuario: {{param}}", "network_latency": "La latencia de red para MXview One Central Manager superó los 100 ms", "new_port_role_selected": "Seleccione la nueva función del puerto.", "new_root_bridge_selected_in_topology": "Seleccione el nuevo puente raíz en la topología.", "notification_sfp_rx_below": "Recepción de SFP por debajo del umbral", "notification_sfp_temp_over": "Temperatura de SFP por encima de del umbral", "notification_sfp_tx_below": "Transmisión de SFP por debajo del umbral", "notification_sfp_voltage_below": "Voltaje de SFP por debajo de del umbral", "notification_sfp_voltage_over": "Voltaje de SFP por encima de del umbral", "nport_syslog_over_threshold": "NPort está por encima de su umbral de registro del sistema", "opcua_server_start": "Se inicia el servidor OPC UA de MXview One.", "opcua_server_stop": "El servidor OPC UA de MXview One se ha detenido.", "ospf_designated_router_changed": "El enrutador designado por OSPF ha cambiado.", "ospf_designated_router_interface_and_adjacency_changed": "Las interfaces y adyacencias del enrutador designado por OSPF cambian.", "out_of_memory": "Menos del 20% de memoria disponible.", "output_bandwidth_over_threshold": "La utilización del ancho de banda de salida ha superado el umbral.", "output_bandwidth_over_threshold_disabled": "No hay ningún umbral establecido para la utilización del ancho de banda de salida del puerto {{portIndex}}.", "output_bandwidth_over_threshold_recovery": "La utilización del ancho de banda de salida del puerto {{portIndex}} se recuperó, el valor es {{currentValue}}.", "output_bandwidth_over_threshold_with_port": "La utilización del ancho de banda de salida ({{currentValue}}) de puerto {{portIndex}} ha superado el umbral ({{threshold}}).", "output_bandwidth_under_threshold": "La utilización del ancho de banda de salida está por debajo del umbral.", "output_bandwidth_under_threshold_disabled": "No hay ningún umbral establecido para la utilización del ancho de banda de salida del puerto {{portIndex}}.", "output_bandwidth_under_threshold_recovery": "La utilización del ancho de banda de salida del puerto {{portIndex}} se recuperó, el valor es {{currentValue}}.", "output_bandwidth_under_threshold_with_port": "La utilización del ancho de banda de salida ({{currentValue}}) de puerto {{portIndex}} está por debajo del umbral ({{threshold}}).", "output_packet_error_over_threshold": "La tasa de error del paquete de salida ha excedido el umbral.", "output_packet_error_over_threshold_disabled": "No hay ningún umbral establecido para los errores de paquetes de salida del puerto {{portIndex}}.", "output_packet_error_over_threshold_recovery": "El error de paquetes de salida del puerto {{portIndex}} se recuperó, el valor es {{currentValue}}.", "output_packet_error_over_threshold_with_port": "La tasa de error del paquete de salida ({{currentValue}}) de puerto {{portIndex}} ha superado el umbral ({{threshold}}).", "overheat_protection_now_active_for_power_module": "La protección contra sobretemperatura del módulo de fuente de alimentación {{ x }} ahora está activada.", "password_automatically_changed_failed": "No se pudo cambiar automáticamente la contraseña del dispositivo.", "password_automatically_changed_success": "La contraseña del dispositivo se cambió correctamente de forma automática.", "password_automation_scheduled": "La automatización de contraseñas ha llegado a la hora programada y comenzará a ejecutarse.", "phr_port_timediff": "Diferencia horaria del puerto PHR AB.", "phr_port_wrong_lan": "Puerto PHR AB LAN incorrecto.", "poe_off_info": "The device is not powered by PoE", "poe_on_info": "The device is powered by PoE", "port_linkdown_event": "Vínculo de puerto {{portindex}} inactivo", "port_linkdown_recovery": "Vínculo de puerto {{portindex}} activo", "port_linkup_event": "Vínculo de puerto {{portindex}} activo", "port_linkup_recovery": "Vínculo de puerto {{portindex}} inactivo", "port_loop_detect": "Se ha detectado un bucle en el puerto {{portnum}}", "port_loop_detect_resolved": "Se ha resuelto el bucle del puerto {{portnum}}.", "port_loop_detected": "Bucle de puerto", "port_pd_short_circuited": "Puerto {{portnum}} No PD o PD en cortocircuito.", "port_traffic_overload": "Sobrecarga de tráfico del puerto {{portIndex}} de {{percent}}%", "power_danger_recovery": "La alimentación {{param}} se cambió a CA", "power_has_been_cut_due_to_overheating": "Se ha cortado el suministro eléctrico por sobrecalentamiento.", "power_module_fan_malfunction": "El ventilador del módulo de potencia está defectuoso.", "power_type_danger": "La alimentación {{param}} se cambió a CC", "pse_fet_bad": "Error del FET externo del Puerto PoE {{portIndex}}", "pse_over_temp": "Sobrecalentamiento del chip PSE", "pse_veeuvlo": "Bloqueo por baja tensión del chip PSE VEE", "ptp_grandmaster_changed": "PTP Grandmaster cambiado", "ptp_sync_status_changed": "El estado de sincronización PTP cambió", "rateLimit_off": "Límite de velocidad del puerto {{portindex}} desactivado", "rateLimit_on": "La limitación de velocidad está activa en el puerto {{portindex}}.", "recorved_device_lockdown_violation": "Recuperado de una violación de bloqueo del dispositivo", "recorved_l3_firewall_policy_violation": "Recuperación de una violación de la política de firewall (serie NAT)", "redundancy_topology_change": "La topología de redundancia ha cambiado.", "syslog_server_start": "Se inicia un servidor syslog MXview.", "syslog_server_stop": "MXview Un servidor syslog se ha detenido.", "system_temperature_exceeds_threshold": "La temperatura del sistema supera el umbral.", "temporary_account_activate_success": "La cuenta temporal en {{ip}} se activó correctamente.", "temporary_account_deactivate_success": "La cuenta temporal en {{ip}} se desactivó correctamente.", "thermal_sensor_component_overheat_detected": "Se ha detectado sobrecalentamiento del conjunto del sensor térmico.", "trunk_port_link_down": "Vínculo de puerto de trunking {{portindex}} inactivo (Puerto físico: {{param}})", "trunk_port_link_down_recovery": "Vínculo de puerto de trunking {{portindex}} activo (Puerto físico: {{param}})", "trunk_port_link_up": "Vínculo de puerto de trunking {{portindex}} activo (Puerto físico: {{param}})", "trust_access_under_attack": "Infracción de la política de acceso de confianza del dispositivo", "trust_access_under_attack_recovery": "Se recuperó de la infracción de la política de acceso de confianza del dispositivo", "turbo_ring_master_match": "Maestro de Turbo Ring coincidente", "turbo_ring_master_mismatch": "Maestro de Turbo Ring no coincidente", "turbo_ring_master_unknow": "Evento del maestro de Turbo Ring, estado desconocido", "turbochain_topology_change": "Se cambió la topología de Turbo Chain", "turboring_coupling_port_change": "Se cambió el puerto de acoplamiento de Turbo Ring", "turboring_master_change": "Se cambió la topología del maestro de Turbo Ring", "unknown_device_detected": "Se ha detectado un dispositivo desconocido", "user_login_fail": "No se pudo autenticar la cuenta", "user_login_success": "La cuenta se autenticó correctamente: {{username}}", "usercode_revoke": "El usuario volvió a generar el código de usuario", "vpn_link_recovery": "<PERSON><PERSON>el VPN {{param}} recuperado", "vpn_linkdown": "T<PERSON>el VPN {{param}} desconectado", "vpn_linkup": "<PERSON><PERSON>el VPN {{param}} conectado", "vrrp_master_changed": "VRRP Master cambiado", "warn_start": "Arranque en caliente"}, "event_detail_title": "ID de detalle del evento: {{eventId}}", "filter": "Condiciones de filtro", "filter_end_time": "Fecha de finalización", "filter_event": "Escriba para filtrar eventos", "filter_event_type": "Filtro rápido de eventos", "filter_from_time": "Fecha de inicio", "filter_hour": "<PERSON><PERSON>", "filter_min": "Min<PERSON>", "filter_sec": "<PERSON><PERSON><PERSON>", "filter_type": {"last_fifty_events": "Últimos 50 eventos", "last_twenty_events": "Últimos 20 eventos", "unack_events": "Eventos no reconocidos", "unack_last_fifty_events": "Últimos 50 eventos no reconocidos", "unack_last_twenty_events": "Últimos 20 eventos no reconocidos"}, "group": "Grupo", "recent_event": "Eventos recientes", "severity": {"any": "<PERSON><PERSON><PERSON><PERSON>", "critical": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "information": "Información", "network_device": "Red y dispositivo", "system_information": "Información del sistema", "warning": "Advertencia"}, "show_event_detail": "<PERSON><PERSON> de<PERSON>les", "source": {"any": "<PERSON><PERSON><PERSON><PERSON>", "mxview": "MXview One", "security_sensing": "Detección de seguridad", "trap": "Captura"}, "table_title": {"ack": "Reconocer", "ack_time": "Hora de reconocimiento", "always_show": "Mostrar siempre \"Eventos recientes\" al inicio", "description": "Descripción", "detail_information": "Información detallada", "event_id": "ID", "event_properties": "Propiedades del evento", "event_source": "Fuente", "event_time": "Hora de emisión", "hide_recent_event": "Ocultar eventos recientes", "severity": "Gravedad", "show_recent_event": "Mostrar eventos recientes", "site_name": "Nombre del sitio", "source_ip": "IP de origen"}, "tabs": {"network_device_title": "Red y dispositivo", "security_event": "Evento de ciberseguridad", "security_title": "Ciberseguridad", "system_title": "Sistema"}}, "execute_cli_object": {"add_cli_object": "Agregar una secuencia de comandos CLI", "alias": "<PERSON><PERSON>", "cli_error": {"connection_failure": "Fallo de conexión", "handshake_failure": "Fallo en el apretón de manos", "login_failure": "Fallo de inicio de sesión", "port_limit": "Número máximo de entradas de direcciones de seguridad del puerto: {{param}}", "reach_maximum_ssid": "Número máximo de SSID: {{param}}", "smmp_configuration_mismatch": "La configuración de SNMP no coincide", "ssh_not_supported": "No se pudo conectar al cliente SSH", "unable_to_set_port": "No se puede establecer el puerto {{port}}", "unknown_error": "Error descon<PERSON>"}, "cli_Script": "Secuencia de comandos CLI", "cli_session_timeout": "Se agotó el tiempo de espera de la sesión CLI", "confirm_selected_devices": "Dispositivos seleccionados", "description": "Descripción", "execute_cli_fail": "No se puede ejecutar el script CLI", "execute_cli_object": "Ejecutar un script CLI", "execute_cli_result_hint": "Si sale de esta pantalla, puede descargar los resultados de la ejecución desde Scripts CLI guardados > Resultados de ejecución.", "execute_cli_results": "Resultados de ejecución de CLI", "execute_cli_script": "Ejecutar secuencia de comandos CLI", "failed": "fallar", "finished": "Finalizado", "in_progress": "En curso ...", "ip": "IP", "model": "<PERSON><PERSON>", "name": "Nombre del script CLI", "no_cli_object_hint": "No se encontraron secuencias de comandos CLI. Agregue primero una secuencia de comandos CLI desde las secuencias de comandos CLI guardadas", "not_sent": "No enviado", "result": "<PERSON><PERSON><PERSON><PERSON>", "save_as_cli_object": "Guardar como secuencia de comandos CLI", "save_cli_object": "Guardar secuencia de comandos CLI", "select_cli_object": "Seleccione una secuencia de comandos CLI", "ssh_connection_timeout": "Tiempo de espera de conexión SSH", "status": "Estado"}, "firmware-management": {"action": "Acción", "add-task": "Agregar tarea", "add-to-schedule": "Actualización programada", "api-message": {"add-schedule-fail": "No se puede programar la tarea", "add-schedule-success": "Tarea programada", "delete-schedule-fail": "No se puede eliminar el cronograma de intervalo de verificación", "delete-schedule-success": "Verificar el cronograma de intervalos eliminado exitosamente", "fm-downloaded": "Archivo de firmware descargado exitosamente", "fm-downloading": "Descargando el archivo de firmware", "fm-ready": "Archivo de firmware listo", "get-data-failed": "No se pueden recuperar datos", "get-download-fm-failed": "No se puede descargar el archivo de firmware", "get-release-note-failed": "No se pueden recuperar las notas de la versión", "get-srs-status-failed": "No se puede consultar el estado del servidor de firmware Moxa", "ignored-model-fail": "No se puede agregar el modelo a la lista de modelos ignorados"}, "check-Firmware-status": "Verificar el estado del firmware", "check-interval": "Comprobar intervalo", "check-now": "<PERSON><PERSON><PERSON>", "connected": "Conectado", "description": "Descripción", "disconnected": "Desconectado", "download-csv-report": "Descargar informe CSV", "download-pdf-report": "Descargar informe en PDF", "execution-time": "Tiempo", "firmware-upgrade-sequential": "Actualización de firmware (secuencial estricto)", "firmware-upgrade-smart-concurrent": "Actualización de firmware (secuencial inteligente)", "ignore-report": "Ignorar informe", "ignore-report-desc1": "¿Está seguro de que desea omitir la descarga del informe?", "ignore-report-desc2": "Si abandona esta página, el informe ya no estará disponible para descargar.", "ignored-models": "Modelos ignorados", "last-update": "Última comprobación", "models": "Modelos", "moxa-firmware-server-status": "Estado del servidor de firmware Moxa", "no-information": "no hay información disponible", "none": "<PERSON><PERSON><PERSON>", "offline-desc": "No hay información disponible. No hay conexión previa al servidor de actualización de firmware. Asegúrese de que el dispositivo esté conectado a Internet e inténtelo nuevamente.", "proceeding-firmware-upgrade": "Estado de actualización del firmware", "proceeding-upgrade-result": "Resultado de la actualización del firmware", "release-note": "Notas de lanzamiento", "repeat-execution": "<PERSON><PERSON>r", "retry-Failed-devices": "Reintentar dispositivos fallidos", "select-devices": "Seleccionar dispositivos", "select-firmware": "Seleccionar firmware", "set-upgrade-sequence": "Secuencia de actualización", "sign-here": "Firma", "start-date": "<PERSON><PERSON>", "status": {"failed": "Fallido", "finished": "Finalizado", "in-progress": "En curso", "waiting": "Espera"}, "table-header": {"alias": "<PERSON><PERSON>", "current-version": "Versión actual", "device-status": "Estado del dispositivo", "ip": "IP", "latest-version-on-firmware-server": "Última versión en el servidor de firmware", "model-series": "Serie de modelos", "order": "Orden", "selected-firmware-ready": "Estado de descarga de firmware seleccionado", "selected-version": "Versión seleccionada", "status": "Estado"}, "task-name": "Nombre de la tarea", "title": "Gestión de firmware", "turn-off-check-interval": "Deshabilitar el intervalo de verificación", "turn-on-check-interval": "Habilitar intervalo de verificación", "unable-to-download-firmware": "No se pudo descargar el firmware", "update-mode": "Modo de actualización", "upgrade-desc1": "No se puede determinar la secuencia de actualización. Primero agregue la computadora que ejecuta MXview One a la topología.", "upgrade-desc2": "La configuración actual solo admite actualizaciones simultáneas del firmware del dispositivo. Para utilizar los métodos de actualización secuencial estricto o secuencial inteligente, primero debe agregar la computadora que ejecuta MXview One a la topología.", "upgrade-firmware-report": "Informe de actualización de firmware", "upgrade-now": "<PERSON><PERSON><PERSON><PERSON>ora", "upgrade-state-desc": "La actualización del firmware puede tardar algún tiempo. Espere a que se complete el proceso de actualización.", "version": "Versión"}, "general": {"common": {"action": "Acción", "allow": "<PERSON><PERSON><PERSON>", "any": "<PERSON><PERSON><PERSON><PERSON>", "deny": "<PERSON><PERSON><PERSON>", "description": "Descripción", "deviceInUse": "Dispositivo en uso", "deviceName": "Nombre del dispositivo", "disabled": "Desactivado", "enabled": "Activado", "endDate": "Fecha de finalización", "filters": "<PERSON><PERSON><PERSON>", "firmwareVersion": "Versión de firmware", "group": "Grupo", "index": "<PERSON><PERSON><PERSON>", "ipAddress": "Dirección IP", "location": "Ubicación", "mac": "Dirección MAC", "name": "Nombre", "online": "En línea", "options": "Opciones", "productModel": "Modelo del Producto", "profileInUse": "Perfil en uso", "refCount": "Referencias", "serialNumber": "Número de serie", "startDate": "Fecha de inicio", "status": "Estado", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "dialog": {"deleteMsg": "¿Está seguro de que desea eliminar el {{item}} seleccionado?", "deleteTitle": "Eliminar {{item}}", "isSelected": "{{ number }} <PERSON><PERSON><PERSON><PERSON>(s) seleccionado(s)", "title_system_message": "Mensaje del sistema", "unsaved_hint_content": "¿Estás seguro que deseas abandonar esta página?\nCualquier cambio realizado no se guardará.", "unsaved_hint_title": "<PERSON><PERSON> sin ahorrar", "warning": "Advertencia"}, "fileDrop": {"browse": "examine", "dropText": "Arrastre y suelte un archivo aquí, o"}, "item_selected": "{{ number }} <PERSON><PERSON><PERSON><PERSON>(s) seleccionado(s)", "log": {"localStorage": "Almacenamiento local", "logDestination": "Destino del registro", "snmpTrapServer": "Servidor de trampa SNMP", "syslogServer": "Servidor <PERSON>", "title": "Registro de eventos"}, "menu": {"jump_page_placeholder": "Pulse Alt+J para saltar a una página"}, "page_state": {"application_error": "Error de solicitud :(", "application_error_desc": "Se produjo un error al procesar esta solicitud.", "back_link": "Volver a la página de índice", "page_not_found": "Página no encontrada :(", "page_not_found_desc": "La URL solicitada no se encontró encontrado en este servidor."}, "severity": {"alert": "<PERSON><PERSON><PERSON>", "critical": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "debug": "<PERSON><PERSON><PERSON>", "emergency": "Emergencia", "error": "Error", "high": "Alto", "information": "Información", "informational": "Informational", "low": "<PERSON><PERSON>", "medium": "Mediano", "notice": "Aviso", "title": "Gravedad", "warning": "Advertencia"}, "shortWeekday": {"fri": "Vie.", "mon": "<PERSON>n.", "sat": "<PERSON> sentó.", "sun": "Sol.", "thu": "<PERSON><PERSON>.", "tue": "Mar.", "wed": "Casarse."}, "table": {"add": "Agregar", "delete": "Eliminar", "download": "<PERSON><PERSON><PERSON>", "downloadAllLogs": "<PERSON><PERSON><PERSON> todos los registros", "edit": "<PERSON><PERSON>", "filter": "Filtrar", "info": "Info", "more": "Más", "permissionDenied": "<PERSON><PERSON><PERSON> den<PERSON>ado", "reboot": "<PERSON><PERSON><PERSON>", "refresh": "Actualizar", "reorderFinish": "Terminar de reordenar", "reorderPriority": "Reordenar prioridades", "search": "Buscar", "transfer": "Transferencia", "upgrade": "Actualizar"}, "top_nav": {"api_doc": {"title": "Referencia de la API"}, "hide_recent_event": "Ocultar menús de navegación", "notifications": {"message_content": "Contenido del evento", "message_readall": "Más notificaciones", "message_title": "Título del evento", "notification_header": "Notificaciones"}, "show_recent_event": "Mostrar menús de navegación", "user_profile": {"advanced_mode": "<PERSON><PERSON>", "change_pwd": "Cambiar contraseña", "greeting": "<PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON>", "manage_account": "Gestionar la cuenta", "reset_factory_default": "Restablecer valores de fábrica", "restart_machine": "Reiniciar la máquina", "search": "Escriba una palabra clave para buscar"}}, "topNav": {"caseInsensitive": "Sin distinción entre mayúsculas y minúsculas", "changePwd": "Cambiar contraseña", "changeSuccess": "Su contraseña se actualizó correctamente. Inicie sesión nuevamente.", "confirmNewPwd": "Confirmar nueva contraseña", "currentPwd": "Contraseña actual", "invalidKey": "Los siguientes nombres están reservados: administrador, operador, espectador, ra<PERSON><PERSON>, administrador, auditor", "logout": "<PERSON><PERSON><PERSON>", "logoutMsg": "¿Seguro que desea finalizar la sesión?", "newPwd": "Nueva contraseña", "subject": "<PERSON><PERSON>", "troubleshoot": "Resolución de problemas", "troubleshootMsg": "Puede exportar los registros de depuración al host local para solucionar problemas.", "updateAuthority": "Actualizar autoridad", "updateSuccess": "La autoridad de tu cuenta ha cambiado. Vuelve a iniciar sesión.", "username": "Nombre de usuario"}, "unit": {"days": "días)", "entries": "entradas", "minute": "Min<PERSON>", "minutes": "minuto (s)", "months": "meses)", "percent": "%", "pkts": "paquete/s", "sec": "segundo.", "seconds": "artículos de segunda clase)", "thousand": "mil"}, "weekday": {"friday": "Viernes", "monday": "<PERSON><PERSON>", "saturday": "Sábado", "sunday": "Domingo", "thursday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "GLOBAL_MESSAGE": {"update_fail": "No se pudo actualizar", "update_success": "Actualizado con éxito"}, "GROUP_PROPERTIES": {"description": "Descripción", "devices": "Dispositivos (normal/advertencia/crítico)", "information": "Información", "name": "Nombre", "title": "Propiedades del grupo"}, "IMAGE": {"deviceSizeError": "La imagen debe tener un tamaño inferior a 100 KB.", "error": "MXview One solo admite los formatos jpg, gif y png", "sizeError": "La imagen debe tener un tamaño inferior a 1 MB"}, "inventory_management": {"active": "<PERSON><PERSON><PERSON><PERSON>", "alias": "<PERSON><PERSON>", "assets_list": "Lista de activos", "available": "Disponible", "channel_extended_end_date": "Fecha de finalización de la garantía extendida del canal", "channel_extended_warranty_end_date_hint": "Si tiene un acuerdo de garantía extendida con su proveedor de canal Moxa, ingrese manualmente la fecha de vencimiento extendida aquí.", "check_warranty_manually": "Verificar garantía manualmente", "check_warranty_status": "Verificar el estado de la garantía", "days": "<PERSON><PERSON> antes", "email_example": "<EMAIL>", "email_to": "Email para", "expire_soon": "expira pronto", "expired": "Muerto", "firmware_version": "Versión De Firmware", "invalid_email_desc": "Dirección de correo electrónico no válida", "ip": "IP", "last_update": "Última actualización", "mac_address": "Dirección MAC", "model": "<PERSON><PERSON>", "multiple_email_hint": "Puede agregar varias direcciones de correo electrónico de destinatarios, separadas por una coma.", "no_data": "N/A", "notify_before": "Enviar recordatorio", "retrieve_data": "<PERSON><PERSON>era<PERSON> datos", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "serial_number": "Número de Serie", "type": "Búsqueda por", "unable_query_warranty_server_status": "No se puede comunicar con el servidor de garantía de Moxa.", "unable_retrieve_warranty_information": "No se puede recuperar la información de la garantía.", "unavailable": "Indisponible", "warranty_end_date": "Fecha de finalización de la garantía", "warranty_end_date_notification": "Notificaciones de vencimiento de garantía", "warranty_management": "Gestión de garantía", "warranty_notification": "Notificaciones de garantía", "warranty_period": "Duración de la garantía", "warranty_server_status": "Estado del servidor de garantía Moxa", "warranty_start_date": "Fecha de inicio de la garantía", "warranty_status": "Estado de la garantía"}, "INVENTORY_REPORT": {"alias": "<PERSON><PERSON>", "filter": "Escriba para filtrar informes de inventario", "fw_version": "Versión de firmware", "ip_address": "Dirección IP", "mac": "Dirección MAC", "model": "<PERSON><PERSON>", "property": "Propiedad", "report_generate_day": "Fecha de generación del informe: ", "site_name": "Nombre del sitio", "system_desc": "Descripción del sistema", "title": "Informe de inventario", "value": "Valor"}, "IP_CONFIGURATION": {"auto_ip": "IP automática", "change_ip_fail": "No se pudo actualizar la configuración de IP", "change_ip_success": "La configuración de IP del dispositivo se actualizó correctamente", "gateway": "<PERSON><PERSON><PERSON>", "hint": "Esta función no está disponible para dispositivos de capa 3", "ip_address": "Dirección IP", "netmask": "Máscara de red", "title": "Configuración de IP"}, "ip_conflict_detected_notification": "Conflicto de IP detectado", "ip_conflict_recovery_notification": "Conflicto de IP resuelto", "ips_configuration": {"dialog-title": "Configuración de IPS", "execute_fail": "Failed to execute", "input-ips": "IPS", "option-detection-mode": "Modo de detección", "option-prevention-mode": "Modo de prevención", "selet-ips-operation-mode": "Modo de funcionamiento IPS", "th-execution-status": "Estado"}, "IPSEC": {"connection_name": "Nombre de la conexión", "ipsec_status_phase1": "Estado de la fase 1 de IPSec", "ipsec_status_phase2": "Estado de la fase 2 de IPSec", "local_gateway": "Puerta de enlace local", "local_subnet": "Subred local", "remote_gateway": "<PERSON><PERSON><PERSON> de enlace remota", "remote_subnet": "<PERSON><PERSON> remota"}, "IW": {"Message": {"CONNECT_TIMEOUT": "Se agotó el tiempo de espera de la conexión", "ERROR_OCCURRED": "Error del servidor. Espere y vuelva a intentarlo.", "FAILED": "No se realizó correctamente", "LOAD_DATA_FAILED": "No se pudieron cargar los datos", "RSSI_SNR_ONLY": "(Solo intensidad de la señal y SNR)", "SET_SUCCESS": "La configuración se modificó correctamente", "SUCCESSED": "Se realizó correctamente", "UPDATE_FAILED": "No se pudieron actualizar los datos"}, "Title": {"ap": "PA", "AUTO_REFREASH": "Actualización automática: ", "AUTO_REFRESH": "Actualizar automáticamente", "BSSID": "BSSID", "channel": "Canal", "client": "Cliente", "client_count": "Número de clientes", "client_router": "Cliente-Router", "CLOSE": "<PERSON><PERSON><PERSON>", "COLOR": "Color", "COLUMN": "Columna de parámetros", "CONDITIONS": "Condición", "CONN_TIME": "Tiempo de conexión (segundos)", "connected": "Conectado", "DEVICE_NAME": "Nombre del dispositivo", "disable": "Disable", "ENABLE": "Habilitar", "FILTER_TABLE_VIEW": "Vista de tabla de filtros", "hint": "Esta página se eliminará en una futura versión del producto. Utilice el complemento inalámbrico en MXview One en su lugar.", "IP_ADDR": "Dirección IP", "link_speed": "Velocidad de vínculo", "MAC": "Dirección MAC", "master": "Maestría", "MODULATION": "Modulación", "noise_floor": "Ruido de fondo", "noise_floor_unit": "<PERSON><PERSON><PERSON> de fondo (dBm)", "OK": "Aceptar", "ONLINE": "En línea", "operation_mode": "Modo de operación", "RSSI": "Intensidad de la señal (dBm)", "security_mode": "<PERSON><PERSON> de seguridad", "signal_level": "<PERSON><PERSON>", "slave": "Esclavo", "SNR": "SNR (dB)", "SNR_A": "SNR-A (dB)", "SNR_B": "SNR-B (dB)", "ssid": "SSID", "TOTAL_AP": "Número de PA: ", "TOTAL_CLIENT": "Número de clientes: ", "tx_power": "Potencia de transmisión", "tx_power_unit": "Potencia de transmisión (dBm)", "tx_rate": "Velocidad de transmisión", "tx_rate_unit": "Velocidad de transmisión (Mb/s)", "uptime": "Tiempo de actividad", "VALUE": "Valor", "WIRELESS_TABLE_VIEW": "Vista de tabla de dispositivos inalámbricos"}}, "JOB_SCHEDULER": {"add_failed": "No se pudo agregar el trabajo", "add_success": "El trabajo se agregó correctamente", "add_title": "<PERSON><PERSON><PERSON>", "alias": "<PERSON><PERSON>", "auto_topology": "Topología automática", "cli_object_name": "Nombre del script CLI", "config_file": "Archivo de configuración", "config_file_error": "MXview One solo admite archivos .ini", "config_file_size_error": "El archivo debe tener un tamaño inferior a 1 MB.", "current_filename": "Nombre de archivo actual", "current_version": "Versión actual", "daily": "Diariamente", "database_backup": "Copia de seguridad de base de datos", "delete_failed": "No se pudo eliminar el trabajo", "delete_success": "El trabajo se eliminó correctamente", "description": "Descripción", "edit_title": "<PERSON><PERSON> t<PERSON>", "excute_cli_object": "Ejecutar script guardado", "execution_time": "Hora de ejecución", "export_configuration": "Exportar configuración", "filter": "Escriba para filtrar trabajos", "fm_sequential": "Secuencial estricto", "fm_smart": "Secuencial inteligente", "friday": "Viernes", "import_configuration": "Importar configuración", "ip": "IP", "job_action": "Acción", "job_log": "Registro de trabajos", "job_name": "Nombre del trabajo", "model_series": "Serie de modelos", "monday": "<PERSON><PERSON>", "monthly": "Mensualmente", "on": "En", "once": "Una vez", "order": "Orden", "registered_devices": "Dispositivos registrados", "repeat_execution": "Repetir eje<PERSON>", "saturday": "Sábado", "schedule_time": "Hora programada", "selected_version": "Versión seleccionada", "show_log_fail": "No se pudo mostrar el registro", "show_log_not_found": "No hay registros disponibles", "start_date": "Fecha de inicio", "sunday": "Domingo", "thursday": "<PERSON><PERSON>", "title": "Programador de mantenimiento", "tuesday": "<PERSON><PERSON>", "update_failed": "No se pudo actualizar el trabajo", "update_success": "El trabajo se actualizó correctamente", "wednesday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "weekly": "<PERSON><PERSON><PERSON><PERSON>"}, "LANG": {"de-DE": "De<PERSON>ch", "en-US": "English", "es-ES": "Español", "fr-FR": "Français", "ja-JP": "日本語", "ru-RU": "Русский язык", "zh-CN": "中文(简体)", "zh-TW": "中文(繁體)"}, "LICENSE": {"activation_code": "Código de activación", "activation_code_error": "El código de activación no es válido", "activation_title": "Activación", "active": "Activar", "active_desc_1": "Visitar", "active_desc_2": "http://license.moxa.com/", "active_desc_3": "e ingrese su código de producto y código de usuario para obtener su código de activación.", "add_fail": "No se pudo agregar la licencia", "add_license": "Agregar licencia", "add_new_license_desc": "Puede agregar una licencia aquí.", "add_new_license": {"activate": "Activar", "activate_intro_link": "Sitio de licencias de Moxa", "activate_intro_pre": "Descargue la licencia del", "activate_intro_suf": "y pegue el código de activación aquí.", "copy_user_code": "Copiar c<PERSON>digo de usuario", "copy_user_code_intro_link": "Sitio de licencias de Moxa", "copy_user_code_intro_pre": "Copie el código de usuario en el", "license_site_step_1_link": "Sitio de licencias de Moxa", "license_site_step_1_pre": "1. In<PERSON>e se<PERSON><PERSON> en el", "license_site_step_2": "2. Seleccione \"Active su licencia\" y \"MXview One\" en el sitio.", "license_site_step_3": "3. <PERSON><PERSON><PERSON> de registro", "license_site_step_3_Free_step": "Vaya al siguiente paso.", "license_site_step_3_Free_title": "Usuarios de la versión gratuita: ", "license_site_step_3_Full_step": "Introduzca el código de registro y el código de usuario en el sitio de licencias de Moxa. Se proporcionará un código de usuario en un paso posterior.", "license_site_step_3_Full_title": "Usuarios de la licencia completa: ", "login_license_site": "Inicie sesión en el sitio de licencias de Moxa", "select_network_adapter": "Seleccionar adaptador de red", "title": "Añadir una licencia nueva"}, "add_success": "La licencia se agregó correctamente", "copied_to_clipboard": "Se copió al portapapeles", "copy_deactivation_code": "Copiar código de desactivación", "copy_older_license_code": "Copiar el código de licencia 2.x", "current_nodes": "Nodos actuales:", "deactivate": "Desactivar", "deactivate_fail": "No se pudo desactivar la licencia", "deactivate_success": "La licencia se desactivó correctamente", "deactivated_licenses": "Licencias desactivadas", "deactivating": "Desactivando...", "deactivation_code": "Código de desactivación", "deactivation_desc": "La licencia no será válida después de la desactivación, ¿está seguro de que desea desactivar la licencia?", "deactivation_title": "Desactivación", "disabled": "Desactivado", "duration": "Duración", "enabled": "Activado", "expired_license": "Licencias vencidas", "free_trial": "<PERSON><PERSON><PERSON> gratis", "free_trial_desc": "Comience a disfrutar del poder de MXview One", "import_license_file": "Importar archivo de licencia", "license": "Licencia:", "license_authorized": "Autorizada", "license_free": "Licencia gratuita", "license_none": "Ninguna", "license_site": "Sitio de licencias de Moxa", "license_start": "Inicio de licencia", "license_title": "Licencia", "license_trial": "Prueba", "license_type": {"node_base_intro": "Especifique el número de dispositivos que MXview One puede monitorear en la red.", "node_base_title": "Licencia basada en nodos", "power_addon": "Licencia de complemento de potencia", "power_intro": "Permite a los usuarios acceder a funciones adicionales relacionadas con la potencia.", "security_addon": "Licencia de complemento de seguridad", "security_intro": "Permite a los usuarios acceder a funciones adicionales relacionadas con la seguridad.", "title": "Tipo de licencia", "trial_intro": "Puede disfrutar del poder de MXview One durante 90 días.", "trial_title": "Licencia de prueba", "wireless_addon": "Licencia de complemento inalámbrico", "wireless_intro": "Permite a los usuarios acceder a funciones inalámbricas adicionales relacionadas."}, "licensed_node": "Nodo con licencia", "licensed_nodes": "Nodos con licencia:", "licenses": "Licencias", "managed_by_central": "The License is managed by MXview One Central", "managed_by_central_licenses_invalidated": "No valid licenses, please check the status in Control Panel.", "mxview": "MXview One", "network_adapter": {"button": "Seleccionar adaptador de red", "change_network_adapter": "Cambiar el adaptador de red", "change_network_adapter_alert_1": "¿Está seguro de que quiere cambiar el adaptador de red?", "change_network_adapter_alert_2": "Todas las licencias se desactivarán una vez que haga clic en \"Confirmar\". No podrá usar MXview One hasta que registre una licencia nueva con un adaptador de red nuevo.", "intro": "MXview One vincula la licencia a un adaptador de red. Elija el adaptador al que desea vincular la licencia. Si vuelve a seleccionar un adaptador de red, todas sus licencias se desactivarán automáticamente y tendrá que volver a registrarlas.", "select_adapters": "Seleccionar adaptadores", "select_adapters_desc": "Seleccione un adaptador de red, MXview One lo utilizará para generar su código de usuario.", "title": "Adaptador de red"}, "node": "Nodos actuales/nodos con licencia:", "nodes": "nodos", "older_license": "Licencia 2.x", "older_license_nodes": "Nodos 2.x", "over_nodes_desc": "Se cerró la sesión porque la cantidad de nodos monitoreados supera los límites que admite su licencia.", "over_nodes_title": "Advertencia", "power_addon_trial": "Comience a disfrutar del complemento de potencia de MXview One", "reactivate_license": {"activate": "Activar", "activate_intro_link": "Sitio de licencias de Moxa", "activate_intro_pre": "Descargue la licencia del ", "activate_intro_suf": "y pegue el código de activación aquí.", "copy_deactivate_code": "Copiar código de desactivación", "copy_deactivate_code_intro_link": "Sitio de licencias de Moxa", "copy_deactivate_code_intro_pre": "Copie el código de desactivación y péguelo en el ", "copy_user_code": "Copiar c<PERSON>digo de usuario", "copy_user_code_intro_link": "Sitio de licencias de Moxa", "copy_user_code_intro_pre": "Copie el código de usuario en el ", "intro": "Utilice tanto el código de desactivación como un código de usuario para reactivar su licencia.", "license_site_step_1_link": "Sitio de licencias de Moxa", "license_site_step_1_pre": "1. In<PERSON>e se<PERSON><PERSON> en el", "license_site_step_2": "2. Haga clic en \"Desactivación de MXview One\" y \"Transferir a otro dispositivo\" en el sitio.", "license_site_step_3": "3. Seleccione MXview One como producto de software.", "login_license_site": "Inicie sesión en el sitio de licencias de Moxa", "title": "Reactivar licencia", "title_abbr": "Reactivar"}, "reason": "Estado", "relaunch": {"activating": "Activando...", "active_note": "La operación finalizará en 10 segundos."}, "remain": "Restante", "security_addon_trial": "Comience a experimentar el complemento de seguridad MXview One", "select_network_interface": "Seleccionar interfaz de red", "site_license_invalid": "Algunos sitios tienen licencias no válidas", "site_license_invalid_title": "Licencia no válida", "start_free_trial": "In<PERSON>ar <PERSON>", "start_free_trial_fail": "No se pudo iniciar la prueba gratuita", "start_free_trial_success": "La prueba gratuita se inició correctamente", "state": "Estado:", "state_all_licenses_invalidated": "No hay licencias válidas", "state_cannot_add_free_license": "No se puede agregar una licencia gratuita si se tienen licencias completas", "state_cannot_add_multiple_free_licenses": "No se pueden agregar varias licencias gratuitas", "state_format_incorrect": "El formato del archivo de licencia es incorrecto.", "state_general_error": "Error general", "state_license_deactivated": "La licencia ya está desactivada", "state_license_expired": "Licencia vencida", "state_license_is_registered": "La licencia ya está registrada en el sistema", "state_license_not_found": "No se encontró la licencia", "state_license_over_2000": "Su licencia superó los 2000 nodos, que es la cantidad máxima disponible para MXview One.", "state_license_upgrade_error": "No se puede agregar la licencia de actualización. MXview One necesita al menos una licencia completa.", "state_license_upgrade_no_full_license ": "No se puede eliminar la licencia. Primero, debe eliminar todas las licencias de actualización.", "state_no_full_license": "No hay licencias de MXview One válidas", "state_no_usercode": "Sin código de usuario", "state_over_nodes": "No hay suficientes nodos para la cantidad de dispositivos que desea implementar, adquiera nodos adicionales", "state_trial_expired": "Prueba vencida", "state_trial_is_began": "La prueba ya se inició", "state_trial_not_activated": "La versión de prueba todavía no se activó", "state_usercode_deactivated": "Código de usuario desactivado", "state_usercode_exists": "El código de usuario ya existe", "state_usercode_not_match": "El código de usuario de la licencia no coincide con el código de usuario del sistema", "state_usercode_not_match_adapter": "No se encontró el adaptador de red vinculado a esta licencia", "title": "Gestión de licencias", "trial_button": "PRUEBA", "trial_day": "días", "trial_expired": "Prueba vencida", "trial_over_nodes": "MXview One se bloqueará después de 30 minutos si no agrega suficientes licencias o elimina nodos por encima del límite de uso.", "trial_remaining": "Prueba restante", "user_code": "<PERSON>ó<PERSON> de usuario:", "valid": "<PERSON><PERSON><PERSON><PERSON>", "wireless_addon_trial": "Comience a disfrutar del complemento inalámbrico de MXview One"}, "link_list": {"rx": "Recepción (%)", "tx": "Transmisión (%)"}, "LINK_PROPERTIES": "Propiedades del vínculo", "LOGIN": {"account_reach_limit": "Se alcanzó el límite de inicios de sesión simultáneos (10)", "all_sites_offline": "Todos los sitios están sin conexión", "default_password_warning": "Cambie la contraseña predeterminada para garantizar la seguridad", "error": "Nombre de usuario/contraseña no válidos", "ie_not_supported": "MXview One no es compatible con IE, utilice Google Chrome para una mejor experiencia", "last_login_fail": "El último registro de un error de inicio de sesión fue", "last_login_succeed": "La última hora de inicio de sesión correcta fue", "login_fail_time": "{{loginTime}} de {{loginIp}}", "login_succeed_time": "{{loginTime}} de {{loginIp}}", "logout": "<PERSON><PERSON><PERSON>", "password": "Contraseña", "password_policy_mismatch": "La contraseña no se ajusta a la política de contraseñas", "sign_in": "In<PERSON><PERSON>", "username": "Nombre de usuario", "welcome": "Bienvenido"}, "max_char": "Máximo {{num}} caracteres", "min_char": "<PERSON><PERSON><PERSON> {{num}} caracteres", "model-port-mapping": {"port": "puerto", "web-ui": "UI web en el dispositivo"}, "MXVIEW_WIZARD": {"complete_page_title": "Completo", "navigate_to_wizard_page": "¿Desea utilizar el asistente de configuración de MXview One?", "step_add_scan_range": "Agregar rango de detección", "step_auto_topology": "Dibujar topología (para dispositivos compatibles con LLDP)", "step_create_group": "Crear grupo", "step_select_site": "Seleccione un sitio para configurar", "step_set_snmp": "Establecer la configuración de SNMP", "step_set_trap_server": "Establecer servidor de captura de SNMP", "title": "Asistente de configuración", "welcom_page_title": "Bienvenido al asistente de configuración"}, "NETWORK_MENU": {"add_link": "<PERSON><PERSON><PERSON> enlace", "add_wifi_ssid": "Agregar SSID de Wi-Fi", "alignment": {"bottom": "Alineación inferior", "left": "Alineación izquierda", "right": "Alineación derecha", "title": "Alineación", "top": "Alineación superior"}, "copy_device_list": "Copiar lista de dispositivos", "create_a_snapshot": "<PERSON><PERSON><PERSON>", "cybersecurity_control": "Controles de seguridad de la red", "delete": "Eliminar", "device_configuration": "Configuración del dispositivo", "device_control": "control de equipos", "device_dashboard": "Panel de control de dispositivos", "device_login_account": "cuenta del dispositivo", "device_panel": "Panel de dispositivos", "device_wireless_settings": "Parámetros por dispositivo", "disable_unsecured_http_and_telnet_console": "Deshabilite las consolas HTTP y Telnet inseguras", "disable_unused_ethernet_and_fiber_ports": "Deshabilite los puertos Ethernet y de fibra óptica no utilizados", "document": {"menu": {"open": "Abrir", "set": "<PERSON><PERSON><PERSON>"}, "title": "Documento"}, "dynamic_mac_sticky": "Sticky MAC dinámico", "edit": {"menu": {"add_device": "<PERSON><PERSON>dir dispositivo", "delete_background": "Eliminar fondo", "export_device_list": "Exportar lista de dispositivos", "export_topology": "Exportar topología", "import_device_list": "Importar lista de dispositivos", "set_background": "<PERSON><PERSON><PERSON>o"}, "title": "<PERSON><PERSON>"}, "execute_cli": {"menu": {"execute_cli_object": "Ejecutar script guardado", "execute_cli_script": "Secuencias de comandos CLI"}, "title": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>"}, "grid": {"menu": {"import_scd": "Importar SCD"}, "title": "Energía"}, "group": {"menu": {"change_group": "Cambiar grupo", "create_group": "Crear grupo", "group_maintenance": "Mantenimiento de grupos"}, "title": "Grupo"}, "grouping": "Grupo", "ips_configuration": "Configuración de IPS", "ipsec_status": "Estado de IPsec", "link_traffic": {"menu": {"packet_error_rate": "Tasa de errores de paquetes", "port_traffic": "Tráfico de puertos"}, "title": "Tráfico de enlaces"}, "locator": "Localizador", "mac_sticky_on_off": "Activar/desactivar Sticky MAC", "maintenance": {"menu": {"advance_settings": "Configuración avanzada", "assign_model": "<PERSON><PERSON><PERSON>o", "basic_information": "Información básica", "change_device_icon": "Cambiar el icono del dispositivo", "device_identification_settings": "Configuración de identificación del dispositivo", "eip_enable": "Habilitar EtherNet/IP", "eip_tcp_port": "Puerto TCP Ethernet/IP", "eip_udp_port": "Puerto UDP Ethernet/IP", "export_config": "Exportar configuración", "generate_qr_code": "Generar código QR", "import_config": "Importar configuración", "ip_configuration": "Configuración de IP", "modbus_enable": "Habilitar Modbus", "modbus_port": "Puerto Modbus", "modbus_tcp_configuration": "Configuración Modbus TCP", "polling_ip_setting": "Ajustes de IP de sondeo", "polling_settings": "Intervalo de sondeo de MXview One", "port_settings": "Configuración del puerto Ethernet/Fibra óptica", "s7_port": "Puerto Siemens S7comm", "s7_status": "Habilitación de Siemens S7comm", "serial_port_monitoring": "Monitoreo del puerto serial", "snmp_settings": "Protocolo de comunicación SNMP", "trap_server": "Ser<PERSON><PERSON> de captura", "upgrade_firmware": "Actualizar Firmware"}, "title": "Mantenimiento"}, "modify_device_alias": "Alias ​​del dispositivo", "policy_profile_deployment": "Despliegue del perfil de política", "reboot": "<PERSON><PERSON><PERSON>", "refresh": "Actualizar", "restart_mac_sticky_learning": "Vuelva a aprender Sticky MAC dinámico", "restore_to_created_snapshot": "Restaurar desde una instantánea", "scale": "Escala", "security_package_deployment": "Implementación de paquetes de seguridad", "set_port_label": "Establecer etiqueta de puerto", "severity_threshold": "Umbral de gravedad", "sfp": "SFP", "sfp_Info": "Información de SFP", "sfp_list": "Lista de SFP", "sfp_sync": "Umbral de sincronización del dispositivo", "tools": {"menu": {"device_panel": "Panel de dispositivos", "mib_browser": "Navegador MIB", "ping": "<PERSON>", "telnet": "Telnet", "web_console": "Consola web"}, "title": "Herramientas"}, "topology": {"menu": {"auto_layout": "Diseño automático", "auto_topology": "Topología automática", "embed": "Insertar widget", "scan_range": "Detección de dispositivos"}, "title": "Topología"}, "ungrouping": "Desagrupar", "upgrade_patch": "Aplicar actualizaciones del sistema", "visualization": {"menu": {"igmp": "IGMP", "security_view": "Vista de seguridad", "traffic_view": "Vista de tráfico", "vlan": "VLAN", "vlan_view": "Vista de VLAN"}, "title": "Visualización"}, "wifi_channel_change": "Cambiar canal wifi", "wireless_settings": "Parámetros generales", "wireless": {"menu": {"wireless_planner_view": "Cobertura inalámbrica", "wireless_playback_view": "Reproducción inalámbrica con roaming", "wireless_table_view": "Resumen de dispositivos inalámbricos"}, "title": "Inalámbrico"}}, "NETWORK": {"current_status": {"no_event": "No hay eventos", "title": "Estado actual", "v3_trap_event_clear_fail": "No se pudieron borrar los eventos de captura V3", "v3_trap_event_suggestion": "Compruebe la configuración de SNMP v3"}, "not_selected": "Seleccione un módulo para mostrar los detalles del dispositivo"}, "NORTHBOUND_INTERFACE": {"custom_opc": {"add_custom_opc": "Agregar etiquetas OPC personalizadas", "all": "Todos", "apply_fail": "No se pudieron agregar etiquetas OPC personalizadas", "apply_success": "Se agregaron nuevas etiquetas OPC personalizadas correctamente", "delete_fail": "No se pudo eliminar la etiqueta OPC", "delete_success": "La etiqueta OPC se eliminó correctamente", "device_properties": "Propiedades del dispositivo", "enable": "Etiquetas OPC personalizadas activadas", "filter_custom_opc": "Escriba para filtrar etiquetas OPC personalizadas", "get_fail": "No se pudieron obtener etiquetas OPC personalizadas", "property_name": "Nombre de la propiedad", "register_devices": "Dispositivos registrados", "title": "Etiquetas OPC personalizadas", "update_custom_opc": "Actualizar etiquetas OPC personalizadas", "update_fail": "No se pudo actualizar la etiqueta OPC", "update_success": "Las etiquetas OPC personalizadas se actualizaron correctamente"}}, "NOTIFICATION_SETTINGS": {"action": "Acción", "action_cant_deleted": "Esta acción está siendo utilizada por una o más notificaciones. Elimine esta acción de todas las notificaciones y vuelva a intentarlo.", "action_information": "Información de la acción", "action_name": "Nombre de la acción", "action_tab_hint": "Vaya a la pestaña Acción y añada una acción primero", "action_type": "Tipo", "add_action": "Agregar acción de notificación", "add_action_fail": "No se pudo agregar la acción", "add_action_success": "La acción nueva se agregó correctamente", "add_notification": "Agregar notificación", "add_notification_fail": "No se pudo agregar la notificación", "add_notification_success": "La notificación nueva se agregó correctamente", "check_security_tab": "Revisa la pestaña de ciberseguridad", "content": "Contenido", "delete_action_fail": "No se pudo eliminar la acción", "delete_action_success": "La acción se eliminó correctamente", "delete_notification_fail": "No se pudo eliminar la notificación", "delete_notification_success": "La notificación se eliminó correctamente", "edit_action": "Editar acción de notificación", "edit_notification": "Editar notificación", "email": "Correo electrónico", "email_content_hint": "El contenido aquí se añadirá al cuerpo del correo de notificación predeterminado", "event_type": "Tipo", "file_size_error": "El archivo debe tener un tamaño inferior a 1 MB", "file_type_error": "MXview One solo admite archivos .wav", "filter_action": "Escriba para filtrar acciones", "filter_notification": "Escriba para filtrar notificaciones", "messagebox": "Message Box", "mobile": "MXview ToGo", "mobile_number": "Número de móvil", "notification": "Notificación", "notification_name": "Nombre de la notificación", "notification_name_exist": "Este nombre ya está siendo utilizado por otra notificación", "receiver_email": "Correo electrónico del receptor", "register_devices": "Dispositivos registrados", "register_subscribers": "Acciones registradas", "slack": "<PERSON><PERSON>ck", "sms": "SMS", "snmptrap": "Captura de SNMP", "sound": "Archivo de sonido", "teams": "Microsoft Teams", "testConnection": "Conexión de prueba", "title": "Configuración de notificaciones", "update_action_fail": "No se pudo actualizar la acción", "update_action_success": "La acción se actualizó correctamente", "update_notification_fail": "No se pudo actualizar la notificación", "update_notification_success": "La notificación se actualizó correctamente", "webhook": "Webhook", "webhook_fail": "No se pudo enviar el webhook", "webhook_success": "El webhook se envió correctamente"}, "OPC_UA_SERVER": {"add_opc_tags": "Agregar etiqueta OPC", "anonymous": "<PERSON><PERSON><PERSON>", "auth_setting": "Configuración de autenticación", "certificate": "Certificado", "certificate_link": "Descargue y administre certificados desde el", "change_authentication_password": "Cambiar contraseña de autenticación", "change_password": "Cambiar contraseña", "control_panel": "Panel de control MXview One", "create_tags_fail": "Create tag fail", "create_tags_success": "Create tag success", "delete_tag_content": "¿Está seguro de que desea eliminar esta etiqueta OPC?", "delete_tags": "Delete OPC tag", "delete_tags_content": "Are you sure you want to delete these OPC tags?", "delete_tags_fail": "Delete tags fail", "delete_tags_success": "Etiquetas eliminadas correctamente", "device_property": "Device Property", "disabled": "Desactivado", "edit_opc_tags": "Editar etiqueta OPC", "edit_tags_fail": "Edit tag fail", "edit_tags_success": "Edit tag success", "enable_opc_server": "Habilitar el servidor OPC UA", "enabled": "Activado", "exceed_server_performance": "Se alcanzó el número máximo de dispositivos registrados (4000).", "get_tags_list_fail": "Get tags list fail", "ip_domain_name": "IP/Nombre de dominio", "method": "<PERSON><PERSON><PERSON><PERSON>", "opc_tags": "OPC Tags", "property_name": "Device Property", "registered_device": "Registered Device", "security": "<PERSON><PERSON> de seguridad", "security_placeholder": "No permitir seguridad", "server_settings": "Server Settings", "status": "status", "support_security_policy": "Políticas de seguridad admitidas", "tag_name": "Tag Name", "tag_name_duplicate": "El nombre de esta etiqueta ya existe", "tags_exceed_limit": "MXview One only supports 2000 tags.", "title": "OPC UA Server", "update_fail": "No se pudo actualizar la configuración", "update_server_setting_fail": "No se pudo actualizar la configuración del servidor", "update_server_setting_fail_no_up": "No se pudo actualizar la configuración. La IP especificada no existe.", "update_server_setting_success": "La configuración del servidor se actualizó correctamente", "username": "Nombre de usuario"}, "PAGES_MENU": {"about": "Acerca de", "administration": {"account_management": "Gestión de cuentas", "device_settings_template": "Plantilla de dispositivo predeterminada", "global_device_settings": "Ajustes globales del dispositivo", "license_management": "Gestión de licencias", "maintenance_scheduler": "Programador de mantenimiento", "preferences": "Preferencias", "system_settings": "Ajustes del sistema", "title": "Administración", "troubleshooting": "Resolución de problemas"}, "alert": {"custom_events": "Evento personalizado", "device_threshold": "Umbral del dispositivo", "event_settings": "Configuración de eventos", "link_threshold": "Umbral del enlace", "notifications": "Gestión de notificaciones", "title": "<PERSON><PERSON><PERSON>"}, "cli_object_database": {"title": "Scripts CLI guardados"}, "dashboard": "Panel de control", "device_management": {"account_password": "Cuentas y Contraseñas", "configuration_control": "Configuración y Control", "title": "Gestión de dispositivos"}, "devices": {"device_configurations": "Configuraciones de dispositivos", "list_of_devices": "Lista de dispositivos"}, "event": {"all_events": "Historial de eventos", "custom_events_management": "Evento personalizado", "notification_management": "Gestión de notificaciones", "syslog_settings": "Configuración de registro del sistema", "syslog_viewer": "V<PERSON>or de Syslog", "title": "Gestión de eventos"}, "firewall_policy_management": {"dos_descr": "Configurar la política DoS", "ips_descr": "Configurar la política IPS", "layer3to7_descr": "Configurar la política de firewall de capa 3 a 7", "policy_profile_deployment": "Despliegue del perfil de política", "policy_profile_management": "Gestión de perfiles de políticas", "security_package_deployment": "Implementación de paquetes de seguridad", "security_package_management": "Gestión de paquetes de seguridad", "sessionControl_descr": "Configurar la política de control de sesión", "title": "Gestión de políticas de firewall"}, "firmware_management": {"title": "Gestión de firmware"}, "help": {"about_mxview": "Acerca de MXview One", "api_documentation": "Documentación de API", "title": "<PERSON><PERSON><PERSON>", "user_manual": "Manual de usuario"}, "license": "Licencia", "links": {"list_of_rj45_links": "Lista de enlaces RJ45", "list_of_sfp_links": "Lista de enlaces SFP", "list_of_wifi_links": "Lista de enlaces Wi-Fi", "title": "Enlaces"}, "migrations": {"configuration_center": "Centro de configuración de dispositivos", "database_backup": "Copia de seguridad de base de datos", "job_scheduler": "Programador de mantenimiento", "title": "Migraciones"}, "network": {"scan_range": "Rango de <PERSON>", "title": "Red", "topology": "Topología", "wizard": "<PERSON><PERSON><PERSON>"}, "northbound_interface": {"custom_opc_tags": "Etiquetas OPC personalizadas", "opc_ua_server": "OPC UA Server", "restful_api_management": "Gestión de claves de API", "title": "Integración", "web_widget_embedded": "Widget web integrado"}, "preferences": "Preferencias", "report": {"assets_and_warranty": "Activos y garantía", "availability_report": "Informe de disponibilidad", "inventory_report": "Informe de inventario", "rogue_device_detection": "Detección de dispositivos no autorizados", "title": "Informes", "vlan": "Informe de VLAN"}, "scan_range_wizard": {"title": "Detección de dispositivos"}, "security": {"account_management": "Gestión de cuentas", "security_analyser": "Analizador de seguridad", "title": "Seguridad"}}, "pages": {"deviceDeployment": {"alreadySentSms": "Ya se enviaron {{ smsNumber }}/{{ max }} SMS en este mes", "applied": "Aplicado", "atLeastSelectOne": "Seleccione al menos un comando de control SMS", "cellularModuleDisable": "El módulo celular está deshabilitado, no se permite enviar comandos SMS.", "cellularStartConnecting": "Inicio de conexión celular", "cellularStopConnecting": "<PERSON><PERSON><PERSON> deja de conectarse", "configSync": "Se sincronizará la configuración de los dispositivos seleccionados.", "daily": "Diariamente", "date": "<PERSON><PERSON>", "deleteMsg": "¿Está seguro de que desea eliminar los dispositivos seleccionados?", "deleteSchedule": "Eliminar horario", "deleteScheduleSuccess": "La programación del dispositivo(s) se eliminó correctamente", "deleteSuccess": "El o los dispositivos se eliminaron correctamente", "deleteTitle": "Eliminar dispositivo(s)", "device_ip": "IP del dispositivo", "deviceConfiguration": "Configuración del dispositivo", "deviceDetail": "Detalle del dispositivo", "deviceDisableHint": "La función está deshabilitada desde el lado del dispositivo.", "deviceSelected": "dispositivo(s) seleccionado(s)", "endTime": "Fecha de finalización", "firmware": "Firmware", "firmwareUpgrade": "Se actualizará el firmware de los dispositivos seleccionados.", "firmwareVersion": "Versión de firmware", "general": "General", "groupName": "Nombre del grupo", "groupSelected": "grupo(s) seleccionado(s)", "invalidDate": "<PERSON><PERSON>", "invalidPeriod": "Periodo inválido", "lastRebootTime": "Hora del último reinicio", "lastUpdate": "Última comprobación", "lastUpdateTime": "Hora de la última actualización", "location": "Ubicación", "mac": "MAC", "manually": "A mano", "maxSms": "Has alcanzado el límite mensual de SMS (MÁX. {{ max }})", "noConfigAvailable": "No hay configuraciones disponibles", "noConfigMsg": "Verifique las configuraciones en la página Administración/Configuración del dispositivo.", "noFirmwareMsg": "Verifique los archivos de firmware en la página Administración/Firmware.", "noPackageMsg": "Consulte los paquetes de seguridad en la página Administración de paquetes de seguridad.", "noProfileAvailable": "No hay perfiles disponibles", "noProfileMsg": "Consulte los perfiles en la página de Gestión de perfiles de políticas.", "notSupportModel": "No es compatible con el modelo", "notSync": "No sincronizado", "noVersionAvailable": "No hay ninguna versión disponible", "oneTime": "<PERSON>", "outOfSync": "Fuera de sincronía", "package": "<PERSON><PERSON><PERSON>", "packageUpgrade": "Se actualizará el paquete de seguridad de los dispositivos seleccionados.", "packageVersion": "Versión del paquete", "period": "<PERSON><PERSON><PERSON>", "policyProfile": "Perfiles de políticas", "processing": "Procesamiento", "profileName": "Nombre de perfil", "profileSync": "Se sincronizará el perfil de los dispositivos seleccionados.", "reboot": "El(los) dispositivo(s) se reiniciarán.", "rebootDisabled": "Sólo se pueden reiniciar los dispositivos en línea.", "rebootMsg": "¿Está seguro de que desea reiniciar el(los) dispositivo(s) seleccionado(s)?", "rebootTitle": "Reiniciar dispositivo(s)", "remoteSmsControl": "Control remoto por SMS", "restoreConfigDisabled": "Sólo se pueden sincronizar dispositivos en línea del mismo tipo de modelo.", "sameVersionWarning": "Uno o más de los dispositivos seleccionados ya tienen aplicada la versión {{ version }}.", "schedule": "Programar", "scheduleDisabled": "Sólo se pueden programar dispositivos del mismo tipo de modelo.", "scheduleOverlapMsg": "No se puede seleccionar el intervalo de tiempo que ya asigna el reinicio o la actualización del firmware.", "scheduleSettings": "Configuración de programación", "scheduling": "Programación", "schedulingMode": "Modo de programación", "schedulingPeriod": "Periodo de programación", "schedulingReboot": "Programación de reinicio", "selectConfigFile": "Seleccionar archivo de configuración", "selectFile": "Seleccionar archivo", "sendSms": "Enviar SMS", "sendSmsControl": "Control de envío de SMS", "sendSmsOnCell": "Seleccione un dispositivo OnCell para enviar SMS de control", "sendSmsSuccess": "Enviar SMS con éxito", "serialNumber": "Número de serie", "setDoOff": "Desactivar DO", "setDoOn": "Establecer DO en", "shouldBeSameVersion": "La versión del paquete de los dispositivos seleccionados debe ser la misma.", "shouldHaveJanus": "Uno o más de los dispositivos seleccionados no tienen un paquete de seguridad instalado.", "shouldSyncOnline": "Sólo se pueden sincronizar los dispositivos en línea.", "showAll": "Mostrar todos los grupos y dispositivos", "showSelected": "Mostrar grupos y dispositivos seleccionados", "smsCountDownHint": "Enviar siguiente SMS después de 60 segundos", "softwarePackage": "Paquetes de seguridad", "startIpsecTunnel": "Iniciar <PERSON><PERSON><PERSON>", "startTime": "Fecha de inicio", "status": "Estado", "statusProfileName": "Estado / Nombre del perfil", "stopIpsecTunnel": "Detener el túnel IPsec", "switchSim": "Cambiar SIM", "sync": "sincronizado", "syncConfig": "Configuración de sincronización", "syncConfigTitle": "Sincronizar configuración con dispositivo(s)", "syncModified": "Sincronizado (Modificado)", "syncProfile": "Sincronizar perfil", "syncProfileTitle": "Sincronizar perfil con dispositivo(s)", "systemRestart": "Reinicio del sistema", "time": "Tiempo", "updateScheduleSuccess": "El cronograma del dispositivo se actualizó exitosamente.", "upgradeDisabled": "Sólo se pueden actualizar los dispositivos en línea.", "upgradePackageError": "Las versiones de firmware superiores a 2.5.0 e inferiores a 2.4.x no pueden coexistir.", "upgradePackageNotSameDisabled": "Sólo se pueden seleccionar los dispositivos del mismo tipo de modelo", "upToDate": "A hoy", "version": "Versión", "weekly": "<PERSON><PERSON><PERSON><PERSON>", "weeklyDay": "Día de la semana"}, "logging": {"eventLog": {"adp": "ADP", "audit": "Auditoría", "device": "Dispositivo", "dos": "Política DoS", "dpi": "Política de filtrado de protocolos", "endDate": "Fecha de finalización", "endTime": "Hora de finalización", "event": "Evento", "firewall": "Cortafuegos", "ips": "IPS", "l2Policy": "Política de capa 2", "l3Policy": "Política de capas 3 a 7", "malformed": "<PERSON><PERSON><PERSON> malformados", "sc": "Control de sesión", "setting": "Configuración", "severity": "Gravedad", "startDate": "Fecha de inicio", "startTime": "Hora de inicio", "tab": {"audit": {"deviceName": "Nombre del dispositivo", "event": "Evento", "groupName": "Nombre del grupo", "message": "Men<PERSON><PERSON>", "severity": "Gravedad", "time": "Tiempo", "username": "Nombre de usuario"}, "device": {"deviceName": "Nombre del dispositivo", "event": "Evento", "groupName": "Nombre del grupo", "mac": "Dirección MAC", "message": "Men<PERSON><PERSON>", "severity": "Gravedad", "time": "Tiempo", "username": "Nombre de usuario"}, "firewall": {"action": "Acción", "adp": "ADP", "all": "Todos", "appProtocol": "Protocolo de aplicación", "category": "Categoría", "deviceName": "Nombre del dispositivo", "dos": "Política DoS", "dpi": "Política de filtrado de protocolos", "dstIp": "IP de destino", "dstMac": "MAC de destino", "dstPort": "Puerto de destino", "etherType": "<PERSON><PERSON><PERSON> <PERSON>", "event": "Evento", "fromInterface": "Interfaz entrante", "groupName": "Nombre del grupo", "icmpCode": "Código ICMP", "icmpType": "Tipo de ICMP", "id": "<PERSON><PERSON><PERSON>", "ips": "IPS", "ipsCategory": "Categoría IPS", "ipsSeverity": "Gravedad del IPS", "l3Policy": "Política de capas 3 a 7", "malformed": "<PERSON><PERSON><PERSON> malformados", "message": "Mensaje adicional", "policyId": "ID de política", "policyName": "Nombre de directiva", "protocol": "Protocolo IP", "security": "Seguridad", "sessionControl": "Control de sesión", "severity": "Gravedad", "srcIp": "IP de origen", "srcMac": "Fuente MAC", "srcPort": "Puerto de origen", "subCategory": "Subcategoría", "tcpFlag": "Indicadores TCP", "time": "Tiempo", "toInterface": "Interfaz de salida", "trustAccess": "Acceso confiable", "username": "Nombre de usuario", "vlanId": "ID de VLAN"}, "vpn": {"deviceName": "Nombre del dispositivo", "event": "Evento", "groupName": "Nombre del grupo", "message": "Mensaje adicional", "severity": "Gravedad", "time": "Tiempo", "username": "Nombre de usuario"}}, "trustAccess": "Acceso confiable", "vpn": "vpn"}, "notification": {"advancedSettingMsg": "Una vez que se alcanza el número máximo de notificaciones en un período de tiempo, no se envían más notificaciones hasta el siguiente período.", "advancedSettings": "Configuración avanzada", "appProtocol": "Protocolo de aplicación", "arpFlood": "ARP-Flood", "atLeastOneReceiver": "Al menos un receptor", "bufferOverflow": "Desbordamiento de búfer", "chooseDevices": "Seleccione dispositivos", "createdBy": "<PERSON><PERSON>o por", "createNotification": "Agregar evento de ciberseguridad", "createSuccess": "Evento de ciberseguridad creado con éxito", "deleteFailed": "Este evento se utiliza para notificaciones y no se puede eliminar.", "deleteKey": "Eventos de ciberseguridad", "deleteNotification": "Eliminar notificación", "deleteSuccess": "Eventos de ciberseguridad eliminados con éxito", "deviceCount": "Número de dispositivos", "deviceName": "Nombre del dispositivo", "DNP3": "DNP3", "dosAttacks": "Ataques DoS", "dstIp": "IP de destino", "dstMac": "MAC de destino", "editNotification": "Editar evento de ciberseguridad", "EIP": "EIP", "email": "Correo electrónico", "emailContent": "Contenido del correo electrónico", "emailContentDefault": "El evento ${event} activado en el dispositivo ${productModel}, ${deviceName}, ocurrió a las ${eventTime}.", "emailHeader": "[MXsecurity] Notificación ${notificationName}\ngenerado desde ${deviceName}", "emailMsgAutoSentFrom": "Esta notificación fue enviada automáticamente\nde MXsecurity.", "emailMsgCheck": "Por favor revise la información detallada\nen MXsecurity.", "emailMsgGreeting": "Estimado señor/señora:", "emailMsgSignOff": "Atentamente,\nSeguridad MX", "eq": "Igual a", "event": "Evento", "event_used": "Este evento ya está en uso en la configuración de notificaciones y no se puede modificar.", "eventFilter": "Elija un evento y filtre", "eventFilterRule": "Regla de filtrado de eventos", "eventTime": "Hora del evento", "exploits": "Hazaña<PERSON>", "fileVulnerabilities": "Vulnerabilidades de archivos", "filterRule": "Regla de filtrado", "filterRuleDetail": "Detalle de las reglas de filtrado", "finScan": "FIN Scan", "floodingScan": "Inundación y escaneo", "GOOSE": "GOOSE", "gt": "<PERSON>ás bajo que", "gte": "<PERSON>or o igual que", "icmpDeath": "ICMP-Flood", "IEC-104": "IEC-104", "ipAddress": "Dirección IP", "ipRangeHint": "Podrías usar * para representar el resultado\nde la máscara de subred /8/16/24, ej.\n192.168.*.*\nNo se pudo utilizar al principio de\nuna dirección IP o sola en el medio *", "ipsCate": "Categoría IPS", "ipSpoofing": "Suplantación de IP", "ipsSeverity": "Gravedad del IPS", "location": "Ubicación", "lt": "Más alto que", "lte": "Mayor o igual que", "macAddress": "Dirección MAC", "macRangeHint": "Podrías usar * para representar un rango de\nDirección MAC, p. ej. 00:90:E8:*:*:*\nNo se pudo utilizar al comienzo de una\nDirección MAC o sola en el medio.", "malwareTraffic": "Tráfico de malware", "maxEnableSize": "El máximo de notificaciones habilitadas es {{num}}.", "maxNotification": "MAX. Notificación", "maxPerUserSize": "El máximo de notificaciones por usuario es {{num}}.", "MMS": "MMS", "Modbus/TCP": "Modbus/TCP", "newTcpWithoutSynScan": "TCP Sessions Without SYN", "nmapIdScan": "NMAP-ID Scan", "nmapXmasScan": "NMAP-<PERSON><PERSON>", "notificationActions": "Acciones de notificación", "notificationEvent": "Evento de notificación", "notificationInfo": "Información de notificación", "notificationLimit": "Límite de notificaciones", "notificationName": "Nombre del evento", "nullScan": "<PERSON><PERSON>", "OmronFINS": "Omron FINS", "periodTime": "Periodo de tiempo", "policyName": "Nombre de directiva", "productModel": "Modelo del Producto", "protocolAttackProtection": "Protección contra ataques de protocolo", "receiverEmailAddress": "Dirección de correo electrónico del destinatario", "receiverSetting": "Configuración del receptor", "reconnaissance": "Reconocimiento", "resetToDefault": "Restablecen a los predeterminados", "serialNumber": "Número de serie", "severity": "Gravedad", "severityMode": "<PERSON><PERSON>", "severityRule": "Regla de severidad", "showAllDevices": "Mostrar todos los dispositivos", "showSelectedDevices": "Mostrar dispositivos seleccionados", "srcIp": "IP de origen", "srcMac": "Fuente MAC", "Step7Comm": "Step7Comm", "subCate": "Subcategoría", "synFinScan": "SYN/FIN Scan", "synFlood": "SYN-Flood", "synRstScan": "SYN/RST Scan", "syslog": "Syslog", "syslogContent": "Contenido de Syslog", "syslogContentDefault": "Se activó la notificación ${notificationName} en el dispositivo ${productModel}, ${deviceName}, a las ${eventTime}. Consulta la información detallada en MXsecurity.", "udpFlood": "UDP-Flood", "updateSuccess": "El evento de ciberseguridad se actualizó con éxito", "webThreats": "Amenazas web", "xmasScan": "<PERSON><PERSON>"}}, "management": {"deviceConfiguration": {"configModel": "Modelo de configuración", "configName": "Configurar nombre", "createSuccess": "Configuración del dispositivo creada exitosamente.", "deleteKey": "Configuraciones del dispositivo", "deleteSuccess": "Las configuraciones del dispositivo se eliminaron correctamente", "editConfig": "Editar configuración", "enterConfigInfo": "Introducir información del archivo de configuración", "firmwareVersion": "Versión de firmware", "group": "Grupo", "isReferenced": "Se hace referencia a una o más de las configuraciones seleccionadas.", "lastModifiedTime": "Hora de última modificación", "location": "Ubicación", "mac": "Dirección MAC", "maxTableSize": "La configuración máxima es {{num}}.", "noModelMsg": "No existe modelo para configuración", "offlineWarning": "Dispositivo sin conexión", "onlyAcceptIni": "Sólo se aceptan archivos de configuración en formato ' .ini ' .", "onlyOneFilePerTime": "<PERSON><PERSON><PERSON> se puede cargar un archivo a la vez.", "selectConfigFile": "Seleccionar archivo de configuración", "selectWarning": "Solo permite un dispositivo para la copia de seguridad de la configuración", "serialNumber": "Número de serie", "updateSuccess": "La configuración del dispositivo se actualizó correctamente", "uploadConfigFile": "Subir archivo de configuración (.ini)", "uploadConfigMethod": "Método de configuración de carga", "uploadConfigTitle": "Cargar archivo de configuración del dispositivo", "uploadDeviceConfig": "Cargar configuración desde el dispositivo", "uploadLocalConfig": "Cargar configuración desde local"}, "deviceGroup": {"accessPermission": "Permiso de acceso", "addDevices": "Agregar dispositivos", "adminPermission": "Los usuarios administradores tienen permiso para todos los grupos.", "createGroup": "Crear grupo", "createSuccess": "Grupo de dispositivos creado exitosamente.", "deleteKey": "Eliminar grupo(s) de dispositivos", "deleteSuccess": "Los grupos de dispositivos se eliminaron correctamente.", "description": "Descripción", "deviceCount": "Número de dispositivos", "editGroup": "Editar grupo de dispositivos", "enterGroupInfo": "Introducir información del grupo", "firmwareVersion": "Versión de firmware", "grantAccessPermission": "Conceder per<PERSON><PERSON> de acceso", "group": "Grupo", "groupName": "Nombre del grupo", "location": "Ubicación", "mac": "MAC", "role": "Papel", "serialNumber": "Número de serie", "showAllDevices": "Mostrar todos los dispositivos", "showSelectedDevices": "Mostrar dispositivos seleccionados", "status": "Estado", "updateSuccess": "El grupo de dispositivos se actualizó correctamente.", "username": "Nombre de usuario"}, "firmware": {"buildTime": "Tiempo de construcción", "deleteKey": "Firmware", "deleteSuccess": "Firmware eliminado exitosamente.", "description": "Descripción", "dropZoneTitle": "Subir un archivo de firmware (.rom)", "isReferenced": "Se hace referencia a uno o más del firmware seleccionado.", "maxRowMsg": "El número máximo de archivos de firmware es {{ max }}.", "maxSize": "El tamaño máximo de archivo permitido es 1 GB.", "modelSeries": "Serie de modelos", "onlyAcceptRom": "Sólo se aceptan archivos de firmware en formato ' .rom ' .", "onlyOneFilePerTime": "<PERSON><PERSON><PERSON> se puede cargar un archivo a la vez.", "uploadFirmware": "Subir firmware", "uploadSuccess": "Firmware cargado exitosamente.", "version": "Versión"}, "inUse": "Sí", "object": {"filter": {"address": "Dirección IP y subred", "code": "Código", "createObject": "<PERSON><PERSON><PERSON> objeto", "createSuccess": "Objeto creado exitosamente", "customIpProtocol": "Protocolo IP personalizado", "decimal": "(Decimal)", "deleteKey": "<PERSON><PERSON><PERSON><PERSON>(s)", "deleteSuccess": "Objeto(s) eliminado(s) exitosamente.", "detail": "Detalles", "editObject": "<PERSON>ar objeto", "endPort": "Puerto: Fin", "icmp": "ICMP", "icmpCode": "Código ICMP", "icmpType": "Tipo de ICMP", "industrialAppService": "Servicio de aplicaciones industriales", "ipAddress": "Dirección IP", "ipEnd": "Dirección IP: Fin", "ipProtocol": "Protocolo IP", "ipRange": "<PERSON>ngo de <PERSON>", "ipStart": "Dirección IP: Inicio", "ipType": "Tipo de IP", "isReferenced": "Se hace referencia a uno o más de los objetos seleccionados", "leaveAsAny": "Déjelo en blanco para representar Cualquiera", "maxRowMsg": "El número máximo de objetos es {{ max }}.", "name": "Filtrar", "needSelectedMsg": "Seleccione al menos un elemento", "networkName": "Nombre de red", "networkService": "Servicio de <PERSON>", "objectName": "Nombre", "objectReference": "Referencias de objetos", "objectReferenceMsg": "Este objeto está referenciado por un índice de política en los siguientes perfiles:", "objectType": "Tipo de objeto", "port": "Puerto", "portRange": "Rango de puertos TCP y UDP", "selectIndustrialAppService": "Seleccione los servicios de aplicación industrial *", "selectNetworkService": "Seleccione el(los) servicio(s) de red *", "servicePortType": "Tipo de puerto de servicio", "singleIp": "IP única", "singlePort": "Puerto TCP y UDP", "startPort": "Puerto: Inicio", "subnet": "<PERSON>red", "subnetMask": "Subnet Mask", "tcp": "TCP", "tcpUdp": "TCP y UDP", "type": "Tipo", "udp": "UDP", "updateSuccess": "Objeto actualizado exitosamente", "userDefinedService": "<PERSON><PERSON><PERSON> definido por el usuario"}, "interface": {"bridge": "Puente", "createInterface": "Crear objeto de interfaz", "createSuccess": "Interfaz creada exitosamente.", "deleteKey": "Interfaz(es)", "deleteSuccess": "Interfaz(es) eliminadas exitosamente.", "editInterface": "Editar objeto de interfaz", "interfaceName": "Nombre de interfaz", "interfaceReference": "Referencias de interfaz", "interfaceReferenceMsg": "Esta interfaz está referenciada por un índice de política en los siguientes perfiles:", "invalidKey": "Los siguientes nombres están reservados: Cualquier", "isReferenced": "Se hace referencia a una o más de las interfaces seleccionadas.", "maxRowMsg": "El número máximo de interfaces es {{ max }}.", "mode": "Modo", "name": "interfaz", "port": "Basado en puerto", "updateSuccess": "Interfaz actualizada exitosamente.", "vlan": "VLAN", "vlanIdBridgeType": "ID de VLAN / Modo puente", "zone": "Basado en zonas"}}, "policyProfile": {"createProfile": "<PERSON><PERSON><PERSON> perfil de polí<PERSON>", "createSuccess": "Perfil creado exitosamente.", "deleteKey": "Perfil(es)", "deleteSuccess": "Perfil(es) eliminado(s) exitosamente.", "deployment": {"profile_title": "Despliegue del perfil de política", "security_title": "Implementación de paquetes de seguridad", "title": "Despliegue del perfil de política"}, "dos": {"all_protection_types": "Habilitar todos los tipos de protección", "dosLogSetting": "Configuración del registro DoS", "dosSetting": "Configuración DoS", "floodProtection": "Protección contra inundaciones", "limit": "Límite", "portScanProtection": "Protección de escaneo de puertos", "sessionSYNProtection": "Protección SYN de sesión", "stat1": "<PERSON><PERSON>", "stat10": "SYN-Flood", "stat11": "ARP-Flood", "stat12": "UDP-Flood", "stat2": "<PERSON><PERSON>", "stat3": "NMAP-<PERSON><PERSON>", "stat4": "SYN/FIN Scan", "stat5": "FIN Scan", "stat6": "NMAP-ID Scan", "stat7": "SYN/RST Scan", "stat8": "TCP Sessions Without SYN", "stat8Tooltip": "Limitación: Para arquitecturas de red asimétricas y cuando NAT está habilitado, se recomienda encarecidamente no habilitar la opción \"Sesiones TCP sin SYN\" para evitar desconexiones inesperadas.", "stat9": "ICMP-Flood", "title": "DoS"}, "editProfile": "Editar perfil de política", "ips": {"accept": "Aceptar", "category": "Categoría", "custom": "(costumbre)", "id": "ID", "impact": "Impacto", "monitor": "Monitor", "noPackageMsg": "Consulte los paquetes de seguridad en la página Administración de paquetes de seguridad.", "noVersionAvailable": "No hay ninguna versión disponible", "packageVersion": "Versión del paquete", "reference": "Referencia", "reset": "Reiniciar", "ruleSetting": "Configuración de reglas", "title": "IPS", "updateSuccess": "Regla(s) actualizada(s) exitosamente.", "warningMsg": "Antes de configurar cualquier política, asegúrese de que la función Sistema de prevención de intrusiones (IPS) esté habilitada en la pantalla Firewall > Protección avanzada > Configuración en la interfaz web del dispositivo."}, "isReferenced": "Se hace referencia a uno o más de los perfiles seleccionados.", "layer3to7": {"allowAll": "<PERSON><PERSON><PERSON>", "createPolicy": "Crear política de capas 3 a 7", "createSuccess": "Política de capa 3-7 creada exitosamente.", "default_action_log": "Registro de eventos", "default_action_log_destination": "Destino del registro", "default_action_severity": "Gravedad", "defaultAction": "Acción", "deleteKey": "Políticas de capa 3 a 7", "deleteSuccess": "Las políticas de capa 3 a 7 se eliminaron correctamente.", "deleteTitle": "Eliminar la política de capas 3 a 7", "denyAll": "<PERSON><PERSON><PERSON> todo", "destinationAddress": "Dirección de destino", "destinationPort": "Puerto o protocolo de destino", "destIpAddress": "Dirección IP de destino", "destService": "<PERSON><PERSON><PERSON>", "editPolicy": "Editar la política de capas 3 a 7", "enforce": "Estado", "enforcement": "Estado", "event": "Evento", "eventSetting": "Configuración de política predeterminada", "filterMode": "<PERSON>do de filtro", "globalSetting": "Configuración global del firewall", "incomingInterface": "Interfaz entrante", "ipAndPortFiltering": "Filtrado de IP y puertos", "ipAndSourceMacBinding": "Vinculación de IP y MAC de origen", "ipTypeError": "El protocolo IP del puerto de origen ({{ source }}) es diferente del protocolo IP del puerto de destino ({{ dest }})", "maxRowMsg": "El número máximo de objetos es {{ max }}.", "outgoingInterface": "Interfaz de salida", "policyName": "Nombre", "protocolService": "Protocolo y Servicio", "sourceAddress": "Dirección de origen", "sourceIpAddress": "Dirección IP de origen", "sourceMacAddress": "Dirección MAC de origen", "sourceMacFiltering": "Filtrado de MAC de origen", "sourcePort": "Puerto de origen", "title": "Capa 3-7", "updateSuccess": "La política de capas 3-7 se actualizó correctamente."}, "maxRowMsg": "El número máximo de perfiles es {{ max }}.", "profileName": "Nombre de perfil", "profileReference": "Referencias del perfil", "profileReferenceMsg": "Este perfil está referenciado por los siguientes dispositivos:", "sessionControl": {"concurrentTcp": "Conexiones TCP concurrentes", "connectionsRequestUnit": "conexiones/es", "connectionsUnit": "Conexiones", "createPolicy": "Crear una política de control de sesión", "createSuccess": "Política de control de sesión creada exitosamente.", "deleteKey": "Política(s) de control de sesión", "deleteSuccess": "Las políticas de control de sesión se eliminaron correctamente.", "destinationIp": "IP de destino", "destinationPort": "Puerto de destino", "destIpAddress": "Dirección IP", "destPort": "Puerto", "drop": "soltar", "editPolicy": "Editar la política de control de sesión", "enforcement": "Estado", "maxRowMsg": "El número máximo de políticas para este dispositivo es {{ max }}.", "monitor": "Monitor", "sub_title": "Protector de recursos de servicio y host de red", "tcpConnectionLimit": "Límite de conexión TCP", "tcpDestError": "La dirección IP y el puerto no pueden ser ambos al mismo tiempo", "tcpDestination": "Destino TCP", "tcpLimitError": "Debes configurar al menos una limitación", "tcpLimitMsg": "Se requiere al menos una limitación", "title": "Control de sesión", "totalTcp": "Conexiones TCP totales", "updateSuccess": "La política de control de sesión se actualizó correctamente."}, "tabInspection": "Objetos de inspección", "tabInterface": "Objetos de interfaz", "tabPolicyProfile": "Perfiles de políticas", "title": "Gestión de perfiles de políticas", "updateSuccess": "Perfil actualizado exitosamente."}, "scheduleInUse": "Ho<PERSON>io en uso", "scheduling": "Programación", "softwarePackage": {"applicationProducts": "Productos aplicables", "auto-download": "Descarga automática", "bugsFixed": "Errores corregidos", "buildTime": "Tiempo de construcción", "changes": "Cambios", "checkConnection": "Verifique el estado de la conexión al servidor de actualización de Moxa.", "checkNewPackage": "Comprueba si hay nuevas versiones de paquetes en el servidor MOXA.", "checkSoftwarePackage": "Comprobar actualizaciones de paquetes", "daily": "Diariamente", "deleteKey": "Paquete(s) de seguridad", "deleteSuccess": "Paquete(s) de seguridad eliminados exitosamente.", "description": "Descripción", "detailInfo": "Información detallada", "dropZoneTitle": "Subir un archivo de paquete (.pkg)", "endDate": "Fecha de finalización", "endTime": "Hora de finalización", "enhancements": "<PERSON><PERSON><PERSON>", "event": "Evento", "isReferenced": "Se hace referencia a uno o más de los paquetes seleccionados.", "janus": "Paquetes de seguridad de red", "lastConnectionCheck": "Comprobación de la última conexión", "lastSoftwarePackageUpdateResult": "Resultado de la última actualización del paquete de seguridad", "licenseActivationReminder": "Recordatorio de activación de licencia", "licenseActivationReminderContent": "Para garantizar mecanismos de seguridad mejorados, active la licencia para habilitar esta función.", "licenseTransferReminder": "Recordatorio de transferencia de licencia", "licenseTransferReminderContent": "Para garantizar mecanismos de seguridad mejorados, transfiera su licencia de MXsecurity antes de cargar los paquetes de seguridad de red.", "local": "Local", "log": "Registros de eventos", "maxPackageMsg": "Descargas simultáneas máximas: {{ max }} archivos.", "maxRowMsg": "El número máximo de paquetes de software es {{ max }}.", "maxSize": "El tamaño máximo de archivo permitido es 1 GB.", "message": "Men<PERSON><PERSON>", "newFeatures": "Nuevas características", "notes": "Notas", "onlyAcceptPkg": "Sólo se aceptan archivos en formato ' .pkg ' .", "onlyOneFilePerTime": "<PERSON><PERSON><PERSON> se puede cargar un archivo a la vez.", "packageDownloading": "El descargador de paquetes está funcionando para otros", "packageReference": "Referencias de paquetes", "packageReferenceMsg": "Este paquete está referenciado por los siguientes perfiles:", "period": "<PERSON><PERSON><PERSON>", "productModel": "Modelo del Producto", "releaseDate": "<PERSON><PERSON>", "releaseNote": "Notas de lanzamiento", "scheduling": "Comprobación de actualización programada", "schedulingMode": "Modo de programación", "server": "Estado del servidor de actualización de Moxa", "serverDisconnected": "El paquete de seguridad solo se puede comprobar cuando el servidor está conectado.", "severity": "Gravedad", "softwarePackageAlreadyLatest": "El paquete de seguridad está actualizado.", "softwarePackageCheck": "Comprobación del paquete de seguridad", "softwarePackagesFile": "Archivo de paquetes de seguridad", "softwarePackagesUpdateCheck": "<PERSON>ual<PERSON><PERSON> paque<PERSON> de seguridad", "startDate": "Fecha de inicio", "startTime": "Hora de inicio", "supportedFunctions": "Funciones admitidas", "supportedOperatingSystems": "Sistemas operativos compatibles", "supportModel": "Modelos compatibles", "supportSeries": "Series compatibles", "syncSettingNotSet": "Complete la configuración de sincronización para verificar el paquete de seguridad.", "syncSettings": "Comprobación de actualización programada", "syncSettingUpdateSuccess": "Configuración actualizada exitosamente.", "syncSoftwarePackageBySchedule": "Comprueba automáticamente si hay actualizaciones del paquete de seguridad para los modelos especificados según un cronograma especificado por el usuario.", "syncSoftwarePackageByScheduleTooltip": "Configure la frecuencia con la que se verificará el servidor Moxa en busca de actualizaciones de paquetes de seguridad.", "time": "Time", "title": "Gestión de paquetes de seguridad", "updateCheckTooltip": "Verifique el servidor Moxa en busca de actualizaciones del paquete de seguridad para asegurarse de que está usando la última versión.", "uploadBy": "subido por", "uploadSoftwarePackage": "Subir paque<PERSON>", "uploadSuccess": "Paquete de seguridad cargado exitosamente.", "username": "Nombre de usuario", "version": "Versión", "weekday": "día", "weekly": "<PERSON><PERSON><PERSON><PERSON>", "zeus": "Paquetes de agente de MXsecurity"}}}, "PORT_SETTING": {"another_port_setting_error": "Se está procesando otra configuración", "apply_another_port": "Aplicar la configuración a otro puerto", "disable_port_warning": "Advertencia: Al desactivar este puerto, se desconectarán los dispositivos conectados a él.", "enable": "Habilitar", "get_port_setting_fail": "No se pudo obtener la configuración del puerto", "hint": "* If the settings fail, please confirm that the selected port can be configured.", "media_type": "Tipo de medio", "port": "Puerto", "port_description": "Descripción del puerto", "port_name": "Nombre del puerto", "port_select": "Seleccionó", "set_fail": "No se pudieron actualizar algunos puertos, vuelva a intentarlo más tarde", "set_success": "Todos los ajustes de los puertos se actualizaron correctamente", "title": "Configuración del puerto Ethernet/Fibra óptica"}, "PREFERENCES": {"advanced": "<PERSON><PERSON><PERSON>", "appearance": "Apariencia", "default_view": {"choose_start_page": "Seleccione una página de inicio", "dashboard": "Panel de control", "title": "Vista predeterminada", "topology": "Topología"}, "device_appearance": {"alias": "<PERSON><PERSON>", "bottom_hint": "Si cambia la configuración del alias, elimine el dispositivo en la topología y, luego, vuelva a realizar la detección o añada un dispositivo para completar la configuración de 'Alias'", "bottom_label": "Etiqueta inferior", "bottom_label_items": {"alias": "<PERSON><PERSON>", "location": "Ubicación", "mac": "MAC", "model_name": "Nombre del modelo", "none": "<PERSON><PERSON><PERSON>", "sysname": "SysName"}, "get_fail": "No se pudo obtener la apariencia del dispositivo", "ip_address": "Dirección IP", "set_fail": "No se pudo actualizar la apariencia del dispositivo", "set_success": "La apariencia del dispositivo se actualizó correctamente", "title": "Dispositivo"}, "device": {"login": "In<PERSON><PERSON>", "title": "Dispositivo"}, "dialog": {"desc": "Borrar todos los ajustes de 'No volver a mostrar este mensaje' y volver a mostrar todos los cuadros de diálogo", "title": "Cuadro de diálogo"}, "display": "Apariencia", "email_config": {"allow_selfsigned_cert": "Permitir certificado autofirmado", "apply_fail": "No se pudo actualizar la configuración del servidor de correo electrónico", "apply_success": "La configuración del servidor de correo electrónico se actualizó correctamente", "encryption": "Cifrado", "password": "Contraseña", "port_number": "Número de puerto", "sender_address": "Dirección del remitente", "server_domain_name": "Nombre de dominio/IP del servidor", "title": "Configuración del servidor de correo electrónico", "username": "Nombre de usuario"}, "events": {"apply_fail": "No se pudo establecer el umbral de eventos", "apply_success": "El umbral de eventos se actualizó correctamente", "availability_under": "Disponibilidad por debajo de", "bandwidth_utilization_over": "Utilización del ancho de banda por encima de", "bandwidth_utilization_under": "Utilización del ancho de banda por debajo de", "link_down": "Inactivo", "link_up": "Activo", "packet_error_rate_over": "Tasa de error de paquetes por encima de", "port_looping": "Bucle de puerto", "sfp_rx_below_threshold": "Recepción de SFP por debajo de", "sfp_temp_over_threshold": "Temperatura de SFP por encima de", "sfp_tx_below_threshold": "Transmisión de SFP por debajo de", "sfp_volt_below_threshold": "Voltaje de SFP por debajo de", "sfp_volt_over_threshold": "Voltaje de SFP por encima de", "title": "Eventos"}, "labs": {"colored_link_desc": "Cuando se activa, todos los enlaces inalámbricos se colorean de acuerdo con su intensidad de señal.", "desc": "Al activar Labs se añadirán las características experimentales que se enumeran a continuación. Estas se encuentran en una fase inicial, pero no dañarán su sistema", "dialog_title": "Habilitar las características de Labs", "title": "MXview One Labs"}, "language": {"default_language": "Idioma", "en_US": "English", "fail": "No se pudo actualizar el idioma", "success": "Se actualizó el idioma", "title": "Idioma", "zh_CN": "简体中文", "zh_TW": "繁體中文"}, "login_authentication": {"apply_fail": "No se pudo actualizar la autenticación de inicio de sesión", "apply_success": "La autenticación de inicio de sesión se actualizó correctamente", "authentication_protocol": "Protocolo de autenticación", "local": "Local", "tacacs": "TACACS+", "tacacs_local": "TACACS+, Local", "title": "Autenticación de inicio de sesión"}, "login_notification": {"fail": "No se pudo actualizar la notificación de inicio de sesión", "login_authentication_failure_message": "Mensaje de error de autenticación de inicio de sesión", "login_message": "Mensaje de inicio de sesión", "show_default_password_notification": "Mostrar notificación de contraseña predeterminada", "show_login_failure_records": "Mostrar registros de fallos de inicio de sesión", "success": "Se actualizó la notificación de inicio de sesión", "title": "Notificación de inicio de sesión"}, "management_interface": {"help": "Esta página se utiliza para configurar las interfaces de conexión de MXview One a los dispositivos, incluidos los puertos http, https y telnet", "http_port": "Puerto HTTP", "htts_port": "Puerto HTTPS", "invalid_port": "Ingrese un número entero de 1 a 65535", "set_fail": "No se pudo actualizar la interfaz de gestión", "set_success": "La interfaz de gestión se actualizó correctamente", "telnet_port": "Puerto Telnet", "title": "Interfaz de gestión", "web_console_protocol": "Protocolo de la consola web"}, "modbus_tcp_configuration": {"port": "port"}, "opc_server_config": {"apply_fail": "No se pudo actualizar la configuración del servidor OPC", "apply_success": "La configuración del servidor OPC se actualizó correctamente", "enable_opc_server": "Habilitar", "title": "Configuración del servidor OPC"}, "password_policy": {"fail": "No se pudo actualizar la política de contraseñas", "has_digits": "Al menos un dígito (0 - 9)", "has_special_chars": "Al menos un carácter especial (~!@#$%^&*-_|;:,.<>[]{}())", "min_password_length": "<PERSON><PERSON><PERSON> (4 - 16)", "min_password_length_error": "Ingrese un valor válido", "mixed_case": "Una combinación de letras mayúsculas y minúsculas (A - Z, a - z)", "password_strength_check": "Comprobación de la complejidad de la contraseña", "success": "Se actualizó la política de contraseñas", "title": "Política de contraseñas"}, "search": "Buscar", "security_view": {"all": "Todos", "awk_device_credentials_hint": "Para admitir la vista de seguridad, debe establecer un nombre de usuario y una contraseña para este dispositivo", "basic": "Básico", "basic_text": "Básico", "built_in_profile": "Perfil incorporado", "check_item": "Comprobar elemento", "colors_for_check_result": "Colores para el resultado de la comprobación", "current_setting": "Configuración actual:", "custom": "Personalizado", "custom_profile": "Seleccione una línea de base para el perfil personalizado", "device_security_level": "Nivel de seguridad del dispositivo:", "failed": "No se pudo actualizar la vista de seguridad", "filter_result": "resultados para", "high": "Alto", "high_text": "Alto", "medium": "Mediano", "medium_text": "Mediano", "new_profile": "Perfil nuevo", "not_pass": "No superado", "open": "Abrir", "pass": "Superado", "profile": "Perfil", "profile_details": "Detalles del perfil", "success": "Se actualizó la vista de seguridad", "title": "Vista de seguridad", "unknown": "Desconocido", "user_defined": "Definido por el usuario"}, "Server": "<PERSON><PERSON><PERSON>", "site_name_configuration": "Configuración del nombre del sitio", "sms_config": {"apply_fail": "No se pudo actualizar la configuración de SMS", "apply_success": "La configuración de SMS se actualizó correctamente", "baud_rate": "Velocidad en baudios", "com_port": "Puerto COM", "mode": "Modo", "title": "Configuración de SMS"}, "snmp_configuration": {"help": "Establezca la configuración de SNMP para acceder a los dispositivos de la red", "title": "Configuración de SNMP"}, "SNMP_TRAP": {"apply_fail": "No se pudo actualizar la configuración del servidor de captura de SNMP", "apply_success": "La configuración del servidor de captura de SNMP se actualizó correctamente", "community1": "Comunidad del servidor de captura 1", "community2": "Comunidad del servidor de captura 2", "device_list": "Lista de dispositivos", "device_trap": "Servidor de captura de SNMP del dispositivo", "forward_trap_control1": "Reenviar la captura recibida al servidor 1", "forward_trap_control2": "Reenviar la captura recibida al servidor 2", "ip1": "Dirección IP del servidor de captura 1", "ip2": "Dirección IP del servidor de captura 2", "mxview_trap": "Servidor de captura de SNMP de MXview One", "version": "Versión de SNMP", "version_1": "Versión de SNMP 1", "version_2": "Versión de SNMP 2c"}, "syslog_config": {"already_running": "El servidor ya está en marcha o detenido", "apply_fail": "No se pudo actualizar la configuración del servidor de Syslog", "apply_success": "La configuración del servidor de Syslog se actualizó correctamente", "enable_syslog_server": "Habilitar el servidor de Syslog incorporado", "invalid_port": "Ingrese un número entero de 1 a 65535", "syslog_server_port": "Puerto del servidor de Syslog", "title": "Configuración del servidor de Syslog"}, "system_configuration": {"apply_fail": "No se pudo actualizar la configuración del sistema", "apply_success": "La configuración del sistema se actualizó correctamente", "background_discovery": "Detección en segundo plano", "disk_hint": "La alarma se desactiva si el umbral se establece en 0", "playback": "Reproducción", "playback_hint_1": "* Cuando la función \"Reproducción\" está activada, MXview One graba el estado de los dispositivos y enlaces cuando se produce un evento para que pueda entrar en el modo de reproducción más tarde para ver el proceso detallado", "playback_hint_2": "* Se requiere más espacio en disco cuando la función \"Reproducción\" está activada", "threshold_disk_space": "Umbral de espacio en disco (MB)", "title": "Configuración del sistema"}, "table": {"default": "Predeterminado", "dense": "<PERSON><PERSON>", "fail": "No se pudo actualizar la configuración de la tabla", "success": "Se actualizó la configuración de la tabla", "table_row_height": "Alto de fila", "title": "Tabla"}, "tacacs": {"apply_fail": "No se pudo actualizar la configuración del servidor TACACS+", "apply_success": "La configuración del servidor TACACS+ se actualizó correctamente", "auth_type": "Tipo de autenticación", "auth_type_asc_two": "ASCII", "auth_type_chap": "CHAP", "auth_type_ms_chap": "MS-CHAP", "auth_type_pap": "PAP", "server_address": "Dirección del servidor", "share_key": "Compartir clave", "tcp_port": "Puerto TCP", "timeout": "Tiempo de espera", "title": "Servidor TACACS+"}, "title": "Preferencias", "topology_appearance": {"access_port": "Puerto de acceso", "background": "Fondo", "background_color": "Color de fondo", "directed_line_style": "Estilo de línea dirigida", "edit_igmp_visualization_color": "Editar el color de visualización de IGMP", "edit_traffic_load_color": "Editar el color de la carga de tráfico", "edit_vlan_visualization_color": "Editar el color de visualización de VLAN", "elbow_line_style": "Estilo de línea de codo", "fail": "No se pudo actualizar el aspecto de la topología", "hsr_ring": "Anillo HSR", "igmp_visualization": "Visualización de IGMP", "link_down": "Inactivo", "link_up": "Activo", "member": "Miembro", "poe": "PoE", "poe_color": "Color del enlace de PoE", "prp_lan_a": "LAN de PRP A", "prp_lan_b": "LAN de PRP B", "querier": "Solicitante", "rstp": "RSTP", "show_poe": "Mostrar el estado de PoE en la topología", "status_color": "Color de estado", "success": "Se actualizó el aspecto de la topología", "text_size": "Tamaño del texto", "text_size_large": "Grande", "text_size_medium": "Mediano", "text_size_small": "Pequeño", "title": "Topología", "topology_style": "Estilo de línea de topología", "traffic_load": "Carga de tráfico", "trunk_port": "Puerto troncal", "turbo_chain": "Turbo Chain", "turbo_ring_v1": "Turbo Ring V1", "turbo_ring_v2": "Turbo Ring V2", "vlan_visualization": "Visualización de VLAN"}, "user": "Usuario"}, "rogue_device_detection": {"add_device_for_rogue_device_detection": "Agregue este dispositivo a la línea base de dispositivos para la detección de dispositivos no autorizados", "add_device_to_baseline": "Agregar dispositivo a la línea base", "add_device_to_baseline_content": "¿Está seguro de que desea agregar este dispositivo a la línea base?", "add_devices_to_baseline_content": "¿Está seguro de que desea agregar estos dispositivos a la línea base?", "add_scan_device_for_rogue_device_detection": "Agregue los dispositivos escaneados a la línea base de dispositivos para la detección de dispositivos no autorizados", "clear_all_rogue_device_history": "Borrar el historial de dispositivos no autorizados", "clear_all_rogue_device_history_hint": "Se borrará todo el historial del dispositivo no autorizado. Estás seguro de que quieres continuar?", "connected_switch_port": "Interruptor/puerto conectado", "creation_time": "Creado en", "current_rogue_device": "Dispositivos no autorizados actuales", "delete_device_from_baseline": "Eliminar dispositivo de la línea base", "delete_device_from_baseline_content": "Este dispositivo se eliminará de la línea base y se agregará como dispositivo no autorizado.", "delete_devices_from_baseline_content": "Estos dispositivos se eliminarán de la línea base y se agregarán como dispositivos no autorizados.", "device_baseline": "Línea base del dispositivo", "device_baseline_content": "Esta operación creará una nueva línea base y sobrescribirá la existente.", "download_all_history_data_to_csv": "Exportar todos los datos del historial a CSV", "download_current_page_to_csv": "Exportar página actual a CSV", "first_seen": "visto por primera vez", "ip": "Dirección IP", "last_seen": "Ultima vez visto", "mac": "Dirección MAC", "must_create_a_baseline_first": "No existe ninguna línea base del dispositivo. Primero cree una línea de base.", "no_devices_can_add": "No se detectaron dispositivos. Primero agregue dispositivos a MXview One.", "port": "Puerto", "rogue_device_history": "Historial de dispositivos no autorizados", "rogue_device_settings": "Configuración de dispositivos no autorizados", "sequence_no": "Secuencia No.", "unknown": "Desconocido", "vendor": "Proveedor de tarjetas de red"}, "SCAN_RANGE": {"add_scan_range": "Agregar rango de detección", "button": {"back": "Regresar", "browse_topology": "Explorar topología", "cancel": "<PERSON><PERSON><PERSON>", "discovery": "Aceptar y ejecutar la detección", "next": "Siguient<PERSON>", "recover": "Recuperación", "scan_new_network": "Examinar una red nueva"}, "cidr_address_range": "Intervalo de direcciones CIDR", "duplicate_range": "El nuevo rango se superpone con los rangos existentes", "edit_scan_range": "Editar rango de detección", "firstIp_higher_lastIp": "El intervalo de IP no es válido (Primera IP > Última IP)", "subnet_mask": "Subnet Mask", "table_title": {"active": "Habilitar rango de detección", "background_scan": "Escaneo de fondo", "conflict_scan": "Detectar conflicto de IP", "edit": "<PERSON><PERSON>", "end_ip": "Última dirección IP", "group": "Grupo", "name": "Nombre", "site_name": "Nombre del sitio", "start_ip": "Primera dirección IP"}, "wizard": {"complete": "Completo", "complete_message": "Se agregaron {{discoveryDevices}} dispositivos a MXview One", "discovery_result": "Resultado de la detección", "network_range": "Intervalo(s) de red", "save_hint": "Los intervalos examinados se guardarán después de la detección de dispositivos", "title": "Detección de dispositivos"}}, "script_automation": {"add_a_script_automation": "Agregar una automatización de script", "add_script_button_hint": "El número máximo de automatizaciones de scripts es 200.", "add_script_first_hint": "No se encontraron automatizaciones de script. Vaya a la pantalla Automatización de script para agregar una automatización de script.", "add_script_sutomation": "Agregar automatización de scripts", "adjustable_buttons": "Reordenar botones", "affected_devices": "Dispositivos afectados", "affected_devices_info": "Esta acción afectará a {{ affectedDevices }} dispositivos.", "affected_devices_info_2": "Esta acción afectará a los siguientes dispositivos {{ affectedDevices }}", "align_buttons": "Alinear todos los grupos en una sola columna", "all_devices": "Todos los dispositivos", "automation_button": "Botones de automatización", "background_color": "Color de fondo", "button_name": "Nombre del botón", "button": {"panel": "Panel de botones", "privilege": "Se requiere autenticación de administrador", "state": {"hint": "Solo un botón de un grupo puede estar en el estado ' en '", "off": "De", "on": "En", "title": "Estado del botón"}, "style": "Estilo <PERSON>", "widget": "Widget de botón"}, "cli_id_duplicated": "Esta CLI ya ha sido seleccionada.", "cli_script_and_target_device": "Scripts CLI y dispositivos de destino", "cli_script_hint": "El número máximo de guiones es 50.", "color": "Color", "confirm_proceed": "Quieres proceder?", "create_new_group": "<PERSON><PERSON>r y agregar a un nuevo grupo", "delete_automation": "¿Está seguro de que desea eliminar esta automatización de script?", "delete_multiple_automation": "¿Está seguro de que desea eliminar estas automatizaciones de scripts?", "delete_script_automation": "Eliminar automatización de scripts", "description": "Descripción", "device_missing": "No se pudieron encontrar los siguientes dispositivos", "drag_button": "Arrastre un botón aquí para crear un nuevo grupo.", "edit_button": "<PERSON><PERSON><PERSON>", "edit_group": "Editar grupo", "edit_panel": "Editar el título del panel de botones", "edit_script_button": "Botón Editar automatización", "execute_button": "<PERSON><PERSON><PERSON>", "execute_button_hint": "Ejecutar automatización de scripts", "execute_button_info": "Espere a que finalice el proceso para ver los resultados. Si sale de esta pantalla, puede ir a Scripts CLI guardados > Resultados de ejecución para descargar los resultados.", "extra_large": "Extra grande", "group": "Grupo", "group_already_exist": "El nombre de este grupo ya existe", "group_name": "Nombre del grupo", "invalid_account": "Privilegio de cuenta no válido", "ip_duplicated": "Este dispositivo ya está seleccionado.", "large": "Grande", "last_executed": "Última ejecución", "leave_page_hint": "¿Estás seguro de que quieres salir de esta página?", "leave_without_saving": "<PERSON><PERSON> sin ahorrar", "medium": "Mediano", "more": "Más", "name": "Nombre", "not_change_group": "Usar el grupo actual", "not_saved_hint": "Cualquier cambio que haya realizado no se guardará.", "script_automation": "Automatización de guiones", "select_all": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "select_existing_group": "Pasar a otro grupo", "select_saved_cli_script": "Seleccione el script CLI guardado", "small": "Pequeño", "start_preview": "Iniciar vista previa en topología", "stop_preview": "Detener la vista previa en topología", "target_device": "Dispositivos de destino", "text_color": "Color de texto", "widget_size": "<PERSON><PERSON><PERSON> del widget"}, "SECURITY_ITEM": {"ACCESSIBLE_IP_LIST": "Activar el acceso de confianza", "ACCOUNT_LOCKOUT": "Habilitar el bloqueo por errores de inicio de sesión de la cuenta", "ACCOUNT_VALIDITY": "Validez de la política de cuentas y contraseñas", "AUTO_LOGOUT": "Activar el cierre de sesión automático", "AWK_SERIES": "Inalámbrico", "BROAD_CAST_STORM": "Activar la protección DDoS", "changed": "Modificado", "disabled": "Desactivado", "enabled": "Activado", "ENCRYPT_CONSOLE": "Desactivar los puertos TCP/UDP no cifrados", "ENCRYPTED_CONFIG": "Activar el cifrado de archivos de configuración", "HIGH_SECURE_MODE": "Modo de alta seguridad", "LOGIN_NOTIFICATION": "Configurar el mensaje de inicio de sesión", "MGATE": "<PERSON><PERSON><PERSON>", "non-changed": "No modificado", "not_set": "No establecido", "NPORT": "Servidor de terminales", "NPORT5000": "Ser<PERSON><PERSON> de dispositivos", "NTP_SERVER": "Configurar el cliente NTP", "PASSWORD_CHANGED": "Cambiar la contraseña predeterminada/cadena de la comunidad de SNMP", "PASSWORD_POLICY": "Activar la comprobación de la complejidad de la contraseña", "read_fail": "Error de lectura", "set": "Establecido", "SWITCH": "Interruptor", "SYSLOG": "<PERSON><PERSON><PERSON> servid<PERSON>", "TRAPSYSLOG": "Configurar el servidor de captura de SNMP/Inform o Syslog", "unknown": "Desconocido", "WEB_CERTIFICATE": "Importar certificado web"}, "SERIAL_PORT_MONITORING": {"all_ports": "Todos los puertos", "any_serial_error_count": "Cualquier recuento de errores en serie", "break_error_count": "Recuento de errores de interrupción", "copy_configuration_device": "Copiar configuración a dispositivos", "count_threshold": "Umbral de activación", "counts": "<PERSON><PERSON>(s)", "critical": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "error_status": "Advertencias del puerto serie", "event_condition_rule": "Reglas de activación de eventos", "frame_error_count": "Recuento de errores de trama", "hint_any": "Pruebe los pasos a continuación para resolver el siguiente problema: El puerto serie {{portnum}} ha excedido el umbral de conteo de errores de desbordamiento ({{overrun error count}}), errores de paridad ({{parity error count}}), errores de trama ({{frame error count}}) o errores de interrupción ({{break error count}}).", "hint_break": "Pruebe los pasos a continuación para resolver el siguiente problema: El puerto serie {{portnum}} ha excedido el umbral de recuento de errores de interrupción ({{count}}).\nUna señal de interrupción en serie indica una condición especial en el dispositivo serie conectado, como un problema de cableado, un mal funcionamiento del dispositivo, un reinicio del dispositivo o un proceso de sincronización.", "hint_frame": "Pruebe los pasos a continuación para resolver el siguiente problema: El puerto serie {{portnum}} ha excedido el umbral de conteo de errores de trama ({{count}}).\nCuando el dispositivo Moxa recibe datos en serie, verifica si el formato de trama coincide con los parámetros en serie. Si no coinciden, se considerará un error de trama.", "hint_general_1": "Si la solución sugerida no funcionó o tiene más preguntas, comuníquese con su", "hint_general_2": "primero.", "hint_general_3": "Contactar", "hint_general_4": "si necesita asistencia adicional.", "hint_overrun": "Pruebe los pasos a continuación para resolver el siguiente problema: El puerto serie {{portnum}} ha excedido el umbral de recuento de errores de desbordamiento ({{count}}).\nSi los dispositivos seriales conectados envían datos demasiado rápido para que el dispositivo Moxa los lea, perderá datos, lo que provocará un error de desbordamiento.", "hint_parity": "Pruebe los pasos a continuación para resolver el siguiente problema: El puerto serie {{portnum}} ha excedido el umbral de recuento de errores de paridad ({{count}}).\nUn error de paridad indica que el carácter de datos recibido no coincide con la paridad configurada.", "hint_rx": "Pruebe los pasos a continuación para resolver el siguiente problema: El puerto serie {{portnum}} no ha recibido ningún dato en los últimos {{min}} minuto(s).", "hint_rxtx": "Pruebe los pasos a continuación para resolver el siguiente problema: El puerto serie {{portnum}} no ha transmitido ni recibido ningún dato en los últimos {{min}} minuto(s).", "hint_tx": "Pruebe los pasos a continuación para resolver el siguiente problema: El puerto serie {{portnum}} no ha transmitido ningún dato en los últimos {{min}} minuto(s).", "how_to_resolve": "¿Cómo resolverlo?", "minutes": "<PERSON><PERSON><PERSON>", "no_data_period": "Sin periodo de datos", "overrun__error_count": "Recuento de errores de desbordamiento", "parity__error_count": "Recuento de errores de paridad", "port_duplicated": "Esta combinación de regla de puerto/activador ya está configurada.", "port_properties": "Tipo de evento", "resolve_title": "Resolver problema en el puerto serial {{portnum}}", "rx": "Inactividad de RX", "serial_port": "Puerto serial", "severity": "Gravedad", "step1_break": "Compruebe si los dispositivos seriales conectados funcionan correctamente.", "step1_frame": "Compruebe si la configuración de la interfaz serial (RS-232, RS-422, RS485) y los parámetros de comunicación (por ejemplo, 115200, 8, n, 1) del dispositivo Moxa coinciden con los de los dispositivos seriales conectados.", "step1_ovrrun": "Verifique si el control de flujo de hardware y/o software serial está configurado correctamente tanto en el dispositivo Moxa como en los dispositivos seriales conectados", "step1_parity": "Verifique si las configuraciones de paridad y velocidad en baudios en el dispositivo Moxa y los dispositivos seriales conectados coinciden.", "step1_txrx": "Verifique si el cable serial entre el dispositivo Moxa y los dispositivos finales seriales está conectado correctamente.", "step2_ovrrun": "Verifique si es necesario habilitar el FIFO en el dispositivo Moxa.", "step2_parity": "En áreas con alta interferencia, verifique si el sistema de comunicación en serie está adecuadamente protegido.", "step2_txrx": "Compruebe si los dispositivos seriales conectados funcionan correctamente.", "step3_parity": "Verifique los dispositivos seriales conectados para detectar problemas de cableado o fallas de hardware.", "still_not_working": "¿Sigue sin funcionar?", "title": "Eventos del puerto serie", "tx": "Inactividad de TX", "tx_rx": "Inactividad de TX y RX", "warning": "Advertencia"}, "SEVERITY": {"critical": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "information": "Información", "title": "Gravedad", "warning": "Advertencia"}, "sfpList": {"rx": "Recepción (dBm)", "temperature": "Temp. (°C)", "title": "Lista de SFP", "tx": "Transmisión (dBm)", "voltage": "Voltios (V)"}, "SITE_MANAGEMENT": {"desc": "Descripción", "fail": "No se pudo actualizar la información del sitio", "name": "Nombre", "offline": "Sitio sin conexión ({{siteName}})", "online": "Sitio en línea ({{siteName}})", "success": "Se actualizó la información del sitio", "title": "Gestión de sitios"}, "SITE_MENU": {"management": "Gestión"}, "SITE_PROPERTIES": {"description": "Descripción", "devices": "Dispositivos (normal/advertencia/crítico)", "information": "Información", "name": "Nombre", "title": "Propiedades del sitio"}, "SNACK_BAR": {"acking": "Reconociendo...", "copied": "Copiado", "deleting": "Borrando...", "saving": "Guardando..."}, "SYSLOG_SETTINGS": {"all_severity": "Todos", "authentication": "Autenticación", "enable_syslog_forward": "Reenvío de Syslog", "enable_tcp": "<PERSON><PERSON><PERSON><PERSON> (solo TCP)", "enable_udp": "<PERSON><PERSON><PERSON><PERSON> (solo UDP)", "enable_udp_tcp": "Habilitar (UDP y TCP)", "failed_to_get_syslog_forward_settings": "No se pudo obtener la configuración de reenvío de syslog", "failed_to_get_syslog_settings": "No se pudo obtener la configuración de syslog", "filter_settings_hint": "Puede ingresar varias direcciones IP de origen, separadas por una coma.", "forward_ip1": "IP remota/nombre de dominio 1", "forward_ip2": "IP remota/nombre de dominio 2", "port_1": "Puerto 1", "port_2": "Puerto 2", "protocol": "Protocolo", "source_ip": "IP de origen", "syslog_built_in": "Servidor Syslog integrado", "syslog_filter_settings": "Filtros de registro del sistema", "syslog_forwarding": "Reenvío de Syslog", "syslog_server_settings": "Configuración del servidor Syslog", "tcp": "TCP", "tcp_port": "Puerto TCP", "title": "Configuración de registro del sistema", "tls_cert": "Certificado TLS +", "tls_only": "Solo TLS", "udp": "UDP", "udp_port": "El puerto UDP", "update_failed": "No se pudo actualizar la configuración", "update_success": "Configuración actualizada exitosamente"}, "SYSLOG_VIEWER": {"device_ip": "Dirección IP", "facility": "Instalación", "filter_syslog_event": "Escriba para eventos de Syslog", "ip_error": "Ingrese una dirección IP válida", "message": "Men<PERSON><PERSON>", "priority": {"equals": "Igual que", "high_than": "Mayor o igual que", "lower_than": "<PERSON>or o igual que", "title": "Prioridad"}, "severity": {"alert": "<PERSON><PERSON><PERSON>", "critical": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "debug": "<PERSON><PERSON><PERSON>", "emergency": "Emergencia", "error": "Error", "information": "Información", "notice": "Aviso", "title": "Gravedad", "warning": "Advertencia"}, "site_name": "Nombre del sitio", "timestamp": "Marca de tiempo", "title": "V<PERSON>or de Syslog"}, "SYSTEM": {"request_timeout_title": "Se agotó el tiempo de espera de la solicitud", "trigger_disconnected_desc1": "Desconectado del servidor de MXview One.", "trigger_disconnected_desc2": "Se volverá a conectar en cinco segundos...", "unauthorized_desc": "Acceso denegado debido a credenciales no válidas.", "unauthorized_title": "No autorizado"}, "TABLE": {"add": "Agregar", "adjustable_columns": "Columnas Ajustables", "compare": "Comparar", "delete": "Eliminar", "edit": "<PERSON><PERSON>", "edit_columns": "Editar columnas", "enable": "Activado/desactivado", "export": "Exportar", "exporting": "Exportando ahora...", "filter": "Filtrar", "import": "Importar", "limit_count": "Máx.", "list_collaspe": "<PERSON><PERSON><PERSON>", "list_expand": "Ampliar para obtener más información", "locate": "Localizar", "no_data": "No hay datos para mostrar", "not_support": "La versión del firmware del dispositivo no es compatible", "save": "Guardar", "search": "Buscar", "selected_count": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "show_log": "Mostrar registro", "sync": "Sincronizar", "total": "Total", "waiting_data": "<PERSON><PERSON><PERSON><PERSON>"}, "TOPOLOGY": {"add_tag": "Añadir etiqueta...", "add_tag_fail": "No se pudo agregar la etiqueta", "add_tag_success": "La etiqueta se agregó correctamente", "choose_goose_publisher": "Elija un editor de GOOSE", "colored_link": "Enlaces coloreados según la intensidad de la señal", "delete_tag_failed": "No se pudo eliminar la etiqueta", "delete_tag_success": "La etiqueta se eliminó correctamente", "device_not_found": "No se encontró el dispositivo", "display_opt": "Opciones de visualización", "dynamic_wireless_client_position": "Posición dinámica del cliente", "dynamic_wireless_client_position_desc": "Habilite esta opción para mostrar cada cliente junto al PA al que está conectado actualmente", "editTagTooltip": "Pulse Enter para guardar.\nPulse la tecla ESC para cancelar.", "goose": "GOOSE", "goose_publisher": "Editor de GOOSE", "goose_tampered": "GOOSE alterado", "goose_timeout": "Tiempo de espera de GOOSE", "grouping_failed": "No se pudo agrupar", "grouping_success": "Agrupar con éxito", "legend": "Leyenda", "new_tag": "Etiqueta nueva", "no_subscriber": "No hay suscriptores", "prp_hsr": "PRP/HSR", "prp_hsr_tags": "Etiquetas de PRP/HSR", "publisher": "Editor", "publisher_hint": "Nombre del bloque de control de GOOSE\nAPPID/dirección", "search_topology": "Buscar topología", "set_tag_fail": "No se pudo establecer la etiqueta", "set_tag_success": "La etiqueta se estableció correctamente", "show_all_wireless_clients": "Mostrar clientes", "site_management_not_supported": "Especifique un sitio para utilizar la función de reproducción del historial de roaming", "subscriber": "Suscriptor", "subscriber_hint": "Nombre del IED/nombre del bloque de control de GOOSE", "tag": "Etiqueta", "traffic_view": "Carga de tráfico (%)", "ungrouping_failed": "No se pudo desagrupar", "ungrouping_success": "Desagrupar con éxito", "wireless_display_opt": "Opciones de visualización inalámbrica", "zoom_in": "Ampliar", "zoom_out": "Reducir", "zoom_to_actual_size": "Ajustar al tamaño predeterminado", "zoom_to_fit": "A<PERSON><PERSON> al tamaño"}, "TRAP_CONFIGURATION": {"apply_fail": "No se pudo actualizar la configuración del servidor de captura del dispositivo", "apply_success": "La configuración del servidor de captura del dispositivo se actualizó correctamente", "community_name1": "Nombre de la comunidad 1", "community_name2": "Nombre de la comunidad 2", "destination_ip1": "IP de destino 1", "destination_ip2": "IP de destino 2", "title": "Ser<PERSON><PERSON> de captura"}, "UNIT": {"dB": "dB", "dBi": "dBi", "dBm": "dBm", "hours": "h", "mb": "Mb", "mbps": "Mbps", "meter": "m", "min": "min", "sec": "s", "times": "veces"}, "UPGRADE_FIRMWARE": {"file_type_error": "Los archivos de firmware deben estar en formato de archivo .rom, .tar y .gz.", "upgrade_firmware_fail": "No se pudo actualizar el firmware", "upgrade_firmware_success": "El firmware se actualizó correctamente", "upgrading": "Descargando el archivo de firmware; this operation may take several minutes. Do not turn the power off or disconnect from the network until the process is completed"}, "validators": {"duplicateEmail": "Hay correos electrónicos duplicados", "excludeLastPassword": "La nueva contraseña no puede ser la misma que la última contraseña", "excludeUserName": "No se puede incluir el nombre de usuario", "invalid": "Caracteres no válidos", "invalid_date": "<PERSON><PERSON>", "invalid_format_allow_space": "Solo se permite un máximo de {{count}} espacios en la cadena", "invalidEmail": "Correo electrónico es inválida", "invalidIpAddress": "Dirección IP inválida", "invalidIpAddressOrDomainName": "Dirección IP o nombre de dominio no válido", "invalidLocation": "Permiso especial (-_@!#$%^&*().,/)", "invalidMacAddress": "Dirección MAC no válida", "invalidMacAddressAllZero": "La dirección MAC 00:00:00:00:00:00 está reservada", "invalidSeverity": "Gravedad no válida", "ipRangeError": "Dirección IP: Final debe ser mayor que Dirección IP: Inicio", "isExist": "ya existe", "isExistOrUsedByOtherUser": "Ya existe o es usado por otro usuario", "maxReceiverSize": "El número MÁXIMO de receptores es {{num}}.", "needDigit": "<PERSON>be incluir al menos un dígito (0 - 9)", "needGreaterThan": "{{ largeItem }} debe ser mayor que {{ smallItem }}", "needLowerCase": "Debe incluir al menos un carácter en minúscula (a - z)", "needSpecialCharacter": "Debe incluir al menos un carácter especial (~!@#$%^&amp;*_-+=`|\\(){}[]:;”&#39;&lt;&gt;,.?/)", "needUpperCase": "Debe incluir al menos un carácter en mayúscula (A - Z)", "notMeetPolicy": "No cumple con los requisitos de la política de contraseñas", "portRangeError": "Puerto: <PERSON> debe ser mayor que Puerto: Inicio", "pwdNotMatch": "La contraseña no coincide", "range": "Rango no válido ({{ min }} ~ {{ max }})", "required": "Necesario", "requireMaxLength": "No debe tener más de {{ número }} caracteres", "requireMinLength": "Debe tener al menos {{ númer<PERSON> }} caracteres"}, "Validators": {"require_range_between": "{{ rangeBegin }} - {{ rangeEnd }}"}, "VLAN_TABLE": {"access_ports": "Puertos de acceso", "device_ip": "IP del dispositivo", "empty": "Vacío", "export_csv": "Exportar CSV", "filter_vlan": "Escriba para filtrar VLAN de dispositivos", "hybrid_ports": "Puertos híbridos", "location": "Ubicación", "management_vlan": "VLAN de gestión", "model": "<PERSON><PERSON>", "no": "No", "site_name": "Nombre del sitio", "title": "VLAN", "trunk_ports": "Puertos troncales", "vlan_id": "ID de VLAN", "yes": "Sí"}, "wirelessPlayback": {"decreaseSpeed": "Reducir la velocidad", "increaseSpeed": "Aumentar la velocidad", "noData": "No hay datos en el intervalo de fechas seleccionado", "range": "Duración", "startTime": "Hora de inicio", "timeRange": "Intervalo de tiempo"}}