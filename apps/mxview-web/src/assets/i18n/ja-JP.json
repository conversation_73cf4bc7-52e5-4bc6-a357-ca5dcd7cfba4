{"ABOUT": {"debug_log": "デバッグログ", "debug_log_desc": "ダウンロードボタンをクリックすると、デバッグログが生成され、ダウンロードされます。ダウンロードしたログファイルをMOXAサポートチャネルに送信し、さらに分析することができます。", "desc": "Copyright Moxa, Inc. All Rights Reserved.", "eula": "View the end user license agreement (EULA)", "gateway": "Gatewayのバージョン", "title": "概要", "web": "Webのバージョン"}, "ACCOUNT_MANAGEMENT": {"access_site": "アクセス可能なサイト", "add_account_dialog": "ユーザーアカウントの追加", "add_user_fail": "新しいアカウントを作成できません", "add_user_success": "新しいアカウントが作成されました", "admin": "スーパーバイザー", "all_user": "すべてのユーザー", "authority": "権限", "change_password": "パスワードの変更", "delete_user_fail": "アカウントを削除できません", "delete_user_success": "ユーザーアカウントが削除されました", "demo_user": "デモユーザー", "filter_account": "タイプしてユーザーアカウントをフィルターする", "modify_account_dialog": "ユーザーアカウントの変更", "new_password": "新しいパスワード", "old_password": "古いパスワード", "password": "パスワード", "password_policy_mismatch": "パスワードがパスワードポリシーに準拠していません", "superuser": "管理者", "title": "アカウントの管理", "ui_profile": "UIプロファイル", "update_user_fail": "アカウントを更新できません", "update_user_success": "ユーザーアカウントが更新されました", "user": "ユーザー", "user_account": "ユーザーアカウント", "user_exist": "ユーザがすでに存在している", "username": "ユーザー名"}, "account_password": {"1st_email": "最初のメール受信者", "2nd_email": "2番目のメール受信者", "account_audit": "アカウント監査", "account_info": "口座情報", "account_info_content": "すべてのデバイスのアカウント情報を取得するには、 '更新'ボタンをクリックしてください。これには時間がかかる場合があります。", "account_management": "アカウント管理", "account_password_management_automation": "アカウントとパスワード管理の自動化", "account_status_audit": "会計監査", "account_status_baseline": "アカウントベースライン", "account_status_baseline_content": "この操作により、新しいベースラインが作成され、既存のベースラインが上書きされます。", "accounts": "アカウント", "activate": "アクティブ化", "add_account": "アカウントを追加する", "add_temporary_account": "一時アカウントを追加", "added_account": "追加されたアカウント", "admin": "管理者", "apply_accounts": "対象アカウント", "audit_automation": "監査の自動化", "authority": "権限", "baseline_account": "ベースラインアカウント", "baseline_auto_check_failed": "ベースラインの作成に失敗しました", "change_admin_name": "デフォルトの「管理者」名を変更する", "change_admin_name_content": "MXview One は更新されたアカウント資格情報を使用して次のデバイスにアクセスします。他のデバイスは影響を受けません。", "change_admin_name_contents": "MXview One は更新されたアカウント資格情報を使用して次のデバイスにアクセスします。他のデバイスは影響を受けません。", "check_default_account_failed": "デフォルトアカウントの確認に失敗しました。", "check_password_length": "パスワードの長さを、デバイスで許可されている最大パスワード長内にしてください。", "compability": "互換性がある", "create_baseline_failed": "ベースラインの作成に失敗しました", "create_baseline_failed_no_devices": "ベースラインの作成に失敗しました。デバイスが検出されませんでした。", "days": "日", "default_account": "デフォルトのユーザー名/パスワード", "default_password_audit": "デフォルトパスワード監査", "default_password_audit_info": "デフォルトのアカウント資格情報のスキャンには時間がかかる場合があり、インターフェイスが一時的に使用できなくなります。続行してもよろしいですか?", "delete_account": "アカウントの削除", "delete_temporary_account": "一時アカウントを削除する", "delete_temporary_account_info": "この一時アカウントを削除してもよろしいですか?", "delete_temporary_accounts_info": "これらの一時アカウントを削除してもよろしいですか?", "deleted_account": "削除されたアカウント", "device_alias": "デバイスのエイリアス", "device_ip": "デバイスIP", "edit_account": "アカウント編集", "edit_temporary_account": "一時アカウントを編集", "email_server_configuration": "メールサーバー設定", "email_server_hint": "認証コードが記載されたメールが届かない場合は、迷惑メールフォルダを確認するか、", "email_verified": "メールアドレスが確認されました。", "end_date": "まで有効", "end_time": "終了時間", "fatiled_to_audit_account_due": "アカウント監査を完了できませんでした。{{ ip }} からデバイス アカウント情報を取得できません。", "fatiled_to_create_baseline_due": "ベースラインの作成に失敗しました。{{ ip }} からデバイス アカウント情報を取得できません。", "get_baseline_failed": "ベースラインを取得できませんでした", "get_device_account_failed": "デバイス アカウント情報の照会に失敗しました。他のリクエストが進行中です。しばらくしてからもう一度お試しください。", "incorrect_verification_code": "確認コードが正しくありません。", "last_audit_time": "最終監査日", "last_execution_time": "最終実行", "max_char": "最大 {{num}} 文字", "model": "モデル", "mxa_char": "最大 {{num}} 文字", "new_password": "新しいパスワード", "new_username": "新しいユーザーネーム", "next_audit_time": "次回の監査", "next_schdeule_start_time": "次回予定", "no_data": "該当なし", "not_activate": "無効にする", "not_started": "保留中", "not_started_hint": "このタスクはシステムのシャットダウンにより実行されませんでした。タスクを手動で実行するには、「パスワードの再生成」ボタンをクリックしてください。", "now": "即時", "operation": "アクション", "password": "パスワード", "password_automation": "パスワード自動化", "password_automation_schedule": "パスワード自動化スケジュール", "password_automation_settings": "パスワード自動化ウィザード", "password_email_receiver": "パスワードメール受信者", "password_regenerated_info": "MXview One は次の設定を使用して各デバイスの新しいパスワードを生成します。", "password_resend_info": "次の受信者にデバイスのパスワードを再送信してもよろしいですか?", "password_resend_result_info": "MXview One はデバイス アカウントとパスワード ファイルを次の電子メール アドレスに送信しました:", "password_strength": "パスワードの強度", "random_password_complexity": "パスワードの複雑さを設定する", "random_password_length": "ランダムなパスワードの長さ", "random_password_length_info": "MXview One は選択したデバイスに対してランダムなパスワードを生成します。", "randomized_password_failed": "失敗しました (デバイス アカウントがデータベース アカウントと一致しません)。", "refresh_hint": "この操作を続行する前に、 '更新'ボタンを押してデバイス アカウントを取得してください。", "regenerate_password": "パスワードを再生成", "resend_password_email": "パスワードメールを再送信", "retrieve_data": "データの取得", "retry_failed_devices": "Retry Failed Devices", "schedule": "予定", "schedule_interval": "間隔", "script_error": "このフィールドには、次の文字を含めることはできません: #%&amp;*{}|:\\&quot;&lt;&gt;?/\\\\", "select_device": "デバイスの選択", "select_device_random_password": "ランダムなパスワードを生成するデバイスを選択します。", "send_password_email": "パスワードメールを送信", "send_password_email_success": "MXview One は、デバイス アカウント、パスワード、および実行結果を次の電子メール受信者に送信しました:", "set_password_to_device": "デバイスにパスワードを適用する", "set_schedule_interval_failed": "スケジュール間隔の設定に失敗しました。", "set_schedule_interval_success": "スケジュール間隔が正常に設定されました。", "start_date": "アクティブ開始日", "start_over": "やり直す", "start_time": "始まる時間", "start_wizard": "ウィザードを開始", "status": {"cli_session_timeout": "CLIセッションがタイムアウトしました", "failed": "失敗しました", "failed_account_exist": "失敗しました（このアカウントは既に存在します）", "failed_account_password_incorrect": "失敗しました（アカウントまたはパスワードが正しくありません）", "failed_limit_reached": "失敗しました（デバイス アカウントの制限に達しました）", "failed_not_support_role": "失敗しました（デバイスがサポートされていません）", "failed_other_request": "失敗しました（他のリクエストが進行中です）", "failed_retrieve_account_info": "失敗しました（アカウント情報を取得できません）", "finished": "完了しました", "in_progress": "進行中 ...", "waiting": "待っている"}, "supervisor": "スーパーバイザー", "temporary_account": "一時アカウント", "test_eamil_recipient": "テストメール受信者", "title": "アカウントとパスワード", "unable_to_get_accounts": "アカウントを取得できません", "user": "ユーザー", "username": "ユーザー名", "verififcation_code": "検証コード", "verift_title": "MXview One アカウントを確認する", "verify_code_expiration": "認証コードの有効期限は", "verify_email_not_allowed": "別のメールを送信する前に、少なくとも 1 分お待ちください。", "verify_email_password_receiver": "アカウントとパスワードのメール受信者を確認する", "verify_email_receiver": "メール受信者の確認", "verify_email_server_failed": "電子メール サーバーの構成が無効です。電子メールを送信できません。", "verify_user_failed": "ユーザー名/パスワードが無効です"}, "ADD_DEVICE": {"add_device_fail": "デバイスを追加できません", "add_device_fail_error_message": {"device_has_existed": "この IP を持つデバイスはすでに存在します", "license_limitation_reached": "ライセンス制限に達しました", "model_not_exist": "モデルは存在しません。"}, "add_device_success": "デバイスを正常に追加", "assign_group": "グループへの割り当て", "assign_model": "モデルの割り当て", "authentication": "認証", "auto_detect_model": "自動検出", "data_encryption": "データ暗号化", "encryption_password": "暗号化パスワード", "encryption_type": "暗号化プロトコル", "field_required": "このフィールドは必須です", "snmp_setting": "SNMP設定", "snmp_version": "SNMPバージョン", "title": "デバイスの追加"}, "ADD_LINK": {"alias": "エイリアス", "device": "デバイス", "fail": "リンクを追加できません", "from": "リンク元", "ip_address": "IPアドレス", "model": "モデル", "only_number": "数字のみ許可されています", "port": "ポート", "success": "リンクを正常に追加", "title": "リンクの追加", "to": "リンク先"}, "API_MANAGEMENT": {"access_count": "アクセスカウント", "add_failed": "API鍵の追加に失敗しました", "add_success": "API鍵が追加されました", "add_title": "新しいトークンの追加", "api_key": "API鍵", "application_name": "アプリケーション名", "create_time": "時間の作成", "delete_failed": "API鍵の削除に失敗しました", "delete_success": "API鍵が削除されました", "edit_title": "トークンの編集", "filter": "タイプしてAPI鍵をフィルターする", "regenerate_api_key": "API鍵の再生成", "regenerate_failed": "API鍵の再生成に失敗しました", "regenerate_success": "API鍵が再生成されました", "title": "API鍵管理", "update_failed": "API鍵の更新に失敗しました", "update_success": "API鍵が更新されました"}, "ASSIGN_MODEL": {"apply_to_all": "同じモデルを持つすべてのデバイスにこのアイコンを適用", "assign_model_fail": "モデルを割り当てできません", "assign_model_success": "デバイスモデルが更新されました", "ip_address": "IPアドレス", "model": "モデル", "model_icon": "モデルアイコン", "select_model": "モデルの選択"}, "AVAILABILITY_REPORT": {"alias": "デバイスのエイリアス", "average": "平均の可用性", "days": "日", "end_date": "終了日", "filter": "可用性レポートをフィルターするタイプ", "from_date": "開始日", "query_date": "クエリ日", "report_generate_day": "レポート生成日：", "site_name": "サイト名", "title": "可用性レポート", "worst": "最低の可用性"}, "BASIC_INFORMATION": {"apply_fail": "デバイスシステム情報を設定できません", "apply_success": "デバイスシステム情報が更新されました", "contact": "連絡先", "location": "場所", "model": "モデル", "name": "名前", "title": "基本情報"}, "BUTTON": {"add": "追加", "add_to_scheduler": "スケジューラに追加", "agree": "同意", "apply": "適用", "audit": "監査", "back": "戻る", "cancel": "キャンセル", "change": "変化", "check": "小切手", "checkNow": "今すぐチェック", "clear": "消去", "clear_fail_record": "失敗記録を消去", "close": "閉じる", "compare": "比較", "confirm": "確認", "connected": "接続済み", "continue": "続行", "copy": "コピー", "create": "作成", "deactivate": "非アクティブ化", "decline": "却下", "delete": "削除", "disable_new_version_notifications": "新しいバージョンの通知を無効にする", "disconnected": "切断されました", "download": "ダウンロード", "download_all_logs": "すべてのログをダウンロード", "download_filter_logs": "フィルターログをダウンロード", "edit": "編集", "enable_new_version_notifications": "新しいバージョンの通知を有効にする", "execute": "走る", "faqs": "FAQ", "got_it": "了解", "ignore": "無視する", "leave": "残します", "next": "次へ", "ok": "OK", "query": "<PERSON><PERSON>リ", "reboot": "再起動", "redirect": "リダイレクト", "refresh": "更新", "regenerate": "再生", "resend": "再送", "reset": "リセット", "retry_failed_devices": "失敗したデバイスの再試行", "run": "走る", "save": "保存", "scan": "スキャン", "search": "検索", "security_patch_available": "利用可能なセキュリティパッチ", "select": "選択する", "select_firmware_version": "ファームウェアのバージョンを選択", "send": "送信", "send_test_eamil": "テストメールを送信する", "set": "設定", "upgrade": "アップグレード", "upgrade_firmware": "ファームウェアのアップグレード", "upload": "アップロードする", "verify": "確認", "verify_email": "Eメールを確認します"}, "cli_object_database": {"add_cli_fail": "CLI スクリプトを作成できません", "add_cli_object": "CLIスクリプトの追加", "add_cli_success": "新しい CLI スクリプトが正常に作成されました", "before_date": "日付", "before_time": "時間", "cli_objects": "CLI スクリプト", "cli_script": "CLIスクリプト", "delete_all_execution_results": "CLI スクリプトの実行結果をすべて削除", "delete_all_execution_results_before_time": "前にスクリプト実行結果を削除", "delete_all_execution_results_before_time_desc": "{{param}} より前のすべてのスクリプト実行結果を削除してもよろしいですか?", "delete_all_execution_results_desc": "すべての CLI スクリプトの実行結果を削除してもよろしいですか?", "delete_cli_fail": "CLI スクリプトを削除できません", "delete_cli_object": "CLIスクリプトの削除", "delete_cli_object_desc": "この CLI スクリプトを削除してもよろしいですか?", "delete_cli_object_disabled": "このスクリプトはスケジュールされたタスクにリンクされているため、削除できません。 このスクリプトを削除するには、まず「管理 > メンテナンス スケジューラ」ページでスケジュールされたタスクを変更または削除します。", "delete_cli_objects_desc": "これらの CLI スクリプトを削除してもよろしいですか?", "delete_cli_success": "CLI スクリプトが正常に削除されました", "delete_execution_result_fail": "実行結果を削除できません", "delete_execution_result_success": "スクリプト実行結果は正常に削除されました", "delete_results_before_time": "CLIスクリプトの実行結果の削除", "description": "説明", "download_all_execution_results": "すべての実行結果をダウンロード", "download_all_execution_results_fail": "実行結果をダウンロードできません", "download_execution_results_failed_hint": "ダウンロードできる実行結果はありません。", "edit_cli_fail": "CLI スクリプトを更新できません", "edit_cli_object": "CLIスクリプトの編集", "edit_cli_success": "CLI スクリプトが正常に更新されました", "execution_results": "実行結果", "get_cli_fail": "CLI スクリプトを取得できません", "linked_scheduled_task": "リンクされたスケジュールされたタスク", "linked_script_automation": "リンクされたスクリプトの自動化", "name": "名前", "non_ascii": "ASCII 文字のみが受け入れられます。", "scheduled_execution_cli_object_desc": "[管理] > [メンテナンス スケジューラ] ページから、指定した日時に CLI スクリプトを実行するスケジュールされたタスクを作成できます。", "scheduled_execution_cli_object_info": "スケジュールされたスクリプト", "scheduled_task": "スケジュールされたタスク", "title": "保存された CLI スクリプト"}, "COMBO_BOX": {"disabled": "無効化", "enabled": "有効化", "export_all_event_csv": "すべてのイベントをCSVにエクスポート", "export_all_syslog_csv": "すべてのsyslogをCSVにエクスポート", "export_csv": "CSVのエクスポート", "export_pdf": "PDFのエクスポート", "sequential": "厳格な命令", "smart_concurrent": "スマートシーケンス"}, "COMMAND_BAR": {"hide_automation_button": "自動化ボタンを非表示にする", "hide_button_panel": "ボタンパネルを非表示", "hide_detail": "詳細の非表示", "hide_group": "グループを非表示にする", "list_view": "リストビュー", "show_automation_button": "自動化ボタンを表示", "show_button_panel": "表示ボタンパネル", "show_detail": "詳細の表示", "show_group": "グループを表示する", "topology_view": "トポロジービュー"}, "CONFIG_CENTER": {"alias_name": "エイリアス名", "backup_config": "バックアップ設定", "backup_message": "MXview Oneはこれらの設定ファイルをアーカイブします", "backup_tab": "バックアップ", "backup_tab_hint": "まず[バックアップ]タブに移動し、デバイス設定をエクスポートしてください", "compare_config_basement": "ベースメントの比較： {{compareConfigFileName}}", "compare_config_dialog_title": "設定の比較", "compare_tab": "記録", "compare_target": "ターゲットの比較", "configuration_file": "設定ファイル", "configuration_name": "設定名", "create_time": "時間の作成中", "delete_config_dialog_title": "設定の削除", "delete_config_failed": "デバイス設定の削除に失敗しました", "delete_config_success": "デバイス設定が削除されました", "delete_config_warning_message": "選択した設定を削除してもよろしいですか", "device_list": "デバイスリスト", "export_failed": "エクスポートに失敗しました", "export_success": "エクスポートに成功しました", "from_date": "開始日", "group_name": "グループ", "ip_address": "IPアドレス", "last_check_time": "最終確認時間", "local_file": "ローカルファイル", "restore_config": "リストア設定", "restore_device": "デバイスのリストア - {{selectedDeviceIP}}", "restore_tab": "リストア", "site_name": "サイト", "time": "時間", "title": "デバイス設定センター", "to_date": "終了日"}, "DASHBOARD": {"adpDestIp": "宛先 IP 別 ADP ポリシー イベント上位 5 件", "adpSrcIp": "ソース IP 別 ADP ポリシー イベント上位 5 件", "ap_devices": "アクセスポイント(AP) デバイス", "ap_traffic_load": "AP ネットワーク負荷", "baseline": "基本", "client_devices": "クライアントデバイス", "critial_devices": "危機的デバイス数", "device_availability": "デバイス可用性", "device_availability_intro": "可用性 = (アップタイム / (アップタイム + ダウンタイム)) * 100", "device_summary": "デバイスの概要", "devices": "デバイス", "disk_space_utilization": "ディスク容量使用状況", "dpiDestIp": "宛先 IP による上位 5 つのプロトコル フィルター ポリシー イベント", "dpiSrcIp": "送信元 IP 別上位 5 つのプロトコル フィルター ポリシー イベント", "event_highlight": "イベントのハイライト", "healthy_devices": "健全なデバイス数", "icmp_unreachable": "ICMP到達不能", "iec_level_1": "中", "iec_level_2": "高", "ipsDestIp": "宛先 IP 別 IPS ポリシー イベント上位 5 件", "ipsSrcIp": "ソース IP 別 IPS ポリシー イベント上位 5 件", "l3DestIp": "宛先 IP 別上位 5 つのレイヤー 3-7 ポリシー イベント", "l3SrcIp": "送信元 IP 別上位 5 つのレイヤー 3-7 ポリシー イベント", "last_1_day": "過去１日間", "last_1_hours": "過去１時間", "last_1_weeks": "過去１週間", "last_2_weeks": "過去２週間", "last_24_hours": "過去２４時間", "last_3_days": "過去３日間", "last_3_hours": "過去３時間", "last_30_days": "過去３０日間", "last_30_minutes": "過去３０分間", "last_7_days": "過去７日間", "last_update": "最終更新：", "link_down": "リンクダウン", "link_up": "リンクアップ", "linkButton": "イベントログを表示", "not_pass": "不合格", "now": "今から", "open": "開く", "pass": "合格", "reboot_times": "コールド/ウォームスタートトラップ", "refresh_all": "すべて更新", "security_level": "セキュリティレベル", "security_summary": "セキュリティの概要", "selecting_visible_item": "見えるアイテムの選択", "set_default_tab": "デフォルトタブとして設定", "tabs": {"cybersecurity": "サイバーセキュリティ", "general": "一般的な", "wireless": "ワイヤレス"}, "title": "ダッシュボード", "total_availability": "Device availability is below {{param}}%", "total_devices": "合計デバイス数", "unknown": "不明", "view_network_topology": "ネットワークトポロジーの表示", "warning_devices": "警告デバイス数", "wireless_device_summary": "ワイヤレスデバイス一覧"}, "DATABASE_BACKUP": {"database_name": "名前", "fail": "データベースのバックアップに失敗しました", "success": "データベースは{{param1}}でバックアップされました ", "title": "データベースのバックアップ"}, "DEVICE_LOCATOR": {"stop_trigger_locator": "停止", "title": "デバイスロケータ", "trigger_locator": "開始", "trigger_locator_fail": "デバイスロケータのトリガーに失敗しました", "trigger_locator_off": "デバイスロケータがオフ", "trigger_locator_on": "デバイスロケータがオン"}, "device_management": {"built_in_command_execution_process_is_running": "コマンドを送信できません。 別のコマンドが実行中です。 あとでもう一度試してみてください。", "execute_fail": "実行に失敗しました", "limited_support": "サポートには制限があるため、ユーザーマニュアルをご確認ください。", "select_device": "デバイスを選択", "select_operation": "操作を選択"}, "DEVICE_PANEL": {"panel_status": "パネルステータス", "panel_zoom_size": "デバイスパネルのズームサイズ", "port": "ポート"}, "DEVICE_POLLING_SETTING": {"consecutive_icmp_fail_trigger": "ICMP到達不能イベントをトリガーする連続的な失敗", "consecutive_snmp_fail_trigger": "SNMP到達不能イベントをトリガーする連続的な失敗", "icmp_polling_interval": "ICMPポーリング間隔", "snmp_polling_interval": "SNMPポーリング間隔", "title": "ポーリング設定"}, "DEVICE_PROPERTIES": {"basic_property": {"alias": "エイリアス", "availability": "可用性", "bios_version": "BIOS/ブートローダーのバージョン", "cpu_loading": "CPUロード (%)", "cpu_loading_30_seconds": "CPUロード30秒 (%)", "cpu_loading_300_seconds": "CPUロード300秒 (%)", "cpu_loading_5_seconds": "CPUロード5秒 (%)", "cpu_utilization_300_seconds": "過去 300 秒の CPU 使用率 (%)", "cpu_utilization_60_seconds": "過去 60 秒間の CPU 使用率 (%)", "cpu_utilization_900_seconds": "過去 900 秒間の CPU 使用率 (%)", "disk_utilization_unit": "ディスク使用率 (%)", "fw_system_version": "ファームウェア/システムイメージのバージョン", "fw_version": "ファームウェア/システムイメージバージョン", "mac_address": "MACアドレス", "memory_usage": "メモリ使用量", "memory_usage_unit": "メモリ使用量 (%)", "model_name": "モデル名", "os_type": "オペレーティング·システム", "power_comsumption": "消費電力 (W)", "serial_number": "シリアル番号", "system_contact": "システム連絡先", "system_description": "システムの説明", "system_location": "システムの場所", "system_name": "システム名", "system_object_id": "Sysobjectid", "system_up_time": "System Up Time", "title": "基本デバイスプロパティ"}, "cellular": {"cellular_carrier": "Cellular Carrier", "cellular_ip_address": "Cellular IP Address", "cellular_mode": "Cellular Mode", "cellular_signal": "Cellular Signal", "imei": "IMEI", "imsi": "IMSI", "title": "Cellular Information"}, "goose_table": {"app_id": "app_id", "gocb_name": "GoCB名", "goose_address": "GOOSEアドレス", "ied_name": "IED名", "port": "イングレスポート", "rx_counter": "RXカウンタ", "status": "ステータス", "tampered_port": "タンパードポート", "tampered_port_status": "タンパード・オン・ポート {{ port }}", "title": "GOOSEチェック", "type": "タイプ", "vid": "VID"}, "ipsec": {"l2tp_status": "L2TPステータス", "local_gateway": "ローカルゲートウェイ", "local_subnet": "ローカルサブネット", "name": "IPSec名", "phase_1_status": "フェーズ1ステータス", "phase_2_status": "フェーズ2ステータス", "remote_gateway": "リモートゲートウェイ", "remote_subnet": "リモートサブネット", "title": "IPsecステータス"}, "link": {"from": "来し方", "port": "ポート", "sfpTitle": "SFP 情報", "speed": "リンク速度", "title": "接続情報", "to": "行き先"}, "management_interfaces": {"http_port": "HTTPポート", "https_port": "HTTPSポート", "profinet_enabled": "PROFINET有効化", "ssh_port": "SSHポート", "telnet_port": "Telnetポート", "title": "管理インターフェース"}, "mms": {"title": "MMSのプロパティ"}, "modbus_device_property": {"model": "モデル", "revision": "リビジョン", "title": "Modbus デバイスのプロパティ", "vendor": "ベンダー"}, "not_selected": "デバイスの詳細を表示するにはモジュールを選択してください", "other_device_properties": {"active_redundancy_protocol": "冗長プロトコルのアクティブ化", "auto_ip_config": "自動IP構成", "default_gateway": "デフォルトゲートウェイ", "dns_1_ip_address": "DNS 1 IPアドレス", "dns_2_ip_address": "DNS 2 IPアドレス", "ip_ad_ent_addr": "ipAdEntAddr", "ip_address": "IPアドレス (mib)", "mac_address": "MACアドレス (mib)", "model_name": "モデル名", "monitor_current_mode": "現在のモードを監視する", "monitor_down_stream_rate": "ダウンストリームレートを監視する", "monitor_snr": "SNRを監視する", "monitor_up_stream_rate": "アップストリームレートを監視する", "netmask": "ネットマスク", "title": "その他のデバイスプロパティ"}, "port": {"if_number": "ifNumber", "interface": "インターフェース", "number_of_ports": "ポート数", "poe_port_class": "PoEポートクラス", "poe_power_legacy_pd_detect": "PoE電源レガシーPD検出", "poe_power_output_mode": "PoE電源出力モード", "title": "ポート情報"}, "power": {"power_1_status": "電源1ステータス", "power_2_status": "電源2ステータス", "title": "電源ステータス"}, "redundancy": {"active_redundancy_protocol": "冗長プロトコルのアクティブ化", "dh": "Dual Homing", "iec_624393_redundancy_protocol": "IEC 62439-3 冗長化プロトコル", "rstp": "RSTP", "tc": "Turbo Chain", "title": "冗長性", "trv2": "Turbo Ring V2"}, "selected_module": "選択したモジュール", "snmp": {"1st_trap_community": "第一トラップコミュニティ", "2nd_trap_server_community": "第二トラップコミュニティ", "inform_enabled": "通知有効化", "inform_retries": "通知リトライ", "inform_timeout": "通知タイムアウト", "read_community": "読み取りコミュニティ", "title": "SNMP情報", "trap_server_address_1": "トラップサーバーアドレス1", "trap_server_address_2": "トラップサーバーアドレス2"}, "title": "デバイスのプロパティ", "vpn": {"from_ip": "IPからVPN", "local_connection_name": "ローカルVPN接続名", "remote_connection_name": "リモートVPN接続名", "to_ip": "IPへVPN"}, "wireless": {"channel_width": "チャネル幅", "client_ip": "Client IP", "client_mac": "Client MAC", "client_RSSI": "信号強さ (dBm)", "rf_type": "無線周波数(RF)タイプ", "ssid_index": "SSID 索引", "title": "Wi-Fi 情報", "vap_mgmt_encryption": "VAP 管理暗号化", "vap_wpa_encrypt": "VAP WPA 暗号化", "vapAuthType": "VAP 認証タイプ"}}, "DEVICE_SETTING": {"advanced": "詳細設定", "alias": "エイリアス", "alias_input_invalid": "有効なデバイスエイリアスを入力してください", "apply_fail": "デバイスパラメータを設定できません", "apply_success": "デバイスパラメータが更新されました", "availability_time_frame": "可用性計算の時間枠", "get_parameter_fail": "デバイスパラメータを取得できません", "input_error_message": "新しい値は次の範囲内にある必要があります", "modify_device_alias": "デバイスのエイリアスの変更", "password": "パスワード", "password_input_invalid": "有効なパスワードを入力してください", "polling_interval": "ポーリング間隔", "polling_ip": "ポーリングIP", "snmp_configuration": "SNMP設定", "snmp_port_invalid": "有効なSNMPポートを入力してください", "title": "デバイス設定", "use_global": "グローバルアクセスユーザー名とパスワードを使用", "use_global_device_settings": "グローバルデバイス設定の使用", "username": "ユーザー名", "username_input_invalid": "有効なユーザー名を入力してください"}, "DEVICE": {"device_properties": "デバイスのプロパティ", "device_role": "<PERSON><PERSON> Role", "filter_device": "タイプしてデバイスをフィルターする", "filter_register_device": "タイプして登録済みデバイスをフィルターする", "na": "該当なし", "properties": {"availability": "稼動率", "device_alias": "デバイスのエイリアス", "device_ip": "デバイスIP", "firmware_version": "ファームウェアバージョン", "location": "場所", "mac_address": "MACアドレス", "model_name": "モデル名", "mxsec_flag": "セキュリティアドオン", "severity": "重要度"}, "registered_devices": "登録済", "site_name": "サイト名", "title": "デバイスリスト", "unregistered_devices": "未登録"}, "DeviceDashboard": {"avg_erase_count": "Avg. <PERSON><PERSON>", "change_disk_hint": "Please change your disk", "chartTitle": {"60s_cpu": "CPU 使用率 (過去 60 秒)", "connection": "Connection Status", "cpu": "CPU負荷率", "disk": "Disk Utilization", "memory": "メモリ負荷率", "noiseFloor": "Noise Floor", "raid_mode": "RAID Mode", "signalStrength": "信号強さ", "smart": "S.M.A.R.T.​", "snr": "S/N比 ", "traffic": "ネットワーク負荷"}, "connected": "接続済み", "current_status": "現在の状態:", "cycle_limitation": "Cycle Limitation", "icmp_not_support": "ICMPデバイスはこの機能をサポートしていません。", "link_down_port": "Link Down Ports", "link_up_port": "Link Up Ports", "managed": "管理された", "migrating_data": "Migrating Data", "no_raid": "No RAID", "normal": "Normal", "raid": "RAID", "rebuild": "Rebuild", "smart_hint": "(Self-Monitoring Analysis and Reporting Technology) represents device health status and lifespans information.", "unreachable": "{{ warningWording }} デバイス到達不能。デバイスから完全なデータを取得しない場合があります。"}, "DIALOG": {"add_wifi_ssid": {"aes": "AES", "clear_all_existing_ssid": "既存の SSID をすべてクリアします", "eapol_version": "EAPOLのバージョン", "encryption": "暗号化", "open": "Open", "passphrase": "パスワード", "personal": "Personal", "protected_management_frame": "保護された管理フレームワーク", "rf_band": "RFバンド", "security": "安全性", "ssid": "SSID", "title": "Wi-Fi SSIDを追加する", "tkip_aes_mixed": "TKIP/AESハイブリッド", "wpa_mode": "WPAモード"}, "auto_layout": {"desc": "自動レイアウトを行ってもよろしいですか？（現在のレイアウトはオーバーライドされます）", "title": "自動レイアウト"}, "auto_topology": {"advanced": "アドバンスドトポロジ分析", "advanced_desc": "*追加の時間が必要です。", "advanced_hint": "ICMPデバイスと、あるデバイスサポート、LLDPまたはフォワーディングテーブルの間にリンクを引く", "fail": "自動トポロジーに失敗しました", "link_check": "厳格なリンク検証モード", "link_check_hint": "有効であるときに、両端のデバイスがLLDPテーブルで別のデバイス情報を持っている場合、デバイス間のリンクはトポロジーのみに表示されます。", "new_topology": "新規トポロジー", "new_topology_desc": "既存のリンクは削除されます", "success": "自動トポロジーが正常に実行されました", "title": "自動トポロジー", "update_topology": "トポロジーの更新", "update_topology_desc": "新しいリンクが追加されるまで既存のリンクは保持されます"}, "background_dialog": {"content": "Please set a background image first.\n The background image can be a floor plan or other image that represents the coverage area.", "set_now": "今すぐ設定", "title": "背景画像を設定する"}, "change_group": {"assign_to_group": "グループへの割り当て", "change_group_fail": "デバイスの新しいグループへの移動に失敗しました", "change_group_success": "デバイスが新しいグループに移動しました", "current_group": "現在のグループ", "ip": "IPアドレス", "title": "グループの変更"}, "change_wifi_channel": {"channel": "通路", "channel_width": "チャネル幅", "execute_button": "変化", "title": "Wi-Fiチャンネルを変更する"}, "create_group": {"assign_group": "グループへの割り当て", "create_group_fail": "グループの作成に失敗しました", "create_group_success": "グループが作成されました", "current_group": "現在のグループ", "empty_group_name": "グループの名前を入力する必要があります", "group_desc": "グループの説明", "group_name": "グループ名", "parent_group": "親グループ", "title": "グループの作成"}, "create_snapshot": {"execute_button": "作成する", "title": "スナップショットの作成"}, "data_not_ready": {"content": "機器データがまだ準備できていませんので、後で再試行してください。", "title": "後で再試行してください"}, "delete_account": {"delete_confirm_message": "このアカウントを削除してもよかりますか?", "title": "アカウントの削除"}, "delete_background": {"desc": "背景画像を削除してもよろしいですか？", "failed": "背景の削除に失敗しました", "success": "背景が削除されました", "title": "背景の削除"}, "delete_custom_opc": {"delete_confirm_message": "このカスタムOPCタグを本当に削除してよいですか？", "title": "カスタムOPCタグの削除"}, "delete_device": {"delete_wireless_client_alert": "削除された無線クライアント端末の履歴データも消去され、記載されている機能のトレーサビリティに影響があります。続行しますか？", "delete_wireless_client_alert_title": "デバイス削除の確認", "desc": "このデバイスを削除してもよろしいですか？", "desc_multi": "これらのデバイスを削除してもよろしいですか？", "failed": "デバイスの削除に失敗しました", "success": "デバイスは正常に削除されました", "title": "デバイスの削除"}, "delete_group": {"desc": "このグループを削除してもよろしいですか？", "desc_multi": "これらのグループを削除してもよろしいですか？", "failed": "グループの削除に失敗しました", "success": "グループは正常に削除されました", "title": "グループの削除"}, "delete_link": {"desc": "このリンクを削除してもよろしいですか？", "desc_multi": "これらのリンクを削除してもよろしいですか？", "failed": "リンクの削除に失敗しました", "success": "リンクは正常に削除されました", "title": "リンクの削除"}, "delete_objects": {"desc": "選択したすべてのオブジェクトを削除してもよろしいですか？", "failed": "選択したオブジェクトの削除に失敗しました", "success": "選択したオブジェクトは正常に削除されました", "title": "オブジェクトの削除"}, "delete_site": {"desc": "このサイトを削除してもよろしいですか？", "failed": "サイトの削除に失敗しました", "success": "サイトは正常に削除されました", "title": "サイトの削除"}, "device_settings": {"fail": "Setting Link Budget Parameters (Per-device) Failed", "rx_antenna_gain": "RX Antenna Gain", "rx_cable_loss": "RX Cable Loss", "success": "Setting Link Budget Parameters (Per-device) Success", "title": "Link Budget Parameters (Per-device)", "tx_antenna_gain": "TX Antenna Gain", "tx_cable_loss": "TX Cable Loss"}, "disable_unsecured": {"execute_button": "HTTP と Telnet を無効にする", "title": "安全でない HTTP および Telnet コンソールを無効にする"}, "disable_unused": {"execute_button": "未使用のポートを無効にする", "keep_port_available": "一時的に中断された使用中のポートをアクティブなままにしておく", "title": "未使用のイーサネットおよび光ファイバーポートを無効にする"}, "discovery_device": {"another_discovery_error": "別の検索の処理中です", "discovering": "デバイスの検出中", "discovery_finish": "デバイスの検出が終了しました", "error": "デバイスを検出できません", "title": "デバイス発見"}, "dynamic_mac_sticky": {"address_limit": "アドレス制限", "alias": "エイリアス", "mac_sticky": "Sticky MAC", "mac_sticky_settings": "Sticky MAC 設定", "packet_drop": "パケットをドロップする", "port": "ポート", "port_duplicated": "ポートはすでに構成されています。", "port_format_not_equal": "モデルのポート形式とポート数は同じである必要があります。", "port_selection_guide": "ポート名キー", "port_shutdown": "ポートを閉じる", "security_action": "安全な行動", "title": "ダイナミックSticky MAC"}, "export_config": {"config_center": "設定センター", "config_file": "設定ファイル", "export": "エクスポート", "fail": "デバイス設定をエクスポートできません", "hint": "* Please make sure the username and password for this device are correctly set in \"Advanced Settings\"", "success": "デバイス設定が正常にエクスポートされました", "title": "設定のエクスポート"}, "goose": {"how_to_resolve": "解決方法を教えてください。", "import_scd_tooltip": "GOOSEメッセージを見るには、SCDファイルをインポートしてください。Power > Import SCD をクリックします。", "ip_port": "{{ ip }}のポート{{ port }}の状態", "open_web_console": "ウェブコンソールを開く", "port_tampered_msg": "GOOSE Port Tamperedの原因", "port_tampered_title": "GOOSEポート改ざんの問題を解決する", "reset_goose": "改ざんされたGOOSEメッセージのリセット", "reset_goose_desc": "GOOSE メッセージ {cbName }}/{{ appId }}/{{ mac }} グローバルデバイス設定", "reset_goose_title": "この改ざんされたGOOSEメッセージのインスタンスをすべてリセットしてもよろしいですか？", "resolve_goose_tampered_desc_1": "GOOSE Port Tampered問題を解決するために、以下の手順を試してください。", "resolve_goose_tampered_desc_2": "1. IED の設定を確認する", "resolve_goose_tampered_desc_3": "IEDのGOOSEパブリッシュ/サブスクライブメッセージが正しく設定されていることを確認します。", "resolve_goose_tampered_desc_4": "2. ポートの状態をチェックする", "resolve_goose_tampered_desc_5": "{{ ip }}のポート{{ port }}のステータスを確認してください。", "resolve_goose_tampered_desc_6": "2. すべてのデバイスが認証されていることを確認してください", "resolve_goose_tampered_desc_7": "ネットワーク上に未認証のデバイスがないかどうか確認してください。", "resolve_goose_tampered_desc_8": "GOOSE SA Tamperedの問題を解決するために、以下の手順を試してみてください。", "resolve_goose_timeout_desc_1": "GOOSEタイムアウトの問題を解決するために、以下の手順を試してください。", "resolve_goose_timeout_desc_10": "まだ動かない？", "resolve_goose_timeout_desc_11": "SFPモジュールを取り外し、再度取り付けてください。", "resolve_goose_timeout_desc_12_1": "技術的な質問がある場合は、まず、チャネルパートナーに連絡してください。", "resolve_goose_timeout_desc_12_2": "チャネルパートナー", "resolve_goose_timeout_desc_12_3": "first", "resolve_goose_timeout_desc_13_1": "連絡先", "resolve_goose_timeout_desc_13_2": "Moxaテクニカルサポート", "resolve_goose_timeout_desc_13_3": "にお問い合わせください。", "resolve_goose_timeout_desc_2": "1. IEDの設定を確認する", "resolve_goose_timeout_desc_3": "IEDのGOOSEメッセージの発行/購読が正しく設定されていることを確認します。", "resolve_goose_timeout_desc_4": "2. ポートがリンクダウンしていないことを確認する", "resolve_goose_timeout_desc_5": "GOOSEフロー({{ cbName }})上の各機器のポートがリンクダウンしていないことを確認してください。", "resolve_goose_timeout_desc_6": "3. ポートにTx/Rxエラーが発生していないことを確認する", "resolve_goose_timeout_desc_7": "リンクをクリックし、Link Traffic を選択して、Packet Error Rate セクションを見ます。ポートにエラーがないことを確認してください。", "resolve_goose_timeout_desc_8": "4. ファイバーポートが特定のしきい値を超えていないか確認する", "resolve_goose_timeout_desc_9": "\"SFP\" > \"SFP List\"のボタンをクリックします。ポートがある閾値を超えていないことを確認します。", "sa_tampered_msg": "GOOSE SAの改ざん", "sa_tampered_name_msg": "GOOSE メッセージ ({{ cbName }}/{{ appId }}/{ mac }}) が別の GOOSE ソース アドレスと競合している。", "sa_tampered_title": "GOOSE SA 改ざん問題を解決する", "tampered": "改ざん", "timeout": "タイムアウト", "timeout_msg": "GOOSEタイムアウトが発生する原因", "timeout_title": "GOOSEタイムアウト問題の解決"}, "import_config": {"config_center": "設定センター", "config_file": "設定ファイル", "config_file_error": "MXview Oneは.iniフォーマットファイルのみをサポートしています", "config_file_size_error": "最大ファイルサイズは3MB", "fail": "デバイス設定をインポートできません", "hint": "* Please make sure the username and password for this device are correctly set in \"Advanced Settings\"", "import": "インポート", "success": "デバイス設定が正常にインポートされました", "title": "設定のインポート"}, "link_traffic": {"date": "日付", "from": "リンク元", "packet_error_rate_title": "パケットエラー率", "port_traffic_title": "ポートトラフィック", "time": "時", "to": "リンク先", "utilization": "使用率", "value": "值"}, "mac_sticky_switch": {"mac_sticky": "Sticky MAC", "title": "Sticky MAC のオン/オフ"}, "maintain_group": {"change_icon": "Change group icon", "create": "作成", "create_group_fail": "グループの作成に失敗しました", "create_group_success": "グループが作成されました", "delete": "削除", "delete_group_fail": "グループの削除に失敗しました", "delete_group_success": "グループが削除されました", "empty_group_name": "グループの名前を入力する必要があります", "group_desc": "グループの説明", "group_name": "グループ名", "modify_group_fail": "グループの変更に失敗しました", "modify_group_success": "グループが変更されました", "reset_icon": "Reset to default image", "title": "グループメンテナンス"}, "ping": {"failed": "Pingに失敗しました", "title": "<PERSON>"}, "policy_profile": {"delete_msg": "選択したプロファイルを削除してもよろしいですか?", "delete_title": "プロフィールを削除"}, "reboot": {"execute_button": "再起動", "reboot_sequence": "再起動シーケンス", "title": "再起動", "unable_determine_reboot_sequence_hint": "再起動シーケンスを決定できません。 トポロジに MXview One がインストールされたコンピューターが含まれていることを確認して、再試行してください。"}, "relearn_dynamic_mac_sticky": {"execute_button": "もう一度勉強してください", "title": "ダイナミックSticky MAC を再学習する"}, "restore_to_create_snapshot": {"execute_button": "回復する", "title": "スナップショットから復元"}, "scd": {"import_failed_desc": "以下の問題点をご確認の上、SCDファイルを修正し、再度インポートをお試しください。", "import_failed_title": "SCDのインポートに問題がある", "import_succeed_desc": "GOOSEメッセージとフロー設計がネットワークトポロジーに正常に組み込まれました。", "import_succeed_title": "インポート完了", "missing": "欠落している", "missing_device": "以下のデバイスが見つかりません。", "missing_device_desc": "以下の問題点をご確認ください。", "scd_file_error": "MXview Oneは.scd形式のみをサポートしています。", "scd_file_size_error": "最大ファイルサイズは100MBです", "select_file": "ファイルを選択", "tag": "タグ", "topology_change_desc_1": "以下の手順で問題を解決してください。", "topology_change_desc_2": "1. 不足しているデバイスを追加する", "topology_change_desc_3": "編集」→「デバイスの追加」を選択します。", "topology_change_desc_4": "2. SCDファイルの再インポート", "topology_change_desc_5": "電源」→「SCDのインポート」を選択します。", "topology_change_title": "トポロジーを変更する際の問題", "visualize_goose_messages": "IED 間の GOOSE メッセージを可視化するには、まず SCD ファイルをインポートする必要があります。", "visualize_goose_messages_title": "GOOSEメッセージの可視化"}, "set_background": {"browse": "参照して", "desc": "ここに画像をドラッグするか、 ", "desc1": " 背景を設定します", "failed": "背景の設定に失敗しました", "image": "イメージ", "image_alpha": "透明度", "image_error": "選択されたファイルは画像ではありません。", "image_position": "ポジション", "image_saturation": "彩度", "image_x": "X", "image_y": "Y", "preview": "プレビュー", "size_error": "画像のサイズは1KB～20MBの間でなければなりません。", "success": "背景が更新されました", "title": "背景の設定", "topology_size": "トポロジーサイズ"}, "set_document": {"current_filename": "現在のファイル名", "delete": "ドキュメントを削除する", "error": "MXview OneはPDFフォーマットのみをサポートしています", "failed": "ドキュメントの設定に失敗しました", "file_size_error": "ドキュメントの最大サイズは20MBです。", "open": "ドキュメントを開く", "set": "ドキュメントの設定", "success": "デバイスドキュメントが更新されました", "title": "ドキュメントの設定", "upload": "アップロードするファイルを選択"}, "set_port_label": {"error": "エラー", "failed": "ポートラベルの設定に失敗しました", "from": "送信元：", "success": "ポートラベルが更新されました", "title": "ポートラベルの設定", "to": "宛先：", "use_custom_label": "カスタムラベルの使用", "vpn_link_desc": "VPNリンクのラベルは設定できません"}, "set_scale": {"error": "Parameter error", "fail": "Setting Scale Failed", "success": "Setting Scale Success", "title": "Set Scale"}, "severity_threshold": {"bandwidth_input_invalid": "0-100の整数を入力してください。", "bandwidth_utilization": "帯域幅使用率", "critical": "危機的", "failed": "重要度しきい値の設定に失敗しました", "information": "情報", "over": "超過", "packet_error_rate": "パケットエラー率", "sfp_rx_over": "SFP RX オーバー", "sfp_rx_under": "SFP RX アンダー", "sfp_temp_over": "SFP 温度 オーバー", "sfp_threshold": "SFP しきい値", "sfp_tx_over": "SFP TX オーバー", "sfp_tx_under": "SFP TX アンダー", "sfp_vol_over": "SFP 電圧 オーバー", "sfp_vol_under": "SFP 電圧 アンダー", "success": "重要度しきい値が更新されました", "title": "重要度しきい値", "under": "未満", "warning": "警告"}, "sfp_info": {"date": "日付", "dateError": "無効な日付範囲", "from": "リンク元", "port": "ポート", "sfpRxPower": "SFP RX", "sfpRxPower_label": "RX", "sfpRxPower_scale": " dBm", "sfpTemperature": "SFP 温度", "sfpTemperature_label": "温度", "sfpTemperature_scale": " °C", "sfpTxPower": "SFP TX", "sfpTxPower_label": "TX", "sfpTxPower_scale": " dBm", "sfpVoltage": "SFP 電圧", "sfpVoltage_label": "電圧", "sfpVoltage_scale": " V", "time": "時間", "title": "SFP 情報", "to": "リンク先", "value": "値"}, "sfp_sync": {"confirm_desc": "デバイスからSFPのしきい値を同期させますか？", "content": "MOXAスイッチからSFP Thresholdを同期させることができます。同期後は、Fiber Checkの温度、Tx Power、Rx Powerが各リンクのSFP Thresholdと同期されます。", "failed": "SFPしきい値の同期に失敗", "hint": "* SFP Thresholdを確認するには、リンクをクリックし、Severity Threshold > SFP Thresholdを選択します。", "success": "SFP しきい値同期成功", "title": "デバイスからのSFP閾値の同期"}, "wireless_settings": {"fail": "Setting Link Budget Parameters (General) Failed", "rxSensitivityHigh": "RX Sensitivity High", "rxSensitivityLow": "RX Sensitivity Low", "rxSensitivityMedium": "RX Sensitivity Medium", "sr": "Reserved Safety Factor", "success": "Setting Link Budget Parameters (General) Success", "title": "Link Budget Parameters (General)"}}, "EMBED_WIDGET": {"click_preview": "プレビュー (クリックしてください)", "copied_to_clipboard": "リンクがクリップボードにコピーされました", "copy_link": "リンクのコピー", "custom": "カスタマイズ", "desc": "これを任意のHTMLページに貼り付けます", "embed": "埋め込み", "height": "高さ", "layout_1": "レイアウト1", "layout_2": "レイアウト2", "layout_3": "レイアウト3", "layout_4": "レイアウト4", "link": "リンク", "no_api_key": "まずAPIキーを作成する必要があります", "preview": "プレビュー", "recent_event": "最新イベント", "select_api_key": "API鍵の選択", "select_layout": "レイアウトの選択", "title": "埋め込みWebウィジェット", "topology": "トポロジー", "topology_recent_event": "トポロジーと最新イベント", "width": "幅"}, "error_handler": {"error_session_expired_dialog": "セッションの有効期限切れ。システムによってログインページにリダイレクトされます。"}, "ERROR_MESSAGE": {"get_data_fail": "データの取得に失敗しました", "input_invalid_char": "有効な名前を入力してください", "input_invalid_characters": "このフィールドには、次の文字を含めることはできません: #%&*:<>?|{}\\\"/", "input_invalid_contact": "有効な連絡先を入力してください", "input_invalid_email": "有効な電子メールを入力してください。", "input_invalid_location": "有効な場所を入力してください", "input_invalid_mac": "無効なMACアドレス", "input_invalid_password_characters": "このフィールドには、次の文字を含めることはできません:  '\\\"/`", "input_invalid_script": "このフィールドには、次の文字を含めることはできません: #%&amp;*{}|:&quot;&lt;&gt;?/\\", "input_ip_invalid": "有効なIPアドレスを入力してください", "input_required": "この値は必須です", "non_restricted_ascii": "' \\\" ` \\\\ を除く ASCII 文字"}, "errors": {"A001": "必須フィールドがありません", "A002": "クエリ文字列がありません", "A003": "形式が正しくありません", "D001": "ライセンスの最大数に達しました", "D002": "デバイスが見つかりません", "D003": "デバイスはオンラインである必要があります", "D004": "デバイスが削除されました", "F001": "ファームウェアが見つかりません", "F002": "このファームウェアはすでに存在します", "F003": "ファームウェアファイルの最大数に達しました", "G001": "このグループは既に存在します", "G002": "グループが見つかりません", "G003": "デフォルトグループは変更できません", "G004": "管理者ユーザーはグループに割り当てることができません", "I001": "このインターフェースはすでに存在します", "I002": "インターフェースが見つかりません", "I003": "デフォルトグループは変更できません", "I004": "このインターフェースはセキュリティ プロファイルによって参照されます。", "L001": "無効なアクティベーションコード", "L002": "ライセンスの有効期限が切れました", "L003": "重複したアクティベーションコード", "L004": "ノードの最大数に達しました", "L005": "デバイスを有効化または無効化できません", "L006": "開始時間が無効です", "L007": "新しいタイプのライセンスの開始時間が無効です", "O001": "このオブジェクトは既に存在します", "O002": "オブジェクトが見つかりません", "O003": "このオブジェクトはセキュリティ プロファイルによって参照されます。", "P001": "パッケージが見つかりません", "P002": "このパッケージは既に存在します", "P003": "パッケージの最大数に達しました", "P004": "サポートされていないバージョン", "S001": "更新DBエラー", "SP001": "このプロフィールは既に存在します", "SP002": "プロフィールが見つかりません", "T001": "不正なトークン", "T002": "トークンの有効期限が切れました", "T003": "無効なトークン", "U001": "アクセス拒否", "U002": "このユーザー名は既に存在します", "U003": "ユーザーが見つかりません", "U004": "役割が見つかりません", "U005": "無効なユーザー名またはパスワード", "U006": "パスワードが最小文字数を満たしていません", "U007": "パスワードが最大長を超えています", "U008": "パスワードはユーザー名と同じにすることはできません", "U009": "少なくとも1つの大文字を含める必要があります", "U010": "少なくとも1つの小文字を含める必要があります", "U011": "少なくとも1桁の数字を含める必要があります", "U012": "少なくとも1つの英数字以外の文字を含める必要があります", "U013": "パスワードは以前のパスワードと同じにすることはできません", "U014": "無効なユーザー名", "Unknown": "不明"}, "EULA": {"agree_hint": "MXview One を使用するには、同意書に同意してください。", "eula_title": "EULA"}, "EVENT_MENU": {"ack": "確認", "ack_all": "すべて確認", "filter_event": "フィルター条件"}, "EVENT": {"ack_all_events": {"ack_all_event_success": "すべてのイベントが確認されました。", "button_ack_hint": "Ack Selected Event(s)", "button_hint": "すべてのイベントを確認", "confirm_message": "すべてのイベントが確認されます！このプロセスを続行しますか？", "unable_ack_all_event": "すべてのイベントを確認できません"}, "ack": {"ack_fail": "イベントを確認できません", "acked": "確認済み", "any": "任意の", "unacked": "未確認"}, "all_event": "すべてのイベント", "all_group": "すべてのグループ", "all_site": "すべてのサイト", "clear_all_events": {"button_hint": "すべてのイベントを消去", "clear_all_event_success": "すべてのイベントが消去されました。", "confirm_message": "すべてのイベントが消去されます！このプロセスを続行しますか？", "confirm_message_network": "すべてのネットワークおよびデバイス イベントがクリアされます。このプロセスを続行してもよろしいですか?", "confirm_message_system": "すべてのシステム イベントがクリアされます。このプロセスを続行しますか?", "unable_clear_all_event": "すべてのイベントを消去できません"}, "custom_events": {"activate": "カスタムイベントの有効化", "add_custom_event": "カスタムイベントの追加", "all": "すべて", "all_devices": "すべてのデバイス", "apply_fail": "カスタムイベントを設定できません", "apply_success": "カスタムイベントが追加されました", "below": "未満", "condition": "条件", "condition_operator": "条件演算子", "condition_value": "条件値", "consecutive_updates": "連続ポーリング", "delete_fail": "カスタムイベントを削除できません", "delete_success": "1 つ以上のカスタムイベントが削除されました", "description": "説明", "device_properties": "デバイスのプロパティ", "devices": "デバイス", "duration": "期間", "equal": "等価", "event_name": "イベント名", "filter_custom_event": "タイプしてカスタムイベントをフィルターする", "get_fail": "カスタムイベントを取得できません", "helper": "カスタムイベントを追加してデバイスを割り当てます", "not_equal": "非等価", "over": "超過", "recovery_description": "リカバリの説明", "register": "登録", "register_devices": "デバイスの登録", "search": "検索", "severity": "重要度", "title": "カスタムイベント", "update_custom_event": "カスタムイベントの更新", "update_fail": "カスタムイベントを更新できません", "update_success": "カスタムイベントが更新されました"}, "event_description": {"abc_attache_warning": "ABCデバイスが取り付けられました", "abc_auto_import_warning": "自動インポート設定に失敗しました", "abc_config_warning": "設定ファイルのエクスポートに失敗しました", "abc_detache_warning": "ABCデバイスが取り外されました", "abc_log_warning": "ログファイルのエクスポートに失敗しました", "abc_space_warning": "十分なスペースがありません", "abc_unauthorized_warning": "不正なメディアが検出されました", "abc_unknow_warning": "不明", "abc02_warning": "USBイベント： {{param}}", "account_audit_baseline_failed": "アカウント監査を完了できませんでした。すべてのデバイスからデータを取得できません。", "account_audit_baseline_match": "アカウント監査が正常に完了しました。結果はベースラインと一致しています。", "account_audit_failed": "アカウント監査を完了できませんでした。{{ip}} のデバイスからデータを取得できません。", "account_audit_match": "アカウント監査が正常に完了しました。結果はベースラインと一致しています。", "account_audit_mismatch": "アカウント監査が正常に完了しました。結果がベースラインと一致しません。", "account_audit_unable_retrieve_device": "アカウント監査を完了できませんでした。すべてのデバイスからデータを取得できません。", "accountAudit1": "アカウント監査がベースラインと一致しない", "accountAudit2": "アカウント監査を完了できませんでした", "all_event_clear": "すべてのイベントが消去されました", "auth_fail": "デバイスのログイン認証に失敗しました。", "availability_down": "しきい値未満のデバイス可用性", "availability_down_recovery": "しきい値未満のデバイス可用性のリカバリ", "background_scan_found": "新しいデバイスが見つかりました (IP: {{ip}})。", "cli_button_event_all_failed": "IP: {{sourceIP}} の {{user}} がボタン: {{cliName}} を実行しました。実行結果はすべて失敗です。", "cli_button_event_all_finished": "IP: {{sourceIP}} の {{user}} がボタン: {{cliName}} を実行しました。実行結果はすべて完了です。", "cli_button_event_all_partially_finished": "IP: {{sourceIP}} の {{user}} がボタン: {{cliName}} を実行しました。実行結果は部分的に完了しました。", "cli_button_event_start": "IP: {{sourceIP}} の {{user}} がボタン: {{cliName}} を実行しました。", "cli_saved_script_event": "IP: {{sourceIP}} の {{user}} が CLI コマンド {{cliName}} を実行しました", "cli_script_event": "IP: {{sourceIP}} からの {{user}} が CLI の実行を開始します。", "cold_start": "コールドスタート", "custom_event_detail": "{{param1}}。しきい値={{param2}}、値={{param3}}。 {{param4}}", "custom_event_recovery": "カスタムイベントが回復した", "custom_event_recovery_detail": "{{param1}} 復旧しました。しきい値={{param2}}、値={{param3}}。 {{param4}}", "custom_event_trigger": "カスタムイベントが発生した", "cybersecurity_event_trigger": "サイバーセキュリティイベント", "ddos_under_attack": "セキュアルーターがDDoS攻撃を受けています", "ddos_under_attack_recovery": "セキュアルーターはもうDDoS攻撃を受けていません", "device_configuration_change": "デバイス設定が変更されました。", "device_firmware_upgrade": "デバイスファームウェアが更新されました", "device_infom_receive": "デバイス情報が受信されました", "device_lockdown_violation": "デバイスのロックダウン違反", "device_power_down": "電源 {{param}} オフ", "device_power_down_off_to_on": "PWR {{param}} オフ > オン。", "device_power_on": "電源 {{param}} オン", "device_power_on_to_off": "PWR {{param}} オン > オフ。", "device_snmp_reachable": "デバイスSNMP到達可能", "device_snmp_unreachable": "デバイスSNMP到達不能", "di_off": "DI {{param}}} オフ", "di_on": "DI {{param}}} オン", "disk_space_not_enough": "使用可能なディスク容量がしきい値未満です", "event_config_import": "新しい構成ファイルがインポートされました", "event_ddos_attack": "DoS攻撃が検出されました", "event_ddos_attack_recovery": "セキュアルーターのDDoS攻撃のリカバリ", "event_dying_gasp": "システムの電源が切れます。 デバイスはそのコンデンサによって電力を供給されます", "event_eps_is_off": "PoE外部電源がオフになりました", "event_eps_is_on": "PoE外部電源がオンになりました", "event_firewall_attack": "ファイアウォール攻撃を受けているセキュアルーター", "event_firewall_attack_recovery": "セキュアルーターのファイアウォール攻撃のリカバリ", "event_ieee_lldp_table_change": "{{trapDetail}}", "event_ieee_rstp_root_change": "RSTPの新しいルートがトポロジーで選択されました", "event_ieee_rstp_topology_change": "トポロジーがRSTPによって変更されました", "event_linux_account_setting_change": "{{username}}のアカウント設定が変更されました", "event_linux_config_change": "{{modules}}設定は{{username}}により修正されました。", "event_linux_config_import": "{{username}}による構成インポート{{successOrFail}}", "event_linux_config_import_failed": "失敗", "event_linux_config_import_succeed": "成功", "event_linux_coupling_change": "Turbo Ring v2カップリングパスステータスが変更されました", "event_linux_di_off": "デジタル入力{{index}}がオフになりました", "event_linux_di_on": "デジタル入力{{index}}がオンになりました", "event_linux_dotlx_auth_fail": "802.1X認証は{{reason}}によりポート{{portIndex}}で失敗しました", "event_linux_dual_homing_change": "デュアルホーミングパスが切り替わりました", "event_linux_log_capacity_threshold": "イベントログエントリの数{{value}}がしきい値に達しました", "event_linux_low_input_voltage": "電源の入力電圧がしきい値を下回っています", "event_linux_master_change": "Ring {{index}}マスターが変更されました", "event_linux_master_mismatch": "Ring {{index}}マスター設定が一致しません", "event_linux_over_power_budget_limit": "すべてのPDの消費電力{{value}}が最大入力電力{{threshold}}を超えました", "event_linux_password_change": "{{username}}のパスワードが変更されました", "event_linux_pd_no_response": "PoEポート{{portIndex}}デバイスはPD障害チェックに応答していません", "event_linux_pd_over_current": "PoEポート{{portIndex}}の電流が安全制限を超えました", "event_linux_pd_power_off": "PoEポート{{portIndex}} PD電源オフ", "event_linux_pd_power_on": "PoEポート{{portIndex}} PD電源オン", "event_linux_port_recovery_by_ratelimit": "ポート{{portIndex}}はレート制限により復旧しました", "event_linux_port_shutdown_by_ratelimit": "ポート{{portIndex}}はレート制限を超えるトラフィックによりブロックされています。", "event_linux_port_shutdown_by_security": "ポート{{portIndex}}はポートセキュリティによりシャットダウンしました", "event_linux_power_detection_fail": "PoEポート{{portIndex}}デバイスは{{devicetype}}です,{{suggestion}}してください。", "event_linux_power_detection_fail_devietype_na": "該当なし", "event_linux_power_detection_fail_devietype_noPresent": "存在なし", "event_linux_power_detection_fail_devietype_unknown": "不明", "event_linux_power_detection_fail_suggestion_disable_POE": "POEを無効化", "event_linux_power_detection_fail_suggestion_enable_legacy": "レガシーを選択する", "event_linux_power_detection_fail_suggestion_enable_POE": "POEを有効化", "event_linux_power_detection_fail_suggestion_no": "提案なし", "event_linux_power_detection_fail_suggestion_rais_EPS_voltage": "Eps電圧を上昇させる", "event_linux_power_detection_fail_suggestion_select_auto": "自動を選択する", "event_linux_power_detection_fail_suggestion_select_force": "強制を選択する", "event_linux_power_detection_fail_suggestion_select_high_power": "高電源を選択する", "event_linux_power_off": "電源{{index}}がオフになりました", "event_linux_power_on": "電源{{index}}がオンになりました", "event_linux_redundant_port_health_check": "冗長ポート{{portIndex}}健全性チェックが失敗しました", "event_linux_RMON_trap_is_falling": "RMONトラップが低下している", "event_linux_RMON_trap_is_raising": "RMONトラップが上昇している", "event_linux_rstp_invalid_bpdu": "RSTPポート{{portIndex}}は無効なBPDUを受信しました（タイプ: {{type}}, 値:{{value}}）", "event_linux_rstp_migration": "ポート{{portIndex}}が{{originTopology}}から{{changeTopology}}に変更されました", "event_linux_rstp_new_port_role": "RSTPポート{{portIndex}}のロールが{{originalRole}}から{{newRole}}に変更されました", "event_linux_ssl_cer_change": "SSL証明書が変更されました", "event_linux_topology_change": "トポロジーが変更されました。", "event_linux_topology_change_by_type": "トポロジーが{{topologyType}} によって変更されました", "event_linux_user_login_lockout": "{{param}}がログイン試行に失敗したため、{{username}}はロックされています", "event_linux_user_login_success": "{{username}}は{{interface}}を介して正常にログインしました", "event_log_cleared_trap_event_info": "The event logs were cleared (User: {{ user }}, IP: {{ ip }}, Interface: {{ interface }})", "event_message_serial_device_port_any_recovery": "シリアル ポート {{portnum}} が通常の動作を再開しました。", "event_message_serial_device_port_break": "シリアル ポート {{portnum}} がエラーを受信しました: エラー カウントが中断されました。", "event_message_serial_device_port_frame": "シリアル ポート {{portnum}} がエラーを受信しました: フレーム エラー カウント。", "event_message_serial_device_port_overrun": "シリアル ポート {{portnum}} がエラーを受信しました: オーバーラン エラー数。", "event_message_serial_device_port_parity": "シリアル ポート {{portnum}} がエラーを受信しました: パリティ エラー数。", "event_message_serial_device_port_rx": "シリアルポート {{portnum}} の RX は、過去 {{min}} 分間にデータを受信しませんでした。", "event_message_serial_device_port_rx_recovery": "シリアルポート {{portnum}} の RX がデータの受信を再開しました。", "event_message_serial_device_port_rxtx": "シリアル ポート {{portnum}} の RX と TX は、過去 {{min}} 分間にデータを受信しませんでした。", "event_message_serial_device_port_rxtx_recovery": "シリアルポート {{portnum}} の RX と TX がデータの受信を再開しました。", "event_message_serial_device_port_tx": "シリアルポート {{portnum}} の TX は、過去 {{min}} 分間にデータを受信しませんでした。", "event_message_serial_device_port_tx_recovery": "シリアルポート {{portnum}} の TX がデータの受信を再開しました。", "event_not_found_type": "Unknown event type: {{ eventType }}", "event_pd_check_fail": "PD障害チェック（非応答）", "event_pd_over_current": "PoEポート {{portIndex}} 過電流/短絡", "event_pd_power_off": "PoEポート {{portIndex}} 電源オフ", "event_pd_power_on": "PoEポート {{portIndex}} 電源オン", "event_prp_function_fail": "PRP機能が失敗しました", "event_serial_device_port_break": "シリアルポートがエラーを受信しました: エラーカウントをブレーク", "event_serial_device_port_frame": "シリアルポートがエラーを受信しました: フレーム エラー数", "event_serial_device_port_overrun": "シリアルポートがエラーを受信しました: オーバーランエラー数", "event_serial_device_port_parity": "シリアルポートがエラーを受信しました: パリティエラー数", "event_serial_device_port_rx": "シリアルポートのRXはデータを受信しませんでした", "event_serial_device_port_rxtx": "シリアルポートのRXとTXはデータを受信しませんでした", "event_serial_device_port_tx": "シリアルポートのTXはデータを受信しませんでした", "event_sfp_rx_below": "SFPポート{{portIndex}}のRX電力({{currentdB}}dBm)は閾値({{thresholddB}}dBm)未満です。", "event_sfp_rx_below_recovery": "SFP ポート {{portIndex}} RX {{recoverydB}}dBm は回復しました", "event_sfp_temp_over": "SFPポート{{portIndex}}のSFPモジュール温度({{currentTemp}}℃)は閾値({{currentTemp}}℃)を超えました。", "event_sfp_temp_over_recovery": "SFP ポート {{portIndex}} 温度 {{recoveryTemp}}ºc は回復しました", "event_sfp_tx_below": "SFPポート{{portIndex}}のTX電力({{currentdB}})は閾値({{thresholddB}}dBm)未満です。", "event_sfp_tx_below_recovery": "SFP ポート {{portIndex}} TX {{recoverydB}}dBm は回復しました", "event_sfp_voltage_below": "SFPポート{{portIndex}}のモジュール電圧({{currentVoltage}}V)は閾値({{thresholdVoltage}}V)未満です。", "event_sfp_voltage_below_recovery": "SFP ポート {{portIndex}} 電圧 {{recoveryVoltage}}v は回復しました", "event_sfp_voltage_over": "SFPポート{{portIndex}}のモジュール電圧({{currentVoltage}}V)は閾値({{thresholdVoltage}}V)を超えました。", "event_sfp_voltage_over_recovery": "SFP ポート {{portIndex}} 電圧 {{recoveryVoltage}}v は回復しました", "event_too_many_login_failure": "ウェブインターフェースへのアクセスは、ログイン失敗が多すぎるために一時的にブロックされています。", "event_too_many_login_failure_recovery": "Webアクセスが再開されます。", "event_tracking_port_enabled_status": "ひとつのポートイネーブル関連トラッキングエントリは変更されました", "event_tracking_static_route_status_changed": "ひとつの静的ルート関連トラッキングエントリは変更されました", "event_tracking_status_changed": "ひとつの追跡エントリステータスは変更されました", "event_tracking_vrrp_status_changed": "ひとつのVRRP関連トラッキングエントリは変更されました", "event_trusted_access_attack": "トラストアクセス攻撃を受けているセキュアルーター", "event_trusted_access_attack_recovery": "セキュアルーターのトラストアクセス攻撃のリカバリ", "event_user_info_change": "アカウント情報が変更されました： {{trapoid}}", "event_v3_trap_parse_error": "V3トラップパースエラー", "event_v3_trap_parse_error_recovery": "V3トラップパースエラーイベントは消去されました", "exceed_poe_threshold": "PoEシステムのしきい値の超過", "fan_module_malfunction": "ファンモジュールが故障しています。", "fiber_warning": "{{portIndex}} ファイバー警告（{{warningType}}）", "firewall_policy_violation": "ファイアウォールポリシーとDoSルール： {{trapoid}}", "firewall_under_attack": "セキュアルーターのファイアウォールが攻撃を受けています", "firewall_under_attack_recovery": "セキュアルーターのファイアウォールはもう攻撃を受けていません", "firmware_upgraded": "ファームウェアのアップグレード", "firmware_version_release": "ファームウェアのアップデートが利用可能です。 詳細については、ファームウェア管理ページを確認してください。", "goose_healthy": "GOOSE ステータス。 健全なGOOSEメッセージ{{ display }}", "goose_healthy_with_value": "GOOSE ステータス。 健全なGOOSEメッセージ{{ display }}", "goose_tampered": "GOOSEステータス  改ざん", "goose_tampered_with_value": "GOOSE ステータス。 改ざんGOOSEメッセージ{{ display }}", "goose_timeout": "GOOSEの状態を表示します。 タイムアウト", "goose_timeout_with_value": "GOOSE ステータス。 タイムアウト GOOSE メッセージ {{ display }}", "high_cpu_loading": "CPU負荷が10分間連続で85%を超えました。", "icmp_packet_loss_over_critical_threhold": "デバイスICMPパケット損失率の最大値 {{param1}}（危機しきい値 {{param2}} 以上）", "icmp_packet_loss_over_critical_threhold_recovery": "デバイスICMPパケット損失率が {{param1}} を下回っています（危機しきい値 {{param2}} 以上）", "icmp_packet_loss_over_threhold": "デバイスICMPパケット損失率の最大値 {{param1}}（しきい値 {{param2}} 以上）", "icmp_packet_loss_over_threhold_recovery": "デバイスICMPパケット損失率が {{param1}} を下回っています（しきい値 {{param2}} 以上）", "icmp_reachable": "デバイスICMP到達可能", "icmp_unreachable": "デバイスICMP到達不能", "iei_fiber_warning": "ファイバー警告がトリガーされました", "input_bandwidth_over_threshold": "入力帯域幅利用率が閾値を超えました。", "input_bandwidth_over_threshold_disabled": "ポート{{portIndex}}の入力帯域使用率に閾値が設定されていません。", "input_bandwidth_over_threshold_recovery": "ポート{{portIndex}}の入力帯域の使用率が回復し，値が{{currentValue}}になりました。", "input_bandwidth_over_threshold_with_port": "ポート{{portIndex}}の入力帯域幅利用率({{currentValue}})が閾値({{threshold}})を超えました。", "input_bandwidth_under_threshold": "入力帯域幅利用率は閾値未満です。", "input_bandwidth_under_threshold_disabled": "ポート{{portIndex}}の入力帯域使用率に閾値が設定されていません。", "input_bandwidth_under_threshold_recovery": "ポート{{portIndex}}の入力帯域使用率が回復し，値が{{currentValue}}となった状態。", "input_bandwidth_under_threshold_with_port": "ポート{{portIndex}}の入力帯域幅利用率({{currentValue}})は閾値({{threshold}})未満です。", "input_packet_error_over_threshold": "入力パケットエラーレートが閾値を超えました。", "input_packet_error_over_threshold_disabled": "ポート{{portIndex}}の入力パケット誤り率に閾値の設定はありません。", "input_packet_error_over_threshold_recovery": "ポート{{portIndex}}の入力パケット誤り率が回復し、値が{{currentValue}}になりました。", "input_packet_error_over_threshold_with_port": "ポート{{portIndex}}の入力パケットエラーレート({{currentValue}})が閾値({{threshold}})を超えました。", "insufficient_disk_space": "利用可能なディスク容量は5 GB未満です。", "interface_set_as_ospf_designated_router": "インターフェイスは OSPF 指定ルーターとして設定されます。", "ip_conflict_detected": "{{ip}} の IP 競合が検出されました。競合する MAC:", "ip_conflict_detected_failed": "Npcap/WinPcap/Libpcap が見つからないため、IP 競合検出を実行できません", "ip_conflict_recovery": "IP 競合が解決されました。", "iw_client_joined": "参加済みクライアント： {{param}}", "iw_client_left": "残りのクライアント： {{param}}", "l3_firewall_policy_violation": "ファイアウォール ポリシー違反 (NAT シリーズ)", "license_limitation_reached": "最大ライセンス制限に達しました。", "license_not_enough": "ノード制限を超えています。いくつかのノードを削除するか、MXview Oneライセンスをアップグレードしてください。", "license_over": "ノード制限を超えています。いくつかのノードを削除するか、MXview Oneライセンスを更新してください", "lldp_change": "LLDPテーブルが変更されました", "logging_capacity": "イベントログが容量のしきい値を超えています", "login_radius_fail": "RADIUS+サーバーによるログイン認証が失敗しました", "login_radius_success": "RADIUS+サーバーによるログイン認証が成功しました", "login_tacas_fail": "TACACS+サーバーによるログイン認証が失敗しました", "login_tacas_success": "TACACS+サーバーによるログイン認証が成功しました", "mac_sticky_violation": "MACスティッキー違反", "mrp_multiple_event": "MRP 複数マネージャー イベントが発生しました。", "mrp_ring_open_event": "MRP リングオープンイベントが発生しました。", "mstp_topology_changed": "MSTP トポロジの変更", "mxview_autopology_finish": "自動トポロジーが終了しました", "mxview_autopology_start": "自動トポロジーが開始しました", "mxview_db_backup_fail": "データベースのバックアップに失敗しました", "mxview_db_backup_sucess": "データベースのバックアップが完了し、%MXviewPRO_Data%\\db_backup\\{{param1}} {{param2}}に保存されました", "mxview_job_done": "ジョブ：{{jobname}}が完了しました", "mxview_job_start": "ジョブ: {{jobname}} 開始", "mxview_server_license_limit": "MXview One Central Manager のライセンスが不足しています", "mxview_server_start": "MXview Oneサーバーが開始しました", "mxview_sms_fail": "SMS通知を送信できません", "mxview_sms_success": "MXview OneはSMS通知を正常に送信しました", "mxview_user_lockout": "アカウント {{param}} は一時的にロックアウトされました", "mxview_user_login_fail": "MXview Oneへのログインに失敗しました。", "mxview_user_login_sucess": "ユーザーログイン： {{param}}", "mxview_user_logout": "ユーザーログアウト： {{param}}", "network_latency": "MXview One Central Manager へのネットワーク遅延が 100 ミリ秒を超えました", "new_port_role_selected": "新しいポートの役割を選択します。", "new_root_bridge_selected_in_topology": "トポロジ内の新しいルート ブリッジを選択します。", "notification_sfp_rx_below": "SFP ポート {{portIndex}} RX アンダーしきい値", "notification_sfp_temp_over": "SFP ポート {{portIndex}} 温度 オーバーしきい値", "notification_sfp_tx_below": "SFP ポート {{portIndex}} TX アンダーしきい値", "notification_sfp_voltage_below": "SFP ポート {{portIndex}} 電圧 アンダーしきい値", "notification_sfp_voltage_over": "SFP ポート {{portIndex}} 電圧 オーバーしきい値", "nport_syslog_over_threshold": "NPortがシステムログのしきい値を超えています", "opcua_server_start": "MXview One OPC UA サーバーが起動します。", "opcua_server_stop": "MXview One OPC UA サーバーが停止しました。", "ospf_designated_router_changed": "OSPF 指定ルーターが変更されました。", "ospf_designated_router_interface_and_adjacency_changed": "OSPF 指定ルーター インターフェイスと隣接関係が変更されます。", "out_of_memory": "使用可能なメモリは20%未満です。", "output_bandwidth_over_threshold": "出力帯域幅利用率が閾値を超えました。", "output_bandwidth_over_threshold_disabled": "ポート{{portIndex}}の出力帯域使用率に閾値が設定されていません。", "output_bandwidth_over_threshold_recovery": "ポート{{portIndex}}の出力帯域使用率が回復し，値が{{currentValue}}になりました。", "output_bandwidth_over_threshold_with_port": "ポート{{portIndex}}の出力帯域幅利用率({{currentValue}})が閾値({{threshold}})を超えました。", "output_bandwidth_under_threshold": "出力帯域幅利用率は閾値未満です。", "output_bandwidth_under_threshold_disabled": "ポート{{portIndex}}の出力帯域使用率に閾値が設定されていません。", "output_bandwidth_under_threshold_recovery": "ポート{{portIndex}}の出力帯域利用率が回復し，値が{{currentValue}}になっています。", "output_bandwidth_under_threshold_with_port": "ポート{{portIndex}}の出力帯域幅利用率({{currentValue}})は閾値({{threshold}})未満です。", "output_packet_error_over_threshold": "出力パケットエラーレートが閾値を超えました。", "output_packet_error_over_threshold_disabled": "ポート{{portIndex}}の出力パケットエラーに閾値が設定されていない。", "output_packet_error_over_threshold_recovery": "ポート{{portIndex}}の出力パケットエラーは回復し，値は{{currentValue}}です。", "output_packet_error_over_threshold_with_port": "ポート{{portIndex}}の出力パケットエラーレート({{currentValue}})が閾値({{threshold}})を超えました。", "overheat_protection_now_active_for_power_module": "電源モジュール {{ x }} の過熱保護が有効になりました。", "password_automatically_changed_failed": "デバイスのパスワードを自動的に変更できませんでした。", "password_automatically_changed_success": "デバイスのパスワードが自動的に変更されました。", "password_automation_scheduled": "パスワード自動化がスケジュールされた時間に到達し、実行を開始します。", "phr_port_timediff": "PHR AB ポートのタイムディフ。", "phr_port_wrong_lan": "PHR AB ポートが間違った LAN です。", "poe_off_info": "The device is not powered by PoE", "poe_on_info": "The device is powered by PoE", "port_linkdown_event": "ポート {{portindex}} リンクダウン", "port_linkdown_recovery": "ポート {{portindex}} リンクアップ", "port_linkup_event": "ポート {{portindex}} リンクアップ", "port_linkup_recovery": "ポート {{portindex}} リンクダウン", "port_loop_detect": "ポート {{portnum}} でポート ループが検出されました", "port_loop_detect_resolved": "ポート {{portnum}} でのポート ループが解決されました。", "port_loop_detected": "ポートルーピング", "port_pd_short_circuited": "ポート {{portnum}} が非 PD または PD で短絡しています。", "port_traffic_overload": "ポート {{portIndex}} トラフィック過負荷 {{percent}}%", "power_danger_recovery": "電源 {{param}} がACに変更されました", "power_has_been_cut_due_to_overheating": "過熱により電源が遮断されました。", "power_module_fan_malfunction": "電源モジュールのファンが故障しています。", "power_type_danger": "電源 {{param}} がDCに変更されました", "pse_fet_bad": "PoEポート {{portIndex}} 外部FETに障害が発生しました", "pse_over_temp": "PSEチップが温度超過です", "pse_veeuvlo": "PSEチップVEE不足電圧ロックアウト", "ptp_grandmaster_changed": "PTP Grandmaster変更", "ptp_sync_status_changed": "PTP同期ステータスが変更されました", "rateLimit_off": "ポート {{portindex}} レート制限オフ", "rateLimit_on": "レート制限はポート{{portindex}}で有効です。", "recorved_device_lockdown_violation": "デバイスのロックダウン違反から回復しました", "recorved_l3_firewall_policy_violation": "ファイアウォール ポリシー違反からの回復 (NAT シリーズ)", "redundancy_topology_change": "冗長トポロジーが変更されました。", "syslog_server_start": "MXview 1 つの syslog サーバーが起動します。", "syslog_server_stop": "MXview 1 つの syslog サーバーが停止しました。", "system_temperature_exceeds_threshold": "システム温度がしきい値を超えています。", "temporary_account_activate_success": "{{ip}} の一時アカウントが正常に有効化されました。", "temporary_account_deactivate_success": "{{ip}} の一時アカウントが正常に無効化されました。", "thermal_sensor_component_overheat_detected": "温度センサーアセンブリの過熱が検出されました。", "trunk_port_link_down": "トランキングポート{{portindex}} リンクダウン（物理ポート：{{param}}）", "trunk_port_link_down_recovery": "トランキングポート{{portindex}} リンクアップ（物理ポート：{{param}}）", "trunk_port_link_up": "トランキングポート{{portindex}} リンクアップ（物理ポート：{{param}}）", "trust_access_under_attack": "セキュアルーターのトラストアクセスに違反が発生しました", "trust_access_under_attack_recovery": "セキュアルーターのトラストアクセスにはもう違反が発生していません", "turbo_ring_master_match": "Turbo Ringマスターが一致しました", "turbo_ring_master_mismatch": "Turbo Ringマスターが一致しません", "turbo_ring_master_unknow": "Turbo Ringマスターイベント。ステータスが不明です", "turbochain_topology_change": "Turbo Chainトポロジーが変更されました", "turboring_coupling_port_change": "Turbo Ringカップリングポートが変更されました", "turboring_master_change": "Turbo Ringマスターが変更されました", "unknown_device_detected": "不明なデバイスが検出されました", "user_login_fail": "アカウントの認証に失敗しました", "user_login_success": "アカウントの認証に成功しました： {{username}}", "usercode_revoke": "Usercodeはユーザーによって再生成されます。", "vpn_link_recovery": "VPNトンネル {{param}} が復旧しました", "vpn_linkdown": "VPNトンネル {{param}} が切断されました", "vpn_linkup": "VPNトンネル {{param}} が接続されました", "vrrp_master_changed": "VRRP Master変更", "warn_start": "ウォームスタート"}, "event_detail_title": "イベント詳細ID：{{eventId}}", "filter": "フィルター条件", "filter_end_time": "終了日", "filter_event": "タイプしてイベントをフィルターする", "filter_event_type": "クイックフィルターイベント", "filter_from_time": "開始日", "filter_hour": "時間", "filter_min": "分", "filter_sec": "秒", "filter_type": {"last_fifty_events": "最近の50イベント", "last_twenty_events": "最近の20イベント", "unack_events": "未確認イベント", "unack_last_fifty_events": "最近の未確認50イベント", "unack_last_twenty_events": "最近の未確認20イベント"}, "group": "グループ", "recent_event": "最新イベント", "severity": {"any": "任意の", "critical": "危機的", "information": "情報", "network_device": "ネットワークとデバイス", "system_information": "システム情報", "warning": "警告"}, "show_event_detail": "詳細を表示", "source": {"any": "任意の", "mxview": "MXview One", "security_sensing": "セキュリティセンシング", "trap": "トラップ"}, "table_title": {"ack": "確認", "ack_time": "確認時刻", "always_show": "起動時に常に最近のイベントを表示する", "description": "説明", "detail_information": "詳細情報", "event_id": "ID", "event_properties": "イベントのプロパティ", "event_source": "ソース", "event_time": "発行された時間", "hide_recent_event": "最近のイベントを非表示にする", "severity": "重要度", "show_recent_event": "最近のイベントを表示する", "site_name": "サイト名", "source_ip": "ソースIP"}, "tabs": {"network_device_title": "ネットワークとデバイス", "security_event": "サイバーセキュリティイベント", "security_title": "サイバーセキュリティ", "system_title": "システム"}}, "execute_cli_object": {"add_cli_object": "CLI スクリプトを追加する", "alias": "エイリアス", "cli_error": {"connection_failure": "接続失敗", "handshake_failure": "ハンドシェイクの失敗", "login_failure": "ログイン失敗", "port_limit": "ポート セキュリティ アドレス エントリの最大数: {{param}}", "reach_maximum_ssid": "SSID の最大数: {{param}}", "smmp_configuration_mismatch": "SNMP設定の不一致", "ssh_not_supported": "SSHクライアントへの接続に失敗しました", "unable_to_set_port": "ポート {{port}} を設定できません", "unknown_error": "不明なエラー"}, "cli_Script": "CLIスクリプト", "cli_session_timeout": "CLIセッションがタイムアウトしました", "confirm_selected_devices": "選択したデバイス", "description": "説明", "execute_cli_fail": "CLI スクリプトを実行できません", "execute_cli_object": "CLI スクリプトを実行する", "execute_cli_result_hint": "この画面を終了した場合は、[保存された CLI スクリプト] > [実行結果] から実行結果をダウンロードできます。", "execute_cli_results": "CLIの実行結果", "execute_cli_script": "CLIスクリプトを実行する", "failed": "失敗", "finished": "終了した", "in_progress": "進行中 ...", "ip": "IP", "model": "モデル", "name": "CLI スクリプト名", "no_cli_object_hint": "CLI スクリプトが見つかりません。 まず、保存された CLI スクリプトから CLI スクリプトを追加します", "not_sent": "送信されません", "result": "結果", "save_as_cli_object": "CLI スクリプトとして保存", "save_cli_object": "CLI スクリプトの保存", "select_cli_object": "CLI スクリプトの選択", "ssh_connection_timeout": "SSH 接続タイムアウト", "status": "状態"}, "firmware-management": {"action": "アクション", "add-task": "タスクの追加", "add-to-schedule": "計画されたアップグレード", "api-message": {"add-schedule-fail": "タスクをスケジュールできません", "add-schedule-success": "スケジュールされたタスク", "delete-schedule-fail": "チェック間隔スケジュールを削除できません", "delete-schedule-success": "チェック間隔スケジュールが正常に削除されました", "fm-downloaded": "ファームウェアファイルが正常にダウンロードされました", "fm-downloading": "ファームウェアファイルをダウンロードしています", "fm-ready": "ファームウェアファイルの準備ができました", "get-data-failed": "データを取得できません", "get-download-fm-failed": "ファームウェアファイルをダウンロードできません", "get-release-note-failed": "リリースノートを取得できません", "get-srs-status-failed": "Moxaファームウェアサーバーのステータスをクエリできません", "ignored-model-fail": "無視されるモデルのリストにモデルを追加できません"}, "check-Firmware-status": "ファームウェアのステータスを確認する", "check-interval": "チェック間隔", "check-now": "今すぐチェック", "connected": "接続済み", "description": "説明", "disconnected": "切断されました", "download-csv-report": "CSVレポートをダウンロード", "download-pdf-report": "PDFレポートをダウンロード", "execution-time": "時間", "firmware-upgrade-sequential": "ファームウェアのアップグレード (厳密なシーケンシャル)", "firmware-upgrade-smart-concurrent": "ファームウェアのアップグレード (スマート シーケンシャル)", "ignore-report": "レポートを無視する", "ignore-report-desc1": "レポートのダウンロードをスキップしてもよろしいですか?", "ignore-report-desc2": "このページを離れると、レポートをダウンロードできなくなります。", "ignored-models": "無視されたモデル", "last-update": "最終チェック日", "models": "モデル", "moxa-firmware-server-status": "Moxaファームウェアサーバーのステータス", "no-information": "情報はありません", "none": "なし", "offline-desc": "情報はありません。 ファームウェア更新サーバーへの以前の接続はありません。 デバイスがインターネットに接続されていることを確認して、もう一度試してください。", "proceeding-firmware-upgrade": "ファームウェアのアップグレードステータス", "proceeding-upgrade-result": "ファームウェアのアップグレード結果", "release-note": "リリースノート", "repeat-execution": "繰り返す", "retry-Failed-devices": "失敗したデバイスの再試行", "select-devices": "デバイスの選択", "select-firmware": "ファームウェアの選択", "set-upgrade-sequence": "アップグレードシーケンス", "sign-here": "サイン", "start-date": "日付", "status": {"failed": "失敗した", "finished": "終了した", "in-progress": "進行中", "waiting": "待っている"}, "table-header": {"alias": "エイリアス", "current-version": "現行版", "device-status": "デバイスのステータス", "ip": "IP", "latest-version-on-firmware-server": "ファームウェアサーバー上の最新バージョン", "model-series": "モデルシリーズ", "order": "注文", "selected-firmware-ready": "選択したファームウェアのダウンロードステータス", "selected-version": "選択したバージョン", "status": "状態"}, "task-name": "タスク名", "title": "ファームウェア管理", "turn-off-check-interval": "チェック間隔を無効にする", "turn-on-check-interval": "チェック間隔を有効にする", "unable-to-download-firmware": "ファームウェアのダウンロードに失敗しました", "update-mode": "アップデートモード", "upgrade-desc1": "更新シーケンスを決定できません。 まず、MXview One を実行しているコンピューターをトポロジに追加してください。", "upgrade-desc2": "現在のセットアップでは、デバイス ファームウェアの同時アップグレードのみがサポートされています。 Strict Sequential または Smart Sequential アップグレード方法を使用するには、まず MXview One を実行しているコンピューターをトポロジに追加する必要があります。", "upgrade-firmware-report": "ファームウェアのアップグレードレポート", "upgrade-now": "今すぐアップグレード", "upgrade-state-desc": "ファームウェアのアップグレードには時間がかかる場合があります。 アップグレードプロセスが完了するまでお待ちください。", "version": "バージョン"}, "general": {"common": {"action": "アクション", "allow": "許可する", "any": "任意の", "deny": "拒否", "description": "説明", "deviceInUse": "使用中のデバイス", "deviceName": "デバイス名", "disabled": "無効化", "enabled": "有効化", "endDate": "終了日", "filters": "フィルター", "firmwareVersion": "ファームウェアバージョン", "group": "グループ", "index": "索引", "ipAddress": "IPアドレス", "location": "場所", "mac": "MACアドレス", "name": "名称", "online": "オンライン", "options": "オプション", "productModel": "製品モデル", "profileInUse": "使用中のプロファイル", "refCount": "参考文献", "serialNumber": "シリアル番号", "startDate": "開始日", "status": "状態", "title": "タイトル"}, "dialog": {"deleteMsg": "選択した {{ item }} を削除してもよろしいですか?", "deleteTitle": "{{ item }} を削除", "isSelected": "{{ number }} 個のアイテムが選択されました", "title_system_message": "システムメッセージ", "unsaved_hint_content": "本当にこのページを離れますか?\n変更内容は保存されません。", "unsaved_hint_title": "保存せずに終了", "warning": "警告"}, "fileDrop": {"browse": "参照して", "dropText": "ファイルをここにドラッグアンドドロップするか、"}, "item_selected": "{{ number }} 個のアイテムが選択されました", "log": {"localStorage": "ローカルストレージ", "logDestination": "ログの保存先", "snmpTrapServer": "SNMP トラップ サーバー", "syslogServer": "Syslog サーバー", "title": "イベントログ"}, "menu": {"jump_page_placeholder": "Alt+Jを押してページをジャンプ"}, "page_state": {"application_error": "アプリケーションエラー：（", "application_error_desc": "このリクエストの処理中にエラーが発生しました。", "back_link": "インデックスページに戻る", "page_not_found": "ページが見つかりません：（", "page_not_found_desc": "リクエストされたURLがこのサーバーで見つかりませんでした。"}, "severity": {"alert": "アラート", "critical": "重大", "debug": "デバッグ", "emergency": "緊急", "error": "エラー", "high": "高", "information": "情報", "informational": "情報提供", "low": "低", "medium": "中間", "notice": "注意", "title": "重大度", "warning": "警告"}, "shortWeekday": {"fri": "金。", "mon": "月曜日。", "sat": "土曜", "sun": "太陽。", "thu": "木。", "tue": "火曜日", "wed": "水曜日。"}, "table": {"add": "追加", "delete": "削除", "download": "ダウンロード", "downloadAllLogs": "すべてのログをダウンロード", "edit": "編集", "filter": "フィルタ", "info": "情報", "more": "多くの", "permissionDenied": "アクセス拒否", "reboot": "再起動", "refresh": "更新", "reorderFinish": "並べ替えを完了する", "reorderPriority": "優先順位を変更する", "search": "検索", "transfer": "転送", "upgrade": "アップグレード"}, "top_nav": {"api_doc": {"title": "APIリファレンス"}, "hide_recent_event": "ナビゲーションメニューを隠す", "notifications": {"message_content": "イベントの内容", "message_readall": "その他の通知", "message_title": "イベントのタイトル", "notification_header": "通知"}, "show_recent_event": "ナビゲーションメニューを表示する", "user_profile": {"advanced_mode": "アドバンスドモード", "change_pwd": "パスワードの変更", "greeting": "こんにちは", "logout": "ログアウト", "manage_account": "アカウントの管理", "reset_factory_default": "工場出荷時のデフォルトにリセット", "restart_machine": "マシンの再起動", "search": "検索するキーワードを入力してください"}}, "topNav": {"caseInsensitive": "大文字と小文字を区別しない", "changePwd": "パスワードの変更", "changeSuccess": "パスワードは正常に更新されました。再度ログインしてください。", "confirmNewPwd": "新しいパスワードを確認", "currentPwd": "現在のパスワード", "invalidKey": "次の名前は予約されています: admin、operator、viewer、root、administrator、auditor", "logout": "ログアウト", "logoutMsg": "ログアウトしてもよろしいですか？", "newPwd": "新しいパスワード", "subject": "被験者", "troubleshoot": "トラブルシューティング", "troubleshootMsg": "トラブルシューティングのためにデバッグ ログをローカル ホストにエクスポートできます。", "updateAuthority": "更新権限", "updateSuccess": "アカウント権限が変更されました。再度ログインしてください。", "username": "ユーザー名"}, "unit": {"days": "日々）", "entries": "エントリ", "minute": "分", "minutes": "分）", "months": "月", "percent": "％", "pkts": "パケット/秒", "sec": "秒。", "seconds": "秒", "thousand": "千"}, "weekday": {"friday": "金曜日", "monday": "月曜日", "saturday": "土曜日", "sunday": "日曜日", "thursday": "木曜日", "tuesday": "火曜日", "wednesday": "水曜日"}}, "GLOBAL_MESSAGE": {"update_fail": "更新に失敗しました", "update_success": "更新成功"}, "GROUP_PROPERTIES": {"description": "説明", "devices": "デバイス（通常/警告/危機）", "information": "情報", "name": "名前", "title": "グループのプロパティ"}, "IMAGE": {"deviceSizeError": "最大画像サイズ：100KB", "error": "MXview Oneは、jpg、gif、pngフォーマットのみをサポートしています", "sizeError": "最大画像ファイルサイズは 1MB"}, "inventory_management": {"active": "有効", "alias": "エイリアス", "assets_list": "資産一覧", "available": "利用できる", "channel_extended_end_date": "チャネル延長保証終了日", "channel_extended_warranty_end_date_hint": "Moxa チャネル プロバイダーと延長保証契約を結んでいる場合は、ここで延長有効期限を手動で入力してください。", "check_warranty_manually": "保証を手動で確認する", "check_warranty_status": "保証ステータスを確認する", "days": "数日前", "email_example": "例@例.com", "email_to": "にメールする", "expire_soon": "まもなく期限切れ", "expired": "期限切れ", "firmware_version": "ファームウェアバージョン", "invalid_email_desc": "無効なメールアドレス", "ip": "IP", "last_update": "最終チェック日", "mac_address": "MACアドレス", "model": "モデル", "multiple_email_hint": "複数の受信者のメール アドレスをコンマで区切って追加できます。", "no_data": "該当なし", "notify_before": "リマインダーを送信", "retrieve_data": "データの取得", "select": "Select", "serial_number": "シリアルナンバー", "type": "で検索", "unable_query_warranty_server_status": "Moxa 保証サーバーにアクセスできません。", "unable_retrieve_warranty_information": "保証情報を取得できません。", "unavailable": "利用できません", "warranty_end_date": "保証終了日", "warranty_end_date_notification": "保証期限通知", "warranty_management": "保証管理", "warranty_notification": "保証通知", "warranty_period": "保証期間", "warranty_server_status": "Moxa 保証サーバーのステータス", "warranty_start_date": "保証開始日", "warranty_status": "保証状況"}, "INVENTORY_REPORT": {"alias": "エイリアス", "filter": "インベントリレポートをフィルターするタイプ", "fw_version": "ファームウェアバージョン", "ip_address": "IPアドレス", "mac": "MACアドレス", "model": "モデル", "property": "プロパティ", "report_generate_day": "レポート生成日：", "site_name": "サイト名", "system_desc": "システムの説明", "title": "インベントリレポート", "value": "値"}, "IP_CONFIGURATION": {"auto_ip": "自動IP", "change_ip_fail": "IP設定を行うことができません", "change_ip_success": "デバイスIP設定が更新されました", "gateway": "ゲートウェイ", "hint": "この機能は、レイヤー3デバイスでは使用できません。", "ip_address": "IPアドレス", "netmask": "ネットマスク", "title": "IP設定"}, "ip_conflict_detected_notification": "IP 競合が検出されました", "ip_conflict_recovery_notification": "IP 競合が解決されました", "ips_configuration": {"dialog-title": "IPS設定", "execute_fail": "実行に失敗しました", "input-ips": "IPS", "option-detection-mode": "検出モード", "option-prevention-mode": "予防モード", "selet-ips-operation-mode": "IPS動作モード", "th-execution-status": "実行ステータス"}, "IPSEC": {"connection_name": "接続名", "ipsec_status_phase1": "IPSecステータスフェーズ1", "ipsec_status_phase2": "IPSecステータスフェーズ2", "local_gateway": "ローカルゲートウェイ", "local_subnet": "ローカルサブネット", "remote_gateway": "リモートゲートウェイ", "remote_subnet": "リモートサブネット"}, "IW": {"Message": {"CONNECT_TIMEOUT": "接続タイムアウト", "ERROR_OCCURRED": "サーバーエラー。しばらくしてからもう一度お試しください。", "FAILED": "失敗しました", "LOAD_DATA_FAILED": "データのロード中にエラーが発生しました！", "RSSI_SNR_ONLY": "（信号強度とSNRのみ）", "SET_SUCCESS": "設定が正常に変更されました", "SUCCESSED": "成功しました", "UPDATE_FAILED": "データの更新に失敗しました！"}, "Title": {"ap": "AP", "AUTO_REFREASH": "自動更新： ", "AUTO_REFRESH": "自動更新", "BSSID": "BSSID", "channel": "Channel", "client": "クライアント", "client_count": "クライアント数", "client_router": "Client-Router", "CLOSE": "閉じる", "COLOR": "色", "COLUMN": "パラメータ列", "CONDITIONS": "条件", "CONN_TIME": "接続タイム（秒）", "connected": "接続済み", "DEVICE_NAME": "デバイス名", "disable": "Disable", "ENABLE": "有効化", "FILTER_TABLE_VIEW": "フィルターテーブルビュー", "hint": "This page will be removed in a future product release. Please use Wireless Add-on in MXview One instead.", "IP_ADDR": "IPアドレス", "link_speed": "リンク速度", "MAC": "MACアドレス", "master": "主人", "MODULATION": "変調", "noise_floor": "Noise Floor", "noise_floor_unit": "Noise Floor (dBm)", "OK": "OK", "ONLINE": "オンライン", "operation_mode": "運行モード", "RSSI": "信号強度（dBm）", "security_mode": "セキュリティモード", "signal_level": "Signal Level", "slave": "奴隷", "SNR": "SNR (dB)", "SNR_A": "SNR-A (dB)", "SNR_B": "SNR-B (dB)", "ssid": "SSID", "TOTAL_AP": "AP数： ", "TOTAL_CLIENT": "クライアント数： ", "tx_power": "TX パワー", "tx_power_unit": "TX パワー (dBm)", "tx_rate": "TX レート", "tx_rate_unit": "TX レート (Mb/s)", "uptime": "稼働時間", "VALUE": "値", "WIRELESS_TABLE_VIEW": "ワイヤレステーブルビュー"}}, "JOB_SCHEDULER": {"add_failed": "ジョブの追加に失敗しました", "add_success": "新しいジョブが追加されました", "add_title": "新しいジョブの追加", "alias": "エイリアス", "auto_topology": "自動トポロジー", "cli_object_name": "CLI スクリプト名", "config_file": "設定ファイル", "config_file_error": "MXview Oneは.iniフォーマットのみをサポートしています", "config_file_size_error": "Maximum file size is 1 MB", "current_filename": "現在のファイル名", "current_version": "現行版", "daily": "毎日", "database_backup": "データベースのバックアップ", "delete_failed": "ジョブの削除に失敗しました", "delete_success": "ジョブが削除されました", "description": "説明", "edit_title": "ジョブの編集", "excute_cli_object": "保存したスクリプトを実行する", "execution_time": "実行時間", "export_configuration": "設定のエクスポート", "filter": "ジョブをフィルターするタイプ", "fm_sequential": "厳密なシーケンシャル", "fm_smart": "スマートシーケンシャル", "friday": "金曜日", "import_configuration": "設定のインポート", "ip": "IP", "job_action": "アクション", "job_log": "ジョブログ", "job_name": "ジョブ名", "model_series": "モデルシリーズ", "monday": "月曜日", "monthly": "毎月", "on": "オン", "once": "1回", "order": "注文", "registered_devices": "登録済みデバイス", "repeat_execution": "繰り返し実行", "saturday": "土曜日", "schedule_time": "スケジュール時間", "selected_version": "選択したバージョン", "show_log_fail": "ログの表示に失敗しました", "show_log_not_found": "ログを表示できません", "start_date": "開始日", "sunday": "日曜日", "thursday": "木曜日", "title": "メンテナンススケジューラー", "tuesday": "火曜日", "update_failed": "ジョブの更新に失敗しました", "update_success": "ジョブが更新されました", "wednesday": "水曜日", "weekly": "毎週"}, "LANG": {"de-DE": "De<PERSON>ch", "en-US": "English", "es-ES": "Español", "fr-FR": "Français", "ja-JP": "日本語", "ru-RU": "Русский язык", "zh-CN": "中文(简体)", "zh-TW": "中文(繁體)"}, "LICENSE": {"activation_code": "アクティベーションコード", "activation_code_error": "無効なアクティベーションコード", "activation_title": "アクティベーション", "active": "アクティブ化", "active_desc_1": "訪問", "active_desc_2": "http://license.moxa.com", "active_desc_3": "にアクセスし、製品コードとユーザーコードを入力してアクティベーションコードを取得してください。", "add_fail": "ライセンスを追加できません", "add_license": "ライセンスを追加してください", "add_new_license_desc": "ここでライセンスを追加できます", "add_new_license": {"activate": "アクティブ化", "activate_intro_link": "Moxaライセンスサイト", "activate_intro_pre": "ライセンスは以下からダウンロードしてください", "activate_intro_suf": "からライセンスをダウンロードし、アクティベーションコードをここに貼り付けてください。", "copy_user_code": "ユーザーコードをコピー", "copy_user_code_intro_link": "Moxaライセンスサイト", "copy_user_code_intro_pre": "ユーザーコードを", "copy_user_code_intro_suf": "にコピーしてください。", "license_site_step_1_link": "Moxaライセンスサイト", "license_site_step_1_pre": "1. ", "license_site_step_1_suf": "にログインしてください。", "license_site_step_2": "2. サイトの「 Activate Your License 」および「 MXview One 」を選択してください。", "license_site_step_3": "3. 登録コード", "license_site_step_3_Free_step": "次のステップに進む。", "license_site_step_3_Free_title": "無料バージョンユーザー：", "license_site_step_3_Full_step": "Moxaライセンスサイトで登録コードとユーザーコードを入力してください。ユーザーコードは次のステップに進みます。", "license_site_step_3_Full_title": "フルライセンスユーザー：", "login_license_site": "Moxaライセンスサイトにログイン", "select_network_adapter": "ネットワークアダプターを選択", "title": "新しいライセンスを追加"}, "add_success": "ライセンスを正常に追加", "copied_to_clipboard": "クリップボードにコピーされました", "copy_deactivation_code": "非アクティベーションコードのコピー", "copy_older_license_code": "2.xライセンスコードをコピーしてください", "current_nodes": "現在のノード数:", "deactivate": "非アクティブ化", "deactivate_fail": "ライセンスを非アクティブ化できません", "deactivate_success": "ライセンスを正常に非アクティブ化", "deactivated_licenses": "無効化されたライセンス", "deactivating": "非アクティブ化中...", "deactivation_code": "非アクティベーションコード", "deactivation_desc": "ライセンスは非アクティブ化後に無効になります。このライセンスを非アクティブ化してもよろしいですか？", "deactivation_title": "非アクティベーション", "disabled": "無効化", "duration": "期間", "enabled": "有効化", "expired_license": "ライセンスの有効期限が切れています", "free_trial": "無料トライアル", "free_trial_desc": "MXview Oneのパワーの体験を開始する", "import_license_file": "ライセンスファイルのインポート", "license": "ライセンス：", "license_authorized": "認可済み", "license_free": "無料ライセンス", "license_none": "なし", "license_site": "Moxaライセンスサイト", "license_start": "ライセンスの開始", "license_title": "ライセンス", "license_trial": "トライアル", "license_type": {"node_base_intro": "MXview Oneがネットワーク上で監視できるデバイスの台数を指定します。", "node_base_title": "ノードベースライセンス", "power_addon": "パワーアドオンライセンス", "power_intro": "より多くの電源関連機能を利用できるようになります。", "security_addon": "セキュリティアドオンライセンス", "security_intro": "ユーザーが追加のセキュリティ関連機能にアクセスできるようにします。", "title": "ライセンスのタイプ", "trial_intro": "90日間で、MXview Oneのパワーを実感してください。", "trial_title": "試用ライセンス", "wireless_addon": "ワイヤレスアドオンライセンス", "wireless_intro": "より多くのワイヤレス関連機能にアクセスできるようになります。"}, "licensed_node": "ライセンス済みノード", "licensed_nodes": "ライセンス済みノード数:", "licenses": "ライセンス", "managed_by_central": "The License is managed by MXview One Central", "managed_by_central_licenses_invalidated": "No valid licenses, please check the status in Control Panel.", "mxview": "MXview One", "network_adapter": {"button": "アダプターを選択", "change_network_adapter": "ネットワークアダプターを変更", "change_network_adapter_alert_1": "ネットワークアダプターを変更してもよろしいですか？", "change_network_adapter_alert_2": "「確認」をクリックすると、すべてのライセンスが非アクティブ化されます。新しいネットワークアダプターによって新しいライセンスを登録するまで、MXview Oneを使用できません。", "intro": "MXview Oneはライセンスを1つのネットワークアダプターにバインドします。バインドするアダプターを選択してください。ネットワークアダプターを再度選択すると、すべてのライセンスが自動的に非アクティブ化されます。この場合、再度登録が必要になります。", "select_adapters": "ネットワークアダプターを選択", "select_adapters_desc": "ネットワークアダプターを選択してください。MXview Oneはそれを使用してユーザーコードを生成します。", "title": "ネットワークアダプター"}, "node": "現在のノード数/ライセンス済みノード数：", "nodes": "ノード", "older_license": "2.xライセンス", "older_license_nodes": "2.xノード", "over_nodes_desc": "監視対象ノード数がサポートされているライセンス数を超えているため、ログアウトしています。", "over_nodes_title": "警告", "power_addon_trial": "MXview OneのPower Add-onを体験してください。", "reactivate_license": {"activate": "アクティブ化", "activate_intro_link": "Moxaライセンスサイト", "activate_intro_pre": "からライセンスをダウンロードします", "activate_intro_suf": "からライセンスをダウンロードし、アクティベーションコードをここに貼り付けてください。", "copy_deactivate_code": "非アクティブ化コードをコピー", "copy_deactivate_code_intro_link": "Moxaライセンスサイト", "copy_deactivate_code_intro_pre": "非アクティベーションコードをコピーし、", "copy_deactivate_code_intro_suf": "に貼り付けてください。", "copy_user_code": "ユーザーコードをコピー", "copy_user_code_intro_link": "Moxaライセンスサイト", "copy_user_code_intro_pre": "ユーザーコードを", "copy_user_code_intro_suf": "にコピーしてください。", "intro": "非アクティベーションコードとユーザーコードの両方を使用して、ライセンスを再度アクティブ化します。", "license_site_step_1_link": "Moxaライセンスサイト", "license_site_step_1_pre": "1. ", "license_site_step_1_suf": "にログインしてください。", "license_site_step_2": "2. サイトの「 MXview One Deactivation 」および「 Transfer to another device 」をクリックします。", "license_site_step_3": "3. ソフトウェア製品からMXview Oneを選択してください。", "login_license_site": "Moxaライセンスサイトにログイン", "title": "ライセンスを再度アクティブ化", "title_abbr": "再度アクティブ化"}, "reason": "ステート", "relaunch": {"activating": "ライセンスを啓用中です，しばらくお待ちください。", "active_note": "啓用作業は10秒ほど完成します。"}, "remain": "残った", "security_addon_trial": "MXview One セキュリティアドオンを体験してみましょう", "select_network_interface": "ネットワークインターフェイスの選択", "site_license_invalid": "一部のサイトで無効なライセンスがあります", "site_license_invalid_title": "ライセンスが無効です", "start_free_trial": "試用開始", "start_free_trial_fail": "無料トライアルの開始に失敗しました", "start_free_trial_success": "無料トライアルを正常に開始", "state": "ステート：", "state_all_licenses_invalidated": "有効なライセンスなし", "state_cannot_add_free_license": "完全なライセンスを所有している場合は無料ライセンスを追加できません", "state_cannot_add_multiple_free_licenses": "複数の無料ライセンスの追加はできません", "state_format_incorrect": "ライセンスファイルのフォーマットが正しくありません", "state_general_error": "一般的なエラー", "state_license_deactivated": "ライセンスはすでに無効化されています", "state_license_expired": "ライセンスの期限が切れました", "state_license_is_registered": "ライセンスはすでにシステムに登録されています", "state_license_not_found": "ライセンスが見つかりません", "state_license_over_2000": "ライセンスが2000ノードを超えています。これはMXview Oneで使用できる最大量です。", "state_license_upgrade_error": "アップグレードライセンスを追加できません。先に少なくとも1つのフルライセンスを登録してください", "state_license_upgrade_no_full_license ": "ライセンスを解除できません，先にすべてのアップグレードライセンスを外すしてください。", "state_no_full_license": "有効なMXview Oneライセンスがありません", "state_no_usercode": "ユーザーコードなし", "state_over_nodes": "導入するデバイスの量に対して十分なノードが使用できないため、追加のノードを購入してください。 ", "state_trial_expired": "トライアル期限切れ", "state_trial_is_began": "トライアルはすでに開始しています", "state_trial_not_activated": "トライアルはまだアクティブ化されていません", "state_usercode_deactivated": "ユーザーコードが無効化されています", "state_usercode_exists": "ユーザーコードはすでに存在します", "state_usercode_not_match": "ライセンスのユーザーコードがシステムのユーザーコードと一致しません", "state_usercode_not_match_adapter": "ユーザーコードにバインドされたネットワークアダプタが見つかりません", "title": "ライセンスマネージャー", "trial_button": "トライアル", "trial_day": "日", "trial_expired": "トライアル期限切れ", "trial_over_nodes": "十分なライセンスを追加しない場合、または使用ノードを削除した場合、30分後にMXview Oneがロックされます。", "trial_remaining": "トライアル残り", "user_code": "ユーザーコード：", "valid": "有効", "wireless_addon_trial": "アドオンの体験を開始する"}, "link_list": {"rx": "RX (%)", "tx": "TX (%)"}, "LINK_PROPERTIES": "リンクのプロパティ", "LOGIN": {"account_reach_limit": "ログインアカウント制限（10）を超えています", "all_sites_offline": "すべてのサイトがオフラインです", "default_password_warning": "より高いセキュリティレベルを考慮してデフォルトのパスワードを変更してください", "error": "ユーザー名/パスワードが無効です", "ie_not_supported": "MXview OneはIEをサポートしていません。最高の体験を得るために、Google Chromeを使用してください。", "last_login_fail": "最終ログイン失敗記録", "last_login_succeed": "最終ログイン成功時刻", "login_fail_time": " {{loginIp}}による{{loginTime}}", "login_succeed_time": " {{loginIp}}による{{loginTime}}", "logout": "ログアウト", "password": "パスワード", "password_policy_mismatch": "パスワードがパスワードポリシーと一致しない", "sign_in": "サインイン", "username": "ユーザー名", "welcome": "ようこそ"}, "max_char": "最大 {{num}} 文字", "min_char": "最小 {{num}} 文字", "model-port-mapping": {"port": "ポート", "web-ui": "デバイス上のWeb UI"}, "MXVIEW_WIZARD": {"complete_page_title": "完了", "navigate_to_wizard_page": "MXview Oneセットアップウィザードを使用しますか", "step_add_scan_range": "スキャン範囲の追加", "step_auto_topology": "トポロジーの描画（LLDPをサポートするデバイスを対象）", "step_create_group": "グループの作成", "step_select_site": "設定するサイトを選択してください", "step_set_snmp": "SNMP設定のセット", "step_set_trap_server": "SNMPトラップサーバーの設定", "title": "セットアップウィザード", "welcom_page_title": "セットアップウィザードへようこそ"}, "NETWORK_MENU": {"add_link": "リンクの追加", "add_wifi_ssid": "Wi-Fi SSIDを追加する", "alignment": {"bottom": "下揃え", "left": "左揃え", "right": "右揃え", "title": "整列", "top": "上揃え"}, "copy_device_list": "デバイスリストのコピー", "create_a_snapshot": "スナップショットの作成", "cybersecurity_control": "ネットワークセキュリティ制御", "delete": "削除", "device_configuration": "デバイス構成", "device_control": "機器制御", "device_dashboard": "デバイスダッシュボード", "device_login_account": "デバイスアカウント", "device_panel": "デバイスパネル", "device_wireless_settings": "Per-device Parameters", "disable_unsecured_http_and_telnet_console": "安全でない HTTP および Telnet コンソールを無効にする", "disable_unused_ethernet_and_fiber_ports": "未使用のイーサネットおよび光ファイバーポートを無効にする", "document": {"menu": {"open": "開く", "set": "設定"}, "title": "ドキュメント"}, "dynamic_mac_sticky": "ダイナミックSticky MAC", "edit": {"menu": {"add_device": "デバイスの追加", "delete_background": "背景の削除", "export_device_list": "デバイスリストのエクスポート", "export_topology": "トポロジーのエクスポート", "import_device_list": "デバイスリストのインポート", "set_background": "背景の設定"}, "title": "編集"}, "execute_cli": {"menu": {"execute_cli_object": "保存したスクリプトを実行する", "execute_cli_script": "CLI スクリプト"}, "title": "スクリプトの実行"}, "grid": {"menu": {"import_scd": "SCDのインポート"}, "title": "パワー"}, "group": {"menu": {"change_group": "グループの変更", "create_group": "グループの作成", "group_maintenance": "グループメンテナンス"}, "title": "グループ"}, "grouping": "グループ", "ips_configuration": "IPS設定", "ipsec_status": "IPsecのステータス", "link_traffic": {"menu": {"packet_error_rate": "パケットエラー率", "port_traffic": "ポートトラフィック"}, "title": "リンクトラフィック"}, "locator": "ロケータ", "mac_sticky_on_off": "Sticky MACのオン/オフ", "maintenance": {"menu": {"advance_settings": "詳細設定", "assign_model": "モデルの割り当て", "basic_information": "基本情報", "change_device_icon": "デバイスアイコンの変更", "device_identification_settings": "デバイス識別設定", "eip_enable": "EtherNet/IP 有効化", "eip_tcp_port": "EtherNet/IP TCP ポート", "eip_udp_port": "EtherNet/IP UDP ポート", "export_config": "設定のエクスポート", "generate_qr_code": "QRコードの生成", "import_config": "設定のインポート", "ip_configuration": "IP設定", "modbus_enable": "Modbus の有効化", "modbus_port": "Modbus ポート", "modbus_tcp_configuration": "Modbus TCP 設定", "polling_ip_setting": "ポーリングIP設定", "polling_settings": "MXview One ポーリング間隔", "port_settings": "イーサネット/光ファイバーポートの設定", "s7_port": "シーメンス S7comm ポート", "s7_status": "シーメンスS7comm有効", "serial_port_monitoring": "シリアルポート監視", "snmp_settings": "SNMP通信プロトコル", "trap_server": "トラップサーバー", "upgrade_firmware": "ファームウェアのアップグレード"}, "title": "メンテナンス"}, "modify_device_alias": "デバイスエイリアス", "policy_profile_deployment": "ポリシープロファイルの展開", "reboot": "再起動", "refresh": "更新", "restart_mac_sticky_learning": "ダイナミックSticky MAC を再学習する", "restore_to_created_snapshot": "スナップショットから復元", "scale": "Scale", "security_package_deployment": "セキュリティ パッケージの展開", "set_port_label": "ポートラベルの設定", "severity_threshold": "重要度しきい値", "sfp": "SFP", "sfp_Info": "SFP 情報", "sfp_list": "SFP リスト", "sfp_sync": "デバイスからのしきい値の同期", "tools": {"menu": {"device_panel": "デバイスパネル", "mib_browser": "MIBブラウザ", "ping": "<PERSON>", "telnet": "Telnet", "web_console": "ウェブコンソール"}, "title": "ツール"}, "topology": {"menu": {"auto_layout": "自動レイアウト", "auto_topology": "自動トポロジー", "embed": "ウィジェットの埋め込み", "scan_range": "デバイス発見"}, "title": "トポロジー"}, "ungrouping": "グループを解除する", "upgrade_patch": "システムアップデートを適用する", "visualization": {"menu": {"igmp": "IGMP", "security_view": "セキュリティビュー", "traffic_view": "トラフィックビュー", "vlan": "VLAN", "vlan_view": "Vlanビュー"}, "title": "可視化"}, "wifi_channel_change": "Wi-Fiチャンネルを変更する", "wireless_settings": "General Parameters", "wireless": {"menu": {"wireless_planner_view": "推定信号カバー範囲", "wireless_playback_view": "ローミング履歴再生", "wireless_table_view": "ワイヤレスデバイス一覧"}, "title": "ワイヤレス"}}, "NETWORK": {"current_status": {"no_event": "イベントなし", "title": "現在のステータス", "v3_trap_event_clear_fail": "V3トラップイベントの消去に失敗しました", "v3_trap_event_suggestion": "snmp v3の構成を確認してください"}, "not_selected": "デバイスの詳細を表示するにはモジュールを選択してください"}, "NORTHBOUND_INTERFACE": {"custom_opc": {"add_custom_opc": "カスタムOPCタグの追加", "all": "すべて", "apply_fail": "カスタムOPCタグを追加できません", "apply_success": "新しいカスタムOPCタグが追加されました", "delete_fail": "OPCタグを削除できません", "delete_success": "OPCタグが削除されました", "device_properties": "デバイスのプロパティ", "enable": "カスタムOPCタグが有効になっています", "filter_custom_opc": "タイプしてカスタムOPCタグをフィルターする", "get_fail": "カスタムOPCタグを取得できません", "property_name": "プロパティ名", "register_devices": "デバイスの登録", "title": "カスタムOPCタグ", "update_custom_opc": "カスタムOPCタグの更新", "update_fail": "OPCタグを更新できません", "update_success": "カスタムOPCタグが更新されました"}}, "NOTIFICATION_SETTINGS": {"action": "アクション", "action_cant_deleted": "このアクションは、1つ以上の通知を使用しています。これらの通知からアクションの選択を解除してもう一度削除してください", "action_information": "アクション情報", "action_name": "アクション名", "action_tab_hint": "アクションタブに移動し、まずアクションを追加してください", "action_type": "タイプ", "add_action": "通知アクションの追加", "add_action_fail": "アクションを追加できません", "add_action_success": "新しいアクションが追加されました", "add_notification": "通知の追加", "add_notification_fail": "通知を追加できません", "add_notification_success": "新しい通知が追加されました", "check_security_tab": "サイバーセキュリティタブを確認する", "content": "内容", "delete_action_fail": "アクションを削除できません", "delete_action_success": "アクションが削除されました", "delete_notification_fail": "通知を削除できません", "delete_notification_success": "通知が削除されました", "edit_action": "通知アクションの編集", "edit_notification": "通知の編集", "email": "電子メール", "email_content_hint": "この内容は、デフォルトの通知メールの本体に添えられる。", "event_type": "タイプ", "file_size_error": "最大ファイルサイズは1MBです。", "file_type_error": "MXview Oneは.wavファイルのみをサポートしています", "filter_action": "タイプしてアクションをフィルターする", "filter_notification": "タイプして通知をフィルターする", "messagebox": "メッセージボックス", "mobile": "MXview ToGo", "mobile_number": "携帯電話番号", "notification": "通知", "notification_name": "通知名", "notification_name_exist": "この名前は他の通知で既に使用されています", "receiver_email": "受信者の電子メール", "register_devices": "登録済みデバイス", "register_subscribers": "登録済みアクション", "slack": "<PERSON><PERSON>ck", "sms": "SMS", "snmptrap": "SNMPトラップ", "sound": "音声ファイル", "teams": "Microsoft Teams", "testConnection": "接続テスト", "title": "通知設定", "update_action_fail": "アクションを更新できません", "update_action_success": "アクションが更新されました", "update_notification_fail": "通知を更新できません", "update_notification_success": "通知が更新されました", "webhook": "Webhook", "webhook_fail": "Webhookを実行できません ", "webhook_success": "Webhook が送信されました"}, "OPC_UA_SERVER": {"add_opc_tags": "OPCタグを追加", "anonymous": "匿名の", "auth_setting": "認証設定", "certificate": "証明書", "certificate_link": "証明書をダウンロードして管理するには、", "change_authentication_password": "認証パスワードの変更", "change_password": "パスワードを変更する", "control_panel": "MXview One コントロールパネル", "create_tags_fail": "Unable to add tag", "create_tags_success": "タグが正常に作成されました", "delete_tag_content": "この OPC タグを削除してもよろしいですか?", "delete_tags": "OPC タグの削除", "delete_tags_content": "これらの OPC タグを削除してもよろしいですか?", "delete_tags_fail": "タグを削除できませんでした", "delete_tags_success": "タグが正常に削除されました", "device_property": "Device Property", "disabled": "無効化", "edit_opc_tags": "OPC タグの編集", "edit_tags_fail": "タグの更新に失敗しました", "edit_tags_success": "タグが正常に更新されました", "enable_opc_server": "OPC UAサーバーを有効にする", "enabled": "有効化", "exceed_server_performance": "登録済みデバイスの最大数 (4000) に達しました。", "get_tags_list_fail": "タグリストを取得できませんでした", "ip_domain_name": "IP/ドメイン名", "method": "方法", "opc_tags": "OPC Tags", "property_name": "Device Property", "registered_device": "Registered Device", "security": "セキュリティモード", "security_placeholder": "セキュリティを許可しない", "server_settings": "Server Settings", "status": "status", "support_security_policy": "サポートされているセキュリティポリシー", "tag_name": "Tag Name", "tag_name_duplicate": "このタグ名は既に存在します", "tags_exceed_limit": "タグの最大数 (2000) に達しました。", "title": "OPC UA Server", "update_fail": "設定の更新に失敗しました", "update_server_setting_fail": "サーバー設定の更新に失敗しました", "update_server_setting_fail_no_up": "設定の更新に失敗しました。指定された IP は存在しません。", "update_server_setting_success": "サーバー設定が正常に更新されました", "username": "ユーザー名"}, "PAGES_MENU": {"about": "概要", "administration": {"account_management": "アカウント管理", "device_settings_template": "デフォルトのデバイステンプレート", "global_device_settings": "グローバルデバイス設定", "license_management": "ライセンス管理", "maintenance_scheduler": "メンテナンススケジューラー", "preferences": "環境設定", "system_settings": "システム設定", "title": "管理", "troubleshooting": "トラブルシューティング"}, "alert": {"custom_events": "Custom Events", "device_threshold": "<PERSON><PERSON>", "event_settings": "イベント設定", "link_threshold": "<PERSON>", "notifications": "通知管理", "title": "アラート "}, "cli_object_database": {"title": "保存された CLI スクリプト"}, "dashboard": "ダッシュボード", "device_management": {"account_password": "アカウントとパスワード", "configuration_control": "構成と制御", "title": "端末管理"}, "devices": {"device_configurations": "Device Configurations", "list_of_devices": "List of Devices"}, "event": {"all_events": "イベント履歴", "custom_events_management": "カスタマイズされたイベント", "notification_management": "通知管理", "syslog_settings": "Syslog設定", "syslog_viewer": "Syslogビューア", "title": "イベント管理"}, "firewall_policy_management": {"dos_descr": "DoSポリシーを構成する", "ips_descr": "IPSポリシーの設定", "layer3to7_descr": "レイヤー3-7ファイアウォールポリシーを構成する", "policy_profile_deployment": "ポリシープロファイルの展開", "policy_profile_management": "ポリシープロファイル管理", "security_package_deployment": "セキュリティ パッケージの展開", "security_package_management": "セキュリティパッケージ管理", "sessionControl_descr": "セッション制御ポリシーを構成する", "title": "ファイアウォールポリシー管理"}, "firmware_management": {"title": "ファームウェア管理"}, "help": {"about_mxview": "MXview Oneについて", "api_documentation": "API ドキュメント", "title": "ヘルプ", "user_manual": "ユーザーマニュアル"}, "license": "ライセンス", "links": {"list_of_rj45_links": "List of RJ45 Links", "list_of_sfp_links": "List of SFP Links", "list_of_wifi_links": "List of Wi-Fi Links", "title": "Links"}, "migrations": {"configuration_center": "デバイス設定センター", "database_backup": "データベースのバックアップ", "job_scheduler": "メンテナンススケジューラー", "title": "移行"}, "network": {"scan_range": "スキャン範囲", "title": "ネットワーク", "topology": "トポロジー", "wizard": "ウィザード"}, "northbound_interface": {"custom_opc_tags": "カスタムOPCタグ", "opc_ua_server": "OPC UA Server", "restful_api_management": "RESTful API管理", "title": "統合", "web_widget_embedded": "埋め込みWebウィジェット"}, "preferences": "環境設定", "report": {"assets_and_warranty": "資産と保証", "availability_report": "可用性レポート", "inventory_report": "インベントリレポート", "rogue_device_detection": "不正デバイスの検出", "title": "レポート", "vlan": "VLANレポート"}, "scan_range_wizard": {"title": "デバイス発見"}, "security": {"account_management": "アカウント管理", "security_analyser": "セキュリティアナライザー", "title": "セキュリティ"}}, "pages": {"deviceDeployment": {"alreadySentSms": "今月は既に {{ smsNumber }}/{{ max }} 件の SMS を送信しました", "applied": "適用", "atLeastSelectOne": "少なくとも1つのSMS制御コマンドを選択してください", "cellularModuleDisable": "セルラー モジュールが無効になっているため、SMS コマンドの送信は許可されません。", "cellularStartConnecting": "セルラー接続開始", "cellularStopConnecting": "携帯電話の接続停止", "configSync": "選択したデバイスの構成が同期されます。", "daily": "毎日", "date": "データ", "deleteMsg": "選択したデバイスを削除してもよろしいですか?", "deleteSchedule": "スケジュールを削除", "deleteScheduleSuccess": "デバイスのスケジュールが正常に削除されました", "deleteSuccess": "デバイスが正常に削除されました", "deleteTitle": "デバイスを削除", "device_ip": "デバイスIP", "deviceConfiguration": "デバイス構成", "deviceDetail": "デバイスの詳細", "deviceDisableHint": "デバイス側から機能が無効になっている", "deviceSelected": "選択されたデバイス", "endTime": "終了日", "firmware": "ファームウェア", "firmwareUpgrade": "選択したデバイスのファームウェアがアップグレードされます。", "firmwareVersion": "ファームウェアバージョン", "general": "一般的な", "groupName": "グループ名", "groupSelected": "選択されたグループ", "invalidDate": "無効な日付", "invalidPeriod": "無効な期間", "lastRebootTime": "最終再起動時間", "lastUpdate": "最終チェック日", "lastUpdateTime": "最終更新時間", "location": "場所", "mac": "MAC", "manually": "手動で", "maxSms": "月間 SMS 制限 (最大 {{ max }}) に達しました", "noConfigAvailable": "利用可能な構成はありません", "noConfigMsg": "管理/デバイス構成ページで構成を確認します。", "noFirmwareMsg": "管理/ファームウェア ページでファームウェア ファイルを確認します。", "noPackageMsg": "セキュリティ パッケージ管理ページでセキュリティ パッケージを確認します。", "noProfileAvailable": "利用可能なプロフィールはありません", "noProfileMsg": "ポリシー プロファイル管理ページでプロファイルを確認します。", "notSupportModel": "サポートされていないモデル", "notSync": "同期されていません", "noVersionAvailable": "利用可能なバージョンはありません", "oneTime": "一度", "outOfSync": "同期していない", "package": "パッケージ", "packageUpgrade": "選択したデバイスのセキュリティ パッケージがアップグレードされます。", "packageVersion": "パッケージバージョン", "period": "期間", "policyProfile": "政策プロファイル", "processing": "処理", "profileName": "プロファイル名", "profileSync": "選択したデバイスのプロファイルが同期されます。", "reboot": "デバイスが再起動されます。", "rebootDisabled": "再起動できるのはオンラインデバイスのみです。", "rebootMsg": "選択したデバイスを再起動してもよろしいですか?", "rebootTitle": "デバイスを再起動する", "remoteSmsControl": "リモートSMSコントロール", "restoreConfigDisabled": "同期できるのは、同じモデルタイプのオンラインデバイスのみです。", "sameVersionWarning": "選択したデバイスの 1 つ以上には、バージョン {{ version }} がすでに適用されています。", "schedule": "スケジュール", "scheduleDisabled": "スケジュールできるのは、同じモデルタイプのデバイスのみです。", "scheduleOverlapMsg": "再起動またはファームウェアのアップグレードがすでに割り当てられている時間スロットを選択することはできません。", "scheduleSettings": "スケジュール設定", "scheduling": "スケジューリング", "schedulingMode": "スケジュールモード", "schedulingPeriod": "スケジュール期間", "schedulingReboot": "再起動のスケジュール", "selectConfigFile": "設定ファイルを選択", "selectFile": "ファイルを選択", "sendSms": "SMSを送信", "sendSmsControl": "SMSコントロールの送信", "sendSmsOnCell": "SMSコントロールを送信するOnCellデバイスを1つ選択", "sendSmsSuccess": "SMS の送信に成功しました", "serialNumber": "シリアル番号", "setDoOff": "DOをオフに設定", "setDoOn": "DOをオンに設定", "shouldBeSameVersion": "選択したデバイスのパッケージ バージョンは同じである必要があります。", "shouldHaveJanus": "選択したデバイスの 1 つ以上にセキュリティ パッケージがインストールされていません。", "shouldSyncOnline": "同期できるのはオンラインデバイスのみです。", "showAll": "すべてのグループとデバイスを表示", "showSelected": "選択したグループとデバイスを表示", "smsCountDownHint": "60秒後に次のSMSを送信します", "softwarePackage": "セキュリティパッケージ", "startIpsecTunnel": "IPSecトンネルを開始する", "startTime": "開始日", "status": "状態", "statusProfileName": "ステータス / プロフィール名", "stopIpsecTunnel": "IPSecトンネルを停止する", "switchSim": "SIMの切り替え", "sync": "同期済み", "syncConfig": "同期設定", "syncConfigTitle": "設定をデバイスに同期する", "syncModified": "同期済み（変更済み）", "syncProfile": "プロファイルを同期", "syncProfileTitle": "プロファイルをデバイスに同期する", "systemRestart": "システムの再起動", "time": "時間", "updateScheduleSuccess": "デバイススケジュールが正常に更新されました。", "upgradeDisabled": "アップグレードできるのはオンラインデバイスのみです。", "upgradePackageError": "ファームウェア バージョン 2.5.0 以上と 2.4.x 未満は共存できません。", "upgradePackageNotSameDisabled": "同じモデルタイプのデバイスのみ選択できます", "upToDate": "最新の", "version": "バージョン", "weekly": "毎週", "weeklyDay": "週日"}, "logging": {"eventLog": {"adp": "ADP", "audit": "監査", "device": "デバイス", "dos": "DoSポリシー", "dpi": "プロトコル フィルター ポリシー", "endDate": "終了日", "endTime": "終了時間", "event": "タイプ", "firewall": "ファイアウォール", "ips": "IPS", "l2Policy": "レイヤー2ポリシー", "l3Policy": "レイヤー3-7ポリシー", "malformed": "不正なパケット", "sc": "セッション制御", "setting": "設定", "severity": "重大度", "startDate": "開始日", "startTime": "開始時間", "tab": {"audit": {"deviceName": "デバイス名", "event": "タイプ", "groupName": "グループ名", "message": "メッセージ", "severity": "重大度", "time": "時間", "username": "ユーザー名"}, "device": {"deviceName": "デバイス名", "event": "タイプ", "groupName": "グループ名", "mac": "MACアドレス", "message": "メッセージ", "severity": "重大度", "time": "時間", "username": "ユーザー名"}, "firewall": {"action": "アクション", "adp": "ADP", "all": "すべて", "appProtocol": "アプリケーションプロトコル", "category": "カテゴリー", "deviceName": "デバイス名", "dos": "DoSポリシー", "dpi": "プロトコル フィルター ポリシー", "dstIp": "宛先IP", "dstMac": "宛先MAC", "dstPort": "宛先ポート", "etherType": "イーサタイプ", "event": "タイプ", "fromInterface": "受信インターフェース", "groupName": "グループ名", "icmpCode": "ICMPコード", "icmpType": "ICMPタイプ", "id": "索引", "ips": "IPS", "ipsCategory": "IPSカテゴリー", "ipsSeverity": "IPS 重大度", "l3Policy": "レイヤー3-7ポリシー", "malformed": "不正なパケット", "message": "追加メッセージ", "policyId": "ポリシーID", "policyName": "ポリシー名", "protocol": "IP プロトコル", "security": "安全性", "sessionControl": "セッション制御", "severity": "重大度", "srcIp": "ソースIP", "srcMac": "送信元MAC", "srcPort": "送信元ポート", "subCategory": "サブカテゴリ", "tcpFlag": "TCP フラグ", "time": "時間", "toInterface": "送信インターフェース", "trustAccess": "信頼できるアクセス", "username": "ユーザー名", "vlanId": "VLAN ID"}, "vpn": {"deviceName": "デバイス名", "event": "タイプ", "groupName": "グループ名", "message": "追加メッセージ", "severity": "重大度", "time": "時間", "username": "ユーザー名"}}, "trustAccess": "信頼できるアクセス", "vpn": "VPN"}, "notification": {"advancedSettingMsg": "一定期間内に通知の最大数に達すると、次の期間まで通知は送信されなくなります。", "advancedSettings": "詳細設定", "appProtocol": "アプリケーションプロトコル", "arpFlood": "ARP-Flood", "atLeastOneReceiver": "少なくとも 1 人の受信者", "bufferOverflow": "バッファオーバーフロー", "chooseDevices": "デバイスを選択", "createdBy": "によって作成された", "createNotification": "サイバーセキュリティイベントを追加", "createSuccess": "サイバーセキュリティイベントが正常に作成されました", "deleteFailed": "このイベントは通知に使用されるため、削除することはできません。", "deleteKey": "サイバーセキュリティイベント", "deleteNotification": "通知の削除", "deleteSuccess": "サイバーセキュリティイベントが正常に削除されました", "deviceCount": "デバイス数", "deviceName": "デバイス名", "DNP3": "DNP3", "dosAttacks": "DoS攻撃", "dstIp": "宛先IP", "dstMac": "宛先MAC", "editNotification": "サイバーセキュリティイベントの編集", "EIP": "EIP", "email": "電子メール", "emailContent": "メールの内容", "emailContentDefault": "デバイス ${productModel}、${deviceName} でトリガーされたイベント ${event} が ${eventTime} に発生しました。", "emailHeader": "[MXsecurity] 通知 ${notificationName}\n${deviceName} から生成されました", "emailMsgAutoSentFrom": "この通知は自動的に送信されました\nMXsecurityより。", "emailMsgCheck": "詳細情報をご確認ください\nMXsecurity で。", "emailMsgGreeting": "拝啓、", "emailMsgSignOff": "よろしくお願いします、\nMXセキュリティ", "eq": "等しい", "event": "タイプ", "event_used": "このイベントは通知設定で既に使用されており、変更できません。", "eventFilter": "イベントとフィルターを選択", "eventFilterRule": "イベントフィルタールール", "eventTime": "イベント時間", "exploits": "エクスプロイト", "fileVulnerabilities": "ファイルの脆弱性", "filterRule": "フィルタールール", "filterRuleDetail": "フィルタールールの詳細", "finScan": "FIN Scan", "floodingScan": "フラッディングとスキャン", "GOOSE": "GOOSE", "gt": "より低い", "gte": "以下", "icmpDeath": "ICMP-Flood", "IEC-104": "IEC-104", "ipAddress": "IPアドレス", "ipRangeHint": "結果を表すには*を使用することができます\n/8/16/24 サブネット マスクの例。\n192.168.*.*\n当初は使用できなかった\nIPアドレスまたは中間の単独*", "ipsCate": "IPSカテゴリー", "ipSpoofing": "IPスプーフィング", "ipsSeverity": "IPS 重大度", "location": "場所", "lt": "より高い", "lte": "以上", "macAddress": "MACアドレス", "macRangeHint": "*を使用して範囲を表すことができます\nMAC アドレス、例: 00:90:E8:*:*:*\n開始時に使用できませんでした\nMAC アドレスまたは真ん中に単独で存在します。", "malwareTraffic": "マルウェアトラフィック", "maxEnableSize": "有効にできる通知の最大数は {{num}} です。", "maxNotification": "最大通知", "maxPerUserSize": "ユーザーあたりの通知の最大数は {{num}} です。", "MMS": "ＭＭS", "Modbus/TCP": "Modbus/TCP", "newTcpWithoutSynScan": "SYNなしのTCPセッション", "nmapIdScan": "NMAP-ID Scan", "nmapXmasScan": "NMAP-<PERSON><PERSON>", "notificationActions": "通知アクション", "notificationEvent": "通知イベント", "notificationInfo": "通知情報", "notificationLimit": "通知制限", "notificationName": "イベント名", "nullScan": "<PERSON><PERSON>", "OmronFINS": "Omron FINS", "periodTime": "期間", "policyName": "ポリシー名", "productModel": "製品モデル", "protocolAttackProtection": "プロトコル攻撃保護", "receiverEmailAddress": "受信者のメールアドレス", "receiverSetting": "受信機の設定", "reconnaissance": "偵察", "resetToDefault": "デフォルトにリセット", "serialNumber": "シリアル番号", "severity": "重大度", "severityMode": "重大度モード", "severityRule": "重大度ルール", "showAllDevices": "すべてのデバイスを表示", "showSelectedDevices": "選択したデバイスを表示", "srcIp": "ソースIP", "srcMac": "送信元MAC", "Step7Comm": "Step7Comm", "subCate": "サブカテゴリー", "synFinScan": "SYN/FIN Scan", "synFlood": "SYN-Flood", "synRstScan": "SYN/RST Scan", "syslog": "Syslog", "syslogContent": "Syslog コンテンツ", "syslogContentDefault": "通知 ${notificationName} がデバイスでトリガーされました。${productModel}、${deviceName}、${eventTime} に発生しました。詳細情報については、MXsecurity でご確認ください。", "udpFlood": "UDP-Flood", "updateSuccess": "サイバーセキュリティイベントが正常に更新されました", "webThreats": "ウェブの脅威", "xmasScan": "<PERSON><PERSON>"}}, "management": {"deviceConfiguration": {"configModel": "構成モデル", "configName": "名前の設定", "createSuccess": "デバイス構成が正常に作成されました。", "deleteKey": "デバイス構成", "deleteSuccess": "デバイス構成が正常に削除されました", "editConfig": "設定を編集", "enterConfigInfo": "設定ファイル情報を入力する", "firmwareVersion": "ファームウェアバージョン", "group": "グループ", "isReferenced": "選択された構成の 1 つ以上が参照されます。", "lastModifiedTime": "最終更新日時", "location": "場所", "mac": "MACアドレス", "maxTableSize": "最大構成数は {{num}} です。", "noModelMsg": "構成のモデルはありません", "offlineWarning": "デバイスがオフラインです", "onlyAcceptIni": "' .ini '形式の構成ファイルのみが受け入れられます。", "onlyOneFilePerTime": "一度にアップロードできるファイルは 1 つだけです。", "selectConfigFile": "設定ファイルを選択", "selectWarning": "構成のバックアップには1つのデバイスのみ許可されます", "serialNumber": "シリアル番号", "updateSuccess": "デバイス設定が正常に更新されました", "uploadConfigFile": "設定ファイル (.ini) をアップロードする", "uploadConfigMethod": "アップロード設定方法", "uploadConfigTitle": "デバイス構成ファイルのアップロード", "uploadDeviceConfig": "デバイスから構成をアップロード", "uploadLocalConfig": "ローカルから構成をアップロード"}, "deviceGroup": {"accessPermission": "アクセス許可", "addDevices": "デバイスを追加", "adminPermission": "管理者ユーザーはすべてのグループへの権限を持ちます", "createGroup": "グループの作成", "createSuccess": "デバイス グループが正常に作成されました。", "deleteKey": "デバイスグループを削除", "deleteSuccess": "デバイス グループが正常に削除されました。", "description": "説明", "deviceCount": "デバイス数", "editGroup": "デバイスグループの編集", "enterGroupInfo": "グループ情報を入力する", "firmwareVersion": "ファームウェアバージョン", "grantAccessPermission": "アクセス許可を付与する", "group": "グループ", "groupName": "グループ名", "location": "場所", "mac": "MAC", "role": "役割", "serialNumber": "シリアル番号", "showAllDevices": "すべてのデバイスを表示", "showSelectedDevices": "選択したデバイスを表示", "status": "状態", "updateSuccess": "デバイス グループが正常に更新されました。", "username": "ユーザー名"}, "firmware": {"buildTime": "ビルド時間", "deleteKey": "ファームウェア", "deleteSuccess": "ファームウェアが正常に削除されました。", "description": "説明", "dropZoneTitle": "ファームウェアファイル（.rom）をアップロードする", "isReferenced": "選択されたファームウェアの 1 つ以上が参照されます。", "maxRowMsg": "ファームウェア ファイルの最大数は {{ max }} です。", "maxSize": "許容される最大ファイルサイズは 1 GB です。", "modelSeries": "モデルシリーズ", "onlyAcceptRom": "' .rom '形式のファームウェア ファイルのみが受け入れられます。", "onlyOneFilePerTime": "一度にアップロードできるファイルは 1 つだけです。", "uploadFirmware": "ファームウェアのアップロード", "uploadSuccess": "ファームウェアが正常にアップロードされました。", "version": "バージョン"}, "inUse": "はい", "object": {"filter": {"address": "IP アドレスとサブネット", "code": "コード", "createObject": "オブジェクトの作成", "createSuccess": "オブジェクトが正常に作成されました", "customIpProtocol": "カスタム IP プロトコル", "decimal": "(10進数)", "deleteKey": "オブジェクト", "deleteSuccess": "オブジェクトは正常に削除されました。", "detail": "詳細", "editObject": "オブジェクトの編集", "endPort": "ポート: 終了", "icmp": "ICMP", "icmpCode": "ICMPコード", "icmpType": "ICMPタイプ", "industrialAppService": "産業アプリケーションサービス", "ipAddress": "IPアドレス", "ipEnd": "IPアドレス: 終了", "ipProtocol": "IP プロトコル", "ipRange": "IP範囲", "ipStart": "IPアドレス: 開始", "ipType": "IPタイプ", "isReferenced": "選択されたオブジェクトの1つ以上が参照されています", "leaveAsAny": "空白のままにすると、すべてを表す", "maxRowMsg": "オブジェクトの最大数は {{ max }} です。", "name": "フィルタ", "needSelectedMsg": "少なくとも1つの項目を選択してください", "networkName": "ネットワーク名", "networkService": "ネットワークサービス", "objectName": "名称", "objectReference": "オブジェクト参照", "objectReferenceMsg": "このオブジェクトは、次のプロファイルのポリシー インデックスによって参照されます。", "objectType": "オブジェクトタイプ", "port": "ポート", "portRange": "TCP および UDP ポート範囲", "selectIndustrialAppService": "産業用アプリケーションサービスを選択してください *", "selectNetworkService": "ネットワークサービスを選択してください *", "servicePortType": "サービスポートタイプ", "singleIp": "単一IP", "singlePort": "TCPおよびUDPポート", "startPort": "ポート: 開始", "subnet": "サブネット", "subnetMask": "Subnet Mask", "tcp": "TCP", "tcpUdp": "TCP と UDP", "type": "タイプ", "udp": "UDP", "updateSuccess": "オブジェクトが正常に更新されました", "userDefinedService": "ユーザー定義サービス"}, "interface": {"bridge": "橋", "createInterface": "インターフェースオブジェクトの作成", "createSuccess": "インターフェースが正常に作成されました。", "deleteKey": "インターフェース", "deleteSuccess": "インターフェースが正常に削除されました。", "editInterface": "インターフェースオブジェクトの編集", "interfaceName": "インターフェイス名", "interfaceReference": "インターフェースリファレンス", "interfaceReferenceMsg": "このインターフェースは、次のプロファイルのポリシー インデックスによって参照されます。", "invalidKey": "以下の名前は予約されています: 任意", "isReferenced": "選択されたインターフェースの 1 つ以上が参照されます。", "maxRowMsg": "インターフェースの最大数は {{ max }} です。", "mode": "モード", "name": "インターフェース", "port": "ポートベース", "updateSuccess": "インターフェースが正常に更新されました。", "vlan": "VLAN", "vlanIdBridgeType": "VLAN ID / ブリッジモード", "zone": "ゾーンベース"}}, "policyProfile": {"createProfile": "ポリシープロファイルを作成する", "createSuccess": "プロフィールが正常に作成されました。", "deleteKey": "プロフィール", "deleteSuccess": "プロフィールが正常に削除されました。", "deployment": {"profile_title": "ポリシープロファイルの展開", "security_title": "セキュリティ パッケージの展開", "title": "ポリシープロファイルの展開"}, "dos": {"all_protection_types": "すべての保護タイプを有効にする", "dosLogSetting": "DoSログ設定", "dosSetting": "DoS設定", "floodProtection": "洪水対策", "limit": "限定", "portScanProtection": "ポートスキャン保護", "sessionSYNProtection": "セッションSYN保護", "stat1": "ヌルスキャン", "stat10": "SYNフラッド", "stat11": "ARPフラッド", "stat12": "UDPフラッド", "stat2": "クリスマススキャン", "stat3": "NMAP-クリスマススキャン", "stat4": "SYN/FINスキャン", "stat5": "FINスキャン", "stat6": "NMAP-IDスキャン", "stat7": "SYN/RST スキャン", "stat8": "SYNなしのTCPセッション", "stat8Tooltip": "制限事項: 非対称ネットワーク アーキテクチャの場合、NAT が有効になっているときは、予期しない切断を回避するために、「SYN なしの TCP セッション」を無効にしないことを強くお勧めします。", "stat9": "ICMPフラッド", "title": "DoS"}, "editProfile": "ポリシープロファイルを編集", "ips": {"accept": "受け入れます", "category": "カテゴリー", "custom": "（カスタム）", "id": "ID", "impact": "影響", "monitor": "モニター", "noPackageMsg": "セキュリティ パッケージ管理ページでセキュリティ パッケージを確認します。", "noVersionAvailable": "利用可能なバージョンはありません", "packageVersion": "パッケージバージョン", "reference": "参照", "reset": "リセット", "ruleSetting": "ルール設定", "title": "IPS", "updateSuccess": "ルールが正常に更新されました。", "warningMsg": "ポリシーを設定する前に、デバイスの Web インターフェイスの [ファイアウォール] > [高度な保護] > [設定] 画面で侵入防止システム (IPS) 機能が有効になっていることを確認してください。"}, "isReferenced": "選択されたプロファイルの 1 つ以上が参照されます。", "layer3to7": {"allowAll": "すべて許可", "createPolicy": "レイヤー3-7ポリシーの作成", "createSuccess": "レイヤー 3-7 ポリシーが正常に作成されました。", "default_action_log": "イベントログ", "default_action_log_destination": "ログの保存先", "default_action_severity": "重大度", "defaultAction": "アクション", "deleteKey": "レイヤー3-7ポリシー", "deleteSuccess": "レイヤー 3-7 ポリシーが正常に削除されました。", "deleteTitle": "レイヤー3-7ポリシーの削除", "denyAll": "すべて拒否", "destinationAddress": "宛先住所", "destinationPort": "宛先ポートまたはプロトコル", "destIpAddress": "宛先IPアドレス", "destService": "目的地サービス", "editPolicy": "レイヤー3-7ポリシーの編集", "enforce": "状態", "enforcement": "状態", "event": "タイプ", "eventSetting": "デフォルトのポリシー設定", "filterMode": "フィルターモード", "globalSetting": "グローバルファイアウォール設定", "incomingInterface": "受信インターフェース", "ipAndPortFiltering": "IP およびポート フィルタリング", "ipAndSourceMacBinding": "IP と送信元 MAC のバインディング", "ipTypeError": "送信元ポート IP プロトコル ({{ source }}) が宛先ポート IP プロトコル ({{ dest }}) と異なります", "maxRowMsg": "オブジェクトの最大数は {{ max }} です。", "outgoingInterface": "送信インターフェース", "policyName": "名称", "protocolService": "プロトコルとサービス", "sourceAddress": "送信元アドレス", "sourceIpAddress": "送信元IPアドレス", "sourceMacAddress": "送信元MACアドレス", "sourceMacFiltering": "送信元MACフィルタリング", "sourcePort": "送信元ポート", "title": "レイヤー3-7", "updateSuccess": "レイヤー 3-7 ポリシーが正常に更新されました。"}, "maxRowMsg": "プロファイルの最大数は {{ max }} です。", "profileName": "プロファイル名", "profileReference": "プロフィール参照", "profileReferenceMsg": "このプロファイルは次のデバイスによって参照されます:", "sessionControl": {"concurrentTcp": "同時TCP接続", "connectionsRequestUnit": "接続数/秒", "connectionsUnit": "接続", "createPolicy": "セッション制御ポリシーの作成", "createSuccess": "セッション制御ポリシーが正常に作成されました。", "deleteKey": "セッション制御ポリシー", "deleteSuccess": "セッション制御ポリシーが正常に削除されました。", "destinationIp": "宛先IP", "destinationPort": "宛先ポート", "destIpAddress": "IPアドレス", "destPort": "ポート", "drop": "落とす", "editPolicy": "セッション制御ポリシーの編集", "enforcement": "状態", "maxRowMsg": "このデバイスのポリシーの最大数は {{ max }} です。", "monitor": "モニター", "sub_title": "ネットワークホストおよびサービスリソースプロテクター", "tcpConnectionLimit": "TCP 接続制限", "tcpDestError": "IP アドレスとポートの両方を同時に Any にすることはできません", "tcpDestination": "TCP 宛先", "tcpLimitError": "少なくとも1つの制限を設定する必要があります", "tcpLimitMsg": "少なくとも1つの制限が必要です", "title": "セッション制御", "totalTcp": "合計TCP接続数", "updateSuccess": "セッション制御ポリシーが正常に更新されました。"}, "tabInspection": "検査対象", "tabInterface": "インターフェースオブジェクト", "tabPolicyProfile": "政策プロファイル", "title": "ポリシープロファイル管理", "updateSuccess": "プロフィールが正常に更新されました。"}, "scheduleInUse": "使用中のスケジュール", "scheduling": "スケジューリング", "softwarePackage": {"applicationProducts": "対象製品", "auto-download": "自動ダウンロード", "bugsFixed": "バグ修正", "buildTime": "ビルド時間", "changes": "変更点", "checkConnection": "Moxa アップデート サーバーへの接続状態を確認します。", "checkNewPackage": "MOXA サーバー上の新しいパッケージ バージョンを確認します。", "checkSoftwarePackage": "パッケージの更新を確認する", "daily": "毎日", "deleteKey": "セキュリティパッケージ", "deleteSuccess": "セキュリティ パッケージが正常に削除されました。", "description": "説明", "detailInfo": "詳細情報", "dropZoneTitle": "パッケージファイル（.pkg）をアップロードする", "endDate": "終了日", "endTime": "終了時間", "enhancements": "機能強化", "event": "タイプ", "isReferenced": "選択されたパッケージの 1 つ以上が参照されます。", "janus": "ネットワーク セキュリティ パッケージ", "lastConnectionCheck": "最後の接続チェック", "lastSoftwarePackageUpdateResult": "最新のセキュリティ パッケージ更新結果", "licenseActivationReminder": "ライセンスアクティベーションリマインダー", "licenseActivationReminderContent": "セキュリティ メカニズムを強化するには、ライセンスをアクティブ化してこの機能を有効にしてください。", "licenseTransferReminder": "ライセンス譲渡のお知らせ", "licenseTransferReminderContent": "セキュリティ メカニズムを強化するには、ネットワーク セキュリティ パッケージをアップロードする前に MXsecurity ライセンスを転送してください。", "local": "ローカル", "log": "イベントログ", "maxPackageMsg": "最大同時ダウンロード数: {{ max }} ファイル。", "maxRowMsg": "ソフトウェア パッケージの最大数は {{ max }} です。", "maxSize": "許容される最大ファイルサイズは 1 GB です。", "message": "メッセージ", "newFeatures": "新機能", "notes": "ノート", "onlyAcceptPkg": "' .pkg '形式のファイルのみが受け入れられます。", "onlyOneFilePerTime": "一度にアップロードできるファイルは 1 つだけです。", "packageDownloading": "パッケージダウンローダーは他のユーザーでも機能しています", "packageReference": "パッケージ参照", "packageReferenceMsg": "このパッケージは次のプロファイルによって参照されます:", "period": "期間", "productModel": "製品モデル", "releaseDate": "発売日", "releaseNote": "リリースノート", "scheduling": "スケジュールされた更新チェック", "schedulingMode": "スケジュールモード", "server": "Moxa アップデートサーバのステータス", "serverDisconnected": "セキュリティ パッケージは、サーバーが接続されている場合にのみ確認できます。", "severity": "重大度", "softwarePackageAlreadyLatest": "セキュリティ パッケージは最新です。", "softwarePackageCheck": "セキュリティパッケージチェック", "softwarePackagesFile": "セキュリティ パッケージ ファイル", "softwarePackagesUpdateCheck": "セキュリティパッケージの更新", "startDate": "開始日", "startTime": "開始時間", "supportedFunctions": "サポートされている機能", "supportedOperatingSystems": "サポートされているオペレーティングシステム", "supportModel": "対応モデル", "supportSeries": "サポートされているシリーズ", "syncSettingNotSet": "同期設定を完了してセキュリティ パッケージを確認します。", "syncSettings": "スケジュールされた更新チェック", "syncSettingUpdateSuccess": "設定が正常に更新されました。", "syncSoftwarePackageBySchedule": "ユーザーが指定したスケジュールに基づいて、指定されたモデルのセキュリティ パッケージの更新を自動的にチェックします。", "syncSoftwarePackageByScheduleTooltip": "Moxa サーバーでセキュリティ パッケージの更新を確認する頻度を設定します。", "time": "時間", "title": "セキュリティパッケージ管理", "updateCheckTooltip": "Moxa サーバーでセキュリティ パッケージの更新を確認し、最新バージョンを使用していることを確認してください。", "uploadBy": "アップロード者", "uploadSoftwarePackage": "パッケージをアップロード", "uploadSuccess": "セキュリティ パッケージが正常にアップロードされました。", "username": "ユーザー名", "version": "バージョン", "weekday": "日", "weekly": "毎週", "zeus": "MXsecurity エージェント パッケージ"}}}, "PORT_SETTING": {"another_port_setting_error": "別の設定は処理中です。", "apply_another_port": "設定を他のポートに適用", "disable_port_warning": "警告：このポートを無効にすると、このポートに接続されているデバイスが切断されます。", "enable": "有効化", "get_port_setting_fail": "ポート設定を取得できません", "hint": "* If the settings fail, please confirm that the selected port can be configured.", "media_type": "Media Type", "port": "ポート", "port_description": "ポート説明", "port_name": "ポート名", "port_select": "選択しました", "set_fail": "一部のポートを更新できません。後でもう一度お試しください", "set_success": "すべてのポート設定が更新されました", "title": "イーサネット/光ファイバーポートの設定"}, "PREFERENCES": {"advanced": "詳細設定", "appearance": "外観", "default_view": {"choose_start_page": "スタートページの選択", "dashboard": "ダッシュボード", "title": "デフォルトの表示", "topology": "トポロジー"}, "device_appearance": {"alias": "エイリアス", "bottom_hint": "Aliasの設定を変更した場合は、トポロジー上のデバイスを削除してから、再スキャンまたはデバイスを追加して'Alias'の設定を完了させてください。", "bottom_label": "ボトムラベル", "bottom_label_items": {"alias": "エイリアス", "location": "場所", "mac": "MAC", "model_name": "モデル名", "none": "なし", "sysname": "システム名"}, "get_fail": "デバイス外観を取得できません", "ip_address": "IPアドレス", "set_fail": "デバイス外観を設定できません", "set_success": "デバイス外観が更新されました", "title": "<PERSON><PERSON>"}, "device": {"login": "ログイン", "title": "デバイス"}, "dialog": {"desc": "[このメッセージを再度表示しない] を全て消去し、非表示のダイアログをすべて再度表示してください", "title": "ダイアログ"}, "display": "外観", "email_config": {"allow_selfsigned_cert": "自己署名証明書を許可する", "apply_fail": "Eメールサーバー設定を行うことができません", "apply_success": "Eメールサーバー設定が更新されました", "encryption": "暗号化", "password": "パスワード", "port_number": "ポート番号", "sender_address": "送信者アドレス", "server_domain_name": "サーバードメイン名/IP", "title": "メールサーバー設定", "username": "ユーザー名"}, "events": {"apply_fail": "イベントしきい値を設定できません", "apply_success": "イベントしきい値が更新されました", "availability_under": "可用性未満", "bandwidth_utilization_over": "帯域幅使用率超過", "bandwidth_utilization_under": "帯域幅使用率未満", "link_down": "リンクダウン", "link_up": "リンクアップ", "packet_error_rate_over": "パケットエラー率超過", "port_looping": "ポートルーピング", "sfp_rx_below_threshold": "SFP RX アンダー", "sfp_temp_over_threshold": "SFP 温度 オーバー", "sfp_tx_below_threshold": "SFP TX アンダー", "sfp_volt_below_threshold": "SFP 電圧 アンダー", "sfp_volt_over_threshold": "SFP 電圧 オーバー", "title": "イベント"}, "labs": {"colored_link_desc": "有効にすると、すべてのワイヤレスリンクがSNRで色分けされます。", "desc": "Labsをオンにすると、以下のような実験的な機能が追加されます。", "dialog_title": "Labs機能を有効にする", "title": "MXview One Labs"}, "language": {"default_language": "デフォルト言語", "en_US": "英語", "fail": "デフォルト言語を設定できません", "success": "デフォルト言語が更新されました", "title": "言語", "zh_CN": "简体中文", "zh_TW": "繁體中文"}, "login_authentication": {"apply_fail": "ログイン認証を設定できません", "apply_success": "ログイン認証が更新されました", "authentication_protocol": "認証プロトコル", "local": "ローカル", "tacacs": "TACACS+", "tacacs_local": "TACACS+, そして ローカル", "title": "ログイン認証"}, "login_notification": {"fail": "ログイン通知を設定できません", "login_authentication_failure_message": "ログイン認証失敗メッセージ", "login_message": "ログインメッセージ", "show_default_password_notification": "デフォルトパスワード通知の表示", "show_login_failure_records": "ログイン失敗記録の表示", "success": "ログイン通知が更新されました", "title": "ログイン通知"}, "management_interface": {"help": "このページは、http、https、telnetポートを含むMXview Oneからデバイスへの接続インターフェイスを設定する際に使用されます", "http_port": "HTTPポート", "htts_port": "HTTPSポート", "invalid_port": "ポート番号は1～65535の範囲で指定する必要があります", "set_fail": "管理インターフェイスを設定できません", "set_success": "管理インターフェイスが更新されました", "telnet_port": "Telnetポート", "title": "管理インターフェイス", "web_console_protocol": "ウェブコンソールプロトコル"}, "modbus_tcp_configuration": {"port": "port"}, "opc_server_config": {"apply_fail": "OPCサーバー設定を行うことができません", "apply_success": "OPCサーバー設定が更新されました", "enable_opc_server": "有効化", "title": "OPCサーバー設定"}, "password_policy": {"fail": "パスワードポリシーを設定できません", "has_digits": "1つ以上の数字（0～9）", "has_special_chars": "1つ以上の特殊文字（~!@#$%^&*-_|;:,.<>[]{}()）", "min_password_length": "最小長さ（4～16）", "min_password_length_error": "有効な値を入力してください", "mixed_case": "大文字と小文字が混在しています（A～Z、a～z）", "password_strength_check": "パスワード複雑性の強度チェック", "success": "パスワードポリシーが更新されました", "title": "パスワードポリシー"}, "search": "検索", "security_view": {"all": "すべて", "awk_device_credentials_hint": "セキュリティビューをサポートするには、このデバイスのユーザー名とパスワードを設定する必要があります", "basic": "基本", "basic_text": "基本", "built_in_profile": "内蔵プロファイル", "check_item": "項目のチェック", "colors_for_check_result": "確認の色の結果", "current_setting": "現在の設定：", "custom": "カスタム", "custom_profile": "カスタムプロファイルのベースラインを選択", "device_security_level": "デバイスセキュリティレベル：", "failed": "セキュリティビューの設定に失敗しました", "filter_result": "フィルターの結果", "high": "高", "high_text": "高", "medium": "中", "medium_text": "中", "new_profile": "新しいプロファイル", "not_pass": "合格", "open": "開く", "pass": "合格", "profile": "プロファイル", "profile_details": "プロファイルの詳細", "success": "セキュリティビューが更新されました", "title": "セキュリティビュー", "unknown": "不明", "user_defined": "ユーザー定義"}, "Server": "サーバー", "site_name_configuration": "サイト名の設定", "sms_config": {"apply_fail": "SMS設定を行うことができません", "apply_success": "SMS設定が更新されました", "baud_rate": "ボーレート", "com_port": "COMポート", "mode": "モード", "title": "SMS設定"}, "snmp_configuration": {"help": "ネットワークデバイスにアクセスするためのSNMP設定を行う", "title": "SNMP設定"}, "SNMP_TRAP": {"apply_fail": "SNMPトラップ設定を行うことができません", "apply_success": "SNMPトラップサーバー設定が更新されました", "community1": "トラップサーバー1のコミュニティ", "community2": "トラップサーバー2のコミュニティ", "device_list": "デバイスリスト", "device_trap": "デバイスのSNMPトラップサーバー", "forward_trap_control1": "受信トラップをサーバー1に転送", "forward_trap_control2": "受信トラップをサーバー2に転送", "ip1": "トラップサーバー1のIPアドレス", "ip2": "トラップサーバー2のIPアドレス", "mxview_trap": "MXview OneのSNMPトラップサーバー", "version": "SNMPバージョン", "version_1": "SNMPバージョン1", "version_2": "SNMPバージョン2c"}, "syslog_config": {"already_running": "サーバーはすでに実行しているか停止しています", "apply_fail": "Syslogサーバー設定を行うことができません", "apply_success": "Syslogサーバー設定が更新されました", "enable_syslog_server": "内蔵のsyslogサーバーの有効化", "invalid_port": "ポート番号は1～65535の範囲で指定する必要があります", "syslog_server_port": "Syslogサーバーポート", "title": "Syslogサーバー設定"}, "system_configuration": {"apply_fail": "システム設定を行うことができません", "apply_success": "システム設定が更新されました", "background_discovery": "背景検出", "disk_hint": "0に設定すると、アラームは無効化されます", "playback": "再生", "playback_hint_1": "* \"再生\"機能が有効化されている場合、イベントの発生中にMXview Oneはデバイスとリンクのステータスを記録します。後で詳細プロセスを見るために再生モードに入ることができます。", "playback_hint_2": "* \"再生\"機能が有効化されている場合、追加のディスク容量が必要です。", "threshold_disk_space": "ディスク容量のしきい値（MB）", "title": "システム設定"}, "table": {"default": "デフォルト", "dense": "密集", "fail": "表設定を設定できません", "success": "表設定が更新されました", "table_row_height": "行の高さ", "title": "表"}, "tacacs": {"apply_fail": "TACACS サーバー設定を行えません", "apply_success": "TACACS+ サーバー設定が更新されました", "auth_type": "認証タイプ", "auth_type_asc_two": "ASCII", "auth_type_chap": "CHAP", "auth_type_ms_chap": "MS-CHAP", "auth_type_pap": "PAP", "server_address": "サーバーアドレス", "share_key": "共有キー", "tcp_port": "TCP ポート", "timeout": "タイムアウト", "title": "TACACS+ サーバー"}, "title": "基本設定", "topology_appearance": {"access_port": "アクセスポート", "background": "背景", "background_color": "背景色", "directed_line_style": "方向付きの線のスタイル", "edit_igmp_visualization_color": "IGMPの可視化の色", "edit_traffic_load_color": "トラフィック負荷の色の編集", "edit_vlan_visualization_color": "VLANの可視化の色", "elbow_line_style": "エルボーの線のスタイル", "fail": "トポロジー外観を設定できません", "hsr_ring": "HSR Ring", "igmp_visualization": "IGMPの可視化", "link_down": "リンクダウン", "link_up": "リンクアップ", "member": "メンバ", "poe": "PoE", "poe_color": "PoEリンクの色", "prp_lan_a": "PRP LAN A", "prp_lan_b": "PRP LAN B", "querier": "クエリア", "rstp": "RSTP", "show_poe": "トポロジーのPoEステータスの表示", "status_color": "ステータスの色", "success": "トポロジー外観が更新されました", "text_size": "文字のサイズ", "text_size_large": "大", "text_size_medium": "中", "text_size_small": "小", "title": "Topology", "topology_style": "トポロジーの線のスタイル", "traffic_load": "トラフィック負荷", "trunk_port": "トランクポート", "turbo_chain": "Turbo Chain", "turbo_ring_v1": "Turbo Ring V1", "turbo_ring_v2": "Turbo Ring V2", "vlan_visualization": "VLANの可視化"}, "user": "ユーザー"}, "rogue_device_detection": {"add_device_for_rogue_device_detection": "不正デバイス検出のデバイスベースラインにこのデバイスを追加します", "add_device_to_baseline": "ベースラインにデバイスを追加", "add_device_to_baseline_content": "このデバイスをベースラインに追加してもよろしいですか?", "add_devices_to_baseline_content": "これらのデバイスをベースラインに追加してもよろしいですか?", "add_scan_device_for_rogue_device_detection": "スキャンしたデバイスを不正デバイス検出のデバイスベースラインに追加する", "clear_all_rogue_device_history": "不正デバイスの履歴を消去", "clear_all_rogue_device_history_hint": "すべての不正デバイスの履歴が消去されます。続行してもよろしいですか?", "connected_switch_port": "接続されたスイッチ/ポート", "creation_time": "作成日", "current_rogue_device": "現在の不正デバイス", "delete_device_from_baseline": "ベースラインからデバイスを削除", "delete_device_from_baseline_content": "このデバイスはベースラインから削除され、不正デバイスとして追加されます。", "delete_devices_from_baseline_content": "これらのデバイスはベースラインから削除され、不正デバイスとして追加されます。", "device_baseline": "デバイスベースライン", "device_baseline_content": "この操作により、新しいベースラインが作成され、既存のベースラインが上書きされます。", "download_all_history_data_to_csv": "すべての履歴データをCSVにエクスポート", "download_current_page_to_csv": "現在のページをCSVにエクスポート", "first_seen": "初登場", "ip": "IPアドレス", "last_seen": "最後に見たのは", "mac": "MACアドレス", "must_create_a_baseline_first": "デバイス ベースラインが存在しません。まずベースラインを作成してください。", "no_devices_can_add": "デバイスが検出されませんでした。まず MXview One にデバイスを追加してください。", "port": "ポート", "rogue_device_history": "不正デバイスの履歴", "rogue_device_settings": "不正デバイスの設定", "sequence_no": "シーケンス番号", "unknown": "不明", "vendor": "NICベンダー"}, "SCAN_RANGE": {"add_scan_range": "スキャン範囲の追加", "button": {"back": "戻る", "browse_topology": "トポロジーの参照", "cancel": "キャンセル", "discovery": "確認して検出を実行", "next": "次へ", "recover": "<PERSON>カ<PERSON><PERSON>", "scan_new_network": "新しいネットワークのスキャン"}, "cidr_address_range": "CIDRアドレス範囲", "duplicate_range": "新しい範囲が既存の範囲と重複しています", "edit_scan_range": "スキャン範囲の編集", "firstIp_higher_lastIp": "IP 範囲が無効です (最初の IP > 最後の IP)", "subnet_mask": "Subnet Mask", "table_title": {"active": "スキャン範囲の有効化", "background_scan": "バックグラウンドスキャン", "conflict_scan": "IP競合検出", "edit": "編集", "end_ip": "最後のIPアドレス", "group": "グループ", "name": "名前", "site_name": "サイト名", "start_ip": "最初のIPアドレス"}, "wizard": {"complete": "完了", "complete_message": "{{discoveryDevices}} がMXview Oneに追加されています", "discovery_result": "検出結果", "network_range": "ネットワーク範囲", "save_hint": "スキャンされた範囲は、デバイス検出後に保存されます。", "title": "デバイス発見"}}, "script_automation": {"add_a_script_automation": "スクリプト自動化を追加する", "add_script_button_hint": "スクリプト自動化の最大数は 200 です。", "add_script_first_hint": "スクリプト自動化が見つかりません。スクリプト自動化画面に移動して、スクリプト自動化を追加してください。", "add_script_sutomation": "スクリプト自動化の追加", "adjustable_buttons": "ボタンの並べ替え", "affected_devices": "影響を受けるデバイス", "affected_devices_info": "このアクションは {{ affectedDevices }} 台のデバイスに影響します。", "affected_devices_info_2": "このアクションは次の {{ affectedDevices }} デバイスに影響します", "align_buttons": "すべてのグループを1列に揃える", "all_devices": "すべてのデバイス", "automation_button": "自動化ボタン", "background_color": "背景色", "button_name": "ボタン名", "button": {"panel": "パネル", "privilege": "管理者権限認証が必要", "state": {"hint": "グループ内のボタンのうち 1 つだけが' on '状態になります。", "off": "オフ", "on": "オン", "title": "ボタンの状態"}, "style": "ボタンスタイル", "widget": "ボタンウィジェット"}, "cli_id_duplicated": "この CLI はすでに選択されています。", "cli_script_and_target_device": "CLI スクリプトとターゲットデバイス", "cli_script_hint": "スクリプトの最大数は 50 です。", "color": "色", "confirm_proceed": "続行しますか？", "create_new_group": "新しいグループを作成して追加する", "delete_automation": "このスクリプト自動化を削除してもよろしいですか?", "delete_multiple_automation": "これらのスクリプト自動化を削除してもよろしいですか?", "delete_script_automation": "スクリプト自動化の削除", "description": "説明", "device_missing": "以下のデバイスが見つかりませんでした", "drag_button": "新しいグループを作成するには、ここにボタンをドラッグします。", "edit_button": "編集ボタン", "edit_group": "グループの編集", "edit_panel": "ボタンパネルのタイトルを編集", "edit_script_button": "オートメーション編集ボタン", "execute_button": "実行ボタン", "execute_button_hint": "スクリプト自動化の実行", "execute_button_info": "結果を表示するには、プロセスが完了するまでお待ちください。この画面を離れた場合は、「保存された CLI スクリプト」>「実行結果」に移動して結果をダウンロードできます。", "extra_large": "特大", "group": "グループ", "group_already_exist": "このグループ名は既に存在します", "group_name": "グループ名", "invalid_account": "無効なアカウント権限", "ip_duplicated": "このデバイスはすでに選択されています。", "large": "大", "last_executed": "最終実行", "leave_page_hint": "このページを離れてもよろしいですか？", "leave_without_saving": "保存せずに終了", "medium": "中", "more": "多くの", "name": "名前", "not_change_group": "現在のグループを使用する", "not_saved_hint": "変更内容は保存されません。", "script_automation": "スクリプト自動化", "select_all": "すべて選択", "select_existing_group": "別のグループに移動する", "select_saved_cli_script": "保存したCLIスクリプトを選択", "small": "小", "start_preview": "トポロジーでプレビューを開始", "stop_preview": "トポロジのプレビューを停止", "target_device": "対象デバイス", "text_color": "テキストの色", "widget_size": "ウィジェットのサイズ"}, "SECURITY_ITEM": {"ACCESSIBLE_IP_LIST": "トラステッドアクセスの有効化", "ACCOUNT_LOCKOUT": "アカウントログイン失敗時のロックアウトの有効化", "ACCOUNT_VALIDITY": "アカウントとパスワードポリシーの妥当性", "AUTO_LOGOUT": "自動ログアウトの有効化", "AWK_SERIES": "ワイヤレス", "BROAD_CAST_STORM": "Enable DDoS Protection", "changed": "変更されました", "disabled": "無効化", "enabled": "有効化", "ENCRYPT_CONSOLE": "非暗号化のTCP / UDPポートの無効化", "ENCRYPTED_CONFIG": "設定ファイルの暗号化の有効化", "HIGH_SECURE_MODE": "ハイセキュアモード", "LOGIN_NOTIFICATION": "ログインメッセージの設定", "MGATE": "ゲートウェイ", "non-changed": "未変更", "not_set": "未設定", "NPORT": "ターミナルサーバー", "NPORT5000": "デバイスサーバー", "NTP_SERVER": "NTPクライアントの設定", "PASSWORD_CHANGED": "デフォルトパスワード/SNMPコミュニティ文字列の変更", "PASSWORD_POLICY": "パスワード複雑性の強度チェックの有効化", "read_fail": "読み取りに失敗しました", "set": "設定", "SWITCH": "スイッチ", "SYSLOG": "Syslogサーバーの設定", "TRAPSYSLOG": "SNMPトラップ/インフォームサーバーまたはSyslogサーバーの設定", "unknown": "不明", "WEB_CERTIFICATE": "Web証明書のインポート"}, "SERIAL_PORT_MONITORING": {"all_ports": "すべてのポート", "any_serial_error_count": "シリアルエラー数", "break_error_count": "ブレークエラー数", "copy_configuration_device": "デバイスに構成をコピーする", "count_threshold": "トリガーしきい値", "counts": "カウント", "critical": "重大", "error_status": "シリアルポートの警告", "event_condition_rule": "イベントトリガールール", "frame_error_count": "フレームエラー数", "hint_any": "次の問題を解決するには、次の手順を試してください: シリアル ポート {{portnum}} がオーバーラン エラー ({{overrun error count}})、パリティ エラー ({{parity error count}})、フレーム エラー ({{frame error count}})、またはブレーク エラー ({{break error count}}) のカウントしきい値を超えました。", "hint_break": "次の問題を解決するには、次の手順を試してください: シリアル ポート {{portnum}} がブレーク エラー数のしきい値 ({{count}}) を超えました。\nシリアル ブレーク信号は、配線の問題、デバイスの故障、デバイスのリセット、同期プロセスなど、接続されたシリアル デバイスの特殊な状態を示します。", "hint_frame": "次の問題を解決するには、次の手順を試してください: シリアル ポート {{portnum}} がフレーム エラー数のしきい値 ({{count}}) を超えました。\nMoxa デバイスはシリアル データを受信すると、フレーム形式がシリアル パラメータと一致するかどうかを確認します。一致しない場合は、フレーム エラーとしてカウントされます。", "hint_general_1": "提案された解決策が機能しない場合、またはさらに質問がある場合は、", "hint_general_2": "first", "hint_general_3": "コンタクト", "hint_general_4": "にお問い合わせください。", "hint_overrun": "次の問題を解決するには、次の手順を試してください: シリアル ポート {{portnum}} がオーバーラン エラー数のしきい値 ({{count}}) を超えました。\n接続されたシリアル デバイスが Moxa デバイスが読み取ることができないほど速くデータを送信すると、データがドロップされ、オーバーラン エラーが発生します。", "hint_parity": "次の問題を解決するには、次の手順を試してください: シリアル ポート {{portnum}} がパリティ エラー数のしきい値 ({{count}}) を超えました。\nパリティ エラーは、受信したデータ文字が設定されたパリティと一致しないことを示します。", "hint_rx": "次の問題を解決するには、次の手順を試してください: シリアル ポート {{portnum}} は、過去 {{min}} 分間にデータを受信していません。", "hint_rxtx": "次の問題を解決するには、次の手順を試してください: シリアル ポート {{portnum}} は、過去 {{min}} 分間にデータを送信または受信していません。", "hint_tx": "次の問題を解決するには、次の手順を試してください: シリアル ポート {{portnum}} は、過去 {{min}} 分間にデータを送信していません。", "how_to_resolve": "解決方法を教えてください。", "minutes": "議事録", "no_data_period": "データなし期間", "overrun__error_count": "オーバーランエラー数", "parity__error_count": "パリティエラー数", "port_duplicated": "このポート/トリガー ルールの組み合わせはすでに構成されています。", "port_properties": "イベントタイプ", "resolve_title": "シリアルポート {{portnum}} の問題を解決する", "rx": "RX 非アクティブ", "serial_port": "シリアルポート", "severity": "重大度", "step1_break": "接続されているシリアルデバイスが正常に動作しているかどうかを確認します。", "step1_frame": "Moxa デバイスのシリアル インターフェイス設定 (RS-232、RS-422、RS485) と通信パラメータ (例: 115200、8、n、1) が、接続されているシリアル デバイスの設定と一致しているかどうかを確認します。", "step1_ovrrun": "Moxaデバイスと接続されたシリアルデバイスの両方で、シリアルハードウェアおよび/またはソフトウェアフロー制御が適切に設定されているかどうかを確認します。", "step1_parity": "Moxa デバイスと接続されたシリアル デバイスのパリティとボーレートの設定が一致しているかどうかを確認します。", "step1_txrx": "Moxa デバイスとシリアル エンド デバイス間のシリアル ケーブルが正しく接続されているかどうかを確認します。", "step2_ovrrun": "Moxa デバイスで FIFO を有効にする必要があるかどうかを確認します。", "step2_parity": "干渉が激しい場所では、シリアル通信システムが適切に保護されているかどうかを確認してください。", "step2_txrx": "接続されているシリアルデバイスが正常に動作しているかどうかを確認します。", "step3_parity": "接続されているシリアル デバイスに配線の問題やハードウェア障害がないか確認します。", "still_not_working": "まだ動かない？", "title": "シリアルポートイベント", "tx": "TX 非アクティブ", "tx_rx": "TX および RX 非アクティブ", "warning": "警告"}, "SEVERITY": {"critical": "危機的", "information": "情報", "title": "重要度", "warning": "警告"}, "sfpList": {"rx": "RX (dBm)", "temperature": "温度(°C)", "title": "SFP リスト", "tx": "TX (dBm)", "voltage": "電圧 (V)"}, "SITE_MANAGEMENT": {"desc": "説明", "fail": "サイト情報を更新できません", "name": "名前", "offline": "サイトはオフラインです（{{siteName}}）", "online": "サイトはオンラインです（{{siteName}}）", "success": "サイト情報が更新されました", "title": "サイト管理"}, "SITE_MENU": {"management": "管理"}, "SITE_PROPERTIES": {"description": "説明", "devices": "デバイス（通常/警告/危機）", "information": "情報", "name": "名前", "title": "サイトのプロパティ"}, "SNACK_BAR": {"acking": "確認中...", "copied": "コピーされた", "deleting": "削除中...", "saving": "保存中..."}, "SYSLOG_SETTINGS": {"all_severity": "すべて", "authentication": "認証", "enable_syslog_forward": "Syslog 転送", "enable_tcp": "有効にする (TCP のみ)", "enable_udp": "有効にする（UDPのみ）", "enable_udp_tcp": "有効 (UDP および TCP)", "failed_to_get_syslog_forward_settings": "Syslog転送設定を取得できませんでした", "failed_to_get_syslog_settings": "Syslog設定を取得できませんでした", "filter_settings_hint": "複数の送信元 IP アドレスをコンマで区切って入力できます。", "forward_ip1": "リモート IP/ドメイン名 1", "forward_ip2": "リモート IP/ドメイン名 2", "port_1": "ポート1", "port_2": "ポート2", "protocol": "プロトコル", "source_ip": "ソースIP", "syslog_built_in": "内蔵Syslogサーバー", "syslog_filter_settings": "Syslog フィルター", "syslog_forwarding": "Syslog 転送", "syslog_server_settings": "Syslog サーバーの設定", "tcp": "TCP", "tcp_port": "TCP ポート", "title": "Syslog設定", "tls_cert": "TLS + 証明書", "tls_only": "TLSのみ", "udp": "UDP", "udp_port": "UDPポート", "update_failed": "設定の更新に失敗しました", "update_success": "設定が正常に更新されました"}, "SYSLOG_VIEWER": {"device_ip": "IPアドレス", "facility": "ファシリティ", "filter_syslog_event": "Syslogイベントをフィルターするタイプ", "ip_error": "有効なIPアドレスを入力してください", "message": "メッセージ", "priority": {"equals": "等しい", "high_than": "以上", "lower_than": "以下", "title": "優先度"}, "severity": {"alert": "アラート", "critical": "危機的", "debug": "デバッグ", "emergency": "緊急", "error": "エラー", "information": "情報", "notice": "注意", "title": "重要度", "warning": "警告"}, "site_name": "サイト名", "timestamp": "タイムスタンプ", "title": "Syslogビューア"}, "SYSTEM": {"request_timeout_title": "リクエストのタイムアウト", "trigger_disconnected_desc1": "MXview Oneサーバーから切断されました", "trigger_disconnected_desc2": "5秒後に再接続...", "unauthorized_desc": "無効な資格情報のためアクセスが拒否されました", "unauthorized_title": "無許可"}, "TABLE": {"add": "追加", "adjustable_columns": "調整可能な列", "compare": "比較", "delete": "削除", "edit": "編集", "edit_columns": "列の編集", "enable": "有効/無効", "export": "エクスポート", "exporting": "現在エクスポート中...", "filter": "フィルター", "import": "インポート", "limit_count": "マックス。", "list_collaspe": "畳む", "list_expand": "展開して詳細を表示", "locate": "ロケート", "no_data": "データ表示なし", "not_support": "デバイスのファームウェアを更新してください", "save": "保存", "search": "検索", "selected_count": "選択されました", "show_log": "ログを表示", "sync": "同期", "total": "合計", "waiting_data": "デバイスデータは現在収集中です"}, "TOPOLOGY": {"add_tag": "Add Tag...", "add_tag_fail": "Unable to add tag", "add_tag_success": "Add a tag successfully", "choose_goose_publisher": "GOOSEパブリッシャーを選択してください。", "colored_link": "SNRで色分けされたリンク", "delete_tag_failed": "Unable to delete tag", "delete_tag_success": "Delete a tag successfully", "device_not_found": "デバイスが見つかりません", "display_opt": "トポロジー表示オプション", "dynamic_wireless_client_position": "動的クライアントポジション", "dynamic_wireless_client_position_desc": "接続されたAPの周囲にクライアントを表示する", "editTagTooltip": "Press Enter to save.\nPress ESC key to cancel.", "goose": "GOOSE", "goose_publisher": "GOOSEパブリッシャー", "goose_tampered": "GOOSE 改ざん", "goose_timeout": "GOOSEタイムアウト", "grouping_failed": "グループ化に失敗しました", "grouping_success": "正常にグループ化されました", "legend": "Legend", "new_tag": "New tag", "no_subscriber": "サブスクライバーなし", "prp_hsr": "PRP/HSR", "prp_hsr_tags": "PRP/HSR Tags", "publisher": "パブリッシャー", "publisher_hint": "GOOSE control block name\nAPPID / Address", "search_topology": "トポロジーの検索", "set_tag_fail": "Unable to set a tag", "set_tag_success": "Set a tag successfully", "show_all_wireless_clients": "クライアントを表示する", "site_management_not_supported": "ローミング履歴再生機能を使用するサイトを指定してください", "subscriber": "サブスクライバー", "subscriber_hint": "IED Name / GOOSE control block name", "tag": "Tag", "traffic_view": "トラフィック負荷（%）", "ungrouping_failed": "解除に失敗しました", "ungrouping_success": "正常に解除されました", "wireless_display_opt": "ワイヤレス表示オプション", "zoom_in": "ズームイン", "zoom_out": "ズームアウト", "zoom_to_actual_size": "デフォルトサイズにズーム", "zoom_to_fit": "ウィンドウに合わせてズーム"}, "TRAP_CONFIGURATION": {"apply_fail": "デバイストラップサーバーを設定できません", "apply_success": "デバイストラップサーバー設定が更新されました", "community_name1": "コミュニティ名1", "community_name2": "コミュニティ名2", "destination_ip1": "宛先IP1", "destination_ip2": "宛先IP2", "title": "トラップサーバー"}, "UNIT": {"dB": "dB", "dBi": "dBi", "dBm": "dBm", "hours": "時間", "mb": "Mb", "mbps": "Mbps", "meter": "m", "min": "分", "sec": "秒", "times": "回"}, "UPGRADE_FIRMWARE": {"file_type_error": "ファームウェア ファイルは、.rom、.tar、および .gz ファイル形式である必要があります。", "upgrade_firmware_fail": "ファームウェアをアップロードできません", "upgrade_firmware_success": "ファームウェアが更新されました", "upgrading": "ファームウェアファイルのダウンロード。この操作には数分かかる場合があります。処理が完了するまで、電源を切ったり、ネットワークを切断したりしないでください"}, "validators": {"duplicateEmail": "重複したメールがあります", "excludeLastPassword": "新しいパスワードは前回のパスワードと同じにすることはできません", "excludeUserName": "ユーザー名を含めることはできません", "invalid": "無効な文字", "invalid_date": "無効な日付", "invalid_format_allow_space": "文字列には最大 {{count}} 個のスペースのみ使用できます", "invalidEmail": "無効なメール", "invalidIpAddress": "無効なIPアドレス", "invalidIpAddressOrDomainName": "無効な IP アドレスまたはドメイン名", "invalidLocation": "特別許可 (-_@!#$%^&*().,/)", "invalidMacAddress": "無効なMACアドレス", "invalidMacAddressAllZero": "MACアドレス00:00:00:00:00:00は予約されています", "invalidSeverity": "重大度が無効です", "ipRangeError": "IP アドレス: 終了は IP アドレス: 開始よりも大きくする必要があります", "isExist": "すでに存在する", "isExistOrUsedByOtherUser": "すでに存在するか、他のユーザーによって使用されています", "maxReceiverSize": "最大受信者数は {{num}} です。", "needDigit": "少なくとも 1 つの数字 (0 - 9) を含める必要があります", "needGreaterThan": "{{ largeItem }} は {{ smallItem }} より大きくする必要があります", "needLowerCase": "少なくとも 1 つの小文字 (a - z) を含める必要があります", "needSpecialCharacter": "少なくとも 1 つの特殊文字を含める必要があります (~!@#$%^&amp;*_-+=`|\\(){}[]:;”&#39;&lt;&gt;,.?/)", "needUpperCase": "少なくとも 1 つの大文字 (A - Z) を含める必要があります", "notMeetPolicy": "パスワードポリシーの要件を満たしていません", "portRangeError": "ポート:終了はポート:開始よりも大きい必要があります", "pwdNotMatch": "パスワードが一致しません", "range": "無効な範囲 ({{ min }} ~ {{ max }})", "required": "必須", "requireMaxLength": "{{ number }} 文字以内でなければなりません", "requireMinLength": "少なくとも {{ number }} 文字の長さにする必要があります"}, "Validators": {"require_range_between": "{{ rangeBegin }} - {{ rangeEnd }}"}, "VLAN_TABLE": {"access_ports": "アクセスポート", "device_ip": "デバイスIP", "empty": "空欄", "export_csv": "CSVのエクスポート", "filter_vlan": "タイプしてデバイスVLANをフィルターする", "hybrid_ports": "ハイブリッドポート", "location": "場所", "management_vlan": "管理VLAN", "model": "モデル", "no": " いいえ", "site_name": "サイト名", "title": "VLAN", "trunk_ports": "トランクポート", "vlan_id": "VLAN ID", "yes": " はい"}, "wirelessPlayback": {"decreaseSpeed": "再生速度を下げる", "increaseSpeed": "再生速度を上げる", "noData": "選択した日付範囲にデータがありません", "range": "時間範囲", "startTime": "始まる時間", "timeRange": "時間範囲"}}