{"ABOUT": {"debug_log": "調試日誌", "debug_log_desc": "點擊下載按鈕，生成並下載調試日誌。通過MOXA支援頻道發送下載的日誌檔，以便進一步分析。", "desc": "Copyright Moxa, Inc. All Rights Reserved.", "eula": "檢視使用者授權合約 (EULA)", "gateway": "閘道版本", "title": "關於", "web": "Web 版本"}, "ACCOUNT_MANAGEMENT": {"access_site": "可存取伺服器", "add_account_dialog": "新增使用者帳號", "add_user_fail": "無法建立新帳號", "add_user_success": "新帳號已經建立成功", "admin": "監工", "all_user": "所有使用者", "authority": "權限", "change_password": "更改密碼", "delete_user_fail": "無法刪除使用者帳號", "delete_user_success": "帳號已經刪除成功", "demo_user": "展示使用者", "filter_account": "鍵入以篩選使用者帳號", "modify_account_dialog": "修改使用者帳號", "new_password": "新密碼", "old_password": "舊密碼", "password": "密碼", "password_policy_mismatch": "密碼不符合密碼政策", "superuser": "管理員", "title": "帳號管理", "ui_profile": "UI 描述檔", "update_user_fail": "無法更新新使用者帳號", "update_user_success": "帳號已經更新成功", "user": "使用者", "user_account": "使用者帳號", "user_exist": "使用者已經存在", "username": "使用者名稱"}, "account_password": {"1st_email": "第一個電子郵件收件者", "2nd_email": "第二個電子郵件收件者", "account_audit": "帳號審計", "account_info": "帳號資訊", "account_info_content": "點擊「更新」按鈕以檢索所有裝置的帳號資訊。這可能要花點時間。", "account_management": "帳號管理", "account_password_management_automation": "帳號和密碼管理自動化", "account_status_audit": "帳號審計", "account_status_baseline": "帳號基準", "account_status_baseline_content": "此操作將建立一個新的基準並覆蓋現有的。", "accounts": "帳號", "activate": "已生效", "add_account": "新增帳號", "add_temporary_account": "新增臨時帳號", "added_account": "新增帳號", "admin": "管理員", "apply_accounts": "套用的帳號", "audit_automation": "審計自動化", "authority": "權限", "baseline_account": "帳號基準", "baseline_auto_check_failed": "建立基準失敗", "change_admin_name": "更改預設「管理員」名稱", "change_admin_name_content": "MXview One 將使用更新的帳號憑證存取以下設備。其他設備不受影響。", "change_admin_name_contents": "MXview One 將使用更新的帳號密碼存取以下裝置。其他設備不受影響。", "check_default_account_failed": "無法檢查預設帳號。", "check_password_length": "請確保密碼長度在裝置允許的最大密碼長度範圍內。", "compability": "相容的", "create_baseline_failed": "無法建立基準", "create_baseline_failed_no_devices": "無法建立基準。未偵測到任何設備。", "days": "天", "default_account": "預設使用者名稱/密碼", "default_password_audit": "預設密碼審計", "default_password_audit_info": "掃描預設帳號密碼可能需要一些時間，並且會使畫面暫時無法使用。您確定要繼續嗎？", "delete_account": "刪除帳號", "delete_temporary_account": "刪除臨時帳號", "delete_temporary_account_info": "您確定要刪除該臨時帳號嗎？", "delete_temporary_accounts_info": "您確定要刪除這些臨時帳號嗎？", "deleted_account": "已刪除的帳號", "device_alias": "設備別名", "device_ip": "設備 IP", "edit_account": "編輯帳號", "edit_temporary_account": "編輯臨時帳號", "email_server_configuration": "Email 伺服器設定", "email_server_hint": "如果您沒有收到包含驗證碼的電子郵件，請檢查您的垃圾郵件資料夾或驗證", "email_verified": "電子郵件地址已驗證。", "end_date": "失效日期", "end_time": "結束時間", "fatiled_to_audit_account_due": "無法完成帳號審核。無法從 {{ ip }} 檢索設備帳號資訊。", "fatiled_to_create_baseline_due": "無法建立基準。無法從 {{ ip }} 檢索設備帳號資訊。", "get_baseline_failed": "無法取得基準", "get_device_account_failed": "查詢設備帳號資訊失敗，其他請求正在進行中。請稍後重試。", "incorrect_verification_code": "驗證碼不正確。", "last_audit_time": "上次審計", "last_execution_time": "上次執行", "max_char": "最多 {{num}} 個字元", "model": "型號", "mxa_char": "最多 {{num}} 個字元", "new_password": "新密碼", "new_username": "新使用者名稱", "next_audit_time": "下次審計", "next_schdeule_start_time": "下次預定時間", "no_data": "N/A", "not_activate": "尚未生效", "not_started": "待執行", "not_started_hint": "由於系統關閉，此任務未執行。點擊「重新產生密碼」按鈕來執行任務。", "now": "立即", "operation": "操作", "password": "密碼", "password_automation": "密碼自動化", "password_automation_schedule": "密碼自動化排程", "password_automation_settings": "密碼自動化精靈", "password_email_receiver": "密碼電子郵件收件者", "password_regenerated_info": "MXview One 將使用以下設定為每台設備產生新密碼。", "password_resend_info": "您確定要將設備密碼重新傳送給以下收件者嗎？", "password_resend_result_info": "MXview One 已將設備帳號和密碼檔案傳送至以下電子郵件地址：", "password_strength": "密碼強度", "random_password_complexity": "設定密碼複雜性", "random_password_length": "隨機密碼長度", "random_password_length_info": "MXview One 將為所選設備產生隨機密碼。", "randomized_password_failed": "失敗（設備帳號與資料庫帳號不符。）", "refresh_hint": "在繼續執行此操作之前，請按「更新」按鈕檢索裝置帳號。", "regenerate_password": "重新產生密碼", "resend_password_email": "重新發送密碼電子郵件", "retrieve_data": "檢索資料", "retry_failed_devices": "重試失敗的設備", "schedule": "排程", "schedule_interval": "間隔", "script_error": "此欄位不得包含以下任何字元：#%&amp;*{}|:\\&quot;&lt;&gt;?/\\\\", "select_device": "選擇設備", "select_device_random_password": "選擇要產生隨機密碼的設備。", "send_password_email": "傳送密碼電子郵件", "send_password_email_success": "MXview One 已將設備帳號、密碼和執行結果傳送至以下電子郵件收件者：", "set_password_to_device": "將密碼套用於設備", "set_schedule_interval_failed": "無法設定排程間隔。", "set_schedule_interval_success": "排程間隔設定成功。", "start_date": "生效日期", "start_over": "重新開始", "start_time": "開始時間", "start_wizard": "啟動精靈", "status": {"cli_session_timeout": "CLI 會話逾時", "failed": "失敗", "failed_account_exist": "失敗（該帳號已存在）", "failed_account_password_incorrect": "失敗（帳號或密碼錯誤）", "failed_limit_reached": "失敗（已達到設備帳號限制）", "failed_not_support_role": "失敗（設備不支援）", "failed_other_request": "失敗（其他請求正在進行中）", "failed_retrieve_account_info": "失敗（無法檢索帳號資訊）", "finished": "已完成", "in_progress": "進行中 ...", "waiting": "等待中"}, "supervisor": "監工", "temporary_account": "臨時帳號", "test_eamil_recipient": "測試電子郵件收件者", "title": "帳號及密碼", "unable_to_get_accounts": "無法取得帳號", "user": "使用者", "username": "使用者名稱", "verififcation_code": "驗證碼", "verift_title": "驗證您的 MXview One 帳號", "verify_code_expiration": "驗證碼過期時間", "verify_email_not_allowed": "請等待至少一分鐘後再發送另一封電子郵件。", "verify_email_password_receiver": "驗證帳號和密碼電子郵件收件者", "verify_email_receiver": "驗證電子郵件收件者", "verify_email_server_failed": "電子郵件伺服器配置無效。無法傳送電子郵件。", "verify_user_failed": "無效的使用者名稱或密碼"}, "ADD_DEVICE": {"add_device_fail": "無法新增設備", "add_device_fail_error_message": {"device_has_existed": "具有該IP的設備已存在", "license_limitation_reached": "已達到許可限制", "model_not_exist": "該型號不存在。"}, "add_device_success": "新增設備成功", "assign_group": "指派群組", "assign_model": "指定型號", "authentication": "認證", "auto_detect_model": "自動偵測", "data_encryption": "數據加密", "encryption_password": "加密密碼", "encryption_type": "加密方式", "field_required": "此欄位為必填", "snmp_setting": "SNMP 設置", "snmp_version": "SNMP 版本", "title": "新增設備"}, "ADD_LINK": {"alias": "別名", "device": "設備", "fail": "無法增加連線", "from": "從", "ip_address": "IP 位址", "model": "型號", "only_number": "僅支援數字。", "port": "埠", "success": "新增連線成功", "title": "新增連線", "to": "到"}, "API_MANAGEMENT": {"access_count": "使用次數", "add_failed": "新增 API 金鑰失敗", "add_success": "新增 API 金鑰成功", "add_title": "新增 token", "api_key": "API 金鑰", "application_name": "應用程式名稱", "create_time": "建立時間", "delete_failed": "刪除 API 金鑰失敗", "delete_success": "刪除 API 金鑰成功", "edit_title": "編輯 token", "filter": "鍵入以篩選 API 金鑰", "regenerate_api_key": "重新產生 API 金鑰", "regenerate_failed": "重新產生 API 金鑰失敗", "regenerate_success": "重新產生 API 金鑰成功", "title": "API 管理", "update_failed": "更新 API 金鑰失敗", "update_success": "更新 API 金鑰成功"}, "ASSIGN_MODEL": {"apply_to_all": "應用此圖示至相同型號的所有設備", "assign_model_fail": "無法指定設備型號", "assign_model_success": "指定設備型號成功", "ip_address": "IP地址", "model": "型號", "model_icon": "型號圖示", "select_model": "選擇型號"}, "AVAILABILITY_REPORT": {"alias": "設備別名", "average": "平均可用性", "days": "天數", "end_date": "結束日期", "filter": "鍵入以篩選可用性報告", "from_date": "開始日期", "query_date": "查詢日期", "report_generate_day": "報表產生日期: ", "site_name": "站台名稱", "title": "可用性報告", "worst": "最差可用性"}, "BASIC_INFORMATION": {"apply_fail": "無法储存設備系統資訊", "apply_success": "設備系統資訊储存成功", "contact": "聯繫", "location": "位置", "model": "型號", "name": "名稱", "title": "基本資訊"}, "BUTTON": {"add": "新增", "add_to_scheduler": "新增到排程", "agree": "同意", "apply": "啟用", "audit": "審計", "back": "上一步", "cancel": "取消", "change": "變更", "check": "檢查", "checkNow": "立即檢查", "clear": "清除", "clear_fail_record": "清除錯誤登入記錄", "close": "關閉", "compare": "比較", "confirm": "確認", "connected": "已連線", "continue": "繼續", "copy": "複製", "create": "創建", "deactivate": "停用", "decline": "拒絕", "delete": "刪除", "disable_new_version_notifications": "停用新版本通知", "disconnected": "未連線", "download": "下載", "download_all_logs": "下載所有日誌", "download_filter_logs": "下載過濾器日誌", "edit": "編輯", "enable_new_version_notifications": "啟用新版本通知", "execute": "執行", "faqs": "FAQs", "got_it": "知道了", "ignore": "忽略", "leave": "離開", "next": "下一步", "ok": "確認", "query": "查詢", "reboot": "重啟", "redirect": "重定向", "refresh": "重新整理", "regenerate": "重新產生", "resend": "重新發送", "reset": "重設", "retry_failed_devices": "重試失敗的設備", "run": "執行", "save": "儲存", "scan": "掃描", "search": "搜尋", "security_patch_available": "可用安全補丁", "select": "選擇", "select_firmware_version": "選擇韌體版本", "send": "發送", "send_test_eamil": "發送測試電子郵件", "set": "設定", "upgrade": "升級", "upgrade_firmware": "更新韌體", "upload": "上傳", "verify": "驗證", "verify_email": "驗證電子郵件"}, "cli_object_database": {"add_cli_fail": "無法新增 CLI 腳本", "add_cli_object": "新增 CLI 腳本", "add_cli_success": "新的 CLI 腳本已成功新增", "before_date": "日期", "before_time": "時間", "cli_objects": "CLI 腳本", "cli_script": "CLI腳本", "delete_all_execution_results": "刪除所有 CLI 腳本執行結果", "delete_all_execution_results_before_time": "刪除之前的腳本執行結果", "delete_all_execution_results_before_time_desc": "您確定要刪除 {{param}} 之前的所有腳本執行結果嗎？", "delete_all_execution_results_desc": "您確定要刪除所有 CLI 腳本執行結果嗎？", "delete_cli_fail": "無法刪除 CLI 腳本", "delete_cli_object": "刪除 CLI 腳本", "delete_cli_object_desc": "您確定要刪除此 CLI 腳本嗎？", "delete_cli_object_disabled": "該腳本無法刪除，因為它已連結到排程任務或腳本自動化。", "delete_cli_objects_desc": "您確定要刪除這些 CLI 腳本嗎？", "delete_cli_success": "CLI 腳本刪除成功", "delete_execution_result_fail": "無法刪除執行結果", "delete_execution_result_success": "腳本執行結果刪除成功", "delete_results_before_time": "刪除 CLI 腳本執行結果", "description": "描述", "download_all_execution_results": "下載所有執行結果", "download_all_execution_results_fail": "無法下載執行結果", "download_execution_results_failed_hint": "沒有可供下載的執行結果。", "edit_cli_fail": "無法更新 CLI 腳本", "edit_cli_object": "編輯 CLI 腳本", "edit_cli_success": "CLI 腳本更新成功", "execution_results": "執行結果", "get_cli_fail": "無法取得 CLI 腳本", "linked_scheduled_task": "連結的排程任務", "linked_script_automation": "連結的腳本自動化", "name": "名稱", "non_ascii": "僅接受 ASCII 字元。", "scheduled_execution_cli_object_desc": "您可以從管理 > 維護計畫頁面新增排程任務以在指定日期和時間執行 CLI 腳本。", "scheduled_execution_cli_object_info": "排程腳本", "scheduled_task": "排程任務", "title": "儲存的 CLI 腳本"}, "COMBO_BOX": {"disabled": "未啟用", "enabled": "已啟用", "export_all_event_csv": "將所有事件匯出到 CSV", "export_all_syslog_csv": "將所有 Syslog 匯出到 CSV", "export_csv": "匯出 CSV", "export_pdf": "匯出 PDF", "sequential": "嚴格依序", "smart_concurrent": "智慧依序"}, "COMMAND_BAR": {"hide_automation_button": "隱藏按鈕工具", "hide_button_panel": "隱藏按鈕面板", "hide_detail": "隱藏詳細資訊", "hide_group": "隱藏群組", "list_view": "清單檢視", "show_automation_button": "顯示按鈕工具", "show_button_panel": "顯示按鈕面板", "show_detail": "顯示詳細資訊", "show_group": "顯示群組", "topology_view": "拓撲檢視"}, "CONFIG_CENTER": {"alias_name": "別名", "backup_config": "備份配置", "backup_message": "系統會將檔案壓縮成一個zip檔", "backup_tab": "備份", "backup_tab_hint": "請先轉到備份分頁並匯出設備配置", "compare_config_basement": "比較基準: {{compareConfigFileName}}", "compare_config_dialog_title": "比較配置", "compare_tab": "記錄", "compare_target": "比較目標", "configuration_file": "配置檔案", "configuration_name": "配置名稱", "create_time": "建立時間", "delete_config_dialog_title": "刪除配置", "delete_config_failed": "刪除設備配置失敗", "delete_config_success": "刪除設備配置成功", "delete_config_warning_message": "你確定要刪除選定的配置嗎？", "device_list": "設備列表", "export_failed": "匯出失敗", "export_success": "匯出成功", "from_date": "開始日期", "group_name": "群組", "ip_address": "IP 位址", "last_check_time": "上次檢查時間", "local_file": "本地檔案", "restore_config": "還原配置", "restore_device": "還原設備 - {{selectedDeviceIP}}", "restore_tab": "還原", "site_name": "站台", "time": "時間", "title": "設備配置中心", "to_date": "結束日期"}, "DASHBOARD": {"adpDestIp": "最常發生 ADP 規則事件的前 5 名目的地 IP", "adpSrcIp": "最常發生 ADP 規則事件的前 5 名來源 IP", "ap_devices": "AP 設備", "ap_traffic_load": "AP 流量負載", "baseline": "基本", "client_devices": "用戶端設備", "critial_devices": "嚴重", "device_availability": "設備可用性", "device_availability_intro": "MXview One 使用以下的公式計算最近 24 小時的可用性。\n可用性比率 = (運作時間 / (運作時間+停機時間)) * 100", "device_summary": "設備摘要", "devices": "設備", "disk_space_utilization": "磁碟空間使用率", "dpiDestIp": "最常發生協議過濾規則事件的前 5 名目的地 IP", "dpiSrcIp": "最常發生協議過濾規則事件的前 5 名來源 IP", "event_highlight": "事件摘要", "healthy_devices": "正常", "icmp_unreachable": "ICMP unreachable", "iec_level_1": "中", "iec_level_2": "高", "ipsDestIp": "最常發生 IPS 規則事件的前 5 名目的地 IP", "ipsSrcIp": "最常發生 IPS 規則事件的前 5 名來源 IP", "l3DestIp": "最常發生第 3-7 層規則事件的前 5 名目的地 IP", "l3SrcIp": "最常發生第 3-7 層規則事件的前 5 名來源 IP", "last_1_day": "最近1天", "last_1_hours": "最近1小時", "last_1_weeks": "最近1週", "last_2_weeks": "最近2週", "last_24_hours": "最近24小時", "last_3_days": "最近3天", "last_3_hours": "最近3小時", "last_30_days": "最近30天", "last_30_minutes": "最近30分鐘", "last_7_days": "最近7天", "last_update": "最後更新:", "link_down": "斷線", "link_up": "連線", "linkButton": "顯示事件日誌", "not_pass": "沒有通過", "now": "即時", "open": "未達安全層級", "pass": "通過", "reboot_times": "Cold/Warm Start trap", "refresh_all": "全部重新整理", "security_level": "安全層級", "security_summary": "安全摘要", "selecting_visible_item": "選擇要顯示的項目", "set_default_tab": "設定為預設選項卡", "tabs": {"cybersecurity": "網路安全", "general": "一般", "wireless": "無線"}, "title": "儀表板", "total_availability": "Device availability is below {{param}}%", "total_devices": "所有設備", "unknown": "未知", "view_network_topology": "查看網路拓撲", "warning_devices": "警告", "wireless_device_summary": "無線設備摘要"}, "DATABASE_BACKUP": {"database_name": "名稱", "fail": "資料庫備份失敗", "success": "資料庫備份成功 {{param1}}", "title": "資料庫備份"}, "DEVICE_LOCATOR": {"stop_trigger_locator": "結束", "title": "定位設備", "trigger_locator": "開始", "trigger_locator_fail": "無法觸發設備定位", "trigger_locator_off": "關閉設備定位成功", "trigger_locator_on": "觸發設備定位成功"}, "device_management": {"built_in_command_execution_process_is_running": "無法發送命令。 另一個命令正在運行。 稍後再試。", "execute_fail": "執行失敗", "limited_support": "有限度支援，請查看使用手冊。", "select_device": "選擇裝置", "select_operation": "選擇操作"}, "DEVICE_PANEL": {"panel_status": "面板狀態", "panel_zoom_size": "設備面板縮放尺寸", "port": "埠"}, "DEVICE_POLLING_SETTING": {"consecutive_icmp_fail_trigger": "觸發ICMP無回應事件的連續輪詢失敗次數", "consecutive_snmp_fail_trigger": "觸發SNMP無回應事件的連續輪詢失敗次數", "icmp_polling_interval": "ICMP輪詢間隔", "snmp_polling_interval": "SNMP輪詢間隔", "title": "輪詢間隔設置"}, "DEVICE_PROPERTIES": {"basic_property": {"alias": "別名", "availability": "可用性", "bios_version": "BIOS/引導加載程序版本", "cpu_loading": "CPU負載 (%)", "cpu_loading_30_seconds": "CPU負載30秒 (%)", "cpu_loading_300_seconds": "CPU負載300秒 (%)", "cpu_loading_5_seconds": "CPU負載5秒  (%)", "cpu_utilization_300_seconds": "近300秒CPU利用率(%)", "cpu_utilization_60_seconds": "近60秒CPU利用率(%)", "cpu_utilization_900_seconds": "近900秒CPU利用率(%)", "disk_utilization_unit": "磁盤利用率 (%)", "fw_system_version": "固件/系統映像版本", "fw_version": "韌體/系統映像版本", "mac_address": "MAC地址", "memory_usage": "記憶體使用情況", "memory_usage_unit": "記憶體使用情況 (%)", "model_name": "型號名稱", "os_type": "操作系統", "power_comsumption": "耗電量 (W)", "serial_number": "序列號", "system_contact": "系統聯繫", "system_description": "系統描述", "system_location": "系統位置", "system_name": "系統名稱", "system_object_id": "Sysobjectid", "system_up_time": "系統運行時間", "title": "基本設備屬性"}, "cellular": {"cellular_carrier": "行動網路業者", "cellular_ip_address": "行動網路 IP 地址", "cellular_mode": "行動網路模式", "cellular_signal": "行動網路訊號", "imei": "IMEI", "imsi": "IMSI", "title": "行動網路資訊"}, "goose_table": {"app_id": "應用程式_ID", "gocb_name": "GOCB名稱", "goose_address": "GOOSE地址", "ied_name": "IED名稱", "port": "入站埠", "rx_counter": "RX計數器", "status": "狀態", "tampered_port": "被篡改的埠", "tampered_port_status": "被篡改的埠 {{port}}", "title": "GOOSE檢查", "type": "類型", "vid": "VID"}, "ipsec": {"l2tp_status": "IPSec L2TP狀態", "local_gateway": "本地閘道", "local_subnet": "本地子網路", "name": "IPSec名稱", "phase_1_status": "IPSec第1階段狀態", "phase_2_status": "IPSec第2階段狀態", "remote_gateway": "遠端閘道", "remote_subnet": "遠端子網路", "title": "IPsec狀態"}, "link": {"from": "從", "port": "埠", "sfpTitle": "SFP 資訊", "speed": "連線速度", "title": "連線資訊", "to": "至"}, "management_interfaces": {"http_port": "HTTP埠", "https_port": "HTTPS埠", "profinet_enabled": "PROFINET啟用", "ssh_port": "SSH埠", "telnet_port": "Telnet埠", "title": "管理界面"}, "mms": {"title": "MMS資訊"}, "modbus_device_property": {"model": "模型", "revision": "修訂", "title": "Modbus 設備屬性", "vendor": "供應商"}, "not_selected": "選擇一個模組以顯示裝置詳細資訊", "other_device_properties": {"active_redundancy_protocol": "已啟用的冗餘協議", "auto_ip_config": "自動IP配置", "default_gateway": "預設閘道", "dns_1_ip_address": "DNS 1 IP地址", "dns_2_ip_address": "DNS 2 IP地址", "ip_ad_ent_addr": "ipAdEntAddr", "ip_address": "IP地址 (mib)", "mac_address": "MAC地址 (mib)", "model_name": "型號名稱", "monitor_current_mode": "監控現有模式", "monitor_down_stream_rate": "監控下行速率", "monitor_snr": "監測信噪比", "monitor_up_stream_rate": "監控上行速率", "netmask": "網路遮罩", "title": "其他設備屬性"}, "port": {"if_number": "ifNumber", "interface": "介面", "number_of_ports": "埠數量", "poe_port_class": "PoE埠類", "poe_power_legacy_pd_detect": "PoE電源舊版PD檢測", "poe_power_output_mode": "PoE電源輸出模式", "title": "埠資訊"}, "power": {"power_1_status": "電源 1 狀態", "power_2_status": "電源 2 狀態", "title": "電源狀態"}, "redundancy": {"active_redundancy_protocol": "已啟用的冗餘協議", "dh": "Dual Homing", "iec_624393_redundancy_protocol": "IEC 62439-3 冗餘協議", "rstp": "RSTP", "tc": "Turbo Chain", "title": "冗餘", "trv2": "Turbo Ring V2"}, "selected_module": "選定模組", "snmp": {"1st_trap_community": "第一組 Trap Community", "2nd_trap_server_community": "第二組 Trap Community", "inform_enabled": "通知啟用", "inform_retries": "通知重試", "inform_timeout": "通知超時", "read_community": "Read Community", "title": "SNMP資訊", "trap_server_address_1": "第一組 Trap 伺服器位址", "trap_server_address_2": "第二組 Trap 伺服器位址"}, "title": "設備屬性", "vpn": {"from_ip": "VPN來自IP", "local_connection_name": "本地VPN連接名稱", "remote_connection_name": "遠端VPN連接名稱", "to_ip": "VPN到IP"}, "wireless": {"channel_width": "通道寬度", "client_ip": "用戶端 IP", "client_mac": "用戶端 MAC", "client_RSSI": "用戶端訊號強度 (dBm)", "rf_type": "RF 類型", "ssid_index": "SSID Index", "title": "無線資訊", "vap_mgmt_encryption": "VAP 管理加密", "vap_wpa_encrypt": "VAP WPA 加密", "vapAuthType": "VAP 驗證類型"}}, "DEVICE_SETTING": {"advanced": "進階", "alias": "別名", "alias_input_invalid": "請輸入有效的設備別名", "apply_fail": "無法設置設備訪問密碼", "apply_success": "已成功設置設備參數", "availability_time_frame": "妥善率計算時間長度", "get_parameter_fail": "無法取得設備參數", "input_error_message": "輸入錯誤，請檢查您的設置，然後重試", "modify_device_alias": "修改設備別名", "password": "密碼", "password_input_invalid": "請輸入有效的密碼", "polling_interval": "輪詢間隔", "polling_ip": "輪詢 IP", "snmp_configuration": "SNMP 組態", "snmp_port_invalid": "請輸入有效SNMP埠", "title": "設備設置", "use_global": "使用全域使用者帳號和存取密碼", "use_global_device_settings": "使用全域設備設置", "username": "使用者名稱", "username_input_invalid": "請輸入有效的使用者名稱"}, "DEVICE": {"device_properties": "設備屬性", "device_role": "設備角色", "filter_device": "鍵入以篩選設備", "filter_register_device": "鍵入以篩選註冊設備", "na": "不明", "properties": {"availability": "可用性", "device_alias": "設備別名", "device_ip": "設備 IP", "firmware_version": "韌體版本", "location": "位置", "mac_address": "MAC 位址", "model_name": "型號名稱", "mxsec_flag": "安全模組", "severity": "嚴重性"}, "registered_devices": "已註冊", "site_name": "站台名稱", "title": "設備列表", "unregistered_devices": "未註冊"}, "DeviceDashboard": {"avg_erase_count": "Avg. <PERSON><PERSON>", "change_disk_hint": "請更換磁碟", "chartTitle": {"60s_cpu": "CPU 利用率（過去 60 秒）", "connection": "連線狀態", "cpu": "CPU使用情況", "disk": "磁碟使用", "memory": "記憶體使用情況", "noiseFloor": "Noise Floor", "raid_mode": "RAID 模式", "signalStrength": "訊號強度", "smart": "S.M.A.R.T.​", "snr": "雜訊比", "traffic": "流量負載"}, "connected": "已連接", "current_status": "即時狀態:", "cycle_limitation": "Cycle Limitation", "icmp_not_support": "ICMP 裝置不支援此功能", "link_down_port": "斷線埠", "link_up_port": "連線埠", "managed": "管理中", "migrating_data": "遷移資料", "no_raid": "没有 RAID", "normal": "正常", "raid": "RAID", "rebuild": "重建", "smart_hint": "(Self-Monitoring Analysis and Reporting Technology) 代表磁碟的健康狀態和壽命訊息", "unreachable": "設備無法以{{ warningWording }}存取，MXview One 可能無法取回完整的設備資訊。"}, "DIALOG": {"add_wifi_ssid": {"aes": "AES", "clear_all_existing_ssid": "清除所有現有 SSID", "eapol_version": "EAPOL 版本", "encryption": "加密", "open": "Open", "passphrase": "密碼", "personal": "Personal", "protected_management_frame": "受保護的管理訊框", "rf_band": "RF 頻段", "security": "安全", "ssid": "SSID", "title": "新增 Wi-Fi SSID", "tkip_aes_mixed": "TKIP / AES 混合", "wpa_mode": "WPA 模式"}, "auto_layout": {"desc": "您確定要自動排列拓撲? (目前的拓撲將會被覆蓋)", "title": "自動排列"}, "auto_topology": {"advanced": "進階拓墣分析", "advanced_desc": "*需要花費較長時間", "advanced_hint": "會在支援 LLDP 或 Forwarding Table 的 ICMP 設備之間增加連線", "fail": "自動拓撲失敗", "link_check": "嚴格連線驗證模式", "link_check_hint": "如果啟用，只有當兩端的設備在各自的 LLDP 表中擁有另一台設備的資訊時，設備間的連線才會在拓撲結構中顯示。", "new_topology": "建立拓墣", "new_topology_desc": "現有連線將會被刪除", "success": "自動拓撲成功", "title": "自動拓撲", "update_topology": "更新拓墣", "update_topology_desc": "保留現有連線, 新連線將會被增加"}, "background_dialog": {"content": "請先設置背景圖\n 背景圖可為樓層平面圖或可顯示其覆蓋區的圖片", "set_now": "現在設置", "title": "設置背景"}, "change_group": {"assign_to_group": "分配到群組", "change_group_fail": "修改群組失敗", "change_group_success": "修改群組成功", "current_group": "群組", "ip": "IP 地址", "title": "修改群組"}, "change_wifi_channel": {"channel": "頻道", "channel_width": "頻道寬度", "execute_button": "變更", "title": "更改 Wi-Fi 頻道"}, "create_group": {"assign_group": "分配到群組", "create_group_fail": "建立群組失敗", "create_group_success": "建立群組成功", "current_group": "群組", "empty_group_name": "您必須輸入群組名稱", "group_desc": "群組描述", "group_name": "群組名稱", "parent_group": "上層群組", "title": "建立群組"}, "create_snapshot": {"execute_button": "創建", "title": "建立快照"}, "data_not_ready": {"content": "設備資料還沒有準備好，請稍後再試。", "title": "請稍後再試"}, "delete_account": {"delete_confirm_message": "是否確定要刪除此帳號?", "title": "刪除帳號"}, "delete_background": {"desc": "確定要刪除背景?", "failed": "刪除背景失敗", "success": "刪除背景成功", "title": "刪除背景"}, "delete_custom_opc": {"delete_confirm_message": "您確定要刪除這個自訂OPC標籤嗎？", "title": "刪除自訂OPC標籤"}, "delete_device": {"delete_wireless_client_alert": "被刪除的無線用戶端設備的歷史資料也將被清除，並影響所列功能的可追溯性。您想繼續嗎？", "delete_wireless_client_alert_title": "確認設備的刪除", "desc": "確定要刪除設備嗎?", "desc_multi": "確定要刪除這些設備嗎?", "failed": "刪除設備失敗", "success": "刪除設備成功", "title": "刪除設備"}, "delete_group": {"desc": "確定要刪除群組嗎?", "desc_multi": "確定要刪除這些群組嗎?", "failed": "刪除群組失敗", "success": "刪除群組成功", "title": "刪除群組"}, "delete_link": {"desc": "確定要刪除連線嗎?", "desc_multi": "確定要刪除這些連線嗎?", "failed": "刪除連線失敗", "success": "刪除連線成功", "title": "刪除連線"}, "delete_objects": {"desc": "確定要刪除所有選定的物件嗎?", "failed": "刪除物件失敗", "success": "刪除物件成功", "title": "刪除物件"}, "delete_site": {"desc": "確定要刪除站台嗎?", "failed": "刪除站台失敗", "success": "刪除站台成功", "title": "刪除站台"}, "device_settings": {"fail": "設定鏈路預算參數(個別設備)失敗", "rx_antenna_gain": "接收天線增益", "rx_cable_loss": "接收線纜損耗", "success": "設定鏈路預算參數(個別設備)成功", "title": "鏈路預算參數(個別設備)", "tx_antenna_gain": "發送天線增益", "tx_cable_loss": "發送線纜損耗"}, "disable_unsecured": {"execute_button": "停用 HTTP 和 Telnet", "title": "停用不安全的 HTTP 和 Telnet 終端"}, "disable_unused": {"execute_button": "停用未使用的連接埠", "keep_port_available": "確保使用中的連接埠不會因為暫時斷線而被關閉", "title": "停用未使用的乙太網路和光纖連接埠"}, "discovery_device": {"another_discovery_error": "另一個設備發現正在處理", "discovering": "正在發現設備", "discovery_finish": "設備發現完畢", "error": "設備發現錯誤", "title": "設備發現"}, "dynamic_mac_sticky": {"address_limit": "位址限制", "alias": "別名", "mac_sticky": "Sticky MAC", "mac_sticky_settings": "Sticky MAC 設定", "packet_drop": "丟棄封包", "port": "連接埠", "port_duplicated": "該連接埠已被設定。", "port_format_not_equal": "型號必須具有相同的連接埠格式和連接埠數量。", "port_selection_guide": "連接埠對照表", "port_shutdown": "關閉連接埠", "security_action": "安全行動", "title": "動態 Sticky MAC"}, "export_config": {"config_center": "組態管理中心", "config_file": "組態檔", "export": "匯出", "fail": "匯出設備設置檔失敗", "hint": "* 請確認設備的使用者名稱和密碼有在\"進階設定\"裡設定正確", "success": "匯出設備設置檔成功", "title": "匯出配置"}, "goose": {"how_to_resolve": "如何解決？", "import_scd_tooltip": "請匯入一個SCD檔以查看GOOSE訊息。點擊Power > Import SCD。", "ip_port": "{{ ip }}的{{ port }}埠", "open_web_console": "打開Web控制台", "port_tampered_msg": "GOOSE埠被篡改的原因是", "port_tampered_title": "解決GOOSE埠被篡改的問題", "reset_goose": "重置被篡改的GOOSE訊息", "reset_goose_desc": "GOOSE訊息：{{ cbName }}/{{ appId }}/{{ mac }}。", "reset_goose_title": "您確定要重置這個被篡改的GOOSE訊息的所有實例嗎？", "resolve_goose_tampered_desc_1": "請嘗試以下步驟來解決GOOSE埠被篡改的問題", "resolve_goose_tampered_desc_2": "1. 檢查 IED 的設置", "resolve_goose_tampered_desc_3": "確保IED的GOOSE發佈/訂閱資訊被正確設置。", "resolve_goose_tampered_desc_4": "2. 檢查埠狀態", "resolve_goose_tampered_desc_5": "請檢查{{ ip }}的埠{{ port }}狀態。", "resolve_goose_tampered_desc_6": "2. 檢查以確保所有設備都被授權", "resolve_goose_tampered_desc_7": "請檢查網路上是否有任何未經授權的設備。", "resolve_goose_tampered_desc_8": "請嘗試以下步驟解決GOOSE SA被篡改的問題", "resolve_goose_timeout_desc_1": "嘗試以下步驟來解決GOOSE超時問題。", "resolve_goose_timeout_desc_10": "還是不能運作？", "resolve_goose_timeout_desc_11": "移除SFP模組並重新安裝。", "resolve_goose_timeout_desc_12_1": "如果有任何技術問題，請先聯繫您的", "resolve_goose_timeout_desc_12_2": "通路夥伴", "resolve_goose_timeout_desc_12_3": "。", "resolve_goose_timeout_desc_13_1": "聯繫", "resolve_goose_timeout_desc_13_2": "Moxa技術支援", "resolve_goose_timeout_desc_13_3": "如果您仍然需要額外的支援。", "resolve_goose_timeout_desc_2": "1. 檢查IED的設置", "resolve_goose_timeout_desc_3": "確保IED的發佈/訂閱GOOSE訊息的設置正確。", "resolve_goose_timeout_desc_4": "2. 確保埠不是斷線狀態", "resolve_goose_timeout_desc_5": "檢查 GOOSE 流({{ cbName }}/{{ appId }}/{{ mac }})上的每個設備的埠不是斷線狀態。", "resolve_goose_timeout_desc_6": "3. 確保埠沒有任何 TX/RX 錯誤。", "resolve_goose_timeout_desc_7": "點擊一個連線，選擇 \"連線流量\"，查看\"封包錯誤率\"的部分。確保該埠沒有任何錯誤。", "resolve_goose_timeout_desc_8": "4. 檢查光纖埠是否超過一定的閾值", "resolve_goose_timeout_desc_9": "選擇 \"SFP\" > \"SFP 列表\" 按鈕。確保埠沒有超過一定的閾值。", "sa_tampered_msg": "GOOSE SA被篡改", "sa_tampered_name_msg": "GOOSE訊息（{{ cbName }}/{{ appId }}/{{ mac }}）與另一個GOOSE源位址衝突。", "sa_tampered_title": "解決GOOSE SA被篡改的問題", "tampered": "被篡改", "timeout": "超時", "timeout_msg": "GOOSE超時的原因是", "timeout_title": "解決GOOSE超時問題"}, "import_config": {"config_center": "組態管理中心", "config_file": "組態檔", "config_file_error": "Mxview只支援ini檔案格式", "config_file_size_error": "最大檔案大小為3MB", "fail": "匯入設備組態檔失敗", "hint": "* 請確認設備的使用者名稱和密碼有在\"進階設定\"裡設定正確", "import": "匯入", "success": "匯入設備組態檔成功", "title": "匯入配置"}, "link_traffic": {"date": "日期", "from": "從", "packet_error_rate_title": "封包錯誤率", "port_traffic_title": "頻寬使用率", "time": "時間", "to": "到", "utilization": "頻寬利用率", "value": "值"}, "mac_sticky_switch": {"mac_sticky": "Sticky MAC", "title": "Sticky MAC 開/關"}, "maintain_group": {"change_icon": "更改群組圖示", "create": "建立", "create_group_fail": "建立群組失敗", "create_group_success": "建立群組成功", "delete": "刪除", "delete_group_fail": "刪除群組失敗", "delete_group_success": "刪除群組成功", "empty_group_name": "您必須輸入群組名稱", "group_desc": "群組描述", "group_name": "群組名稱", "modify_group_fail": "修改群組失敗", "modify_group_success": "修改群組成功", "reset_icon": "重置為預設圖示", "title": "群組維護"}, "ping": {"failed": "<PERSON> 失败", "title": "<PERSON>"}, "policy_profile": {"delete_msg": "您確定要刪除所選的配置檔嗎？", "delete_title": "刪除配置檔"}, "reboot": {"execute_button": "重啟", "reboot_sequence": "重啟順序", "title": "重啟", "unable_determine_reboot_sequence_hint": "無法決定重新啟動順序。 確保拓樸包含一台裝有 MXview One 的電腦，然後重試。"}, "relearn_dynamic_mac_sticky": {"execute_button": "重新學習", "title": "重新學習動態 Sticky MAC"}, "restore_to_create_snapshot": {"execute_button": "還原", "title": "還原快照"}, "scd": {"import_failed_desc": "請看下面的問題列表，請更正SCD檔案並重新嘗試匯入。", "import_failed_title": "匯入SCD的問題", "import_succeed_desc": "GOOSE訊息和流程設計已成功地建立在網路拓撲中。", "import_succeed_title": "匯入完成!", "missing": "缺少", "missing_device": "找不到以下設備", "missing_device_desc": "請看下面的問題列表。", "scd_file_error": "MXview One只支持.scd格式", "scd_file_size_error": "檔案最大尺寸為100 MB", "select_file": "選擇檔案", "tag": "標籤", "topology_change_desc_1": "嘗試以下這些步驟來解決問題。", "topology_change_desc_2": "1. 新增丟失的設備", "topology_change_desc_3": "選擇 \"編輯\" > \"新增設備\" 按鈕。", "topology_change_desc_4": "2. 再次匯入SCD檔", "topology_change_desc_5": "選擇 \"編輯\" > \"新增設備\" 按鈕。", "topology_change_title": "改變拓撲的問題", "visualize_goose_messages": "要在IED之間實現GOOSE訊息的視覺化，必須先匯入一個SCD檔。", "visualize_goose_messages_title": "視覺化GOOSE訊息"}, "set_background": {"browse": "瀏覽圖檔", "desc": "拖曳一個圖檔到這裡或是", "desc1": "來設置背景", "failed": "設置背景失敗", "image": "圖檔", "image_alpha": "透明度", "image_error": "選擇的檔案不是圖片", "image_position": "位置", "image_saturation": "飽和度", "image_x": "X", "image_y": "Y", "preview": "預覽", "size_error": "圖片大小必須在1KB ~ 20MB之間。", "success": "設置背景成功", "title": "設置背景", "topology_size": "拓撲大小"}, "set_document": {"current_filename": "當前檔案名稱", "delete": "刪除檔案", "error": "Mxview只支援PDF檔案格式", "failed": "設定檔案失败", "file_size_error": "最大檔案大小為20MB.僅限PDF。", "open": "開啟檔案", "set": "設定檔案", "success": "設定檔案成功", "title": "設定檔案", "upload": "選擇上傳檔案"}, "set_port_label": {"error": "錯誤", "failed": "設定連線標籤失败", "from": "從:", "success": "設定連線標籤成功", "title": "設定連線標籤", "to": "到:", "use_custom_label": "自訂標籤", "vpn_link_desc": "不能修改VPN連線標籤"}, "set_scale": {"error": "參數錯誤", "fail": "設定比例失敗", "success": "設定比例成功", "title": "設定比例"}, "severity_threshold": {"bandwidth_input_invalid": "請輸入一個0-100的整數", "bandwidth_utilization": "頻寬利用率", "critical": "嚴重", "failed": "設定嚴重性閾值失敗", "information": "資訊", "over": "高於", "packet_error_rate": "封包錯誤率", "sfp_rx_over": "SFP RX 高於", "sfp_rx_under": "SFP RX 低於", "sfp_temp_over": "SFP 溫度高於", "sfp_threshold": "SFP 閾值", "sfp_tx_over": "SFP TX 高於", "sfp_tx_under": "SFP TX 低於", "sfp_vol_over": "SFP 電壓高於", "sfp_vol_under": "SFP 電壓低於", "success": "設定嚴重性閾值成功", "title": "嚴重性閾值", "under": "低於", "warning": "警告"}, "sfp_info": {"date": "日期", "dateError": "日期區間錯誤", "from": "從", "port": "埠", "sfpRxPower": "SFP RX", "sfpRxPower_label": "RX", "sfpRxPower_scale": " dBm", "sfpTemperature": "SFP 溫度", "sfpTemperature_label": "溫度", "sfpTemperature_scale": " °C", "sfpTxPower": "SFP TX", "sfpTxPower_label": "TX", "sfpTxPower_scale": " dBm", "sfpVoltage": "SFP 電壓", "sfpVoltage_label": "電壓", "sfpVoltage_scale": " V", "time": "時間", "title": "SFP 資訊", "to": "到", "value": "值"}, "sfp_sync": {"confirm_desc": "您確定要從設備上同步SFP閾值嗎？", "content": "您可以從MOXA交換機上同步SFP Threshold。同步後，光纖檢查的溫度、Tx Power和Rx Power將被同步到每個連線的SFP Threshold。", "failed": "同步SFP閾值失敗", "hint": "* 要檢查SFP閾值，您可以點擊一個連線，選擇嚴重程度閾值>SFP閾值。", "success": "同步SFP閾值成功", "title": "從設備同步SFP閾值"}, "wireless_settings": {"fail": "設定鏈路預算參數(通用)失敗", "rxSensitivityHigh": "接收感度(高)", "rxSensitivityLow": "接收感度(低)", "rxSensitivityMedium": "接收感度(中)", "sr": "預留安全係數", "success": "設定鏈路預算參數(通用)成功", "title": "鏈路預算參數(通用)"}}, "EMBED_WIDGET": {"click_preview": "點擊預覽", "copied_to_clipboard": "已將連結複製到剪貼簿", "copy_link": "複製連結", "custom": "自訂", "desc": "將這段程式碼貼到任何 HTML 頁面中", "embed": "嵌入", "height": "高度", "layout_1": "版型 1", "layout_2": "版型 2", "layout_3": "版型 3", "layout_4": "版型 4", "link": "連結", "no_api_key": "您必須要先建立 API 金鑰", "preview": "預覽", "recent_event": "最近事件", "select_api_key": "選擇 API 金鑰", "select_layout": "選擇版型", "title": "網頁嵌入小工具", "topology": "拓撲", "topology_recent_event": "拓撲與最近事件", "width": "寬度"}, "error_handler": {"error_session_expired_dialog": "登入憑證已失效。系統將會自動幫您轉址回到登入頁面。"}, "ERROR_MESSAGE": {"get_data_fail": "無法擷取資料", "input_invalid_char": "請輸入有效的IP名字", "input_invalid_characters": "該欄位不能包含以下任何字元 #%&*:<>?|{}\\\"/", "input_invalid_contact": "請輸入有效的連絡人", "input_invalid_email": "請輸入一個有效的電子郵件", "input_invalid_location": "請輸入有效的位置", "input_invalid_mac": "MAC 位址無效", "input_invalid_password_characters": "這個欄位不能包含以下任何字元 '\\\"/`。", "input_invalid_script": "此欄位不得包含以下任何字元：#%&amp;*{}|:&quot;&lt;&gt;?/\\", "input_ip_invalid": "請輸入有效的IP地址", "input_required": "此值為必填", "non_restricted_ascii": "ASCII 字符，除了 ' \\\" ` \\\\"}, "errors": {"A001": "缺少必填欄位", "A002": "缺少查詢字串", "A003": "格式不正確", "D001": "已達最大授權數量", "D002": "找不到該設備", "D003": "設備必須在線", "D004": "該設備已被刪除", "F001": "找不到韌體", "F002": "該韌體已經存在", "F003": "已達到最大韌體檔案數", "G001": "該群組已存在", "G002": "找不到該組", "G003": "預設群組不可修改", "G004": "管理員使用者無法指派到群組", "I001": "該介面已經存在", "I002": "找不到介面", "I003": "預設群組不可修改", "I004": "此介面由安全配置檔引用。", "L001": "無效的授權碼", "L002": "授權已過期", "L003": "授權碼重複", "L004": "達到最大節點數", "L005": "設備無法啟用或停用", "L006": "開始時間無效", "L007": "新型授權的開始時間無效", "O001": "該物件已存在", "O002": "找不到物件", "O003": "該物件由安全配置檔引用。", "P001": "找不到該安裝包", "P002": "該安裝包已存在", "P003": "已達最大安裝包數", "P004": "不支援的版本", "S001": "更新資料庫錯誤", "SP001": "此配置檔已存在", "SP002": "找不到配置檔", "T001": "未經授權的token", "T002": "Token 已過期", "T003": "token 無效", "U001": "沒有權限", "U002": "該用戶名已存在", "U003": "找不到用戶", "U004": "找不到該角色", "U005": "無效的使用者名稱或密碼", "U006": "密碼不符合最小長度", "U007": "密碼超過最大長度", "U008": "密碼不能與使用者名稱相同", "U009": "必須包含至少一個大寫字符", "U010": "必須包含至少一個小寫字符", "U011": "必須包含至少一位數字", "U012": "必須包含至少一個非字母數字字符", "U013": "密碼不能與之前的密碼相同", "U014": "使用者名稱無效", "Unknown": "未知錯誤"}, "EULA": {"agree_hint": "請同意使用MXview One的協議。", "eula_title": "EULA"}, "EVENT_MENU": {"ack": "確認", "ack_all": "全部確認", "filter_event": "篩選條件"}, "EVENT": {"ack_all_events": {"ack_all_event_success": "全部事件已被確認", "button_ack_hint": "確認選擇的事件", "button_hint": "確認全部事件", "confirm_message": "所有事件將全部確認，您確定要繼續?", "unable_ack_all_event": "無法確認全部事件"}, "ack": {"ack_fail": "無法確認事件", "acked": "已確認", "any": "任何", "unacked": "未確認"}, "all_event": "所有事件", "all_group": "所有群组", "all_site": "所有站台", "clear_all_events": {"button_hint": "清除所有事件", "clear_all_event_success": "事件已全部清除", "confirm_message": "所有事件全部清除，您確定要繼續?", "confirm_message_network": "所有網路和設備事件都將被清除。您確定要繼續嗎？", "confirm_message_system": "所有系統事件將被清除。您確定要繼續嗎？", "unable_clear_all_event": "不能清除所有事件"}, "custom_events": {"activate": "啟用事件", "add_custom_event": "新增自定義事件", "all": "所有事件", "all_devices": "所有設備", "apply_fail": "無法設置自定義事件", "apply_success": "自定義事件已成功新增", "below": "小於", "condition": "條件", "condition_operator": "條件", "condition_value": "條件值", "consecutive_updates": "連續輪詢次數", "delete_fail": "無法刪除自定義事件", "delete_success": "自定義事件已成功刪除", "description": "描述", "device_properties": "設備屬性", "devices": "設備", "duration": "觸發需時", "equal": "等於", "event_name": "事件名稱", "filter_custom_event": "鍵入以篩選自定義事件", "get_fail": "無法獲取自定義事件", "helper": "您可以自定事件並且將這些事件註冊到某些設備中，當設備某個屬性符合事件的條件時就會發出事件告警", "not_equal": "不等於", "over": "大於", "recovery_description": "恢復說明", "register": "註冊", "register_devices": "註冊設備", "search": "搜索", "severity": "嚴重性", "title": "自訂事件", "update_custom_event": "更新自定義事件", "update_fail": "無法更新自定義事件", "update_success": "自定義事件已成功更新"}, "event_description": {"abc_attache_warning": "插入USB設備", "abc_auto_import_warning": "自動匯入失敗", "abc_config_warning": "組態匯出失敗", "abc_detache_warning": "移除USB設備", "abc_log_warning": "事件匯出失敗", "abc_space_warning": "空間不足", "abc_unauthorized_warning": "偵測到未經授權的媒體", "abc_unknow_warning": "未知", "abc02_warning": "USB 事件: {{param}}", "account_audit_baseline_failed": "無法完成帳號審計。無法檢索所有設備資料。", "account_audit_baseline_match": "帳號審計已成功完成。結果與基準相符。", "account_audit_failed": "無法完成帳號審計。無法從位於 {{ip}} 的裝置檢索資料。", "account_audit_match": "帳號審計已成功完成。結果與基準相符。", "account_audit_mismatch": "帳號審計已成功完成。結果與基準不相符。", "account_audit_unable_retrieve_device": "無法完成帳號審計。無法檢索所有設備資料。", "accountAudit1": "帳號審計與基準不相符", "accountAudit2": "未能完成帳號審計", "all_event_clear": "所有事件已被清除", "auth_fail": "無法驗證裝置登入", "availability_down": "設備可用性低於閾值", "availability_down_recovery": "設備可用性高於閾值", "background_scan_found": "發現新設備（IP：{{ip}}）。", "cli_button_event_all_failed": "{{user}} 自 IP: {{sourceIP}} 執行了按鈕：{{cliName}}。執行結果為全部失敗。", "cli_button_event_all_finished": "{{user}} 自 IP: {{sourceIP}} 執行了按鈕：{{cliName}}。執行結果為全部完成。", "cli_button_event_all_partially_finished": "{{user}} 自 IP: {{sourceIP}} 執行了按鈕：{{cliName}}。執行結果為部分完成。", "cli_button_event_start": "{{user}} 自 IP: {{sourceIP}} 執行了按鈕：{{cliName}}。", "cli_saved_script_event": "{{user}} 自 IP: {{sourceIP}} 執行了 CLI 指令：{{cliName}}", "cli_script_event": "{{user}} 自 IP: {{sourceIP}} 開始執行 CLI。", "cold_start": "冷啟動", "custom_event_detail": "{{param1}}. 閾值={{param2}}, 值={{param3}}，{{param4}}", "custom_event_recovery": "自訂事件已恢復", "custom_event_recovery_detail": "{{param1}} 已恢復，閾值={{param2}}，值={{param3}}. {{param4}}", "custom_event_trigger": "自訂事件被觸發", "cybersecurity_event_trigger": "網路安全事件", "ddos_under_attack": "路由器遭受DoS攻擊", "ddos_under_attack_recovery": "路由器DoS攻擊恢復正常", "device_configuration_change": "設備組態已變更。", "device_firmware_upgrade": "設備韌體更新", "device_infom_receive": "收到設備通知", "device_lockdown_violation": "設備鎖定違規", "device_power_down": "電源 {{param}} 斷電", "device_power_down_off_to_on": "PWR {{param}} 關 > 開。", "device_power_on": "電源 {{param}} 通電", "device_power_on_to_off": "PWR {{param}} 開 > 關。", "device_snmp_reachable": "設備恢復以SNMP存取", "device_snmp_unreachable": "設備無法以SNMP存取", "di_off": "DI {{param}}} 關閉", "di_on": "DI {{param}}} 開啟", "disk_space_not_enough": "硬碟剩餘空間低於閾值", "event_config_import": "已匯入新的配置檔案", "event_ddos_attack": "偵測到 DoS 攻擊", "event_ddos_attack_recovery": "安全路由器DoS攻擊恢復正常", "event_dying_gasp": "系統電源已關閉，設備由其電容器供電", "event_eps_is_off": "PoE外部電源已關閉", "event_eps_is_on": "PoE外部電源已打開", "event_firewall_attack": "違反防火牆規則", "event_firewall_attack_recovery": "違反防火牆規則恢復正常", "event_ieee_lldp_table_change": "{{trapDetail}}", "event_ieee_rstp_root_change": "RSTP 拓墣中新的 root 已被選擇", "event_ieee_rstp_topology_change": "拓撲已被改為RSTP", "event_linux_account_setting_change": "帳號設定 {{username}} 已被更改", "event_linux_config_change": "{{modules}} 組態已被 {{username}} 修改。", "event_linux_config_import": "{{username}} 設定匯入{{successOrFail}}", "event_linux_config_import_failed": "失敗", "event_linux_config_import_succeed": "成功", "event_linux_coupling_change": "Turbo Ring v2 coupling 路徑狀態已被改變", "event_linux_di_off": "DI {{index}} 已被關閉", "event_linux_di_on": "DI {{index}} 已被開啟", "event_linux_dotlx_auth_fail": "802.1X 認證失敗在埠{{portIndex}} 因 {{reason}}", "event_linux_dual_homing_change": "Dual Homing 路徑已被轉換", "event_linux_log_capacity_threshold": "事件紀錄数量 {{value}} 已超過閾值", "event_linux_low_input_voltage": "輸入電壓低於閾值", "event_linux_master_change": "Ring {{index}} master 已被改變", "event_linux_master_mismatch": "Ring {{index}} master 設定並不匹配", "event_linux_over_power_budget_limit": "所有PDs的消耗電源 {{value}} 已超過最大輸入電源值 {{threshold}}", "event_linux_password_change": "使用者 {{username}} 密碼已被更改", "event_linux_pd_no_response": "PoE 埠 {{portIndex}} 對PD failure check 無反應", "event_linux_pd_over_current": "PoE 埠 {{portIndex}} 電源已超過安全標準", "event_linux_pd_power_off": "PoE 埠 {{portIndex}} PD 電源關閉", "event_linux_pd_power_on": "PoE 埠 {{portIndex}} PD 電源開啟", "event_linux_port_recovery_by_ratelimit": "埠 {{portIndex}} 因限速已恢復功能", "event_linux_port_shutdown_by_ratelimit": "流量超出速率限制，已封鎖連接埠 {{portIndex}}。", "event_linux_port_shutdown_by_security": "埠 {{portIndex}} 因 port security 被關閉", "event_linux_power_detection_fail": "埠 {{portIndex}} 顯示設備狀態為{{devicetype}}，建議 : {{suggestion}}", "event_linux_power_detection_fail_devietype_na": "不明", "event_linux_power_detection_fail_devietype_noPresent": "不顯示", "event_linux_power_detection_fail_devietype_unknown": "未知", "event_linux_power_detection_fail_suggestion_disable_POE": "關閉 POE", "event_linux_power_detection_fail_suggestion_enable_legacy": "開啟 legacy", "event_linux_power_detection_fail_suggestion_enable_POE": "打開 POE", "event_linux_power_detection_fail_suggestion_no": "無", "event_linux_power_detection_fail_suggestion_rais_EPS_voltage": "升起 Eps 電壓", "event_linux_power_detection_fail_suggestion_select_auto": "選擇自動", "event_linux_power_detection_fail_suggestion_select_force": "選擇 force", "event_linux_power_detection_fail_suggestion_select_high_power": "選擇 high power", "event_linux_power_off": "電源 {{index}} 已被關閉", "event_linux_power_on": "電源 {{index}} 已被開啟", "event_linux_redundant_port_health_check": "備用埠 {{portIndex}} health check 失敗", "event_linux_RMON_trap_is_falling": "RMON trap 已結束", "event_linux_RMON_trap_is_raising": "RMON trap 已觸發", "event_linux_rstp_invalid_bpdu": "RSTP 埠 {{portIndex}} 收到不正確的BPDU (型態: {{type}}, 值:{{value}})", "event_linux_rstp_migration": "埠 {{portIndex}} 已從 {{originTopology} 被改成 {{changeTopology}}", "event_linux_rstp_new_port_role": "RSTP 埠 {{portIndex}} 角色從 {{originalRole}} 被改為 {{newRole}}", "event_linux_ssl_cer_change": "SSL 認證已被更新", "event_linux_topology_change": "拓撲結構已變更。", "event_linux_topology_change_by_type": "拓撲已被更改為 {{topologyType}}", "event_linux_user_login_lockout": "使用者 {{username}} 因 {{param}} 次登入失敗帳號被鎖", "event_linux_user_login_success": "使用者 {{username}} 從 {{interface}} 成功登入", "event_log_cleared_trap_event_info": "Event logs 已被清除 (使用者: {{ user }}, IP: {{ ip }}, 介面: {{ interface }})", "event_message_serial_device_port_any_recovery": "序列埠 {{portnum}} 已恢復正常運作。", "event_message_serial_device_port_break": "序列埠 {{portnum}} 收到錯誤：中斷錯誤計數。", "event_message_serial_device_port_frame": "序列埠 {{portnum}} 收到錯誤：訊框錯誤計數。", "event_message_serial_device_port_overrun": "序列埠 {{portnum}} 收到錯誤：溢位錯誤計數。", "event_message_serial_device_port_parity": "序列埠 {{portnum}} 收到錯誤：奇偶校驗錯誤計數。", "event_message_serial_device_port_rx": "序列埠 {{portnum}} 的 RX 在最後 {{min}} 分鐘內沒有收到任何資料。", "event_message_serial_device_port_rx_recovery": "序列埠 {{portnum}} 的 RX 已恢復接收資料。", "event_message_serial_device_port_rxtx": "序列埠 {{portnum}} 的 RX 和 TX 在最後 {{min}} 分鐘內沒有收到任何資料。", "event_message_serial_device_port_rxtx_recovery": "序列埠 {{portnum}} 的 RX 和 TX 已恢復接收資料。", "event_message_serial_device_port_tx": "序列埠 {{portnum}} 的 TX 在最後 {{min}} 分鐘內沒有收到任何資料。", "event_message_serial_device_port_tx_recovery": "序列埠 {{portnum}} 的 TX 已恢復接收資料。", "event_not_found_type": "Unknown event type: {{ eventType }}", "event_pd_check_fail": "PD 失效檢查 (無回應)", "event_pd_over_current": "PoE 埠 {{portIndex}} 電流超標/短路", "event_pd_power_off": "PoE 埠 {{portIndex}} 電源關閉", "event_pd_power_on": "PoE 埠 {{portIndex}} 電源開啟", "event_prp_function_fail": "PRP功能失敗", "event_serial_device_port_break": "序列埠收到錯誤：中斷錯誤計數", "event_serial_device_port_frame": "序列埠收到錯誤：訊框錯誤計數", "event_serial_device_port_overrun": "序列埠收到錯誤：溢位錯誤計數", "event_serial_device_port_parity": "序列埠收到錯誤：奇偶校驗錯誤計數", "event_serial_device_port_rx": "序列埠 RX 未收到任何資料", "event_serial_device_port_rxtx": "序列埠 RX 和 TX 未收到任何資料", "event_serial_device_port_tx": "序列埠 TX 未收到任何資料", "event_sfp_rx_below": "SFP 連接埠 {{portIndex}} 的RX ({{currentdB}} dBm) 低於門檻值 ({{thresholddB}} dBm)。", "event_sfp_rx_below_recovery": "SFP 埠 {{portIndex}} Rx {{recoverydB}}dBm 已恢復正常", "event_sfp_temp_over": "SFP 連接埠 {{portIndex}} 的溫度 ({{currentTemp}} ºc) 已超出門檻值 ({{currentTemp}} ºc)。", "event_sfp_temp_over_recovery": "SFP 埠 {{portIndex}} 溫度 {{recoveryTemp}}ºc 已恢復正常", "event_sfp_tx_below": "SFP 連接埠 {{portIndex}} 的發射功率 ({{currentdB}} dBm) 低於門檻值 ({{thresholddB}} dBm)。", "event_sfp_tx_below_recovery": "SFP Tx {{recoverydB}}dBm 已恢復正常", "event_sfp_voltage_below": "SFP 連接埠 {{portIndex}} 的電壓 ({{currentVoltage}} V) 低於門檻值 ({{thresholdVoltage}} V)。", "event_sfp_voltage_below_recovery": "SFP 埠 {{portIndex}} 電壓 {{recoveryVoltage}}v 已恢復正常", "event_sfp_voltage_over": "SFP 連接埠 {{portIndex}} 的電壓 ({{currentVoltage}} V) 已超出門檻值 ({{thresholdVoltage}} V)。", "event_sfp_voltage_over_recovery": "SFP 埠 {{portIndex}} 電壓 {{recoveryVoltage}}v 已恢復正常", "event_too_many_login_failure": "登入失敗太多次，暫時無法以Web存取。", "event_too_many_login_failure_recovery": "登入失敗太多次事件結束。已恢復以Web存取。", "event_tracking_port_enabled_status": "與埠啟用相關的 Tracking entries 已更改", "event_tracking_static_route_status_changed": "與靜態路由相關的 Tracking entries 已更改", "event_tracking_status_changed": "Tracking entries 的狀態已更改", "event_tracking_vrrp_status_changed": "與 VRRP 相關的 Tracking entries 已更改", "event_trusted_access_attack": "違反設備信任存取規則", "event_trusted_access_attack_recovery": "違反信任存取規則恢復正常", "event_user_info_change": "帳號資訊異動: {{trapoid}}", "event_v3_trap_parse_error": "V3 trap 封包解析錯誤", "event_v3_trap_parse_error_recovery": "V3 trap 封包解析錯誤事件已清除", "exceed_poe_threshold": "超過PoE系統閾值", "fan_module_malfunction": "風扇模組故障。", "fiber_warning": "{{portIndex}} 光纖警告 ( {{warningType}} )", "firewall_policy_violation": "防火牆與DoS攻擊規則: {{trapoid}}", "firewall_under_attack": "違反防火牆規則", "firewall_under_attack_recovery": "違反防火牆規則恢復正常", "firmware_upgraded": "韌體升級", "firmware_version_release": "有韌體更新可用。 查看韌體管理頁面瞭解詳細資訊。", "goose_healthy": "GOOSE狀態：正常\nGOOSE 資訊 {{ display }}", "goose_healthy_with_value": "GOOSE status: Healthy \nGOOSE message {{ display }}", "goose_tampered": "GOOSE狀態：被篡改", "goose_tampered_with_value": "GOOSE狀態: 被篡改\nGOOSE訊息 {{ display }}", "goose_timeout": "GOOSE狀態：超時", "goose_timeout_with_value": "GOOSE狀態: 超時\nGOOSE訊息 {{ display }}", "high_cpu_loading": "CPU 負載已連續 10 分鐘超過 85%。", "icmp_packet_loss_over_critical_threhold": "設備封包遺失率到達{{param1}}(超過嚴重閾值{{param2}})", "icmp_packet_loss_over_critical_threhold_recovery": "設備封包遺失率恢復到{{param1}}(超過嚴重閾值{{param2}})", "icmp_packet_loss_over_threhold": "設備封包遺失率到達{{param1}}, 超過閾值{{param2}} (基於ICMP)", "icmp_packet_loss_over_threhold_recovery": "設備封包遺失率恢復到{{param1}}, 超過閾值{{param2}} (基於ICMP)", "icmp_reachable": "設備恢復存取", "icmp_unreachable": "設備無法存取", "iei_fiber_warning": "觸發了光纖警告", "input_bandwidth_over_threshold": "輸入頻寬利用率已超過門檻值。", "input_bandwidth_over_threshold_disabled": "埠{{portIndex}}的輸入頻寬利用率沒有設置閾值。", "input_bandwidth_over_threshold_recovery": "埠{{portIndex}}輸入頻寬利用率恢復，數值為{{currentValue}}。", "input_bandwidth_over_threshold_with_port": "連接埠 {{portIndex}} 的輸入頻寬利用率 ({{currentValue}}) 已超過門檻值 ({{threshold}})。", "input_bandwidth_under_threshold": "輸入頻寬利用率低於門檻值。", "input_bandwidth_under_threshold_disabled": "埠{{portIndex}的輸入頻寬利用率沒有設置閾值。", "input_bandwidth_under_threshold_recovery": "埠{{portIndex}}輸入頻寬利用率恢復，數值為{{currentValue}}。", "input_bandwidth_under_threshold_with_port": "連接埠 {{portIndex}} 的輸入頻寬利用率 ({{currentValue}}) 低於門檻值 ({{threshold}})。", "input_packet_error_over_threshold": "輸入封包錯誤率已超過門檻值。", "input_packet_error_over_threshold_disabled": "埠{{portIndex}}的輸入封包錯誤率沒有閾值設置。", "input_packet_error_over_threshold_recovery": "埠{{portIndex}}輸入封包錯誤率恢復，數值為{{currentValue}}。", "input_packet_error_over_threshold_with_port": "連接埠 {{portIndex}} 的輸入封包錯誤率 ({{currentValue}}) 已超過門檻值 ({{threshold}})。", "insufficient_disk_space": "可用磁碟空間不足 5 GB。", "interface_set_as_ospf_designated_router": "介面設定為 OSPF 指定路由器。", "ip_conflict_detected": "偵測到 {{ip}} 的 IP 衝突，MAC 衝突：", "ip_conflict_detected_failed": "無法執行 IP 衝突偵測，因為未找到 Npcap/WinPcap/Libpcap", "ip_conflict_recovery": "IP 衝突已解決。", "iw_client_joined": "用戶端 加入: {{param}}", "iw_client_left": "用戶端 離開: {{param}}", "l3_firewall_policy_violation": "違反防火牆規則（NAT 系列）", "license_limitation_reached": "已達到授權上限。", "license_not_enough": "節點超過授權數量。請刪除一些節點，或者新增另一個授權", "license_over": "節點超過授權數量。請刪除一些節點，或者更新您的MXview One授權", "lldp_change": "LLDP 資料變更", "logging_capacity": "事件超出容量限制", "login_radius_fail": "通過RADIUS伺服器登入認證失敗", "login_radius_success": "通過RADIUS伺服器登入認證成功", "login_tacas_fail": "通過TACACS+伺服器登入認證失敗", "login_tacas_success": "通過TACACS+伺服器登入認證成功", "mac_sticky_violation": "違反 MAC sticky", "mrp_multiple_event": "發生多 MRP 管理者事件。", "mrp_ring_open_event": "發生 MRP 環打開事件。", "mstp_topology_changed": "MSTP 拓樸發生變化", "mxview_autopology_finish": "自動拓撲完成", "mxview_autopology_start": "自動拓撲開始", "mxview_db_backup_fail": "資料庫備份失敗", "mxview_db_backup_sucess": "資料庫備份完成, 儲存目錄 \\%%MXviewPRO_Data\\%%\\db_backup\\{{param1}} {{param2}}", "mxview_job_done": "作業: {{jobname}} 完成了", "mxview_job_start": "作業：{{jobname}}開始", "mxview_server_license_limit": "MXview One Central Manager 沒有足夠的授權", "mxview_server_start": "MXview One伺服器啟動", "mxview_sms_fail": "無法發送簡訊通知", "mxview_sms_success": "MXview One成功發送簡訊通知", "mxview_user_lockout": "{{param}} 帳號暫時停用", "mxview_user_login_fail": "無法登入 MXview One。", "mxview_user_login_sucess": "使用者登入成功: {{param}}", "mxview_user_logout": "使用者登出: {{param}}", "network_latency": "MXview One Central Manager 的網絡延遲超過 100 毫秒", "new_port_role_selected": "選擇新連接埠角色。", "new_root_bridge_selected_in_topology": "在拓樸中選擇了新的根橋。", "notification_sfp_rx_below": "SFP Rx 低於閾值", "notification_sfp_temp_over": "SFP 溫度超過閾值", "notification_sfp_tx_below": "SFP Tx 低於閾值", "notification_sfp_voltage_below": "SFP 電壓低於閾值", "notification_sfp_voltage_over": "SFP 電壓超過閾值", "nport_syslog_over_threshold": "NPort 的系統日誌超過閾值", "opcua_server_start": "MXview One OPC UA 伺服器已啟動。", "opcua_server_stop": "MXview One OPC UA 伺服器已停止。", "ospf_designated_router_changed": "OSPF 指定路由器發生變化。", "ospf_designated_router_interface_and_adjacency_changed": "OSPF 指定路由器介面和鄰接關係發生變化。", "out_of_memory": "可用記憶體不足 20%。", "output_bandwidth_over_threshold": "輸出頻寬利用率已超過門檻值。", "output_bandwidth_over_threshold_disabled": "埠{{portIndex}}的輸出頻寬利用率沒有設置閾值。", "output_bandwidth_over_threshold_recovery": "埠{{portIndex}}輸出頻寬利用率恢復，數值為{{currentValue}}。", "output_bandwidth_over_threshold_with_port": "連接埠 {{portIndex}} 的輸出頻寬利用率 ({{currentValue}}) 已超過門檻值 ({{threshold}})。", "output_bandwidth_under_threshold": "輸出頻寬利用率低於門檻值。", "output_bandwidth_under_threshold_disabled": "埠{{portIndex}}的輸出頻寬利用率沒有設置閾值。", "output_bandwidth_under_threshold_recovery": "埠{{portIndex}}輸出頻寬利用率恢復，數值為{{currentValue}}。", "output_bandwidth_under_threshold_with_port": "連接埠 {{portIndex}} 的輸出頻寬利用率 ({{currentValue}}) 低於門檻值 ({{threshold}})。", "output_packet_error_over_threshold": "輸出封包錯誤率已超過門檻值。", "output_packet_error_over_threshold_disabled": "埠{{portIndex}}的輸出封包錯誤沒有設置閾值。", "output_packet_error_over_threshold_recovery": "埠{{portIndex}}輸出封包錯誤恢復，數值為{{currentValue}}。", "output_packet_error_over_threshold_with_port": "連接埠 {{portIndex}} 的輸出封包錯誤率 ({{currentValue}}) 已超過門檻值 ({{threshold}})。", "overheat_protection_now_active_for_power_module": "電源模組 {{ x }} 的過熱保護現已啟動。", "password_automatically_changed_failed": "自動更改設備密碼失敗。", "password_automatically_changed_success": "設備密碼自動更改成功。", "password_automation_scheduled": "密碼自動化已達到排程時間並將開始執行。", "phr_port_timediff": "PHR AB 連接埠時差。", "phr_port_wrong_lan": "PHR AB 連接埠 LAN 錯誤。", "poe_off_info": "設備未通過 PoE 供電", "poe_on_info": "設備由 PoE 供電", "port_linkdown_event": "埠 {{portindex}} 斷線", "port_linkdown_recovery": "埠 {{portindex}} 恢復連線", "port_linkup_event": "埠 {{portindex}} 連線", "port_linkup_recovery": "埠 {{portindex}} 恢復連線", "port_loop_detect": "已偵測到連接埠 {{portnum}} 循環", "port_loop_detect_resolved": "連接埠 {{portnum}} 上的連接埠循環問題已解決。", "port_loop_detected": "連接埠迴圈", "port_pd_short_circuited": "連接埠 {{portnum}} 非 PD 或 PD 短路。", "port_traffic_overload": "埠{{portIndex}}頻寬利用率到達{{percent}}%", "power_danger_recovery": "電源{{param}} 以交流電(AC)供電中", "power_has_been_cut_due_to_overheating": "由於過熱，電源已被切斷。", "power_module_fan_malfunction": "電源模組風扇故障。", "power_type_danger": "電源{{param}} 以直流電(DC)供電中", "pse_fet_bad": "PoE 埠 {{portIndex}} 外部 FET 失效", "pse_over_temp": "PSE 晶片溫度超標", "pse_veeuvlo": "PSE 晶片 VEE 欠壓鎖定", "ptp_grandmaster_changed": "PTP Grandmaster 變更", "ptp_sync_status_changed": "PTP 同步狀態已更改", "rateLimit_off": "埠 {{portindex}} 流量限制關閉", "rateLimit_on": "連接埠 {{portindex}} 上的速率限制已啟用。", "recorved_device_lockdown_violation": "設備鎖定違規恢復正常", "recorved_l3_firewall_policy_violation": "違反防火牆規則恢復正常（NAT 系列）", "redundancy_topology_change": "冗餘拓撲已更改。", "syslog_server_start": "MXview One syslog 伺服器已啟動。", "syslog_server_stop": "MXview One syslog 伺服器已停止。", "system_temperature_exceeds_threshold": "系統溫度超過門檻值。", "temporary_account_activate_success": "{{ip}} 的臨時帳號已成功啟動。", "temporary_account_deactivate_success": "{{ip}} 的臨時帳號已成功停用。", "thermal_sensor_component_overheat_detected": "偵測到熱感測器組件過熱。", "trunk_port_link_down": "聚合埠 {{portindex}} 斷線 (物理埠 {{param}})", "trunk_port_link_down_recovery": "聚合埠 {{portindex}} 恢復連線 (物理埠 {{param}})", "trunk_port_link_up": "聚合埠 {{portindex}} 連線 (物理埠 {{param}})", "trust_access_under_attack": "違反設備信任存取規則", "trust_access_under_attack_recovery": "違反信任存取規則恢復正常", "turbo_ring_master_match": "Turbo Ring master 無誤", "turbo_ring_master_mismatch": "Turbo Ring master 發生錯誤", "turbo_ring_master_unknow": "Turbo Ring master 事件, 狀態未知", "turbochain_topology_change": "Turbo Chain 拓撲改變", "turboring_coupling_port_change": "Turbo Ring Coupling 埠改變", "turboring_master_change": "Turbo Ring Master 改變", "unknown_device_detected": "偵測到未知設備", "user_login_fail": "帳號登入失敗", "user_login_success": "帳號登入成功: {{username}} ", "usercode_revoke": "使用者重新產生 usercode。", "vpn_link_recovery": "VPN tunnel {{param}} 恢復連線", "vpn_linkdown": "VPN tunnel {{param}} 斷線", "vpn_linkup": "VPN tunnel {{param}} 連線", "vrrp_master_changed": "VRRP Master變更", "warn_start": "熱啟動"}, "event_detail_title": "事件細節訊息 ID: {{eventId}}", "filter": "篩選條件", "filter_end_time": "結束日期", "filter_event": "鍵入以篩選事件", "filter_event_type": "快速篩選事件", "filter_from_time": "開始日期", "filter_hour": "小時", "filter_min": "分鐘", "filter_sec": "秒", "filter_type": {"last_fifty_events": "最新五十筆事件", "last_twenty_events": "最新二十筆事件", "unack_events": "未確認事件", "unack_last_fifty_events": "最新五十筆未確認事件", "unack_last_twenty_events": "最新二十筆未確認事件"}, "group": "群組", "recent_event": "最近事件", "severity": {"any": "任何", "critical": "嚴重", "information": "資訊", "network_device": "網路與設備", "system_information": "系統資訊", "warning": "警告"}, "show_event_detail": "顯示詳細資訊", "source": {"any": "任何", "mxview": "MXview One", "security_sensing": "安全檢測", "trap": "Trap"}, "table_title": {"ack": "確認", "ack_time": "確認時間", "always_show": "啟動時總是顯示最近的事件", "description": "描述", "detail_information": "細節資訊", "event_id": "ID", "event_properties": "事件屬性", "event_source": "來源", "event_time": "事件時間", "hide_recent_event": "隱藏最近的事件", "severity": "嚴重性", "show_recent_event": "顯示最近的事件", "site_name": "站台名稱", "source_ip": "來源 IP"}, "tabs": {"network_device_title": "網路與設備", "security_event": "網路安全事件", "security_title": "網路安全", "system_title": "系統"}}, "execute_cli_object": {"add_cli_object": "新增 CLI 腳本", "alias": "別名", "cli_error": {"connection_failure": "連線失敗", "handshake_failure": "交握失敗", "login_failure": "登入失敗", "port_limit": "連接埠安全位址記錄的最大數量：{{param}}", "reach_maximum_ssid": "SSID 最大數量：{{param}}", "smmp_configuration_mismatch": "SNMP 設定不匹配", "ssh_not_supported": "無法連接到 SSH 用戶端", "unable_to_set_port": "無法設定連接埠 {{port}}。", "unknown_error": "未知錯誤"}, "cli_Script": "CLI腳本", "cli_session_timeout": "CLI 會話逾時", "confirm_selected_devices": "選定的設備", "description": "描述", "execute_cli_fail": "無法執行 CLI 腳本", "execute_cli_object": "執行 CLI 腳本", "execute_cli_result_hint": "如果離開此畫面，您可以從“儲存的 CLI 腳本”>“執行結果”下載執行結果。", "execute_cli_results": "CLI 執行結果", "execute_cli_script": "執行 CLI 腳本", "failed": "失敗", "finished": "已完成", "in_progress": "進行中 ...", "ip": "IP", "model": "型號", "name": "CLI 腳本名稱", "no_cli_object_hint": "未找到 CLI 腳本。 首先從儲存的 CLI 腳本中新增 CLI 腳本", "not_sent": "未發送", "result": "結果", "save_as_cli_object": "另存為 CLI 腳本", "save_cli_object": "儲存 CLI 腳本", "select_cli_object": "選擇 CLI 腳本", "ssh_connection_timeout": "SSH 連線逾時", "status": "狀態"}, "firmware-management": {"action": "動作", "add-task": "新增任務", "add-to-schedule": "排程更新", "api-message": {"add-schedule-fail": "無法排程任務", "add-schedule-success": "任務已排程", "delete-schedule-fail": "無法刪除檢查間隔排程", "delete-schedule-success": "檢查間隔排程刪除成功", "fm-downloaded": "韌體檔下載成功", "fm-downloading": "下載韌體文件", "fm-ready": "韌體文件準備就緒", "get-data-failed": "無法取得資料", "get-download-fm-failed": "無法下載韌體文件", "get-release-note-failed": "無法取得發行說明", "get-srs-status-failed": "無法查詢 Moxa 韌體伺服器狀態", "ignored-model-fail": "無法將型號增加到“忽略的型號”清單中"}, "check-Firmware-status": "檢查韌體狀態", "check-interval": "檢查間隔", "check-now": "立即檢查", "connected": "已連線", "description": "描述", "disconnected": "未連線", "download-csv-report": "下載 CSV 報告", "download-pdf-report": "下載 PDF 報告", "execution-time": "時間", "firmware-upgrade-sequential": "韌體更新（嚴格依序）", "firmware-upgrade-smart-concurrent": "韌體更新（智慧依序）", "ignore-report": "忽略報告", "ignore-report-desc1": "您確定要跳過下載報告嗎？", "ignore-report-desc2": "如果您離開此頁面，該報告將不再可供下載。", "ignored-models": "忽略的型號", "last-update": "最後檢查", "models": "型號", "moxa-firmware-server-status": "Moxa 韌體伺服器狀態", "no-information": "無資料", "none": "無", "offline-desc": "無資料。 之前沒有連接到韌體更新伺服器。 確保設備已連接到網際網路，然後重試。", "proceeding-firmware-upgrade": "韌體更新狀態", "proceeding-upgrade-result": "韌體更新結果", "release-note": "發行說明", "repeat-execution": "重複", "retry-Failed-devices": "重試失敗的設備", "select-devices": "選擇設備", "select-firmware": "選擇韌體", "set-upgrade-sequence": "升級順序", "sign-here": "簽名", "start-date": "日期", "status": {"failed": "失敗的", "finished": "完成的", "in-progress": "進行中", "waiting": "等待中"}, "table-header": {"alias": "別名", "current-version": "當前版本", "device-status": "設備狀態", "ip": "IP", "latest-version-on-firmware-server": "韌體伺服器上的最新版本", "model-series": "型號系列", "order": "順序", "selected-firmware-ready": "所選韌體下載狀態", "selected-version": "選定版本", "status": "狀態"}, "task-name": "任務名稱", "title": "韌體管理", "turn-off-check-interval": "停用檢查間隔", "turn-on-check-interval": "啟用檢查間隔", "unable-to-download-firmware": "下載韌體失敗", "update-mode": "更新模式", "upgrade-desc1": "無法決定更新順序。 請先將運行 MXview One 的電腦增加到拓撲中。", "upgrade-desc2": "當前設置僅支援設備韌體同時更新。 要使用嚴格依序或智慧依序更新方法，您必須首先將運行 MXview One 的電腦增加到拓撲中。", "upgrade-firmware-report": "韌體更新報告", "upgrade-now": "立即更新", "upgrade-state-desc": "韌體更新可能需要一些時間。 請等待更新過程完成。", "version": "版本"}, "general": {"common": {"action": "動作", "allow": "允許", "any": "任何", "deny": "拒絕", "description": "描述", "deviceInUse": "使用中的設備", "deviceName": "設備名稱", "disabled": "停用", "enabled": "啟用", "endDate": "結束日期", "filters": "篩選", "firmwareVersion": "韌體版本", "group": "群組", "index": "索引", "ipAddress": "IP 位址", "location": "位置", "mac": "MAC 位址", "name": "名稱", "online": "上線", "options": "選項", "productModel": "產品型號", "profileInUse": "使用中的配置檔", "refCount": "引用", "serialNumber": "序號", "startDate": "開始日期", "status": "狀態", "title": "面板名稱"}, "dialog": {"deleteMsg": "您確定要刪除所選的{{ item }}嗎？", "deleteTitle": "刪除 {{ item }}", "isSelected": "已選擇 {{ number }} 項", "title_system_message": "系統訊息", "unsaved_hint_content": "您確定要離開此頁面嗎？\n您所做的任何更改都不會被儲存。", "unsaved_hint_title": "離開且不儲存", "warning": "警告"}, "fileDrop": {"browse": "瀏覽", "dropText": "將文件拖放到此處，或者"}, "item_selected": "已選擇 {{ number }} 項", "log": {"localStorage": "本地儲存", "logDestination": "日誌存取於", "snmpTrapServer": "SNMP Trap 伺服器", "syslogServer": "Syslog 伺服器", "title": "事件日誌"}, "menu": {"jump_page_placeholder": "Alt+J 快速切換頁面"}, "page_state": {"application_error": "應用程式錯誤", "application_error_desc": "Code有問題，救我...", "back_link": "回到首頁", "page_not_found": "找不到頁面 :(", "page_not_found_desc": "目前在伺服器上無法處理這個網址。"}, "severity": {"alert": "警告", "critical": "嚴重", "debug": "除錯", "emergency": "緊急", "error": "錯誤", "high": "高", "information": "資訊", "informational": "資訊", "low": "低", "medium": "中", "notice": "通知", "title": "嚴重性", "warning": "警告"}, "shortWeekday": {"fri": "星期五", "mon": "星期一", "sat": "星期六", "sun": "星期日", "thu": "星期四", "tue": "星期二", "wed": "星期三"}, "table": {"add": "新增", "delete": "刪除", "download": "下載", "downloadAllLogs": "下載所有日誌", "edit": "編輯", "filter": "篩選", "info": "資訊", "more": "更多的", "permissionDenied": "沒有權限", "reboot": "重啟", "refresh": "重新整理", "reorderFinish": "完成重新排序", "reorderPriority": "重新排序優先級", "search": "搜尋", "transfer": "移轉", "upgrade": "升級"}, "top_nav": {"api_doc": {"title": "API 文件"}, "hide_recent_event": "隱藏導航選單", "notifications": {"message_content": "事件敘述在這裡", "message_readall": "瀏覽所有通知訊息", "message_title": "事件標題在這裡", "notification_header": "通知訊息"}, "show_recent_event": "顯示導航選單", "user_profile": {"advanced_mode": "進階模式", "change_pwd": "變更密碼", "greeting": "嗨", "logout": "登出", "manage_account": "管理帳號", "reset_factory_default": "重置出廠設置", "restart_machine": "重新啟動機器", "search": "鍵入要搜索的關鍵字"}}, "topNav": {"caseInsensitive": "不區分大小寫", "changePwd": "更改密碼", "changeSuccess": "您的密碼已成功更新。請重新登入。", "confirmNewPwd": "確認新密碼", "currentPwd": "現在密碼", "invalidKey": "保留以下名稱：admin、operator、viewer、root、administrator、auditor", "logout": "登出", "logoutMsg": "您確定要退出嗎？", "newPwd": "新密碼", "subject": "內容", "troubleshoot": "故障排除", "troubleshootMsg": "您可以將調試日誌匯出到本機主機以進行故障排除。", "updateAuthority": "更新權限", "updateSuccess": "您的帳號權限已變更。請重新登入。", "username": "使用者名稱"}, "unit": {"days": "天", "entries": "條目", "minute": "分鐘", "minutes": "分鐘", "months": "月", "percent": "%", "pkts": "包/秒", "sec": "秒", "seconds": "秒", "thousand": "千"}, "weekday": {"friday": "星期五", "monday": "星期一", "saturday": "星期六", "sunday": "星期日", "thursday": "星期四", "tuesday": "星期二", "wednesday": "星期三"}}, "GLOBAL_MESSAGE": {"update_fail": "更新失敗", "update_success": "更新成功"}, "GROUP_PROPERTIES": {"description": "描述", "devices": "設備 (正常 / 警告 / 嚴重)", "information": "資訊", "name": "名稱", "title": "群組屬性"}, "IMAGE": {"deviceSizeError": "最大圖片尺寸大小為100KB", "error": "Mxview 只支援 jpg, gif, png 檔案格式", "sizeError": "圖片尺寸大小上限為1MB"}, "inventory_management": {"active": "有效", "alias": "别名", "assets_list": "資產列表", "available": "可用的", "channel_extended_end_date": "通路延長保固截止日期", "channel_extended_warranty_end_date_hint": "如果您與 Moxa 通路供應商簽訂了延長保固協議，請在此手動輸入延長的到期日期。", "check_warranty_manually": "手動檢查保固", "check_warranty_status": "檢查保固狀態", "days": "天前", "email_example": "<EMAIL>", "email_to": "發電子郵件給", "expire_soon": "即將到期", "expired": "已到期", "firmware_version": "韌體版本", "invalid_email_desc": "無效的電子郵件地址", "ip": "IP", "last_update": "上次更新", "mac_address": "MAC 位址", "model": "型號", "multiple_email_hint": "您可以新增多個收件者電子郵件地址，以逗號分隔。", "no_data": "N/A", "notify_before": "發送提醒", "retrieve_data": "檢索資料", "select": "選擇", "serial_number": "序號", "type": "搜尋依據", "unable_query_warranty_server_status": "無法連接 Moxa 保固伺服器。", "unable_retrieve_warranty_information": "無法檢索保固資訊。", "unavailable": "不可用的", "warranty_end_date": "保固截止日期", "warranty_end_date_notification": "保固到期通知", "warranty_management": "保固管理", "warranty_notification": "保固通知", "warranty_period": "保固期限", "warranty_server_status": "Moxa 保固伺服器狀態", "warranty_start_date": "保固開始日期", "warranty_status": "保固狀態"}, "INVENTORY_REPORT": {"alias": "別名", "filter": "鍵入以篩選庫存報告", "fw_version": "韌體版本", "ip_address": "IP 地址", "mac": "MAC 地址", "model": "型號", "property": "屬性", "report_generate_day": "產生報表日期: ", "site_name": "站台名稱", "system_desc": "系統描述", "title": "庫存報告", "value": "值"}, "IP_CONFIGURATION": {"auto_ip": "自動 IP", "change_ip_fail": "無法設置IP配置", "change_ip_success": "成功設置IP配置", "gateway": "閘道", "hint": "此功能不適用於第3層設備。", "ip_address": "IP地址", "netmask": "網路遮罩", "title": "IP 組態"}, "ip_conflict_detected_notification": "偵測到 IP 衝突", "ip_conflict_recovery_notification": "IP 衝突已解決", "ips_configuration": {"dialog-title": "IPS配置", "execute_fail": "Failed to execute", "input-ips": "IPS", "option-detection-mode": "檢測模式", "option-prevention-mode": "預防模式", "selet-ips-operation-mode": "IPS操作模式", "th-execution-status": "狀態"}, "IPSEC": {"connection_name": "連接名稱", "ipsec_status_phase1": "IPSec第1階段狀態", "ipsec_status_phase2": "IPSec第2階段狀態", "local_gateway": "本地閘道", "local_subnet": "本地子網路", "remote_gateway": "遠端閘道", "remote_subnet": "遠端子網路"}, "IW": {"Message": {"CONNECT_TIMEOUT": "連線超時", "ERROR_OCCURRED": "伺服器連線錯誤，請稍等", "FAILED": "失敗", "LOAD_DATA_FAILED": "讀取資料失敗", "RSSI_SNR_ONLY": "(訊號強度與雜訊比)", "SET_SUCCESS": "設定成功", "SUCCESSED": "成功", "UPDATE_FAILED": "更新資料錯誤"}, "Title": {"ap": "AP", "AUTO_REFREASH": "自動更新: ", "AUTO_REFRESH": "自動更新", "BSSID": "BSSID", "channel": "Channel", "client": "用戶端", "client_count": "用戶端數量", "client_router": "Client-Router", "CLOSE": "關閉", "COLOR": "顏色", "COLUMN": "欄位", "CONDITIONS": "條件", "CONN_TIME": "連線時間 (秒)", "connected": "連接時間", "DEVICE_NAME": "設備名稱", "disable": "Disable", "ENABLE": "啟用", "FILTER_TABLE_VIEW": "篩選表格", "hint": "此頁面將在以後的產品版本中刪除。請改用MXview One中的Wireless Add-on。", "IP_ADDR": "IP 位址", "link_speed": "連線速度", "MAC": "MAC 位址", "master": "Master", "MODULATION": "調變", "noise_floor": "Noise Floor", "noise_floor_unit": "Noise Floor (dBm)", "OK": "確認", "ONLINE": "上線", "operation_mode": "運作模式", "RSSI": "訊號強度 (dBm)", "security_mode": "安全模式", "signal_level": "Signal Level", "slave": "Slave", "SNR": "雜訊比 (dB)", "SNR_A": "雜訊比-A (dB)", "SNR_B": "雜訊比-B (dB)", "ssid": "SSID", "TOTAL_AP": "基地台數量: ", "TOTAL_CLIENT": "用戶端數量: ", "tx_power": "TX 功率", "tx_power_unit": "TX 功率 (dBm)", "tx_rate": "TX 速率", "tx_rate_unit": "TX 速率 (Mb/s)", "uptime": "運行時間", "VALUE": "數值", "WIRELESS_TABLE_VIEW": "無線狀態列表"}}, "JOB_SCHEDULER": {"add_failed": "新增作業失敗", "add_success": "新增作業成功", "add_title": "新增作業", "alias": "別名", "auto_topology": "自動拓撲", "cli_object_name": "CLI 腳本名稱", "config_file": "組態檔案", "config_file_error": "Mxview只支援ini檔案格式", "config_file_size_error": "最大檔案大小為1MB", "current_filename": "當前檔案名稱", "current_version": "當前版本", "daily": "每日", "database_backup": "資料庫維護", "delete_failed": "刪除作業失敗", "delete_success": "刪除作業成功", "description": "描述", "edit_title": "編輯作業", "excute_cli_object": "執行儲存的腳本", "execution_time": "執行時間", "export_configuration": "匯出配置", "filter": "鍵入以篩選作業排程", "fm_sequential": "嚴格依序", "fm_smart": "智慧依序", "friday": "星期五", "import_configuration": "匯入配置", "ip": "IP", "job_action": "動作", "job_log": "作業日誌", "job_name": "作業名稱", "model_series": "型號系列", "monday": "星期一", "monthly": "每月", "on": "週期", "once": "一次", "order": "順序", "registered_devices": "註冊的設備", "repeat_execution": "重複執行", "saturday": "星期六", "schedule_time": "作業時間", "selected_version": "選定版本", "show_log_fail": "還沒有日誌可以顯示", "show_log_not_found": "無法顯示日誌", "start_date": "開始日期", "sunday": "星期日", "thursday": "星期四", "title": "維護計畫", "tuesday": "星期二", "update_failed": "更新作業失敗", "update_success": "更新作業成功", "wednesday": "星期三", "weekly": "每週"}, "LANG": {"de-DE": "De<PERSON>ch", "en-US": "English", "es-ES": "Español", "fr-FR": "Français", "ja-JP": "日本語", "ru-RU": "Русский язык", "zh-CN": "中文(简体)", "zh-TW": "中文(繁體)"}, "LICENSE": {"activation_code": "授權碼", "activation_code_error": "無效的授權碼", "activation_title": "啟用", "active": "啟用", "active_desc_1": "造訪", "active_desc_2": "http://license.moxa.com/", "active_desc_3": "並填寫 Product Code 和 User Code 以獲得您的 Active Code", "add_fail": "增加授權失敗", "add_license": "增加授權", "add_new_license_desc": "您可以在這裡增加新的授權", "add_new_license": {"activate": "啟用", "activate_intro_link": "Moxa授權網站", "activate_intro_pre": "從", "activate_intro_suf": "下載授權，並在此處貼上授權碼。", "copy_user_code": "複製User Code", "copy_user_code_intro_link": "Moxa授權網站", "copy_user_code_intro_pre": "複製User Code至", "license_site_step_1_link": "Moxa授權網站", "license_site_step_1_pre": "1. 登入", "license_site_step_2": "2. 在此網站選擇「Activate Your License」和「MXview One」", "license_site_step_3": "3. 註冊碼", "license_site_step_3_Free_step": "前往下一步", "license_site_step_3_Free_title": "免費版使用者：", "license_site_step_3_Full_step": "鍵入註冊碼及User Code於Moxa授權網站，User Code將在後續的步驟中取得", "license_site_step_3_Full_title": "完整版使用者：", "login_license_site": "登入Moxa授權網站", "select_network_adapter": "選擇網卡", "title": "增加新授權"}, "add_success": "增加授權成功", "copied_to_clipboard": "已複製到剪貼簿", "copy_deactivation_code": "複製解除授權碼", "copy_older_license_code": "複製2.x授權碼", "current_nodes": "管理的設備數量:", "deactivate": "停用", "deactivate_fail": "停用授權失敗", "deactivate_success": "停用授權成功", "deactivated_licenses": "已停用的授權", "deactivating": "停用中...", "deactivation_code": "解除授權碼", "deactivation_desc": "停用後授權將會失效，確定要停用授權嗎？", "deactivation_title": "停用授權", "disabled": "未啟用", "duration": "有效天數", "enabled": "已啟用", "expired_license": "過期授權", "free_trial": "免費試用", "free_trial_desc": "開始試用免費的 MXview One", "import_license_file": "輸入授權檔", "license": "授權:", "license_authorized": "已授權", "license_free": "免費授權", "license_none": "無", "license_site": "Moxa授權網站", "license_start": "授權開始時間", "license_title": "授權", "license_trial": "試用", "license_type": {"node_base_intro": "提供MXview One在網路中可以監控的設備數量。", "node_base_title": "基於節點的授權", "power_addon": "電力附加授權", "power_intro": "為使用者提供更多與電力相關的功能。", "security_addon": "安全附加授權", "security_intro": "提供使用者訪問更多的安全相關功能。", "title": "授權類型", "trial_intro": "您可以在90天內體驗到MXview One的強大功能。", "trial_title": "試用授權", "wireless_addon": "無線附加授權", "wireless_intro": "提供使用者訪問更多的無線相關功能。"}, "licensed_node": "授權點數", "licensed_nodes": "授權總數量:", "licenses": "授權", "managed_by_central": "授權由 MXview One Central 管理", "managed_by_central_licenses_invalidated": "無有效授權，請在 Control Panel 中確認狀態", "mxview": "MXview One", "network_adapter": {"button": "選擇網卡", "change_network_adapter": "更改網卡", "change_network_adapter_alert_1": "您確定要更改網卡嗎？", "change_network_adapter_alert_2": "如果更改網卡，將終止目前所有授權。 在使用新網卡註冊新授權之前，您將無法使用MXview One。", "intro": "MXview One將授權綁定到一個網卡，請選擇要綁定的網卡。 重新選擇網卡將終止所有授權，您必須重新註冊所有授權。", "select_adapters": "選擇網卡", "select_adapters_desc": "請選擇一張網卡。我們將使用這張網卡來產生 User Code", "title": "網卡"}, "node": "管理的設備數量 / 授權總數量:", "nodes": "授權數量", "older_license": "2.x 授權", "older_license_nodes": "2.x 授權數量", "over_nodes_desc": "因為監控的節點已超過授權的數量，您已被登出", "over_nodes_title": "警告", "power_addon_trial": "開始體驗MXview One中的電力附加功能。", "reactivate_license": {"activate": "啟用", "activate_intro_link": "Moxa授權網站", "activate_intro_pre": "從", "activate_intro_suf": "下載授權，並在此處貼上授權碼。", "copy_deactivate_code": "複製解除授權碼", "copy_deactivate_code_intro_link": "Moxa授權網站", "copy_deactivate_code_intro_pre": "複製解除授權碼，在", "copy_deactivate_code_intro_suf": "中貼上。", "copy_user_code": "複製User Code", "copy_user_code_intro_link": "Moxa授權網站", "copy_user_code_intro_pre": "複製User Code至", "intro": "使用解除授權碼及User Code來重新啟用您的授權", "license_site_step_1_link": "Moxa授權網站", "license_site_step_1_pre": "1. 登入", "license_site_step_2": "2. 在此網站點選「MXview One Deactivation」和「Transfer to another device」", "license_site_step_3": "3. 在Software Product中選擇MXview One", "login_license_site": "登入Moxa授權網站", "title": "重新啟用授權", "title_abbr": "重新啟用"}, "reason": "狀態", "relaunch": {"activating": "啟用中...", "active_note": "操作將在10秒內完成。"}, "remain": "剩餘", "security_addon_trial": "開始體驗 MXview One 中的安全附加功能。", "select_network_interface": "選擇網卡", "site_license_invalid": "在某些站台中有無效的授權", "site_license_invalid_title": "授權無效", "start_free_trial": "開始試用", "start_free_trial_fail": "開始免費試用失敗", "start_free_trial_success": "開始免費試用成功", "state": "狀態:", "state_all_licenses_invalidated": "無有效授權", "state_cannot_add_free_license": "無法增加免費授權", "state_cannot_add_multiple_free_licenses": "無法增加多個免費授權", "state_format_incorrect": "授權的格式不正確", "state_general_error": "一般錯誤", "state_license_deactivated": "此授權已停用", "state_license_expired": "授權過期", "state_license_is_registered": "此授權已註冊", "state_license_not_found": "找不到授權", "state_license_over_2000": "總授權點數超過2000，但MXview One最多支援2000點", "state_license_upgrade_error": "無法新增 Upgrade License， MXview One需要至少一個完整授權", "state_license_upgrade_no_full_license ": "無法刪除此授權，請先移除所有 Upgrade License", "state_no_full_license": "無 MXview One 授權", "state_no_usercode": "沒有 User Code", "state_over_nodes": "監控的節點已超過授權的數量", "state_trial_expired": "試用過期", "state_trial_is_began": "試用已開始", "state_trial_not_activated": "試用還沒啟動", "state_usercode_deactivated": "User Code 停用", "state_usercode_exists": "User Code 已存在", "state_usercode_not_match": "授權中的 User Code 與系統中的不一致", "state_usercode_not_match_adapter": "未找到綁定到此授權的User Code", "title": "授權管理", "trial_button": "試用", "trial_day": "天", "trial_expired": "試用期滿", "trial_over_nodes": "如果您不刪除超過的節點，MXview One 將在 10 分鐘後自動登出", "trial_remaining": "試用剩餘", "user_code": "User Code:", "valid": "有效", "wireless_addon_trial": "開始試用 MXview One 上的無線附加功能"}, "link_list": {"rx": "RX (%)", "tx": "TX (%)"}, "LINK_PROPERTIES": "連線屬性", "LOGIN": {"account_reach_limit": "帳號超過授權數量(10)", "all_sites_offline": "所有站台都離線", "default_password_warning": "請改變預設密碼以得到較高的安全性", "error": "無效的使用者名稱或密碼", "ie_not_supported": "MXview One不支援IE, 請使用Chrome以獲得最佳體驗", "last_login_fail": "最新登入失敗記錄如下", "last_login_succeed": "您最後成功登入的時間是", "login_fail_time": "{{loginTime}} 來自 {{loginIp}}", "login_succeed_time": "{{loginTime}} 來自 {{loginIp}}", "logout": "登出", "password": "密碼", "password_policy_mismatch": "密碼不符合密碼政策", "sign_in": "登入", "username": "使用者名稱", "welcome": "歡迎"}, "max_char": "最多 {{num}} 個字元", "min_char": "最少 {{num}} 個字元", "model-port-mapping": {"port": "連接埠", "web-ui": "裝置上的 Web UI"}, "MXVIEW_WIZARD": {"complete_page_title": "完成", "navigate_to_wizard_page": "您想使用MXview One安裝精靈嗎？", "step_add_scan_range": "新增掃描範圍", "step_auto_topology": "產生網路拓撲 (具有 LLDP 的設備)", "step_create_group": "建立群組", "step_select_site": "請選擇要設置的站台", "step_set_snmp": "設定SNMP", "step_set_trap_server": "設定 Trap 伺服器", "title": "設定精靈", "welcom_page_title": "歡迎使用初始化精靈"}, "NETWORK_MENU": {"add_link": "增加連線", "add_wifi_ssid": "新增 Wi-Fi SSID", "alignment": {"bottom": "靠下對齊", "left": "靠左對齊", "right": "靠右對齊", "title": "對齊", "top": "靠上對齊"}, "copy_device_list": "複製設備列表", "create_a_snapshot": "建立快照", "cybersecurity_control": "網路安全控制", "delete": "刪除", "device_configuration": "設備配置", "device_control": "設備控制", "device_dashboard": "設備儀表板", "device_login_account": "設備帳戶", "device_panel": "設備面板", "device_wireless_settings": "個別設備參數", "disable_unsecured_http_and_telnet_console": "停用不安全的 HTTP 和 Telnet 終端", "disable_unused_ethernet_and_fiber_ports": "停用未使用的乙太網路和光纖端口", "document": {"menu": {"open": "開啟", "set": "設定"}, "title": "文件"}, "dynamic_mac_sticky": "動態 Sticky MAC", "edit": {"menu": {"add_device": "新增設備", "delete_background": "刪除背景", "export_device_list": "匯出設備列表", "export_topology": "匯出拓撲", "import_device_list": "匯入設備", "set_background": "設置背景"}, "title": "編輯"}, "execute_cli": {"menu": {"execute_cli_object": "執行儲存的腳本", "execute_cli_script": "CLI 腳本"}, "title": "執行腳本"}, "grid": {"menu": {"import_scd": "匯入SCD"}, "title": "電源"}, "group": {"menu": {"change_group": "修改群組", "create_group": "建立群組", "group_maintenance": "群組維護"}, "title": "群組"}, "grouping": "組成群組", "ips_configuration": "IPS配置", "ipsec_status": "IPSec 狀態", "link_traffic": {"menu": {"packet_error_rate": "封包錯誤率", "port_traffic": "頻寬使用率"}, "title": "連線流量"}, "locator": "設備定位", "mac_sticky_on_off": "Sticky MAC 開/關", "maintenance": {"menu": {"advance_settings": "進階設定", "assign_model": "指定型號", "basic_information": "基本資訊", "change_device_icon": "更改設備圖示", "device_identification_settings": "設備識別設定", "eip_enable": "EtherNet/IP 啟用", "eip_tcp_port": "EtherNet/IP TCP 連接埠", "eip_udp_port": "EtherNet/IP UDP 連接埠", "export_config": "匯出配置", "generate_qr_code": "產生QR Code", "import_config": "匯入配置", "ip_configuration": "IP組態", "modbus_enable": "Modbus 啟用", "modbus_port": "Modbus 連接埠", "modbus_tcp_configuration": "Modbus TCP 設置", "polling_ip_setting": "輪詢 IP", "polling_settings": "MXview One 輪詢間隔", "port_settings": "乙太網路/光纖連接埠設置", "s7_port": "Siemens S7comm 連接埠", "s7_status": "Siemens S7comm 啟用", "serial_port_monitoring": "序列埠監控", "snmp_settings": "SNMP 通訊協議", "trap_server": "Trap 伺服器", "upgrade_firmware": "升級韌體"}, "title": "維護"}, "modify_device_alias": "設備別名", "policy_profile_deployment": "規則配置檔部署", "reboot": "重啟", "refresh": "重新整理", "restart_mac_sticky_learning": "重新學習動態 Sticky MAC", "restore_to_created_snapshot": "還原快照", "scale": "設定比例", "security_package_deployment": "網安防護包部署", "set_port_label": "設定連線標籤", "severity_threshold": "嚴重性閾值", "sfp": "SFP", "sfp_Info": "SFP 資訊", "sfp_list": "SFP 列表", "sfp_sync": "從設備同步閾值", "tools": {"menu": {"device_panel": "設備面板", "mib_browser": "MIB瀏覽器", "ping": "<PERSON>", "telnet": "Telnet", "web_console": "網頁控制台"}, "title": "工具"}, "topology": {"menu": {"auto_layout": "自動排列", "auto_topology": "自動拓撲", "embed": "嵌入小工具", "scan_range": "設備發現"}, "title": "拓撲"}, "ungrouping": "取消群組", "upgrade_patch": "執行系統更新", "visualization": {"menu": {"igmp": "IGMP", "security_view": "安全檢視", "traffic_view": "流量檢視", "vlan": "VLAN", "vlan_view": "Vlan 檢視"}, "title": "視覺化"}, "wifi_channel_change": "更改 Wi-Fi 頻道", "wireless_settings": "通用參數", "wireless": {"menu": {"wireless_planner_view": "無線網路覆蓋", "wireless_playback_view": "無線漫遊重播", "wireless_table_view": "無線設備列表"}, "title": "無線"}}, "NETWORK": {"current_status": {"no_event": "無事件", "title": "即時狀態", "v3_trap_event_clear_fail": "V3 trap 事件清除失敗", "v3_trap_event_suggestion": "請確認 snmp v3 設定"}, "not_selected": "選擇一個模組以顯示裝置詳細資訊"}, "NORTHBOUND_INTERFACE": {"custom_opc": {"add_custom_opc": "增加自訂OPC標籤", "all": "所有OPC", "apply_fail": "無法設置自定義OPC標籤", "apply_success": "自定義OPC標籤已成功新增", "delete_fail": "无法删除自定义OPC标签", "delete_success": "自定義OPC標籤已刪除", "device_properties": "設備屬性", "enable": "啟用自訂OPC標籤", "filter_custom_opc": "鍵入以篩選自訂OPC標籤", "get_fail": "無法獲取自定義OPC標籤", "property_name": "屬性名稱", "register_devices": "註冊設備", "title": "自訂OPC標籤", "update_custom_opc": "更新自訂OPC標籤", "update_fail": "無法更新自定義OPC標籤", "update_success": "自定義OPC標籤已成功更新"}}, "NOTIFICATION_SETTINGS": {"action": "通知輸出", "action_cant_deleted": "尚有通知包含此動作。請先清除相關通知，然後再刪除這個動作", "action_information": "動作資訊", "action_name": "動作名稱", "action_tab_hint": "請切換到通知輸出分頁並先新增動作", "action_type": "類型", "add_action": "新增通知輸出", "add_action_fail": "增加動作失败", "add_action_success": "增加動作成功", "add_notification": "新增通知", "add_notification_fail": "增加通知配置失败", "add_notification_success": "增加通知配置成功", "check_security_tab": "檢查網路安全分頁", "content": "內容", "delete_action_fail": "無法刪除動作", "delete_action_success": "刪除動作成功", "delete_notification_fail": "無法刪除通知配置", "delete_notification_success": "刪除通知配置成功", "edit_action": "編輯通知輸出", "edit_notification": "編輯通知", "email": "電子郵件", "email_content_hint": "此處的內容將加到預設通知郵件的內容中", "event_type": "種類", "file_size_error": "最大檔案大小為1MB", "file_type_error": "聲音檔只支援WAV格式", "filter_action": "鍵入以篩選通知動作配置", "filter_notification": "鍵入以篩選通知配置", "messagebox": "訊息框", "mobile": "MXview ToGo", "mobile_number": "手機電話", "notification": "通知", "notification_name": "通知名稱", "notification_name_exist": "這個名字已經被另一個通知所使用", "receiver_email": "收件人電子郵件", "register_devices": "註冊設備", "register_subscribers": "訂閱者", "slack": "<PERSON><PERSON>ck", "sms": "SMS", "snmptrap": "SNMP Trap", "sound": "聲音檔", "teams": "Microsoft Teams", "testConnection": "測試連接", "title": "通知配置", "update_action_fail": "無法更新動作", "update_action_success": "動作已经更新成功", "update_notification_fail": "無法更新通知配置", "update_notification_success": "通知配置已经更新成功", "webhook": "Webhook", "webhook_fail": "無法執行Webhook", "webhook_success": "Webhook已發送"}, "OPC_UA_SERVER": {"add_opc_tags": "新增 OPC 標籤", "anonymous": "匿名的", "auth_setting": "認證設定", "certificate": "憑證", "certificate_link": "從以下位置下載和管理憑證", "change_authentication_password": "更改認證密碼", "change_password": "更改密碼", "control_panel": "MXview One 控制面板", "create_tags_fail": "無法新增標籤", "create_tags_success": "標籤新增成功", "delete_tag_content": "您確定要刪除此 OPC 標籤嗎？", "delete_tags": "刪除 OPC 標籤", "delete_tags_content": "您確定要刪除這些 OPC 標籤嗎？", "delete_tags_fail": "刪除標籤失敗", "delete_tags_success": "標籤刪除成功", "device_property": "設備屬性", "disabled": "未啟用", "edit_opc_tags": "編輯 OPC 標籤", "edit_tags_fail": "更新標籤失敗", "edit_tags_success": "標籤更新成功", "enable_opc_server": "啟用 OPC UA 伺服器", "enabled": "啟用", "exceed_server_performance": "已達到註冊設備的最大數量 (4000)。", "get_tags_list_fail": "檢索標籤列表失敗", "ip_domain_name": "IP/網域", "method": "方式", "opc_tags": "OPC標籤", "property_name": "設備屬性", "registered_device": "註冊的設備", "security": "安全模式", "security_placeholder": "允許不安全", "server_settings": "伺服器設定", "status": "狀態", "support_security_policy": "支援的安全政策", "tag_name": "標籤名稱", "tag_name_duplicate": "該標籤名稱已存在", "tags_exceed_limit": "已達到最大標籤數 (2000)。", "title": "OPC UA 伺服器", "update_fail": "更新設定失敗", "update_server_setting_fail": "無法更新伺服器設定", "update_server_setting_fail_no_up": "更新設定失敗。指定的 IP 不存在。", "update_server_setting_success": "伺服器設定更新成功", "username": "帳號及密碼"}, "PAGES_MENU": {"about": "關於", "administration": {"account_management": "帳號管理", "device_settings_template": "預設設備範本", "global_device_settings": "全域設備設置", "license_management": "授權管理", "maintenance_scheduler": "維護計畫", "preferences": "偏好選項", "system_settings": "系統設置", "title": "管理", "troubleshooting": "故障排除"}, "alert": {"custom_events": "自訂事件", "device_threshold": "設備閾值", "event_settings": "事件設定", "link_threshold": "連線閾值", "notifications": "通知管理", "title": "警告"}, "cli_object_database": {"title": "儲存的 CLI 腳本"}, "dashboard": "儀表板", "device_management": {"account_password": "帳號及密碼", "configuration_control": "配置和控制", "title": "設備管理"}, "devices": {"device_configurations": "設備配置", "list_of_devices": "設備列表"}, "event": {"all_events": "歷史事件", "custom_events_management": "自訂事件", "notification_management": "通知管理", "syslog_settings": "Syslog 設定", "syslog_viewer": "Syslog 瀏覽器", "title": "事件管理"}, "firewall_policy_management": {"dos_descr": "配置 DoS 規則", "ips_descr": "配置 IPS 規則", "layer3to7_descr": "配置第3-7 層防火牆規則", "policy_profile_deployment": "規則配置檔部署", "policy_profile_management": "規則配置檔管理", "security_package_deployment": "網安防護包部署", "security_package_management": "網安防護包管理", "sessionControl_descr": "配置會話控制規則", "title": "防火牆規則管理"}, "firmware_management": {"title": "韌體管理"}, "help": {"about_mxview": "關於MXview One", "api_documentation": "API文檔", "title": "幫助", "user_manual": "使用者手冊"}, "license": "授權", "links": {"list_of_rj45_links": "RJ45 連線列表", "list_of_sfp_links": "SFP 連線列表", "list_of_wifi_links": "Wi-Fi 連線列表", "title": "連線"}, "migrations": {"configuration_center": "設備配置中心", "database_backup": "資料庫備份", "job_scheduler": "維護計畫", "title": "遷移"}, "network": {"scan_range": "掃描範圍", "title": "網路", "topology": "拓撲", "wizard": "精靈"}, "northbound_interface": {"custom_opc_tags": "自訂OPC標籤", "opc_ua_server": "OPC UA Server", "restful_api_management": "RESTful API 管理", "title": "整合", "web_widget_embedded": "網頁嵌入小工具"}, "preferences": "偏好選項", "report": {"assets_and_warranty": "資產和保固", "availability_report": "可用性報告", "inventory_report": "庫存報告", "rogue_device_detection": "非法設備偵測", "title": "報告", "vlan": "VLAN"}, "scan_range_wizard": {"title": "掃描範圍精靈"}, "security": {"account_management": "帳號管理", "security_analyser": "安全分析", "title": "安全"}}, "pages": {"deviceDeployment": {"alreadySentSms": "本月已發送 {{ smsNumber }}/{{ max }} 封簡訊", "applied": "應用", "atLeastSelectOne": "選擇至少一則簡訊控制指令", "cellularModuleDisable": "蜂窩模組被禁用，不允許發送簡訊命令。", "cellularStartConnecting": "行動網路開始連接", "cellularStopConnecting": "行動網路停止連接", "configSync": "所選設備的配置將會同步。", "daily": "每日", "date": "日期", "deleteMsg": "您確定要刪除所選設備嗎？", "deleteSchedule": "刪除排程", "deleteScheduleSuccess": "設備排程已成功刪除", "deleteSuccess": "刪除設備成功", "deleteTitle": "刪除設備", "device_ip": "設備 IP", "deviceConfiguration": "設備配置", "deviceDetail": "設備詳情", "deviceDisableHint": "裝置端已停用該功能", "deviceSelected": "選定的設備", "endTime": "結束日期", "firmware": "韌體", "firmwareUpgrade": "所選設備的韌體將被升級。", "firmwareVersion": "韌體版本", "general": "全域設定", "groupName": "群組名稱", "groupSelected": "選定的群組", "invalidDate": "無效日期", "invalidPeriod": "無效期間", "lastRebootTime": "上次重啟時間", "lastUpdate": "最後檢查", "lastUpdateTime": "最後更新時間", "location": "位置", "mac": "MAC", "manually": "手動", "maxSms": "您已達到每月簡訊限額（最大. {{ max }}）", "noConfigAvailable": "無可用配置", "noConfigMsg": "在「管理/設備配置」頁面查看設定資訊。", "noFirmwareMsg": "在「管理/韌體」頁面查看韌體檔案。", "noPackageMsg": "在網安防護包管理頁面，查看網安防護包。", "noProfileAvailable": "沒有可用的設定檔", "noProfileMsg": "在「規則配置檔管理」頁面查看配置檔。", "notSupportModel": "不支援型號", "notSync": "未同步", "noVersionAvailable": "無可用版本", "oneTime": "一次", "outOfSync": "不同步", "package": "防護包", "packageUpgrade": "所選設備的網安防護包將會升級。", "packageVersion": "防護包版本", "period": "期間", "policyProfile": "規則配置檔", "processing": "處理中", "profileName": "配置檔名稱", "profileSync": "所選設備的配置檔將被同步。", "reboot": "設備將重新啟動。", "rebootDisabled": "只能重新啟動在線設備。", "rebootMsg": "您確定要重新啟動所選設備嗎？", "rebootTitle": "重新啟動設備", "remoteSmsControl": "遠端簡訊控制", "restoreConfigDisabled": "僅可同步相同型號的線上設備。", "sameVersionWarning": "一台或多台選定的裝置已套用版本 {{ version }}。", "schedule": "排程", "scheduleDisabled": "只能排程相同型號的設備。", "scheduleOverlapMsg": "無法選擇已指派重新啟動或韌體升級的時間段。", "scheduleSettings": "排程設定", "scheduling": "排程", "schedulingMode": "排程模式", "schedulingPeriod": "排程周期", "schedulingReboot": "排程重啟", "selectConfigFile": "選擇設定檔", "selectFile": "選擇檔案", "sendSms": "傳送簡訊", "sendSmsControl": "傳簡訊控制", "sendSmsOnCell": "選擇一台 OnCell 裝置發送簡訊控制", "sendSmsSuccess": "簡訊發送成功", "serialNumber": "序號", "setDoOff": "設定 DO 關閉", "setDoOn": "設定 DO 開啟", "shouldBeSameVersion": "所選設備的軟體版本應相同。", "shouldHaveJanus": "一台或多台選定的設備未安裝網安防護包。", "shouldSyncOnline": "只能同步線上設備。", "showAll": "顯示所有群組和設備", "showSelected": "顯示選定的群組和設備", "smsCountDownHint": "60 秒後發送下一條短信", "softwarePackage": "網安防護包", "startIpsecTunnel": "啟動 IPsec 隧道", "startTime": "開始日期", "status": "狀態", "statusProfileName": "狀態/設定檔名稱", "stopIpsecTunnel": "停止 IPsec 隧道", "switchSim": "切換SIM卡", "sync": "已同步", "syncConfig": "同步配置", "syncConfigTitle": "將配置同步到設備", "syncModified": "已同步（已修改）", "syncProfile": "同步配置檔", "syncProfileTitle": "將配置檔同步到設備", "systemRestart": "系統重啟", "time": "時間", "updateScheduleSuccess": "設備排程更新成功。", "upgradeDisabled": "僅線上設備可以升級。", "upgradePackageError": "2.5.0 以上和 2.4.x 以下的韌體版本不能共存。", "upgradePackageNotSameDisabled": "只能選擇相同型號的設備", "upToDate": "最新", "version": "版本", "weekly": "每週", "weeklyDay": "每周某日"}, "logging": {"eventLog": {"adp": "ADP", "audit": "審計", "device": "設備", "dos": "DoS規則", "dpi": "協定篩選規則", "endDate": "結束日期", "endTime": "結束時間", "event": "事件", "firewall": "防火牆", "ips": "IPS", "l2Policy": "第 2 層規則", "l3Policy": "第 3-7 層規則", "malformed": "格式錯誤的數據包", "sc": "連線數控制", "setting": "設定", "severity": "嚴重性", "startDate": "開始日期", "startTime": "開始時間", "tab": {"audit": {"deviceName": "設備名稱", "event": "事件", "groupName": "群組名稱", "message": "訊息", "severity": "嚴重性", "time": "時間", "username": "使用者名稱"}, "device": {"deviceName": "設備名稱", "event": "事件", "groupName": "群組名稱", "mac": "MAC 位址", "message": "訊息", "severity": "嚴重性", "time": "時間", "username": "使用者名稱"}, "firewall": {"action": "動作", "adp": "ADP", "all": "所有事件", "appProtocol": "應用協定", "category": "類別", "deviceName": "設備名稱", "dos": "DoS規則", "dpi": "協定篩選規則", "dstIp": "目的IP", "dstMac": "目的地MAC位址", "dstPort": "目的連接埠", "etherType": "乙太類型", "event": "事件", "fromInterface": "傳入介面", "groupName": "群組名稱", "icmpCode": "ICMP 程式碼", "icmpType": "ICMP 類型", "id": "索引", "ips": "IPS", "ipsCategory": "IPS類別", "ipsSeverity": "IPS 嚴重性", "l3Policy": "第 3-7 層規則", "malformed": "格式錯誤的數據包", "message": "附加資訊", "policyId": "規則 ID", "policyName": "規則名稱", "protocol": "IP 協定", "security": "安全", "sessionControl": "連線數控制", "severity": "嚴重性", "srcIp": "來源 IP", "srcMac": "來源MAC", "srcPort": "來源連接埠", "subCategory": "子類別", "tcpFlag": "TCP 標誌", "time": "時間", "toInterface": "傳出介面", "trustAccess": "可信任存取", "username": "使用者名稱", "vlanId": "VLAN ID"}, "vpn": {"deviceName": "設備名稱", "event": "事件", "groupName": "群組名稱", "message": "附加資訊", "severity": "嚴重性", "time": "時間", "username": "使用者名稱"}}, "trustAccess": "可信任存取", "vpn": "VPN"}, "notification": {"advancedSettingMsg": "一旦在一段時間內達到最大通知數量，則在下一個週期之前不再發送通知。", "advancedSettings": "進階設定", "appProtocol": "應用協定", "arpFlood": "ARP-Flood", "atLeastOneReceiver": "至少一個收件者", "bufferOverflow": "緩衝區溢位", "chooseDevices": "選擇設備", "createdBy": "創建自", "createNotification": "新增網路安全事件", "createSuccess": "網路安全事件建立成功", "deleteFailed": "此事件用於通知，無法刪除。", "deleteKey": "網路安全事件", "deleteNotification": "刪除通知", "deleteSuccess": "網路安全事件已成功刪除", "deviceCount": "設備數量", "deviceName": "設備名稱", "DNP3": "DNP3", "dosAttacks": "DoS 攻擊", "dstIp": "目的IP", "dstMac": "目的地MAC位址", "editNotification": "編輯網路安全事件", "EIP": "EIP", "email": "電子郵件", "emailContent": "電子郵件內容", "emailContentDefault": "事件 ${event} 在設備 ${productModel}, ${deviceName} 上觸發，發生時間為 ${eventTime}。", "emailHeader": "[MXsecurity] 通知 ${notificationName}\n從 ${deviceName} 生成", "emailMsgAutoSentFrom": "此通知已自動自 MXsecurity發送。", "emailMsgCheck": "請在 MXsecurity 上查看詳細訊息。", "emailMsgGreeting": "尊敬的先生/女士，", "emailMsgSignOff": "此致，\nMXsecurity", "eq": "等於", "event": "事件", "event_used": "此事件已在通知設定中使用，無法修改。", "eventFilter": "選擇事件和過濾條件", "eventFilterRule": "事件過濾規則", "eventTime": "事件時間", "exploits": "漏洞", "fileVulnerabilities": "文件漏洞", "filterRule": "過濾規則", "filterRuleDetail": "過濾規則詳情", "finScan": "FIN Scan", "floodingScan": "泛洪和掃描", "GOOSE": "GOOSE", "gt": "低於", "gte": "小於或等於", "icmpDeath": "ICMP-Flood", "IEC-104": "IEC-104", "ipAddress": "IP 位址", "ipRangeHint": "您可以使用 * 表示 /8/16/24 子網路遮罩的結果\\n，例如\\n 192.168.*.* \\n不能在 IP 位址的開頭\\n 或單獨使用在中間 *", "ipsCate": "IPS類別", "ipSpoofing": "IP欺騙", "ipsSeverity": "IPS 嚴重性", "location": "位置", "lt": "高於", "lte": "大於或等於", "macAddress": "MAC 位址", "macRangeHint": "您可以使用 * 來表示 MAC 位址的範圍，例如。 00:90:E8:*:*:* \\n不能用在 MAC 位址的開頭或單獨用在中間。", "malwareTraffic": "惡意軟體流量", "maxEnableSize": "最大啟用的通知數為 {{num}}。", "maxNotification": "最大通知", "maxPerUserSize": "每位使用者的最大通知數為 {{num}}。", "MMS": "MMS", "Modbus/TCP": "Modbus/TCP", "newTcpWithoutSynScan": "TCP Sessions Without SYN", "nmapIdScan": "NMAP-ID Scan", "nmapXmasScan": "NMAP-<PERSON><PERSON>", "notificationActions": "通知操作", "notificationEvent": "通知事件", "notificationInfo": "通知訊息", "notificationLimit": "通知限制", "notificationName": "事件名稱", "nullScan": "<PERSON><PERSON>", "OmronFINS": "Omron FINS", "periodTime": "時段", "policyName": "規則名稱", "productModel": "產品型號", "protocolAttackProtection": "協定攻擊防護", "receiverEmailAddress": "收件人電子郵件", "receiverSetting": "收件者設定", "reconnaissance": "偵察", "resetToDefault": "重設為預設值", "serialNumber": "序號", "severity": "嚴重性", "severityMode": "嚴重性模式", "severityRule": "嚴重性規則", "showAllDevices": "顯示所有設備", "showSelectedDevices": "顯示選擇的設備", "srcIp": "來源 IP", "srcMac": "來源MAC", "Step7Comm": "Step7Comm", "subCate": "子類別", "synFinScan": "SYN/FIN Scan", "synFlood": "SYN-Flood", "synRstScan": "SYN/RST Scan", "syslog": "Syslog", "syslogContent": "Syslog 內容", "syslogContentDefault": "通知 ${notificationName} 已在設備上觸發。 ${productModel}、${deviceName}，發生在 ${eventTime}。詳細資訊請查看 MXsecurity。", "udpFlood": "UDP-Flood", "updateSuccess": "通知配置已经更新成功", "webThreats": "網路威脅", "xmasScan": "<PERSON><PERSON>"}}, "management": {"deviceConfiguration": {"configModel": "配置型號", "configName": "配置名稱", "createSuccess": "設備配置創建成功。", "deleteKey": "設備配置", "deleteSuccess": "設備配置已成功刪除", "editConfig": "編輯配置", "enterConfigInfo": "輸入設定檔資訊", "firmwareVersion": "韌體版本", "group": "群組", "isReferenced": "引用一個或多個選定的配置。", "lastModifiedTime": "最後修改時間", "location": "位置", "mac": "MAC 位址", "maxTableSize": "最大配置為 {{num}}。", "noModelMsg": "沒有配置型號", "offlineWarning": "設備離線", "onlyAcceptIni": "僅接受' .ini '格式的設定檔。", "onlyOneFilePerTime": "一次只能上傳一個文件。", "selectConfigFile": "選擇設定檔", "selectWarning": "只允許一台設備進行設定備份", "serialNumber": "序號", "updateSuccess": "設備設定更新成功", "uploadConfigFile": "上傳配置文件 (.ini)", "uploadConfigMethod": "上傳配置方法", "uploadConfigTitle": "上傳設備設定檔", "uploadDeviceConfig": "從設備上傳配置", "uploadLocalConfig": "從本地上傳配置"}, "deviceGroup": {"accessPermission": "存取權限", "addDevices": "新增設備", "adminPermission": "管理員使用者擁有所有群組的權限", "createGroup": "建立群組", "createSuccess": "設備群組建立成功", "deleteKey": "刪除設備群組", "deleteSuccess": "設備群組刪除成功", "description": "描述", "deviceCount": "設備數量", "editGroup": "編輯設備群組", "enterGroupInfo": "輸入群組資訊", "firmwareVersion": "韌體版本", "grantAccessPermission": "授予存取權限", "group": "群組", "groupName": "群組名稱", "location": "位置", "mac": "MAC", "role": "角色", "serialNumber": "序號", "showAllDevices": "顯示所有設備", "showSelectedDevices": "顯示選擇的設備", "status": "狀態", "updateSuccess": "設備群組更新成功。", "username": "使用者名稱"}, "firmware": {"buildTime": "建構時間", "deleteKey": "韌體", "deleteSuccess": "韌體刪除成功。", "description": "描述", "dropZoneTitle": "上傳韌體檔案 (.rom)", "isReferenced": "引用了一個或多個選定韌體。", "maxRowMsg": "韌體檔案的數量最多為 {{ max }}。", "maxSize": "允許的最大檔案大小為 1 GB。", "modelSeries": "型號系列", "onlyAcceptRom": "僅接受 '.rom' 格式的韌體檔案。", "onlyOneFilePerTime": "一次只能上傳一個檔案。", "uploadFirmware": "上傳韌體", "uploadSuccess": "韌體上傳成功。", "version": "版本"}, "inUse": "是", "object": {"filter": {"address": "IP 位址和子網", "code": "程式碼", "createObject": "建立物件", "createSuccess": "物件新增成功。", "customIpProtocol": "自訂IP協定", "decimal": "（十進制）", "deleteKey": "物件", "deleteSuccess": "物件已成功刪除。", "detail": "詳細資訊", "editObject": "編輯物件", "endPort": "連接埠：結束", "icmp": "ICMP", "icmpCode": "ICMP 程式碼", "icmpType": "ICMP 類型", "industrialAppService": "工業應用服務", "ipAddress": "IP 位址", "ipEnd": "IP 位址： 結束", "ipProtocol": "IP協定", "ipRange": "IP範圍", "ipStart": "IP 位址： 開始", "ipType": "IP類型", "isReferenced": "引用一個或多個選取物件", "leaveAsAny": "留空代表任意", "maxRowMsg": "物件的最大數量為 {{ max }}。", "name": "篩選", "needSelectedMsg": "至少選擇一項", "networkName": "網路名稱", "networkService": "網路服務", "objectName": "名稱", "objectReference": "物件引用", "objectReferenceMsg": "該物件由以下配置檔中的規則索引引用：", "objectType": "物件類型", "port": "連接埠", "portRange": "TCP 和 UDP 連接埠範圍", "selectIndustrialAppService": "選擇工業應用服務 *", "selectNetworkService": "選擇網路服務*", "servicePortType": "服務連接埠類型", "singleIp": "單一IP", "singlePort": "TCP 和 UDP 連接埠", "startPort": "連接埠： 啟動", "subnet": "子網", "subnetMask": "子網路遮罩", "tcp": "TCP", "tcpUdp": "TCP 和 UDP", "type": "類型", "udp": "UDP", "updateSuccess": "物件更新成功。", "userDefinedService": "自訂服務"}, "interface": {"bridge": "橋接", "createInterface": "建立網路介面物件", "createSuccess": "介面創建成功。", "deleteKey": "介面", "deleteSuccess": "介面刪除成功。", "editInterface": "編輯網路介面物件", "interfaceName": "介面名稱", "interfaceReference": "介面引用", "interfaceReferenceMsg": "此介面由以下配置檔中的規則索引引用：", "invalidKey": "以下名稱已保留：任何", "isReferenced": "引用了一個或多個選定的介面。", "maxRowMsg": "介面的最大數量為 {{ max }}。", "mode": "模式", "name": "介面", "port": "基於連接埠", "updateSuccess": "介面更新成功。", "vlan": "VLAN", "vlanIdBridgeType": "VLAN ID / 橋接模式", "zone": "基於分區"}}, "policyProfile": {"createProfile": "建立規則配置檔", "createSuccess": "配置檔已成功建立。", "deleteKey": "配置檔", "deleteSuccess": "配置檔已成功刪除。", "deployment": {"profile_title": "規則配置檔部署", "security_title": "網安防護包部署", "title": "規則配置檔部署"}, "dos": {"all_protection_types": "啟用所有保護類型", "dosLogSetting": "DoS 日誌設定", "dosSetting": "DoS 設定", "floodProtection": "防洪", "limit": "限制", "portScanProtection": "連接埠掃描保護", "sessionSYNProtection": "連線數 SYN 保護", "stat1": "<PERSON><PERSON>", "stat10": "SYN-Flood", "stat11": "ARP-Flood", "stat12": "UDP-Flood", "stat2": "<PERSON><PERSON>", "stat3": "NMAP-<PERSON><PERSON>", "stat4": "SYN/FIN Scan", "stat5": "FIN Scan", "stat6": "NMAP-ID Scan", "stat7": "SYN/RST Scan", "stat8": "TCP Sessions Without SYN", "stat8Tooltip": "限制：對於非對稱網路架構且啟用 NAT 時，強烈建議不要停用“TCP Sessions Without SYN”，以避免意外中斷連線。", "stat9": "ICMP-Flood", "title": "DoS"}, "editProfile": "編輯規則配置檔", "ips": {"accept": "接受", "category": "類別", "custom": "（客製）", "id": "ID", "impact": "影響", "monitor": "監視", "noPackageMsg": "在網安防護包管理頁面，查看網安防護包。", "noVersionAvailable": "無可用版本", "packageVersion": "防護包版本", "reference": "參考", "reset": "重置", "ruleSetting": "規則設定", "title": "IPS", "updateSuccess": "規則更新成功。", "warningMsg": "在設定任何規則之前，請確保已在設備 Web 介面的防火牆 > 進階防護 > 配置功能頁啟用了入侵防禦系統 (IPS) 功能。"}, "isReferenced": "引用一個或多個選定的配置檔。", "layer3to7": {"allowAll": "允許全部", "createPolicy": "建立第 3-7 層規則", "createSuccess": "第 3-7 層規則創建成功。", "default_action_log": "事件日誌", "default_action_log_destination": "日誌存取於", "default_action_severity": "嚴重性", "defaultAction": "動作", "deleteKey": "第 3-7 層規則", "deleteSuccess": "第 3-7 層規則已成功刪除。", "deleteTitle": "刪除 3-7 層規則", "denyAll": "全部拒絕", "destinationAddress": "目的地位址", "destinationPort": "目標連接埠或協定", "destIpAddress": "目的IP位址", "destService": "目的地服務", "editPolicy": "編輯第 3-7 層規則", "enforce": "狀態", "enforcement": "狀態", "event": "事件", "eventSetting": "預設規則設定", "filterMode": "過濾模式", "globalSetting": "全域防火牆設定", "incomingInterface": "傳入介面", "ipAndPortFiltering": "IP 和連接埠過濾", "ipAndSourceMacBinding": "IP與來源MAC綁定", "ipTypeError": "來源連接埠 IP 協定 ({{ source }}) 與目標連接埠 IP 協定 ({{ dest }}) 不同", "maxRowMsg": "物件的最大數量為 {{ max }}。", "outgoingInterface": "傳出介面", "policyName": "名稱", "protocolService": "協議與服務", "sourceAddress": "來源位址", "sourceIpAddress": "來源IP位址", "sourceMacAddress": "來源MAC位址", "sourceMacFiltering": "來源MAC過濾", "sourcePort": "來源連接埠", "title": "第 3-7 層", "updateSuccess": "第 3-7 層規則更新成功。"}, "maxRowMsg": "配置檔的最大數量為 {{ max }}。", "profileName": "配置檔名稱", "profileReference": "配置檔參考", "profileReferenceMsg": "以下設備引用該配置檔：", "sessionControl": {"concurrentTcp": "並行 TCP 連線數", "connectionsRequestUnit": "連接數/秒", "connectionsUnit": "連接", "createPolicy": "建立連線數控制規則", "createSuccess": "會話控制規則建立成功。", "deleteKey": "連線數控制規則", "deleteSuccess": "連線數控制規則刪除成功。", "destinationIp": "目的IP", "destinationPort": "目的連接埠", "destIpAddress": "IP 位址", "destPort": "連接埠", "drop": "丟棄", "editPolicy": "編輯連線數控制規則", "enforcement": "狀態", "maxRowMsg": "此設備的最大規則數量為 {{ max }}。", "monitor": "監視", "sub_title": "網路主機和服務資源保護器", "tcpConnectionLimit": "TCP 連線限制", "tcpDestError": "IP 位址和連接埠不能同時為 \"任何\"", "tcpDestination": "TCP 目的地", "tcpLimitError": "您必須至少配置一項限制", "tcpLimitMsg": "至少需要一項限制", "title": "連線數控制", "totalTcp": "TCP 連線總數", "updateSuccess": "連線數控制規則更新成功。"}, "tabInspection": "檢查條件物件", "tabInterface": "網路介面物件", "tabPolicyProfile": "規則配置檔", "title": "規則配置檔管理", "updateSuccess": "配置檔已成功更新。"}, "scheduleInUse": "使用中的計劃", "scheduling": "排程", "softwarePackage": {"applicationProducts": "適用產品", "auto-download": "自動下載", "bugsFixed": "錯誤修復", "buildTime": "建立時間", "changes": "變更", "checkConnection": "檢查 Moxa 更新伺服器的連線狀態。", "checkNewPackage": "檢查 MOXA 伺服器上的新網安防護包版本。", "checkSoftwarePackage": "檢查網安防護包更新", "daily": "每日", "deleteKey": "網安防護包", "deleteSuccess": "網安防護包已成功刪除。", "description": "描述", "detailInfo": "詳細資訊", "dropZoneTitle": "上傳防護包 (.pkg)", "endDate": "結束日期", "endTime": "結束時間", "enhancements": "優化項目", "event": "事件", "isReferenced": "引用一個或多個選取防護包", "janus": "網安防護包", "lastConnectionCheck": "最後連接檢查", "lastSoftwarePackageUpdateResult": "最後網安防護包更新结果", "licenseActivationReminder": "授權啟動提醒", "licenseActivationReminderContent": "為了確保增強的安全機制，請啟動授權以啟用此功能。", "licenseTransferReminder": "授權轉移提醒", "licenseTransferReminderContent": "為了確保增強的安全機制，請在上傳網路安全套件之前轉移您的 MXsecurity 授權。", "local": "本地", "log": "事件日誌", "maxPackageMsg": "最大同時下載數：{{ max }} 個檔案。", "maxRowMsg": "防護包的最大數量為{{ max }}。", "maxSize": "允許的最大檔案大小為 1 GB。", "message": "訊息", "newFeatures": "新功能", "notes": "備註", "onlyAcceptPkg": "僅接受“.pkg”格式的檔案。", "onlyOneFilePerTime": "一次只能上傳一個文件。", "packageDownloading": "安裝包下載器正在運作", "packageReference": "防護包參考", "packageReferenceMsg": "該套件由以下配置檔引用：", "period": "期間", "productModel": "產品型號", "releaseDate": "發布日期", "releaseNote": "發行說明", "scheduling": "排程更新檢查", "schedulingMode": "排程模式", "server": "Moxa 更新伺服器狀態", "serverDisconnected": "只有連接伺服器後才能檢查網安防護包。", "severity": "嚴重性", "softwarePackageAlreadyLatest": "網安防護包已是最新版本。", "softwarePackageCheck": "網安防護包檢查", "softwarePackagesFile": "網安防護包檔案", "softwarePackagesUpdateCheck": "更新網安防護包", "startDate": "開始日期", "startTime": "開始時間", "supportedFunctions": "支援的功能", "supportedOperatingSystems": "支援的作業系統", "supportModel": "支援的型號", "supportSeries": "支援的系列", "syncSettingNotSet": "完成同步設定以檢查網安防護包。", "syncSettings": "排程更新檢查", "syncSettingUpdateSuccess": "設定更新成功", "syncSoftwarePackageBySchedule": "根據使用者指定的排程自動檢查指定型號的網安防護包更新。", "syncSoftwarePackageByScheduleTooltip": "設定檢查 Moxa 伺服器網安防護包更新的頻率。", "time": "時間", "title": "網安防護包管理", "updateCheckTooltip": "檢查 Moxa 伺服器的網安防護包更新，以確保您使用的是最新版本。", "uploadBy": "上傳自", "uploadSoftwarePackage": "上傳防護包", "uploadSuccess": "網安防護包上傳成功。", "username": "使用者名稱", "version": "版本", "weekday": "天", "weekly": "每週", "zeus": "MXsecurity 代理安裝包"}}}, "PORT_SETTING": {"another_port_setting_error": "另一個設定正在處理", "apply_another_port": "將設置應用到另一個埠", "disable_port_warning": "警告：禁用此埠將斷開連接到此埠的設備。", "enable": "啟用", "get_port_setting_fail": "無法獲取埠設置", "hint": "* 如果設置失敗，請確認所選埠可以配置", "media_type": "Media Type", "port": "埠", "port_description": "埠描述", "port_name": "埠名稱", "port_select": "您選擇了", "set_fail": "部分埠設定失敗，請稍候再試", "set_success": "所有埠設定成功", "title": "乙太網路/光纖連接埠設置"}, "PREFERENCES": {"advanced": "進階", "appearance": "外觀", "default_view": {"choose_start_page": "選擇一個起始頁", "dashboard": "儀表板", "title": "預設檢視", "topology": "拓撲"}, "device_appearance": {"alias": "别名", "bottom_hint": "如果您改變了別名設置，請刪除拓撲上的設備，然後重新掃描或新增設備以完成'別名'設置。", "bottom_label": "底部標籤", "bottom_label_items": {"alias": "别名", "location": "位置", "mac": "MAC", "model_name": "型號名稱", "none": "無", "sysname": "SysName"}, "get_fail": "無法取得設備外觀設定", "ip_address": "IP 地址", "set_fail": "無法儲存設備外觀", "set_success": "設備外觀儲存成功", "title": "設備"}, "device": {"login": "登入", "title": "設備"}, "dialog": {"desc": "清除所有\"不再顯示此訊息\"設定，然後再次顯示所有隱藏的對話框", "title": "對話框"}, "display": "外觀", "email_config": {"allow_selfsigned_cert": "允許自簽憑證", "apply_fail": "無法储存Email server 設定", "apply_success": "Email server 設定储存成功", "encryption": "加密", "password": "密碼", "port_number": "通訊埠號", "sender_address": "寄送人位址", "server_domain_name": "伺服器網域名稱/IP", "title": "Email Server 設定", "username": "使用者名稱"}, "events": {"apply_fail": "無法儲存事件閾值組態", "apply_success": "事件閾值組態儲存成功", "availability_under": "可用性小於", "bandwidth_utilization_over": "頻寬利用率超過", "bandwidth_utilization_under": "頻寬利用率低於", "link_down": "斷線", "link_up": "連線", "packet_error_rate_over": "封包錯誤率超過", "port_looping": "連接埠迴圈", "sfp_rx_below_threshold": "SFP RX 低於", "sfp_temp_over_threshold": "SFP 溫度超過", "sfp_tx_below_threshold": "SFP TX 低於", "sfp_volt_below_threshold": "SFP 電壓低於", "sfp_volt_over_threshold": "SFP 電壓超過", "title": "事件"}, "labs": {"colored_link_desc": "啟用後，所有的無線連線都會按信噪比來顯示顏色", "desc": "切換到實驗室會增加以下實驗性功能，它們處於早期階段，但不會對您的系統造成傷害。", "dialog_title": "啟用實驗室功能", "title": "MXview One實驗室"}, "language": {"default_language": "語言", "en_US": "English", "fail": "無法儲存預設語言", "success": "預設語言儲存成功", "title": "語言", "zh_CN": "简体中文", "zh_TW": "繁體中文"}, "login_authentication": {"apply_fail": "無法儲存登入認證", "apply_success": "登入認證儲存成功", "authentication_protocol": "認證協議", "local": "本地", "tacacs": "TACACS+", "tacacs_local": "TACACS+, 本地", "title": "登入認證"}, "login_notification": {"fail": "無法儲存登入訊息", "login_authentication_failure_message": "登入認證錯誤訊息", "login_message": "登入訊息", "show_default_password_notification": "顯示預設密碼訊息", "show_login_failure_records": "顯示登入失敗記錄", "success": "登入訊息儲存成功", "title": "登入訊息"}, "management_interface": {"help": " 此設定頁面用來設定MXview One如何與switch通訊，包括http,https,telnet port的設定", "http_port": "HTTP Port", "htts_port": "HTTPS Port", "invalid_port": "埠號碼必須是 1-65535", "set_fail": "無法設置管理通訊埠", "set_success": "設置管理介面成功", "telnet_port": "Telnet Port", "title": "管理介面", "web_console_protocol": "網頁控制台通訊協定"}, "modbus_tcp_configuration": {"port": "port"}, "opc_server_config": {"apply_fail": "無法儲存 OPC server 配置", "apply_success": "OPC server 配置储存成功", "enable_opc_server": "啟用", "title": "OPC Server 組態"}, "password_policy": {"fail": "無法儲存密碼規則", "has_digits": "最少一個數字 (0~9)", "has_special_chars": "最少一個特殊字元 (~!@#$%^&*-_|;:,.<>[]{}())", "min_password_length": "最短長度 (4 - 16)", "min_password_length_error": "請輸入有效的值", "mixed_case": "混合大小寫 (A~Z, a~z)", "password_strength_check": "密碼強度檢查", "success": "密碼規則儲存成功", "title": "密碼規則"}, "search": "搜索", "security_view": {"all": "全部", "awk_device_credentials_hint": "要支援安全檢視，必須設定此設備的使用者名稱和密碼", "basic": "基本", "basic_text": "基本", "built_in_profile": "內建設定檔", "check_item": "檢查項目", "colors_for_check_result": "顏色定義", "current_setting": "當前設定:", "custom": "自訂", "custom_profile": "選擇一個設定檔", "device_security_level": "設備安全級別:", "failed": "設置安全檢視失敗", "filter_result": "篩選結果為", "high": "高", "high_text": "高", "medium": "中", "medium_text": "中", "new_profile": "新配置檔", "not_pass": "未通過", "open": "未達安全層級", "pass": "通過", "profile": "配置檔", "profile_details": "內建設定檔詳細資訊", "success": "設置安全檢視成功", "title": "安全檢視", "unknown": "未支援", "user_defined": "使用者自訂"}, "Server": "伺服器", "site_name_configuration": "站台名稱配置", "sms_config": {"apply_fail": "無法储存 SMS 設定", "apply_success": "SMS 設定储存成功", "baud_rate": "Baud <PERSON>", "com_port": "COM 埠", "mode": "模式", "title": "SMS 設定"}, "snmp_configuration": {"help": "設置SNMP配置以訪問網絡設備", "title": "SNMP組態"}, "SNMP_TRAP": {"apply_fail": "無法進行 SNMP Trap 設定", "apply_success": "SNMP Trap Server 設置已被設定成功", "community1": "Trap Server 1 的 Community", "community2": "Trap Server 2 的 Community", "device_list": "裝置列表", "device_trap": "裝置的 Trap Server", "forward_trap_control1": "轉發 Trap 到 Server 1", "forward_trap_control2": "轉發 Trap 到 Server 2", "ip1": "Trap Server 1 的 IP 位址", "ip2": "Trap Server 2 的 IP 位址", "mxview_trap": "MXview One 的 SNMP Trap Server", "version": "SNMP 版本", "version_1": "SNMP 版本 1", "version_2": "SNMP 版本 2c"}, "syslog_config": {"already_running": "配置檔案不正確。 請打開 %MXviewPRO_Data%\\nms-platform\\runtime\\generic-processor\\configs 中的 config.json 檔案，並將 enable_syslog_server 設置為\"true\"。", "apply_fail": "無法儲存 syslog server 配置", "apply_success": "Syslog 伺服器配置储存成功", "enable_syslog_server": "啟用內建 syslog 伺服器", "invalid_port": "埠號碼必須是 1-65535", "syslog_server_port": "Syslog 伺服器埠", "title": "Syslog 伺服器組態"}, "system_configuration": {"apply_fail": "无法储存系統組態", "apply_success": "系統組態储存成功", "background_discovery": "背景發現", "disk_hint": "設置為0表示停用磁碟空間報警", "playback": "重播", "playback_hint_1": "* 當\"重播\"功能被啟用，MXview One將記錄事件發生時設備和連線的狀態，您可以進入播放模式觀看詳細過程。", "playback_hint_2": "* 啟用\"重播\"功能需要額外的磁碟空間。.", "threshold_disk_space": "磁碟空間閾值 (MB)", "title": "系統組態"}, "table": {"default": "預設", "dense": "緊密", "fail": "無法儲存表格設定", "success": "表格設定储存成功", "table_row_height": "行高", "title": "表格"}, "tacacs": {"apply_fail": "無法儲存 TACACS 服務器配置", "apply_success": "TACACS+ 服務器配置儲存成功", "auth_type": "認證類型", "auth_type_asc_two": "ASCII", "auth_type_chap": "CHAP", "auth_type_ms_chap": "MS-CHAP", "auth_type_pap": "PAP", "server_address": "伺服器位址", "share_key": "共享密鑰", "tcp_port": "TCP 埠號", "timeout": "超時", "title": "TACACS+ 伺服器"}, "title": "偏好選項", "topology_appearance": {"access_port": "Access Port", "background": "背景", "background_color": "背景顏色", "directed_line_style": "直線樣式", "edit_igmp_visualization_color": "編輯 IGMP 視覺化顏色", "edit_traffic_load_color": "編輯流量負載顏色", "edit_vlan_visualization_color": "編輯 VLAN 視覺化 顏色", "elbow_line_style": "曲折線樣式", "fail": "無法儲存拓墣外觀", "hsr_ring": "HSR Ring", "igmp_visualization": "IGMP 視覺化", "link_down": "斷線", "link_up": "連線", "member": "Member", "poe": "PoE", "poe_color": "PoE連線顏色", "prp_lan_a": "PRP LAN A", "prp_lan_b": "PRP LAN B", "querier": "<PERSON><PERSON>", "rstp": "RSTP", "show_poe": "在網路拓撲上顯示PoE資訊", "status_color": "狀態顏色", "success": "拓墣外觀儲存成功", "text_size": "字體大小", "text_size_large": "大", "text_size_medium": "中", "text_size_small": "小", "title": "拓墣", "topology_style": "拓墣連線樣式", "traffic_load": "流量負載", "trunk_port": "Trunk Port", "turbo_chain": "Turbo Chain", "turbo_ring_v1": "Turbo Ring V1", "turbo_ring_v2": "Turbo Ring V2", "vlan_visualization": "VLAN 視覺化"}, "user": "使用者"}, "rogue_device_detection": {"add_device_for_rogue_device_detection": "將此設備新增到設備基準以進行非法設備偵測", "add_device_to_baseline": "新增設備至基準", "add_device_to_baseline_content": "您確定要將此設備新增至基準嗎？", "add_devices_to_baseline_content": "您確定要將這些設備新增至基準嗎？", "add_scan_device_for_rogue_device_detection": "將掃描到的設備新增至設備基準以進行非法設備偵測", "clear_all_rogue_device_history": "清除非法設備歷史記錄", "clear_all_rogue_device_history_hint": "所有非法設備歷史記錄都將被清除。您確定要繼續嗎？", "connected_switch_port": "連接的交換機/連接埠", "creation_time": "建立於", "current_rogue_device": "目前的非法設備", "delete_device_from_baseline": "從基準中刪除設備", "delete_device_from_baseline_content": "該設備將從基準中刪除並新增為非法設備。", "delete_devices_from_baseline_content": "這些設備將從基準中刪除並新增為非法設備。", "device_baseline": "設備基準", "device_baseline_content": "此操作將建立一個新基準並覆蓋現有基準。", "download_all_history_data_to_csv": "將所有歷史資料匯出到 CSV", "download_current_page_to_csv": "將目前頁面匯出為 CSV", "first_seen": "首次看到", "ip": "IP 位址", "last_seen": "最後看到", "mac": "MAC 位址", "must_create_a_baseline_first": "無設備基準。請先建立一個基準。", "no_devices_can_add": "未偵測到設備。請先將設備新增至 MXview One。", "port": "連接埠", "rogue_device_history": "非法設備歷史記錄", "rogue_device_settings": "非法設備設定", "sequence_no": "序號", "unknown": "未知的", "vendor": "網卡供應商"}, "SCAN_RANGE": {"add_scan_range": "新增掃描範圍", "button": {"back": "上一步", "browse_topology": "瀏覽拓撲", "cancel": "取消", "discovery": "確認並執行發現程序", "next": "下一步", "recover": "恢復", "scan_new_network": "掃描新的網路"}, "cidr_address_range": "CIDR 位址範圍", "duplicate_range": "掃描範圍與存在範圍重疊", "edit_scan_range": "編輯掃描範圍", "firstIp_higher_lastIp": "IP 範圍無效 (第一個 IP > 最後一個 IP)", "subnet_mask": "子網路遮罩", "table_title": {"active": "有效掃描範圍", "background_scan": "背景掃描", "conflict_scan": "IP 衝突偵測", "edit": "編輯", "end_ip": "最後一個 IP 位址", "group": "群組", "name": "名稱", "site_name": "站台名稱", "start_ip": "第一個 IP 地址"}, "wizard": {"complete": "完成", "complete_message": "有{{discoveryDevices}}台已被新增到MXview One", "discovery_result": "發現結果", "network_range": "網路範圍", "save_hint": "設備發現後，掃描的範圍將被保存。", "title": "掃描範圍精靈"}}, "script_automation": {"add_a_script_automation": "新增腳本自動化", "add_script_button_hint": "腳本自動化的最大數量為 200。", "add_script_first_hint": "未找到腳本自動化。前往腳本自動化畫面以新增腳本自動化。", "add_script_sutomation": "新增腳本自動化", "adjustable_buttons": "重新排序按鈕", "affected_devices": "受影響的設備", "affected_devices_info": "此操作將影響 {{ affectedDevices }} 台設備。", "affected_devices_info_2": "此操作將影響以下 {{ affectedDevices }} 台設備", "align_buttons": "將所有群組對齊在一欄中", "all_devices": "所有設備", "automation_button": "自動化按鈕", "background_color": "背景顏色", "button_name": "按鈕名稱", "button": {"panel": "按鈕面板", "privilege": "需要管理員權限認證", "state": {"hint": "在任何時間，一個群組中只能有一個按鈕處於「開啟」狀態", "off": "關閉", "on": "開啟", "title": "按鈕狀態"}, "style": "顯示模式", "widget": "按鈕工具"}, "cli_id_duplicated": "此 CLI 已被選取。", "cli_script_and_target_device": "CLI 腳本和目標設備", "cli_script_hint": "腳本的最大數量為 50。", "color": "顏色", "confirm_proceed": "您想繼續嗎？", "create_new_group": "建立並新增到新群組", "delete_automation": "您確定要刪除此腳本自動化嗎？", "delete_multiple_automation": "您確定要刪除這些腳本自動化嗎？", "delete_script_automation": "刪除腳本自動化", "description": "描述", "device_missing": "無法找到以下設備", "drag_button": "將按鈕拖曳到此處以建立新群組。", "edit_button": "編輯按鈕", "edit_group": "編輯群組", "edit_panel": "編輯按鈕面板名稱", "edit_script_button": "編輯腳本自動化", "execute_button": "執行按鈕", "execute_button_hint": "執行腳本自動化", "execute_button_info": "請等待該過程完成以查看結果。如果離開此畫面，您可以前往「已儲存的 CLI 腳本」>「執行結果」下載結果。", "extra_large": "特大", "group": "群組", "group_already_exist": "此群組名稱已存在", "group_name": "群組名稱", "invalid_account": "帳號權限無效", "ip_duplicated": "該設備已被選取。", "large": "大", "last_executed": "上次執行", "leave_page_hint": "您確定要離開此頁面嗎？", "leave_without_saving": "離開且不儲存", "medium": "中", "more": "更多資訊", "name": "名稱", "not_change_group": "使用目前群組", "not_saved_hint": "您所做的任何更改都不會被儲存。", "script_automation": "腳本自動化", "select_all": "全選", "select_existing_group": "移至另一群組", "select_saved_cli_script": "選擇已儲存的 CLI 腳本", "small": "小", "start_preview": "啟用拓樸預覽", "stop_preview": "停止預覽", "target_device": "目標設備", "text_color": "文字顏色", "widget_size": "小工具尺寸"}, "SECURITY_ITEM": {"ACCESSIBLE_IP_LIST": "啟用Trusted Access的功能", "ACCOUNT_LOCKOUT": "啟用帳號鎖定的功能", "ACCOUNT_VALIDITY": "帳號與密碼規則驗證", "AUTO_LOGOUT": "啟用自動登出的功能", "AWK_SERIES": "無線設備", "BROAD_CAST_STORM": "啟用 DDoS Protection 的功能", "changed": "已變更", "disabled": "未啟用", "enabled": "已啟用", "ENCRYPT_CONSOLE": "關閉未加密的TCP/UDP連接埠", "ENCRYPTED_CONFIG": "啟用加密設定檔的功能", "HIGH_SECURE_MODE": "高安全性配置", "LOGIN_NOTIFICATION": "設定登入訊息", "MGATE": "閘道器", "non-changed": "未變更", "not_set": "未設定", "NPORT": "終端設備伺服器", "NPORT5000": "串列設備伺服器", "NTP_SERVER": "設定NTP Client", "PASSWORD_CHANGED": "變更預設密碼 / SNMP Community String", "PASSWORD_POLICY": "啟用檢測密碼強度的功能", "read_fail": "讀取失敗", "set": "已設定", "SWITCH": "交換機", "SYSLOG": "設定Syslog Server", "TRAPSYSLOG": "設定SNMP Trap/Inform或Syslog Server", "unknown": "未支援", "WEB_CERTIFICATE": "匯入網頁憑證"}, "SERIAL_PORT_MONITORING": {"all_ports": "所有連接埠", "any_serial_error_count": "任何序列錯誤計數", "break_error_count": "中斷錯誤計數", "copy_configuration_device": "將配置複製到設備", "count_threshold": "觸發閾值", "counts": "次", "critical": "嚴重", "error_status": "序列埠警告", "event_condition_rule": "事件觸發規則", "frame_error_count": "訊框錯誤計數", "hint_any": "請按照下方步驟嘗試解決以下問題：序列埠 {{portnum}} 已超過溢位錯誤 ({{overrun error count}})、奇偶校驗錯誤 ({{parity error count}})、訊框錯誤 ({{ frame error count}}) 或中斷錯誤 ({{break error count}}) 計數閾值。", "hint_break": "請按照下方步驟嘗試解決以下問題：序列埠 {{portnum}} 已超過中斷錯誤計數閾值 ({{count}})。\n串列中斷訊號表示所連接的串列設備出現特殊情況，例如接線問題、設備故障、設備重置或同步過程。", "hint_frame": "請按照下方步驟嘗試解決以下問題：序列埠 {{portnum}} 已超過訊框錯誤計數閾值 ({{count}})。\n當 Moxa 設備接收到串列資料時，會檢查訊框格式是否與串列參數匹配。如果不匹配，則計為訊框錯誤。", "hint_general_1": "如果建議的解決方案無效或者您還有其他問題，請先聯絡您的", "hint_general_2": "。", "hint_general_3": "聯繫", "hint_general_4": "如果您仍然需要額外的支援。", "hint_overrun": "請按照下方步驟嘗試解決以下問題：序列埠 {{portnum}} 已超過溢位錯誤計數閾值 ({{count}})。\n如果連接的串列設備發送資料的速度太快，以致於 Moxa 設備無法讀取，將導致數據遺失，從而引發溢位錯誤。", "hint_parity": "請按照下方步驟嘗試解決以下問題：序列埠 {{portnum}} 已超過奇偶校驗錯誤計數閾值 ({{count}})。\n奇偶校驗錯誤表示接收到的資料字元與配置的奇偶校驗不符。", "hint_rx": "請按照下方步驟嘗試解決以下問題：序列埠 {{portnum}} 在過去 {{min}} 分鐘內未收到任何資料。", "hint_rxtx": "請按照下方步驟嘗試解決以下問題：序列埠 {{portnum}} 在過去 {{min}} 分鐘內未傳輸或收到任何資料。", "hint_tx": "請按照下方步驟嘗試解決以下問題：序列埠 {{portnum}} 在過去 {{min}} 分鐘內未傳輸任何資料。", "how_to_resolve": "如何解決？", "minutes": "分鐘", "no_data_period": "無數據期間", "overrun__error_count": "溢位錯誤計數", "parity__error_count": "奇偶校驗錯誤計數", "port_duplicated": "此連接埠/觸發規則組合已配置", "port_properties": "事件類型", "resolve_title": "解決序列埠 {{portnum}} 問題", "rx": "RX 閒置", "serial_port": "序列埠", "severity": "嚴重性", "step1_break": "檢查連接的串口設備是否正常運作。", "step1_frame": "檢查 Moxa 設備上的序列介面設定（RS-232, RS-422, RS485）和通訊參數（例如：115200, 8, n, 1）是否與連接的序列設備設定相符。", "step1_ovrrun": "檢查 Moxa 設備和所連接的串列設備，串列硬體和/或軟體流量控制是否皆設定正確。", "step1_parity": "檢查 Moxa 設備和連接的序列設備，兩者的奇偶校驗和鮑率設定是否相符。", "step1_txrx": "檢查 Moxa 設備與串列終端設備之間的串列電纜是否連接正確。", "step2_ovrrun": "檢查是否需要在 Moxa 設備上啟用 FIFO。", "step2_parity": "在高度干擾地區，請檢查串列通訊系統是否受到良好的保護。", "step2_txrx": "檢查連接的串列設備是否正常運作。", "step3_parity": "檢查所連接的串列設備是否存在接線問題或硬體故障。", "still_not_working": "還是不能運作？", "title": "序列埠事件", "tx": "TX 閒置", "tx_rx": "TX 和 RX 閒置", "warning": "警告"}, "SEVERITY": {"critical": "嚴重", "information": "資訊", "title": "嚴重性", "warning": "警告"}, "sfpList": {"rx": "RX (dBm)", "temperature": "溫度 (°C)", "title": "SFP 列表", "tx": "TX (dBm)", "voltage": "電壓 (V)"}, "SITE_MANAGEMENT": {"desc": "描述", "fail": "無法更新站台", "name": "名稱", "offline": "站台斷線 ({{siteName}})", "online": "站台上線 ({{siteName}})", "success": "更新站台成功", "title": "站台管理"}, "SITE_MENU": {"management": "管理"}, "SITE_PROPERTIES": {"description": "描述", "devices": "設備 (正常 / 警告 / 嚴重)", "information": "資訊", "name": "名稱", "title": "站台屬性"}, "SNACK_BAR": {"acking": "確認中...", "copied": "已複製", "deleting": "删除中...", "saving": "儲存中..."}, "SYSLOG_SETTINGS": {"all_severity": "所有的", "authentication": "驗證", "enable_syslog_forward": "Syslog 轉送", "enable_tcp": "啟用（僅限 TCP）", "enable_udp": "啟用（僅限 UDP）", "enable_udp_tcp": "啟用（UDP 和 TCP）", "failed_to_get_syslog_forward_settings": "無法取得 syslog 轉送設定", "failed_to_get_syslog_settings": "無法取得 syslog 設定", "filter_settings_hint": "您可以輸入多個來源 IP 位址，以逗號分隔。", "forward_ip1": "遠端 IP/域名1", "forward_ip2": "遠端 IP/域名2", "port_1": "連接埠 1", "port_2": "連接埠 2", "protocol": "協定", "source_ip": "來源 IP", "syslog_built_in": "內建 Syslog 伺服器", "syslog_filter_settings": "Syslog 篩選", "syslog_forwarding": "Syslog 轉送", "syslog_server_settings": "Syslog 伺服器設定", "tcp": "TCP", "tcp_port": "TCP 連接埠", "title": "Syslog 設定", "tls_cert": "TLS + 憑證", "tls_only": "僅 TLS", "udp": "UDP", "udp_port": "UDP 連接埠", "update_failed": "更新設定失敗", "update_success": "設定更新成功"}, "SYSLOG_VIEWER": {"device_ip": "IP 地址", "facility": "程序", "filter_syslog_event": "鍵入以篩選 syslog 事件", "ip_error": "請輸入有效的IP地址", "message": "訊息", "priority": {"equals": "等於", "high_than": "大於等於", "lower_than": "小於等於", "title": "级别"}, "severity": {"alert": "警告", "critical": "危急", "debug": "除錯", "emergency": "緊急事件", "error": "錯誤", "information": "資訊", "notice": "注意", "title": "級別", "warning": "警告"}, "site_name": "站台名稱", "timestamp": "時間", "title": "Syslog 瀏覽器"}, "SYSTEM": {"request_timeout_title": "請求逾時", "trigger_disconnected_desc1": "與 MXview One 伺服器的連接中斷", "trigger_disconnected_desc2": "5 秒後開始重新連線...", "unauthorized_desc": "因為認證無效而拒絕存取", "unauthorized_title": "未經授權"}, "TABLE": {"add": "新增", "adjustable_columns": "可調整欄位", "compare": "比較", "delete": "刪除", "edit": "編輯", "edit_columns": "編輯欄位", "enable": "啟用", "export": "匯出", "exporting": "匯出中...", "filter": "篩選", "import": "匯入", "limit_count": "最大", "list_collaspe": "合起", "list_expand": "展開以獲取更多資訊", "locate": "定位", "no_data": "無資料顯示", "not_support": "不支援此設備版本", "save": "儲存", "search": "搜索", "selected_count": "已選取", "show_log": "顯示記錄", "sync": "同步", "total": "總數", "waiting_data": "等待資料中"}, "TOPOLOGY": {"add_tag": "新增標籤...", "add_tag_fail": "無法新增標籤", "add_tag_success": "成功新增標籤", "choose_goose_publisher": "選擇一個GOOSE發佈者", "colored_link": "按信噪比著色的連線", "delete_tag_failed": "無法刪除標籤", "delete_tag_success": "成功刪除標籤", "device_not_found": "找不到設備", "display_opt": "顯示選項", "dynamic_wireless_client_position": "動態用戶端位置", "dynamic_wireless_client_position_desc": "啟用此選項將顯示每個AP周圍的每個用戶端", "editTagTooltip": "按 Enter 儲存。\n按 ESC 取消。", "goose": "GOOSE", "goose_publisher": "GOOSE發佈者", "goose_tampered": "GOOSE 被篡改", "goose_timeout": "GOOSE 超時", "grouping_failed": "組成群組失败。", "grouping_success": "組成群組成功。", "legend": "圖例", "new_tag": "新標籤", "no_subscriber": "沒有訂閱者", "prp_hsr": "PRP/HSR", "prp_hsr_tags": "PRP/HSR 標籤", "publisher": "發佈者", "publisher_hint": "GOOSE 控制塊名稱\nAPPID / 位址", "search_topology": "搜索拓撲", "set_tag_fail": "無法設置標籤", "set_tag_success": "成功設置標籤", "show_all_wireless_clients": "顯示無線用戶端", "site_management_not_supported": "漫遊播放僅支持單個站台檢視", "subscriber": "訂閱者", "subscriber_hint": "IED 名稱 / GOOSE 控制塊名稱", "tag": "標籤", "traffic_view": "流量負載(%)", "ungrouping_failed": "取消群組失敗。", "ungrouping_success": "取消群組成功。", "wireless_display_opt": "無線顯示選項", "zoom_in": "放大", "zoom_out": "縮小", "zoom_to_actual_size": "縮放到實際尺寸", "zoom_to_fit": "縮放到最適大小"}, "TRAP_CONFIGURATION": {"apply_fail": "無法設置Trap Server配置", "apply_success": "成功設置Trap Server配置", "community_name1": "Community Name 1", "community_name2": "Community Name 2", "destination_ip1": "目的 IP1", "destination_ip2": "目的 IP2", "title": "Trap Server"}, "UNIT": {"dB": "dB", "dBi": "dBi", "dBm": "dBm", "hours": "小時", "mb": "Mb", "mbps": "Mbps", "meter": "公尺", "min": "分鐘", "sec": "秒", "times": "次數"}, "UPGRADE_FIRMWARE": {"file_type_error": "韌體檔必須採用 .rom、.tar 和 .gz 檔案格式。", "upgrade_firmware_fail": "韌體升級失敗", "upgrade_firmware_success": "韌體升級成功", "upgrading": "上傳韌體檔案，此作業可能需要幾分鐘，不要斷電或中斷網路，請稍等片刻"}, "validators": {"duplicateEmail": "有重複的電子郵件", "excludeLastPassword": "新密碼不能與上次密碼相同", "excludeUserName": "不能包含使用者名稱", "invalid": "無效字元", "invalid_date": "無效日期", "invalid_format_allow_space": "字串中最多允許有 {{count}} 個空格", "invalidEmail": "無效的電子郵件", "invalidIpAddress": "IP 位址無效", "invalidIpAddressOrDomainName": "IP 位址或網域名稱無效", "invalidLocation": "允許特殊字元 (-_@!#$%^&*().,/)", "invalidMacAddress": "MAC 位址無效", "invalidMacAddressAllZero": "MAC 位址 00:00:00:00:00:00 已保留", "invalidSeverity": "無效嚴重性", "ipRangeError": "結束IP 位址需要大於開始 IP 位址", "isExist": "已經存在", "isExistOrUsedByOtherUser": "已存在或已被其他使用者使用", "maxReceiverSize": "最大接收者數量為 {{num}}。", "needDigit": "必須包含至少一位數字 (0 - 9)", "needGreaterThan": "{{ largeItem }} 需要大於 {{smallItem }}", "needLowerCase": "必須包含至少一個小寫字元 (a - z)", "needSpecialCharacter": "必須包含至少一個特殊字元 (~!@#$%^&amp;*_-+=`|\\(){}[]:;”&#39;&lt;&gt;,.?/)", "needUpperCase": "必須包含至少一個大寫字元 (A - Z)", "notMeetPolicy": "不符合密碼策略要求", "portRangeError": "結束連接埠需要大於開始連接埠", "pwdNotMatch": "密碼不相符", "range": "無效範圍 ({{ min }} ~ {{ max }})", "required": "必填", "requireMaxLength": "長度不得超過 {{ number }} 個字元", "requireMinLength": "長度必須至少為 {{ number }} 個字元"}, "Validators": {"require_range_between": "{{ rangeBegin }} - {{ rangeEnd }}"}, "VLAN_TABLE": {"access_ports": "Access 埠", "device_ip": "設備 IP", "empty": "空", "export_csv": "匯出 CSV 檔", "filter_vlan": "鍵入以篩選 VLAN", "hybrid_ports": "Hybrid 埠", "location": "位置", "management_vlan": "管理網路 ID", "model": "型號", "no": "非", "site_name": "站台名稱", "title": "VLAN", "trunk_ports": "Trunk 埠", "vlan_id": "VLAN ID", "yes": "是"}, "wirelessPlayback": {"decreaseSpeed": "降低速度", "increaseSpeed": "加快速度", "noData": "在所選擇的日期範圍內沒有數據", "range": "範圍", "startTime": "開始時間", "timeRange": "時間區間"}}