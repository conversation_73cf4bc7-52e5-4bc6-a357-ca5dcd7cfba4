{"ABOUT": {"debug_log": "Journaux de débogage", "debug_log_desc": "Cliquez sur le bouton Télécharger pour générer et télécharger les journaux de débogage. Envoyez le fichier journal téléchargé par le biais du canal de support MOXA pour une analyse plus approfondie.", "desc": "Copyright Moxa, Inc. Tous droits réservés.", "eula": "View the end user license agreement (EULA)", "gateway": "Version de la passerelle", "title": "À propos de", "web": "Version Web"}, "ACCOUNT_MANAGEMENT": {"access_site": "Sites accessibles", "add_account_dialog": "Ajouter un compte d'utilisateur", "add_user_fail": "Impossible de créer le compte", "add_user_success": "Un compte est créé", "admin": "Superviseur", "all_user": "<PERSON><PERSON> utilisateur", "authority": "Autorité", "change_password": "Modifier le mot de passe", "delete_user_fail": "Impossible de supprimer le compte", "delete_user_success": "Le compte d'utilisateur est supprimé", "demo_user": "Utilisa<PERSON>ur <PERSON>", "filter_account": "Taper pour filtrer le compte d'utilisateur", "modify_account_dialog": "Modifier le compte d'utilisateur", "new_password": "nouveau mot de passe", "old_password": "Ancien mot de passe", "password": "Mot de passe", "password_policy_mismatch": "Le mot de passe n'est pas conforme à la stratégie de mot de passe", "superuser": "Administrateur", "title": "Gestion de compte", "ui_profile": "Profil IU", "update_user_fail": "Impossible de mettre à jour le compte", "update_user_success": "Le compte d'utilisateur est mis à jour", "user": "Utilisa<PERSON>ur", "user_account": "User Account", "user_exist": "L'utilisateur existe déjà", "username": "Nom d'utilisateur"}, "account_password": {"1st_email": "1er destinataire de l'e-mail", "2nd_email": "2ème destinataire de l'e-mail", "account_audit": "Audit de compte", "account_info": "Information sur le compte", "account_info_content": "Cliquez sur le bouton ' Actualiser ' pour récupérer les informations de compte pour tous les appareils. <PERSON>la peut prendre un certain temps.", "account_management": "Gestion de compte", "account_password_management_automation": "Automatisation de la gestion des comptes et des mots de passe", "account_status_audit": "Vérification des comptes", "account_status_baseline": "Référence des comptes", "account_status_baseline_content": "Cette opération créera une nouvelle ligne de base et écrasera celle existante.", "accounts": "<PERSON><PERSON><PERSON>", "activate": "Activer", "add_account": "Ajouter un compte", "add_temporary_account": "Ajouter un compte temporaire", "added_account": "<PERSON><PERSON><PERSON>", "admin": "Admin", "apply_accounts": "Comptes applicables", "audit_automation": "Automatisation des audits", "authority": "Autorité", "baseline_account": "Comptes de référence", "baseline_auto_check_failed": "Échec de la création de la référence", "change_admin_name": "Modifier le nom « Admin » par défaut", "change_admin_name_content": "MXview One utilisera les informations d'identification du compte mises à jour pour accéder au(x) appareil(s) suivant(s). Les autres appareils ne sont pas concernés.", "change_admin_name_contents": "MXview One utilisera les informations d'identification du compte mises à jour pour accéder au(x) appareil(s) suivant(s). Les autres appareils ne sont pas concernés.", "check_default_account_failed": "Échec de la vérification du compte par défaut.", "check_password_length": "Assurez-vous que la longueur du mot de passe soit comprise dans la longueur maximale autorisée sur le(s) périphérique(s).", "compability": "Compatible", "create_baseline_failed": "Échec de la création de la référence", "create_baseline_failed_no_devices": "Échec de la création de la ligne de base. Aucun appareil détecté.", "days": "Jours", "default_account": "Nom d'utilisateur/mot de passe par défaut", "default_password_audit": "Audit de mot de passe par défaut", "default_password_audit_info": "La recherche des informations d'identification du compte par défaut peut prendre un certain temps et laissera l'interface temporairement indisponible. Es-tu sur de vouloir continuer?", "delete_account": "Supprimer le compte", "delete_temporary_account": "Supprimer un compte temporaire", "delete_temporary_account_info": "Êtes-vous sûr de vouloir supprimer ce compte temporaire ?", "delete_temporary_accounts_info": "Êtes-vous sûr de vouloir supprimer ces comptes temporaires ?", "deleted_account": "Comptes supprimés", "device_alias": "Alias du dispositif", "device_ip": "Adresse IP du dispositif", "edit_account": "Modifier le compte", "edit_temporary_account": "Modifier un compte temporaire", "email_server_configuration": "Configuration du serveur de messagerie", "email_server_hint": "Si vous n'avez pas reçu l'e-mail contenant le code de vérification, vérifiez votre dossier spam ou vérifiez le", "email_verified": "L'adresse e-mail a été vérifiée.", "end_date": "Valable jusque", "end_time": "<PERSON>ure De Fin", "fatiled_to_audit_account_due": "Échec de l'audit du compte. Impossible de récupérer les informations du compte de l'appareil à partir de {{ ip }}.", "fatiled_to_create_baseline_due": "Échec de la création de la ligne de base. Impossible de récupérer les informations du compte de l'appareil à partir de {{ ip }}.", "get_baseline_failed": "Impossible d'obtenir la ligne de base", "get_device_account_failed": "Échec de l'interrogation des informations sur le compte de l'appareil. D'autres demandes sont en cours. Réessayez plus tard.", "incorrect_verification_code": "Code de vérification incorrect.", "last_audit_time": "Dernier audit", "last_execution_time": "Dernière exécution", "max_char": "Maximum de {{num}} caractères", "model": "<PERSON><PERSON><PERSON><PERSON>", "mxa_char": "Maximum de {{num}} caractères", "new_password": "Nouveau mot de passe", "new_username": "Nouveau nom d'utilisateur", "next_audit_time": "Prochain audit", "next_schdeule_start_time": "Suivant prévu pour", "no_data": "s.o.", "not_activate": "Désactiver", "not_started": "En attendant", "not_started_hint": "Cette tâche n'a pas été exécutée en raison d'un arrêt du système. Cliquez sur le bouton « Régénérer le mot de passe » pour exécuter manuellement la tâche.", "now": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "operation": "Action", "password": "Mot de passe", "password_automation": "Automatisation des mots de passe", "password_automation_schedule": "Calendrier d'automatisation des mots de passe", "password_automation_settings": "Assistant d'automatisation des mots de passe", "password_email_receiver": "Mot de passe Destinataire de l'e-mail", "password_regenerated_info": "MXview One utilisera les paramètres suivants pour générer un nouveau mot de passe pour chaque appareil.", "password_resend_info": "Êtes-vous sûr de vouloir renvoyer le mot de passe de l'appareil au(x) destinataire(s) suivant(s) ?", "password_resend_result_info": "MXview One a envoyé le fichier de compte et de mot de passe de l'appareil à la ou aux adresses e-mail suivantes :", "password_strength": "Fiabilité du mot de passe", "random_password_complexity": "Définir la complexité du mot de passe", "random_password_length": "Longueur du mot de passe aléatoire", "random_password_length_info": "MXview One générera un mot de passe aléatoire pour les appareils sélectionnés.", "randomized_password_failed": "Échec (Le compte de l'appareil ne correspond pas au compte de base de données.)", "refresh_hint": "<PERSON><PERSON><PERSON><PERSON> appuyer sur le bouton ' Actualiser ' pour récupérer les comptes d&#39;appareil avant de poursuivre cette action.", "regenerate_password": "Ré<PERSON>n<PERSON>rer le mot de passe", "resend_password_email": "Renvoyer l'e-mail du mot de passe", "retrieve_data": "Récupération des données", "retry_failed_devices": "Réessayer les appareils en panne", "schedule": "Prévu", "schedule_interval": "Intervalle", "script_error": "Ce champ ne peut contenir aucun des caractères suivants : #%&amp;*{}|:\\&quot;&lt;&gt;?/\\\\", "select_device": "Sélectionnez les appareils", "select_device_random_password": "Sélectionnez le ou les appareils pour lesquels générer un mot de passe aléatoire.", "send_password_email": "Envoyer un e-mail de mot de passe", "send_password_email_success": "MXview One a envoyé le compte de l'appareil, le mot de passe et le résultat de l'exécution au(x) destinataire(s) de courrier électronique suivant :", "set_password_to_device": "Appliquer le mot de passe aux appareils", "set_schedule_interval_failed": "Échec de la définition de l'intervalle de planification.", "set_schedule_interval_success": "Intervalle de planification défini avec succès.", "start_date": "Actif à partir de", "start_over": "Recommencer", "start_time": "<PERSON><PERSON> d<PERSON>", "start_wizard": "<PERSON><PERSON><PERSON><PERSON> l'assistant", "status": {"cli_session_timeout": "Expiration de la session CLI", "failed": "<PERSON><PERSON><PERSON>", "failed_account_exist": "Échec (Ce compte existe déjà)", "failed_account_password_incorrect": "Échec (compte ou mot de passe incorrect)", "failed_limit_reached": "Échec (limite de compte d'appareil atteinte)", "failed_not_support_role": "Échec (appareil non pris en charge)", "failed_other_request": "Échec (d'autres demandes sont en cours)", "failed_retrieve_account_info": "Échec (impossible de récupérer les informations du compte)", "finished": "<PERSON><PERSON><PERSON><PERSON>", "in_progress": "En cours ...", "waiting": "En attendant"}, "supervisor": "Superviseur", "temporary_account": "Comptes temporaires", "test_eamil_recipient": "Test du destinataire de l'e-mail", "title": "Comptes et mots de passe", "unable_to_get_accounts": "Impossible de récupérer les comptes", "user": "Utilisa<PERSON>ur", "username": "Nom d'utilisateur", "verififcation_code": "Code de vérification", "verift_title": "Vérifiez votre compte MXview One", "verify_code_expiration": "Le code de vérification expire dans", "verify_email_not_allowed": "<PERSON><PERSON><PERSON><PERSON> attendre au moins une minute avant d'envoyer un autre e-mail.", "verify_email_password_receiver": "Vérifier le(s) compte(s) et le mot de passe du(des) destinataire(s) de l'e-mail", "verify_email_receiver": "Vérifier le destinataire du courrier électronique", "verify_email_server_failed": "La configuration du serveur de messagerie n'est pas valide. Impossible d'envoyer des e-mails.", "verify_user_failed": "Nom d'utilisateur/mot de passe non valide"}, "ADD_DEVICE": {"add_device_fail": "Impossible d'ajouter le dispositif", "add_device_fail_error_message": {"device_has_existed": "Un appareil avec cette IP existe déjà", "license_limitation_reached": "Limite de licence atteinte", "model_not_exist": "Le modèle n'existe pas."}, "add_device_success": "Un dispositif a été ajouté", "assign_group": "Attribuer au groupe", "assign_model": "Attribuer un modèle", "authentication": "Authentification", "auto_detect_model": "Détection automatique", "data_encryption": "Chiffrement des données", "encryption_password": "Mot de passe du chiffrement", "encryption_type": "Protocole du chiffrement", "field_required": "Ce champ est obligatoire", "snmp_setting": "Paramètre SNMP", "snmp_version": "Version SNMP", "title": "Ajouter un dispositif"}, "ADD_LINK": {"alias": "<PERSON><PERSON>", "device": "Dispositif", "fail": "Impossible d'ajouter la liaison", "from": "De", "ip_address": "Adresse IP", "model": "<PERSON><PERSON><PERSON><PERSON>", "only_number": "Autorise uniquement les chiffres", "port": "Port", "success": "Une liaison a été ajoutée", "title": "Ajouter une liaison", "to": "À"}, "API_MANAGEMENT": {"access_count": "Nombre d'accès", "add_failed": "Échec de l'ajout d'une clé API", "add_success": "Ajout d'une clé API", "add_title": "Ajouter un nouveau jeton", "api_key": "Clé API", "application_name": "Nom de l'application", "create_time": "Heure de création", "delete_failed": "Échec de la suppression de la clé API", "delete_success": "La clé API est supprimée", "edit_title": "Modifier le jeton", "filter": "Taper pour filtrer les clés API", "regenerate_api_key": "Régénérer la clé API", "regenerate_failed": "Échec de la régénération de la clé API", "regenerate_success": "La clé API est régénérée", "title": "Gestion des clés API", "update_failed": "Échec de la mise à jour de la clé API", "update_success": "Clé API mise à jour"}, "ASSIGN_MODEL": {"apply_to_all": "Appliquer cette icône à tous les dispositifs ayant le même modèle", "assign_model_fail": "Impossible d'attribuer le modèle", "assign_model_success": "Le modèle du dispositif est mis à jour", "ip_address": "Adresse IP", "model": "<PERSON><PERSON><PERSON><PERSON>", "model_icon": "Icône du modèle", "select_model": "Sélectionner un modèle"}, "AVAILABILITY_REPORT": {"alias": "Alias du dispositif", "average": "Disponibilité moyenne", "days": "Jours", "end_date": "Date de fin", "filter": "Taper pour filtrer le rapport de disponibilité", "from_date": "Date de début", "query_date": "Date de la requête", "report_generate_day": "Date de génération du rapport:", "site_name": "Nom du site", "title": "Rapport de disponibilité", "worst": "<PERSON>re disponibilité"}, "BASIC_INFORMATION": {"apply_fail": "Impossible de définir les informations système du dispositif", "apply_success": "Les informations système du dispositif sont mises à jour", "contact": "Contact", "location": "Emplacement", "model": "<PERSON><PERSON><PERSON><PERSON>", "name": "Nom", "title": "Informations de base"}, "BUTTON": {"add": "Ajouter", "add_to_scheduler": "Ajouter au planificateur", "agree": "Accepter", "apply": "Appliquer", "audit": "Audit", "back": "Retour", "cancel": "Annuler", "change": "Changement", "check": "Vérifier", "checkNow": "<PERSON><PERSON><PERSON><PERSON>ant", "clear": "<PERSON><PERSON><PERSON><PERSON>", "clear_fail_record": "Effacer les enregistrements des défauts", "close": "<PERSON><PERSON><PERSON>", "compare": "Comparer", "confirm": "Confirmer", "connected": "Connecté", "continue": "<PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "deactivate": "Désactiver", "decline": "D<PERSON><PERSON>lin", "delete": "<PERSON><PERSON><PERSON><PERSON>", "disable_new_version_notifications": "Désactiver les notifications de nouvelle version", "disconnected": "Débranché", "download": "Télécharger", "download_all_logs": "Télécharger tous les journaux", "download_filter_logs": "Télécharger les journaux de filtrage", "edit": "Modifier", "enable_new_version_notifications": "Activer les notifications de nouvelle version", "execute": "<PERSON><PERSON><PERSON>", "faqs": "FAQ", "got_it": "J'ai compris", "ignore": "<PERSON><PERSON><PERSON>", "leave": "Pars", "next": "Suivant", "ok": "OK", "query": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reboot": "Redémarrage", "redirect": "RÉORIENTER", "refresh": "Actualiser", "regenerate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resend": "renvoyer", "reset": "Réinitialiser", "retry_failed_devices": "Réessayer les appareils en panne", "run": "<PERSON><PERSON><PERSON>", "save": "Enregistrer", "scan": "Analyse", "search": "Recherche", "security_patch_available": "Correctifs de sécurité disponibles", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "select_firmware_version": "Sélectionnez la version du micrologiciel", "send": "Envoyer", "send_test_eamil": "Envoyer un e-mail de test", "set": "Définir", "upgrade": "Mettre a niveau", "upgrade_firmware": "Mise à niveau du firmware", "upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "verify": "Vérifier", "verify_email": "Vérifier les courriels"}, "cli_object_database": {"add_cli_fail": "Impossible de créer un script CLI", "add_cli_object": "Ajouter un script CLI", "add_cli_success": "Nouveau script CLI créé avec succès", "before_date": "Date", "before_time": "Temps", "cli_objects": "Scripts CLI", "cli_script": "Script CLI", "delete_all_execution_results": "Supprimer tous les résultats d'exécution du script CLI", "delete_all_execution_results_before_time": "Supprimer les résultats de l'exécution du script avant", "delete_all_execution_results_before_time_desc": "Êtes-vous sûr de vouloir supprimer tous les résultats de l'exécution du script avant le {{param}} ?", "delete_all_execution_results_desc": "Êtes-vous sûr de vouloir supprimer tous les résultats de l'exécution du script CLI ?", "delete_cli_fail": "Impossible de supprimer le script CLI", "delete_cli_object": "Supprimer le script CLI", "delete_cli_object_desc": "Êtes-vous sûr de vouloir supprimer ce script CLI ?", "delete_cli_object_disabled": "Ce script ne peut pas être supprimé car il est lié à une tâche planifiée. Pour supprimer ce script, modifiez ou supprimez d'abord la tâche planifiée sur la page Administration > Planificateur de maintenance.", "delete_cli_objects_desc": "Êtes-vous sûr de vouloir supprimer ces scripts CLI ?", "delete_cli_success": "Script CLI supprimé avec succès", "delete_execution_result_fail": "Impossible de supprimer les résultats de l'exécution", "delete_execution_result_success": "Résultats de l'exécution du script supprimés avec succès", "delete_results_before_time": "Supprimer les résultats de l'exécution du script CLI", "description": "Description", "download_all_execution_results": "Télécharger tous les résultats d'exécution", "download_all_execution_results_fail": "Impossible de télécharger les résultats de l'exécution", "download_execution_results_failed_hint": "Aucun résultat d'exécution disponible au téléchargement.", "edit_cli_fail": "Impossible de mettre à jour le script CLI", "edit_cli_object": "Modifier le script CLI", "edit_cli_success": "Script CLI mis à jour avec succès", "execution_results": "Résultats d'exécution", "get_cli_fail": "Impossible de récupérer les scripts CLI", "linked_scheduled_task": "Tâches planifiées liées", "linked_script_automation": "Automatisations de scripts liés", "name": "Nom", "non_ascii": "Seuls les caractères ASCII sont acceptés.", "scheduled_execution_cli_object_desc": "Vous pouvez créer des tâches planifiées pour exécuter des scripts CLI à une date et une heure spécifiées à partir de la page Administration > Planificateur de maintenance.", "scheduled_execution_cli_object_info": "Script planifié", "scheduled_task": "Tâche planifiée", "title": "Scripts CLI enregistrés"}, "COMBO_BOX": {"disabled": "Désactivé", "enabled": "Activé", "export_all_event_csv": "Exporter tous les événements au format CSV", "export_all_syslog_csv": "Exporter tout syslog au format CSV", "export_csv": "Exporter le fichier CSV", "export_pdf": "Exporter le fichier PDF", "sequential": "ordre strict", "smart_concurrent": "s<PERSON><PERSON> intelligente"}, "COMMAND_BAR": {"hide_automation_button": "Masquer les boutons d'automatisation", "hide_button_panel": "Masquer le panneau de boutons", "hide_detail": "Masquer les détails", "hide_group": "Masquer les groupes", "list_view": "Affichage de la liste", "show_automation_button": "Afficher les boutons d'automatisation", "show_button_panel": "Aff<PERSON>r le panneau de boutons", "show_detail": "Aff<PERSON>r les détails", "show_group": "Afficher les groupes", "topology_view": "Affichage de la topologie"}, "CONFIG_CENTER": {"alias_name": "Nom de l'alias", "backup_config": "Configuration de la sauvegarde", "backup_message": "MXview One archivera ces fichiers de configuration", "backup_tab": "<PERSON><PERSON><PERSON><PERSON>", "backup_tab_hint": "Allez à l'onglet Sauvegarde et exportez d'abord la configuration du dispositif", "compare_config_basement": "Comparer les éléments de base : {{compareConfigFileName}}", "compare_config_dialog_title": "Comparer les <PERSON>", "compare_tab": "Enregistrements", "compare_target": "Comparer la cible", "configuration_file": "Fichier de configuration", "configuration_name": "Nom de la configuration", "create_time": "Nom de la création", "delete_config_dialog_title": "Supprimer la configuration", "delete_config_failed": "Échec de la suppression de la configuration du dispositif", "delete_config_success": "La configuration du dispositif est supprimée", "delete_config_warning_message": "Voulez-vous vraiment supprimer la configuration sélectionnée ?", "device_list": "Liste des dispositifs", "export_failed": "Échec de l’exportation", "export_success": "Exportation réussie", "from_date": "Date de début", "group_name": "Groupe", "ip_address": "Adresse IP", "last_check_time": "Heure de la dernière vérification", "local_file": "Fichier local", "restore_config": "Configuration de la restauration", "restore_device": "Restaurer le dispositif - {{selectedDeviceIP}}", "restore_tab": "<PERSON><PERSON><PERSON>", "site_name": "Site", "time": "<PERSON><PERSON>", "title": "Centre de configuration des appareils", "to_date": "Date de fin"}, "DASHBOARD": {"adpDestIp": "Top 5 des événements de politique ADP par adresse IP de destination", "adpSrcIp": "Top 5 des événements liés à la politique ADP par IP source", "ap_devices": "Périphériques AP", "ap_traffic_load": "Charge de trafic AP", "baseline": "Basique/Ligne de base générale", "client_devices": "Périphériques clients", "critial_devices": "Dispositifs critiques", "device_availability": "Disponibilité du dispositif", "device_availability_intro": "Disponibilité = (temps de fonctionnement / (temps de fonctionnement+temps d'arrêt)) * 100", "device_summary": "Résumé des dispositifs", "devices": "Dispositifs", "disk_space_utilization": "Utilisation de l'espace disque", "dpiDestIp": "Top 5 des événements de politique de filtrage de protocole par IP de destination", "dpiSrcIp": "Top 5 des événements de politique de filtrage de protocole par IP source", "event_highlight": "Points essentiels des événements", "healthy_devices": "Dispositifs sains", "icmp_unreachable": "ICMP inaccessible", "iec_level_1": "<PERSON><PERSON><PERSON>", "iec_level_2": "<PERSON><PERSON><PERSON>", "ipsDestIp": "Top 5 des événements liés à la politique IPS par adresse IP de destination", "ipsSrcIp": "Top 5 des événements liés à la politique IPS par IP source", "l3DestIp": "Top 5 des événements de politique de couche 3 à 7 par adresse IP de destination", "l3SrcIp": "Top 5 des événements de politique de couche 3 à 7 par IP source", "last_1_day": "1 jour", "last_1_hours": "<PERSON><PERSON><PERSON> he<PERSON>", "last_1_weeks": "1 semaine", "last_2_weeks": "2 dernières semaines", "last_24_hours": "24 dernières heures", "last_3_days": "3 derniers jours", "last_3_hours": "3 dernières heures", "last_30_days": "30 derniers jours", "last_30_minutes": "30 dernières minutes", "last_7_days": "7 derniers jours", "last_update": "Dernière mise à jour :", "link_down": "<PERSON>ne <PERSON>", "link_up": "Établissement d'une liaison", "linkButton": "Afficher le journal des événements", "not_pass": "Échec", "now": "A partir de maintenant", "open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pass": "Réussite", "reboot_times": "Interception démarrage à froid/à chaud", "refresh_all": "Tout actualiser", "security_level": "Niveau de sécurité", "security_summary": "Résumé de la sécurité", "selecting_visible_item": "Sélection des éléments visibles", "set_default_tab": "Définir comme onglet par défaut", "tabs": {"cybersecurity": "Cybersécurité", "general": "Général", "wireless": "Sans fil"}, "title": "Tableau de bord", "total_availability": "Device availability is below {{param}}%", "total_devices": "Nombre total de dispositifs", "unknown": "Inconnu", "view_network_topology": "Afficher la topologie du réseau", "warning_devices": "Dispositifs avec avertissement", "wireless_device_summary": "Résumé des périphériques sans fil"}, "DATABASE_BACKUP": {"database_name": "Nom", "fail": "Échec de la sauvegarde de la base de données", "success": "La base de données est sauvegardée à {{param1}}", "title": "Sauvegarde de la base de données"}, "DEVICE_LOCATOR": {"stop_trigger_locator": "<PERSON><PERSON><PERSON><PERSON>", "title": "Localisateur de dispositif", "trigger_locator": "<PERSON><PERSON><PERSON><PERSON>", "trigger_locator_fail": "Échec du déclenchement du localisateur de dispositif", "trigger_locator_off": "Le localisateur de dispositif est désactivé", "trigger_locator_on": "Le localisateur de dispositif est activé"}, "device_management": {"built_in_command_execution_process_is_running": "Impossible d'envoyer la commande. Une autre commande est en cours d'exécution. Réessayez plus tard.", "execute_fail": "Échec de l'exécution", "limited_support": "Le support est limité, ve<PERSON><PERSON>z consulter le manuel d'utilisation.", "select_device": "Sélectionner un appareil", "select_operation": "Sélectionner une opération"}, "DEVICE_PANEL": {"panel_status": "État du panneau", "panel_zoom_size": "Taille d'agrandissement du panneau du dispositif", "port": "Port"}, "DEVICE_POLLING_SETTING": {"consecutive_icmp_fail_trigger": "Échec consécutif au déclenchement d'un événement ICMP inaccessible", "consecutive_snmp_fail_trigger": "Échec consécutif au déclenchement d'un événement SNMP inaccessible", "icmp_polling_interval": "Intervalle d'interrogation ICMP", "snmp_polling_interval": "Intervalle d'interrogation SNMP", "title": "Paramètres d'interrogation"}, "DEVICE_PROPERTIES": {"basic_property": {"alias": "<PERSON><PERSON>", "availability": "Disponibilité", "bios_version": "Version du BIOS/chargeur de démarrage", "cpu_loading": "Chargement de l'UC (%)", "cpu_loading_30_seconds": "Chargement de l'UC 30 secondes (%)", "cpu_loading_300_seconds": "Chargement de l'UC 300 secondes (%)", "cpu_loading_5_seconds": "Chargement de l'UC 5 secondes (%)", "cpu_utilization_300_seconds": "Utilisation du processeur au cours des 300 dernières secondes (%)", "cpu_utilization_60_seconds": "Utilisation du processeur au cours des 60 dernières secondes (%)", "cpu_utilization_900_seconds": "Utilisation du processeur au cours des 900 dernières secondes (%)", "disk_utilization_unit": "Utilisation du disque (%)", "fw_system_version": "Version du micrologiciel/de l'image système", "fw_version": "Version du micrologiciel/de l'image système", "mac_address": "Adresse MAC", "memory_usage": "Utilisation mémoire", "memory_usage_unit": "Utilisation mémoire (%)", "model_name": "Nom du modèle", "os_type": "Système opérateur", "power_comsumption": "Consommation électrique (W)", "serial_number": "Numéro de série", "system_contact": "Contact du système", "system_description": "Description du système", "system_location": "Emplacement du système", "system_name": "Nom du système", "system_object_id": "Sysobjectid", "system_up_time": "System Up Time", "title": "Propriétés de base du dispositif"}, "cellular": {"cellular_carrier": "Cellular Carrier", "cellular_ip_address": "Cellular IP Address", "cellular_mode": "Cellular Mode", "cellular_signal": "Cellular Signal", "imei": "IMEI", "imsi": "IMSI", "title": "Cellular Information"}, "goose_table": {"app_id": "app_id", "gocb_name": "Nom du GoCB", "goose_address": "Adresse GOOSE", "ied_name": "Nom de l'IED", "port": "Port d'entrée", "rx_counter": "Compteur RX", "status": "Statut", "tampered_port": "Port trafiqué", "tampered_port_status": "Sabotage sur le port {{ port }}}.", "title": "Vérification GOOSE", "type": "Type", "vid": "VID"}, "ipsec": {"l2tp_status": "État L2TP", "local_gateway": "Passerelle locale", "local_subnet": "Sous-réseau local", "name": "Nom de l'IPSec", "phase_1_status": "État de Phase 1", "phase_2_status": "État de Phase 2", "remote_gateway": "Passerelle à distance", "remote_subnet": "Sous-réseau à distance", "title": "État de l'IPsec"}, "link": {"from": "<PERSON><PERSON><PERSON>", "port": "Port", "sfpTitle": "Informations relatives au SFP", "speed": "Vitesse de liaison", "title": "Informations sur la liaison", "to": "Vers"}, "management_interfaces": {"http_port": "Port HTTP", "https_port": "Port HTTPS", "profinet_enabled": "PROFINET activé", "ssh_port": "Port SSH", "telnet_port": "Port Telnet", "title": "Interfaces de gestion"}, "mms": {"title": "Propriétés du MMS"}, "modbus_device_property": {"model": "<PERSON><PERSON><PERSON><PERSON>", "revision": "Révision", "title": "Propriétés de l'appareil Modbus", "vendor": "Fournisseur"}, "not_selected": "Sélectionnez un module pour afficher les détails de l'appareil", "other_device_properties": {"active_redundancy_protocol": "Protocole de redondance actif", "auto_ip_config": "Config. de l'IP automatique", "default_gateway": "Passerelle par défaut", "dns_1_ip_address": "Adresse IP du DNS 1", "dns_2_ip_address": "Adresse IP du DNS 2", "ip_ad_ent_addr": "ipAdEntAddr", "ip_address": "Adresse IP (mib)", "mac_address": "Adresse MAC (mib)", "model_name": "Nom du modèle", "monitor_current_mode": "Surveiller le mode actuel", "monitor_down_stream_rate": "Surveiller le débit en aval", "monitor_snr": "Surveiller le rapport signal/bruit", "monitor_up_stream_rate": "Surveiller le débit en amont", "netmask": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Autres propriétés du dispositif"}, "port": {"if_number": "ifNumber", "interface": "interface", "number_of_ports": "Nombre de ports", "poe_port_class": "Classe du port PoE", "poe_power_legacy_pd_detect": "Détection du PD conventionnel d'alimentation PoE", "poe_power_output_mode": "Mode de sortie de puissance PoE", "title": "Informations sur le port"}, "power": {"power_1_status": "État d'alimentation 1", "power_2_status": "État d'alimentation 2", "title": "État d'alimentation"}, "redundancy": {"active_redundancy_protocol": "Protocole de redondance actif", "dh": "Dual Homing", "iec_624393_redundancy_protocol": "Protocole de redondance IEC 62439-3", "rstp": "RSTP", "tc": "Turbo Chain", "title": "Redondance", "trv2": "Turbo Ring V2"}, "selected_module": "<PERSON><PERSON><PERSON>", "snmp": {"1st_trap_community": "Communauté du 1er trap", "2nd_trap_server_community": "Communauté du 2e trap", "inform_enabled": "Notification activée", "inform_retries": "Réessayages de la notification", "inform_timeout": "Temporisation de la notification", "read_community": "Lire la communauté", "title": "Informations SNMP", "trap_server_address_1": "<PERSON><PERSON><PERSON> du <PERSON>ur trap 1", "trap_server_address_2": "<PERSON><PERSON><PERSON> <PERSON> trap 2"}, "title": "Propriétés des dispositifs", "vpn": {"from_ip": "VPN depuis l'IP", "local_connection_name": "Nom de la connexion VPN locale", "remote_connection_name": "Nom de la connexion VPN à distance", "to_ip": "VPN vers l'IP"}, "wireless": {"channel_width": "Largeur de canal", "client_ip": "Client IP", "client_mac": "Client MAC", "client_RSSI": "Puissance du signal client (dBm)", "rf_type": "Type RF", "ssid_index": "Indice SSID", "title": "Information sans fil", "vap_mgmt_encryption": "Cryptage de gestion VAP", "vap_wpa_encrypt": "Cryptage VAP WPA", "vapAuthType": "Type d'authentification VAP"}}, "DEVICE_SETTING": {"advanced": "<PERSON><PERSON><PERSON>", "alias": "<PERSON><PERSON>", "alias_input_invalid": "Entrez un alias de dispositif valide", "apply_fail": "Impossible de définir les paramètres du dispositif", "apply_success": "Les paramètres du dispositif sont mis à jour", "availability_time_frame": "Intervalle de temps pour le calcul de disponibilité", "get_parameter_fail": "Impossible d'obtenir les paramètres du dispositif", "input_error_message": "La nouvelle valeur doit se situer dans une plage de", "modify_device_alias": "Modifier l'alias du dispositif", "password": "Mot de passe", "password_input_invalid": "Entrez un mot de passe valide", "polling_interval": "Intervalle d'interrogation", "polling_ip": "<PERSON><PERSON>e IP d'interrogation", "snmp_configuration": "Configuration SNMP", "snmp_port_invalid": "Veuillez saisir un port SNMP valide", "title": "Paramètre du dispositif", "use_global": "Utiliser le nom d'utilisateur et le mot de passe pour l'accès global", "use_global_device_settings": "Utiliser les paramètres globaux du dispositif", "username": "Nom d'utilisateur", "username_input_invalid": "Entrez un nom d'utilisateur valide"}, "DEVICE": {"device_properties": "Propriétés des dispositifs", "device_role": "Rôle du dispositif", "filter_device": "Taper pour filtrer les dispositifs", "filter_register_device": "Taper pour filtrer des dispositifs d'enregistrement", "na": "s.o.", "properties": {"availability": "Disponibilité", "device_alias": "Alias du dispositif", "device_ip": "Adresse IP du dispositif", "firmware_version": "Version de microprogramme", "location": "Emplacement", "mac_address": "Adresse MAC", "model_name": "Nom du modèle", "mxsec_flag": "Module complémentaire de sécurité", "severity": "Gravité"}, "registered_devices": "Inscrit", "site_name": "Nom du site", "title": "Liste des dispositifs", "unregistered_devices": "Non enregistré"}, "DeviceDashboard": {"avg_erase_count": "Avg. <PERSON><PERSON>", "change_disk_hint": "Please change your disk", "chartTitle": {"60s_cpu": "Utilisation du processeur (60 dernières secondes)", "connection": "Connection Status", "cpu": "Utilisation du CPU", "disk": "Disk Utilization", "memory": "Utilisation de la mémoire", "noiseFloor": "Noise Floor", "raid_mode": "RAID Mode", "signalStrength": "Puissance du signal", "smart": "S.M.A.R.T.​", "snr": "SNR", "traffic": "Charge de trafic"}, "connected": "Connecté", "current_status": "État actuel:", "cycle_limitation": "Cycle Limitation", "icmp_not_support": "Le périphérique ICMP ne prend pas en charge cette fonctionnalité.", "link_down_port": "Link Down Ports", "link_up_port": "Link Up Ports", "managed": "<PERSON><PERSON><PERSON>", "migrating_data": "Migrating Data", "no_raid": "No RAID", "normal": "Normal", "raid": "RAID", "rebuild": "Rebuild", "smart_hint": "(Self-Monitoring Analysis and Reporting Technology) represents device health status and lifespans information.", "unreachable": "{{ warningWording }} inaccessible. Il est possible que MXview One n'obtienne pas de données complètes du périphérique."}, "DIALOG": {"add_wifi_ssid": {"aes": "AES", "clear_all_existing_ssid": "Effacer tous les SSID existants", "eapol_version": "Version EAPOL", "encryption": "chiffrement", "open": "Open", "passphrase": "mot de passe", "personal": "Personal", "protected_management_frame": "cadre de gestion protégé", "rf_band": "Bande RF", "security": "Sécurité", "ssid": "SSID", "title": "Ajouter un SSID Wi-Fi", "tkip_aes_mixed": "Hybride TKIP/AES", "wpa_mode": "Mode WPA"}, "auto_layout": {"desc": "Voulez-vous vraiment effectuer la disposition automatique? (Cela remplacera mission actuelle)", "title": "Disposition automatique"}, "auto_topology": {"advanced": "Analyse de topologies avancée", "advanced_desc": "*Nécessite plus de temps.", "advanced_hint": "Établir des liaisons entre un dispositif ICMP et un support de dispositif soit LLDP ou Table de transfert", "fail": "Échec de l'exécution de la topologie automatique", "link_check": "Mode de vérification de liaison stricte", "link_check_hint": "Si cette option est activée, les liens entre les appareils ne seront affichés sur la topologie que si les appareils aux deux extrémités disposent des informations de l'autre appareil dans leur table LLDP.", "new_topology": "Nouvelle topologie", "new_topology_desc": "Les liaisons existantes seront supprimées", "success": "La topologie automatique est exécutée correctement", "title": "Topologie automatique", "update_topology": "Mettre à jour la topologie", "update_topology_desc": "Les liaisons existantes seront conservées lors de l'ajout de nouvelles liaisons"}, "background_dialog": {"content": "Please set a background image first.\n The background image can be a floor plan or other image that represents the coverage area.", "set_now": "Définir maintenant", "title": "Définir l'image de fond"}, "change_group": {"assign_to_group": "Attribuer au groupe", "change_group_fail": "Échec du déplacement des dispositifs vers le nouveau groupe", "change_group_success": "Les dispositifs sont déplacés vers le nouveau groupe", "current_group": "Groupe actuel", "ip": "Adresse IP", "title": "Modifier le groupe"}, "change_wifi_channel": {"channel": "canal", "channel_width": "Largeur de canal", "execute_button": "Changement", "title": "Changer de canal Wi-Fi"}, "create_group": {"assign_group": "Attribuer au groupe", "create_group_fail": "Échec de la création du groupe", "create_group_success": "Le groupe est créé", "current_group": "Groupe actuel", "empty_group_name": "V<PERSON> devez entrer un nom de groupe", "group_desc": "Description du groupe", "group_name": "Nom du groupe", "parent_group": "Groupe parent", "title": "Créer un groupe"}, "create_snapshot": {"execute_button": "c<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON> un instantané"}, "data_not_ready": {"content": "Les données de l'appareil ne sont pas encore prêtes, ve<PERSON><PERSON><PERSON> réessayer plus tard.", "title": "Veuillez réessayer plus tard"}, "delete_account": {"delete_confirm_message": "Etes-vous sûr que vous voulez supprimer ce compte?", "title": "Supprimer le compte"}, "delete_background": {"desc": "Voulez-vous vraiment supprimer l'image de l'arrière-plan ?", "failed": "Échec de la suppression de l'arrière-plan", "success": "L'arrière-plan est supprimé", "title": "Supprimer l'arrière-plan"}, "delete_custom_opc": {"delete_confirm_message": "Etes-vous sûr de vouloir supprimer cette étiquette OPC personnalisée ?", "title": "Supprimer un tag OPC personnalisé"}, "delete_device": {"delete_wireless_client_alert": "Les données historiques du ou des dispositifs clients sans fil supprimés seront également purgées et affecteront la traçabilité des fonctions répertoriées. Voulez-vous continuer ?", "delete_wireless_client_alert_title": "Confirmation de la suppression du ou des dispositifs", "desc": "Voulez-vous vraiment supprimer ce dispositif ?", "desc_multi": "Voulez-vous vraiment supprimer ces dispositifs ?", "failed": "Échec de la suppression du dispositif", "success": "Les dispositifs ont été supprimés", "title": "Supprimer le dispositif"}, "delete_group": {"desc": "Voulez-vous vraiment supprimer ce groupe ?", "desc_multi": "Voulez-vous vraiment supprimer ces groupes ?", "failed": "Échec de la suppression du groupe", "success": "Les groupes ont été supprimés", "title": "Supprimer le groupe"}, "delete_link": {"desc": "Voulez-vous vraiment supprimer cette liaison ?", "desc_multi": "Voulez-vous vraiment supprimer ces liaisons ?", "failed": "Échec de la suppression de la liaison", "success": "Les liaisons ont été supprimées", "title": "Supprimer la liaison"}, "delete_objects": {"desc": "Voulez-vous vraiment supprimer tous les objets sélectionnés ?", "failed": "Échec de la suppression des objets sélectionnés", "success": "Les objets sélectionnés ont été supprimés", "title": "Supprimer les objets"}, "delete_site": {"desc": "Voulez-vous vraiment supprimer ce site ?", "failed": "Échec de la suppression du site", "success": "Le site a été supprimé", "title": "Supprimer le site"}, "device_settings": {"fail": "Setting Link Budget Parameters (Per-device) Failed", "rx_antenna_gain": "RX Antenna Gain", "rx_cable_loss": "RX Cable Loss", "success": "Setting Link Budget Parameters (Per-device) Success", "title": "Link Budget Parameters (Per-device)", "tx_antenna_gain": "TX Antenna Gain", "tx_cable_loss": "TX Cable Loss"}, "disable_unsecured": {"execute_button": "Désactivez HTTP et Telnet", "title": "Désactivez les consoles HTTP et Telnet non sécurisées"}, "disable_unused": {"execute_button": "Désactiver les ports inutilisés", "keep_port_available": "Garder actif un port temporairement interrompu en cours d'utilisation", "title": "Désactivez les ports Ethernet et fibre optique inutilisés"}, "discovery_device": {"another_discovery_error": "Une autre recherche est en cours", "discovering": "Découverte de dispositifs", "discovery_finish": "La découverte de dispositifs est terminée", "error": "Impossible de découvrir des dispositifs", "title": "Découverte des dispositifs"}, "dynamic_mac_sticky": {"address_limit": "Restrictions d'adresse", "alias": "<PERSON><PERSON>", "mac_sticky": "Sticky MAC", "mac_sticky_settings": "Paramètres Sticky MAC", "packet_drop": "Déposer des paquets", "port": "port", "port_duplicated": "Le port a déjà été configuré.", "port_format_not_equal": "Les modèles doivent avoir le même format de port et le même nombre de ports.", "port_selection_guide": "clé du nom de port", "port_shutdown": "fermer le port", "security_action": "action sécuritaire", "title": "Sticky MAC dynamique"}, "export_config": {"config_center": "Centre de configuration", "config_file": "Fichier de configuration", "export": "Exporter", "fail": "Impossible d'exporter la configuration du dispositif", "hint": "* Please make sure the username and password for this device are correctly set in \"Advanced Settings\"", "success": "La configuration du dispositif a été exportée", "title": "Exporter la configuration"}, "goose": {"how_to_resolve": "Comment résoudre ce problème ?", "import_scd_tooltip": "Veuillez importer un fichier SCD pour voir les messages GOOSE. Cliquez sur Alimentation > Importer SCD.", "ip_port": "Port {{ port }} de {{ ip }}.", "open_web_console": "Ouvrir la console Web", "port_tampered_msg": "Altération du port GOOSE causée par", "port_tampered_title": "Résoudre le problème de sabotage du port GOOSE", "reset_goose": "Réinitialisation du message GOOSE falsifié", "reset_goose_desc": "Message GOOSE : {{ cbName }}/{{ appId }}/{{ mac }}", "reset_goose_title": "Êtes-vous sûr de vouloir réinitialiser toutes les instances de ce message GOOSE trafiqué ?", "resolve_goose_tampered_desc_1": "Essayez les étapes suivantes pour résoudre le problème de sabotage du port GOOSE", "resolve_goose_tampered_desc_2": "1. V<PERSON><PERSON><PERSON>z les paramètres de l'IED (ou des IED)", "resolve_goose_tampered_desc_3": "Assurez-vous que les messages de publication/abonnement GOOSE de l'IED sont correctement configurés.", "resolve_goose_tampered_desc_4": "2. Vérifiez l'état du port", "resolve_goose_tampered_desc_5": "Veuillez vérifier l'état du port {{ port }} de {{ ip }}.", "resolve_goose_tampered_desc_6": "2. <PERSON><PERSON><PERSON><PERSON><PERSON> que tous les appareils sont autorisés", "resolve_goose_tampered_desc_7": "Vérifiez qu'il n'y a pas d'appareils non autorisés sur le réseau.", "resolve_goose_tampered_desc_8": "Essayez de suivre les étapes suivantes pour résoudre le problème de falsification de SA GOOSE", "resolve_goose_timeout_desc_1": "Essayez ces étapes pour résoudre les problèmes de Timeout GOOSE.", "resolve_goose_timeout_desc_10": "<PERSON><PERSON> ne fonctionne toujours pas ?", "resolve_goose_timeout_desc_11": "Retirez le module SFP et installez-le à nouveau.", "resolve_goose_timeout_desc_12_1": "Pour toute question technique, veuillez d'abord contacter votre partenaire commercial.", "resolve_goose_timeout_desc_12_2": "partenaire de distribution", "resolve_goose_timeout_desc_12_3": "first", "resolve_goose_timeout_desc_13_1": "<PERSON><PERSON>", "resolve_goose_timeout_desc_13_2": "Support technique Moxa", "resolve_goose_timeout_desc_13_3": "si vous avez encore besoin d'une assistance supplémentaire.", "resolve_goose_timeout_desc_2": "1. V<PERSON><PERSON><PERSON>z les paramètres de l'IED (ou des IED)", "resolve_goose_timeout_desc_3": "Assurez-vous que les messages GOOSE de publication/abonnement de l'IED sont correctement configurés.", "resolve_goose_timeout_desc_4": "2. <PERSON><PERSON><PERSON><PERSON>vous que le port n'est PAS en panne", "resolve_goose_timeout_desc_5": "Vérifiez que le port de chaque dispositif sur le flux GOOSE ({{ cbName }}) n'est PAS en panne.", "resolve_goose_timeout_desc_6": "3. Véri<PERSON>z que le port n'a pas eu d'erreur Tx/Rx.", "resolve_goose_timeout_desc_7": "Cliquez sur un lien, choisissez Link Traffic pour voir la section Packet Error Rate. Assurez-vous que le port n'a pas eu d'erreur.", "resolve_goose_timeout_desc_8": "4. V<PERSON>ri<PERSON>z si les ports fibre dépassent un certain seuil", "resolve_goose_timeout_desc_9": "Choisissez le bouton \"SFP\" > \"SFP List\". Vérifiez que le port ne dépasse PAS un certain seuil.", "sa_tampered_msg": "GOOSE SA altéré", "sa_tampered_name_msg": "Le message GOOSE ({{ cbName }}/{{ appId }}/{ mac }}) entre en conflit avec une autre adresse source GOOSE.", "sa_tampered_title": "Résoudre le problème de falsification de GOOSE SA", "tampered": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timeout": "<PERSON><PERSON><PERSON>'attente", "timeout_msg": "Timeout GOOSE causé par", "timeout_title": "Résoudre le problème du Timeout GOOSE"}, "import_config": {"config_center": "Centre de configuration", "config_file": "Fichier de configuration", "config_file_error": "MXview One prend en charge uniquement les fichiers au format .ini", "config_file_size_error": "La taille maximale des fichiers est de 3 MB", "fail": "Impossible d'importer la configuration du dispositif", "hint": "* Please make sure the username and password for this device are correctly set in \"Advanced Settings\"", "import": "Importer", "success": "La configuration du dispositif a été importée", "title": "Importer la configuration"}, "link_traffic": {"date": "Date", "from": "De", "packet_error_rate_title": "<PERSON>x d'erreurs de paquets", "port_traffic_title": "Trafic du port", "time": "Time", "to": "À", "utilization": "Utilisation", "value": "Value"}, "mac_sticky_switch": {"mac_sticky": "Sticky MAC", "title": "Sticky MAC activé/désactivé"}, "maintain_group": {"change_icon": "Change group icon", "create": "<PERSON><PERSON><PERSON>", "create_group_fail": "Échec de la création du groupe", "create_group_success": "Le groupe est créé", "delete": "<PERSON><PERSON><PERSON><PERSON>", "delete_group_fail": "Échec de la suppression du groupe", "delete_group_success": "Le groupe est supprimé", "empty_group_name": "V<PERSON> devez entrer un nom de groupe", "group_desc": "Description du groupe", "group_name": "Nom du groupe", "modify_group_fail": "Échec de la modification du groupe", "modify_group_success": "Le groupe est modifié", "reset_icon": "Reset to default image", "title": "Maintenance du groupe"}, "ping": {"failed": "Échec du ping", "title": "<PERSON>"}, "policy_profile": {"delete_msg": "Êtes-vous sûr de vouloir supprimer le(s) profil(s) sélectionné(s) ?", "delete_title": "Supprimer le(s) profil(s)"}, "reboot": {"execute_button": "Redémarrage", "reboot_sequence": "Séquence de redémarrage", "title": "Redémarrage", "unable_determine_reboot_sequence_hint": "Impossible de déterminer la séquence de redémarrage. Assurez-vous que la topologie contient un ordinateur sur lequel MXview One est installé et réessayez."}, "relearn_dynamic_mac_sticky": {"execute_button": "étudier à nouveau", "title": "Réapprendre le Sticky MAC dynamique"}, "restore_to_create_snapshot": {"execute_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Restaurer à partir d'un instantané"}, "scd": {"import_failed_desc": "Consultez la liste des problèmes ci-dessous, corrigez le fichier SCD et réessayez d'importer.", "import_failed_title": "Problèmes d'importation du SCD", "import_succeed_desc": "Les messages GOOSE et la conception du flux sont intégrés avec succès dans la topologie du réseau.", "import_succeed_title": "Importation terminée !", "missing": "Manque l'adresse", "missing_device": "Impossible de trouver le(s) périphérique(s) suivant(s)", "missing_device_desc": "Consultez la liste des problèmes ci-dessous.", "scd_file_error": "MXview One ne prend en charge que le format .scd", "scd_file_size_error": "La taille maximale du fichier est de 100 Mo", "select_file": "Sélectionnez le fichier", "tag": "balise", "topology_change_desc_1": "Essayez les étapes suivantes pour résoudre les problèmes.", "topology_change_desc_2": "1. <PERSON><PERSON><PERSON><PERSON> le(s) dispositif(s) manquant(s)", "topology_change_desc_3": "Choisissez le bouton \"Edit\" > \"Add Device\".", "topology_change_desc_4": "2. Importez à nouveau le fichier SCD", "topology_change_desc_5": "Choisissez le bouton \"Alimentation\" > \"Importer SCD\".", "topology_change_title": "Problèmes de modification de la topologie", "visualize_goose_messages": "Pour visualiser les messages GOOSE entre les DEI, vous devez d'abord importer un fichier SCD.", "visualize_goose_messages_title": "Visualisation des messages GOOSE"}, "set_background": {"browse": "<PERSON><PERSON><PERSON><PERSON>", "desc": "Faire glisser une image ici ou ", "desc1": " Pour définir l'arrière-plan", "failed": "Échec de la définition de l'arrière-plan", "image": "Image", "image_alpha": "Alpha", "image_error": "Le fichier sélectionné n'était pas une image", "image_position": "Position", "image_saturation": "Saturation", "image_x": "X", "image_y": "Y", "preview": "<PERSON><PERSON><PERSON><PERSON>", "size_error": "La taille de l'image doit être comprise entre 1KB ~ 20MB.", "success": "L'arrière-plan est mis à jour", "title": "Définir l'arrière-plan", "topology_size": "Taille de la topologie"}, "set_document": {"current_filename": "Nom de fichier actuel", "delete": "Supprimer le document", "error": "MXview One prend en charge uniquement le format PDF", "failed": "Échec de la définition du document", "file_size_error": "La taille maximale du document est de 20MB.PDF uniquement.", "open": "Ouv<PERSON>r le document", "set": "Définir le document", "success": "Le document du dispositif est mis à jour", "title": "Définir le document", "upload": "Sélectionner un fichier à charger"}, "set_port_label": {"error": "<PERSON><PERSON><PERSON>", "failed": "Échec de la définition de l'étiquette du port", "from": "De :", "success": "L'étiquette du port est mise à jour", "title": "Définir l'étiquette du port", "to": "À :", "use_custom_label": "Utiliser une étiquette personnalisée", "vpn_link_desc": "L'étiquette d'une liaison VPN n'est pas configurable"}, "set_scale": {"error": "Parameter error", "fail": "Setting Scale Failed", "success": "Setting Scale Success", "title": "Set Scale"}, "severity_threshold": {"bandwidth_input_invalid": "Veuillez entrer un nombre entier de 0 à 100", "bandwidth_utilization": "Utilisation de la bande passante", "critical": "Critique", "failed": "Échec de la définition du seuil de gravité", "information": "Informations", "over": "Au-dessus de", "packet_error_rate": "<PERSON>x d'erreurs de paquets", "sfp_rx_over": "SFP RX supérieur à", "sfp_rx_under": "SFP RX est inférieur à", "sfp_temp_over": "TempératurexSFP supérieure à", "sfp_threshold": "Seuil de SFP", "sfp_tx_over": "SFP TX supérieur à", "sfp_tx_under": "SFP TX est inférieur à", "sfp_vol_over": "Tension SFP supérieure à", "sfp_vol_under": "Tension SFP inférieure à", "success": "Le seuil de gravité est mis à jour", "title": "<PERSON><PERSON> g<PERSON>", "under": "En dessous de", "warning": "Avertissement"}, "sfp_info": {"date": "Date", "dateError": "Plage de dates non valide", "from": "De", "port": "Port", "sfpRxPower": "SFP RX", "sfpRxPower_label": "RX", "sfpRxPower_scale": " dBm", "sfpTemperature": "SFP Température", "sfpTemperature_label": "Temp.", "sfpTemperature_scale": "°C", "sfpTxPower": "SFP TX", "sfpTxPower_label": "TX", "sfpTxPower_scale": " dBm", "sfpVoltage": "SFP Tension", "sfpVoltage_label": "Tension", "sfpVoltage_scale": " V", "time": "<PERSON><PERSON>", "title": "Informations relatives au SFP", "to": "À", "value": "<PERSON><PERSON>"}, "sfp_sync": {"confirm_desc": "Etes-vous sûr de vouloir synchroniser le seuil SFP du dispositif ?", "content": "Vous pouvez synchroniser le seuil SFP à partir du commutateur MOXA. Après la synchronisation, la température, la puissance Tx et la puissance Rx de la vérification de la fibre seront synchronisées avec le seuil SFP de chaque lien.", "failed": "Echec de la synchronisation du seuil SFP", "hint": "* Pour vérifier le seuil SFP, vous pouvez cliquer sur un lien, choisir Seuil de gravité > Seuil SFP.", "success": "Sync SFP Threshold Success", "title": "Synchronisation du seuil SFP à partir du dispositif"}, "wireless_settings": {"fail": "Setting Link Budget Parameters (General) Failed", "rxSensitivityHigh": "RX Sensitivity High", "rxSensitivityLow": "RX Sensitivity Low", "rxSensitivityMedium": "RX Sensitivity Medium", "sr": "Reserved Safety Factor", "success": "Setting Link Budget Parameters (General) Success", "title": "Link Budget Parameters (General)"}}, "EMBED_WIDGET": {"click_preview": "Cliquez pour afficher l'aperçu", "copied_to_clipboard": "Liaison copiée vers le Presse-papiers", "copy_link": "<PERSON><PERSON><PERSON> la <PERSON>", "custom": "Personnalisation", "desc": "Co<PERSON>r cela dans une page HTML", "embed": "Intégré", "height": "<PERSON><PERSON>", "layout_1": "Disposition 1", "layout_2": "Disposition 2", "layout_3": "Disposition 3", "layout_4": "Disposition 4", "link": "<PERSON>ison", "no_api_key": "<PERSON><PERSON> de<PERSON> d'abord créer une clé API", "preview": "<PERSON><PERSON><PERSON><PERSON>", "recent_event": "Év<PERSON><PERSON> récent", "select_api_key": "Sélectionner une clé API", "select_layout": "Sélectionner la disposition", "title": "Widget Web intégré", "topology": "Topologie", "topology_recent_event": "Topologie et événement récent", "width": "<PERSON><PERSON>"}, "error_handler": {"error_session_expired_dialog": "La session a expiré. Le système va vous rediriger vers la page Connexion."}, "ERROR_MESSAGE": {"get_data_fail": "Impossible de récupérer les données", "input_invalid_char": "Entrez un nom valide", "input_invalid_characters": "Ce champ ne peut contenir aucun des caractères suivants : #%&*:<>?|{}\\\"/", "input_invalid_contact": "Veuillez saisir un contact valide", "input_invalid_email": "Veuillez saisir une adresse électronique valide", "input_invalid_location": "Veuillez saisir un emplacement valide", "input_invalid_mac": "Adresse MAC invalide", "input_invalid_password_characters": "Ce champ ne peut contenir aucun des caractères suivants : '\\\"/`", "input_invalid_script": "Ce champ ne peut contenir aucun des caractères suivants : #%&amp;*{}|:&quot;&lt;&gt;?/\\", "input_ip_invalid": "Entrez une adresse IP valide", "input_required": "Cette valeur est requise", "non_restricted_ascii": "Caractères ASCII, sauf ' \\\" ` \\\\"}, "errors": {"A001": "Champ obligatoire manquant", "A002": "<PERSON><PERSON><PERSON>u<PERSON> man<PERSON>", "A003": "Format incorrect", "D001": "Nombre maximum de licences atteint", "D002": "L'appareil est introuvable", "D003": "L'appareil doit être en ligne", "D004": "L'appareil a été supprimé", "F001": "Le firmware est introuvable", "F002": "Ce firmware existe déjà", "F003": "Nombre maximal de fichiers de firmware atteint", "G001": "Ce groupe existe déjà", "G002": "Le groupe est introuvable", "G003": "Le groupe par défaut ne peut pas être modifié", "G004": "Les utilisateurs administrateurs ne peuvent pas être affectés à des groupes", "I001": "Cette interface existe déjà", "I002": "L'interface est introuvable", "I003": "Le groupe par défaut ne peut pas être modifié", "I004": "Cette interface est référencée par un profil de sécurité.", "L001": "Code d'activation non valide", "L002": "La licence a expiré", "L003": "Code d'activation en double", "L004": "Nombre maximal de nœuds atteint", "L005": "L'appareil ne peut pas être activé ou désactivé", "L006": "Heure de début invalide", "L007": "Heure de début non valide pour la nouvelle licence de type", "O001": "Cet objet existe déjà", "O002": "L'objet est introuvable", "O003": "Cet objet est référencé par un profil de sécurité.", "P001": "Le paquet est introuvable", "P002": "Ce package existe déjà", "P003": "Nombre maximum de colis atteint", "P004": "Version non prise en charge", "S001": "Erreur de mise à jour de la base de données", "SP001": "Ce profil existe déjà", "SP002": "Le profil est introuvable", "T001": "Jeton non autorisé", "T002": "Jeton expiré", "T003": "<PERSON><PERSON> invalide", "U001": "Permission refusée", "U002": "Ce nom d'utilisateur existe déjà", "U003": "L'utilisateur est introuvable", "U004": "Le rôle est introuvable", "U005": "Nom d'utilisateur/mot de passe non valide", "U006": "Le mot de passe ne respecte pas la longueur minimale", "U007": "Le mot de passe dépasse la longueur maximale", "U008": "Le mot de passe ne peut pas être le même que le nom d'utilisateur", "U009": "Doit inclure au moins un caractère majuscule", "U010": "Doit inclure au moins un caractère minuscule", "U011": "Doit inclure au moins un chiffre", "U012": "Doit inclure au moins un caractère non alphanumérique", "U013": "Le mot de passe ne peut pas être le même que le mot de passe précédent", "U014": "nom d'utilisateur invalide", "Unknown": "inconnu"}, "EULA": {"agree_hint": "Veuillez accepter l'accord d'utilisation de MXview One.", "eula_title": "EULA"}, "EVENT_MENU": {"ack": "Accusé de ré<PERSON>", "ack_all": "Accuser ré<PERSON> de tout", "filter_event": "Filtrer les conditions"}, "EVENT": {"ack_all_events": {"ack_all_event_success": "Tous les événements ont fait l'objet d'un accusé de réception.", "button_ack_hint": "Ack Selected Event(s)", "button_hint": "Accusé de réception de tous les événements", "confirm_message": "Tous les événements feront l'objet d'un accusé de réception ! Souhaitez-vous poursuivre ce processus?", "unable_ack_all_event": "Impossible d'accuser réception de tous les événements"}, "ack": {"ack_fail": "Impossible d'envoyer l'accusé de réception de l'événement", "acked": "Accusé de réception envoyé", "any": "N'importe quel", "unacked": "Accusé de réception non envoyé"}, "all_event": "Tous les événements", "all_group": "Tous les groupes", "all_site": "Tous les sites", "clear_all_events": {"button_hint": "Effacer tous les événements", "clear_all_event_success": "Tous les événements ont été effacés.", "confirm_message": "Tous les événements seront effacés ! Souhaitez-vous continuer ce processus?", "confirm_message_network": "Tous les événements liés au réseau et aux périphériques seront effacés. Êtes-vous sûr de vouloir continuer ce processus ?", "confirm_message_system": "Tous les événements système seront effacés. Êtes-vous sûr de vouloir continuer ce processus ?", "unable_clear_all_event": "Impossible d'effacer tous les événements"}, "custom_events": {"activate": "Activer l'événement personnalisé", "add_custom_event": "Ajouter un événement personnalisé", "all": "Tous", "all_devices": "Tous les dispositifs", "apply_fail": "Impossible de définir l'événement personnalisé", "apply_success": "L'événement personnalisé est ajouté", "below": "En dessous de", "condition": "Condition", "condition_operator": "Opérateur de condition", "condition_value": "Valeur de condition", "consecutive_updates": "interrogations consécutives", "delete_fail": "Impossible de supprimer l'événement personnalisé", "delete_success": "Un ou plusieurs évènements personnalisés ont été supprimés", "description": "Description", "device_properties": "Propriétés des dispositifs", "devices": "Dispositifs", "duration": "<PERSON><PERSON><PERSON>", "equal": "<PERSON><PERSON>", "event_name": "Nom de l'événement", "filter_custom_event": "Taper pour filtrer les événements personnalisés", "get_fail": "Impossible d'obtenir les événements personnalisés", "helper": "Ajouter les événements personnalisés et leur attribuer des dispositifs", "not_equal": "<PERSON><PERSON><PERSON><PERSON>", "over": "Au-dessus de", "recovery_description": "Description de la récupération", "register": "Enregistrer", "register_devices": "Enregistrer les dispositifs", "search": "<PERSON><PERSON><PERSON>", "severity": "Gravité", "title": "Év<PERSON><PERSON> person<PERSON>", "update_custom_event": "Mettre à jour l'événement personnalisé", "update_fail": "Impossible de mettre à jour l'événement personnalisé", "update_success": "L'événement personnalisé est mis à jour"}, "event_description": {"abc_attache_warning": "Le dispositif ABC est attaché", "abc_auto_import_warning": "échec de la configuration de l'importation automatique", "abc_config_warning": "échec de l'exportation du fichier de configuration", "abc_detache_warning": "Le dispositif ABC est détaché", "abc_log_warning": "échec de l'exportation du fichier journal", "abc_space_warning": "espace insuffisant", "abc_unauthorized_warning": "détection d'un support non autorisée", "abc_unknow_warning": "inconnu", "abc02_warning": "Événement USB : {{param}}", "account_audit_baseline_failed": "Échec de l'audit du compte. Impossible de récupérer les données de tous les appareils.", "account_audit_baseline_match": "Audit de compte terminé avec succès. Le résultat correspond à la ligne de base.", "account_audit_failed": "Échec de l'audit du compte. Impossible de récupérer les données des appareils sur {{ip}}.", "account_audit_match": "Audit de compte terminé avec succès. Le résultat correspond à la ligne de base.", "account_audit_mismatch": "Audit de compte terminé avec succès. Le résultat ne correspond pas à la ligne de base.", "account_audit_unable_retrieve_device": "Échec de l'audit du compte. Impossible de récupérer les données de tous les appareils.", "accountAudit1": "L'audit des comptes ne correspond pas à la référence", "accountAudit2": "Échec de l'audit du compte", "all_event_clear": "Tous les événements ont été effacés", "auth_fail": "Échec de l'authentification de la connexion à l'appareil.", "availability_down": "Disponibilité du dispositif en dessous du seuil", "availability_down_recovery": "Disponibilité du dispositif en dessous de la récupération du seuil", "background_scan_found": "Nouvel appareil trouvé (IP : {{ip}}).", "cli_button_event_all_failed": "{{user}} depuis l'IP : {{sourceIP}} a exécuté le bouton : {{cliName}}. Le résultat de l’exécution est All Failed.", "cli_button_event_all_finished": "{{user}} depuis l'IP : {{sourceIP}} a exécuté le bouton : {{cliName}}. Le résultat de l'exécution est Tout terminé.", "cli_button_event_all_partially_finished": "{{user}} depuis l'IP : {{sourceIP}} a exécuté le bouton : {{cliName}}. Le résultat de l'exécution est Partiellement terminé.", "cli_button_event_start": "{{user}} depuis l'IP : {{sourceIP}} a exécuté le bouton : {{cliName}}.", "cli_saved_script_event": "{{user}} depuis IP : {{sourceIP}} a exécuté la commande CLI : {{cliName}}", "cli_script_event": "{{user}} depuis IP : {{sourceIP}} commence à exécuter la CLI.", "cold_start": "Démarrage à froid", "custom_event_detail": "{{param1}}. <PERSON><PERSON> = {{param2}}, valeur ={{param3}}. {{param4}}", "custom_event_recovery": "Événement personnalisé ré<PERSON>", "custom_event_recovery_detail": "{{param1}} est récupéré. <PERSON><PERSON> = {{param2}}, valeur ={{param3}}. {{param4}}", "custom_event_trigger": "Événement personnal<PERSON>", "cybersecurity_event_trigger": "Événement sur la cybersécurité", "ddos_under_attack": "Un routeur sécurisé fait l'objet d'une attaque DDoS", "ddos_under_attack_recovery": "Un routeur sécurisé ne fait plus l'objet d'une attaque DDoS", "device_configuration_change": "La configuration de l'appareil a changé.", "device_firmware_upgrade": "Le microprogramme du dispositif est mis à jour", "device_infom_receive": "Les informations sur le dispositif ont été reçues", "device_lockdown_violation": "Violation du verrouillage de l'appareil", "device_power_down": "Mettre {{param}} hors tension", "device_power_down_off_to_on": "REP {{param}} Désactivé > Activé.", "device_power_on": "Mettre {{param}} sous tension", "device_power_on_to_off": "REP {{param}} Activé > Désactivé.", "device_snmp_reachable": "Le dispositif SNMP est accessible", "device_snmp_unreachable": "Le dispositif SNMP est inaccessible", "di_off": "DI {{param}}} d<PERSON><PERSON><PERSON><PERSON>", "di_on": "DI {{param}}} activé", "disk_space_not_enough": "L'espace disponible sur le disque est inférieur au seuil", "event_config_import": "Un nouveau fichier de configuration est importé", "event_ddos_attack": "Attaque DoS détectée", "event_ddos_attack_recovery": "Récupération après une attaque DDoS sur le routeur sécurisé", "event_dying_gasp": "Le système est hors tension. Le périphérique est alimenté par son condensateur", "event_eps_is_off": "La source d'alimentation externe PoE est éteinte", "event_eps_is_on": "La source d'alimentation externe PoE est activée", "event_firewall_attack": "Le routeur sécurisé fait objet d'une attaque de pare-feu", "event_firewall_attack_recovery": "Récupération après une attaque de pare-feu sur le routeur sécurisé", "event_ieee_lldp_table_change": "{{trapDetail}}", "event_ieee_rstp_root_change": "La nouvelle racine RSTP a été sélectionnée dans la topologie", "event_ieee_rstp_topology_change": "La topologie a été modifiée par RSTP", "event_linux_account_setting_change": "Les réglages du compte de {{username}} ont été modifiés", "event_linux_config_change": "Le {{modules}} la configuration a été modifiée par {{username}}.", "event_linux_config_import": "Importation de la configuration {{successOrFail}} par {{username}}", "event_linux_config_import_failed": "échec", "event_linux_config_import_succeed": "réussite", "event_linux_coupling_change": "L'état du chemin de couplage du Turbo Ring v2 a été modifié", "event_linux_di_off": "L'entrée numérique de l'{{index}} a été désactivée", "event_linux_di_on": "L'entrée numérique de l'{{index}} a été activée", "event_linux_dotlx_auth_fail": "Échec de l'authentification 802.1X sur le port {{portIndex}} avec {{reason}}", "event_linux_dual_homing_change": "Le chemin de connexion double a été branché", "event_linux_log_capacity_threshold": "Le nombre d'entrées du journal des événements {{value}} a atteint le seuil", "event_linux_low_input_voltage": "La tension d'entrée de l'alimentation électrique est sous le seuil", "event_linux_master_change": "Le maître de l'anneau {{index}} a été modifié", "event_linux_master_mismatch": "Le réglage du maître de l'anneau {{index}} ne correspond pas", "event_linux_over_power_budget_limit": "La puissance consommée {{value}} de tous les PD a dépassé la puissance d'entrée maximum {{threshold}}", "event_linux_password_change": "Le mot de passe de {{username}} ont été modifié", "event_linux_pd_no_response": "Le dispositif du port PoE {{portIndex}} ne répond pas à la vérification de l'échec du PD.", "event_linux_pd_over_current": "Le courant du port PoE {{portIndex}} a dépassé la limite de sécurité.", "event_linux_pd_power_off": "PD du port PoE {{portIndex}} désactivé", "event_linux_pd_power_on": "PD du port PoE {{portIndex}} activé", "event_linux_port_recovery_by_ratelimit": "Le port {{portIndex}} a été récupéré par la limite de taux", "event_linux_port_shutdown_by_ratelimit": "Port {{portIndex}} est bloqué en raison d'un trafic dépassant la limite de débit.", "event_linux_port_shutdown_by_security": "Arrêt du port {{portIndex}} par la sécurité du port", "event_linux_power_detection_fail": "Le dispositif du port PoE {{portIndex}} est {{devicetype}}. Veuillez {{suggestion}}.", "event_linux_power_detection_fail_devietype_na": "s.o.", "event_linux_power_detection_fail_devietype_noPresent": "non présent", "event_linux_power_detection_fail_devietype_unknown": "inconnu", "event_linux_power_detection_fail_suggestion_disable_POE": "désactiver le POE", "event_linux_power_detection_fail_suggestion_enable_legacy": "activer système antérieur", "event_linux_power_detection_fail_suggestion_enable_POE": "activer le P<PERSON>", "event_linux_power_detection_fail_suggestion_no": "aucune suggestion", "event_linux_power_detection_fail_suggestion_rais_EPS_voltage": "augmenter la tension du BAE", "event_linux_power_detection_fail_suggestion_select_auto": "sélectionner auto", "event_linux_power_detection_fail_suggestion_select_force": "sélectionner force", "event_linux_power_detection_fail_suggestion_select_high_power": "sélectionner haute puissance", "event_linux_power_off": "L'alimentation de l'{{index}} a été désactivée", "event_linux_power_on": "L'alimentation de l'{{index}} a été activée", "event_linux_redundant_port_health_check": "Échec du bilan de santé du port redondant {{portIndex}}", "event_linux_RMON_trap_is_falling": "L'alerte RMON diminue", "event_linux_RMON_trap_is_raising": "L'alerte RMON augmente", "event_linux_rstp_invalid_bpdu": "RSTP port {{portIndex}} received an invalid BPDU (type: {{type}}, valeur: {{value}})", "event_linux_rstp_migration": "Le port {{portIndex}} est passé de {{originTopology}} à {{changeTopology}}", "event_linux_rstp_new_port_role": "Le rôle du port RSTP {{portIndex}} est passé de {{originalRole}} à {{newRole}}", "event_linux_ssl_cer_change": "Le certificat SSL a été modifié", "event_linux_topology_change": "La topologie a changé.", "event_linux_topology_change_by_type": "La topologie a été modifiée par {{topologyType}}", "event_linux_user_login_lockout": "{{username}} est verrouillé à cause de {{param}} tentatives de connexion infructueuses", "event_linux_user_login_success": "{{username}} s'est connecté via l'{{interface}}", "event_log_cleared_trap_event_info": "The event logs were cleared (User: {{ user }}, IP: {{ ip }}, Interface: {{ interface }})", "event_message_serial_device_port_any_recovery": "Le port série {{portnum}} a repris son fonctionnement normal.", "event_message_serial_device_port_break": "Le port série {{portnum}} a reçu une erreur : Break Error Count.", "event_message_serial_device_port_frame": "Le port série {{portnum}} a reçu une erreur : nombre d'erreurs de trame.", "event_message_serial_device_port_overrun": "Le port série {{portnum}} a reçu une erreur : dépassement du nombre d'erreurs.", "event_message_serial_device_port_parity": "Le port série {{portnum}} a reçu une erreur : nombre d'erreurs de parité.", "event_message_serial_device_port_rx": "Le RX du port série {{portnum}} n'a reçu aucune donnée au cours des {{min}} dernières minutes.", "event_message_serial_device_port_rx_recovery": "Le RX du port série {{portnum}} a repris la réception des données.", "event_message_serial_device_port_rxtx": "Le RX et le TX du port série {{portnum}} n'ont reçu aucune donnée au cours des {{min}} dernières minutes.", "event_message_serial_device_port_rxtx_recovery": "Le RX et le TX du port série {{portnum}} ont repris la réception des données.", "event_message_serial_device_port_tx": "Le TX du port série {{portnum}} n'a reçu aucune donnée au cours des {{min}} dernières minutes.", "event_message_serial_device_port_tx_recovery": "Le TX du port série {{portnum}} a repris la réception des données.", "event_not_found_type": "Unknown event type: {{ eventType }}", "event_pd_check_fail": "Vérification de défaillance PD (aucune réponse)", "event_pd_over_current": "Surintensité/court-circuit sur le port PoE {{portIndex}}", "event_pd_power_off": "Mise hors tension du port PoE {{portIndex}}", "event_pd_power_on": "Mise sous tension du port PoE {{portIndex}}", "event_prp_function_fail": "La fonction PRP a échoué", "event_serial_device_port_break": "Le port série a reçu une erreur : nombre d'erreurs de rupture", "event_serial_device_port_frame": "Le port série a reçu une erreur : nombre d'erreurs de trame", "event_serial_device_port_overrun": "Le port série a reçu une erreur : nombre d'erreurs dépassé", "event_serial_device_port_parity": "Le port série a reçu une erreur : nombre d'erreurs de parité", "event_serial_device_port_rx": "Le RX du port série n'a reçu aucune donnée", "event_serial_device_port_rxtx": "Le RX et le TX du port série n'ont reçu aucune donnée", "event_serial_device_port_tx": "Le TX du port série n'a reçu aucune donnée", "event_sfp_rx_below": "La puissance RX ({{currentdB}} dBm) du port SFP {{portIndex}} est en dessous du seuil ({{thresholddB}} dBm).", "event_sfp_rx_below_recovery": "SFP du Port {{portIndex}} RX{{recoverydB}}dBm a été rétabli", "event_sfp_temp_over": "La température du module ({{currentTemp}}ºc) du port SFP {{portIndex}} a dépassé le seuil ({{currentTemp}}ºc).", "event_sfp_temp_over_recovery": "La température SFP du port {{portIndex}} {{recoveryTemp}}ºc a été rétablie", "event_sfp_tx_below": "La puissance d'émission ({{currentdB}} dBm) du port SFP {{portIndex}} est en dessous du seuil ({{thresholddB}} dBm).", "event_sfp_tx_below_recovery": "SFP du Port {{portIndex}} TX {{recoverydB}}dBm a été rétabli", "event_sfp_voltage_below": "La tension du module ({{currentVoltage}} V) du port SFP {{portIndex}} est en dessous du seuil ({{thresholdVoltage}} V).", "event_sfp_voltage_below_recovery": "La tension SFP du port {{portIndex}} {{recoveryVoltage}}v a été rétablie", "event_sfp_voltage_over": "La tension du module ({{currentVoltage}} V) du port SFP {{portIndex}} a dépassé le seuil ({{thresholdVoltage}} V).", "event_sfp_voltage_over_recovery": "La tension SFP du port {{portIndex}} {{recoveryVoltage}}v a été rétablie", "event_too_many_login_failure": "Accès à l'interface web temporairement bloqué en raison d'un trop grand nombre d'échecs de connexion.", "event_too_many_login_failure_recovery": "L'événement Trop d'événements d'échec se termine. L'accès au Web reprend.", "event_tracking_port_enabled_status": "L'une des entrées de suivi liée au port activé est modifiée", "event_tracking_static_route_status_changed": "L'une des entrées de suivi liée à l’itinéraire statique est modifiée", "event_tracking_status_changed": "L'un des états d'entrées de suivi est modifié", "event_tracking_vrrp_status_changed": "L'une des entrées de suivi liées au VRRP est modifiée.", "event_trusted_access_attack": "Le routeur sécurisé fait objet d'une attaque d'accès approuvé", "event_trusted_access_attack_recovery": "Récupération après une attaque d'accès approuvé sur le routeur sécurisé", "event_user_info_change": "Les informations du compte ont été modifiées : {{trapoid}}", "event_v3_trap_parse_error": "Erreur d'analyse du trap V3", "event_v3_trap_parse_error_recovery": "L'événement d'erreur d'analyse du trap V3 est effacé", "exceed_poe_threshold": "Dépassement du seuil du système PoE", "fan_module_malfunction": "Le module de ventilation est défectueux.", "fiber_warning": "{{portIndex}} Avertissement de fibre ( {{warningType}} )", "firewall_policy_violation": "Stratégie de pare-feu et règle DoS : {{trapoid}}", "firewall_under_attack": "Le pare-feu d'un routeur sécurisé fait l'objet d'une attaque", "firewall_under_attack_recovery": "Le pare-feu d'un routeur sécurisé ne fait plus l'objet d'une attaque", "firmware_upgraded": "Mise à jour du firmware", "firmware_version_release": "Une mise à jour du firmware est disponible. Consultez la page Gestion du micrologiciel pour plus de détails.", "goose_healthy": "État GOOSE : <PERSON><PERSON><PERSON>  Message GOOSE sain {{ display }}", "goose_healthy_with_value": "Statut GOOSE :  Message GOOSE sain {{ display }}", "goose_tampered": "Statut GOOSE :  Falsification", "goose_tampered_with_value": "Statut GOOSE :  Message GOOSE \"Tampered\" {{ display }} (falsifié)", "goose_timeout": "Statut GOOSE :  Timeout", "goose_timeout_with_value": "Statut GOOSE : <PERSON><PERSON><PERSON>  Timeout (dé<PERSON> d'attente) Message GOOSE {{ display }}", "high_cpu_loading": "La charge du processeur a dépassé 85 % pendant 10 minutes consécutives.", "icmp_packet_loss_over_critical_threhold": "Taux de perte de paquets ICMP du dispositif jusqu'à {{param1}} (au-dessus du seuil critique {{param2}})", "icmp_packet_loss_over_critical_threhold_recovery": "Le taux de perte de paquets ICMP du dispositif se situe en dessous de {{param1}} (au-dessus du seuil critique {{param2}})", "icmp_packet_loss_over_threhold": "Taux de perte de paquets ICMP du dispositif jusqu'à {{param1}} (au-dessus du seuil {{param2}})", "icmp_packet_loss_over_threhold_recovery": "Le taux de perte de paquets ICMP du dispositif se situe en dessous de {{param1}} (au-dessus du seuil {{param2}})", "icmp_reachable": "Le dispositif ICMP est accessible", "icmp_unreachable": "Le dispositif ICMP est inaccessible", "iei_fiber_warning": "Un avertissement de fibre est déclenché", "input_bandwidth_over_threshold": "L'utilisation de la bande passante d'entrée a dépassé le seuil.", "input_bandwidth_over_threshold_disabled": "Pas de seuil défini pour l'utilisation de la bande passante d'entrée du port {{portIndex}}.", "input_bandwidth_over_threshold_recovery": "L'utilisation de la bande passante d'entrée du port {{portIndex}} a récupéré, la valeur est {{valeur actuelle}}.", "input_bandwidth_over_threshold_with_port": "L'utilisation de la bande passante d'entrée ({{currentValue}}) de port {{portIndex}} a dépassé le seuil ({{threshold}}).", "input_bandwidth_under_threshold": "L'utilisation de la bande passante d'entrée est inférieure au seuil.", "input_bandwidth_under_threshold_disabled": "Aucun seuil n'est défini pour l'utilisation de la bande passante d'entrée du port {{portIndex}}.", "input_bandwidth_under_threshold_recovery": "L'utilisation de la bande passante d'entrée du port {{portIndex}} a récupéré, la valeur est {{valeur actuelle}}.", "input_bandwidth_under_threshold_with_port": "L'utilisation de la bande passante d'entrée ({{currentValue}}) de port {{portIndex}} est en dessous du seuil ({{threshold}}).", "input_packet_error_over_threshold": "Le taux d'erreur des paquets d'entrée a dépassé le seuil.", "input_packet_error_over_threshold_disabled": "Aucun seuil n'est défini pour le taux d'erreur des paquets d'entrée pour le port {{portIndex}}.", "input_packet_error_over_threshold_recovery": "Le taux d'erreur de paquet d'entrée du port {{portIndex}} a été récupéré, la valeur est {{valeur actuelle}}.", "input_packet_error_over_threshold_with_port": "Le taux d'erreur des paquets d'entrée ({{currentValue}}) de port {{portIndex}} a dépassé le seuil ({{threshold}}).", "insufficient_disk_space": "Moins de 5 Go d'espace disque disponible.", "interface_set_as_ospf_designated_router": "L'interface est configurée en tant que routeur désigné OSPF.", "ip_conflict_detected": "Conflit IP détecté pour {{ip}}, MAC en conflit :", "ip_conflict_detected_failed": "La détection des conflits IP ne peut pas s'exécuter car Npcap/WinPcap/Libpcap est introuvable", "ip_conflict_recovery": "Conflit IP résolu.", "iw_client_joined": "Client joint : {{param}}", "iw_client_left": "Client laissé : {{param}}", "l3_firewall_policy_violation": "Violation de la politique de pare-feu (série NAT)", "license_limitation_reached": "Limite maximale de licence atteinte.", "license_not_enough": "La limite du nombre de nœuds est dépassée. Supprimez des nœuds ou procédez à une mise à niveau de votre licence MXview One.", "license_over": "La limite du nombre de nœuds est dépassée. Supprimez des nœuds ou procédez à une mise à jour de votre licence MXview One", "lldp_change": "La table LLDP a été modifiée", "logging_capacity": "Le journal des événements dépasse le seuil de capacité", "login_radius_fail": "Échec de l'authentification de connexion sur le serveur RADIUS+", "login_radius_success": "L'authentification de connexion sur le serveur RADIUS+ s'est effectuée correctement", "login_tacas_fail": "Échec de l'authentification de connexion sur le serveur TACACS+", "login_tacas_success": "L'authentification de connexion sur le serveur TACACS+ s'est effectuée correctement", "mac_sticky_violation": "Violation persistante MAC", "mrp_multiple_event": "Un événement impliquant plusieurs gestionnaires MRP s'est produit.", "mrp_ring_open_event": "Un événement d'ouverture de l'anneau MRP s'est produit.", "mstp_topology_changed": "Modifications de la topologie MSTP", "mxview_autopology_finish": "Topologie automatique terminée", "mxview_autopology_start": "Topologie automatique démarrée", "mxview_db_backup_fail": "Échec de la sauvegarde de la base de données", "mxview_db_backup_sucess": "La sauvegarde de la base de données est terminée, stockée à l'emplacement %MXviewPRO_Data%\\db_backup\\{{param1}} {{param2}}", "mxview_job_done": "travail: {{jobname}} est fait", "mxview_job_start": "Tâche : {{jobname}} début", "mxview_server_license_limit": "Pas assez de licences de MXview One Central Manager", "mxview_server_start": "Le serveur MXview One est démarré", "mxview_sms_fail": "Impossible d'envoyer une notification SMS", "mxview_sms_success": "MXview One a envoyé une notification SMS", "mxview_user_lockout": "Le compte {{param}} a été verrouillé momentanément", "mxview_user_login_fail": "Échec de la connexion à MXview One.", "mxview_user_login_sucess": "Connexion de l'utilisateur : {{param}}", "mxview_user_logout": "Déconnexion de l'utilisateur : {{param}}", "network_latency": "La latence du réseau vers MXview One Central Manager a dépassé 100 ms", "new_port_role_selected": "Sélectionnez le nouveau rôle de port.", "new_root_bridge_selected_in_topology": "Sélectionnez le nouveau pont racine dans la topologie.", "notification_sfp_rx_below": "SFP RX est en dessous du seuil", "notification_sfp_temp_over": "La température SFP est supérieure au seuil", "notification_sfp_tx_below": "SFP TX est en dessous du seuil", "notification_sfp_voltage_below": "La tension SFP est en dessous du seuil", "notification_sfp_voltage_over": "La tension SFP est au-dessus du seuil", "nport_syslog_over_threshold": "Le NPort se situe au-dessus du seuil du journal système", "opcua_server_start": "Le serveur MXview One OPC UA est démarré.", "opcua_server_stop": "Le serveur MXview One OPC UA s'est arrêté.", "ospf_designated_router_changed": "Le routeur désigné OSPF a changé.", "ospf_designated_router_interface_and_adjacency_changed": "Les interfaces et les contiguïtés des routeurs désignées par OSPF changent.", "out_of_memory": "Moins de 20 % de mémoire disponible.", "output_bandwidth_over_threshold": "L'utilisation de la bande passante de sortie a dépassé le seuil.", "output_bandwidth_over_threshold_disabled": "Aucun seuil n'a été défini pour l'utilisation de la bande passante de sortie du port {{portIndex}}.", "output_bandwidth_over_threshold_recovery": "Utilisation de la bande passante de sortie du port {{portIndex}} récupé<PERSON>e, la valeur est {{valeur actuelle}}.", "output_bandwidth_over_threshold_with_port": "L'utilisation de la bande passante de sortie ({{currentValue}}) de port {{portIndex}} a dépassé le seuil ({{threshold}}).", "output_bandwidth_under_threshold": "L'utilisation de la bande passante de sortie est inférieure au seuil.", "output_bandwidth_under_threshold_disabled": "Aucun seuil n'est défini pour l'utilisation de la bande passante de sortie du port {{portIndex}}.", "output_bandwidth_under_threshold_recovery": "Utilisation de la bande passante de sortie du port {{portIndex}} récupé<PERSON>e, la valeur est {{valeur actuelle}}.", "output_bandwidth_under_threshold_with_port": "L'utilisation de la bande passante de sortie ({{currentValue}}) de port {{portIndex}} est en dessous du seuil ({{threshold}}).", "output_packet_error_over_threshold": "Le taux d'erreur des paquets de sortie a dépassé le seuil.", "output_packet_error_over_threshold_disabled": "Aucun paramètre de seuil pour l'erreur de paquet de sortie pour le port {{portIndex}}.", "output_packet_error_over_threshold_recovery": "L'erreur de paquet de sortie du port {{portIndex}} a été récupérée, la valeur est {{valeur actuelle}}.", "output_packet_error_over_threshold_with_port": "Le taux d'erreur des paquets de sortie ({{currentValue}}) de port {{portIndex}} a dépassé le seuil ({{threshold}}).", "overheat_protection_now_active_for_power_module": "La protection contre la surchauffe du module d'alimentation {{ x }} est désormais activée.", "password_automatically_changed_failed": "Échec de la modification automatique du mot de passe de l'appareil.", "password_automatically_changed_success": "Le mot de passe de l'appareil a été modifié automatiquement avec succès.", "password_automation_scheduled": "L'automatisation des mots de passe a atteint l'heure prévue et va commencer à s'exécuter.", "phr_port_timediff": "Différenciation horaire du port PHR AB", "phr_port_wrong_lan": "Port PHR AB LAN incorrect.", "poe_off_info": "The device is not powered by PoE", "poe_on_info": "The device is powered by PoE", "port_linkdown_event": "Panne de liaison sur le port {{portindex}}", "port_linkdown_recovery": "Établissement d'une liaison sur le port {{portindex}}", "port_linkup_event": "Établissement d'une liaison sur le port {{portindex}}", "port_linkup_recovery": "Panne de liaison sur le port {{portindex}}", "port_loop_detect": "Une boucle de port sur le port {{portnum}} a été détectée", "port_loop_detect_resolved": "Le problème de boucle de port sur le port {{portnum}} a été résolu.", "port_loop_detected": "Boucle de port", "port_pd_short_circuited": "Port {{portnum}} Non-PD ou PD court-circuité.", "port_traffic_overload": "Surcharge du trafic du port {{portIndex}} {{percent}}%", "power_danger_recovery": "L'alimentation {{param}} est passée sur CA", "power_has_been_cut_due_to_overheating": "L'alimentation électrique a été coupée en raison d'une surchauffe.", "power_module_fan_malfunction": "Le ventilateur du module d'alimentation est défectueux.", "power_type_danger": "L'alimentation {{param}} est passée sur CC", "pse_fet_bad": "Échec du FET externe du port PoE {{portIndex}}", "pse_over_temp": "Température excessive de la puce PSE", "pse_veeuvlo": "VEE de la puce PSE en dessous du verrouillage de tension", "ptp_grandmaster_changed": "PTP Grandmaster changé", "ptp_sync_status_changed": "L'état de la synchronisation PTP a été modifié", "rateLimit_off": "Limite de débit du port {{portindex}} désactivée", "rateLimit_on": "La limitation de débit est active sur le port {{portindex}}.", "recorved_device_lockdown_violation": "Récupéré après une violation du verrouillage de l'appareil", "recorved_l3_firewall_policy_violation": "Récupération après violation de la politique de pare-feu (série NAT)", "redundancy_topology_change": "La topologie redondante a été modifiée.", "syslog_server_start": "Le serveur syslog MXview One est démarré.", "syslog_server_stop": "Le serveur syslog MXview One s'est arrêté.", "system_temperature_exceeds_threshold": "La température du système dépasse le seuil.", "temporary_account_activate_success": "Le compte temporaire chez {{ip}} a été activé avec succès.", "temporary_account_deactivate_success": "Le compte temporaire chez {{ip}} a été désactivé avec succès.", "thermal_sensor_component_overheat_detected": "Une surchauffe de l’ensemble du capteur thermique a été détectée.", "trunk_port_link_down": "Panne de liaison sur le port de jonction {{portindex}} (port physique :{{param}})", "trunk_port_link_down_recovery": "Établissement d'une liaison sur le port de jonction {{portindex}} (port physique :{{param}})", "trunk_port_link_up": "Établissement d'une liaison sur le port de jonction {{portindex}} (port physique :{{param}})", "trust_access_under_attack": "L'accès approuvé d'un routeur sécurisé est violé", "trust_access_under_attack_recovery": "L'accès approuvé d'un routeur sécurisé n'est plus violé", "turbo_ring_master_match": "Le Turbo Ring principal correspond", "turbo_ring_master_mismatch": "Le Turbo Ring principal ne correspond pas", "turbo_ring_master_unknow": "Événement principal de Turbo Ring, l'état est inconnu", "turbochain_topology_change": "La topologie de Turbo Chain a été modifiée", "turboring_coupling_port_change": "Le port de couplage Turbo Ring a été modifié", "turboring_master_change": "Le Turbo Ring principal a été modifié", "unknown_device_detected": "Un appareil inconnu a été détecté", "user_login_fail": "Échec de l'authentification du compte", "user_login_success": "<PERSON><PERSON><PERSON><PERSON> de l'authentification du compte : {{username}}", "usercode_revoke": "Le usercode est régénéré par l'utilisateur.", "vpn_link_recovery": "Le tunnel VPN {{param}} est récupéré", "vpn_linkdown": "Le tunnel VPN {{param}} est déconnecté", "vpn_linkup": "Le tunnel VPN {{param}} est connecté", "vrrp_master_changed": "VRRP Master changé", "warn_start": "Démarrage à chaud"}, "event_detail_title": "Identifiant détaillé de l'événement : {{eventId}}", "filter": "Filtrer les conditions", "filter_end_time": "Date de fin", "filter_event": "Taper pour filtrer l'événement", "filter_event_type": "Événement de filtre rapide", "filter_from_time": "Date de début", "filter_hour": "<PERSON><PERSON>", "filter_min": "Minute", "filter_sec": "Seconde", "filter_type": {"last_fifty_events": "Cinquante derniers événements", "last_twenty_events": "Vingt derniers événements", "unack_events": "Événements n'ayant pas fait l'objet d'un envoi d'accusé de réception", "unack_last_fifty_events": "Cinquante derniers événements n'ayant pas fait l'objet d'un envoi d'accusé de réception", "unack_last_twenty_events": "<PERSON>gt derniers événements n'ayant pas fait l'objet d'un envoi d'accusé de réception"}, "group": "Groupe", "recent_event": "Événements récents", "severity": {"any": "N'importe quel", "critical": "Critique", "information": "Informations", "network_device": "Réseau et appareil", "system_information": "Informations système", "warning": "Avertissement"}, "show_event_detail": "<PERSON><PERSON><PERSON><PERSON> le d<PERSON>", "source": {"any": "N'importe quel", "mxview": "MXview One", "security_sensing": "Détection de sécurité", "trap": "Interception"}, "table_title": {"ack": "Accusé de ré<PERSON>", "ack_time": "Du<PERSON>e du l'accusé de réception", "always_show": "Toujours afficher l'événement récent au démarrage", "description": "Description", "detail_information": "Informations détaillées", "event_id": "ID", "event_properties": "Propriété des événements", "event_source": "Source", "event_time": "Heure d'émission", "hide_recent_event": "Cacher les événements récents", "severity": "Gravité", "show_recent_event": "Afficher les événements récents", "site_name": "Nom du site", "source_ip": "Adresse IP de la source"}, "tabs": {"network_device_title": "Réseau et appareil", "security_event": "Événement sur la cybersécurité", "security_title": "Cybersécurité", "system_title": "Système"}}, "execute_cli_object": {"add_cli_object": "Ajouter un script CLI", "alias": "<PERSON><PERSON>", "cli_error": {"connection_failure": "Échec de connexion", "handshake_failure": "Échec de la poignée de main", "login_failure": "Erreur d'authentification", "port_limit": "Nombre maximum d'entrées d'adresses de sécurité de port : {{param}}", "reach_maximum_ssid": "Nombre maximum de SSID : {{param}}", "smmp_configuration_mismatch": "Incompatibilité de configuration SNMP", "ssh_not_supported": "Échec de la connexion au client SSH", "unable_to_set_port": "Impossible de définir le port {{port}}", "unknown_error": "<PERSON><PERSON><PERSON> inconnue"}, "cli_Script": "Script CLI", "cli_session_timeout": "La session CLI a expiré", "confirm_selected_devices": "Appareils sélectionnés", "description": "Description", "execute_cli_fail": "Impossible d'exécuter le script CLI", "execute_cli_object": "Exécuter un script CLI", "execute_cli_result_hint": "Si vous quittez cet écran, vous pouvez télécharger les résultats d'exécution depuis Scripts CLI enregistrés > Résultats d'exécution.", "execute_cli_results": "Résultats d'exécution de la CLI", "execute_cli_script": "Exécuter le script CLI", "failed": "<PERSON><PERSON><PERSON>", "finished": "<PERSON><PERSON>", "in_progress": "En cours ...", "ip": "IP", "model": "<PERSON><PERSON><PERSON><PERSON>", "name": "Nom du script CLI", "no_cli_object_hint": "Aucun script CLI trouvé. Ajoutez d'abord un script CLI à partir des scripts CLI enregistrés", "not_sent": "Pas envoy<PERSON>", "result": "Résultat", "save_as_cli_object": "Enregistrer en tant que script CLI", "save_cli_object": "Enregistrer le script CLI", "select_cli_object": "Sélectionnez un script CLI", "ssh_connection_timeout": "Expiration du délai de connexion SSH", "status": "Statut"}, "firmware-management": {"action": "Action", "add-task": "Ajouter une tâche", "add-to-schedule": "Mise à niveau programmée", "api-message": {"add-schedule-fail": "Impossible de planifier la tâche", "add-schedule-success": "Tâche planifiée", "delete-schedule-fail": "Impossible de supprimer le programme d'intervalle de vérification", "delete-schedule-success": "Vérifier le programme d'intervalle supprimé avec succès", "fm-downloaded": "Fichier du micrologiciel téléchargé avec succès", "fm-downloading": "Téléchargement du fichier du micrologiciel", "fm-ready": "Fichier de firmware prêt", "get-data-failed": "Impossible de récupérer les données", "get-download-fm-failed": "Impossible de télécharger le fichier du firmware", "get-release-note-failed": "Impossible de récupérer les notes de version", "get-srs-status-failed": "Impossible d'interroger l'état du serveur du micrologiciel Moxa", "ignored-model-fail": "Impossible d'ajouter un modèle à la liste des modèles ignorés"}, "check-Firmware-status": "Vérifier l'état du micrologiciel", "check-interval": "Vérifier l'intervalle", "check-now": "<PERSON><PERSON><PERSON><PERSON>ant", "connected": "Connecté", "description": "Description", "disconnected": "Débranché", "download-csv-report": "Télécharger le rapport CSV", "download-pdf-report": "Télécharger le rapport PDF", "execution-time": "Temps", "firmware-upgrade-sequential": "Mise à niveau du micrologiciel (séquentielle stricte)", "firmware-upgrade-smart-concurrent": "Mise à niveau du micrologiciel (Smart Sequential)", "ignore-report": "Ignorer le rapport", "ignore-report-desc1": "Êtes-vous sûr de vouloir ignorer le téléchargement du rapport ?", "ignore-report-desc2": "Si vous quittez cette page, le rapport ne sera plus disponible au téléchargement.", "ignored-models": "<PERSON><PERSON><PERSON><PERSON> i<PERSON>", "last-update": "Dernière vérification", "models": "<PERSON><PERSON><PERSON><PERSON>", "moxa-firmware-server-status": "État du serveur du micrologiciel Moxa", "no-information": "Pas d'information disponible", "none": "Aucun", "offline-desc": "Pas d'information disponible. Aucune connexion précédente au serveur de mise à jour du micrologiciel. Assurez-vous que l'appareil est connecté à Internet et réessayez.", "proceeding-firmware-upgrade": "État de la mise à niveau du micrologiciel", "proceeding-upgrade-result": "Résultat de la mise à niveau du micrologiciel", "release-note": "Notes de version", "repeat-execution": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retry-Failed-devices": "Réessayer les appareils en panne", "select-devices": "Sélectionnez les appareils", "select-firmware": "Sélectionnez le micrologiciel", "set-upgrade-sequence": "<PERSON><PERSON><PERSON> de mise à niveau", "sign-here": "Signature", "start-date": "Date", "status": {"failed": "<PERSON><PERSON><PERSON>", "finished": "<PERSON><PERSON>", "in-progress": "En cours", "waiting": "En attendant"}, "table-header": {"alias": "<PERSON><PERSON>", "current-version": "Version actuelle", "device-status": "Statut du périphérique", "ip": "IP", "latest-version-on-firmware-server": "Dernière version sur le serveur du micrologiciel", "model-series": "Série de modèles", "order": "Commande", "selected-firmware-ready": "État de téléchargement du micrologiciel sélectionné", "selected-version": "Version sélectionnée", "status": "Statut"}, "task-name": "Nom de la tâche", "title": "Gestion du micrologiciel", "turn-off-check-interval": "Désactiver l'intervalle de vérification", "turn-on-check-interval": "Activer l'intervalle de vérification", "unable-to-download-firmware": "Échec du téléchargement du micrologiciel", "update-mode": "Mode de mise à jour", "upgrade-desc1": "Impossible de déterminer la séquence de mise à jour. Veuillez d'abord ajouter l'ordinateur exécutant MXview One à la topologie.", "upgrade-desc2": "La configuration actuelle prend uniquement en charge les mises à niveau simultanées du micrologiciel des appareils. Pour utiliser les méthodes de mise à niveau Strict Sequential ou Smart Sequential, vous devez d'abord ajouter l'ordinateur exécutant MXview One à la topologie.", "upgrade-firmware-report": "Rapport de mise à niveau du micrologiciel", "upgrade-now": "Mettre à jour maintenant", "upgrade-state-desc": "La mise à niveau du micrologiciel peut prendre un certain temps. Veuillez attendre la fin du processus de mise à niveau.", "version": "Version"}, "general": {"common": {"action": "Action", "allow": "Autoriser", "any": "N'importe quel", "deny": "<PERSON><PERSON>", "description": "Description", "deviceInUse": "Appareil en cours d'utilisation", "deviceName": "Nom du dispositif", "disabled": "Désactivé", "enabled": "Activé", "endDate": "Date de fin", "filters": "Filtres", "firmwareVersion": "Version de microprogramme", "group": "Groupe", "index": "Index", "ipAddress": "Adresse IP", "location": "Emplacement", "mac": "Adresse MAC", "name": "Nom", "online": "En ligne", "options": "Options", "productModel": "modèle du produit", "profileInUse": "Profil en cours d'utilisation", "refCount": "Les références", "serialNumber": "Numéro de série", "startDate": "Date de début", "status": "Statut", "title": "Titre"}, "dialog": {"deleteMsg": "Êtes-vous sûr de vouloir supprimer l'élément {{ item }} sélectionné ?", "deleteTitle": "Supprimer {{ item }}", "isSelected": "{{ number }} é<PERSON><PERSON>(s) sélectionné(s)", "title_system_message": "Message système", "unsaved_hint_content": "Etes-vous sûr de vouloir quitter cette page ?\nLes modifications que vous avez apportées ne seront pas enregistrées.", "unsaved_hint_title": "Partir sans économiser", "warning": "Avertissement"}, "fileDrop": {"browse": "<PERSON><PERSON><PERSON><PERSON>", "dropText": "Faites glisser et déposez un fichier ici, ou"}, "item_selected": "{{ number }} é<PERSON><PERSON>(s) sélectionné(s)", "log": {"localStorage": "Stockage local", "logDestination": "Destination du journal", "snmpTrapServer": "Serveur de trappes SNMP", "syslogServer": "Serveur Syslog", "title": "Journal des événements"}, "menu": {"jump_page_placeholder": "Appuyer sur Alt+J pour sauter la page"}, "page_state": {"application_error": "Erreur de l'application : (", "application_error_desc": "Une erreur s'est produite lors du traitement de cette demande.", "back_link": "Retour à la page Index", "page_not_found": "Page introuvable : (", "page_not_found_desc": "L'URL demandée est introuvable sur ce serveur."}, "severity": {"alert": "<PERSON><PERSON><PERSON>", "critical": "Critique", "debug": "Déboguer", "emergency": "Urgence", "error": "<PERSON><PERSON><PERSON>", "high": "<PERSON><PERSON><PERSON>", "information": "Informations", "informational": "Informational", "low": "Faible", "medium": "<PERSON><PERSON><PERSON>", "notice": "<PERSON><PERSON>", "title": "Gravité", "warning": "Avertissement"}, "shortWeekday": {"fri": "Ven.", "mon": "<PERSON>n.", "sat": "<PERSON><PERSON>.", "sun": "Soleil.", "thu": "<PERSON><PERSON>.", "tue": "Mar.", "wed": "<PERSON><PERSON><PERSON>."}, "table": {"add": "Ajouter", "delete": "<PERSON><PERSON><PERSON><PERSON>", "download": "Télécharger", "downloadAllLogs": "Télécharger tous les journaux", "edit": "Modifier", "filter": "Filtre", "info": "Infos", "more": "Plus", "permissionDenied": "Permission refusée", "reboot": "Redémarrage", "refresh": "Actualiser", "reorderFinish": "Terminer la réorganisation", "reorderPriority": "Réorganiser les priorités", "search": "<PERSON><PERSON><PERSON>", "transfer": "Transfert", "upgrade": "Mettre a niveau"}, "top_nav": {"api_doc": {"title": "Référence de l'API"}, "hide_recent_event": "Cacher les menus de navigation", "notifications": {"message_content": "Contenu de l'événement", "message_readall": "Plus de notifications", "message_title": "Titre de l'événement", "notification_header": "Notifications"}, "show_recent_event": "Afficher les menus de navigation", "user_profile": {"advanced_mode": "Mode avancé", "change_pwd": "Modifier le mot de passe", "greeting": "Bonjour", "logout": "Se déconnecter", "manage_account": "<PERSON><PERSON><PERSON> le compte", "reset_factory_default": "Réinitialiser aux valeurs par défaut", "restart_machine": "Redémarrer la machine", "search": "Saisissez un mot-clé à rechercher"}}, "topNav": {"caseInsensitive": "Insensible à la casse", "changePwd": "Modifier le mot de passe", "changeSuccess": "Votre mot de passe a été mis à jour avec succès. Veuillez vous reconnecter.", "confirmNewPwd": "Confirmer le nouveau mot de passe", "currentPwd": "Mot de passe actuel", "invalidKey": "Les noms suivants sont réservés : admin, opérateur, visualiseur, root, administrateur, auditeur", "logout": "Se déconnecter", "logoutMsg": "Êtes-vous sûr de vouloir vous déconnecter?", "newPwd": "nouveau mot de passe", "subject": "Sujet", "troubleshoot": "Dépannage", "troubleshootMsg": "Vous pouvez exporter les journaux de débogage vers l'hôte local pour le dépannage.", "updateAuthority": "Mettre à jour l'autorité", "updateSuccess": "L'autorisation de votre compte a été modifiée. Veuillez vous reconnecter.", "username": "Nom d'utilisateur"}, "unit": {"days": "jours)", "entries": "entrées", "minute": "Minute", "minutes": "minute (s)", "months": "mois)", "percent": "%", "pkts": "paquet/s", "sec": "seconde.", "seconds": "seconde(s)", "thousand": "mille"}, "weekday": {"friday": "<PERSON><PERSON><PERSON><PERSON>", "monday": "<PERSON><PERSON>", "saturday": "<PERSON><PERSON>", "sunday": "<PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON>"}}, "GLOBAL_MESSAGE": {"update_fail": "Échec de mise à jour", "update_success": "Mis à jour avec succés"}, "GROUP_PROPERTIES": {"description": "Description", "devices": "Dispositifs (Normal/Avertissement/Critique)", "information": "Informations", "name": "Nom", "title": "Propriétés du groupe"}, "IMAGE": {"deviceSizeError": "La taille maximale des images est de 100KB", "error": "MXview One prend en charge uniquement les formats jpg, gif et png", "sizeError": "La taille maximale de l'image est de 1MB"}, "inventory_management": {"active": "Valide", "alias": "<PERSON><PERSON>", "assets_list": "Liste des actifs", "available": "Disponible", "channel_extended_end_date": "Date de fin de garantie prolongée du canal", "channel_extended_warranty_end_date_hint": "Si vous disposez d'un accord de garantie prolongée avec votre fournisseur de canal Moxa, saisissez manuellement la date d'expiration prolongée ici.", "check_warranty_manually": "Vérifier manuellement la garantie", "check_warranty_status": "Vérifier l'état de la garantie", "days": "<PERSON><PERSON> avant", "email_example": "<EMAIL>", "email_to": "Envoyer à", "expire_soon": "Expire bient<PERSON>t", "expired": "Expiré", "firmware_version": "Version Du Firmware", "invalid_email_desc": "Adresse e-mail invalide", "ip": "IP", "last_update": "Dernière Mise À Jour", "mac_address": "<PERSON><PERSON><PERSON>", "model": "<PERSON><PERSON><PERSON><PERSON>", "multiple_email_hint": "Vous pouvez ajouter plusieurs adresses e-mail de destinataires, séparées par une virgule.", "no_data": "s.o.", "notify_before": "Envoyer un rappel", "retrieve_data": "Récupérer des données", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "serial_number": "Numéro <PERSON>", "type": "Recherché par", "unable_query_warranty_server_status": "Impossible d'accéder au serveur de garantie <PERSON>.", "unable_retrieve_warranty_information": "Impossible de récupérer les informations de garantie.", "unavailable": "Indisponible", "warranty_end_date": "Date de fin de garantie", "warranty_end_date_notification": "Notifications d'expiration de la garantie", "warranty_management": "Gestion de la garantie", "warranty_notification": "Notifications de garantie", "warranty_period": "<PERSON><PERSON><PERSON> garantie", "warranty_server_status": "Statut du serveur de garantie Mo<PERSON>", "warranty_start_date": "Date de début de garantie", "warranty_status": "Statut de la garantie"}, "INVENTORY_REPORT": {"alias": "<PERSON><PERSON>", "filter": "Taper pour filtrer le rapport d'inventaire", "fw_version": "Version microprogramme", "ip_address": "Adresse IP", "mac": "Adresse MAC", "model": "<PERSON><PERSON><PERSON><PERSON>", "property": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "report_generate_day": "Date de génération du rapport:", "site_name": "Nom du site", "system_desc": "Description du système", "title": "Rapport d'inventaire", "value": "<PERSON><PERSON>"}, "IP_CONFIGURATION": {"auto_ip": "Adresse IP automatique", "change_ip_fail": "Impossible de définir la configuration IP", "change_ip_success": "La configuration IP du dispositif est mise à jour", "gateway": "<PERSON><PERSON><PERSON>", "hint": "TCette fonction n'est pas disponible pour les appareils de la couche 3.", "ip_address": "Adresse IP", "netmask": "<PERSON><PERSON>", "title": "Configuration IP"}, "ip_conflict_detected_notification": "Conflit IP détecté", "ip_conflict_recovery_notification": "Conflit IP résolu", "ips_configuration": {"dialog-title": "Configuration IPS", "execute_fail": "Failed to execute", "input-ips": "IPS", "option-detection-mode": "Mode de détection", "option-prevention-mode": "Mode de prévention", "selet-ips-operation-mode": "Mode de fonctionnement IPS", "th-execution-status": "Statut"}, "IPSEC": {"connection_name": "Nom de la connexion", "ipsec_status_phase1": "État IPSec Phase 1", "ipsec_status_phase2": "État IPSec Phase 2", "local_gateway": "Passerelle locale", "local_subnet": "Sous-réseau local", "remote_gateway": "Passerelle distante", "remote_subnet": "Sous<PERSON><PERSON><PERSON><PERSON> distant"}, "IW": {"Message": {"CONNECT_TIMEOUT": "Expiration du délai de la connexion", "ERROR_OCCURRED": "Erreur du serveur. Réessayez après quelques instants.", "FAILED": "Échec", "LOAD_DATA_FAILED": "Erreur lors du chargement des données !", "RSSI_SNR_ONLY": "(Force du signal et SNR uniquement)", "SET_SUCCESS": "Paramètre modifié correctement", "SUCCESSED": "<PERSON><PERSON><PERSON><PERSON>", "UPDATE_FAILED": "Échec de la mise à jour des données !"}, "Title": {"ap": "AP", "AUTO_REFREASH": "Actualisation automatique : ", "AUTO_REFRESH": "Actualisation automatique", "BSSID": "BSSID", "channel": "Channel", "client": "Client", "client_count": "Nombre de clients", "client_router": "Client-Router", "CLOSE": "<PERSON><PERSON><PERSON>", "COLOR": "<PERSON><PERSON><PERSON>", "COLUMN": "<PERSON><PERSON><PERSON>", "CONDITIONS": "Condition", "CONN_TIME": "Durée de la connexion (s)", "connected": "Connecté", "DEVICE_NAME": "Nom du dispositif", "disable": "Disable", "ENABLE": "Activer", "FILTER_TABLE_VIEW": "Filtrer l'affichage de la table", "hint": "This page will be removed in a future product release. Please use Wireless Add-on in MXview One instead.", "IP_ADDR": "Adresse IP", "link_speed": "Vitesse de liaison", "MAC": "Adresse MAC", "master": "<PERSON><PERSON><PERSON><PERSON>", "MODULATION": "Modulation", "noise_floor": "Noise Floor", "noise_floor_unit": "Noise Floor (dBm)", "OK": "OK", "ONLINE": "En ligne", "operation_mode": "Mode de fonctionnement", "RSSI": "Force du signal (dBm)", "security_mode": "Mode de sécurité", "signal_level": "Signal Level", "slave": "Esclave", "SNR": "SNR (dB)", "SNR_A": "SNR-A (dB)", "SNR_B": "SNR-B (dB)", "ssid": "SSID", "TOTAL_AP": "Nombre d'AP : ", "TOTAL_CLIENT": "Nombre de clients : ", "tx_power": "Puissance TX", "tx_power_unit": "Puissance TX (dBm)", "tx_rate": "Débit de transmission TX", "tx_rate_unit": "Débit de transmission TX (Mb/s)", "uptime": "Disponibilité", "VALUE": "<PERSON><PERSON>", "WIRELESS_TABLE_VIEW": "Résumé des périphériques sans fil"}}, "JOB_SCHEDULER": {"add_failed": "Échec de l'ajout de la tâche", "add_success": "Une nouvelle tâche est ajoutée", "add_title": "Ajouter une nouvelle tâche", "alias": "<PERSON><PERSON>", "auto_topology": "Topologie automatique", "cli_object_name": "Nom du script CLI", "config_file": "Fichier de configuration", "config_file_error": "MXview One prend en charge uniquement le format .ini", "config_file_size_error": "La taille maximale des fichiers est de 1 MB", "current_filename": "Nom de fichier actuel", "current_version": "Version actuelle", "daily": "Quotidien", "database_backup": "Sauvegarde de la base de données", "delete_failed": "Échec de la suppression de la tâche", "delete_success": "La tâche est supprimée", "description": "Description", "edit_title": "Modifier la tâche", "excute_cli_object": "Exécuter le script enregistré", "execution_time": "Durée d'exécution", "export_configuration": "Exporter la configuration", "filter": "Taper pour filtrer les tâches", "fm_sequential": "<PERSON><PERSON><PERSON><PERSON>", "fm_smart": "<PERSON><PERSON><PERSON><PERSON>", "friday": "<PERSON><PERSON><PERSON><PERSON>", "import_configuration": "Importer la configuration", "ip": "IP", "job_action": "Action", "job_log": "Journal des tâches", "job_name": "Nom de la tâche", "model_series": "Série de modèles", "monday": "<PERSON><PERSON>", "monthly": "<PERSON><PERSON><PERSON>", "on": "Activé", "once": "Une fois", "order": "Commande", "registered_devices": "Dispositifs enregistrés", "repeat_execution": "Répéter l'exécution", "saturday": "<PERSON><PERSON>", "schedule_time": "Heure de planification", "selected_version": "Version sélectionnée", "show_log_fail": "Afficher l'échec du journal", "show_log_not_found": "Aucun journal ne peut être affiché", "start_date": "Date de début", "sunday": "<PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "title": "Planificateur de maintenance", "tuesday": "<PERSON><PERSON>", "update_failed": "Échec de la mise à jour de la tâche", "update_success": "La tâche est mise à jour", "wednesday": "<PERSON><PERSON><PERSON><PERSON>", "weekly": "Hebdomadaire"}, "LANG": {"de-DE": "De<PERSON>ch", "en-US": "English", "es-ES": "Español", "fr-FR": "Français", "ja-JP": "日本語", "ru-RU": "Русский язык", "zh-CN": "中文(简体)", "zh-TW": "中文(繁體)"}, "LICENSE": {"activation_code": "Code d'activation", "activation_code_error": "Code d'activation non valide", "activation_title": "Activation", "active": "Activer", "active_desc_1": "Consultez", "active_desc_2": "http://license.moxa.com/", "active_desc_3": "et renseignez le Code produit et le Code utilisateur pour obtenir votre code d'activation.", "add_fail": "Impossible d'ajouter la licence", "add_license": "Ajouter une licence", "add_new_license_desc": "Vous pouvez ajouter une licence ici", "add_new_license": {"activate": "Activer", "activate_intro_link": "site de Licence Moxa", "activate_intro_pre": "Téléchargez la licence sur le ", "activate_intro_suf": ", et collez le Code d'activation ici.", "copy_user_code": "Copier le code utilisateur", "copy_user_code_intro_link": "site de Licence Moxa", "copy_user_code_intro_pre": "Copiez le Code utilisateur sur le ", "license_site_step_1_link": "site de Licence Moxa", "license_site_step_1_pre": "1. Connexion au ", "license_site_step_2": "2. <PERSON><PERSON><PERSON>z \"Activate Your License\" et \"MXview One\" sur le site", "license_site_step_3": "3. Code d'enregistrement", "license_site_step_3_Free_step": "Passez à l'étape suivante.", "license_site_step_3_Free_title": "Utilisateur de la version gratuite: ", "license_site_step_3_Full_step": "Saisissez le Code d'enregistrement et le Code utilisateur sur le site de Licence Moxa. Le Code utilisateur devrait être obtenu aux étapes suivantes.", "license_site_step_3_Full_title": "Utilisateur de la licence complète: ", "login_license_site": "Connexion au site de Licence Moxa", "select_network_adapter": "Sélectionner un adaptateur réseau", "title": "Ajouter une nouvelle licence"}, "add_success": "La licence a été ajoutée", "copied_to_clipboard": "Copier dans le Presse-papiers", "copy_deactivation_code": "Copier le code de désactivation", "copy_older_license_code": "Copier le code de licence 2.x", "current_nodes": "Nœuds actuels:", "deactivate": "Désactiver", "deactivate_fail": "Impossible de désactiver la licence", "deactivate_success": "La licence a été désactivée", "deactivated_licenses": "Licences désactivées", "deactivating": "Désactivation…", "deactivation_code": "Code de désactivation", "deactivation_desc": "La licence ne sera pas valide après sa désactivation. Êtes-vous sûr de vouloir désactiver la licence ?", "deactivation_title": "Désactivation", "disabled": "Désactivé", "duration": "<PERSON><PERSON><PERSON>", "enabled": "Activé", "expired_license": "Licences expirées", "free_trial": "Évaluation gratuite", "free_trial_desc": "Démarrer pour découvrir la puissance de MXview One", "import_license_file": "Importer le fichier de licence", "license": "Licence :", "license_authorized": "Autorisé", "license_free": "Licence gratuite", "license_none": "Aucune", "license_site": "Site de Licence Moxa", "license_start": "Démarrage de la licence", "license_title": "Licence", "license_trial": "Évaluation", "license_type": {"node_base_intro": "Fournit le nombre de dispositifs que MXview One peut surveiller dans le réseau.", "node_base_title": "Licence basée sur le nœud", "power_addon": "Licence complémentaire d'alimentation", "power_intro": "Permet aux utilisateurs d'accéder à davantage de fonctions liées à l'alimentation.", "security_addon": "Licence complémentaire de sécurité", "security_intro": "Permet aux utilisateurs d'accéder à des fonctions supplémentaires liées à la sécurité.", "title": "Type de licence", "trial_intro": "Vous pouvez découvrir la puissance de MXview One pendant 90 jours.", "trial_title": "Licence d'essai", "wireless_addon": "Licence complémentaire sans fil", "wireless_intro": "Permet aux utilisateurs d'accéder à davantage de fonctions liées au sans fil."}, "licensed_node": "Nœud sous licence", "licensed_nodes": "Nœuds sous licence:", "licenses": "Licences", "managed_by_central": "The License is managed by MXview One Central", "managed_by_central_licenses_invalidated": "No valid licenses, please check the status in Control Panel.", "mxview": "MXview One", "network_adapter": {"button": "Sélectionner un adaptateur réseau", "change_network_adapter": "Changer l'adaptateur r<PERSON><PERSON>", "change_network_adapter_alert_1": "Êtes-vous sûr de vouloir changer l'adaptateur réseau ?", "change_network_adapter_alert_2": "Toutes les licences seront désactivées lorsque vous aurez cliqué sur \"Confirmer\". Vous ne serez plus en mesure d'utiliser MXview One jusqu'à l'enregistrement de la nouvelle licence avec le nouvel adaptateur réseau.", "intro": "MXview One associe la licence à un adaptateur réseau. Veuillez choisir l'adaptateur que vous souhaitez relier. Sélectionner à nouveau un adaptateur réseau désactivera automatiquement toutes les licences, vous devrez les enregistrer à nouveau.", "select_adapters": "Sélectionner des adaptateurs", "select_adapters_desc": "Veuillez sélectionner un adaptateur réseau', MXview One l'utilise pour générer votre code utilisateur.", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "node": "Nœuds actuels/Nœuds sous licence :", "nodes": "<PERSON><PERSON><PERSON>", "older_license": "2.x License", "older_license_nodes": "Nœuds 2.x", "over_nodes_desc": "Vous êtes déconnecté, car le nombre de nœuds surveillés dépasse le nombre de licences prises en charge.", "over_nodes_title": "Avertissement", "power_addon_trial": "Commencez à expérimenter le Power Add-on dans MXview One.", "reactivate_license": {"activate": "Activer", "activate_intro_link": "site de Licence Moxa", "activate_intro_pre": "Téléchargez la licence sur le ", "activate_intro_suf": ", et collez le Code d'activation ici.", "copy_deactivate_code": "Copier le code de désactivation", "copy_deactivate_code_intro_link": "site de Licence Moxa", "copy_deactivate_code_intro_pre": "Copiez le Code de désactivation et collez-le sur le ", "copy_user_code": "Copier le code utilisateur", "copy_user_code_intro_link": "site de Licence Moxa", "copy_user_code_intro_pre": "Copiez le Code utilisateur sur le ", "intro": "Utilisez le Code de désactivation ainsi qu'un Code utilisateur pour réactiver votre licence.", "license_site_step_1_link": "site de Licence Moxa", "license_site_step_1_pre": "1. Connexion au ", "license_site_step_2": "2. Cliquez sur \"MXview One Deactivation\" et \"Transfer to another device\" sur le site.", "license_site_step_3": "3. Sélectionnez MXview One dans Produit logiciel", "login_license_site": "Connexion au site de Licence Moxa", "title": "Réactiver la licence", "title_abbr": "<PERSON><PERSON><PERSON><PERSON>"}, "reason": "État", "relaunch": {"activating": "Activation en cours...", "active_note": "L'opération se terminera dans 10 secondes."}, "remain": "Reste", "security_addon_trial": "Commencez à découvrir le module complémentaire de sécurité MXview One", "select_network_interface": "Sélectionnez l'interface réseau", "site_license_invalid": "Vous avez des licences non valides sur des sites", "site_license_invalid_title": "Licence non valide", "start_free_trial": "Démarrer l'essai", "start_free_trial_fail": "Échec du démarrage de l'évaluation gratuite", "start_free_trial_success": "L'évaluation gratuite a démarré", "state": "État :", "state_all_licenses_invalidated": "Aucune licence valide", "state_cannot_add_free_license": "Impossible d'ajouter une licence gratuite lorsque vous possédez des licences complètes", "state_cannot_add_multiple_free_licenses": "Impossible d'ajouter plusieurs licences gratuites", "state_format_incorrect": "Le format de fichier de licence est incorrect", "state_general_error": "<PERSON><PERSON><PERSON>", "state_license_deactivated": "La licence est déjà désactivée", "state_license_expired": "La licence a expiré", "state_license_is_registered": "La licence est déjà enregistrée dans le système", "state_license_not_found": "Licence introuvable", "state_license_over_2000": "Votre licence a dépassé les 2 000 nœuds, ce qui correspond à la capacité maximale de MXview One.", "state_license_upgrade_error": "Impossible d'ajouter la licence mise à niveau. MXview One nécessite au minimum une licence complète.", "state_license_upgrade_no_full_license ": "Impossible de supprimer la licence. <PERSON><PERSON> d'abord supprimer toutes les licences mises à niveau.", "state_no_full_license": "Pas de licence MXview One", "state_no_usercode": "Aucun code utilisateur", "state_over_nodes": "Achetez des nœuds supplémentaires, car il n'y a pas suffisamment de nœuds disponibles pour le nombre de dispositifs que vous souhaitez déployer. ", "state_trial_expired": "Évaluation expirée", "state_trial_is_began": "L'évaluation a déjà commencé", "state_trial_not_activated": "L'évaluation n'est pas encore activée", "state_usercode_deactivated": "Le code utilisateur est désactivé", "state_usercode_exists": "Il existe déjà un code utilisateur", "state_usercode_not_match": "Le code utilisateur d'une licence ne correspond pas au code utilisateur du système", "state_usercode_not_match_adapter": "Le code utilisateur lié à l'adaptateur réseau est introuvable", "title": "Gestionnaire de licences", "trial_button": "ÉVALUATION", "trial_day": "Jours", "trial_expired": "Évaluation expirée", "trial_over_nodes": "MXview One sera verrouillé au bout de 30 minutes si vous n'ajoutez pas suffisamment de licences ou si vous ne supprimez pas les nœuds qui dépassent le nombre d'utilisations.", "trial_remaining": "Évaluation restante", "user_code": "Code utilisateur :", "valid": "Valide", "wireless_addon_trial": "Commencez à découvrir le module d'extension sans fil dans MXview One"}, "link_list": {"rx": "RX (%)", "tx": "TX (%)"}, "LINK_PROPERTIES": "Propriétés de la liaison", "LOGIN": {"account_reach_limit": "La limite de compte de connexion est dépassée (10)", "all_sites_offline": "Tous les sites sont en ligne", "default_password_warning": "Modifiez le mot de passe par défaut pour bénéficier d'un niveau de sécurité plus élevé", "error": "Nom d'utilisateur/mot de passe non valide", "ie_not_supported": "MXview One ne prend pas en charge IE. Veuillez utiliser Google Chrome pour une meilleure expérience.", "last_login_fail": "Les derniers enregistrements d'échec de connexion", "last_login_succeed": "La dernière durée de connexion réussie était", "login_fail_time": "{{loginTime}} de {{loginIp}}", "login_succeed_time": "{{loginTime}} de {{loginIp}}", "logout": "Se déconnecter", "password": "Mot de passe", "password_policy_mismatch": "Le mot de passe ne correspond pas à la politique de mot de passe", "sign_in": "Se connecter", "username": "Nom d'utilisateur", "welcome": "Bienvenue"}, "max_char": "Maximum de {{num}} caractères", "min_char": "Minimum de {{num}} caractères", "model-port-mapping": {"port": "port", "web-ui": "Interface utilisateur Web sur l'appareil"}, "MXVIEW_WIZARD": {"complete_page_title": "<PERSON><PERSON><PERSON><PERSON>", "navigate_to_wizard_page": "<PERSON><PERSON><PERSON><PERSON>-vous utiliser l'assistant de configuration MXview One", "step_add_scan_range": "Ajouter une plage d'analyse", "step_auto_topology": "Dessiner la topologie (pour les dispositifs prenants en charge LLDP)", "step_create_group": "Créer un groupe", "step_select_site": "Veuillez sélectionner un site à configurer", "step_set_snmp": "Définir le paramètre SNMP", "step_set_trap_server": "Définir le serveur d'interception SNMP", "title": "Assistant de configuration", "welcom_page_title": "Bienvenue dans l'assistant de configuration"}, "NETWORK_MENU": {"add_link": "Ajouter une liaison", "add_wifi_ssid": "Ajouter un SSID Wi-Fi", "alignment": {"bottom": "Alignement en bas", "left": "Alignement à gauche", "right": "Alignement à droite", "title": "Alignement", "top": "Alignement en haut"}, "copy_device_list": "Copier la liste des dispositifs", "create_a_snapshot": "<PERSON><PERSON><PERSON> un instantané", "cybersecurity_control": "Contrôles de sécurité du réseau", "delete": "<PERSON><PERSON><PERSON><PERSON>", "device_configuration": "Configuration de l'appareil", "device_control": "contrôle des équipements", "device_dashboard": "Tableau de bord de périphérique", "device_login_account": "compte d'appareil", "device_panel": "Panneau des dispositifs", "device_wireless_settings": "Per-device Parameters", "disable_unsecured_http_and_telnet_console": "Désactivez les consoles HTTP et Telnet non sécurisées", "disable_unused_ethernet_and_fiber_ports": "Désactivez les ports Ethernet et fibre optique inutilisés", "document": {"menu": {"open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "set": "Définir"}, "title": "Document"}, "dynamic_mac_sticky": "Sticky MAC dynamique", "edit": {"menu": {"add_device": "Ajouter un dispositif", "delete_background": "Supprimer l'arrière-plan", "export_device_list": "Exporter la liste des dispositifs", "export_topology": "Exporter la topologie", "import_device_list": "Importer la liste des dispositifs", "set_background": "Définir l'arrière-plan"}, "title": "Modifier"}, "execute_cli": {"menu": {"execute_cli_object": "Exécuter le script enregistré", "execute_cli_script": "Scripts CLI"}, "title": "Script de lancement"}, "grid": {"menu": {"import_scd": "Importer SCD"}, "title": "Puissance"}, "group": {"menu": {"change_group": "Modifier le groupe", "create_group": "Créer un groupe", "group_maintenance": "Maintenance du groupe"}, "title": "Groupe"}, "grouping": "Groupe", "ips_configuration": "Configuration IPS", "ipsec_status": "État IPsec", "link_traffic": {"menu": {"packet_error_rate": "<PERSON>x d'erreurs de paquets", "port_traffic": "Trafic du port"}, "title": "Trafic de la liaison"}, "locator": "Localisateur", "mac_sticky_on_off": "Sticky MAC activé/désactivé", "maintenance": {"menu": {"advance_settings": "Paramètres avancés", "assign_model": "Attribuer un modèle", "basic_information": "Informations de base", "change_device_icon": "Modifier l'icône du dispositif", "device_identification_settings": "Paramètres d'identification de l'appareil", "eip_enable": "Activation d'EtherNet/IP", "eip_tcp_port": "Port TCP Ethernet/IP", "eip_udp_port": "Port UDP Ethernet/IP", "export_config": "Exporter la configuration", "generate_qr_code": "Générer le code QR", "import_config": "Importer la configuration", "ip_configuration": "Configuration IP", "modbus_enable": "Activation Modbus", "modbus_port": "Port Modbus", "modbus_tcp_configuration": "Paramètres Modbus TCP", "polling_ip_setting": "Paramètres de l'adresse IP d'interrogation", "polling_settings": "Intervalle d'interrogation MXview One", "port_settings": "Paramètres du port Ethernet/fibre optique", "s7_port": "Port de communication Siemens S7", "s7_status": "Activation de Siemens S7comm", "serial_port_monitoring": "Surveillance du port série", "snmp_settings": "Protocole de communication SNMP", "trap_server": "<PERSON><PERSON>ur d'interception", "upgrade_firmware": "Mettre à niveau le microprogramme"}, "title": "Maintenance"}, "modify_device_alias": "Alias ​​de l'appareil", "policy_profile_deployment": "Déploiement du profil de politique", "reboot": "Redémarrage", "refresh": "Actualiser", "restart_mac_sticky_learning": "Réapprendre le Sticky MAC dynamique", "restore_to_created_snapshot": "Restaurer à partir d'un instantané", "scale": "Scale", "security_package_deployment": "Déploiement du package de sécurité", "set_port_label": "Définir l'étiquette du port", "severity_threshold": "<PERSON><PERSON> g<PERSON>", "sfp": "SFP", "sfp_Info": "Informations relatives au SFP", "sfp_list": "Liste SFP", "sfp_sync": "Seuil de synchronisation du dispositif", "tools": {"menu": {"device_panel": "Panneau du dispositif", "mib_browser": "Navigateur MIB", "ping": "<PERSON>", "telnet": "Telnet", "web_console": "Console Web"}, "title": "Outils"}, "topology": {"menu": {"auto_layout": "Disposition automatique", "auto_topology": "Topologie automatique", "embed": "Widget intégré", "scan_range": "Découverte des dispositifs"}, "title": "Topologie"}, "ungrouping": "Dissocier", "upgrade_patch": "Appliquer les mises à jour du système", "visualization": {"menu": {"igmp": "IGMP", "security_view": "Affichage de la sécurité", "traffic_view": "Affichage du trafic", "vlan": "VLAN", "vlan_view": "Affichage du VLAN"}, "title": "Visualisation"}, "wifi_channel_change": "Changer de canal Wi-Fi", "wireless_settings": "General Parameters", "wireless": {"menu": {"wireless_planner_view": "Couverture sans fil", "wireless_playback_view": "Lecture roaming sans fil", "wireless_table_view": "Résumé des périphériques sans fil"}, "title": "Sans fil"}}, "NETWORK": {"current_status": {"no_event": "Aucun <PERSON>", "title": "État actuel", "v3_trap_event_clear_fail": "Échec de l'effacement de l'événement du trap V3", "v3_trap_event_suggestion": "Veuillez vérifier la configuration snmp v3"}, "not_selected": "Sélectionnez un module pour afficher les détails de l'appareil"}, "NORTHBOUND_INTERFACE": {"custom_opc": {"add_custom_opc": "Ajouter des étiquettes OPC personnalisées", "all": "Tous", "apply_fail": "Impossible d'ajouter des étiquettes OPC personnalisées", "apply_success": "De nouvelles étiquettes OPC personnalisées ont été ajoutées", "delete_fail": "Impossible de supprimer l'étiquette OPC", "delete_success": "La balise OPC est supprimée", "device_properties": "Propriétés des dispositifs", "enable": "Étiquettes OPC personnalisées activées", "filter_custom_opc": "Taper pour filtrer les étiquettes OPC personnalisées", "get_fail": "Impossible d'obtenir les étiquettes OPC personnalisées", "property_name": "Nom de propriété", "register_devices": "Enregistrer les dispositifs", "title": "Étiquettes OPC personnalisées", "update_custom_opc": "Mettre à jour les étiquettes OPC personnalisées", "update_fail": "Impossible de mettre à jour l'étiquette OPC personnalisée", "update_success": "Les étiquettes OPC personnalisées ont été mises à jour"}}, "NOTIFICATION_SETTINGS": {"action": "Action", "action_cant_deleted": "Cette action est utilisée par une ou plusieurs notifications. Désélectionnez l'action de ces notifications et supprimez-la de nouveau", "action_information": "Informations sur l'action", "action_name": "Nom de l'action", "action_tab_hint": "Allez à l'onglet Action et ajoutez d'abord une action", "action_type": "Type", "add_action": "Ajouter une action de notification", "add_action_fail": "Impossible d'ajouter l'action", "add_action_success": "Une nouvelle action est ajoutée", "add_notification": "Ajouter une notification", "add_notification_fail": "Impossible d'ajouter la notification", "add_notification_success": "Une nouvelle notification est ajoutée", "check_security_tab": "Consultez l'onglet cybersécurité", "content": "Contenu", "delete_action_fail": "Impossible de supprimer l'action", "delete_action_success": "L'action est supprimée", "delete_notification_fail": "Impossible de supprimer la notification", "delete_notification_success": "La notification est supprimée", "edit_action": "Modifier une action de notification", "edit_notification": "Modifier une notification", "email": "E-mail", "email_content_hint": "Le contenu ci-présent sera ajouté au corps de l'e-mail de notification par défaut", "event_type": "Type", "file_size_error": "La taille maximale des fichiers est de 1 Mo.", "file_type_error": "MXview One prend en charge uniquement les fichiers .wav", "filter_action": "Taper pour filtrer les actions", "filter_notification": "Taper pour filtrer les notifications", "messagebox": "<PERSON><PERSON><PERSON>", "mobile": "MXview ToGo", "mobile_number": "Numéro de mobile", "notification": "Notification", "notification_name": "Nom de la notification", "notification_name_exist": "Ce nom est déjà utilisé par une autre notification", "receiver_email": "E-mail du destinataire", "register_devices": "Dispositifs enregistrés", "register_subscribers": "Actions enregistrées", "slack": "<PERSON><PERSON>ck", "sms": "SMS", "snmptrap": "Interception SNMP", "sound": "Fichier audio", "teams": "Microsoft Teams", "testConnection": "Test de connexion", "title": "Paramètres de notification", "update_action_fail": "Impossible de mettre à jour l'action", "update_action_success": "L'action est mise à jour", "update_notification_fail": "Impossible de mettre à jour la notification", "update_notification_success": "La notification est mise à jour", "webhook": "Webhook", "webhook_fail": "Impossible d'exécuter le Webhook", "webhook_success": "Le Webhook a été envoyé"}, "OPC_UA_SERVER": {"add_opc_tags": "Ajouter une balise OPC", "anonymous": "Anonyme", "auth_setting": "Paramètres d'authentification", "certificate": "Certificat", "certificate_link": "Téléchargez et gérez les certificats depuis le", "change_authentication_password": "Modifier le mot de passe d'authentification", "change_password": "Changer le mot de passe", "control_panel": "Panneau de configuration MXview One", "create_tags_fail": "Unable to add tag", "create_tags_success": "<PERSON>se c<PERSON> avec succès", "delete_tag_content": "Êtes-vous sûr de vouloir supprimer cette balise OPC ?", "delete_tags": "Supprimer la balise OPC", "delete_tags_content": "Êtes-vous sûr de vouloir supprimer ces balises OPC ?", "delete_tags_fail": "Échec de la suppression des balises", "delete_tags_success": "Balises supprimées avec succès", "device_property": "Device Property", "disabled": "Désactivé", "edit_opc_tags": "Modifier la balise OPC", "edit_tags_fail": "Échec de la mise à jour de la balise", "edit_tags_success": "<PERSON><PERSON> mise à jour avec succès", "enable_opc_server": "Activer le serveur OPC UA", "enabled": "Activé", "exceed_server_performance": "Nombre maximum d'appareils enregistrés (4 000) atteint.", "get_tags_list_fail": "Échec de la récupération de la liste des balises", "ip_domain_name": "IP/Nom de domaine", "method": "Méthode", "opc_tags": "OPC Tags", "property_name": "Device Property", "registered_device": "Registered Device", "security": "Mode de sécurité", "security_placeholder": "N'autoriser aucune s<PERSON>rit<PERSON>", "server_settings": "Server Settings", "status": "status", "support_security_policy": "Politiques de sécurité prises en charge", "tag_name": "Tag Name", "tag_name_duplicate": "Ce nom de balise existe déjà", "tags_exceed_limit": "Nombre maximum de balises (2000) atteint.", "title": "OPC UA Server", "update_fail": "Échec de la mise à jour des paramètres", "update_server_setting_fail": "Échec de la mise à jour des paramètres du serveur", "update_server_setting_fail_no_up": "Échec de la mise à jour des paramètres. L'adresse IP spécifiée n'existe pas.", "update_server_setting_success": "Paramètres du serveur mis à jour avec succès", "username": "Nom d'utilisateur"}, "PAGES_MENU": {"about": "À propos de", "administration": {"account_management": "Gestion des comptes", "device_settings_template": "Mod<PERSON><PERSON> de périphérique par défaut", "global_device_settings": "Paramètres globaux des appareils", "license_management": "Gestion des licences", "maintenance_scheduler": "Planificateur de maintenance", "preferences": "Préférences", "system_settings": "Paramètres du système", "title": "Administration", "troubleshooting": "Dépannage"}, "alert": {"custom_events": "Custom Events", "device_threshold": "<PERSON><PERSON>", "event_settings": "Paramètres de l'événement", "link_threshold": "<PERSON>", "notifications": "Gestion des notifications", "title": "<PERSON><PERSON><PERSON>"}, "cli_object_database": {"title": "Scripts CLI enregistrés"}, "dashboard": "Tableau de bord", "device_management": {"account_password": "Comptes et mots de passe", "configuration_control": "Configuration et contrôle", "title": "Gestion d'appareils"}, "devices": {"device_configurations": "Device Configurations", "list_of_devices": "List of Devices"}, "event": {"all_events": "Historique des événements", "custom_events_management": "Év<PERSON><PERSON> person<PERSON>", "notification_management": "Gestion des notifications", "syslog_settings": "Paramètres Syslog", "syslog_viewer": "Observateur Syslog", "title": "Gestion des événements"}, "firewall_policy_management": {"dos_descr": "Configurer la politique DoS", "ips_descr": "Configurer la politique IPS", "layer3to7_descr": "Configurer la politique de pare-feu de couche 3 à 7", "policy_profile_deployment": "Déploiement du profil de politique", "policy_profile_management": "Gestion des profils de politique", "security_package_deployment": "Déploiement du package de sécurité", "security_package_management": "Gestion des packages de sécurité", "sessionControl_descr": "Configurer la politique de contrôle de session", "title": "Gestion des politiques de pare-feu"}, "firmware_management": {"title": "Gestion du micrologiciel"}, "help": {"about_mxview": "À propos de MXview One", "api_documentation": "Documentation API", "title": "Aide", "user_manual": "<PERSON> de l'utilisateur"}, "license": "Licence", "links": {"list_of_rj45_links": "List of RJ45 Links", "list_of_sfp_links": "List of SFP Links", "list_of_wifi_links": "List of Wi-Fi Links", "title": "Links"}, "migrations": {"configuration_center": "Centre de configuration des appareils", "database_backup": "Sauvegarde de la base de données", "job_scheduler": "Planificateur de maintenance", "title": "Migrations"}, "network": {"scan_range": "Plage d'analyse", "title": "<PERSON><PERSON><PERSON>", "topology": "Topologie", "wizard": "Assistant"}, "northbound_interface": {"custom_opc_tags": "Étiquettes OPC personnalisées", "opc_ua_server": "OPC UA Server", "restful_api_management": "Gestion des API RESTful", "title": "Intégration", "web_widget_embedded": "Widget Web intégré"}, "preferences": "Préférences", "report": {"assets_and_warranty": "Actifs et garantie", "availability_report": "Rapport de disponibilité", "inventory_report": "Rapport d'inventaire", "rogue_device_detection": "Détection des appareils malveillants", "title": "Rapport", "vlan": "Rapport VLAN"}, "scan_range_wizard": {"title": "Découverte des dispositifs"}, "security": {"account_management": "Gestion des comptes", "security_analyser": "Ana<PERSON><PERSON><PERSON> de sécurité", "title": "Sécurité"}}, "pages": {"deviceDeployment": {"alreadySentSms": "V<PERSON> avez d<PERSON> envoyé {{ smsNumber }}/{{ max }} SMS ce mois-ci", "applied": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "atLeastSelectOne": "Sélectionnez au moins une commande de contrôle SMS", "cellularModuleDisable": "Le module cellulaire est désactivé, l'envoi de commandes SMS n'est pas autorisé.", "cellularStartConnecting": "Connexion cellulaire", "cellularStopConnecting": "Arrêt de connexion cellulaire", "configSync": "La configuration des appareils sélectionnés sera synchronisée.", "daily": "Quotidien", "date": "Date", "deleteMsg": "Êtes-vous sûr de vouloir supprimer le(s) appareil(s) sélectionné(s) ?", "deleteSchedule": "Supprimer l'horaire", "deleteScheduleSuccess": "La planification des appareils a été supprimée avec succès", "deleteSuccess": "Les dispositifs ont été supprimés", "deleteTitle": "Supprimer le(s) périphérique(s)", "device_ip": "Adresse IP du dispositif", "deviceConfiguration": "Configuration de l'appareil", "deviceDetail": "<PERSON>é<PERSON> de l'appareil", "deviceDisableHint": "La fonction est désactivée du côté de l'appareil", "deviceSelected": "appareil(s) sélectionné(s)", "endTime": "Date de fin", "firmware": "Firmware", "firmwareUpgrade": "Le firmware des appareils sélectionnés sera mis à niveau.", "firmwareVersion": "Version de microprogramme", "general": "Général", "groupName": "Nom du groupe", "groupSelected": "groupe(s) sélectionné(s)", "invalidDate": "Date invalide", "invalidPeriod": "Période invalide", "lastRebootTime": "Heure du dernier redémarrage", "lastUpdate": "Dernière vérification", "lastUpdateTime": "<PERSON><PERSON> de la dernière mise à jour", "location": "Emplacement", "mac": "MAC", "manually": "<PERSON><PERSON>", "maxSms": "Vous avez atteint la limite mensuelle de SMS (MAX. {{ max }})", "noConfigAvailable": "Aucune configuration disponible", "noConfigMsg": "Vérifiez les configurations sur la page Gestion/Configuration du périphérique.", "noFirmwareMsg": "Vérifiez les fichiers du firmware sur la page Gestion/Firmware.", "noPackageMsg": "Vérifiez les packages de sécurité sur la page Gestion des packages de sécurité.", "noProfileAvailable": "Aucun profil disponible", "noProfileMsg": "Consultez les profils sur la page Gestion des profils de stratégie.", "notSupportModel": "<PERSON><PERSON><PERSON><PERSON> non pris en charge", "notSync": "Non synchronisé", "noVersionAvailable": "Aucune version disponible", "oneTime": "Une fois", "outOfSync": "Désynchronisation", "package": "<PERSON><PERSON>", "packageUpgrade": "Le package de sécurité des appareils sélectionnés sera mis à niveau.", "packageVersion": "Version du package", "period": "Période", "policyProfile": "Profils politiques", "processing": "En traitement", "profileName": "Nom de profil", "profileSync": "Le profil des appareils sélectionnés sera synchronisé.", "reboot": "L'appareil(s) sera(ont) redémarré(s).", "rebootDisabled": "Seuls les appareils en ligne peuvent être redémarrés.", "rebootMsg": "Êtes-vous sûr de vouloir redémarrer le(s) appareil(s) sélectionné(s) ?", "rebootTitle": "Redémar<PERSON> le(s) périphérique(s)", "remoteSmsControl": "Contrôle SMS à distance", "restoreConfigDisabled": "Seuls les appareils en ligne du même type de modèle peuvent être synchronisés.", "sameVersionWarning": "Un ou plusieurs des appareils sélectionnés ont déjà la version {{ version }} appliquée.", "schedule": "<PERSON><PERSON><PERSON>", "scheduleDisabled": "Seuls les appareils du même type de modèle peuvent être planifiés.", "scheduleOverlapMsg": "Impossible de sélectionner le créneau horaire qui attribue déjà le redémarrage ou la mise à niveau du micrologiciel.", "scheduleSettings": "Paramètres de planification", "scheduling": "Planification", "schedulingMode": "Mode de planification", "schedulingPeriod": "Période de planification", "schedulingReboot": "Planification du redémarrage", "selectConfigFile": "Sélectionner le fichier de configuration", "selectFile": "Sélectionnez le fichier", "sendSms": "Envoyer un SMS", "sendSmsControl": "Envoyer un SMS de contrôle", "sendSmsOnCell": "Sélectionnez un appareil OnCell pour envoyer un contrôle SMS", "sendSmsSuccess": "Envoyer un SMS avec succès", "serialNumber": "Numéro de série", "setDoOff": "<PERSON><PERSON><PERSON> DO Off", "setDoOn": "Activer DO", "shouldBeSameVersion": "La version du package des appareils sélectionnés doit être la même.", "shouldHaveJanus": "Un ou plusieurs des appareils sélectionnés n'ont pas de package de sécurité installé.", "shouldSyncOnline": "Seuls les appareils en ligne peuvent être synchronisés.", "showAll": "Afficher tous les groupes et appareils", "showSelected": "Afficher les groupes et les appareils sélectionnés", "smsCountDownHint": "Envoyer le prochain SMS après 60 secondes", "softwarePackage": "Paquets de sécurité", "startIpsecTunnel": "Démarrer le tunnel IPsec", "startTime": "Date de début", "status": "Statut", "statusProfileName": "Statut / Nom du profil", "stopIpsecTunnel": "Arrêter le tunnel IPsec", "switchSim": "Changer de carte SIM", "sync": "Synchronisé", "syncConfig": "Configuration de la synchronisation", "syncConfigTitle": "Synchroniser la configuration avec les appareils", "syncModified": "Synchronisé (modifié)", "syncProfile": "Synchroniser le profil", "syncProfileTitle": "Synchroniser le profil avec les appareils", "systemRestart": "Redémarrage du système", "time": "Temps", "updateScheduleSuccess": "La planification de l'appareil a été mise à jour avec succès.", "upgradeDisabled": "Seuls les appareils en ligne peuvent être mis à niveau.", "upgradePackageError": "Les versions de firmware supérieures à 2.5.0 et inférieures à 2.4.x ne peuvent pas coexister.", "upgradePackageNotSameDisabled": "Seuls les appareils du même type de modèle peuvent être sélectionnés", "upToDate": "À jour", "version": "Version", "weekly": "Hebdomadaire", "weeklyDay": "<PERSON><PERSON><PERSON>"}, "logging": {"eventLog": {"adp": "ADP", "audit": "Audit", "device": "Dispositif", "dos": "Politique DoS", "dpi": "Politique de filtrage de protocole", "endDate": "Date de fin", "endTime": "<PERSON>ure De Fin", "event": "Événement", "firewall": "Pare-feu", "ips": "IPS", "l2Policy": "Politique de couche 2", "l3Policy": "Politique des couches 3 à 7", "malformed": "Paquets mal formés", "sc": "Contrôle de session", "setting": "Réglage", "severity": "Gravité", "startDate": "Date de début", "startTime": "<PERSON><PERSON> d<PERSON>", "tab": {"audit": {"deviceName": "Nom du dispositif", "event": "Événement", "groupName": "Nom du groupe", "message": "Message", "severity": "Gravité", "time": "Temps", "username": "Nom d'utilisateur"}, "device": {"deviceName": "Nom du dispositif", "event": "Événement", "groupName": "Nom du groupe", "mac": "Adresse MAC", "message": "Message", "severity": "Gravité", "time": "Temps", "username": "Nom d'utilisateur"}, "firewall": {"action": "Action", "adp": "ADP", "all": "Tous", "appProtocol": "Protocole d'application", "category": "<PERSON><PERSON><PERSON><PERSON>", "deviceName": "Nom du dispositif", "dos": "Politique DoS", "dpi": "Politique de filtrage de protocole", "dstIp": "IP de destination", "dstMac": "Destination MAC", "dstPort": "Le port de destination", "etherType": "Type d'éther", "event": "Événement", "fromInterface": "Interface entrante", "groupName": "Nom du groupe", "icmpCode": "Code ICMP", "icmpType": "Type ICMP", "id": "Index", "ips": "IPS", "ipsCategory": "Catégorie IPS", "ipsSeverity": "Gravité IPS", "l3Policy": "Politique des couches 3 à 7", "malformed": "Paquets mal formés", "message": "Message supplémentaire", "policyId": "ID de politique", "policyName": "Nom de la politique", "protocol": "Protocole IP", "security": "Sécurité", "sessionControl": "Contrôle de session", "severity": "Gravité", "srcIp": "Adresse IP de la source", "srcMac": "Source MAC", "srcPort": "Port source", "subCategory": "Sous catégorie", "tcpFlag": "Drapeaux TCP", "time": "Temps", "toInterface": "Interface sortante", "trustAccess": "Accès sécu<PERSON>", "username": "Nom d'utilisateur", "vlanId": "ID de VLAN"}, "vpn": {"deviceName": "Nom du dispositif", "event": "Événement", "groupName": "Nom du groupe", "message": "Message supplémentaire", "severity": "Gravité", "time": "Temps", "username": "Nom d'utilisateur"}}, "trustAccess": "Accès sécu<PERSON>", "vpn": "VPN"}, "notification": {"advancedSettingMsg": "Une fois que le nombre maximum de notifications a été atteint au cours de la période donnée, aucune autre notification n'est envoyée jusqu'à la période suivante.", "advancedSettings": "Paramètres avancés", "appProtocol": "Protocole d'application", "arpFlood": "ARP-Flood", "atLeastOneReceiver": "Au moins un récepteur", "bufferOverflow": "Dépassement de tampon", "chooseDevices": "Choisir les appareils", "createdBy": "C<PERSON><PERSON> par", "createNotification": "Ajouter un événement de cybersécurité", "createSuccess": "Un événement de cybersécurité créé avec succès", "deleteFailed": "Cet événement est utilisé pour les notifications et ne peut pas être supprimé.", "deleteKey": "Événement(s) de cybersécurité", "deleteNotification": "Supprimer la notification", "deleteSuccess": "Événement(s) de cybersécurité supprimé(s) avec succès", "deviceCount": "Nombre d'appareils", "deviceName": "Nom du dispositif", "DNP3": "DNP3", "dosAttacks": "Attaques DoS", "dstIp": "IP de destination", "dstMac": "Destination MAC", "editNotification": "Modifier l'événement de cybersécurité", "EIP": "EIP", "email": "E-mail", "emailContent": "Contenu du courrier électronique", "emailContentDefault": "L'événement ${event} déclenché sur l'appareil ${productModel}, ${deviceName}, s'est produit à ${eventTime}.", "emailHeader": "[MXsecurity] Notification ${notificationName}\ngénéré à partir de ${deviceName}", "emailMsgAutoSentFrom": "Cette notification a été envoyée automatiquement\nde MXsecurity.", "emailMsgCheck": "Veuillez vérifier les informations détaillées\nsur MXsecurity.", "emailMsgGreeting": "Cher <PERSON>/Madame,", "emailMsgSignOff": "Cordialement,\nSécurité MX", "eq": "Égal à", "event": "Événement", "event_used": "Cet événement est déjà utilisé dans les paramètres de notification et ne peut pas être modifié.", "eventFilter": "Choisissez l'événement et le filtre", "eventFilterRule": "Règle de filtrage des événements", "eventTime": "Heure de l'évènement", "exploits": "Exploits", "fileVulnerabilities": "Vulnérabilités des fichiers", "filterRule": "Règle de filtrage", "filterRuleDetail": "Détail des règles de filtrage", "finScan": "FIN Scan", "floodingScan": "Inondation et numérisation", "GOOSE": "GOOSE", "gt": "Plus bas que", "gte": "Inférieur ou égal à", "icmpDeath": "ICMP-Flood", "IEC-104": "IEC-104", "ipAddress": "Adresse IP", "ipRangeHint": "Vous pouvez utiliser * pour représenter le résultat\ndu masque de sous-réseau /8/16/24, ex.\n192.168.*.*\nNe pouvait pas être utilisé au début de\nune adresse IP ou seule au milieu *", "ipsCate": "Catégorie IPS", "ipSpoofing": "Usurpation d'adresse IP", "ipsSeverity": "Gravité IPS", "location": "Emplacement", "lt": "Plus haut que", "lte": "Su<PERSON><PERSON><PERSON> ou égal à", "macAddress": "Adresse MAC", "macRangeHint": "Vous pouvez utiliser * pour représenter une plage de\nAdresse MAC, par exemple 00:90:E8:*:*:*\nNe peut pas être utilisé au début d&#39;un\nAdresse MAC ou seule au milieu.", "malwareTraffic": "Trafic de logiciels malveillants", "maxEnableSize": "Le nombre MAX. de notifications activées est de {{num}}.", "maxNotification": "MAX. Notification", "maxPerUserSize": "Le nombre MAX. de notifications par utilisateur est de {{num}}.", "MMS": "MMS", "Modbus/TCP": "Modbus/TCP", "newTcpWithoutSynScan": "TCP Sessions Without SYN", "nmapIdScan": "NMAP-ID Scan", "nmapXmasScan": "NMAP-<PERSON><PERSON>", "notificationActions": "Actions de notification", "notificationEvent": "Événement de notification", "notificationInfo": "Informations sur les notifications", "notificationLimit": "Limite de notification", "notificationName": "Nom de l'événement", "nullScan": "<PERSON><PERSON>", "OmronFINS": "Omron FINS", "periodTime": "Période de temps", "policyName": "Nom de la politique", "productModel": "modèle du produit", "protocolAttackProtection": "Protection contre les attaques de protocole", "receiverEmailAddress": "Adresse e-mail du destinataire", "receiverSetting": "Paramètres du récepteur", "reconnaissance": "Reconnaissance", "resetToDefault": "Réinitialisés", "serialNumber": "Numéro de série", "severity": "Gravité", "severityMode": "Mode de gravité", "severityRule": "<PERSON><PERSON><PERSON> gra<PERSON>", "showAllDevices": "Afficher tous les appareils", "showSelectedDevices": "Afficher les appareils sélectionnés", "srcIp": "Adresse IP de la source", "srcMac": "Source MAC", "Step7Comm": "Step7Comm", "subCate": "Sous-cat<PERSON><PERSON><PERSON>", "synFinScan": "SYN/FIN Scan", "synFlood": "SYN-Flood", "synRstScan": "SYN/RST Scan", "syslog": "Syslog", "syslogContent": "Contenu du journal système", "syslogContentDefault": "La notification ${notificationName} a été déclenchée sur l'appareil. ${productModel}, ${deviceName}, s'est produite à ${eventTime}. Veuillez consulter les informations détaillées sur MXsecurity.", "udpFlood": "UDP-Flood", "updateSuccess": "L'événement sur la cybersécurité a été mis à jour avec succès", "webThreats": "Menaces sur le Web", "xmasScan": "<PERSON><PERSON>"}}, "management": {"deviceConfiguration": {"configModel": "Mod<PERSON><PERSON> de <PERSON>", "configName": "Configurer le nom", "createSuccess": "Configuration de l'appareil créée avec succès.", "deleteKey": "Configuration(s) de l'appareil", "deleteSuccess": "Configuration(s) de l'appareil supprimée(s) avec succès", "editConfig": "Modifier la configuration", "enterConfigInfo": "Entrer les informations du fichier de configuration", "firmwareVersion": "Version de microprogramme", "group": "Groupe", "isReferenced": "Une ou plusieurs des configurations sélectionnées sont référencées.", "lastModifiedTime": "Heure de la dernière modification", "location": "Emplacement", "mac": "Adresse MAC", "maxTableSize": "La configuration maximale est {{num}}.", "noModelMsg": "Il n'y a pas de modèle pour la configuration", "offlineWarning": "Appareil hors ligne", "onlyAcceptIni": "Seuls les fichiers de configuration au format ' .ini ' sont acceptés.", "onlyOneFilePerTime": "Un seul fichier peut être téléchargé à la fois.", "selectConfigFile": "Sélectionner le fichier de configuration", "selectWarning": "Autorise un seul périphérique pour la sauvegarde de la configuration", "serialNumber": "Numéro de série", "updateSuccess": "Les paramètres de l'appareil ont été mis à jour avec succès", "uploadConfigFile": "Télécharger le fichier de configuration (.ini)", "uploadConfigMethod": "Méthode de configuration du téléchargement", "uploadConfigTitle": "Télécharger le fichier de configuration de l'appareil", "uploadDeviceConfig": "Télécharger la configuration depuis l'appareil", "uploadLocalConfig": "Télécharger la configuration depuis le local"}, "deviceGroup": {"accessPermission": "Autorisation d'accès", "addDevices": "Ajouter des appareils", "adminPermission": "Les utilisateurs administrateurs ont l'autorisation d'accéder à tous les groupes", "createGroup": "Créer un groupe", "createSuccess": "Groupe d'appareils créé avec succès.", "deleteKey": "Supprimer le(s) groupe(s) de périphériques", "deleteSuccess": "Groupe(s) d'appareils supprimés avec succès.", "description": "Description", "deviceCount": "Nombre d'appareils", "editGroup": "Modifier le groupe d'appareils", "enterGroupInfo": "Entrez les informations du groupe", "firmwareVersion": "Version de microprogramme", "grantAccessPermission": "Accorder l'autorisation d'accès", "group": "Groupe", "groupName": "Nom du groupe", "location": "Emplacement", "mac": "MAC", "role": "R<PERSON><PERSON>", "serialNumber": "Numéro de série", "showAllDevices": "Afficher tous les appareils", "showSelectedDevices": "Afficher les appareils sélectionnés", "status": "Statut", "updateSuccess": "Le groupe d'appareils a été mis à jour avec succès.", "username": "Nom d'utilisateur"}, "firmware": {"buildTime": "Temps de construction", "deleteKey": "Firmware", "deleteSuccess": "Le micrologiciel a été supprimé avec succès.", "description": "Description", "dropZoneTitle": "Télécharger un fichier de firmware (.rom)", "isReferenced": "Un ou plusieurs firmwares sélectionnés sont référencés.", "maxRowMsg": "Le nombre maximal de fichiers de firmware est de {{ max }}.", "maxSize": "La taille de fichier maximale autorisée est de 1 Go.", "modelSeries": "Série de modèles", "onlyAcceptRom": "Seuls les fichiers de firmware au format ' .rom ' sont acceptés.", "onlyOneFilePerTime": "Un seul fichier peut être téléchargé à la fois.", "uploadFirmware": "Télécharger le micrologiciel", "uploadSuccess": "Le micrologiciel a été téléchargé avec succès.", "version": "Version"}, "inUse": " O<PERSON>", "object": {"filter": {"address": "Adresse IP et sous-réseau", "code": "Code", "createObject": "<PERSON><PERSON><PERSON> un objet", "createSuccess": "Objet c<PERSON>é avec succès", "customIpProtocol": "Protocole IP personnalisé", "decimal": "(Décimal)", "deleteKey": "Objet(s)", "deleteSuccess": "Objet(s) supprimé(s) avec succès.", "detail": "Détails", "editObject": "Modifier l'objet", "endPort": "Port: Fin", "icmp": "ICMP", "icmpCode": "Code ICMP", "icmpType": "Type ICMP", "industrialAppService": "Service d'application industrielle", "ipAddress": "Adresse IP", "ipEnd": "Adresse IP : Fin", "ipProtocol": "Protocole IP", "ipRange": "Plage IP", "ipStart": "Adresse IP : <PERSON><PERSON><PERSON>", "ipType": "Type d'IP", "isReferenced": "Un ou plusieurs des objets sélectionnés sont référencés", "leaveAsAny": "Laissez vide pour représenter n'importe quel", "maxRowMsg": "Le nombre maximal d'objets est {{ max }}.", "name": "<PERSON><PERSON><PERSON>", "needSelectedMsg": "Sélectionnez au moins un élément", "networkName": "Nom du réseau", "networkService": "Service réseau", "objectName": "Nom", "objectReference": "Références d'objet", "objectReferenceMsg": "Cet objet est référencé par un index de politique dans le(s) profil(s) suivant(s) :", "objectType": "Type d'objet", "port": "Port", "portRange": "Plage de ports TCP et UDP", "selectIndustrialAppService": "Sélectionnez le(s) service(s) d'application industrielle *", "selectNetworkService": "Sélectionnez le(s) service(s) réseau *", "servicePortType": "Type de port de service", "singleIp": "IP unique", "singlePort": "Port TCP et UDP", "startPort": "Port : Début", "subnet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subnetMask": "Subnet Mask", "tcp": "TCP", "tcpUdp": "TCP et UDP", "type": "Type", "udp": "UDP", "updateSuccess": "Objet mis à jour avec succès", "userDefinedService": "Service défini par l'utilisateur"}, "interface": {"bridge": "<PERSON>", "createInterface": "<PERSON><PERSON>er un objet d'interface", "createSuccess": "Interface créée avec succès.", "deleteKey": "Interface(s)", "deleteSuccess": "Interface(s) supprimée(s) avec succès.", "editInterface": "Modifier l'objet d'interface", "interfaceName": "Nom de l'interface", "interfaceReference": "Références d'interface", "interfaceReferenceMsg": "Cette interface est référencée par un index de politique dans le(s) profil(s) suivant(s) :", "invalidKey": "Les noms suivants sont réservés : Tout", "isReferenced": "Une ou plusieurs des interfaces sélectionnées sont référencées.", "maxRowMsg": "Le nombre maximal d'interfaces est {{ max }}.", "mode": "Mode", "name": "interface", "port": "Basé sur le port", "updateSuccess": "Interface mise à jour avec succès.", "vlan": "VLAN", "vlanIdBridgeType": "ID VLAN / Mode pont", "zone": "Basé sur les zones"}}, "policyProfile": {"createProfile": "Créer un profil de politique", "createSuccess": "Profil c<PERSON> avec succès.", "deleteKey": "Profil(s)", "deleteSuccess": "Profil(s) supprimé(s) avec succès.", "deployment": {"profile_title": "Déploiement du profil de politique", "security_title": "Déploiement du package de sécurité", "title": "Déploiement du profil de politique"}, "dos": {"all_protection_types": "Activer tous les types de protection", "dosLogSetting": "Paramètres du journal DoS", "dosSetting": "Paramètres DoS", "floodProtection": "Protection contre les inondations", "limit": "Limite", "portScanProtection": "Protection par scan de port", "sessionSYNProtection": "Protection SYN de session", "stat1": "<PERSON><PERSON>", "stat10": "SYN-Flood", "stat11": "ARP-Flood", "stat12": "UDP-Flood", "stat2": "<PERSON><PERSON>", "stat3": "NMAP-<PERSON><PERSON>", "stat4": "SYN/FIN Scan", "stat5": "FIN Scan", "stat6": "NMAP-ID Scan", "stat7": "SYN/RST Scan", "stat8": "TCP Sessions Without SYN", "stat8Tooltip": "Limitation : Pour les architectures réseau asymétriques et lorsque NAT est activé, il est fortement déconseillé d'activer l'option « Sessions TCP sans SYN » désactivée pour éviter les déconnexions inattendues.", "stat9": "ICMP-Flood", "title": "DoS"}, "editProfile": "Modifier le profil de politique", "ips": {"accept": "Accepter", "category": "<PERSON><PERSON><PERSON><PERSON>", "custom": "(coutume)", "id": "ID", "impact": "Impact", "monitor": "<PERSON><PERSON><PERSON>", "noPackageMsg": "Vérifiez les packages de sécurité sur la page Gestion des packages de sécurité.", "noVersionAvailable": "Aucune version disponible", "packageVersion": "Version du package", "reference": "Référence", "reset": "Réinitialiser", "ruleSetting": "Paramètres des règles", "title": "IPS", "updateSuccess": "<PERSON><PERSON><PERSON>(s) mise(s) à jour avec succès.", "warningMsg": "Avant de configurer des politiques, assurez-vous que la fonction Système de prévention des intrusions (IPS) est activée sur l'écran Pare-feu > Protection avancée > Configuration dans l'interface Web de l'appareil."}, "isReferenced": "Un ou plusieurs des profils sélectionnés sont référencés.", "layer3to7": {"allowAll": "Autorise tout", "createPolicy": "Créer une politique de couche 3 à 7", "createSuccess": "La politique de couche 3-7 a été créée avec succès.", "default_action_log": "Journal des événements", "default_action_log_destination": "Destination du journal", "default_action_severity": "Gravité", "defaultAction": "Action", "deleteKey": "Politique(s) de couche 3 à 7", "deleteSuccess": "Les politiques de couche 3 à 7 ont été supprimées avec succès.", "deleteTitle": "Supprimer la politique des couches 3 à 7", "denyAll": "Refuser tout", "destinationAddress": "Adresse de <PERSON>", "destinationPort": "Port ou protocole de destination", "destIpAddress": "Adresse IP de destination", "destService": "Service de destination", "editPolicy": "Modifier la politique des couches 3 à 7", "enforce": "Statut", "enforcement": "Statut", "event": "Événement", "eventSetting": "Paramètres de politique par défaut", "filterMode": "Mode filtre", "globalSetting": "Paramètres globaux du pare-feu", "incomingInterface": "Interface entrante", "ipAndPortFiltering": "Filtrage IP et port", "ipAndSourceMacBinding": "Liaison IP et MAC source", "ipTypeError": "Le protocole IP du port source ({{ source }}) est différent du protocole IP du port de destination ({{ dest }})", "maxRowMsg": "Le nombre maximal d'objets est {{ max }}.", "outgoingInterface": "Interface sortante", "policyName": "Nom", "protocolService": "Protocole et service", "sourceAddress": "<PERSON><PERSON><PERSON> <PERSON>", "sourceIpAddress": "Adresse IP source", "sourceMacAddress": "Adresse MAC source", "sourceMacFiltering": "Filtrage MAC source", "sourcePort": "Port source", "title": "Couche 3-7", "updateSuccess": "La politique des couches 3 à 7 a été mise à jour avec succès."}, "maxRowMsg": "Le nombre maximal de profils est de {{ max }}.", "profileName": "Nom de profil", "profileReference": "Références de profil", "profileReferenceMsg": "Ce profil est référencé par le(s) appareil(s) suivant(s) :", "sessionControl": {"concurrentTcp": "Connexions TCP simultanées", "connectionsRequestUnit": "connexions/s", "connectionsUnit": "relations", "createPolicy": "Créer une politique de contrôle de session", "createSuccess": "La politique de contrôle de session a été créée avec succès.", "deleteKey": "Politique(s) de contrôle de session", "deleteSuccess": "Les politiques de contrôle de session ont été supprimées avec succès.", "destinationIp": "IP de destination", "destinationPort": "Le port de destination", "destIpAddress": "Adresse IP", "destPort": "port", "drop": "<PERSON><PERSON><PERSON> tomber", "editPolicy": "Modifier la politique de contrôle de session", "enforcement": "Statut", "maxRowMsg": "Le nombre maximal de politiques pour cet appareil est de {{ max }}.", "monitor": "<PERSON><PERSON><PERSON>", "sub_title": "Protecteur de ressources de service et d'hôte réseau", "tcpConnectionLimit": "Limite de connexion TCP", "tcpDestError": "L'adresse IP et le port ne peuvent pas être tous les deux identiques", "tcpDestination": "Destination TCP", "tcpLimitError": "<PERSON><PERSON> configurer au moins une limitation", "tcpLimitMsg": "Au moins une limitation est requise", "title": "Contrôle de session", "totalTcp": "Nombre total de connexions TCP", "updateSuccess": "La politique de contrôle de session a été mise à jour avec succès."}, "tabInspection": "Objets d'inspection", "tabInterface": "Objets d'interface", "tabPolicyProfile": "Profils politiques", "title": "Gestion des profils de politique", "updateSuccess": "Mise à jour du profil réussie."}, "scheduleInUse": "Horaire d'utilisation", "scheduling": "Planification", "softwarePackage": {"applicationProducts": "Produits applicables", "auto-download": "Téléchargement automatique", "bugsFixed": "<PERSON> corrigés", "buildTime": "Temps de construction", "changes": "Changements", "checkConnection": "Vérifiez l'état de la connexion au serveur de mise à jour Moxa.", "checkNewPackage": "Vérifie les nouvelles versions de packages sur le serveur MOXA.", "checkSoftwarePackage": "Rechercher les mises à jour du package", "daily": "Quotidien", "deleteKey": "<PERSON><PERSON>(s) de sécurité", "deleteSuccess": "Les packages de sécurité ont été supprimés avec succès.", "description": "Description", "detailInfo": "Informations détaillées", "dropZoneTitle": "Télécharger un fichier de package (.pkg)", "endDate": "Date de fin", "endTime": "<PERSON>ure De Fin", "enhancements": "Améliorations", "event": "Événement", "isReferenced": "Un ou plusieurs des packages sélectionnés sont référencés.", "janus": "Packs de sécurité r<PERSON>", "lastConnectionCheck": "Dernière vérification de connexion", "lastSoftwarePackageUpdateResult": "Résultat de la dernière mise à jour du package de sécurité", "licenseActivationReminder": "Rappel d'activation de licence", "licenseActivationReminderContent": "<PERSON>fin de garantir des mécanismes de sécurité renforcés, veuillez activer la licence pour activer cette fonctionnalité.", "licenseTransferReminder": "Rappel de transfert de licence", "licenseTransferReminderContent": "Pour garantir des mécanismes de sécurité renforcés, veuillez transférer votre licence MXsecurity avant de télécharger les packages de sécurité réseau.", "local": "Local", "log": "Journaux d'événements", "maxPackageMsg": "Nombre maximal de téléchargements simultanés : {{ max }} fichiers.", "maxRowMsg": "Le nombre maximal de packages logiciels est de {{ max }}.", "maxSize": "La taille de fichier maximale autorisée est de 1 Go.", "message": "Message", "newFeatures": "Nouvelles fonctionnalités", "notes": "<PERSON><PERSON><PERSON>", "onlyAcceptPkg": "Seuls les fichiers au format ' .pkg ' sont acceptés.", "onlyOneFilePerTime": "Un seul fichier peut être téléchargé à la fois.", "packageDownloading": "Le téléchargeur de packages fonctionne pour d'autres", "packageReference": "Références de package", "packageReferenceMsg": "Ce package est référencé par le(s) profil(s) suivant(s) :", "period": "Période", "productModel": "modèle du produit", "releaseDate": "Date de sortie", "releaseNote": "Notes de version", "scheduling": "Vérification de mise à jour programmée", "schedulingMode": "Mode de planification", "server": "État du serveur de mise à jour Moxa", "serverDisconnected": "Le package de sécurité ne peut être vérifié que lorsque le serveur est connecté.", "severity": "Gravité", "softwarePackageAlreadyLatest": "Le package de sécurité est à jour.", "softwarePackageCheck": "Vérification du package de sécurité", "softwarePackagesFile": "Fichier de packages de sécurité", "softwarePackagesUpdateCheck": "Mettre à jour le package de sécurité", "startDate": "Date de début", "startTime": "<PERSON><PERSON> d<PERSON>", "supportedFunctions": "Fonctions prises en charge", "supportedOperatingSystems": "Systèmes d'exploitation pris en charge", "supportModel": "<PERSON><PERSON><PERSON><PERSON> pris en charge", "supportSeries": "Séries prises en charge", "syncSettingNotSet": "Complétez les paramètres de synchronisation pour vérifier le package de sécurité.", "syncSettings": "Vérification de mise à jour programmée", "syncSettingUpdateSuccess": "Paramètres mis à jour avec succès.", "syncSoftwarePackageBySchedule": "Recherchez automatiquement les mises à jour des packages de sécurité pour les modèles spécifiés selon une planification spécifiée par l'utilisateur.", "syncSoftwarePackageByScheduleTooltip": "Configurez la fréquence à laquelle vérifier le serveur Moxa pour les mises à jour des packages de sécurité.", "time": "Time", "title": "Gestion des packages de sécurité", "updateCheckTooltip": "Vérifiez les mises à jour du package de sécurité sur le serveur Moxa pour vous assurer que vous utilisez la dernière version.", "uploadBy": "telechargé par", "uploadSoftwarePackage": "Télécharger le package", "uploadSuccess": "Le package de sécurité a été téléchargé avec succès.", "username": "Nom d'utilisateur", "version": "Version", "weekday": "journ<PERSON>", "weekly": "Hebdomadaire", "zeus": "Paquets d'agents MXsecurity"}}}, "PORT_SETTING": {"another_port_setting_error": "Un autre réglage est en cours de traitement.", "apply_another_port": "Appliquer les paramètres à un autre port", "disable_port_warning": "Avertissement : La désactivation de ce port déconnectera les dispositifs connectés à ce port.", "enable": "Activer", "get_port_setting_fail": "Impossible d'obtenir les paramètres du port", "hint": "* If the settings fail, please confirm that the selected port can be configured.", "media_type": "Media Type", "port": "Port", "port_description": "Description du port", "port_name": "Nom du port", "port_select": "Vous avez s<PERSON>ionn<PERSON>", "set_fail": "Certains ports ne peuvent pas être mis à jour, réessayez ultérieurement", "set_success": "Tous les paramètres du port sont mis à jour", "title": "Paramètres du port Ethernet/fibre optique"}, "PREFERENCES": {"advanced": "<PERSON><PERSON><PERSON>", "appearance": "Apparence", "default_view": {"choose_start_page": "Choisir une page de démarrage", "dashboard": "Tableau de bord", "title": "Vue par défaut", "topology": "Topologie"}, "device_appearance": {"alias": "<PERSON><PERSON>", "bottom_hint": "Si vous modifiez le paramètre de l'alias, supprimez le dispositif dans la topologie, puis effectuez une nouvelle analyse ou ajoutez un dispositif pour compléter le paramètre de l'alias.", "bottom_label": "Étiquette du bas", "bottom_label_items": {"alias": "<PERSON><PERSON>", "location": "Emplacement", "mac": "MAC", "model_name": "Nom du modèle", "none": "Aucune", "sysname": "SysName"}, "get_fail": "Impossible d'obtenir l'apparence du dispositif", "ip_address": "Adresse IP", "set_fail": "Impossible de définir l'apparence du dispositif", "set_success": "L'apparence du dispositif est mise à jour", "title": "<PERSON><PERSON>"}, "device": {"login": "Ouverture de session", "title": "Dispositif"}, "dialog": {"desc": "Supprimer tous les paramètres « Ne plus afficher ce message » et afficher de nouveau toutes les boîtes de dialogue", "title": "Boîte de dialogue"}, "display": "Appearance", "email_config": {"allow_selfsigned_cert": "Autoriser le certificat auto-signé", "apply_fail": "Impossible de définir la configuration du serveur de messagerie", "apply_success": "La configuration du serveur de messagerie est mise à jour", "encryption": "Chiffrement", "password": "Mot de passe", "port_number": "Numéro de Port", "sender_address": "<PERSON><PERSON><PERSON> de l'expéditeur", "server_domain_name": "Nom de domaine/Adresse IP du serveur", "title": "Configuration du serveur de messagerie", "username": "Nom d'utilisateur"}, "events": {"apply_fail": "Impossible de définir le seuil de l'événement", "apply_success": "Le seuil d'événement est mis à jour", "availability_under": "Sous-disponibilité", "bandwidth_utilization_over": "Utilisation excessive de la bande passante", "bandwidth_utilization_under": "Sous-utilisation de la bande passante", "link_down": "<PERSON>ne <PERSON>", "link_up": "Établissement d'une liaison", "packet_error_rate_over": "Taux d'erreurs de paquets excessif", "port_looping": "Boucle de port", "sfp_rx_below_threshold": "SFP RX est inférieur à", "sfp_temp_over_threshold": "Température SFP est supérieure à", "sfp_tx_below_threshold": "SFP TX est inférieur à", "sfp_volt_below_threshold": "Tension SFP est inférieure à", "sfp_volt_over_threshold": "Tension SFP est supérieure à", "title": "Événements"}, "labs": {"colored_link_desc": "Lorsque cette option est activée, tous les liens sans fil sont colorés par SNR.", "desc": "L'activation des laboratoires ajoutera des fonctions expérimentales comme ci-dessous, elles sont à un stade précoce mais ne nuisent pas à votre système.", "dialog_title": "Activer les fonctionnalités Labs", "title": "Labs"}, "language": {"default_language": "Langue par défaut", "en_US": "<PERSON><PERSON><PERSON>", "fail": "Impossible de définir la langue par défaut", "success": "La langue par défaut est mise à jour", "title": "<PERSON><PERSON>", "zh_CN": "简体中文", "zh_TW": "繁體中文"}, "login_authentication": {"apply_fail": "Impossible de définir l'authentification de la connexion", "apply_success": "L'authentification de la connexion a été mise à jour", "authentication_protocol": "Protocole d'authentification", "local": "Local", "tacacs": "TACACS+", "tacacs_local": "TACACS+, Local", "title": "Authentification de la connexion"}, "login_notification": {"fail": "Impossible de définir la notification de connexion", "login_authentication_failure_message": "Message d'échec de l'authentification de connexion", "login_message": "Message de connexion", "show_default_password_notification": "Afficher la notification de mot de passe par défaut", "show_login_failure_records": "Afficher les enregistrements d'échec de connexion", "success": "La notification de connexion est mise à jour", "title": "Notification de connexion"}, "management_interface": {"help": "Cette page est utilisée pour définir l'interface de connexion à partir de MXview One vers les dispositifs, y compris http, https et les ports telnet", "http_port": "Port HTTP", "htts_port": "Port HTTPS", "invalid_port": "Le numéro de port doit se situer entre 1 et 65535", "set_fail": "Impossible de définir l'interface de gestion", "set_success": "L'interface de gestion est mise à jour", "telnet_port": "Port Telnet", "title": "Interface de gestion", "web_console_protocol": "Protocole de console Web"}, "modbus_tcp_configuration": {"port": "port"}, "opc_server_config": {"apply_fail": "Impossible de définir la configuration du serveur OPC", "apply_success": "La configuration du serveur OPC est mise à jour", "enable_opc_server": "Activer", "title": "Configuration du serveur OPC"}, "password_policy": {"fail": "Impossible de définir la stratégie de mot de passe", "has_digits": "Au moins un chiffre (0 à 9)", "has_special_chars": "Au moins un caractère spécial (~!@#$%^&*-_|;:,.<>[]{}())", "min_password_length": "<PERSON>ueur minimale (4 à 16)", "min_password_length_error": "Entrez une valeur valide", "mixed_case": "Minuscules et majuscules mélangées (A à Z, a à z)", "password_strength_check": "Vérification du niveau de complexité du mot de passe", "success": "La stratégie de mot de passe est mise à jour", "title": "Stratégie de mot de passe"}, "search": "<PERSON><PERSON><PERSON>", "security_view": {"all": "Tous", "awk_device_credentials_hint": "Pour supporter la vue de sécurité, vous devez définir le nom d'utilisateur et le mot de passe de cet appareil", "basic": "Basique", "basic_text": "Basique", "built_in_profile": "Profil <PERSON>", "check_item": "Vérifier l'élément", "colors_for_check_result": "Couleurs pour le résultat de la vérification", "current_setting": "Paramètre actuel :", "custom": "<PERSON><PERSON><PERSON><PERSON>", "custom_profile": "Sélectionnez la ligne de base pour le profil personnalisé", "device_security_level": "Niveau de sécurité du dispositif :", "failed": "Échec de la définition de la vue de la sécurité", "filter_result": "résultat du filtre", "high": "<PERSON><PERSON><PERSON>", "high_text": "<PERSON><PERSON><PERSON>", "medium": "<PERSON><PERSON><PERSON>", "medium_text": "<PERSON><PERSON><PERSON>", "new_profile": "Nouveau profil", "not_pass": "Échec", "open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pass": "Réussite", "profile": "Profil", "profile_details": "<PERSON><PERSON><PERSON> du profil", "success": "La vue de la sécurité est mise à jour", "title": "Affichage de la sécurité", "unknown": "Inconnu", "user_defined": "Défini par l'utilisateur"}, "Server": "Ser<PERSON><PERSON>", "site_name_configuration": "Configuration du nom du site", "sms_config": {"apply_fail": "Impossible de définir la configuration SMS", "apply_success": "La configuration SMS est mise à jour", "baud_rate": "Vitesse (en bauds)", "com_port": "Port COM", "mode": "Mode", "title": "Paramètre SMS"}, "snmp_configuration": {"help": "Définir la configuration SNMP pour accéder aux dispositifs du réseau", "title": "Configuration SNMP"}, "SNMP_TRAP": {"apply_fail": "Impossible de définir la configuration d'interception SNMP", "apply_success": "La configuration du serveur Interception SNMP est mise à jour", "community1": "Communauté du serveur d'interception 1", "community2": "Communauté du serveur d'interception 2", "device_list": "Liste des dispositifs", "device_trap": "Serveur d'interception SNMP du dispositif", "forward_trap_control1": "Trap transmis sur le Serveur 1", "forward_trap_control2": "Trap transmis sur le Serveur 2", "ip1": "Adresse IP du serveur d'interception 1", "ip2": "Adresse IP du serveur d'interception 2", "mxview_trap": "Serveur d'interception SNMP de MXview One", "version": "Version SNMP", "version_1": "Version SNMP 1", "version_2": "Version SNMP 2c"}, "syslog_config": {"already_running": "Le serveur est déjà en cours d'exécution ou à l'arrêt", "apply_fail": "Impossible de définir la configuration du serveur Syslog", "apply_success": "La configuration du serveur Syslog est mise à jour", "enable_syslog_server": "<PERSON><PERSON> le serveur Syslog intégré", "invalid_port": "Le numéro de port doit se situer entre 1 et 65535", "syslog_server_port": "Port du serveur Syslog", "title": "Configuration du serveur Syslog"}, "system_configuration": {"apply_fail": "Impossible de définir la configuration du système", "apply_success": "La configuration du système est mise à jour", "background_discovery": "Découverte de l'arrière-plan", "disk_hint": "L'alarme est désactivée si définie sur 0", "playback": "Lecture", "playback_hint_1": "* Lorsque la fonction \"Lecture\" est activée, MXview One enregistrera l'état des dispositifs et des liaisons lorsqu'un événement est survenu, et vous pouvez passer en mode lecture ultérieurement pour voir le processus détaillé.", "playback_hint_2": "* Davantage d'espace disque est requis lorsque la fonction \"Lecture\" est activée.", "threshold_disk_space": "Seuil de l'espace disque (Mo)", "title": "Configuration du système"}, "table": {"default": "<PERSON><PERSON> <PERSON><PERSON>", "dense": "<PERSON><PERSON>", "fail": "Impossible de définir les paramètres du tableau", "success": "Les paramètres du tableau ont été mis à jour", "table_row_height": "<PERSON>ur des rangées", "title": "<PERSON><PERSON>"}, "tacacs": {"apply_fail": "Impossible de définir la configuration du serveur TACACS", "apply_success": "TACACS+ la configuration du serveur a été mise à jour", "auth_type": "Type d'authentification", "auth_type_asc_two": "ASCII", "auth_type_chap": "CHAP", "auth_type_ms_chap": "MS-CHAP", "auth_type_pap": "PAP", "server_address": "<PERSON><PERSON><PERSON>", "share_key": "Clé de répartition", "tcp_port": "Port TCP", "timeout": "<PERSON><PERSON><PERSON>'attente", "title": "TACACS+ Serveur"}, "title": "Préférences", "topology_appearance": {"access_port": "Port d'accès", "background": "Arrière-plan", "background_color": "Couleur de l'arrière-plan", "directed_line_style": "Style de ligne dirigée", "edit_igmp_visualization_color": "Modifier la couleur de la visualisation IGMP", "edit_traffic_load_color": "Modifier la couleur de charge du trafic", "edit_vlan_visualization_color": "Modifier la couleur de la visualisation VLAN", "elbow_line_style": "Style de ligne coudée", "fail": "Impossible de définir l'apparence de la topologie", "hsr_ring": "Anneau HSR", "igmp_visualization": "Visualisation IGMP", "link_down": "<PERSON>ne <PERSON>", "link_up": "Établissement d'une liaison", "member": "Membre", "poe": "PoE", "poe_color": "Couleur de la liaison PoE", "prp_lan_a": "PRP LAN A", "prp_lan_b": "PRP LAN B", "querier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rstp": "RSTP", "show_poe": "Afficher l'état PoE sur la topologie", "status_color": "Couleur de l'état", "success": "L'apparence de la topologie est mise à jour", "text_size": "<PERSON>lle du texte", "text_size_large": "Grande", "text_size_medium": "<PERSON><PERSON><PERSON>", "text_size_small": "Petite", "title": "Topology", "topology_style": "Style de ligne de la topologie", "traffic_load": "Charge du trafic", "trunk_port": "Port de jonction", "turbo_chain": "Turbo Chain", "turbo_ring_v1": "Turbo Ring V1", "turbo_ring_v2": "Turbo Ring V2", "vlan_visualization": "Visualisation VLAN"}, "user": "Utilisa<PERSON>ur"}, "rogue_device_detection": {"add_device_for_rogue_device_detection": "Ajouter cet appareil à la référence des appareils pour la détection des appareils malveillants", "add_device_to_baseline": "Ajouter un appareil à la ligne de base", "add_device_to_baseline_content": "Êtes-vous sûr de vouloir ajouter cet appareil à la ligne de base ?", "add_devices_to_baseline_content": "Êtes-vous sûr de vouloir ajouter ces appareils à la ligne de base ?", "add_scan_device_for_rogue_device_detection": "Ajouter les appareils analysés à la référence des appareils pour la détection des appareils malveillants", "clear_all_rogue_device_history": "Effacer l'historique des appareils malveillants", "clear_all_rogue_device_history_hint": "Tout l’historique des appareils malveillants sera effacé. Es-tu sur de vouloir continuer?", "connected_switch_port": "Commutateur/Port connecté", "creation_time": "<PERSON><PERSON><PERSON>", "current_rogue_device": "Appareils malveillants actuels", "delete_device_from_baseline": "Supprimer l'appareil de la ligne de base", "delete_device_from_baseline_content": "Cet appareil sera supprimé de la ligne de base et ajouté en tant qu'appareil malveillant.", "delete_devices_from_baseline_content": "Ces appareils seront supprimés de la ligne de base et ajoutés en tant qu’appareils malveillants.", "device_baseline": "Réfé<PERSON><PERSON> de l'appareil", "device_baseline_content": "Cette opération créera une nouvelle ligne de base et écrasera celle existante.", "download_all_history_data_to_csv": "Exporter toutes les données de l'historique au format CSV", "download_current_page_to_csv": "Exporter la page actuelle au format CSV", "first_seen": "Vu la première fois", "ip": "Adresse IP", "last_seen": "Vu pour la dernière fois", "mac": "<PERSON><PERSON><PERSON>", "must_create_a_baseline_first": "Aucune référence d’appareil n’existe. Créez d’abord une référence.", "no_devices_can_add": "Aucun appareil détecté. Ajoutez d'abord des appareils à MXview One.", "port": "Port", "rogue_device_history": "Historique des appareils malveillants", "rogue_device_settings": "Paramètres des appareils malveillants", "sequence_no": "Numéro de séquence", "unknown": "Inconnu", "vendor": "Fournisseur de carte réseau"}, "SCAN_RANGE": {"add_scan_range": "Ajouter une plage d'analyse", "button": {"back": "Retour", "browse_topology": "Parcourir la topologie", "cancel": "Annuler", "discovery": "OK et exécuter la découverte", "next": "Suivant", "recover": "Récupération", "scan_new_network": "Analyser le nouveau réseau"}, "cidr_address_range": "Plage des adresses CIDR", "duplicate_range": "La nouvelle plage se superpose avec les plages existantes", "edit_scan_range": "Modifier une plage d'analyse", "firstIp_higher_lastIp": "La plage d'IP n'est pas valable (Premier IP > Dernier IP)", "subnet_mask": "Subnet Mask", "table_title": {"active": "Activer la plage d'analyse", "background_scan": "Analyse en arrière-plan", "conflict_scan": "Détecter les conflits IP", "edit": "Modifier", "end_ip": "Dernière adresse IP", "group": "Groupe", "name": "Nom", "site_name": "Nom du site", "start_ip": "Première adresse IP"}, "wizard": {"complete": "<PERSON><PERSON><PERSON><PERSON>", "complete_message": "Il y a {{discoveryDevices}} ajoutés à MXview One", "discovery_result": "Résultat de la découverte", "network_range": "<PERSON><PERSON>", "save_hint": "La ou les plages balayées seront enregistrées après la découverte du dispositif.", "title": "Découverte des dispositifs"}}, "script_automation": {"add_a_script_automation": "Ajouter une automatisation de script", "add_script_button_hint": "Le nombre maximum d'automatisations de scripts est de 200.", "add_script_first_hint": "Aucune automatisation de script trouvée. Accédez à l’écran Script Automation pour ajouter une automatisation de script.", "add_script_sutomation": "Ajouter l'automatisation des scripts", "adjustable_buttons": "Réorganiser les boutons", "affected_devices": "Appareils concernés", "affected_devices_info": "Cette action affectera les appareils {{ affectedDevices }}.", "affected_devices_info_2": "Cette action affectera les appareils {{ affectedDevices }} suivants", "align_buttons": "<PERSON><PERSON><PERSON> tous les groupes dans une seule colonne", "all_devices": "Tous les dispositifs", "automation_button": "Boutons d'automatisation", "background_color": "Couleur de l'arrière-plan", "button_name": "Nom du bouton", "button": {"panel": "Panneau de boutons", "privilege": "Authentification administrateur requise", "state": {"hint": "Un seul bouton dans un groupe peut être à l'état 'Activé' à un moment donné.", "off": "De", "on": "Activé", "title": "État du bouton"}, "style": "Style de bouton", "widget": "Bouton Widget"}, "cli_id_duplicated": "Cette CLI a déjà été sélectionnée.", "cli_script_and_target_device": "Scripts CLI et périphériques cibles", "cli_script_hint": "Le nombre maximum de scripts est de 50.", "color": "<PERSON><PERSON><PERSON>", "confirm_proceed": "Voulez-vous poursuivre?", "create_new_group": "C<PERSON>er et ajouter à un nouveau groupe", "delete_automation": "Êtes-vous sûr de vouloir supprimer cette automatisation de script ?", "delete_multiple_automation": "Êtes-vous sûr de vouloir supprimer ces automatisations de script ?", "delete_script_automation": "Supprimer l'automatisation du script", "description": "Description", "device_missing": "Les appareils suivants sont introuvables", "drag_button": "Faites glisser un bouton ici pour créer un nouveau groupe.", "edit_button": "Bouton Modifier", "edit_group": "Modifier le groupe", "edit_panel": "Modifier le titre du panneau de boutons", "edit_script_button": "Modifier le bouton d'automatisation", "execute_button": "Bouton Exécuter", "execute_button_hint": "Exécution de l'automatisation des scripts", "execute_button_info": "Veuillez attendre la fin du processus pour voir les résultats. Si vous quittez cet écran, vous pouvez accéder à Scripts CLI enregistrés > Résultats d'exécution pour télécharger les résultats.", "extra_large": "Extra large", "group": "Groupe", "group_already_exist": "Ce nom de groupe existe déjà", "group_name": "Nom du groupe", "invalid_account": "Privilège de compte non valide", "ip_duplicated": "Cet appareil est déjà sélectionné.", "large": "Grande", "last_executed": "Dernière exécution", "leave_page_hint": "Êtes-vous sûr de vouloir quitter cette page?", "leave_without_saving": "Partir sans économiser", "medium": "<PERSON><PERSON><PERSON>", "more": "Plus", "name": "Nom", "not_change_group": "Utiliser le groupe actuel", "not_saved_hint": "Les modifications que vous avez apportées ne seront pas enregistrées.", "script_automation": "Automatisation des scripts", "select_all": "<PERSON><PERSON>", "select_existing_group": "Passer à un autre groupe", "select_saved_cli_script": "Sélectionnez le script CLI enregistré", "small": "Petite", "start_preview": "Démarrer l'aperçu dans Topology", "stop_preview": "Arrêter l'aperçu dans la topologie", "target_device": "Appareils cibles", "text_color": "Couleur du texte", "widget_size": "<PERSON><PERSON> du widget"}, "SECURITY_ITEM": {"ACCESSIBLE_IP_LIST": "Activer l'accès approuvé", "ACCOUNT_LOCKOUT": "Activer le verrouillage suite à un défaut de connexion à un compte", "ACCOUNT_VALIDITY": "Validité du compte et de la stratégie de mot de passe", "AUTO_LOGOUT": "Activer la déconnexion automatique", "AWK_SERIES": "Sans fil", "BROAD_CAST_STORM": "Enable DDoS Protection", "changed": "<PERSON><PERSON><PERSON><PERSON>", "disabled": "Désactivé", "enabled": "Activé", "ENCRYPT_CONSOLE": "Désactiver les ports TCP/UDP non chiffrés", "ENCRYPTED_CONFIG": "Activer le chiffrement de fichier de configuration", "HIGH_SECURE_MODE": "Mode de sécurité élevée", "LOGIN_NOTIFICATION": "Définir le message de connexion", "MGATE": "<PERSON><PERSON><PERSON>", "non-changed": "Non modifié", "not_set": "Non défini", "NPORT": "Serveur terminal", "NPORT5000": "Serveur du dispositif", "NTP_SERVER": "Définir le client NTP", "PASSWORD_CHANGED": "Modifier le mot de passe par défaut/la chaîne de communauté SNMP", "PASSWORD_POLICY": "Activer la vérification de la force du mot de passe", "read_fail": "Échec de la lecture", "set": "Définir", "SWITCH": "Basculer", "SYSLOG": "Définir le serveur Syslog", "TRAPSYSLOG": "Définir le serveur d'interception/information SNMP ou le serveur Syslog", "unknown": "Inconnu", "WEB_CERTIFICATE": "Importer le certificat Web"}, "SERIAL_PORT_MONITORING": {"all_ports": "Tous les ports", "any_serial_error_count": "Nombre d'erreurs en série", "break_error_count": "Nombre d'erreurs de rupture", "copy_configuration_device": "Copier la configuration sur les appareils", "count_threshold": "Seuil de d<PERSON>nch<PERSON>", "counts": "<PERSON><PERSON><PERSON>(s)", "critical": "Critique", "error_status": "Avertissements sur le port série", "event_condition_rule": "Règles de déclenchement d&#39;événements", "frame_error_count": "Nombre d'erreurs de trame", "hint_any": "Essayez les étapes ci-dessous pour résoudre le problème suivant : le port série {{portnum}} a dépassé le seuil d'erreur de dépassement ({{overrun error count}}), d'erreur de parité ({{parity error count}}), d'erreur de trame ({{frame error count}}) ou d'erreur d'interruption ({{break error count}}).", "hint_break": "Essayez les étapes ci-dessous pour résoudre le problème suivant : le port série {{portnum}} a dépassé le seuil de nombre d’erreurs d’interruption ({{count}}).\nUn signal de rupture série indique une condition particulière sur le périphérique série connecté, tel qu&#39;un problème de câblage, un dysfonctionnement du périphérique, une réinitialisation du périphérique ou un processus de synchronisation.", "hint_frame": "Essayez les étapes ci-dessous pour résoudre le problème suivant : le port série {{portnum}} a dépassé le seuil de nombre d’erreurs de trame ({{count}}).\nLorsque le périphérique Moxa reçoit des données série, il vérifie si le format de trame correspond aux paramètres série. S&#39;ils ne correspondent pas, cela sera considéré comme une erreur de trame.", "hint_general_1": "Si la solution suggérée n'a pas fonctionné ou si vous avez d'autres questions, veuillez contacter votre", "hint_general_2": "first", "hint_general_3": "<PERSON><PERSON>", "hint_general_4": "si vous avez encore besoin d'une assistance supplémentaire.", "hint_overrun": "Essayez les étapes ci-dessous pour résoudre le problème suivant : le port série {{portnum}} a dépassé le seuil de dépassement du nombre d’erreurs ({{count}}).\nSi les périphériques série connectés envoient des données trop rapidement pour que le périphérique Moxa puisse les lire, il supprimera les données, provoquant une erreur de dépassement.", "hint_parity": "Essayez les étapes ci-dessous pour résoudre le problème suivant : le port série {{portnum}} a dépassé le seuil de nombre d’erreurs de parité ({{count}}).\nUne erreur de parité indique que le caractère de données reçu ne correspond pas à la parité configurée.", "hint_rx": "Essayez les étapes ci-dessous pour résoudre le problème suivant : le port série {{portnum}} n'a reçu aucune donnée au cours des {{min}} dernières minutes.", "hint_rxtx": "Essayez les étapes ci-dessous pour résoudre le problème suivant : le port série {{portnum}} n'a transmis ni reçu aucune donnée au cours des {{min}} dernières minutes.", "hint_tx": "Essayez les étapes ci-dessous pour résoudre le problème suivant : le port série {{portnum}} n'a transmis aucune donnée au cours des {{min}} dernières minutes.", "how_to_resolve": "Comment résoudre ce problème ?", "minutes": "Minutes", "no_data_period": "Aucune pé<PERSON> de donn<PERSON>", "overrun__error_count": "Nombre d'erreurs de dépassement", "parity__error_count": "Nombre d'erreurs de parité", "port_duplicated": "Cette combinaison port/règle de déclenchement est déjà configurée.", "port_properties": "Type d'événement", "resolve_title": "Résoudre le problème sur le port série {{portnum}}", "rx": "Inactivité RX", "serial_port": "Port série", "severity": "Gravité", "step1_break": "Vérifiez si les périphériques série connectés fonctionnent correctement.", "step1_frame": "Vérifiez si les paramètres de l'interface série (RS-232, RS-422, RS485) et les paramètres de communication (par exemple 115200, 8, n, 1) sur l'appareil Moxa correspondent à ceux des appareils série connectés.", "step1_ovrrun": "Vérifiez si le contrôle du flux matériel et/ou logiciel série est correctement défini sur le périphérique Moxa et sur les périphériques série connectés", "step1_parity": "Vérifiez si les paramètres de parité et de débit en bauds sur le périphérique Moxa et les périphériques série connectés correspondent.", "step1_txrx": "Vérifiez si le câble série entre l'appareil Moxa et les périphériques série est correctement connecté.", "step2_ovrrun": "Vérifiez si le FIFO doit être activé sur le périphérique Moxa.", "step2_parity": "Dans les zones à fortes interférences, vérifiez si le système de communication série est correctement protégé.", "step2_txrx": "Vérifiez si les périphériques série connectés fonctionnent correctement.", "step3_parity": "Vérifiez les périphériques série connectés pour détecter d'éventuels problèmes de câblage ou des défauts matériels.", "still_not_working": "<PERSON><PERSON> ne fonctionne toujours pas ?", "title": "Événements du port série", "tx": "Inactivité TX", "tx_rx": "Inactivité TX et RX", "warning": "Avertissement"}, "SEVERITY": {"critical": "Critique", "information": "Informations", "title": "Gravité", "warning": "Avertissement"}, "sfpList": {"rx": "RX (dBm)", "temperature": "Temp.(°C)", "title": "Liste SFP", "tx": "TX (dBm)", "voltage": "Tension (V)"}, "SITE_MANAGEMENT": {"desc": "Description", "fail": "Impossible de mettre à jour les informations du site", "name": "Nom", "offline": "Un site est en hors ligne ({{siteName}})", "online": "Un site est en ligne ({{siteName}})", "success": "Les informations du site sont mises à jour", "title": "Gestion du site"}, "SITE_MENU": {"management": "Gestion"}, "SITE_PROPERTIES": {"description": "Description", "devices": "Dispositifs (Normal/Avertissement/Critique)", "information": "Informations", "name": "Nom", "title": "Propriétés du site"}, "SNACK_BAR": {"acking": "Accusé de réception en cours...", "copied": "<PERSON><PERSON><PERSON>", "deleting": "Suppression…", "saving": "Enregistrement…"}, "SYSLOG_SETTINGS": {"all_severity": "Tous", "authentication": "Authentification", "enable_syslog_forward": "Transfert Syslog", "enable_tcp": "Activer (TCP uniquement)", "enable_udp": "Activer (UDP uniquement)", "enable_udp_tcp": "Activer (UDP et TCP)", "failed_to_get_syslog_forward_settings": "Échec de l'obtention des paramètres de transfert Syslog", "failed_to_get_syslog_settings": "Échec de l'obtention des paramètres Syslog", "filter_settings_hint": "Vous pouvez saisir plusieurs adresses IP sources, séparées par une virgule.", "forward_ip1": "IP distante/Nom de domaine 1", "forward_ip2": "IP distante/Nom de domaine 2", "port_1": "Port 1", "port_2": "Port 2", "protocol": "Protocole", "source_ip": "Adresse IP de la source", "syslog_built_in": "Serveur Syslog intégré", "syslog_filter_settings": "Filtres Syslog", "syslog_forwarding": "Transfert Syslog", "syslog_server_settings": "Paramètres du serveur Syslog", "tcp": "TCP", "tcp_port": "Port TCP", "title": "Paramètres Syslog", "tls_cert": "TLS + certificat", "tls_only": "TLS uniquement", "udp": "UDP", "udp_port": "Port UDP", "update_failed": "Échec de la mise à jour des paramètres", "update_success": "Paramètres mis à jour avec succès"}, "SYSLOG_VIEWER": {"device_ip": "Adresse IP", "facility": "Installation", "filter_syslog_event": "Taper pour filtrer l'événement Syslog", "ip_error": "Veuillez saisir un adresse IP valide", "message": "Message", "priority": {"equals": "Est égal à", "high_than": "Su<PERSON><PERSON><PERSON> ou égal à", "lower_than": "Inférieur ou égal à", "title": "Priorité"}, "severity": {"alert": "<PERSON><PERSON><PERSON>", "critical": "Critique", "debug": "Déboguer", "emergency": "Urgence", "error": "<PERSON><PERSON><PERSON>", "information": "Informations", "notice": "<PERSON><PERSON>", "title": "Gravité", "warning": "Avertissement"}, "site_name": "Nom du site", "timestamp": "Horodatage", "title": "Observateur Syslog"}, "SYSTEM": {"request_timeout_title": "Expiration de la demande", "trigger_disconnected_desc1": "Déconnecté du serveur MXview One", "trigger_disconnected_desc2": "Se reconnecter au bout de 5 secondes…", "unauthorized_desc": "L'accès est refusé en raison d'informations d'identification non valides", "unauthorized_title": "Non autorisé"}, "TABLE": {"add": "Ajouter", "adjustable_columns": "Colonnes réglables", "compare": "Comparer", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "edit_columns": "Modifier les colonnes", "enable": "Activé/Désactivé", "export": "Exporter", "exporting": "En cours d'exportation…", "filter": "<PERSON><PERSON><PERSON>", "import": "Importer", "limit_count": "<PERSON>.", "list_collaspe": "<PERSON><PERSON><PERSON><PERSON>", "list_expand": "Développer pour obtenir plus d'informations", "locate": "Localiser", "no_data": "Aucun affichage de données", "not_support": "Version du microprogramme du périphérique non prise en charge", "save": "Enregistrer", "search": "<PERSON><PERSON><PERSON>", "selected_count": "Sélectionné", "show_log": "Afficher le journal", "sync": "Sync", "total": "total", "waiting_data": "En attente de données"}, "TOPOLOGY": {"add_tag": "Add Tag...", "add_tag_fail": "Unable to add tag", "add_tag_success": "Add a tag successfully", "choose_goose_publisher": "Choisissez un éditeur GOOSE", "colored_link": "Lien coloré par SNR", "delete_tag_failed": "Unable to delete tag", "delete_tag_success": "Delete a tag successfully", "device_not_found": "dispositif introuvable", "display_opt": "Options d'affichage", "dynamic_wireless_client_position": "Afficher les clients autour du point d'accès connecté", "dynamic_wireless_client_position_desc": "En activant cette option, vous afficherez chaque client pour chaque point d’accès connecté", "editTagTooltip": "Press Enter to save.\nPress ESC key to cancel.", "goose": "GOOSE", "goose_publisher": "Éditeur GOOSE", "goose_tampered": "Mot de passe falsifié GOOSE", "goose_timeout": "<PERSON><PERSON><PERSON> d'attente GOOSE", "grouping_failed": "Échec du groupement", "grouping_success": "Groupement réussi", "legend": "Legend", "new_tag": "New tag", "no_subscriber": "<PERSON><PERSON>'<PERSON>", "prp_hsr": "PRP/HSR", "prp_hsr_tags": "PRP/HSR Tags", "publisher": "<PERSON><PERSON><PERSON>", "publisher_hint": "GOOSE control block name\nAPPID / Address", "search_topology": "Rechercher la topologie", "set_tag_fail": "Unable to set a tag", "set_tag_success": "Set a tag successfully", "show_all_wireless_clients": "Show clients", "site_management_not_supported": "Veuillez spécifier un site pour utiliser la fonction de lecture de l'historique d'itinérance", "subscriber": "<PERSON><PERSON><PERSON><PERSON>", "subscriber_hint": "IED Name / GOOSE control block name", "tag": "Tag", "traffic_view": "Charge du trafic (%)", "ungrouping_failed": "Échec du désassemblage", "ungrouping_success": "Désassemblage réussi", "wireless_display_opt": "Option d'affichage sans fil", "zoom_in": "Zoom avant", "zoom_out": "Zoom arri<PERSON>", "zoom_to_actual_size": "Zoom sur la taille par défaut", "zoom_to_fit": "Zoom pour ajuster"}, "TRAP_CONFIGURATION": {"apply_fail": "Impossible de définir le serveur d'interception des dispositifs", "apply_success": "Les paramètres du serveur d'interception du dispositif sont mis à jour", "community_name1": "Nom de communauté 1", "community_name2": "Nom de communauté 2", "destination_ip1": "Adresse IP 1 de destination", "destination_ip2": "Adresse IP 2 de destination", "title": "<PERSON><PERSON>ur d'interception"}, "UNIT": {"dB": "dB", "dBi": "dBi", "dBm": "dBm", "hours": "h", "mb": "Mo", "mbps": "Mbps", "meter": "m", "min": "min", "sec": "s", "times": "fois"}, "UPGRADE_FIRMWARE": {"file_type_error": "Les fichiers du micrologiciel doivent être au format de fichier .rom, .tar et .gz.", "upgrade_firmware_fail": "Impossible de charger le microprogramme", "upgrade_firmware_success": "Le microprogramme est mis à niveau", "upgrading": "Téléchargement du fichier du microprogramme ; cette opération peut prendre plusieurs minutes. Ne mettez pas hors tension et ne déconnectez pas le réseau tant que le processus n'est pas terminé"}, "validators": {"duplicateEmail": "Il y a des e-mails en double", "excludeLastPassword": "Le nouveau mot de passe ne peut pas être le même que le dernier mot de passe", "excludeUserName": "Impossible d'inclure le nom d'utilisateur", "invalid": "Caract<PERSON>(s) non valide(s)", "invalid_date": "Date invalide", "invalid_format_allow_space": "Seul un maximum de {{count}} espaces est autorisé dans la chaîne", "invalidEmail": "E-mail valide", "invalidIpAddress": "Adresse IP invalide", "invalidIpAddressOrDomainName": "Adresse IP ou nom de domaine non valide", "invalidLocation": "Autorisation spéciale (-_@!#$%^&*().,/)", "invalidMacAddress": "Adresse MAC invalide", "invalidMacAddressAllZero": "L'adresse MAC 00:00:00:00:00:00 est réservée", "invalidSeverity": "Gravité invalide", "ipRangeError": "L'adresse IP : fin doit être supérieure à l'adresse IP : début", "isExist": "existe déj<PERSON>", "isExistOrUsedByOtherUser": "existe déjà ou utilisé par un autre utilisateur", "maxReceiverSize": "Le nombre MAX. de récepteurs est de {{num}}.", "needDigit": "<PERSON><PERSON> inclure au moins un chiffre (0 - 9)", "needGreaterThan": "{{ largeItem }} doit être supérieur à {{ smallItem }}", "needLowerCase": "Doit inclure au moins un caractère minuscule (a - z)", "needSpecialCharacter": "Doit inclure au moins un caractère spécial (~!@#$%^&amp;*_-+=`|\\(){}[]:;”&#39;&lt;&gt;,.?/)", "needUpperCase": "Doit inclure au moins un caractère majuscule (A - Z)", "notMeetPolicy": "Ne répond pas aux exigences de la politique de mot de passe", "portRangeError": "Port : Fin doit être supérieur à Port : Début", "pwdNotMatch": "Le mot de passe ne correspond pas", "range": "Plage non valide ({{ min }} ~ {{ max }})", "required": "Requis", "requireMaxLength": "Ne doit pas comporter plus de {{ number }} caractères", "requireMinLength": "Doit contenir au moins {{ number }} caractères"}, "Validators": {"require_range_between": "{{ rangeBegin }} - {{ rangeEnd }}"}, "VLAN_TABLE": {"access_ports": "Ports d'accès", "device_ip": "Adresse IP du dispositif", "empty": "Vide", "export_csv": "Exporter le fichier CSV", "filter_vlan": "Taper pour filtrer le VLAN du dispositif", "hybrid_ports": "Ports hybrides", "location": "Emplacement", "management_vlan": "VLAN de gestion", "model": "<PERSON><PERSON><PERSON><PERSON>", "no": " Non", "site_name": "Nom du site", "title": "VLAN", "trunk_ports": "Ports de jonction", "vlan_id": "ID de VLAN", "yes": " O<PERSON>"}, "wirelessPlayback": {"decreaseSpeed": "Diminuer la vitesse", "increaseSpeed": "Augmenter la vitesse", "noData": "Il n'y a pas de données dans la plage sélectionnée de dates", "range": "<PERSON><PERSON><PERSON>", "startTime": "<PERSON><PERSON> d<PERSON>", "timeRange": "Plage de temps"}}