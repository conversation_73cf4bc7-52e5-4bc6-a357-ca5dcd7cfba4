import { Injectable } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { MatIconRegistry } from '@angular/material/icon';

@Injectable()
export class MxStyleService {
  constructor(private matIconRegistry: MatIconRegistry, private domSanitizer: DomSanitizer) {
    this.matIconRegistry.addSvgIcon(
      'file_download',
      this.domSanitizer.bypassSecurityTrustResourceUrl('../assets/mx-service/img/file-download-outline.svg')
    );
    this.matIconRegistry.addSvgIcon(
      'file_upload',
      this.domSanitizer.bypassSecurityTrustResourceUrl('../assets/mx-service/img/file-upload-outline.svg')
    );
    this.matIconRegistry.addSvgIcon(
      'selectAll',
      this.domSanitizer.bypassSecurityTrustResourceUrl('../assets/img/select_all.svg')
    );
    this.matIconRegistry.addSvgIcon(
      'select',
      this.domSanitizer.bypassSecurityTrustResourceUrl('../assets/img/select.svg')
    );
    this.matIconRegistry.addSvgIcon(
      'deployed_code',
      this.domSanitizer.bypassSecurityTrustResourceUrl('../assets/img/menu_icon/deployed_code.svg')
    );
    this.matIconRegistry.addSvgIcon(
      'reorder',
      this.domSanitizer.bypassSecurityTrustResourceUrl('../assets/img/reorder.svg')
    );
    this.matIconRegistry.addSvgIcon(
      'deactivate',
      this.domSanitizer.bypassSecurityTrustResourceUrl('../assets/img/deactivate.svg')
    );
    this.matIconRegistry.addSvgIcon(
      'certified',
      this.domSanitizer.bypassSecurityTrustResourceUrl('assets/img/svg-icon/certified.svg')
    );
    this.matIconRegistry.addSvgIcon(
      'uncertified',
      this.domSanitizer.bypassSecurityTrustResourceUrl('assets/img/svg-icon/uncertified.svg')
    );
  }
}
