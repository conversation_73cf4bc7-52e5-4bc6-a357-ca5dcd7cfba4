import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { MatLegacyDialog as MatDialog } from '@angular/material/legacy-dialog';

import { TranslateService } from '@ngx-translate/core';
import * as _ from 'lodash';
import { firstValueFrom, Observable } from 'rxjs';

import { ActionMetaType } from '../model/cli-script';
import { smallDisplayMenu } from '../model/menu';
import { ErrorService } from './error.service';
import { GlobalService } from './global-services';
import { DataType } from './mx-platform/DataDef/DataTypeDefinitions';
import { SiteUnit } from './mx-platform/DataDef/SiteDataDefs';
import { DataRepositoryService } from './mx-platform/DataRepository/DataRepository';
import { DataService } from './mx-platform/Service/DataService';
import { LoginResult, LoginService } from './mx-platform/Service/LoginService';
import { TriggerService } from './mx-platform/Service/TriggerService';

@Injectable()
export class AuthService {
  nodes: any[];
  menuData;
  blockedUrls;
  // menu tree object
  private allMenu;
  private defaultProfile = [
    {
      profileName: 'default',
      menus: {},
      toolBar: [],
    },
  ];
  private mxviewOneProfile = [
    {
      profileName: 'MXview One',
      menus: {},
      toolBar: [
        // 'securityView',
        'vlanView',
        'wirelessTableView',
        'generateQrCode',
        'assignModel',
        'basicInformation',
        'ipConfiguration',
        'ping',
        'devicePanel',
        'setDocument',
        'deviceLocator',
        'webConsoleIE',
      ],
    },
  ];
  private taiwanPowerProfile = [
    {
      profileName: 'taiwanPower',
      menus: {
        dashboard: [],
        report: ['vlanTable', 'availabilityReport'],
        northbound: [
          // "apiManagement",
          'embedWidget',
          'customOPC',
          'doc',
        ],
        migration: ['jobScheduler'],
        wizard: [],
      },
      toolBar: [
        'securityView',
        'vlanView',
        'wirelessTableView',
        'generateQrCode',
        'assignModel',
        'basicInformation',
        'ipConfiguration',
        'ping',
        'devicePanel',
        'setDocument',
        'deviceLocator',
        'setPortLabel',
        'webConsoleIE',
      ],
    },
  ];

  private deleteDeviceMenuData = {
    title: 'NETWORK_MENU.delete',
    icon: 'delete_forever',
    click: 'openDeleteDeviceDialog',
    key: 'deleteDevice',
  };

  private edrSelectedMenuData = [
    {
      title: 'NETWORK_MENU.device_configuration',
      icon: 'build',
      menuName: 'deviceConfiguration',
      menuData: [
        {
          title: 'NETWORK_MENU.maintenance.menu.generate_qr_code',
          svgIcon: 'qr_code',
          click: 'generateQrCode',
          key: 'generateQrCode',
        },
        {
          title: 'NETWORK_MENU.maintenance.menu.assign_model',
          icon: 'touch_app',
          click: 'openAssignModelDialog',
          key: 'assignModel',
        },
        {
          title: 'NETWORK_MENU.maintenance.menu.basic_information',
          icon: 'info',
          click: 'openDeviceBasicInfoDialog',
          key: 'basicInformation',
        },
        {
          title: 'NETWORK_MENU.maintenance.menu.ip_configuration',
          icon: 'settings',
          click: 'openDeviceIPConfig',
          key: 'ipConfiguration',
        },
        {
          title: 'NETWORK_MENU.maintenance.menu.trap_server',
          icon: 'computer',
          click: 'openDeviceTrapConfig',
          key: 'trapServer',
        },
        {
          title: 'NETWORK_MENU.maintenance.menu.port_settings',
          icon: 'crop_square',
          click: 'openPortSettingDialog',
          key: 'portSettings',
        },
        {
          title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
          icon: 'settings',
          click: 'openSNMPSetting',
          key: 'snmpSettings',
        },
        {
          title: 'NETWORK_MENU.maintenance.menu.polling_settings',
          icon: 'settings',
          click: 'openPollingSetting',
          key: 'pollingSettings',
        },
        {
          title: 'NETWORK_MENU.device_login_account',
          icon: 'settings',
          click: 'openDeviceLoginAccount',
          key: 'deviceLoginAccount',
        },
        {
          title: 'NETWORK_MENU.modify_device_alias',
          icon: 'settings',
          click: 'openModifyDeviceAlias',
          key: 'modifyDeviceAlias',
        },
        // {
        //   title: 'NETWORK_MENU.maintenance.menu.polling_ip_setting',
        //   icon: 'settings',
        //   click: 'openDevicePollingIpSetting',
        //   key: 'pollingIpSetting',
        // },
        {
          title: 'NETWORK_MENU.maintenance.menu.change_device_icon',
          icon: 'settings',
          click: 'openChangeDeviceIcon',
          key: 'changeDeviceIcon',
        },
      ],
    },
    {
      title: 'NETWORK_MENU.device_control',
      icon: 'device_hub',
      menuName: 'deviceControl',
      menuData: [
        {
          title: 'NETWORK_MENU.maintenance.menu.upgrade_firmware',
          icon: 'file_upload',
          click: 'openUpgradeFirmwareDialog',
          key: 'upgradeFirmware',
        },
        {
          title: 'NETWORK_MENU.maintenance.menu.import_config',
          icon: 'cloud_download',
          click: 'openImportConfig',
          key: 'importConfig',
        },
        {
          title: 'NETWORK_MENU.maintenance.menu.export_config',
          icon: 'backup',
          click: 'openExportConfig',
          key: 'exportConfig',
        },
      ],
    },
    { title: 'NETWORK_MENU.tools.menu.web_console', icon: 'web', click: 'openWebConsole', key: 'webConsole' },
    {
      title: 'NETWORK_MENU.execute_cli.title',
      icon: 'integration_instructions',
      menuName: 'executeCli',
      menuData: [
        {
          title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
          icon: 'terminal',
          click: 'openCliInterface',
          key: 'cliInterface',
        },
        {
          title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
          icon: 'library_books',
          click: 'openExecuteCliObject',
          key: 'executeCliObject',
        },
      ],
    },
    { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
    {
      title: 'NETWORK_MENU.refresh',
      icon: 'refresh',
      click: 'refreshDevice',
      key: 'refreshDevice',
    },
    {
      title: 'NETWORK_MENU.add_link',
      icon: 'link',
      click: 'openAddLink',
      key: 'addLink',
    },
    {
      title: 'NETWORK_MENU.ipsec_status',
      icon: 'lock',
      click: 'openIpSecTable',
      key: 'ipsecStatus',
    },
    this.deleteDeviceMenuData,
  ];

  private edrDeviceTableSeletedMenuData = [
    {
      title: 'NETWORK_MENU.device_configuration',
      icon: 'build',
      menuName: 'deviceConfiguration',
      menuData: [
        {
          title: 'NETWORK_MENU.maintenance.menu.generate_qr_code',
          svgIcon: 'qr_code',
          click: 'generateQrCode',
          key: 'generateQrCode',
        },
        {
          title: 'NETWORK_MENU.maintenance.menu.assign_model',
          icon: 'touch_app',
          click: 'openAssignModelDialog',
          key: 'assignModel',
        },
        {
          title: 'NETWORK_MENU.maintenance.menu.basic_information',
          icon: 'info',
          click: 'openDeviceBasicInfoDialog',
          key: 'basicInformation',
        },
        {
          title: 'NETWORK_MENU.maintenance.menu.ip_configuration',
          icon: 'settings',
          click: 'openDeviceIPConfig',
          key: 'ipConfiguration',
        },
        {
          title: 'NETWORK_MENU.maintenance.menu.trap_server',
          icon: 'computer',
          click: 'openDeviceTrapConfig',
          key: 'trapServer',
        },
        {
          title: 'NETWORK_MENU.maintenance.menu.port_settings',
          icon: 'crop_square',
          click: 'openPortSettingDialog',
          key: 'portSettings',
        },
        {
          title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
          icon: 'settings',
          click: 'openSNMPSetting',
          key: 'snmpSettings',
        },
        {
          title: 'NETWORK_MENU.maintenance.menu.polling_settings',
          icon: 'settings',
          click: 'openPollingSetting',
          key: 'pollingSettings',
        },
        {
          title: 'NETWORK_MENU.device_login_account',
          icon: 'settings',
          click: 'openDeviceLoginAccount',
          key: 'deviceLoginAccount',
        },
        {
          title: 'NETWORK_MENU.modify_device_alias',
          icon: 'settings',
          click: 'openModifyDeviceAlias',
          key: 'modifyDeviceAlias',
        },
        // {
        //   title: 'NETWORK_MENU.maintenance.menu.polling_ip_setting',
        //   icon: 'settings',
        //   click: 'openDevicePollingIpSetting',
        //   key: 'pollingIpSetting',
        // },
        {
          title: 'NETWORK_MENU.maintenance.menu.change_device_icon',
          icon: 'settings',
          click: 'openChangeDeviceIcon',
          key: 'changeDeviceIcon',
        },
      ],
    },
    {
      title: 'NETWORK_MENU.device_control',
      icon: 'device_hub',
      menuName: 'deviceControl',
      menuData: [
        {
          title: 'NETWORK_MENU.maintenance.menu.upgrade_firmware',
          icon: 'file_upload',
          click: 'openUpgradeFirmwareDialog',
          key: 'upgradeFirmware',
        },
        {
          title: 'NETWORK_MENU.maintenance.menu.import_config',
          icon: 'cloud_download',
          click: 'openImportConfig',
          key: 'importConfig',
        },
        {
          title: 'NETWORK_MENU.maintenance.menu.export_config',
          icon: 'backup',
          click: 'openExportConfig',
          key: 'exportConfig',
        },
      ],
    },
    { title: 'NETWORK_MENU.tools.menu.web_console', icon: 'web', click: 'openWebConsole', key: 'webConsole' },

    {
      title: 'NETWORK_MENU.execute_cli.title',
      icon: 'integration_instructions',
      menuName: 'executeCli',
      menuData: [
        {
          title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
          icon: 'terminal',
          click: 'openCliInterface',
          key: 'cliInterface',
        },
        {
          title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
          icon: 'library_books',
          click: 'openExecuteCliObject',
          key: 'executeCliObject',
        },
      ],
    },
    { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
    {
      title: 'NETWORK_MENU.refresh',
      icon: 'refresh',
      click: 'refreshDevice',
      key: 'refreshDevice',
    },
    {
      title: 'NETWORK_MENU.ipsec_status',
      icon: 'lock',
      click: 'openIpSecTable',
      key: 'ipsecStatus',
    },
    this.deleteDeviceMenuData,
  ];

  private twoDevicesSelectedMenuData = [
    {
      title: 'NETWORK_MENU.device_configuration',
      icon: 'build',
      menuName: 'deviceConfiguration',
      menuData: [
        {
          title: 'NETWORK_MENU.maintenance.menu.generate_qr_code',
          svgIcon: 'qr_code',
          click: 'generateQrCode',
          key: 'generateQrCode',
        },
        {
          title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
          icon: 'settings',
          click: 'openSNMPSetting',
          key: 'snmpSettings',
        },
        {
          title: 'NETWORK_MENU.maintenance.menu.change_device_icon',
          icon: 'settings',
          click: 'openChangeDeviceIcon',
          key: 'changeDeviceIcon',
        },
      ],
    },
    {
      title: 'NETWORK_MENU.alignment.title',
      icon: 'format_align_left',
      menuName: 'Alignment',
      menuData: [
        {
          title: 'NETWORK_MENU.alignment.top',
          icon: 'vertical_align_top',
          click: 'alignmentTop',
          key: 'alignmentTop',
        },
        {
          title: 'NETWORK_MENU.alignment.bottom',
          icon: 'vertical_align_bottom',
          click: 'alignmentBottom',
          key: 'alignmentBottom',
        },
        {
          title: 'NETWORK_MENU.alignment.left',
          icon: 'format_align_left',
          click: 'alignmentLeft',
          key: 'alignmentLeft',
        },
        {
          title: 'NETWORK_MENU.alignment.right',
          icon: 'format_align_right',
          click: 'alignmentRight',
          key: 'alignmentRight',
        },
      ],
    },
    {
      title: 'NETWORK_MENU.execute_cli.title',
      icon: 'integration_instructions',
      menuName: 'executeCli',
      menuData: [
        {
          title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
          icon: 'terminal',
          click: 'openCliInterface',
          key: 'cliInterface',
        },
        {
          title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
          icon: 'library_books',
          click: 'openExecuteCliObject',
          key: 'executeCliObject',
        },
      ],
    },
    {
      title: 'NETWORK_MENU.add_link',
      icon: 'link',
      click: 'openAddLink',
      key: 'addLink',
    },
    { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
    this.deleteDeviceMenuData,
  ];

  private deviceTableMultiSelectedMenuData = [
    {
      title: 'NETWORK_MENU.device_configuration',
      icon: 'build',
      menuName: 'deviceConfiguration',
      menuData: [
        {
          title: 'NETWORK_MENU.maintenance.menu.generate_qr_code',
          svgIcon: 'qr_code',
          click: 'generateQrCode',
          key: 'generateQrCode',
        },
        {
          title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
          icon: 'settings',
          click: 'openSNMPSetting',
          key: 'snmpSettings',
        },
        {
          title: 'NETWORK_MENU.maintenance.menu.change_device_icon',
          icon: 'settings',
          click: 'openChangeDeviceIcon',
          key: 'changeDeviceIcon',
        },
      ],
    },
    {
      title: 'NETWORK_MENU.execute_cli.title',
      icon: 'integration_instructions',
      menuName: 'executeCli',
      menuData: [
        {
          title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
          icon: 'terminal',
          click: 'openCliInterface',
          key: 'cliInterface',
        },
        {
          title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
          icon: 'library_books',
          click: 'openExecuteCliObject',
          key: 'executeCliObject',
        },
      ],
    },
    { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
    this.deleteDeviceMenuData,
  ]

  private deviceMultiSelectedMenuData = [
    {
      title: 'NETWORK_MENU.device_configuration',
      icon: 'build',
      menuName: 'deviceConfiguration',
      menuData: [
        {
          title: 'NETWORK_MENU.maintenance.menu.generate_qr_code',
          svgIcon: 'qr_code',
          click: 'generateQrCode',
          key: 'generateQrCode',
        },
        {
          title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
          icon: 'settings',
          click: 'openSNMPSetting',
          key: 'snmpSettings',
        },
        {
          title: 'NETWORK_MENU.maintenance.menu.change_device_icon',
          icon: 'settings',
          click: 'openChangeDeviceIcon',
          key: 'changeDeviceIcon',
        },
      ],
    },
    {
      title: 'NETWORK_MENU.alignment.title',
      icon: 'format_align_left',
      menuName: 'Alignment',
      menuData: [
        {
          title: 'NETWORK_MENU.alignment.top',
          icon: 'vertical_align_top',
          click: 'alignmentTop',
          key: 'alignmentTop',
        },
        {
          title: 'NETWORK_MENU.alignment.bottom',
          icon: 'vertical_align_bottom',
          click: 'alignmentBottom',
          key: 'alignmentBottom',
        },
        {
          title: 'NETWORK_MENU.alignment.left',
          icon: 'format_align_left',
          click: 'alignmentLeft',
          key: 'alignmentLeft',
        },
        {
          title: 'NETWORK_MENU.alignment.right',
          icon: 'format_align_right',
          click: 'alignmentRight',
          key: 'alignmentRight',
        },
      ],
    },
    {
      title: 'NETWORK_MENU.execute_cli.title',
      icon: 'integration_instructions',
      menuName: 'executeCli',
      menuData: [
        {
          title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
          icon: 'terminal',
          click: 'openCliInterface',
          key: 'cliInterface',
        },
        {
          title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
          icon: 'library_books',
          click: 'openExecuteCliObject',
          key: 'executeCliObject',
        },
      ],
    },
    { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
    this.deleteDeviceMenuData,
  ];

  private powerDeviceMenuData = {
    title: 'TOPOLOGY.prp_hsr_tags',
    icon: 'label',
    click: 'openTagDeviceDialog',
    key: 'tag',
  };
  private powerPrpHsrMenuData = {
    title: 'TOPOLOGY.prp_hsr_tags',
    icon: 'label',
    click: 'openTagLinkDialog',
    key: 'tag',
  };
  private visualizationMenuData = [
    {
      title: 'NETWORK_MENU.visualization.menu.traffic_view',
      svgIcon: 'traffic',
      click: 'showTrafficView',
      key: 'trafficView',
    },
    {
      title: 'NETWORK_MENU.visualization.menu.security_view',
      icon: 'security',
      click: 'showSecurityView',
      key: 'securityView',
    },
    {
      title: 'NETWORK_MENU.visualization.menu.vlan_view',
      icon: 'network_cell',
      click: 'showVlanView',
      key: 'vlanView',
    },
  ];

  private wirelessMenu = {
    title: 'NETWORK_MENU.wireless.title',
    icon: 'wifi',
    menuName: 'Wireless',
    menuData: [
      {
        title: 'NETWORK_MENU.wireless.menu.wireless_table_view',
        icon: 'view_list',
        click: 'showWirelessTable',
        key: 'wirelessTableView',
      },
      {
        title: 'NETWORK_MENU.wireless.menu.wireless_playback_view',
        icon: 'replay',
        click: 'showWirelessPlaybackView',
        key: 'wirelessPlaybackView',
      },
    ],
  };

  private powerMenu = {
    title: 'NETWORK_MENU.grid.title',
    icon: 'flash_on',
    menuName: 'grid',
    menuData: [
      {
        title: 'NETWORK_MENU.grid.menu.import_scd',
        icon: 'insert_drive_file',
        click: 'openImportScd',
        key: 'importScd',
      },
    ],
  };

  private wirelessAddonDeviceSelectedMenu = {
    wirelessAddonDeviceSelectedMenu: [
      {
        title: 'NETWORK_MENU.device_configuration',
        icon: 'build',
        menuName: 'deviceConfiguration',
        menuData: [
          {
            title: 'NETWORK_MENU.maintenance.menu.trap_server',
            icon: 'computer',
            click: 'openDeviceTrapConfig',
            key: 'trapServer',
          },
          {
            title: 'NETWORK_MENU.maintenance.menu.port_settings',
            icon: 'crop_square',
            click: 'openPortSettingDialog',
            key: 'portSettings',
          },
          {
            title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
            icon: 'settings',
            click: 'openSNMPSetting',
            key: 'snmpSettings',
          },
          {
            title: 'NETWORK_MENU.maintenance.menu.polling_settings',
            icon: 'settings',
            click: 'openPollingSetting',
            key: 'pollingSettings',
          },
          {
            title: 'NETWORK_MENU.device_login_account',
            icon: 'settings',
            click: 'openDeviceLoginAccount',
            key: 'deviceLoginAccount',
          },
          {
            title: 'NETWORK_MENU.modify_device_alias',
            icon: 'settings',
            click: 'openModifyDeviceAlias',
            key: 'modifyDeviceAlias',
          },
          {
            title: 'NETWORK_MENU.maintenance.menu.change_device_icon',
            icon: 'settings',
            click: 'openChangeDeviceIcon',
            key: 'changeDeviceIcon',
          },
        ],
      },
      {
        title: 'NETWORK_MENU.device_control',
        icon: 'device_hub',
        menuName: 'deviceControl',
        menuData: [
          {
            title: 'NETWORK_MENU.maintenance.menu.upgrade_firmware',
            icon: 'file_upload',
            click: 'openUpgradeFirmwareDialog',
          },
          {
            title: 'NETWORK_MENU.maintenance.menu.import_config',
            icon: 'cloud_download',
            click: 'openImportConfig',
            key: 'importConfig',
          },
          {
            title: 'NETWORK_MENU.maintenance.menu.export_config',
            icon: 'backup',
            click: 'openExportConfig',
            key: 'exportConfig',
          },
        ],
      },
      { title: 'NETWORK_MENU.tools.menu.web_console', icon: 'web', click: 'openWebConsole', key: 'webConsole' },
      {
        title: 'NETWORK_MENU.execute_cli.title',
        icon: 'integration_instructions',
        menuName: 'executeCli',
        menuData: [
          {
            title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
            icon: 'terminal',
            click: 'openCliInterface',
            key: 'cliInterface',
          },
          {
            title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
            icon: 'library_books',
            click: 'openExecuteCliObject',
            key: 'executeCliObject',
          },
        ],
      },

      { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
      {
        title: 'NETWORK_MENU.refresh',
        icon: 'refresh',
        click: 'refreshDevice',
        key: 'refreshDevice',
      },
      {
        title: 'NETWORK_MENU.add_link',
        icon: 'link',
        click: 'openAddLink',
        key: 'addLink',
      },
      {
        title: 'NETWORK_MENU.device_dashboard',
        icon: 'dashboard',
        click: 'openWirelessDeviceDashboard',
        key: 'deviceDashboard',
      },
      this.deleteDeviceMenuData,
    ],
    newWirelessAddonDeviceSelectedMenu: [
      {
        title: 'NETWORK_MENU.device_configuration',
        icon: 'build',
        menuName: 'deviceConfiguration',
        menuData: [
          {
            title: 'NETWORK_MENU.maintenance.menu.trap_server',
            icon: 'computer',
            click: 'openDeviceTrapConfig',
            key: 'trapServer',
          },
          {
            title: 'NETWORK_MENU.maintenance.menu.port_settings',
            icon: 'crop_square',
            click: 'openPortSettingDialog',
            key: 'portSettings',
          },
          {
            title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
            icon: 'settings',
            click: 'openSNMPSetting',
            key: 'snmpSettings',
          },
          {
            title: 'NETWORK_MENU.maintenance.menu.polling_settings',
            icon: 'settings',
            click: 'openPollingSetting',
            key: 'pollingSettings',
          },
          {
            title: 'NETWORK_MENU.device_login_account',
            icon: 'settings',
            click: 'openDeviceLoginAccount',
            key: 'deviceLoginAccount',
          },
          {
            title: 'NETWORK_MENU.modify_device_alias',
            icon: 'settings',
            click: 'openModifyDeviceAlias',
            key: 'modifyDeviceAlias',
          },
          {
            title: 'NETWORK_MENU.maintenance.menu.change_device_icon',
            icon: 'settings',
            click: 'openChangeDeviceIcon',
            key: 'changeDeviceIcon',
          },
        ],
      },
      {
        title: 'NETWORK_MENU.device_control',
        icon: 'device_hub',
        menuName: 'deviceControl',
        menuData: [
          {
            title: 'NETWORK_MENU.maintenance.menu.upgrade_firmware',
            icon: 'file_upload',
            click: 'openUpgradeFirmwareDialog',
            key: 'upgradeFirmware',
          },
        ],
      },
      { title: 'NETWORK_MENU.tools.menu.web_console', icon: 'web', click: 'openWebConsole', key: 'webConsole' },
      {
        title: 'NETWORK_MENU.execute_cli.title',
        icon: 'integration_instructions',
        menuName: 'executeCli',
        menuData: [
          {
            title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
            icon: 'terminal',
            click: 'openCliInterface',
            key: 'cliInterface',
          },
          {
            title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
            icon: 'library_books',
            click: 'openExecuteCliObject',
            key: 'executeCliObject',
          },
        ],
      },
      { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
      {
        title: 'NETWORK_MENU.refresh',
        icon: 'refresh',
        click: 'refreshDevice',
        key: 'refreshDevice',
      },
      {
        title: 'NETWORK_MENU.add_link',
        icon: 'link',
        click: 'openAddLink',
        key: 'addLink',
      },
      {
        title: 'NETWORK_MENU.device_dashboard',
        icon: 'dashboard',
        click: 'openWirelessDeviceDashboard',
        key: 'deviceDashboard',
      },
      this.deleteDeviceMenuData,
    ],
    oldWirelessAddonDeviceSelectedMenu: [
      {
        title: 'NETWORK_MENU.device_configuration',
        icon: 'build',
        menuName: 'deviceConfiguration',
        menuData: [
          {
            title: 'NETWORK_MENU.maintenance.menu.trap_server',
            icon: 'computer',
            click: 'openDeviceTrapConfig',
            key: 'trapServer',
          },
          {
            title: 'NETWORK_MENU.maintenance.menu.port_settings',
            icon: 'crop_square',
            click: 'openPortSettingDialog',
            key: 'portSettings',
          },
          {
            title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
            icon: 'settings',
            click: 'openSNMPSetting',
            key: 'snmpSettings',
          },
          {
            title: 'NETWORK_MENU.maintenance.menu.polling_settings',
            icon: 'settings',
            click: 'openPollingSetting',
            key: 'pollingSettings',
          },
          {
            title: 'NETWORK_MENU.device_login_account',
            icon: 'settings',
            click: 'openDeviceLoginAccount',
            key: 'deviceLoginAccount',
          },
          {
            title: 'NETWORK_MENU.modify_device_alias',
            icon: 'settings',
            click: 'openModifyDeviceAlias',
            key: 'modifyDeviceAlias',
          },
          {
            title: 'NETWORK_MENU.maintenance.menu.change_device_icon',
            icon: 'settings',
            click: 'openChangeDeviceIcon',
            key: 'changeDeviceIcon',
          },
        ],
      },
      { title: 'NETWORK_MENU.tools.menu.web_console', icon: 'web', click: 'openWebConsole', key: 'webConsole' },
      {
        title: 'NETWORK_MENU.execute_cli.title',
        icon: 'integration_instructions',
        menuName: 'executeCli',
        menuData: [
          {
            title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
            icon: 'terminal',
            click: 'openCliInterface',
            key: 'cliInterface',
          },
          {
            title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
            icon: 'library_books',
            click: 'openExecuteCliObject',
            key: 'executeCliObject',
          },
        ],
      },
      { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
      {
        title: 'NETWORK_MENU.refresh',
        icon: 'refresh',
        click: 'refreshDevice',
        key: 'refreshDevice',
      },
      {
        title: 'NETWORK_MENU.add_link',
        icon: 'link',
        click: 'openAddLink',
        key: 'addLink',
      },
      {
        title: 'NETWORK_MENU.device_dashboard',
        icon: 'dashboard',
        click: 'openWirelessDeviceDashboard',
        key: 'deviceDashboard',
      },
      this.deleteDeviceMenuData,
    ],
  };

  private securityEdrSelectedMenu = {
    id: 'cybersecurityControl',
    title: 'NETWORK_MENU.cybersecurity_control',
    icon: 'security',
    menuName: 'cybersecurityControl',
    menuData: [
      {
        title: 'NETWORK_MENU.policy_profile_deployment',
        svgIcon: 'deployed_code',
        click: ActionMetaType.POLICY_PROFILE_DEPLOYMENT,
        key: 'policyProfileDeployment',
      },
      {
        title: 'NETWORK_MENU.security_package_deployment',
        svgIcon: 'deployed_code',
        click: ActionMetaType.SECURITY_PACKAGE_DEPLOYMENT,
        key: 'securityPackageDeployment',
      }
    ],
  };
  private securityEdrIpsSelectedMenu = {
    ...this.securityEdrSelectedMenu,
    menuData: [
      ...this.securityEdrSelectedMenu.menuData,
      {
        title: 'NETWORK_MENU.ips_configuration',
        icon: 'settings',
        click: ActionMetaType.IPS_CONFIGURATION,
        key: 'ipsConfiguration',
      },
    ],
  };

  constructor(
    private dataService: DataService,
    private dataRepository: DataRepositoryService,
    private dialog: MatDialog,
    private errorService: ErrorService,
    private globalService: GlobalService,
    private loginService: LoginService,
    private router: Router,
    private triggerService: TriggerService,
    private translate: TranslateService
  ) {
    this.allMenu = [
      {
        key: 'topology',
        href: '/pages/network',
      },
      {
        key: 'allEvent',
        href: '/pages/event/all-event',
      },
      {
        key: 'eventSettings',
        href: '/pages/event/event-settings',
      },
      {
        key: 'syslogSettings',
        href: '/pages/event/syslog-settings',
      },
      {
        key: 'notification',
        href: '/pages/event/notification',
      },
      {
        key: 'scanRange',
        href: '/pages/scan-range',
      },
      {
        key: 'deviceManagement',
        href: '/pages/device-management',
      },
      {
        key: 'accountPassword',
        href: '/pages/account-password',
      },
      {
        key: 'cliObjectDatabase',
        href: '/pages/cli-object-database',
      },
      {
        key: 'firmwareManagement',
        href: '/pages/firmware-management',
      },
      {
        key: 'firewallPolicyManagement',
        href: '/pages/firewall-policy-management',
      },
      {
        key: 'dashboard',
        href: '/pages/dashboard',
      },
      {
        key: 'opcUaServer',
        href: '/pages/northbound-interface/opc-ua-server',
      },
      {
        key: 'apiManagement',
        href: '/pages/northbound-interface/api-management',
      },
      {
        key: 'embedWidget',
        href: '/pages/northbound-interface/embed-widget',
      },
      {
        key: 'jobScheduler',
        href: '/pages/migrations/job-scheduler',
      },
      {
        key: 'configCenter',
        href: '/pages/migrations/config-center',
      },
      {
        key: 'preferences',
        href: '/pages/preferences',
      },
      {
        key: 'accountManagement',
        href: '/pages/account',
      },
      {
        key: 'license',
        href: '/pages/license',
      },
      {
        key: 'about',
        href: '/pages/about',
      },
      {
        key: 'firewallPolicyManagement',
        href: '/pages/firewall-policy-management',
      },
    ];
  }

  login(username, password): Observable<LoginResult> {
    return this.loginService.doLogin(username, password);
  }

  logout(): void {
    sessionStorage.setItem('inDiscoveryProgress', '-1');
    sessionStorage.setItem('inMXviewWizardProgress', '-1');
    sessionStorage.setItem('isFromMXviewWizard', '-1');
    localStorage.setItem('loginUser', '');
    sessionStorage.setItem('userMode', '');
    sessionStorage.setItem('showLoginNotification', 'NO');
    sessionStorage.setItem('isRefresh', '0');
    this.dialog.closeAll();
    this.triggerService.doDisconnect();
    this.loginService.doLogout();
    this.router.navigate(['/login']);
  }

  generateUiProfile(username): Observable<any> {
    return new Observable(observer => {
      // If showLicensePage is true, use defaultProfile
      if (localStorage.getItem('showLicensePage') === '1') {
        this.generateDynamicMenuNodes(this.defaultProfile[0]);
        const menuData = this.generateCommandBarMenus(this.defaultProfile[0]);
        observer.next(menuData);
        observer.complete();
      } else {
        if (username) {
          let siteId = localStorage.getItem('siteId');
          if (siteId === '-1') {
            siteId = this.autoSelectSiteId();
          }
          if (siteId !== '-1') {
            Promise.all([
              this.dataService.getUserPreference(username),
              this.dataService.getAccountList(username),
              this.dataService.getSite(),
              firstValueFrom(this.dataService.getAddonLicenseBySite(siteId)),
            ])
              .then(([userPreference, accounts, sites, addonInfo]) => {
                // Create Addons List
                this.globalService.createAddonsList(sites, siteId);
                this.globalService.createAddonInfo(addonInfo);
                if (accounts[0] !== undefined) {
                  switch (accounts[0].authority) {
                    case 65537:
                      sessionStorage.setItem('userMode', 'admin');
                      break;
                    case 131073:
                      sessionStorage.setItem('userMode', 'supervisor');
                      break;
                    case 131074:
                      sessionStorage.setItem('userMode', 'user');
                      break;
                    case 131076:
                      sessionStorage.setItem('userMode', 'demo-user');
                      break;
                  }

                  if (sessionStorage.getItem('userMode') === 'user') {
                    const defaultProfile = this.getProfile();
                    defaultProfile[0] = this.addUserBlockUrl(defaultProfile[0]);
                    // this.generateUserMenuNode(defaultProfile[0]);
                    this.generateDynamicMenuNodes(defaultProfile[0]);
                    const menuData = this.generateCommandBarMenus(defaultProfile[0]);
                    observer.next(menuData);
                    observer.complete();
                  } else if (sessionStorage.getItem('userMode') === 'demo-user') {
                    // Load the default profile for demo user
                    const defaultProfile = this.getProfile();
                    this.generateDynamicMenuNodes(defaultProfile[0]);
                    const menuData = this.generateCommandBarMenus(defaultProfile[0]);
                    observer.next(menuData);
                    observer.complete();
                  } else {
                    this.dataService.getUiProfileData(siteId, userPreference[0].ui_profile).subscribe(
                      uiProfile => {
                        uiProfile = this.getProfile();
                        if (sessionStorage.getItem('userMode') === 'supervisor') {
                          uiProfile[0] = this.addSupervisorBlockUrl(uiProfile[0]);
                        }
                        this.generateDynamicMenuNodes(uiProfile[0]);
                        const menuData = this.generateCommandBarMenus(uiProfile[0]);
                        observer.next(menuData);
                        observer.complete();
                      },
                      (error: unknown) => {
                        observer.error();
                      }
                    );
                  }
                }
              })
              .catch(error => {
                console.log('generateUiProfile error=' + JSON.stringify(error));
              });
          } else {
            observer.error();
          }
        }
      }
    });
  }

  getProfile(): any[] {
    let uiProfile = [{ profileName: 'default', menus: {}, toolBar: [] }];
    if (localStorage.getItem('taiwan-power-profile') === '1') {
      // Remove menu items at Taiwan Power project
      uiProfile = _.cloneDeep(this.taiwanPowerProfile);
    } else {
      uiProfile = _.cloneDeep(this.mxviewOneProfile);
    }
    return uiProfile;
  }

  generateCommandBarMenus(uiProfile): void {
    this.menuData = [];
    this.menuData = this.getMenuData();
    if (uiProfile.toolBar.length >= 0) {
      for (const key of uiProfile.toolBar) {
        for (const [menuKey, menuValue] of Object.entries(this.menuData)) {
          for (const [k, v] of Object.entries(menuValue)) {
            if (v.menuData) {
              // Remove item from the second level menu
              this.menuData[menuKey][k].menuData = _.reject(v.menuData, o => {
                return o.key === key;
              });
            }
          }
          // Remove item from the first level menu
          const menuValueArr: any = menuValue;
          this.menuData[menuKey] = _.reject(menuValueArr, o => {
            return o.key === key;
          });
        }
      }
      // Remove the first level menu if its menuData is empty
      for (const menuKey of Object.keys(this.menuData)) {
        this.menuData[menuKey] = _.reject(this.menuData[menuKey], o => {
          return o.menuData && o.menuData.length === 0;
        });
      }
    }
    if (localStorage.getItem('showLicensePage') !== '1') {
      // Add new platform required items
      if (_.includes(this.globalService.siteInfo?.now.addons, 'wireless')) {
        // Add wireless addons required items
        this.menuData['notSelectedMenu'].push(this.wirelessMenu);
        const wirelessAddonDeviceSelectedMenu = _.cloneDeep(this.wirelessAddonDeviceSelectedMenu);
        this.menuData = { ...this.menuData, ...wirelessAddonDeviceSelectedMenu };
      }
      if (_.includes(this.globalService.siteInfo?.now.addons, 'grid')) {
        // Add power addons required items
        this.menuData['notSelectedMenu'].push(this.powerMenu);
        this.menuData['deviceTableSelectedMenu'].splice(
          this.menuData['deviceTableSelectedMenu'].length - 1,
          0,
          this.powerDeviceMenuData
        );
        this.menuData['deviceSelectedMenu'].splice(
          this.menuData['deviceSelectedMenu'].length - 1,
          0,
          this.powerDeviceMenuData
        );
        this.menuData['twoDevicesSelectedMenu'].splice(
          this.menuData['twoDevicesSelectedMenu'].length - 1,
          0,
          this.powerDeviceMenuData
        );
        this.menuData['deviceTableMultiSelectedMenu'].splice(
          this.menuData['deviceTableMultiSelectedMenu'].length - 1,
          0,
          this.powerDeviceMenuData
        );
        this.menuData['deviceMultiSelectedMenu'].splice(
          this.menuData['deviceMultiSelectedMenu'].length - 1,
          0,
          this.powerDeviceMenuData
        );
        this.menuData['icmpSelectedMenu'].splice(
          this.menuData['icmpSelectedMenu'].length - 1,
          0,
          this.powerDeviceMenuData
        );
        this.menuData['edrSelectedMenu'].splice(
          this.menuData['edrSelectedMenu'].length - 1,
          0,
          this.powerDeviceMenuData
        );
        this.menuData['edrDeviceTableSelectedMenu'].splice(
          this.menuData['edrDeviceTableSelectedMenu'].length - 1,
          0,
          this.powerDeviceMenuData
        );
        // 加入 PRP/HSR Tags 功能
        this.menuData['linkSelectedMenu'].splice(
          this.menuData['linkSelectedMenu'].length - 1,
          0,
          this.powerPrpHsrMenuData
        );
        this.menuData['linkMultiSelectedMenu'].splice(
          this.menuData['linkMultiSelectedMenu'].length - 1,
          0,
          this.powerPrpHsrMenuData
        );
        this.menuData['sfpLinkSelectedMenu'].splice(
          this.menuData['sfpLinkSelectedMenu'].length - 1,
          0,
          this.powerPrpHsrMenuData
        );
        this.menuData['computerSelectedMenu'].splice(
          this.menuData['computerSelectedMenu'].length - 1,
          0,
          this.powerPrpHsrMenuData
        );
        this.menuData['computerTableSelectedMenu'].splice(
          this.menuData['computerTableSelectedMenu'].length - 1,
          0,
          this.powerPrpHsrMenuData
        );
        this.menuData['da820CLinkSelectedMenu'].splice(
          this.menuData['da820CLinkSelectedMenu'].length - 1,
          0,
          this.powerPrpHsrMenuData
        );
        this.menuData['da820CSfpLinkSelectedMenu'].splice(
          this.menuData['da820CSfpLinkSelectedMenu'].length - 1,
          0,
          this.powerPrpHsrMenuData
        );
      }
      if (_.includes(this.globalService.siteInfo?.now.addons, 'nsm')) {
        this.menuData['edrSelectedMenu'].splice(
          this.menuData['edrSelectedMenu'].length - 1,
          0,
          this.securityEdrSelectedMenu
        );
        this.menuData['edrIpsSelectedMenu'].splice(
          this.menuData['edrIpsSelectedMenu'].length - 1,
          0,
          this.securityEdrIpsSelectedMenu
        );
        this.menuData['twoDevicesSelectedMenu'].splice(
          this.menuData['twoDevicesSelectedMenu'].length - 1,
          0,
          this.securityEdrSelectedMenu
        );
        this.menuData['twoIpsDevicesSelectedMenu'].splice(
          this.menuData['twoIpsDevicesSelectedMenu'].length - 1,
          0,
          this.securityEdrIpsSelectedMenu
        );
        this.menuData['deviceMultiSelectedMenu'].splice(
          this.menuData['deviceMultiSelectedMenu'].length - 1,
          0,
          this.securityEdrSelectedMenu
        );
        this.menuData['ipsDeviceMultiSelectedMenu'].splice(
          this.menuData['ipsDeviceMultiSelectedMenu'].length - 1,
          0,
          this.securityEdrIpsSelectedMenu
        );
        this.menuData['deviceTableMultiSelectedMenu'].splice(
          this.menuData['deviceTableMultiSelectedMenu'].length - 1,
          0,
          this.securityEdrSelectedMenu
        );
        this.menuData['ipsDeviceTableMultiSelectedMenu'].splice(
          this.menuData['ipsDeviceTableMultiSelectedMenu'].length - 1,
          0,
          this.securityEdrIpsSelectedMenu
        );
        this.menuData['edrDeviceTableSelectedMenu'].splice(
          this.menuData['edrDeviceTableSelectedMenu'].length - 1,
          0,
          this.securityEdrSelectedMenu
        );
        this.menuData['edrIpsDeviceTableSelectedMenu'].splice(
          this.menuData['edrIpsDeviceTableSelectedMenu'].length - 1,
          0,
          this.securityEdrIpsSelectedMenu
        );
        this.menuData['menuForDeviceManagement']
          .find(o => o.title === 'NETWORK_MENU.cybersecurity_control')
          .menuData.push(...this.securityEdrSelectedMenu.menuData);
      }
    }
    return this.menuData;
  }

  searchAndPushBlockedUrls(key): void {
    const index = _.findIndex(this.allMenu, (o: any) => {
      return o.key === key;
    });
    if (index !== -1) {
      this.blockedUrls.push(this.allMenu[index].href);
    }
  }

  addSupervisorBlockUrl(uiProfile): void {
    const allSettingKeys = _.keys(uiProfile.menus);
    const index = _.findIndex(allSettingKeys, o => {
      return o === 'accountManagement';
    });
    if (index === -1) {
      // Add accountManagement to ignore list
      uiProfile.menus['administration'] = ['accountManagement'];
    }
    uiProfile.menus['device_management'] = ['accountPassword'];
    return uiProfile;
  }

  addUserBlockUrl(uiProfile): any {
    uiProfile.menus['scan_range_wizard'] = [];
    uiProfile.menus['notification'] = [];
    uiProfile.menus['event'] = ['eventSettings'];
    uiProfile.menus['northbound'] = ['opcUaServer', 'apiManagement', 'embedWidget'];
    uiProfile.menus['configCenter'] = [];
    uiProfile.menus['administration'] = [
      'accountManagement',
      'globalDeviceSettings',
      'systemSettings',
      'maintenanceScheduler',
      'license',
      'preference',
      'troubleshooting',
    ];
    uiProfile.menus['device_management'] = ['accountPassword'];
    return uiProfile;
  }

  autoSelectSiteId(): string {
    const sites: SiteUnit[] = this.dataRepository.getData(DataType.SITE);
    let siteId: string;
    // Select one online site
    _.forEach(sites, site => {
      if (site.status) {
        siteId = site.site_id;
      }
    });
    if (siteId) {
      return siteId;
    } else {
      this.errorService.handleNormalError(this.translate.instant('LOGIN.all_sites_offline'));
      this.logout();
      return '-1';
    }
  }

  generateUserMenuNode(uiProfile?: any): void {
    this.nodes = smallDisplayMenu;
    // User 權限移除 Dashboard
    if (this.hasPowerAddon()) {
      this.nodes.shift();
    }
    if (uiProfile) {
      this.generateRouteBlockUrls(uiProfile);
    }
  }

  generateDynamicMenuNodes(uiProfile): void {
    this.nodes = [];
    // 新 UI 的選單
    const displayMenu = {
      dashboard: {
        id: '0',
        name: this.translate.instant('PAGES_MENU.dashboard'),
        href: '/pages/dashboard',
        icon: '',
        description: 'dashboard',
      },
      topology: {
        id: '1',
        name: this.translate.instant('PAGES_MENU.network.topology'),
        href: '/pages/network',
        icon: '',
        description: 'topology',
      },
      scan_range_wizard: {
        id: '2',
        name: this.translate.instant('PAGES_MENU.scan_range_wizard.title'),
        href: '/pages/scan-range',
        icon: '',
        description: 'scan-range',
      },
      device_management: {
        id: 'device_management',
        name: this.translate.instant('PAGES_MENU.device_management.title'),
        href: '',
        // href: '/pages/device-management',
        icon: '',
        description: 'device_management',
        children: {
          deviceManagement: {
            id: 'deviceManagement',
            name: this.translate.instant('PAGES_MENU.device_management.configuration_control'),
            href: '/pages/device-management/device-management',
            description: 'device-management',
          },
          accountPassword: {
            id: 'accountPassword',
            name: this.translate.instant('PAGES_MENU.device_management.account_password'),
            href: '/pages/device-management/account-password',
            description: 'account-password',
          },
        },
      },
      ...(this.globalService.siteInfo?.now.addons.includes('nsm') && {
        fire_wall_policy_management: {
          id: 'fire_wall_policy_management',
          name: this.translate.instant('PAGES_MENU.firewall_policy_management.title'),
          href: '/pages/firewall-policy-management',
          icon: '',
          description: 'fire_wall_policy_management',
          children: {
            policyProfileManagement: {
              id: 'policyProfileManagement',
              name: this.translate.instant('PAGES_MENU.firewall_policy_management.policy_profile_management'),
              href: '/pages/firewall-policy-management/policy-profile-management',
              description: 'policy-profile-management',
            },
            policyProfileDeployment: {
              id: 'policyProfileDeployment',
              name: this.translate.instant('PAGES_MENU.firewall_policy_management.policy_profile_deployment'),
              href: '/pages/firewall-policy-management/policy-profile-deployment',
              description: 'policy-profile-deployment',
            },
            securityPackageManagement: {
              id: 'securityPackageManagement',
              name: this.translate.instant('PAGES_MENU.firewall_policy_management.security_package_management'),
              href: '/pages/firewall-policy-management/security-package-management',
              description: 'security-package-management',
            },
            securityPackageDeployment: {
              id: 'securityPackageDeployment',
              name: this.translate.instant('PAGES_MENU.firewall_policy_management.security_package_deployment'),
              href: '/pages/firewall-policy-management/security-package-deployment',
              description: 'security-package-deployment',
            },
          },
        },
      }),
      cli_object_database: {
        id: 'cli_object_database',
        name: this.translate.instant('PAGES_MENU.cli_object_database.title'),
        href: '/pages/cli-object-database',
        icon: '',
        description: 'cli_object_database',
      },
      firmware_management: {
        id: '99',
        name: this.translate.instant('PAGES_MENU.firmware_management.title'),
        href: '/pages/firmware-management',
        icon: '',
        description: 'firmware-management',
      },
      configCenter: {
        id: '3',
        name: this.translate.instant('PAGES_MENU.migrations.configuration_center'),
        href: '/pages/migrations/config-center',
        description: 'config-center',
      },
      event: {
        id: '4',
        name: this.translate.instant('PAGES_MENU.event.title'),
        href: '',
        icon: '',
        description: 'event',
        children: {
          allEvent: {
            id: '4-1',
            name: this.translate.instant('PAGES_MENU.event.all_events'),
            href: '/pages/event/all-event',
            description: 'all-event',
          },
          eventSettings: {
            id: '4-2',
            name: this.translate.instant('PAGES_MENU.alert.event_settings'),
            href: '/pages/event/event-settings',
            description: 'event-settings',
          },
          syslogSettings: {
            id: '4-3',
            name: this.translate.instant('PAGES_MENU.event.syslog_settings'),
            href: '/pages/event/syslog-settings',
            description: 'syslog-settings',
          },
        },
      },
      notification: {
        id: '5',
        name: this.translate.instant('PAGES_MENU.alert.notifications'),
        href: '/pages/event/notification',
        icon: '',
        description: 'notification',
      },
      inventory_managment: {
        id: 'inventory_managment',
        name: this.translate.instant('PAGES_MENU.report.inventory_report'),
        href: '',
        icon: '',
        description: 'inventory_managment',
        children: {
          assets_and_warranty: {
            id: 'assets_and_warranty',
            name: this.translate.instant('PAGES_MENU.report.assets_and_warranty'),
            href: '/pages/assets-and-warranty',
            description: 'assets_and_warranty',
          },
          apiManagement: {
            id: 'rogue_device_detection',
            name: this.translate.instant('PAGES_MENU.report.rogue_device_detection'),
            href: '/pages/rogue-device-detection',
            description: 'rogue_device_detectiont',
          },
        },
      },
      northbound: {
        id: '7',
        name: this.translate.instant('PAGES_MENU.northbound_interface.title'),
        href: '',
        icon: '',
        description: 'northbound',
        children: {
          opcUaServer: {
            id: '7-1',
            name: this.translate.instant('PAGES_MENU.northbound_interface.opc_ua_server'),
            href: '/pages/northbound-interface/opc-ua-server',
            description: 'opc-ua-server',
          },
          apiManagement: {
            id: '7-2',
            name: this.translate.instant('PAGES_MENU.northbound_interface.restful_api_management'),
            href: '/pages/northbound-interface/api-management',
            description: 'api-management',
          },
          embedWidget: {
            id: '7-3',
            name: this.translate.instant('PAGES_MENU.northbound_interface.web_widget_embedded'),
            href: '/pages/northbound-interface/embed-widget',
            description: 'embed-widget',
          },
        },
      },
      administration: {
        id: '11',
        name: this.translate.instant('PAGES_MENU.administration.title'),
        href: '',
        icon: '',
        description: 'administration',
        children: {
          accountManagement: {
            id: '11-1',
            name: this.translate.instant('PAGES_MENU.administration.account_management'),
            href: '/pages/account',
            description: 'account',
          },
          deviceSettingsTemplate: {
            id: '11-2',
            name: this.translate.instant('PAGES_MENU.administration.device_settings_template'),
            href: '/pages/device-settings-template',
            description: 'device-settings-template',
          },
          globalDeviceSettings: {
            id: '11-3',
            name: this.translate.instant('PAGES_MENU.administration.global_device_settings'),
            href: '/pages/global-device-settings',
            description: 'global-device-settings',
          },
          systemSettings: {
            id: '11-4',
            name: this.translate.instant('PAGES_MENU.administration.system_settings'),
            href: '/pages/system-settings',
            description: 'system-settings',
          },
          maintenanceScheduler: {
            id: '11-5',
            name: this.translate.instant('PAGES_MENU.administration.maintenance_scheduler'),
            href: '/pages/migrations/job-scheduler',
            description: 'maintenance-scheduler',
          },
          license: {
            id: '11-6',
            name: this.translate.instant('PAGES_MENU.administration.license_management'),
            href: '/pages/license',
            description: 'license',
          },
          preference: {
            id: '11-7',
            name: this.translate.instant('PAGES_MENU.administration.preferences'),
            href: '/pages/preferences',
            description: 'preference',
          },
          troubleshooting: {
            id: '11-8',
            name: this.translate.instant('PAGES_MENU.administration.troubleshooting'),
            href: '/pages/troubleshooting',
            description: 'troubleshooting',
          },
        },
      },
      help: {
        id: '12',
        name: this.translate.instant('PAGES_MENU.help.title'),
        href: '/pages/help',
        description: 'help',
        children: {
          about: {
            id: '12-1',
            name: this.translate.instant('PAGES_MENU.help.about_mxview'),
            href: '/pages/about',
            description: 'about',
          },
          userManual: {
            id: '12-2',
            name: this.translate.instant('PAGES_MENU.help.user_manual'),
            href: 'https://www.moxa.com/en/products/industrial-network-infrastructure/network-management-software/mxview-one-series#resources',
            description: 'userManual',
          },
          doc: {
            id: '12-3',
            name: this.translate.instant('PAGES_MENU.help.api_documentation'),
            href: '/doc',
            description: 'doc',
          },
        },
      },
    };
    const allSettingKeys = _.keys(uiProfile['menus']);
    const allDisplayMenuKeys = _.keys(displayMenu);
    allDisplayMenuKeys.forEach(menuKey => {
      let showMainMenu = true;
      allSettingKeys.forEach(settingKey => {
        if (menuKey === settingKey) {
          if (displayMenu[menuKey].children && uiProfile['menus'][settingKey].length > 0) {
            const childrenKeys = _.keys(displayMenu[menuKey].children);
            const children = [];
            childrenKeys.forEach(elementKey => {
              const hideChildrenMenu = _.includes(uiProfile['menus'][settingKey], elementKey);
              if (!hideChildrenMenu) {
                children.push(displayMenu[menuKey].children[elementKey]);
              }
            });
            displayMenu[menuKey].children = children;
            if (childrenKeys.length === uiProfile['menus'][settingKey].length) {
              showMainMenu = false;
            }
          } else {
            if (!displayMenu[menuKey].children && uiProfile['menus'][settingKey].length === 0) {
              showMainMenu = false;
            }
          }
        }
      });
      if (showMainMenu) {
        if (displayMenu[menuKey].children) {
          const children = [];
          const childrenKeys = _.keys(displayMenu[menuKey].children);
          childrenKeys.forEach(elementKey => {
            children.push(displayMenu[menuKey].children[elementKey]);
          });
          displayMenu[menuKey].children = children;
        }
        this.nodes.push(displayMenu[menuKey]);
      }
    });
    this.generateRouteBlockUrls(uiProfile);
  }

  generateRouteBlockUrls(uiProfile) {
    this.blockedUrls = [];
    for (const menuKey of Object.keys(uiProfile.menus)) {
      if (uiProfile.menus[menuKey].length === 0) {
        this.searchAndPushBlockedUrls(menuKey);
      } else {
        for (const menu of uiProfile.menus[menuKey]) {
          this.searchAndPushBlockedUrls(menu);
        }
      }
    }
  }

  hasPowerAddon() {
    return (
      localStorage.getItem('showLicensePage') !== '1' && _.includes(this.globalService.siteInfo?.now.addons, 'grid')
    );
  }

  getMenuData(): any {
    return {
      notSelectedMenu: [
        {
          title: 'NETWORK_MENU.topology.title',
          icon: 'device_hub',
          // The menuName do not use special characters (space, hypen or basline etc.)
          menuName: 'Topology',
          menuData: [
            {
              title: 'NETWORK_MENU.topology.menu.scan_range',
              icon: 'search',
              click: 'openScanRange',
              key: 'scanRange',
            },
            {
              title: 'NETWORK_MENU.topology.menu.auto_topology',
              icon: 'brightness_auto',
              click: 'openAutoTopologyDialog',
              key: 'autoTopology',
            },
            {
              title: 'NETWORK_MENU.topology.menu.auto_layout',
              icon: 'format_align_left',
              click: 'openAutoLayout',
              key: 'autoLayout',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.group.title',
          icon: 'group_work',
          menuName: 'Group',
          menuData: [
            {
              title: 'NETWORK_MENU.group.menu.create_group',
              icon: 'add',
              click: 'openCreateGroup',
              key: 'createGroup',
            },
            {
              title: 'NETWORK_MENU.group.menu.group_maintenance',
              icon: 'build',
              click: 'openMaintainGroup',
              key: 'groupMaintenance',
            },
            {
              title: 'NETWORK_MENU.group.menu.change_group',
              icon: 'edit',
              click: 'openChangeGroup',
              key: 'changeGroup',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.edit.title',
          icon: 'edit',
          menuName: 'Edit',
          menuData: [
            { title: 'NETWORK_MENU.edit.menu.add_device', icon: 'add', click: 'openAddDeviceDialog', key: 'addDevice' },
            { title: 'NETWORK_MENU.add_link', icon: 'link', click: 'openAddLink', key: 'addLink' },
            {
              title: 'NETWORK_MENU.edit.menu.export_topology',
              icon: 'image',
              click: 'exportTopology',
              key: 'exportTopology',
            },
            {
              title: 'PREFERENCES.topology_appearance.background',
              icon: 'wallpaper',
              click: 'openSetBackground',
              key: 'setBackground',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.visualization.title',
          icon: 'remove_red_eye',
          menuName: 'Visualization',
          menuData: this.visualizationMenuData,
        },
        {
          title: 'NETWORK_MENU.sfp',
          svgIcon: 'sfp',
          menuName: 'SFP',
          menuData: [
            { title: 'NETWORK_MENU.sfp_list', icon: 'list', click: 'openSfpListDialog', key: 'sfpList' },
            { title: 'NETWORK_MENU.sfp_sync', icon: 'sync', click: 'openSfpSyncDialog', key: 'sfpSync' },
          ],
        },
      ],
      notSelectedTableMenu: [
        {
          title: 'NETWORK_MENU.edit.title',
          icon: 'edit',
          menuName: 'Edit',
          menuData: [
            { title: 'NETWORK_MENU.edit.menu.add_device', icon: 'add', click: 'openAddDeviceDialog', key: 'addDevice' },
          ],
        },
      ],
      deviceTableSelectedMenu: [
        {
          title: 'NETWORK_MENU.device_configuration',
          icon: 'build',
          menuName: 'deviceConfiguration',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.generate_qr_code',
              svgIcon: 'qr_code',
              click: 'generateQrCode',
              key: 'generateQrCode',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.assign_model',
              icon: 'touch_app',
              click: 'openAssignModelDialog',
              key: 'assignModel',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.basic_information',
              icon: 'info',
              click: 'openDeviceBasicInfoDialog',
              key: 'basicInformation',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.ip_configuration',
              icon: 'settings',
              click: 'openDeviceIPConfig',
              key: 'ipConfiguration',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.trap_server',
              icon: 'computer',
              click: 'openDeviceTrapConfig',
              key: 'trapServer',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.port_settings',
              icon: 'crop_square',
              click: 'openPortSettingDialog',
              key: 'portSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
              icon: 'settings',
              click: 'openSNMPSetting',
              key: 'snmpSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.polling_settings',
              icon: 'settings',
              click: 'openPollingSetting',
              key: 'pollingSettings',
            },
            {
              title: 'NETWORK_MENU.device_login_account',
              icon: 'settings',
              click: 'openDeviceLoginAccount',
              key: 'deviceLoginAccount',
            },
            {
              title: 'NETWORK_MENU.modify_device_alias',
              icon: 'settings',
              click: 'openModifyDeviceAlias',
              key: 'modifyDeviceAlias',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.device_control',
          icon: 'device_hub',
          menuName: 'deviceControl',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.upgrade_firmware',
              icon: 'file_upload',
              click: 'openUpgradeFirmwareDialog',
              key: 'upgradeFirmware',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.import_config',
              icon: 'cloud_download',
              click: 'openImportConfig',
              key: 'importConfig',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.export_config',
              icon: 'backup',
              click: 'openExportConfig',
              key: 'exportConfig',
            },
          ],
        },
        { title: 'NETWORK_MENU.tools.menu.web_console', icon: 'web', click: 'openWebConsole', key: 'webConsole' },
        {
          title: 'NETWORK_MENU.execute_cli.title',
          icon: 'integration_instructions',
          menuName: 'executeCli',
          menuData: [
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
              icon: 'terminal',
              click: 'openCliInterface',
              key: 'cliInterface',
            },
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
              icon: 'library_books',
              click: 'openExecuteCliObject',
              key: 'executeCliObject',
            },
          ],
        },
        { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
        {
          title: 'NETWORK_MENU.refresh',
          icon: 'refresh',
          click: 'refreshDevice',
          key: 'refreshDevice',
        },
        this.deleteDeviceMenuData,
      ],
      deviceSerialPortTableSelectedMenu: [
        {
          title: 'NETWORK_MENU.device_configuration',
          icon: 'build',
          menuName: 'deviceConfiguration',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.serial_port_monitoring',
              icon: 'settings',
              click: 'openSerialPortMonitoring',
              key: 'serialPortMonitor',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.generate_qr_code',
              svgIcon: 'qr_code',
              click: 'generateQrCode',
              key: 'generateQrCode',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.assign_model',
              icon: 'touch_app',
              click: 'openAssignModelDialog',
              key: 'assignModel',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.basic_information',
              icon: 'info',
              click: 'openDeviceBasicInfoDialog',
              key: 'basicInformation',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.ip_configuration',
              icon: 'settings',
              click: 'openDeviceIPConfig',
              key: 'ipConfiguration',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.trap_server',
              icon: 'computer',
              click: 'openDeviceTrapConfig',
              key: 'trapServer',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.port_settings',
              icon: 'crop_square',
              click: 'openPortSettingDialog',
              key: 'portSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
              icon: 'settings',
              click: 'openSNMPSetting',
              key: 'snmpSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.polling_settings',
              icon: 'settings',
              click: 'openPollingSetting',
              key: 'pollingSettings',
            },
            {
              title: 'NETWORK_MENU.device_login_account',
              icon: 'settings',
              click: 'openDeviceLoginAccount',
              key: 'deviceLoginAccount',
            },
            {
              title: 'NETWORK_MENU.modify_device_alias',
              icon: 'settings',
              click: 'openModifyDeviceAlias',
              key: 'modifyDeviceAlias',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.device_control',
          icon: 'device_hub',
          menuName: 'deviceControl',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.upgrade_firmware',
              icon: 'file_upload',
              click: 'openUpgradeFirmwareDialog',
              key: 'upgradeFirmware',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.import_config',
              icon: 'cloud_download',
              click: 'openImportConfig',
              key: 'importConfig',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.export_config',
              icon: 'backup',
              click: 'openExportConfig',
              key: 'exportConfig',
            },
          ],
        },
        { title: 'NETWORK_MENU.tools.menu.web_console', icon: 'web', click: 'openWebConsole', key: 'webConsole' },
        {
          title: 'NETWORK_MENU.execute_cli.title',
          icon: 'integration_instructions',
          menuName: 'executeCli',
          menuData: [
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
              icon: 'terminal',
              click: 'openCliInterface',
              key: 'cliInterface',
            },
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
              icon: 'library_books',
              click: 'openExecuteCliObject',
              key: 'executeCliObject',
            },
          ],
        },
        { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
        {
          title: 'NETWORK_MENU.refresh',
          icon: 'refresh',
          click: 'refreshDevice',
          key: 'refreshDevice',
        },
        this.deleteDeviceMenuData,
      ],
      deviceSelectedMenu: [
        {
          title: 'NETWORK_MENU.device_configuration',
          icon: 'build',
          menuName: 'deviceConfiguration',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.generate_qr_code',
              svgIcon: 'qr_code',
              click: 'generateQrCode',
              key: 'generateQrCode',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.assign_model',
              icon: 'touch_app',
              click: 'openAssignModelDialog',
              key: 'assignModel',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.basic_information',
              icon: 'info',
              click: 'openDeviceBasicInfoDialog',
              key: 'basicInformation',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.ip_configuration',
              icon: 'settings',
              click: 'openDeviceIPConfig',
              key: 'ipConfiguration',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.trap_server',
              icon: 'computer',
              click: 'openDeviceTrapConfig',
              key: 'trapServer',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.port_settings',
              icon: 'crop_square',
              click: 'openPortSettingDialog',
              key: 'portSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
              icon: 'settings',
              click: 'openSNMPSetting',
              key: 'snmpSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.polling_settings',
              icon: 'settings',
              click: 'openPollingSetting',
              key: 'pollingSettings',
            },
            {
              title: 'NETWORK_MENU.device_login_account',
              icon: 'settings',
              click: 'openDeviceLoginAccount',
              key: 'deviceLoginAccount',
            },
            {
              title: 'NETWORK_MENU.modify_device_alias',
              icon: 'settings',
              click: 'openModifyDeviceAlias',
              key: 'modifyDeviceAlias',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.change_device_icon',
              icon: 'settings',
              click: 'openChangeDeviceIcon',
              key: 'changeDeviceIcon',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.device_control',
          icon: 'device_hub',
          menuName: 'deviceControl',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.upgrade_firmware',
              icon: 'file_upload',
              click: 'openUpgradeFirmwareDialog',
              key: 'upgradeFirmware',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.import_config',
              icon: 'cloud_download',
              click: 'openImportConfig',
              key: 'importConfig',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.export_config',
              icon: 'backup',
              click: 'openExportConfig',
              key: 'exportConfig',
            },
          ],
        },
        { title: 'NETWORK_MENU.tools.menu.web_console', icon: 'web', click: 'openWebConsole', key: 'webConsole' },
        {
          title: 'NETWORK_MENU.execute_cli.title',
          icon: 'integration_instructions',
          menuName: 'executeCli',
          menuData: [
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
              icon: 'terminal',
              click: 'openCliInterface',
              key: 'cliInterface',
            },
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
              icon: 'library_books',
              click: 'openExecuteCliObject',
              key: 'executeCliObject',
            },
          ],
        },
        { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
        {
          title: 'NETWORK_MENU.document.title',
          icon: 'insert_drive_file',
          click: 'openSetDocument',
          key: 'setDocument',
        },
        {
          title: 'NETWORK_MENU.refresh',
          icon: 'refresh',
          click: 'refreshDevice',
          key: 'refreshDevice',
        },
        {
          title: 'NETWORK_MENU.add_link',
          icon: 'link',
          click: 'openAddLink',
          key: 'addLink',
        },
        this.deleteDeviceMenuData,
      ],
      deviceSerialPortSelectedMenu: [
        {
          title: 'NETWORK_MENU.device_configuration',
          icon: 'build',
          menuName: 'deviceConfiguration',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.serial_port_monitoring',
              icon: 'settings',
              click: 'openSerialPortMonitoring',
              key: 'serialPortMonitor',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.generate_qr_code',
              svgIcon: 'qr_code',
              click: 'generateQrCode',
              key: 'generateQrCode',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.assign_model',
              icon: 'touch_app',
              click: 'openAssignModelDialog',
              key: 'assignModel',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.basic_information',
              icon: 'info',
              click: 'openDeviceBasicInfoDialog',
              key: 'basicInformation',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.ip_configuration',
              icon: 'settings',
              click: 'openDeviceIPConfig',
              key: 'ipConfiguration',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.trap_server',
              icon: 'computer',
              click: 'openDeviceTrapConfig',
              key: 'trapServer',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.port_settings',
              icon: 'crop_square',
              click: 'openPortSettingDialog',
              key: 'portSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
              icon: 'settings',
              click: 'openSNMPSetting',
              key: 'snmpSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.polling_settings',
              icon: 'settings',
              click: 'openPollingSetting',
              key: 'pollingSettings',
            },
            {
              title: 'NETWORK_MENU.device_login_account',
              icon: 'settings',
              click: 'openDeviceLoginAccount',
              key: 'deviceLoginAccount',
            },
            {
              title: 'NETWORK_MENU.modify_device_alias',
              icon: 'settings',
              click: 'openModifyDeviceAlias',
              key: 'modifyDeviceAlias',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.change_device_icon',
              icon: 'settings',
              click: 'openChangeDeviceIcon',
              key: 'changeDeviceIcon',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.device_control',
          icon: 'device_hub',
          menuName: 'deviceControl',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.upgrade_firmware',
              icon: 'file_upload',
              click: 'openUpgradeFirmwareDialog',
              key: 'upgradeFirmware',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.import_config',
              icon: 'cloud_download',
              click: 'openImportConfig',
              key: 'importConfig',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.export_config',
              icon: 'backup',
              click: 'openExportConfig',
              key: 'exportConfig',
            },
          ],
        },
        { title: 'NETWORK_MENU.tools.menu.web_console', icon: 'web', click: 'openWebConsole', key: 'webConsole' },
        {
          title: 'NETWORK_MENU.execute_cli.title',
          icon: 'integration_instructions',
          menuName: 'executeCli',
          menuData: [
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
              icon: 'terminal',
              click: 'openCliInterface',
              key: 'cliInterface',
            },
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
              icon: 'library_books',
              click: 'openExecuteCliObject',
              key: 'executeCliObject',
            },
          ],
        },
        { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
        {
          title: 'NETWORK_MENU.document.title',
          icon: 'insert_drive_file',
          click: 'openSetDocument',
          key: 'setDocument',
        },
        {
          title: 'NETWORK_MENU.refresh',
          icon: 'refresh',
          click: 'refreshDevice',
          key: 'refreshDevice',
        },
        {
          title: 'NETWORK_MENU.add_link',
          icon: 'link',
          click: 'openAddLink',
          key: 'addLink',
        },
        this.deleteDeviceMenuData,
      ],
      newWirelessDeviceSelectedMenu: [
        {
          title: 'NETWORK_MENU.device_configuration',
          icon: 'build',
          menuName: 'deviceConfiguration',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.generate_qr_code',
              svgIcon: 'qr_code',
              click: 'generateQrCode',
              key: 'generateQrCode',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.assign_model',
              icon: 'touch_app',
              click: 'openAssignModelDialog',
              key: 'assignModel',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.basic_information',
              icon: 'info',
              click: 'openDeviceBasicInfoDialog',
              key: 'basicInformation',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.ip_configuration',
              icon: 'settings',
              click: 'openDeviceIPConfig',
              key: 'ipConfiguration',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.trap_server',
              icon: 'computer',
              click: 'openDeviceTrapConfig',
              key: 'trapServer',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.port_settings',
              icon: 'crop_square',
              click: 'openPortSettingDialog',
              key: 'portSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
              icon: 'settings',
              click: 'openSNMPSetting',
              key: 'snmpSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.polling_settings',
              icon: 'settings',
              click: 'openPollingSetting',
              key: 'pollingSettings',
            },
            {
              title: 'NETWORK_MENU.device_login_account',
              icon: 'settings',
              click: 'openDeviceLoginAccount',
              key: 'deviceLoginAccount',
            },
            {
              title: 'NETWORK_MENU.modify_device_alias',
              icon: 'settings',
              click: 'openModifyDeviceAlias',
              key: 'modifyDeviceAlias',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.change_device_icon',
              icon: 'settings',
              click: 'openChangeDeviceIcon',
              key: 'changeDeviceIcon',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.device_control',
          icon: 'device_hub',
          menuName: 'deviceControl',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.upgrade_firmware',
              icon: 'file_upload',
              click: 'openUpgradeFirmwareDialog',
              key: 'upgradeFirmware',
            },
          ],
        },
        { title: 'NETWORK_MENU.tools.menu.web_console', icon: 'web', click: 'openWebConsole', key: 'webConsole' },
        {
          title: 'NETWORK_MENU.execute_cli.title',
          icon: 'integration_instructions',
          menuName: 'executeCli',
          menuData: [
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
              icon: 'terminal',
              click: 'openCliInterface',
              key: 'cliInterface',
            },
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
              icon: 'library_books',
              click: 'openExecuteCliObject',
              key: 'executeCliObject',
            },
          ],
        },
        { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
        {
          title: 'NETWORK_MENU.document.title',
          icon: 'insert_drive_file',
          click: 'openSetDocument',
          key: 'setDocument',
        },
        {
          title: 'NETWORK_MENU.refresh',
          icon: 'refresh',
          click: 'refreshDevice',
          key: 'refreshDevice',
        },
        {
          title: 'NETWORK_MENU.add_link',
          icon: 'link',
          click: 'openAddLink',
          key: 'addLink',
        },
        this.deleteDeviceMenuData,
      ],
      oldWirelessDeviceSelectedMenu: [
        {
          title: 'NETWORK_MENU.device_configuration',
          icon: 'build',
          menuName: 'deviceConfiguration',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.generate_qr_code',
              svgIcon: 'qr_code',
              click: 'generateQrCode',
              key: 'generateQrCode',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.assign_model',
              icon: 'touch_app',
              click: 'openAssignModelDialog',
              key: 'assignModel',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.basic_information',
              icon: 'info',
              click: 'openDeviceBasicInfoDialog',
              key: 'basicInformation',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.ip_configuration',
              icon: 'settings',
              click: 'openDeviceIPConfig',
              key: 'ipConfiguration',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.trap_server',
              icon: 'computer',
              click: 'openDeviceTrapConfig',
              key: 'trapServer',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.port_settings',
              icon: 'crop_square',
              click: 'openPortSettingDialog',
              key: 'portSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
              icon: 'settings',
              click: 'openSNMPSetting',
              key: 'snmpSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.polling_settings',
              icon: 'settings',
              click: 'openPollingSetting',
              key: 'pollingSettings',
            },
            {
              title: 'NETWORK_MENU.device_login_account',
              icon: 'settings',
              click: 'openDeviceLoginAccount',
              key: 'deviceLoginAccount',
            },
            {
              title: 'NETWORK_MENU.modify_device_alias',
              icon: 'settings',
              click: 'openModifyDeviceAlias',
              key: 'modifyDeviceAlias',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.change_device_icon',
              icon: 'settings',
              click: 'openChangeDeviceIcon',
              key: 'changeDeviceIcon',
            },
          ],
        },
        {
          id: 'cybersecurityControl',
          title: 'NETWORK_MENU.cybersecurity_control',
          icon: 'security',
          menuName: 'cybersecurityControl',
          menuData: [
            {
              title: 'NETWORK_MENU.disable_unsecured_http_and_telnet_console',
              icon: 'web_asset_off',
              click: 'openDisableUnsecured',
              key: 'disableUnsecured',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.tools.menu.web_console',
          icon: 'web',
          click: 'openWebConsole',
          key: 'webConsole',
        },
        {
          title: 'NETWORK_MENU.execute_cli.title',
          icon: 'integration_instructions',
          menuName: 'executeCli',
          menuData: [
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
              icon: 'terminal',
              click: 'openCliInterface',
              key: 'cliInterface',
            },
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
              icon: 'library_books',
              click: 'openExecuteCliObject',
              key: 'executeCliObject',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.group.menu.change_group',
          icon: 'edit',
          click: 'openChangeGroup',
          key: 'changeGroup',
        },
        {
          title: 'NETWORK_MENU.document.title',
          icon: 'insert_drive_file',
          click: 'openSetDocument',
          key: 'setDocument',
        },
        {
          title: 'NETWORK_MENU.refresh',
          icon: 'refresh',
          click: 'refreshDevice',
          key: 'refreshDevice',
        },
        {
          title: 'NETWORK_MENU.add_link',
          icon: 'link',
          click: 'openAddLink',
          key: 'addLink',
        },
        this.deleteDeviceMenuData,
      ],
      twoDevicesSelectedMenu: [
        ...this.twoDevicesSelectedMenuData
      ],
      twoIpsDevicesSelectedMenu: [
        ...this.twoDevicesSelectedMenuData
      ],
      twoDevicesNoNsmSelectedMenu: [
        {
          title: 'NETWORK_MENU.device_configuration',
          icon: 'build',
          menuName: 'deviceConfiguration',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.generate_qr_code',
              svgIcon: 'qr_code',
              click: 'generateQrCode',
              key: 'generateQrCode',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
              icon: 'settings',
              click: 'openSNMPSetting',
              key: 'snmpSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.change_device_icon',
              icon: 'settings',
              click: 'openChangeDeviceIcon',
              key: 'changeDeviceIcon',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.alignment.title',
          icon: 'format_align_left',
          menuName: 'Alignment',
          menuData: [
            {
              title: 'NETWORK_MENU.alignment.top',
              icon: 'vertical_align_top',
              click: 'alignmentTop',
              key: 'alignmentTop',
            },
            {
              title: 'NETWORK_MENU.alignment.bottom',
              icon: 'vertical_align_bottom',
              click: 'alignmentBottom',
              key: 'alignmentBottom',
            },
            {
              title: 'NETWORK_MENU.alignment.left',
              icon: 'format_align_left',
              click: 'alignmentLeft',
              key: 'alignmentLeft',
            },
            {
              title: 'NETWORK_MENU.alignment.right',
              icon: 'format_align_right',
              click: 'alignmentRight',
              key: 'alignmentRight',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.execute_cli.title',
          icon: 'integration_instructions',
          menuName: 'executeCli',
          menuData: [
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
              icon: 'terminal',
              click: 'openCliInterface',
              key: 'cliInterface',
            },
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
              icon: 'library_books',
              click: 'openExecuteCliObject',
              key: 'executeCliObject',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.add_link',
          icon: 'link',
          click: 'openAddLink',
          key: 'addLink',
        },
        { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
        this.deleteDeviceMenuData,
      ],
      twoDevicesSerialPortSelectedMenu: [
        {
          title: 'NETWORK_MENU.device_configuration',
          icon: 'build',
          menuName: 'deviceConfiguration',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.generate_qr_code',
              svgIcon: 'qr_code',
              click: 'generateQrCode',
              key: 'generateQrCode',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
              icon: 'settings',
              click: 'openSNMPSetting',
              key: 'snmpSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.serial_port_monitoring',
              icon: 'settings',
              click: 'openSerialPortMonitoring',
              key: 'serialPortMonitor',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.change_device_icon',
              icon: 'settings',
              click: 'openChangeDeviceIcon',
              key: 'changeDeviceIcon',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.alignment.title',
          icon: 'format_align_left',
          menuName: 'Alignment',
          menuData: [
            {
              title: 'NETWORK_MENU.alignment.top',
              icon: 'vertical_align_top',
              click: 'alignmentTop',
              key: 'alignmentTop',
            },
            {
              title: 'NETWORK_MENU.alignment.bottom',
              icon: 'vertical_align_bottom',
              click: 'alignmentBottom',
              key: 'alignmentBottom',
            },
            {
              title: 'NETWORK_MENU.alignment.left',
              icon: 'format_align_left',
              click: 'alignmentLeft',
              key: 'alignmentLeft',
            },
            {
              title: 'NETWORK_MENU.alignment.right',
              icon: 'format_align_right',
              click: 'alignmentRight',
              key: 'alignmentRight',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.execute_cli.title',
          icon: 'integration_instructions',
          menuName: 'executeCli',
          menuData: [
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
              icon: 'terminal',
              click: 'openCliInterface',
              key: 'cliInterface',
            },
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
              icon: 'library_books',
              click: 'openExecuteCliObject',
              key: 'executeCliObject',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.add_link',
          icon: 'link',
          click: 'openAddLink',
          key: 'addLink',
        },
        { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
        this.deleteDeviceMenuData,
      ],
      deviceTableMultiSelectedMenu: [
        ...this.deviceTableMultiSelectedMenuData
      ],
      ipsDeviceTableMultiSelectedMenu: [
        ...this.deviceTableMultiSelectedMenuData
      ],
      deviceNoNsmTableMultiSelectedMenu: [
        {
          title: 'NETWORK_MENU.device_configuration',
          icon: 'build',
          menuName: 'deviceConfiguration',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.generate_qr_code',
              svgIcon: 'qr_code',
              click: 'generateQrCode',
              key: 'generateQrCode',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
              icon: 'settings',
              click: 'openSNMPSetting',
              key: 'snmpSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.change_device_icon',
              icon: 'settings',
              click: 'openChangeDeviceIcon',
              key: 'changeDeviceIcon',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.execute_cli.title',
          icon: 'integration_instructions',
          menuName: 'executeCli',
          menuData: [
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
              icon: 'terminal',
              click: 'openCliInterface',
              key: 'cliInterface',
            },
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
              icon: 'library_books',
              click: 'openExecuteCliObject',
              key: 'executeCliObject',
            },
          ],
        },
        { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
        this.deleteDeviceMenuData,
      ],
      deviceSerialPortTableMultiSelectedMenu: [
        {
          title: 'NETWORK_MENU.device_configuration',
          icon: 'build',
          menuName: 'deviceConfiguration',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.serial_port_monitoring',
              icon: 'settings',
              click: 'openSerialPortMonitoring',
              key: 'serialPortMonitor',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.generate_qr_code',
              svgIcon: 'qr_code',
              click: 'generateQrCode',
              key: 'generateQrCode',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
              icon: 'settings',
              click: 'openSNMPSetting',
              key: 'snmpSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.change_device_icon',
              icon: 'settings',
              click: 'openChangeDeviceIcon',
              key: 'changeDeviceIcon',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.execute_cli.title',
          icon: 'integration_instructions',
          menuName: 'executeCli',
          menuData: [
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
              icon: 'terminal',
              click: 'openCliInterface',
              key: 'cliInterface',
            },
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
              icon: 'library_books',
              click: 'openExecuteCliObject',
              key: 'executeCliObject',
            },
          ],
        },
        { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
        this.deleteDeviceMenuData,
      ],
      deviceMultiSelectedMenu: [
        ...this.deviceMultiSelectedMenuData
      ],
      ipsDeviceMultiSelectedMenu: [
        ...this.deviceMultiSelectedMenuData
      ],
      deviceNoNsmMultiSelectedMenu: [
        {
          title: 'NETWORK_MENU.device_configuration',
          icon: 'build',
          menuName: 'deviceConfiguration',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.generate_qr_code',
              svgIcon: 'qr_code',
              click: 'generateQrCode',
              key: 'generateQrCode',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
              icon: 'settings',
              click: 'openSNMPSetting',
              key: 'snmpSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.change_device_icon',
              icon: 'settings',
              click: 'openChangeDeviceIcon',
              key: 'changeDeviceIcon',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.alignment.title',
          icon: 'format_align_left',
          menuName: 'Alignment',
          menuData: [
            {
              title: 'NETWORK_MENU.alignment.top',
              icon: 'vertical_align_top',
              click: 'alignmentTop',
              key: 'alignmentTop',
            },
            {
              title: 'NETWORK_MENU.alignment.bottom',
              icon: 'vertical_align_bottom',
              click: 'alignmentBottom',
              key: 'alignmentBottom',
            },
            {
              title: 'NETWORK_MENU.alignment.left',
              icon: 'format_align_left',
              click: 'alignmentLeft',
              key: 'alignmentLeft',
            },
            {
              title: 'NETWORK_MENU.alignment.right',
              icon: 'format_align_right',
              click: 'alignmentRight',
              key: 'alignmentRight',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.execute_cli.title',
          icon: 'integration_instructions',
          menuName: 'executeCli',
          menuData: [
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
              icon: 'terminal',
              click: 'openCliInterface',
              key: 'cliInterface',
            },
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
              icon: 'library_books',
              click: 'openExecuteCliObject',
              key: 'executeCliObject',
            },
          ],
        },
        { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
        this.deleteDeviceMenuData,
      ],
      deviceSerialPortMultiSelectedMenu: [
        {
          title: 'NETWORK_MENU.device_configuration',
          icon: 'build',
          menuName: 'deviceConfiguration',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.generate_qr_code',
              svgIcon: 'qr_code',
              click: 'generateQrCode',
              key: 'generateQrCode',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
              icon: 'settings',
              click: 'openSNMPSetting',
              key: 'snmpSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.serial_port_monitoring',
              icon: 'settings',
              click: 'openSerialPortMonitoring',
              key: 'serialPortMonitor',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.change_device_icon',
              icon: 'settings',
              click: 'openChangeDeviceIcon',
              key: 'changeDeviceIcon',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.alignment.title',
          icon: 'format_align_left',
          menuName: 'Alignment',
          menuData: [
            {
              title: 'NETWORK_MENU.alignment.top',
              icon: 'vertical_align_top',
              click: 'alignmentTop',
              key: 'alignmentTop',
            },
            {
              title: 'NETWORK_MENU.alignment.bottom',
              icon: 'vertical_align_bottom',
              click: 'alignmentBottom',
              key: 'alignmentBottom',
            },
            {
              title: 'NETWORK_MENU.alignment.left',
              icon: 'format_align_left',
              click: 'alignmentLeft',
              key: 'alignmentLeft',
            },
            {
              title: 'NETWORK_MENU.alignment.right',
              icon: 'format_align_right',
              click: 'alignmentRight',
              key: 'alignmentRight',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.execute_cli.title',
          icon: 'integration_instructions',
          menuName: 'executeCli',
          menuData: [
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
              icon: 'terminal',
              click: 'openCliInterface',
              key: 'cliInterface',
            },
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
              icon: 'library_books',
              click: 'openExecuteCliObject',
              key: 'executeCliObject',
            },
          ],
        },
        { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
        this.deleteDeviceMenuData,
      ],
      icmpSelectedMenu: [
        {
          title: 'NETWORK_MENU.device_configuration',
          icon: 'build',
          menuName: 'deviceConfiguration',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.generate_qr_code',
              svgIcon: 'qr_code',
              click: 'generateQrCode',
              key: 'generateQrCode',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.assign_model',
              icon: 'touch_app',
              click: 'openAssignModelDialog',
              key: 'assignModel',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
              icon: 'settings',
              click: 'openSNMPSetting',
              key: 'snmpSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.polling_settings',
              icon: 'settings',
              click: 'openPollingSetting',
              key: 'pollingSettings',
            },
            {
              title: 'NETWORK_MENU.device_login_account',
              icon: 'settings',
              click: 'openDeviceLoginAccount',
              key: 'deviceLoginAccount',
            },
            {
              title: 'NETWORK_MENU.modify_device_alias',
              icon: 'settings',
              click: 'openModifyDeviceAlias',
              key: 'modifyDeviceAlias',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.change_device_icon',
              icon: 'settings',
              click: 'openChangeDeviceIcon',
              key: 'changeDeviceIcon',
            },
          ],
        },
        { title: 'NETWORK_MENU.tools.menu.web_console', icon: 'web', click: 'openWebConsole', key: 'webConsole' },
        {
          title: 'NETWORK_MENU.execute_cli.title',
          icon: 'integration_instructions',
          menuName: 'executeCli',
          menuData: [
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
              icon: 'terminal',
              click: 'openCliInterface',
              key: 'cliInterface',
            },
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
              icon: 'library_books',
              click: 'openExecuteCliObject',
              key: 'executeCliObject',
            },
          ],
        },

        { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
        {
          title: 'NETWORK_MENU.document.title',
          icon: 'insert_drive_file',
          click: 'openSetDocument',
          key: 'setDocument',
        },
        {
          title: 'NETWORK_MENU.refresh',
          icon: 'refresh',
          click: 'refreshDevice',
          key: 'refreshDevice',
        },
        {
          title: 'NETWORK_MENU.add_link',
          icon: 'link',
          click: 'openAddLink',
          key: 'addLink',
        },
        this.deleteDeviceMenuData,
      ],
      linkSelectedMenu: [
        {
          title: 'NETWORK_MENU.link_traffic.title',
          svgIcon: 'traffic',
          menuName: 'LinkTraffic',
          menuData: [
            {
              title: 'NETWORK_MENU.link_traffic.menu.port_traffic',
              icon: 'show_chart',
              click: 'openPortTraffic',
              key: 'portTraffic',
            },
            {
              title: 'NETWORK_MENU.link_traffic.menu.packet_error_rate',
              icon: 'assignment_late',
              click: 'openPacketErrorRate',
              key: 'packetErrorRate',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.severity_threshold',
          icon: 'multiline_chart',
          click: 'openSeverityThreshold',
          key: 'severityThreshold',
        },
        {
          title: 'NETWORK_MENU.set_port_label',
          icon: 'label',
          click: 'openSetPortLabel',
          key: 'setPortLabel',
        },
        {
          title: 'NETWORK_MENU.delete',
          icon: 'delete_forever',
          click: 'openDeleteLinkDialog',
          key: 'deleteLink',
        },
      ],
      sfpLinkSelectedMenu: [
        {
          title: 'NETWORK_MENU.link_traffic.title',
          svgIcon: 'traffic',
          menuName: 'LinkTraffic',
          menuData: [
            {
              title: 'NETWORK_MENU.link_traffic.menu.port_traffic',
              icon: 'show_chart',
              click: 'openPortTraffic',
              key: 'portTraffic',
            },
            {
              title: 'NETWORK_MENU.link_traffic.menu.packet_error_rate',
              icon: 'assignment_late',
              click: 'openPacketErrorRate',
              key: 'packetErrorRate',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.severity_threshold',
          icon: 'multiline_chart',
          click: 'openSeverityThreshold',
          key: 'severityThreshold',
        },
        {
          title: 'NETWORK_MENU.set_port_label',
          icon: 'label',
          click: 'openSetPortLabel',
          key: 'setPortLabel',
        },
        {
          title: 'NETWORK_MENU.sfp_Info',
          svgIcon: 'sfp',
          click: 'openSfpInfo',
          key: 'sfpInfo',
        },
        {
          title: 'NETWORK_MENU.delete',
          icon: 'delete_forever',
          click: 'openDeleteLinkDialog',
          key: 'deleteLink',
        },
      ],
      da820CLinkSelectedMenu: [
        {
          title: 'NETWORK_MENU.set_port_label',
          icon: 'label',
          click: 'openSetPortLabel',
          key: 'setPortLabel',
        },
        {
          title: 'NETWORK_MENU.delete',
          icon: 'delete_forever',
          click: 'openDeleteLinkDialog',
          key: 'deleteLink',
        },
      ],
      da820CSfpLinkSelectedMenu: [
        {
          title: 'NETWORK_MENU.set_port_label',
          icon: 'label',
          click: 'openSetPortLabel',
          key: 'setPortLabel',
        },
        {
          title: 'NETWORK_MENU.sfp_Info',
          svgIcon: 'sfp',
          click: 'openSfpInfo',
          key: 'sfpInfo',
        },
        {
          title: 'NETWORK_MENU.delete',
          icon: 'delete_forever',
          click: 'openDeleteLinkDialog',
          key: 'deleteLink',
        },
      ],
      linkMultiSelectedMenu: [
        {
          title: 'NETWORK_MENU.delete',
          icon: 'delete_forever',
          click: 'openDeleteLinkDialog',
          key: 'deleteLink',
        },
      ],
      siteNotSelectedMenu: [],
      siteSelectedMenu: [
        {
          title: 'SITE_MENU.management',
          icon: 'edit',
          click: 'openSiteManagement',
          key: 'siteManagement',
        },
      ],
      siteSelectedDeleteMenu: [
        {
          title: 'NETWORK_MENU.delete',
          icon: 'delete_forever',
          click: 'openDeleteSiteDialog',
          key: 'deleteSite',
        },
      ],
      siteMultiSelectedMenu: [
        {
          title: 'NETWORK_MENU.delete',
          icon: 'delete_forever',
          click: 'openDeleteSiteDialog',
          key: 'deleteSite',
        },
      ],
      groupSelectedMenu: [
        {
          title: 'NETWORK_MENU.delete',
          icon: 'delete_forever',
          click: 'openDeleteGroupDialog',
          key: 'deleteGroup',
        },
      ],
      groupMultiSelectedMenu: [
        {
          title: 'NETWORK_MENU.alignment.title',
          icon: 'format_align_left',
          menuName: 'Alignment',
          menuData: [
            {
              title: 'NETWORK_MENU.alignment.top',
              icon: 'vertical_align_top',
              click: 'alignmentTop',
              key: 'alignmentTop',
            },
            {
              title: 'NETWORK_MENU.alignment.bottom',
              icon: 'vertical_align_bottom',
              click: 'alignmentBottom',
              key: 'alignmentBottom',
            },
            {
              title: 'NETWORK_MENU.alignment.left',
              icon: 'format_align_left',
              click: 'alignmentLeft',
              key: 'alignmentLeft',
            },
            {
              title: 'NETWORK_MENU.alignment.right',
              icon: 'format_align_right',
              click: 'alignmentRight',
              key: 'alignmentRight',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.delete',
          icon: 'delete_forever',
          click: 'openDeleteGroupDialog',
          key: 'deleteGroup',
        },
      ],
      multiObjectSelectedMenu: [
        {
          title: 'NETWORK_MENU.alignment.title',
          icon: 'format_align_left',
          menuName: 'Alignment',
          menuData: [
            {
              title: 'NETWORK_MENU.alignment.top',
              icon: 'vertical_align_top',
              click: 'alignmentTop',
              key: 'alignmentTop',
            },
            {
              title: 'NETWORK_MENU.alignment.bottom',
              icon: 'vertical_align_bottom',
              click: 'alignmentBottom',
              key: 'alignmentBottom',
            },
            {
              title: 'NETWORK_MENU.alignment.left',
              icon: 'format_align_left',
              click: 'alignmentLeft',
              key: 'alignmentLeft',
            },
            {
              title: 'NETWORK_MENU.alignment.right',
              icon: 'format_align_right',
              click: 'alignmentRight',
              key: 'alignmentRight',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.delete',
          icon: 'delete_forever',
          click: 'openDeleteObjectDialog',
          key: 'deleteObject',
        },
      ],
      edrSelectedMenu: [
        ...this.edrSelectedMenuData
      ],
      edrIpsSelectedMenu: [
        ...this.edrSelectedMenuData
      ],
      edrNoNsmSelectedMenu: [
        {
          title: 'NETWORK_MENU.device_configuration',
          icon: 'build',
          menuName: 'deviceConfiguration',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.generate_qr_code',
              svgIcon: 'qr_code',
              click: 'generateQrCode',
              key: 'generateQrCode',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.assign_model',
              icon: 'touch_app',
              click: 'openAssignModelDialog',
              key: 'assignModel',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.basic_information',
              icon: 'info',
              click: 'openDeviceBasicInfoDialog',
              key: 'basicInformation',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.ip_configuration',
              icon: 'settings',
              click: 'openDeviceIPConfig',
              key: 'ipConfiguration',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.trap_server',
              icon: 'computer',
              click: 'openDeviceTrapConfig',
              key: 'trapServer',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.port_settings',
              icon: 'crop_square',
              click: 'openPortSettingDialog',
              key: 'portSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
              icon: 'settings',
              click: 'openSNMPSetting',
              key: 'snmpSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.polling_settings',
              icon: 'settings',
              click: 'openPollingSetting',
              key: 'pollingSettings',
            },
            {
              title: 'NETWORK_MENU.device_login_account',
              icon: 'settings',
              click: 'openDeviceLoginAccount',
              key: 'deviceLoginAccount',
            },
            {
              title: 'NETWORK_MENU.modify_device_alias',
              icon: 'settings',
              click: 'openModifyDeviceAlias',
              key: 'modifyDeviceAlias',
            },
            // {
            //   title: 'NETWORK_MENU.maintenance.menu.polling_ip_setting',
            //   icon: 'settings',
            //   click: 'openDevicePollingIpSetting',
            //   key: 'pollingIpSetting',
            // },
            {
              title: 'NETWORK_MENU.maintenance.menu.change_device_icon',
              icon: 'settings',
              click: 'openChangeDeviceIcon',
              key: 'changeDeviceIcon',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.device_control',
          icon: 'device_hub',
          menuName: 'deviceControl',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.upgrade_firmware',
              icon: 'file_upload',
              click: 'openUpgradeFirmwareDialog',
              key: 'upgradeFirmware',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.import_config',
              icon: 'cloud_download',
              click: 'openImportConfig',
              key: 'importConfig',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.export_config',
              icon: 'backup',
              click: 'openExportConfig',
              key: 'exportConfig',
            },
          ],
        },
        { title: 'NETWORK_MENU.tools.menu.web_console', icon: 'web', click: 'openWebConsole', key: 'webConsole' },
        {
          title: 'NETWORK_MENU.execute_cli.title',
          icon: 'integration_instructions',
          menuName: 'executeCli',
          menuData: [
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
              icon: 'terminal',
              click: 'openCliInterface',
              key: 'cliInterface',
            },
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
              icon: 'library_books',
              click: 'openExecuteCliObject',
              key: 'executeCliObject',
            },
          ],
        },
        { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
        {
          title: 'NETWORK_MENU.refresh',
          icon: 'refresh',
          click: 'refreshDevice',
          key: 'refreshDevice',
        },
        {
          title: 'NETWORK_MENU.add_link',
          icon: 'link',
          click: 'openAddLink',
          key: 'addLink',
        },
        {
          title: 'NETWORK_MENU.ipsec_status',
          icon: 'lock',
          click: 'openIpSecTable',
          key: 'ipsecStatus',
        },
        this.deleteDeviceMenuData,
      ],
      edrDeviceTableSelectedMenu: [
        ...this.edrDeviceTableSeletedMenuData,
      ],
      edrIpsDeviceTableSelectedMenu: [
        ...this.edrDeviceTableSeletedMenuData,
      ],
      edrDeviceNoNsmTableSelectedMenu: [
        {
          title: 'NETWORK_MENU.device_configuration',
          icon: 'build',
          menuName: 'deviceConfiguration',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.generate_qr_code',
              svgIcon: 'qr_code',
              click: 'generateQrCode',
              key: 'generateQrCode',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.assign_model',
              icon: 'touch_app',
              click: 'openAssignModelDialog',
              key: 'assignModel',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.basic_information',
              icon: 'info',
              click: 'openDeviceBasicInfoDialog',
              key: 'basicInformation',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.ip_configuration',
              icon: 'settings',
              click: 'openDeviceIPConfig',
              key: 'ipConfiguration',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.trap_server',
              icon: 'computer',
              click: 'openDeviceTrapConfig',
              key: 'trapServer',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.port_settings',
              icon: 'crop_square',
              click: 'openPortSettingDialog',
              key: 'portSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
              icon: 'settings',
              click: 'openSNMPSetting',
              key: 'snmpSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.polling_settings',
              icon: 'settings',
              click: 'openPollingSetting',
              key: 'pollingSettings',
            },
            {
              title: 'NETWORK_MENU.device_login_account',
              icon: 'settings',
              click: 'openDeviceLoginAccount',
              key: 'deviceLoginAccount',
            },
            {
              title: 'NETWORK_MENU.modify_device_alias',
              icon: 'settings',
              click: 'openModifyDeviceAlias',
              key: 'modifyDeviceAlias',
            },
            // {
            //   title: 'NETWORK_MENU.maintenance.menu.polling_ip_setting',
            //   icon: 'settings',
            //   click: 'openDevicePollingIpSetting',
            //   key: 'pollingIpSetting',
            // },
            {
              title: 'NETWORK_MENU.maintenance.menu.change_device_icon',
              icon: 'settings',
              click: 'openChangeDeviceIcon',
              key: 'changeDeviceIcon',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.device_control',
          icon: 'device_hub',
          menuName: 'deviceControl',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.upgrade_firmware',
              icon: 'file_upload',
              click: 'openUpgradeFirmwareDialog',
              key: 'upgradeFirmware',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.import_config',
              icon: 'cloud_download',
              click: 'openImportConfig',
              key: 'importConfig',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.export_config',
              icon: 'backup',
              click: 'openExportConfig',
              key: 'exportConfig',
            },
          ],
        },
        { title: 'NETWORK_MENU.tools.menu.web_console', icon: 'web', click: 'openWebConsole', key: 'webConsole' },

        {
          title: 'NETWORK_MENU.execute_cli.title',
          icon: 'integration_instructions',
          menuName: 'executeCli',
          menuData: [
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
              icon: 'terminal',
              click: 'openCliInterface',
              key: 'cliInterface',
            },
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
              icon: 'library_books',
              click: 'openExecuteCliObject',
              key: 'executeCliObject',
            },
          ],
        },
        { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
        {
          title: 'NETWORK_MENU.refresh',
          icon: 'refresh',
          click: 'refreshDevice',
          key: 'refreshDevice',
        },
        {
          title: 'NETWORK_MENU.ipsec_status',
          icon: 'lock',
          click: 'openIpSecTable',
          key: 'ipsecStatus',
        },
        this.deleteDeviceMenuData,
      ],
      wirelessTableView: [
        {
          title: 'BUTTON.back',
          icon: 'arrow_back',
          click: 'closeWirelessTableView',
          key: 'wirelessPlaybackViewBack',
        },
      ],
      wirelessPlaybackView: [
        {
          title: 'BUTTON.back',
          icon: 'arrow_back',
          click: 'closeWirelessPlaybackView',
          key: 'wirelessPlaybackViewBack',
        },
      ],
      wirelessPlannerView: [
        {
          title: 'BUTTON.back',
          icon: 'arrow_back',
          click: 'closeWirelessPlannerView',
          key: 'wirelessPlannerViewBack',
        },
        {
          title: 'PREFERENCES.topology_appearance.background',
          icon: 'wallpaper',
          click: 'setBackgroundView',
          key: 'setBackgroundView',
        },
        {
          title: 'NETWORK_MENU.scale',
          icon: 'aspect_ratio',
          click: 'scaleView',
          key: 'wirelessPlannerScaleView',
        },
        {
          title: 'NETWORK_MENU.wireless_settings',
          icon: 'settings',
          click: 'wirelessSettingsView',
          key: 'wirelessSettingsView',
        },
      ],
      wirelessPlannerDeviceView: [
        {
          title: 'NETWORK_MENU.device_wireless_settings',
          icon: 'settings',
          click: 'deviceSettingsView',
          key: 'deviceSettingsView',
        },
      ],
      computerSelectedMenu: [
        {
          title: 'NETWORK_MENU.device_configuration',
          icon: 'build',
          menuName: 'deviceConfiguration',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
              icon: 'settings',
              click: 'openSNMPSetting',
              key: 'snmpSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.polling_settings',
              icon: 'settings',
              click: 'openPollingSetting',
              key: 'pollingSettings',
            },
            {
              title: 'NETWORK_MENU.device_login_account',
              icon: 'settings',
              click: 'openDeviceLoginAccount',
              key: 'deviceLoginAccount',
            },
            {
              title: 'NETWORK_MENU.modify_device_alias',
              icon: 'settings',
              click: 'openModifyDeviceAlias',
              key: 'modifyDeviceAlias',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.change_device_icon',
              icon: 'settings',
              click: 'openChangeDeviceIcon',
              key: 'changeDeviceIcon',
            },
          ],
        },
        { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
        {
          title: 'NETWORK_MENU.refresh',
          icon: 'refresh',
          click: 'refreshDevice',
          key: 'refreshDevice',
        },
        {
          title: 'NETWORK_MENU.add_link',
          icon: 'link',
          click: 'openAddLink',
          key: 'addLink',
        },
        {
          title: 'NETWORK_MENU.device_dashboard',
          icon: 'dashboard',
          click: 'openComputerDeviceDashboard',
          key: 'computerDeviceDashboard',
        },
        this.deleteDeviceMenuData,
      ],
      computerTableSelectedMenu: [
        {
          title: 'NETWORK_MENU.device_configuration',
          icon: 'build',
          menuName: 'deviceConfiguration',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
              icon: 'settings',
              click: 'openSNMPSetting',
              key: 'snmpSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.polling_settings',
              icon: 'settings',
              click: 'openPollingSetting',
              key: 'pollingSettings',
            },
            {
              title: 'NETWORK_MENU.device_login_account',
              icon: 'settings',
              click: 'openDeviceLoginAccount',
              key: 'deviceLoginAccount',
            },
            {
              title: 'NETWORK_MENU.modify_device_alias',
              icon: 'settings',
              click: 'openModifyDeviceAlias',
              key: 'modifyDeviceAlias',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.change_device_icon',
              icon: 'settings',
              click: 'openChangeDeviceIcon',
              key: 'changeDeviceIcon',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.execute_cli.title',
          icon: 'integration_instructions',
          menuName: 'executeCli',
          menuData: [
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
              icon: 'terminal',
              click: 'openCliInterface',
              key: 'cliInterface',
            },
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
              icon: 'library_books',
              click: 'openExecuteCliObject',
              key: 'executeCliObject',
            },
          ],
        },
        { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
        {
          title: 'NETWORK_MENU.refresh',
          icon: 'refresh',
          click: 'refreshDevice',
          key: 'refreshDevice',
        },
        {
          title: 'NETWORK_MENU.device_dashboard',
          icon: 'dashboard',
          click: 'openComputerDeviceDashboard',
          key: 'computerDeviceDashboard',
        },
        this.deleteDeviceMenuData,
      ],
      ucComputerSelectedMenu: [
        {
          title: 'NETWORK_MENU.device_configuration',
          icon: 'build',
          menuName: 'deviceConfiguration',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
              icon: 'settings',
              click: 'openSNMPSetting',
              key: 'snmpSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.polling_settings',
              icon: 'settings',
              click: 'openPollingSetting',
              key: 'pollingSettings',
            },
            {
              title: 'NETWORK_MENU.device_login_account',
              icon: 'settings',
              click: 'openDeviceLoginAccount',
              key: 'deviceLoginAccount',
            },
            {
              title: 'NETWORK_MENU.modify_device_alias',
              icon: 'settings',
              click: 'openModifyDeviceAlias',
              key: 'modifyDeviceAlias',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.change_device_icon',
              icon: 'settings',
              click: 'openChangeDeviceIcon',
              key: 'changeDeviceIcon',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.device_control',
          icon: 'device_hub',
          menuName: 'deviceControl',
          menuData: [
            {
              title: 'NETWORK_MENU.upgrade_patch',
              icon: 'file_upload',
              click: 'openUpgradeFirmwareDialog',
              key: 'upgradeFirmware',
            },
          ],
        },
        { title: 'NETWORK_MENU.tools.menu.web_console', icon: 'web', click: 'openWebConsole', key: 'webConsole' },
        {
          title: 'NETWORK_MENU.execute_cli.title',
          icon: 'integration_instructions',
          menuName: 'executeCli',
          menuData: [
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
              icon: 'terminal',
              click: 'openCliInterface',
              key: 'cliInterface',
            },
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
              icon: 'library_books',
              click: 'openExecuteCliObject',
              key: 'executeCliObject',
            },
          ],
        },
        { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
        {
          title: 'NETWORK_MENU.refresh',
          icon: 'refresh',
          click: 'refreshDevice',
          key: 'refreshDevice',
        },
        {
          title: 'NETWORK_MENU.add_link',
          icon: 'link',
          click: 'openAddLink',
          key: 'addLink',
        },
        {
          title: 'NETWORK_MENU.device_dashboard',
          icon: 'dashboard',
          click: 'openComputerDeviceDashboard',
          key: 'computerDeviceDashboard',
        },
        this.deleteDeviceMenuData,
      ],
      ucComputerTableSelectedMenu: [
        {
          title: 'NETWORK_MENU.device_configuration',
          icon: 'build',
          menuName: 'deviceConfiguration',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
              icon: 'settings',
              click: 'openSNMPSetting',
              key: 'snmpSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.polling_settings',
              icon: 'settings',
              click: 'openPollingSetting',
              key: 'pollingSettings',
            },
            {
              title: 'NETWORK_MENU.device_login_account',
              icon: 'settings',
              click: 'openDeviceLoginAccount',
              key: 'deviceLoginAccount',
            },
            {
              title: 'NETWORK_MENU.modify_device_alias',
              icon: 'settings',
              click: 'openModifyDeviceAlias',
              key: 'modifyDeviceAlias',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.change_device_icon',
              icon: 'settings',
              click: 'openChangeDeviceIcon',
              key: 'changeDeviceIcon',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.device_control',
          icon: 'device_hub',
          menuName: 'deviceControl',
          menuData: [
            {
              title: 'NETWORK_MENU.upgrade_patch',
              icon: 'file_upload',
              click: 'openUpgradeFirmwareDialog',
              key: 'upgradeFirmware',
            },
          ],
        },
        { title: 'NETWORK_MENU.tools.menu.web_console', icon: 'web', click: 'openWebConsole', key: 'webConsole' },
        {
          title: 'NETWORK_MENU.execute_cli.title',
          icon: 'integration_instructions',
          menuName: 'executeCli',
          menuData: [
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
              icon: 'terminal',
              click: 'openCliInterface',
              key: 'cliInterface',
            },
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
              icon: 'library_books',
              click: 'openExecuteCliObject',
              key: 'executeCliObject',
            },
          ],
        },
        { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
        {
          title: 'NETWORK_MENU.refresh',
          icon: 'refresh',
          click: 'refreshDevice',
          key: 'refreshDevice',
        },
        {
          title: 'NETWORK_MENU.device_dashboard',
          icon: 'dashboard',
          click: 'openComputerDeviceDashboard',
          key: 'computerDeviceDashboard',
        },
        this.deleteDeviceMenuData,
      ],
      menuForDeviceManagementInTopology: [
        {
          title: 'NETWORK_MENU.device_configuration',
          icon: 'build',
          menuName: 'deviceConfiguration',
          menuData: [
            {
              title: 'NETWORK_MENU.wifi_channel_change',
              iconSrc: 'network_manage_FILL0_wght400_GRAD0_opsz24.svg',
              click: ActionMetaType.CHANGE_WIFI_CHANNEL,
              key: 'changeWifiChannel',
            },
            {
              title: 'NETWORK_MENU.add_wifi_ssid',
              iconSrc: 'network_manage_FILL0_wght400_GRAD0_opsz24.svg',
              click: ActionMetaType.ADD_WIFI_SSID,
              key: 'addWifiSsid',
            },
            {
              title: 'NETWORK_MENU.dynamic_mac_sticky',
              iconSrc: 'settings_b_roll_FILL0_wght400_GRAD0_opsz24.svg',
              click: ActionMetaType.DYNAMIC_MAC_STICKY,
              key: 'dynamicMACSticky',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.device_control',
          icon: 'device_hub',
          menuName: 'deviceControl',
          menuData: [
            {
              title: 'NETWORK_MENU.reboot',
              icon: 'settings_power',
              click: ActionMetaType.REBOOT,
              key: 'reboot',
            },
            {
              title: 'NETWORK_MENU.create_a_snapshot',
              icon: 'screenshot_monitor',
              click: ActionMetaType.CREATE_SNAPSHOT,
              key: 'createSnapshot',
            },
            {
              title: 'NETWORK_MENU.restore_to_created_snapshot',
              icon: 'settings_backup_restore',
              click: ActionMetaType.RESTORE_TO_CREATE_SNAPSHOT,
              key: 'restoreSnapshot',
            },
          ],
        },
        {
          id: 'cybersecurityControl',
          title: 'NETWORK_MENU.cybersecurity_control',
          icon: 'security',
          menuName: 'cybersecurityControl',
          menuData: [
            {
              title: 'NETWORK_MENU.mac_sticky_on_off',
              iconSrc: 'assignment_turned_in_FILL0_wght400_GRAD0_opsz24.svg',
              click: ActionMetaType.MAC_STICKY_SWITCH,
              key: 'macSticky',
            },
            {
              title: 'NETWORK_MENU.restart_mac_sticky_learning',
              iconSrc: 'assignment_turned_in_FILL0_wght400_GRAD0_opsz24.svg',
              click: ActionMetaType.RELEARN_MAC_STICKY,
              key: 'relearnMacSticky',
            },
            {
              title: 'NETWORK_MENU.disable_unused_ethernet_and_fiber_ports',
              iconSrc: 'do_not_disturb_off_FILL0_wght400_GRAD0_opsz24.svg',
              click: ActionMetaType.DISABLE_UNUSED_PORT,
              key: 'disableUnused',
            },
            {
              title: 'NETWORK_MENU.disable_unsecured_http_and_telnet_console',
              icon: 'web_asset_off',
              click: ActionMetaType.DISABLE_UNSECURED_CONSOLE,
              key: 'disableUnsecured',
            },
          ],
        },
      ],
      menuForDeviceManagement: [
        {
          title: 'NETWORK_MENU.device_configuration',
          icon: 'build',
          menuName: 'deviceConfiguration',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.serial_port_monitoring',
              icon: 'settings',
              click: 'openSerialPortMonitoring',
              key: 'serialPortMonitor',
            },
            {
              title: 'NETWORK_MENU.wifi_channel_change',
              iconSrc: 'network_manage_FILL0_wght400_GRAD0_opsz24.svg',
              click: ActionMetaType.CHANGE_WIFI_CHANNEL,
              key: 'changeWifiChannel',
            },
            {
              title: 'NETWORK_MENU.add_wifi_ssid',
              iconSrc: 'network_manage_FILL0_wght400_GRAD0_opsz24.svg',
              click: ActionMetaType.ADD_WIFI_SSID,
              key: 'addWifiSsid',
            },
            {
              title: 'NETWORK_MENU.dynamic_mac_sticky',
              iconSrc: 'settings_b_roll_FILL0_wght400_GRAD0_opsz24.svg',
              click: ActionMetaType.DYNAMIC_MAC_STICKY,
              key: 'dynamicMACSticky',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.device_control',
          icon: 'device_hub',
          menuName: 'deviceControl',
          menuData: [
            {
              title: 'NETWORK_MENU.reboot',
              icon: 'settings_power',
              click: ActionMetaType.REBOOT,
              key: 'reboot',
            },
            {
              title: 'NETWORK_MENU.create_a_snapshot',
              icon: 'screenshot_monitor',
              click: ActionMetaType.CREATE_SNAPSHOT,
              key: 'createSnapshot',
            },
            {
              title: 'NETWORK_MENU.restore_to_created_snapshot',
              icon: 'settings_backup_restore',
              click: ActionMetaType.RESTORE_TO_CREATE_SNAPSHOT,
              key: 'restoreSnapshot',
            },
          ],
        },
        {
          id: 'cybersecurityControl',
          title: 'NETWORK_MENU.cybersecurity_control',
          icon: 'security',
          menuName: 'cybersecurityControl',
          menuData: [
            {
              title: 'NETWORK_MENU.mac_sticky_on_off',
              iconSrc: 'assignment_turned_in_FILL0_wght400_GRAD0_opsz24.svg',
              click: ActionMetaType.MAC_STICKY_SWITCH,
              key: 'macSticky',
            },
            {
              title: 'NETWORK_MENU.restart_mac_sticky_learning',
              iconSrc: 'assignment_turned_in_FILL0_wght400_GRAD0_opsz24.svg',
              click: ActionMetaType.RELEARN_MAC_STICKY,
              key: 'relearnMacSticky',
            },
            {
              title: 'NETWORK_MENU.disable_unused_ethernet_and_fiber_ports',
              iconSrc: 'do_not_disturb_off_FILL0_wght400_GRAD0_opsz24.svg',
              click: ActionMetaType.DISABLE_UNUSED_PORT,
              key: 'disableUnused',
            },
            {
              title: 'NETWORK_MENU.disable_unsecured_http_and_telnet_console',
              icon: 'web_asset_off',
              click: ActionMetaType.DISABLE_UNSECURED_CONSOLE,
              key: 'disableUnsecured',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.execute_cli.title',
          icon: 'integration_instructions',
          menuName: 'executeCli',
          menuData: [
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_script',
              icon: 'terminal',
              click: ActionMetaType.CLI_INTERFACE,
              key: 'cliInterface',
            },
            {
              title: 'NETWORK_MENU.execute_cli.menu.execute_cli_object',
              icon: 'library_books',
              click: ActionMetaType.EXECUTE_CLI_OBJECT,
              key: 'executeCliObject',
            },
          ],
        },
      ],
      menuForIpcDeviceManagement: [
        {
          title: 'NETWORK_MENU.device_configuration',
          icon: 'build',
          menuName: 'deviceConfiguration',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.serial_port_monitoring',
              icon: 'settings',
              click: 'openSerialPortMonitoring',
              key: 'serialPortMonitor',
            },
            {
              title: 'NETWORK_MENU.wifi_channel_change',
              iconSrc: 'network_manage_FILL0_wght400_GRAD0_opsz24.svg',
              click: ActionMetaType.CHANGE_WIFI_CHANNEL,
              key: 'changeWifiChannel',
            },
            {
              title: 'NETWORK_MENU.add_wifi_ssid',
              iconSrc: 'network_manage_FILL0_wght400_GRAD0_opsz24.svg',
              click: ActionMetaType.ADD_WIFI_SSID,
              key: 'addWifiSsid',
            },
            {
              title: 'NETWORK_MENU.dynamic_mac_sticky',
              iconSrc: 'settings_b_roll_FILL0_wght400_GRAD0_opsz24.svg',
              click: ActionMetaType.DYNAMIC_MAC_STICKY,
              key: 'dynamicMACSticky',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.device_control',
          icon: 'device_hub',
          menuName: 'deviceControl',
          menuData: [
            {
              title: 'NETWORK_MENU.reboot',
              icon: 'settings_power',
              click: ActionMetaType.REBOOT,
              key: 'reboot',
            },
            {
              title: 'NETWORK_MENU.create_a_snapshot',
              icon: 'screenshot_monitor',
              click: ActionMetaType.CREATE_SNAPSHOT,
              key: 'createSnapshot',
            },
            {
              title: 'NETWORK_MENU.restore_to_created_snapshot',
              icon: 'settings_backup_restore',
              click: ActionMetaType.RESTORE_TO_CREATE_SNAPSHOT,
              key: 'restoreSnapshot',
            },
          ],
        },
        {
          id: 'cybersecurityControl',
          title: 'NETWORK_MENU.cybersecurity_control',
          icon: 'security',
          menuName: 'cybersecurityControl',
          menuData: [
            {
              title: 'NETWORK_MENU.mac_sticky_on_off',
              iconSrc: 'assignment_turned_in_FILL0_wght400_GRAD0_opsz24.svg',
              click: ActionMetaType.MAC_STICKY_SWITCH,
              key: 'macSticky',
            },
            {
              title: 'NETWORK_MENU.restart_mac_sticky_learning',
              iconSrc: 'assignment_turned_in_FILL0_wght400_GRAD0_opsz24.svg',
              click: ActionMetaType.RELEARN_MAC_STICKY,
              key: 'relearnMacSticky',
            },
            {
              title: 'NETWORK_MENU.disable_unused_ethernet_and_fiber_ports',
              iconSrc: 'do_not_disturb_off_FILL0_wght400_GRAD0_opsz24.svg',
              click: ActionMetaType.DISABLE_UNUSED_PORT,
              key: 'disableUnused',
            },
            {
              title: 'NETWORK_MENU.disable_unsecured_http_and_telnet_console',
              icon: 'web_asset_off',
              click: ActionMetaType.DISABLE_UNSECURED_CONSOLE,
              key: 'disableUnsecured',
            },
          ],
        },
      ],
      menuForDeviceManagementTitle: [
        {
          title: 'NETWORK_MENU.device_configuration',
          icon: 'build',
          click: 'deviceConfiguration',
        },
        {
          title: 'NETWORK_MENU.device_control',
          icon: 'device_hub',
          click: 'deviceControl',
        },
        {
          title: 'NETWORK_MENU.cybersecurity_control',
          icon: 'security',
          click: 'cybersecurityControl',
        },
        {
          title: 'NETWORK_MENU.execute_cli.title',
          icon: 'integration_instructions',
          click: 'executeCli',
        },
      ],
      wenConsoleMenu: [
        { title: 'NETWORK_MENU.tools.menu.web_console', icon: 'web', click: 'openWebConsole', key: 'webConsole' },
      ],
      groupingMenuForIED: [
        {
          title: 'NETWORK_MENU.grouping',
          icon: 'group_work',
          click: 'handleIedGrouping',
          key: 'iedGrouping',
        },
        {
          title: 'NETWORK_MENU.ungrouping',
          icon: 'group_work',
          click: 'handleIedUngrouping',
          key: 'iedUngrouping',
        },
        {
          title: 'NETWORK_MENU.refresh',
          icon: 'refresh',
          click: 'handleRefreshDevices',
          key: 'refreshDevicesForIED',
        },
      ],
      iopacDeviceMenu: [
        { title: 'NETWORK_MENU.group.menu.change_group', icon: 'edit', click: 'openChangeGroup', key: 'changeGroup' },
        {
          title: 'NETWORK_MENU.delete',
          icon: 'delete_forever',
          click: 'openDeleteGroupDialog',
          key: 'deleteGroup',
        },
      ],
      iopacCpuMenu: [
        {
          title: 'NETWORK_MENU.device_configuration',
          icon: 'build',
          menuName: 'deviceConfiguration',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.generate_qr_code',
              svgIcon: 'qr_code',
              click: 'generateQrCode',
              key: 'generateQrCode',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.assign_model',
              icon: 'touch_app',
              click: 'openAssignModelDialog',
              key: 'assignModel',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.basic_information',
              icon: 'info',
              click: 'openDeviceBasicInfoDialog',
              key: 'basicInformation',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.ip_configuration',
              icon: 'settings',
              click: 'openDeviceIPConfig',
              key: 'ipConfiguration',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
              icon: 'settings',
              click: 'openSNMPSetting',
              key: 'snmpSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.polling_settings',
              icon: 'settings',
              click: 'openPollingSetting',
              key: 'pollingSettings',
            },
            {
              title: 'NETWORK_MENU.modify_device_alias',
              icon: 'settings',
              click: 'openModifyDeviceAlias',
              key: 'modifyDeviceAlias',
            },
          ],
        },
        { title: 'NETWORK_MENU.tools.menu.web_console', icon: 'web', click: 'openWebConsole', key: 'webConsole' },
        {
          title: 'NETWORK_MENU.document.title',
          icon: 'insert_drive_file',
          click: 'openSetDocument',
          key: 'setDocument',
        },
        {
          title: 'NETWORK_MENU.refresh',
          icon: 'refresh',
          click: 'refreshDevice',
          key: 'refreshDevice',
        },
        {
          title: 'NETWORK_MENU.add_link',
          icon: 'link',
          click: 'openAddLink',
          key: 'addLink',
        },
        this.deleteDeviceMenuData,
      ],
      iopacSwitchMenu: [
        {
          title: 'NETWORK_MENU.device_configuration',
          icon: 'build',
          menuName: 'deviceConfiguration',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.generate_qr_code',
              svgIcon: 'qr_code',
              click: 'generateQrCode',
              key: 'generateQrCode',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.assign_model',
              icon: 'touch_app',
              click: 'openAssignModelDialog',
              key: 'assignModel',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.basic_information',
              icon: 'info',
              click: 'openDeviceBasicInfoDialog',
              key: 'basicInformation',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.ip_configuration',
              icon: 'settings',
              click: 'openDeviceIPConfig',
              key: 'ipConfiguration',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.trap_server',
              icon: 'computer',
              click: 'openDeviceTrapConfig',
              key: 'trapServer',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.port_settings',
              icon: 'crop_square',
              click: 'openPortSettingDialog',
              key: 'portSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
              icon: 'settings',
              click: 'openSNMPSetting',
              key: 'snmpSettings',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.polling_settings',
              icon: 'settings',
              click: 'openPollingSetting',
              key: 'pollingSettings',
            },
            {
              title: 'NETWORK_MENU.device_login_account',
              icon: 'settings',
              click: 'openDeviceLoginAccount',
              key: 'deviceLoginAccount',
            },
            {
              title: 'NETWORK_MENU.modify_device_alias',
              icon: 'settings',
              click: 'openModifyDeviceAlias',
              key: 'modifyDeviceAlias',
            },
          ],
        },
        {
          title: 'NETWORK_MENU.device_control',
          icon: 'device_hub',
          menuName: 'deviceControl',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.import_config',
              icon: 'cloud_download',
              click: 'openImportConfig',
              key: 'importConfig',
            },
            {
              title: 'NETWORK_MENU.maintenance.menu.export_config',
              icon: 'backup',
              click: 'openExportConfig',
              key: 'exportConfig',
            },
          ],
        },
        { title: 'NETWORK_MENU.tools.menu.web_console', icon: 'web', click: 'openWebConsole', key: 'webConsole' },
        {
          title: 'NETWORK_MENU.document.title',
          icon: 'insert_drive_file',
          click: 'openSetDocument',
          key: 'setDocument',
        },
        {
          title: 'NETWORK_MENU.refresh',
          icon: 'refresh',
          click: 'refreshDevice',
          key: 'refreshDevice',
        },
        {
          title: 'NETWORK_MENU.add_link',
          icon: 'link',
          click: 'openAddLink',
          key: 'addLink',
        },
        this.deleteDeviceMenuData,
      ],
      iopacCpuSwitchMultiSelectedMenu: [
        {
          title: 'NETWORK_MENU.device_configuration',
          icon: 'build',
          menuName: 'deviceConfiguration',
          menuData: [
            {
              title: 'NETWORK_MENU.maintenance.menu.snmp_settings',
              icon: 'settings',
              click: 'openSNMPSetting',
              key: 'snmpSettings',
            },
          ],
        },
        this.deleteDeviceMenuData,
      ],
    };
  }
}
