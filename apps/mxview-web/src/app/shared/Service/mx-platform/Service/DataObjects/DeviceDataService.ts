import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

import { Observable, forkJoin, lastValueFrom } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

import {
  SNMPVersion,
  SNMPv3AuthType,
  SNMPv3PrivacyProtocol,
  SNMPv3SecurityLevel,
} from '../../DataDef/DataTypeDefinitions';
import {
  DeviceAccessParameterUnit,
  DevicePortEnableUnit,
  DevicePortInterfacesUnit,
  DevicePortNameUnit,
  DeviceSNMPSystemInformationUnit,
  DeviceTrapParameterUnit,
  DeviceTrapServerSettingUnit,
  DeviceTrendPropertyUnit,
  DeviceTrendUnit,
  DeviceUnit,
  IpConfigUnit,
  LEDStatusUnit,
  ModelDefinitionUnit,
  NetworkVlanInformationUnit,
  SerialPortStatus,
} from '../../DataDef/DevicesDataDefs';
import { ServerConfigurationService } from '../../MXviewGatewayConfig';
import { DataObject } from '../DataObject';
import { DeviceAvailabilityUnit, DeviceDocumentUnit, DeviceSummaryUnit } from './../../DataDef/DevicesDataDefs';

@Injectable()
export class DeviceDataService {
  constructor(private dataObject: DataObject, private http: HttpClient) {}

  // -------- DEVICE --------
  getDevice(site?: string, ip?: string, id?: number, ignore_ioPac?: boolean): Promise<any> {
    const options = this.dataObject.createOptions();
    let url: any;

    if (site !== undefined) {
      if (id !== undefined) {
        url = ServerConfigurationService.getHostURL() + '/api/devices/id/' + id + '/site/' + site;
      } else if (ip !== undefined) {
        url = ServerConfigurationService.getHostURL() + '/api/devices/ip/' + ip + '/site/' + site;
      } else {
        url = ServerConfigurationService.getHostURL() + '/api/devices/site/' + site;
      }
    } else {
      url = ServerConfigurationService.getHostURL() + '/api/devices';
    }

    // Append ignore_ioPac query parameter if provided
    if (ignore_ioPac !== undefined) {
      url += (url.includes('?') ? '&' : '?') + 'ignore_ioPac=' + ignore_ioPac;
    }

    return new Promise((resolve, reject) => {
      forkJoin(this.http.get(url, options)).subscribe(
        data => {
          const deviceList = data[0];
          if (deviceList instanceof Array) {
            deviceList.forEach(device => {
              if (
                device.alias !== undefined &&
                device.alias !== null &&
                device.device_components.Alias !== undefined &&
                device.device_components.Alias !== null &&
                device.device_components.Alias.status !== undefined &&
                device.device_components.Alias.status !== null
              ) {
                let filterAlias;
                if (typeof device.alias === 'number') {
                  filterAlias = device.alias.toString();
                } else {
                  filterAlias = device.alias;
                }
                device.device_components.Alias.status = device.alias;
              }
            });
          }
          resolve(deviceList);
        },
        error => {
          reject(error);
        }
      );
    });
  }

  getDeviceByGroup(site?: string, groupId?: number): Promise<any> {
    const options = this.dataObject.createOptions();
    let url: any;

    if (site !== undefined && groupId !== undefined) {
      url = ServerConfigurationService.getHostURL() + '/api/devices/group/' + groupId + '/site/' + site;
    } else if (site !== undefined && groupId === undefined) {
      url = ServerConfigurationService.getHostURL() + '/api/devices/site/' + site;
    } else {
      url = ServerConfigurationService.getHostURL() + '/api/devices';
    }

    return new Promise((resolve, reject) => {
      forkJoin(this.http.get(url, options)).subscribe(
        data => {
          const deviceList = data[0];
          if (deviceList instanceof Array) {
            deviceList.forEach(device => {
              if (
                device.alias !== undefined &&
                device.alias !== null &&
                device.device_components.Alias !== undefined &&
                device.device_components.Alias !== null &&
                device.device_components.Alias.status !== undefined &&
                device.device_components.Alias.status !== null
              ) {
                let filterAlias;
                if (typeof device.alias === 'number') {
                  filterAlias = device.alias.toString();
                } else {
                  filterAlias = device.alias;
                }
                filterAlias = filterAlias.replace('<', '');
                device.alias = filterAlias.replace('>', '');
                device.device_components.Alias.status = device.alias;
              }
            });
          }
          resolve(deviceList);
        },
        error => {
          reject(error);
        }
      );
    });
  }

  getDeviceSNMPSystemInfo(site: string, ip: string): Promise<DeviceSNMPSystemInformationUnit[]> {
    const options = this.dataObject.createOptions();
    let url: any;

    url = ServerConfigurationService.getHostURL() + '/api/devices/ip/' + ip + '/systemInformation/site/' + site;

    return this.http
      .get<DeviceSNMPSystemInformationUnit[]>(url, options)
      .pipe(catchError(this.dataObject.handleError))
      .toPromise();
  }

  getDeviceTrapServerSettings(site: string, ip: string): Promise<DeviceTrapServerSettingUnit[]> {
    const options = this.dataObject.createOptions();
    let url: any;

    url = ServerConfigurationService.getHostURL() + '/api/devices/ip/' + ip + '/trapServers/site/' + site;

    return this.http
      .get<DeviceTrapServerSettingUnit[]>(url, options)
      .pipe(catchError(this.dataObject.handleError))
      .toPromise();
  }

  getIgnoredDevice(site: string): Promise<DeviceUnit[]> {
    const options = this.dataObject.createOptions();
    let url: any;

    url = ServerConfigurationService.getHostURL() + '/api/devices/ignored/site/' + site;

    return this.http
      .get<DeviceUnit[]>(url, options)
      .pipe(
        map(response => {
          response.forEach(device => {
            if (device.alias !== undefined && device.alias.length > 0) {
              device.alias = device.alias.replace('<', '');
              device.alias = device.alias.replace('>', '');
            }
          });
          return response;
        }),
        catchError(this.dataObject.handleError)
      )
      .toPromise();
  }

  deleteDevice(site: string, ids: any[]): Promise<any> {
    const options = this.dataObject.createOptionsWithDeleteDevice(ids);
    let url: any;

    url = ServerConfigurationService.getHostURL() + '/api/devices/site/' + site;

    return this.http.delete<any>(url, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  deleteDeviceFromIgnoredList(site: string, id: number): Promise<any> {
    const options = this.dataObject.createOptionsWithDeviceId(id);
    let url: any;

    url = ServerConfigurationService.getHostURL() + '/api/devices/ignored/site/' + site;

    return this.http.request<any>('delete', url, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  // -------- PoE --------
  getPoE(site: string, groupId: number): Promise<any[]> {
    const options = this.dataObject.createOptions();
    let url: any;

    url = ServerConfigurationService.getHostURL() + '/api/devices/poe/site/' + site + '?groups=' + groupId;

    return this.http.get<any[]>(url, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  // -------- add Device --------
  addDevice(
    site: string,
    ip: string,
    group: number,
    addMode: number,
    sysobjid: string,
    username: string,
    password: string,
    snmpVersion: SNMPVersion,
    snmpPort: number,
    snmpv3SecurityLevel: SNMPv3SecurityLevel,
    snmpv3AuthenticationType: SNMPv3AuthType,
    readCommunity: string,
    writeCommunity: string,
    snmpUsername: string,
    snmpPassword: string,
    snmpv3PrivacyProtocol: SNMPv3PrivacyProtocol,
    snmpv3EncryptionKey: string,
    model: string,
    autoDetectModel: boolean,
    addToBaseline: boolean,
    x?: number,
    y?: number
  ): Promise<any> {
    const options = this.dataObject.createOptions();
    let url: any;
    let bodyString = '';

    let outputObject;
    if (x !== undefined && y !== undefined) {
      outputObject = {
        group,
        addMode,
        sysobjid,
        snmp_version: snmpVersion,
        snmp_port: snmpPort,
        read_community: readCommunity,
        write_community: writeCommunity,
        snmpv3_security_level: snmpv3SecurityLevel,
        snmpv3_authentication_type: snmpv3AuthenticationType,
        snmpv3_privacy_protocol: snmpv3PrivacyProtocol,
        snmpv3_encryption_key: snmpv3EncryptionKey,
        x,
        y,
        model,
        auto_detect_model: autoDetectModel,
        add_to_baseline: addToBaseline,
      };
    } else {
      outputObject = {
        group,
        addMode,
        sysobjid,
        snmp_version: snmpVersion,
        snmp_port: snmpPort,
        read_community: readCommunity,
        write_community: writeCommunity,
        snmpv3_security_level: snmpv3SecurityLevel,
        snmpv3_authentication_type: snmpv3AuthenticationType,
        snmpv3_privacy_protocol: snmpv3PrivacyProtocol,
        snmpv3_encryption_key: snmpv3EncryptionKey,
        model,
        auto_detect_model: autoDetectModel,
        add_to_baseline: addToBaseline,
      };
    }

    if (sysobjid === '0' || sysobjid === '') {
      console.log('add icmp device or auto detect');
      outputObject.sysobjid = undefined;
    } else {
      outputObject.sysobjid = sysobjid;
    }

    if (username !== null && username.length > 0) {
      outputObject.username = username;
    }

    if (password !== null && password.length > 0) {
      outputObject.password = password;
    }

    if (snmpUsername !== null && snmpUsername.length > 0) {
      outputObject.snmp_username = snmpUsername;
    }

    if (snmpPassword !== null && snmpPassword.length > 0) {
      outputObject.snmp_password = snmpPassword;
    }

    bodyString = JSON.stringify(outputObject);

    url = ServerConfigurationService.getHostURL() + '/api/devices/ip/' + ip + '/site/' + site;

    return this.http.post<any>(url, bodyString, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  // -------- export Device config--------
  /*exportConfig(site: string, ip: string, filename: string, username: string, password: string): Promise< any >
  {
      const options = this.dataObject.createOptions();
      let url: any;

      url = ServerConfigurationService.getHostURL() + '/api/devices/ip/'
      + ip + '/config/site/' + site + '?username=' + username + '&password=' + password;

      return this.http.get<any>(url, options)
                      .map( (value, index) => { return this.extractConfigFileData(value, index, filename);} )
                      .pipe(       catchError(this.dataObject.handleError)     ).toPromise();
  }

  private extractConfigFileData(res: Response, index: number, filename: string): any{

      let body = res.json();

      // 處理傳回值
      let returnValue: any = [];
      let tmp: RestfulAPIResult = { statusCode: res.status, statusText: res.statusText, customMessage: ''};
      returnValue.push(tmp);

      let configObj: ExportConfigFile[] = <ExportConfigFile[]>body;

      // save file
      let file = new Blob([configObj[0].config]);
      saveAs(file, filename);

      return returnValue;
  }*/

  updateDeviceSNMPSystemInfo(
    site: string,
    ip: string,
    sysName: string,
    sysContact: string,
    sysLocation: string
  ): Promise<any> {
    const options = this.dataObject.createOptions();
    const bodyString = JSON.stringify({ sysName, sysContact, sysLocation }); // Stringify payload
    const url = ServerConfigurationService.getHostURL() + '/api/devices/ip/' + ip + '/systemInformation/site/' + site;

    return this.http.put<any>(url, bodyString, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  updateDeviceTrapServerSettings(
    site: string,
    deviceIp: string,
    trapServerSettings: DeviceTrapParameterUnit[]
  ): Promise<any> {
    const options = this.dataObject.createOptions();
    const parameterArray: any[] = [];

    for (const entry of trapServerSettings) {
      const ip: string = entry.ip;
      const community: string = entry.community;
      parameterArray.push({ ip, community });
    }

    const bodyString = JSON.stringify(parameterArray); // Stringify payload
    const url = ServerConfigurationService.getHostURL() + '/api/devices/ip/' + deviceIp + '/trapServers/site/' + site;

    return this.http.put<any>(url, bodyString, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  // -------- import device config--------
  importConfig(site: string, ip: string, config: File, username: string, password: string): Promise<any> {
    const options = this.dataObject.createFormDataOptions();

    const formData: FormData = new FormData();
    formData.append('data', config, config.name);
    formData.append('username', username);
    formData.append('password', password);

    const url = ServerConfigurationService.getHostURL() + '/api/devices/ip/' + ip + '/config/site/' + site;

    return this.http.put<any>(url, formData, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  importLocalConfigToConfigCenter(siteId: string, ipAddress: string, configFile: File): Promise<any> {
    const options = this.dataObject.createFormDataOptions();

    const formData: FormData = new FormData();
    formData.append('data', configFile, configFile.name);

    const url =
      ServerConfigurationService.getHostURL() + '/api/ConfigCenter/site/' + siteId + '/config/ip/' + ipAddress;

    return this.http.put<any>(url, formData, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  getDeviceBackupConfigHistoryRecord(siteId: string): Promise<any> {
    const options = this.dataObject.createOptions();
    let url: any;

    url = ServerConfigurationService.getHostURL() + '/api/ConfigCenter/site/' + siteId + '/config/records/list/';
    return this.http.get<any>(url, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  importConfigToDeviceFromConfigCenter(siteId: string, ipAddress: string, filename: string): Promise<any> {
    const options = this.dataObject.createOptions();
    const bodyString = JSON.stringify({ filename }); // Stringify payload
    const url =
      ServerConfigurationService.getHostURL() +
      '/api/ConfigCenter/site/' +
      siteId +
      '/config/records/ip/' +
      ipAddress +
      '?filename=' +
      filename;
    return this.http.put<any>(url, bodyString, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  // -------- firmware upgrade --------
  firmwareUpgrade(site: string, ip: string, data: File, username: string, password: string): Promise<any> {
    const options = this.dataObject.createFormDataOptions();

    const formData: FormData = new FormData();
    formData.append('data', data, data.name);
    formData.append('username', username);
    formData.append('password', password);

    const url = ServerConfigurationService.getHostURL() + '/api/devices/ip/' + ip + '/firmware/site/' + site;

    return this.http.put<any>(url, formData, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  // -------- change icon --------
  changeIconByIp(site: string, ips: string[], icon: File, applyToAll: boolean): Promise<any> {
    const options = this.dataObject.createFormDataOptions();

    const formData: FormData = new FormData();
    formData.append('icon', icon, icon.name);
    formData.append('apply_to_all', String(applyToAll));
    formData.append('ips', JSON.stringify(ips));

    const url = ServerConfigurationService.getHostURL() + '/api/devices/site/' + site + '/icons';

    return this.http.put<any>(url, formData, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  changeIconById(site: string, id: number, icon: File, applyToAll: boolean): Promise<any> {
    const options = this.dataObject.createFormDataOptions();

    const formData: FormData = new FormData();
    formData.append('icon', icon, icon.name);
    formData.append('apply_to_all', String(applyToAll));

    const url = ServerConfigurationService.getHostURL() + '/api/devices/id/' + id + '/icon/site/' + site;

    return this.http.put<any>(url, formData, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  // -------- assign model --------
  assignModel(site: string, ip: string, model: string, sysobjid: string): Promise<any> {
    const options = this.dataObject.createOptions();
    const bodyString = JSON.stringify({ model, sysobjid }); // Stringify payload
    const url = ServerConfigurationService.getHostURL() + '/api/devices/ip/' + ip + '/site/' + site;

    return this.http.put<any>(url, bodyString, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  // -------- assign alias --------
  assignAlias(site: string, ip: string, alias: string): Promise<any> {
    const options = this.dataObject.createOptions();
    const bodyString = JSON.stringify({ alias }); // Stringify payload
    const url = ServerConfigurationService.getHostURL() + '/api/devices/ip/' + ip + '/site/' + site;

    return this.http.put<any>(url, bodyString, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  // -------- change group --------
  changeGroup(site: string, ip: string, group: number): Promise<any> {
    const options = this.dataObject.createOptions();
    const bodyString = JSON.stringify({ group }); // Stringify payload
    const url = ServerConfigurationService.getHostURL() + '/api/devices/ip/' + ip + '/site/' + site;

    return this.http.put<any>(url, bodyString, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  changeToGroup(currentId: number, site: string, groupId: number): Promise<any> {
    const options = this.dataObject.createOptions();
    const bodyString = JSON.stringify({ parent_id: groupId }); // Stringify payload
    const url = ServerConfigurationService.getHostURL() + '/api/groups/site/' + site + '/id/' + currentId;
    return this.http.put<any>(url, bodyString, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  changeDevicesToGroup(site: string, devices: string[], group: number): Promise<any> {
    const options = this.dataObject.createOptions();

    const bodyString = JSON.stringify({ devices });

    const url = ServerConfigurationService.getHostURL() + '/api/devices/group/' + group + '/site/' + site;

    return this.http.post<any>(url, bodyString, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  // -------- getModelDefinitions --------
  getModelDefinitions(site: string): Promise<ModelDefinitionUnit[]> {
    const options = this.dataObject.createOptions();
    let url: any;

    url = ServerConfigurationService.getHostURL() + '/api/models/definitions/site/' + site;

    return this.http.get<any>(url, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  // -------- getDeviceAccessParameter --------
  getDeviceAccessParameters(site: string, ip: string): Promise<DeviceAccessParameterUnit[]> {
    const options = this.dataObject.createOptions();
    let url: any;

    url = ServerConfigurationService.getHostURL() + '/api/devices/ip/' + ip + '/access/site/' + site;

    return this.http.get<any>(url, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  // -------- getIPConfig --------
  getIPConfig(site: string, ip: string): Promise<IpConfigUnit[]> {
    const options = this.dataObject.createOptions();
    let url: any;

    url = ServerConfigurationService.getHostURL() + '/api/devices/ip/' + ip + '/ipconfig/site/' + site;

    return this.http.get<any>(url, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  // -------- refreshDevice --------
  refreshDevice(site: string, ip: string): Promise<any> {
    const options = this.dataObject.createOptions();
    let url: any;
    const bodyString = '';

    url = ServerConfigurationService.getHostURL() + '/api/devices/ip/' + ip + '/refresh/site/' + site;

    return this.http.put<any>(url, bodyString, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  // -------- upadteIPConfig --------
  updateIPconfig(
    site: string,
    ip: string,
    username: string,
    password: string,
    newip: string,
    netmask: string,
    gateway: string,
    dns1: string,
    dns2: string,
    ipflag: number
  ): Promise<any> {
    const options = this.dataObject.createOptions();
    let url: any;

    let bodyString = '';
    const outputObject: any = { username, password, ip: newip, netmask, gateway, ipflag };

    if (dns1 !== undefined) {
      outputObject.dns1 = dns1;
    }
    if (dns2 !== undefined) {
      outputObject.dns2 = dns2;
    }

    bodyString = JSON.stringify(outputObject);
    url = ServerConfigurationService.getHostURL() + '/api/devices/ip/' + ip + '/ipconfig/site/' + site;

    return this.http.put<any>(url, bodyString, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  // -------- getLEDs --------
  getLEDs(site: string, ip: string): Promise<LEDStatusUnit[]> {
    const options = this.dataObject.createOptions();
    let url: any;

    url = ServerConfigurationService.getHostURL() + '/api/devices/ip/' + ip + '/leds/site/' + site;

    return this.http.get<any>(url, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  // -------- Trigger Locator --------
  triggerLocator(site: string, ip: string, doBlink: boolean): Promise<any> {
    const options = this.dataObject.createOptions();
    const url = ServerConfigurationService.getHostURL() + '/api/devices/ip/' + ip + '/locate/site/' + site;

    if (doBlink === true) {
      return this.http.put<any>(url, '', options).pipe(catchError(this.dataObject.handleError)).toPromise();
    } else {
      return this.http.delete<any>(url, options).pipe(catchError(this.dataObject.handleError)).toPromise();
    }
  }

  // -------- Get Port Information --------
  getPortInformation(site: string, ip: string): Promise<DevicePortInterfacesUnit[]> {
    const options = this.dataObject.createOptions();
    let url: any;

    url = ServerConfigurationService.getHostURL() + '/api/devices/ip/' + ip + '/interfaces/site/' + site;

    return this.http.get<any>(url, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  setPortName(site: string, ip: string, items: DevicePortNameUnit[]): Promise<any> {
    const options = this.dataObject.createOptions();
    let url: any;

    const bodyString = JSON.stringify(items);

    url = ServerConfigurationService.getHostURL() + '/api/devices/ip/' + ip + '/interfaces/names/site/' + site;

    return this.http.put<any>(url, bodyString, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  setPortEnable(site: string, ip: string, items: DevicePortEnableUnit[]): Promise<any> {
    const options = this.dataObject.createOptions();
    let url: any;

    const bodyString = JSON.stringify(items);
    url = ServerConfigurationService.getHostURL() + '/api/devices/ip/' + ip + '/interfaces/enable/site/' + site;

    return this.http.put<any>(url, bodyString, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  setDeviceSNMPParameters(
    siteId: string,
    deviceIp: string,
    snmpVersion: number,
    snmpPort: number,
    snmpv3SecurityLevel: number,
    snmpv3AuthenticationType: number,
    readCommunity: string,
    writeCommunity: string,
    snmpUsername: string,
    snmpPassword: string,
    snmpv3EncryptionKey: string,
    snmpv3PrivacyProtocol: number
  ): Promise<any> {
    const options = this.dataObject.createOptions();
    const bodyString = JSON.stringify({
      snmp_version: snmpVersion,
      snmp_port: snmpPort,
      snmpv3_security_level: snmpv3SecurityLevel,
      snmpv3_authentication_type: snmpv3AuthenticationType,
      read_community: readCommunity,
      write_community: writeCommunity,
      snmp_username: snmpUsername,
      snmp_password: snmpPassword,
      snmpv3_encryption_key: snmpv3EncryptionKey,
      snmpv3_privacy_protocol: snmpv3PrivacyProtocol,
    });

    let url: string;

    url = ServerConfigurationService.getHostURL() + '/api/devices/ip/' + deviceIp + '/site/' + siteId + '/';

    return this.http.put<any>(url, bodyString, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  setDevicePollingIp(siteId: string, deviceIp: string, pollingIp: string): Promise<any> {
    const options = this.dataObject.createOptions();
    let url: any;

    let bodyString = '';
    const outputObject: any = {};

    if (pollingIp !== undefined) {
      outputObject.polling_ip = pollingIp;
    }

    bodyString = JSON.stringify(outputObject);
    url = ServerConfigurationService.getHostURL() + '/api/devices/ip/' + deviceIp + '/site/' + siteId;

    return this.http.put<any>(url, bodyString, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  setDeviceParameters(
    site: string,
    id: number,
    autoDetectModel: boolean,
    sysobjectid: string,
    alias: string,
    group: number,
    x: number,
    y: number,
    useGlobalAccount: boolean,
    username: string,
    password: string,
    icmpPollingInterval: number,
    snmpPollingInterval: number,
    icmpUnreachableWarningThreshold: number,
    snmpUnreachableWarningThreshold: number,
    snmpVersion: SNMPVersion,
    snmpPort: number,
    snmpv3SecurityLevel: SNMPv3SecurityLevel,
    snmpv3AuthenticationType: SNMPv3AuthType,
    snmpv3PrivacyProtocol: SNMPv3PrivacyProtocol,
    readCommunity: string,
    writeCommunity: string,
    snmpUsername: string,
    snmpPassword: string,
    snmpv3EncryptionKey: string
  ): Promise<any> {
    const options = this.dataObject.createOptions();
    let url: any;

    let bodyString = '';
    const outputObject: any = {};

    if (autoDetectModel !== undefined) {
      outputObject.auto_detect_model = autoDetectModel;
    }
    if (sysobjectid !== undefined) {
      outputObject.sysobjectid = sysobjectid;
    }
    if (alias !== undefined) {
      outputObject.alias = alias;
    }
    if (group !== undefined) {
      outputObject.group = group;
    }
    if (x !== undefined) {
      outputObject.x = x;
    }
    if (y !== undefined) {
      outputObject.y = y;
    }
    if (useGlobalAccount !== undefined) {
      outputObject.use_global_account = useGlobalAccount;
    }
    if (username !== undefined) {
      outputObject.username = username;
    }
    if (password !== undefined) {
      outputObject.password = password;
    }

    if (icmpPollingInterval !== undefined) {
      outputObject.icmp_polling_interval = icmpPollingInterval;
    }
    if (snmpPollingInterval !== undefined) {
      outputObject.snmp_polling_interval = snmpPollingInterval;
    }
    if (icmpUnreachableWarningThreshold !== undefined) {
      outputObject.icmp_unreachable_warning_threshold = icmpUnreachableWarningThreshold;
    }
    if (snmpUnreachableWarningThreshold !== undefined) {
      outputObject.snmp_unreachable_warning_threshold = snmpUnreachableWarningThreshold;
    }

    if (snmpVersion !== undefined) {
      outputObject.snmp_version = snmpVersion;
    }
    if (snmpPort !== undefined) {
      outputObject.snmp_port = snmpPort;
    }
    if (snmpv3SecurityLevel !== undefined) {
      outputObject.snmpv3_security_level = snmpv3SecurityLevel;
    }
    if (snmpv3AuthenticationType !== undefined) {
      outputObject.snmpv3_authentication_type = snmpv3AuthenticationType;
    }
    if (snmpv3PrivacyProtocol !== undefined) {
      outputObject.snmpv3_privacy_protocol = snmpv3PrivacyProtocol;
    }
    if (readCommunity !== undefined) {
      outputObject.read_community = readCommunity;
    }
    if (writeCommunity !== undefined) {
      outputObject.write_community = writeCommunity;
    }
    if (snmpUsername !== undefined) {
      outputObject.snmp_username = snmpUsername;
    }
    if (snmpPassword !== undefined) {
      outputObject.snmp_password = snmpPassword;
    }
    if (snmpv3EncryptionKey !== undefined) {
      outputObject.snmpv3_encryption_key = snmpv3EncryptionKey;
    }

    bodyString = JSON.stringify(outputObject);
    url = ServerConfigurationService.getHostURL() + '/api/devices/id/' + id + '/site/' + site;

    return this.http.put<any>(url, bodyString, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  // -------- getIPConfig --------
  getNetworkVlanInformation(site: string): Promise<NetworkVlanInformationUnit[]> {
    const options = this.dataObject.createOptions();
    let url: any;

    url = ServerConfigurationService.getHostURL() + '/api/devices/vlan/site/' + site;

    return this.http.get<any>(url, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  setDeviceAdvanceSetting(
    site: string,
    id: number,
    alias: string,
    useGlobalAccount: boolean,
    username?: string,
    password?: string
  ): Promise<any> {
    const options = this.dataObject.createOptions();
    let bodyString = '';
    if (username !== undefined && password !== undefined) {
      bodyString = JSON.stringify({
        alias,
        use_global_account: useGlobalAccount,
        username,
        password,
      });
    } else {
      bodyString = JSON.stringify({
        alias,
        use_global_account: useGlobalAccount,
      });
    }

    let url: string;

    url = ServerConfigurationService.getHostURL() + '/api/devices/id/' + id + '/site/' + site + '/';

    return this.http.put<any>(url, bodyString, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  setDevicePollingSetting(
    site: string,
    id: number,
    icmpPollingInterval: number,
    snmpPollingInterval: number,
    icmpUnreachableWarningThreshold: number,
    snmpUnreachableWarningThreshold: number
  ): Promise<any> {
    const options = this.dataObject.createOptions();
    let bodyString = '';

    bodyString = JSON.stringify({
      icmp_polling_interval: icmpPollingInterval,
      snmp_polling_interval: snmpPollingInterval,
      icmp_unreachable_warning_threshold: icmpUnreachableWarningThreshold,
      snmp_unreachable_warning_threshold: snmpUnreachableWarningThreshold,
    });

    let url: string;

    url = ServerConfigurationService.getHostURL() + '/api/devices/id/' + id + '/site/' + site + '/';

    return this.http.put<any>(url, bodyString, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  getDocumentByIp(site: string, ip: string): Promise<DeviceDocumentUnit[]> {
    const options = this.dataObject.createOptions();

    const url = ServerConfigurationService.getHostURL() + '/api/devices/ip/' + ip + '/documents/site/' + site;

    return this.http.get<any>(url, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  updateDocumentByIp(site: string, ip: string, data: File): Promise<any> {
    const options = this.dataObject.createFormDataOptions();
    const formData: FormData = new FormData();
    formData.append('data', data, data.name);
    const url = ServerConfigurationService.getHostURL() + '/api/devices/ip/' + ip + '/documents/site/' + site;
    return this.http.put<any>(url, formData, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  deleteDocumentByIp(site: string, ip: string): Promise<any> {
    const options = this.dataObject.createFormDataOptions();
    const url = ServerConfigurationService.getHostURL() + '/api/devices/ip/' + ip + '/documents/site/' + site;
    return this.http.delete<any>(url, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  getDeviceInterface(siteId: string, deviceIp: string): Promise<any> {
    const options = this.dataObject.createOptions();
    let url: any;

    url = ServerConfigurationService.getHostURL() + '/api/devices/ip/' + deviceIp + '/ipInterfaces/site/' + siteId;

    return this.http.get<any>(url, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  setTrapServerToAll(siteId: string, serverIp: string, community: string): Promise<any> {
    const options = this.dataObject.createOptions();
    let url: any;

    let bodyString = '';
    const outputObject: any = {};

    if (serverIp !== undefined) {
      outputObject.ip = serverIp;
    }

    if (community !== undefined) {
      outputObject.community = community;
    }

    bodyString = JSON.stringify(outputObject);

    url = ServerConfigurationService.getHostURL() + '/api/devices/trapServers/site/' + siteId;

    return this.http.put<any>(url, bodyString, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  getSecurityLevel(siteId: string, ips: string[], profile: number): Promise<any> {
    const options = this.dataObject.createOptions();
    let url: any;

    url =
      ServerConfigurationService.getHostURL() +
      '/api/devices/securityLevels/site/' +
      siteId +
      '?ips=["' +
      ips.toString() +
      '"]&profile=' +
      profile;

    return this.http.get<any>(url, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  getDeviceSummary(siteId: string): Promise<DeviceSummaryUnit> {
    const options = this.dataObject.createOptions();
    let url: any;

    url = ServerConfigurationService.getHostURL() + '/api/devices/severity/summary/site/' + siteId;

    return this.http.get<any>(url, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  getDeviceAvailabilityList(siteId: string, startTime: number, endTime: number): Promise<DeviceAvailabilityUnit> {
    const options = this.dataObject.createOptions();
    let url: any;
    let params = '';

    if (startTime !== undefined) {
      params += 'start_time=' + String(startTime);
    }

    if (endTime !== undefined) {
      params += '&end_time=' + String(endTime);
    }

    url = ServerConfigurationService.getHostURL() + '/api/devices/availability/site/' + siteId + '/?' + params;

    return this.http.get<any>(url, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  getDeviceVlanStatus(siteId: string, vlanId: string): Promise<any> {
    const options = this.dataObject.createOptions();
    let url: any;

    url = ServerConfigurationService.getHostURL() + '/api/devices/site/' + siteId + '/vlan/id/' + vlanId;

    return this.http.get<any>(url, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  getNetworkVlanIds(siteId: string): Promise<any> {
    const options = this.dataObject.createOptions();
    let url: any;

    url = ServerConfigurationService.getHostURL() + '/api/devices/site/' + siteId + '/vlan/ids';

    return this.http.get<any>(url, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  clearV3TrapParseErrorStatus(siteId: string, id: number): Promise<any> {
    const options = this.dataObject.createFormDataOptions();

    const formData: FormData = new FormData();
    const url =
      ServerConfigurationService.getHostURL() +
      '/api/devices/site/' +
      siteId +
      '/id/' +
      id +
      '/v3TrapReceiveStatusReset';

    return this.http.put<any>(url, formData, options).pipe(catchError(this.dataObject.handleError)).toPromise();
  }

  /**
   * getDeviceTrendProperties
   * @param siteId string
   * @param id number
   * @returnType {Observable<DeviceTrendPropertyUnit[]>}
   */
  getDeviceTrendProperties(siteId: string, id: number): Observable<DeviceTrendPropertyUnit[]> {
    const options = this.dataObject.createOptions();
    const url =
      ServerConfigurationService.getHostURL() + '/api/devices/site/' + siteId + '/id/' + id + '/property/monitored';

    return this.http.get<DeviceTrendPropertyUnit[]>(url, options).pipe(catchError(this.dataObject.handleError));
  }

  /**
   * getDeviceTrendByProperty
   * @param siteId string
   * @param id number
   * @param property string - from getDeviceTrendProperties
   * @param startTime number
   * @param endTime number
   * @param index number
   * @returnType {Observable<DeviceTrendUnit>}
   */
  getDeviceTrendByProperty(
    siteId: string,
    id: number,
    property: string,
    startTime: number,
    endTime: number,
    index?: number
  ): Observable<DeviceTrendUnit> {
    const options = this.dataObject.createOptions();
    let url;
    if (index) {
      url =
        ServerConfigurationService.getHostURL() +
        '/api/devices/site/' +
        siteId +
        '/id/' +
        id +
        '/property/trending/' +
        property +
        '/' +
        index +
        '?start_time=' +
        startTime +
        '&end_time=' +
        endTime;
    } else {
      url =
        ServerConfigurationService.getHostURL() +
        '/api/devices/site/' +
        siteId +
        '/id/' +
        id +
        '/property/trending/' +
        property +
        '?start_time=' +
        startTime +
        '&end_time=' +
        endTime;
    }
    return this.http.get<DeviceTrendUnit>(url, options).pipe(catchError(this.dataObject.handleError));
  }

  getSerialPortStatus(ips: string[]): Promise<SerialPortStatus> {
    const options = this.dataObject.createOptions();
    const parameters = ips.join('&ip=');
    const url = ServerConfigurationService.getHostURL() + '/api/serial_port/monitoring_status?ip=' + parameters;

    return lastValueFrom(this.http.get<SerialPortStatus>(url, options).pipe(catchError(this.dataObject.handleError)));
  }
}
