import { Injectable } from '@angular/core';

import { TranslateService } from '@ngx-translate/core';
import * as _ from 'lodash';
import { Observable, Subject, Subscription } from 'rxjs';

import {
  DataType,
  EventDetectorType,
  SecurityEvents,
  Severity
} from './../../../Service/mx-platform/DataDef/DataTypeDefinitions';
import { SiteUnit } from './../../../Service/mx-platform/DataDef/SiteDataDefs';
import { DataRepositoryService } from './../../../Service/mx-platform/DataRepository/DataRepository';
import { DataService } from './../../../Service/mx-platform/Service/DataService';
import { BroadcastEvent, EventBusService } from './../../../Service/mx-platform/Service/EventBusService';
import {
  EventType,
  authServerType,
  databaseBackupStatus,
  fiberStatus,
  prpHSRPort,
  turboRingMasterStatus,
  usbEventType
} from './event-display-type';

@Injectable()
export class EventService {
  lastUpdateSiteId: string;
  lastUpdateEventId: string;

  content: any[];
  updateAction: string;

  allGroupData: any[];
  allSiteData: SiteUnit[];

  // for push data to component UI
  private triggerEventSubject: Subject<BroadcastEvent>;
  private eventBus: Subscription;
  getGroupData(siteId?: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.dataService
        .getGroup(siteId)
        .then(groupData => {
          this.allGroupData = groupData;
          resolve(groupData);
        })
        .catch(error => {
          reject(error);
          console.log('handleError:' + JSON.stringify(error));
        });
    });
  }

  getAllSiteData(): Promise<SiteUnit[]> {
    return new Promise((resolve, reject) => {
      this.dataService
        .getSite()
        .then(siteData => {
          this.allSiteData = siteData;
          resolve(siteData);
        })
        .catch(error => {
          reject(error);
        });
    });
  }

  getSiteName(siteId: string): string {
    let siteName = '';
    const index: number = _.findIndex(this.allSiteData, siteData => {
      return siteData.site_id === siteId;
    });

    if (index !== -1) {
      if (this.allSiteData[index].site_name !== undefined) {
        siteName = this.allSiteData[index].site_name;
      }
    }
    return siteName;
  }

  getEventData(
    siteId: string,
    eventNum: number,
    offset: number,
    groupID?: number,
    severity?: Severity,
    source?: number,
    startTime?: string,
    endTime?: string,
    sourceIP?: string,
    acked?: boolean,
    securitySening?: SecurityEvents
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      this.dataService
        .getEvent(
          siteId,
          eventNum,
          offset,
          sourceIP,
          groupID,
          severity,
          acked,
          startTime,
          endTime,
          source,
          securitySening
        )
        .then(allEventData => {
          resolve(allEventData);
        })
        .catch(error => {
          reject(error);
        });
    });
  }

  ackEvent(siteId: string, ackedEventId: number[]): Promise<any> {
    return new Promise((resolve, reject) => {
      this.dataService
        .ackEvent(siteId, ackedEventId)
        .then(ackResult => {
          resolve(ackResult);
        })
        .catch(error => {
          reject(error);
          console.log('handleError:' + JSON.stringify(error));
        });
    });
  }

  ackAllEvent(siteId: string, category?: number): Promise<any> {
    return new Promise((resolve, reject) => {
      this.dataService
        .ackAllEvent(siteId, category)
        .then(ackResult => {
          resolve(ackResult);
        })
        .catch(error => {
          reject(error);
          console.log('handleError:' + JSON.stringify(error));
        });
    });
  }

  constructor(
    private dataRepository: DataRepositoryService,
    private eventbus: EventBusService,
    private dataService: DataService,
    private translateService: TranslateService
  ) {
    this.triggerEventSubject = new Subject<BroadcastEvent>();
  }

  updateEventData(): Subject<any> {
    return this.registerEventBus();
  }

  getEventSource(sourceType: any): string {
    const type = Number(sourceType);
    let eventSource = '';

    switch (type) {
      case EventDetectorType.MXVIEW:
        eventSource = this.translateService.instant('EVENT.source.mxview');
        break;
      case EventDetectorType.TRAP:
        eventSource = this.translateService.instant('EVENT.source.trap');
        break;
      case EventDetectorType.SECURITYSENSING:
        eventSource = this.translateService.instant('EVENT.source.security_sensing');
        break;
      default:
        eventSource = this.translateService.instant('EVENT.source.mxview');
        break;
    }
    return eventSource;
  }
  // build Event Description
  assignEventDescription(
    eventType: number,
    value: string,
    port: string,
    user: string,
    trapDetail: string,
    trapOID: string,
    threshold: string,
    eventName?: string,
    eventDescription?: string,
    thresholdType?: string,
    display?: string,
    ip?: string,
    cliname?: string,
    result?: string,
    time?: string,
    status?: string,
    last?: number,
    conflicts?: string,
    source_ip?: string
  ): string {
    let eventDesc = '';
    let jsonObj: any;
    const isLicenseManagedByCentral = localStorage.getItem('isLicenseManagedByCentral') === '1' ? true : false;

    switch (eventType) {
      case EventType.portLinkUpEvent:
        eventDesc = this.translateService.instant('EVENT.event_description.port_linkup_event', { portindex: port });
        break;
      case EventType.portLinkDownEvent:
        eventDesc = this.translateService.instant('EVENT.event_description.port_linkdown_event', { portindex: port });
        break;
      case EventType.portLinkDownRecovery:
        eventDesc = this.translateService.instant('EVENT.event_description.port_linkdown_recovery', {
          portindex: port
        });
        break;
      case EventType.portLinkUpRecovery:
        eventDesc = this.translateService.instant('EVENT.event_description.port_linkup_recovery', { portindex: port });
        break;
      case EventType.icmpUnreachablue:
        eventDesc = this.translateService.instant('EVENT.event_description.icmp_unreachable');
        break;
      case EventType.icmpReachablue:
        eventDesc = this.translateService.instant('EVENT.event_description.icmp_reachable');
        break;
      case EventType.vpnLinkDown:
        eventDesc = this.translateService.instant('EVENT.event_description.vpn_linkdown', {
          param: value,
          portindex: port
        });
        break;
      case EventType.vpnLinkDownRecovery:
        eventDesc = this.translateService.instant('EVENT.event_description.vpn_link_recovery', {
          param: value,
          portindex: port
        });
        break;
      case EventType.vpnLinkUp:
        eventDesc = this.translateService.instant('EVENT.event_description.vpn_linkup', {
          param: value,
          portindex: port
        });
        break;
      case EventType.fiberWarning:
        const fiberEventType = Number(value);
        const portIndex = Number(port);

        let fiberEventTypeLabel = '';
        let portLabel = '';

        switch (fiberEventType) {
          case fiberStatus.fiberTXLowPowerWarning:
            fiberEventTypeLabel = 'txLowPower';
            break;
          case fiberStatus.fiberRXLowPowerWarning:
            fiberEventTypeLabel = 'rxLowPower';
            break;
          case fiberStatus.fiberTXHightPowerWarning:
            fiberEventTypeLabel = 'rxLowPower';
            break;
          case fiberStatus.fiberTemppertureWarning:
            fiberEventTypeLabel = 'temperature';
            break;
          default:
        }
        switch (portIndex) {
          case prpHSRPort.portA:
            portLabel = 'Port A';
            break;
          case prpHSRPort.portB:
            portLabel = 'Port B';
            break;
          case prpHSRPort.interLink:
            portLabel = 'Port INT';
            break;
        }
        eventDesc = this.translateService.instant('EVENT.event_description.fiber_warning', {
          portIndex: portLabel,
          warningType: fiberEventTypeLabel
        });
        break;
      case EventType.icmpPacketlossOverThreshold:
        eventDesc = this.translateService.instant('EVENT.event_description.icmp_packet_loss_over_threhold', {
          param1: value,
          param2: threshold
        });
        break;
      case EventType.icmpPacketlossOverThresholdRecovery:
        eventDesc = this.translateService.instant('EVENT.event_description.icmp_packet_loss_over_threhold_recovery', {
          param1: value,
          param2: threshold
        });
        break;
      case EventType.icmpPacketlossOverCritialThreshold:
        eventDesc = this.translateService.instant('EVENT.event_description.icmp_packet_loss_over_critical_threhold', {
          param1: value,
          param2: threshold
        });
        break;
      case EventType.icmpPacketlossOverCritialThresholdRecovery:
        eventDesc = this.translateService.instant(
          'EVENT.event_description.icmp_packet_loss_over_critical_threhold_recovery',
          { param1: value, param2: threshold }
        );
        break;
      case EventType.devicePowerOffToOn:
        eventDesc = this.translateService.instant('EVENT.event_description.device_power_down_off_to_on', {
          param: value
        });
        break;
      case EventType.devicePowerOnToOff:
        eventDesc = this.translateService.instant('EVENT.event_description.device_power_on_to_off', { param: value });
        break;
      case EventType.devicePowerUp:
        eventDesc = this.translateService.instant('EVENT.event_description.device_power_on', { param: value });
        break;
      case EventType.turboringTopologyChanged:
        if (+value === 3 || +value === 4) {
          eventDesc = this.translateService.instant('EVENT.event_description.turbochain_topology_change');
        } else {
          eventDesc = this.translateService.instant('EVENT.event_description.redundancy_topology_change');
        }
        break;
      case EventType.turboringCouplingportChanged:
        eventDesc = this.translateService.instant('EVENT.event_description.turboring_coupling_port_change');
        break;
      case EventType.turboringMasterChanged:
        eventDesc = this.translateService.instant('EVENT.event_description.turboring_master_change');
        break;
      case EventType.deviceConfigurationChanged:
        eventDesc = this.translateService.instant('EVENT.event_description.device_configuration_change');
        break;
      case EventType.deviceFirmwareUpdated:
        eventDesc = this.translateService.instant('EVENT.event_description.device_firmware_upgrade');
        break;
      case EventType.deviceTrapReceived:
        eventDesc = trapOID;
        break;
      case EventType.deviceInformReceived:
        eventDesc = this.translateService.instant('EVENT.event_description.device_infom_receive');
        break;
      case EventType.rateLimitedOn:
        eventDesc = this.translateService.instant('EVENT.event_description.rateLimit_on', {
          param: value,
          portindex: port
        });
        break;
      case EventType.lldpChanged:
        eventDesc = this.translateService.instant('EVENT.event_description.lldp_change');
        break;
      case EventType.deviceSNMPreachable:
        eventDesc = this.translateService.instant('EVENT.event_description.device_snmp_reachable');
        break;
      case EventType.deviceSNMPunreachable:
        eventDesc = this.translateService.instant('EVENT.event_description.device_snmp_unreachable');
        break;
      case EventType.coldStart:
        eventDesc = this.translateService.instant('EVENT.event_description.cold_start');
        break;
      case EventType.warmStart:
        eventDesc = this.translateService.instant('EVENT.event_description.warn_start');
        break;
      case EventType.authFail:
        eventDesc = this.translateService.instant('EVENT.event_description.auth_fail');
        break;
      case EventType.dIOn:
        eventDesc = this.translateService.instant('EVENT.event_description.di_on');
        break;
      case EventType.dIOff:
        eventDesc = this.translateService.instant('EVENT.event_description.di_off');
        break;
      case EventType.iwClientJoined:
        eventDesc = this.translateService.instant('EVENT.event_description.iw_client_joined', { param: trapDetail });
        break;
      case EventType.iwClientLeft:
        eventDesc = this.translateService.instant('EVENT.event_description.iw_client_left', { param: trapDetail });
        break;
      case EventType.eventMxviewJobStart:
        eventDesc = this.translateService.instant('EVENT.event_description.mxview_job_start', {
          jobname: eventDescription
        });
        break;
      case EventType.eventMxviewJobDone:
        eventDesc = this.translateService.instant('EVENT.event_description.mxview_job_done', {
          jobname: eventDescription
        });
        break;
      case EventType.eventMxviewSMSSucess:
        eventDesc = this.translateService.instant('EVENT.event_description.mxview_sms_success');
        break;
      case EventType.eventMxviewSMSFAIL:
        eventDesc = this.translateService.instant('EVENT.event_description.mxview_sms_fail');
        break;
      case EventType.eventMxviewAutoTopologyFinished:
        eventDesc = this.translateService.instant('EVENT.event_description.mxview_autopology_finish');
        break;
      case EventType.eventMxviewAutoTopologyStarted:
        eventDesc = this.translateService.instant('EVENT.event_description.mxview_autopology_start');
        break;
      case EventType.eventMxviewServerStart:
        eventDesc = this.translateService.instant('EVENT.event_description.mxview_server_start');
        break;
      case EventType.eventMxviewDbBackupEvent:
        if (Number(value) === databaseBackupStatus.success) {
          eventDesc = this.translateService.instant('EVENT.event_description.mxview_db_backup_sucess', {
            param1: port,
            param2: threshold
          });
        } else {
          eventDesc = this.translateService.instant('EVENT.event_description.mxview_db_backup_fail');
        }
        break;
      case EventType.eventNetworkLatency:
        eventDesc = this.translateService.instant('EVENT.event_description.network_latency');
        break;
      case EventType.eventMxviewUserLoginSuccessful:
        eventDesc = this.translateService.instant('EVENT.event_description.mxview_user_login_sucess', { param: user });
        break;
      case EventType.eventMxviewUserLoginFailed:
        eventDesc = this.translateService.instant('EVENT.event_description.mxview_user_login_fail');
        break;
      case EventType.eventMxviewUserBadLogin:
        eventDesc = this.translateService.instant('EVENT.event_description.mxview_user_login_fail', { param: user });
        break;
      case EventType.eventMxviewUserLogout:
        eventDesc = this.translateService.instant('EVENT.event_description.mxview_user_logout', { param: user });
        break;
      case EventType.eventMXviewUserLockout:
        eventDesc = this.translateService.instant('EVENT.event_description.mxview_user_lockout', { param: user });
        break;
      case EventType.powerTypeDanger:
        eventDesc = this.translateService.instant('EVENT.event_description.power_type_danger', { param: value });
        break;
      case EventType.powerTypeDangerRecover:
        eventDesc = this.translateService.instant('EVENT.event_description.power_danger_recovery', { param: value });
        break;
      case EventType.customEventTriggered:
        if (eventName !== undefined && eventDescription !== undefined) {
          if (
            threshold &&
            typeof threshold === 'string' &&
            threshold.substring(0, 1) === '{' &&
            threshold.substring(threshold.length - 1, threshold.length) === '}'
          ) {
            threshold = threshold.substring(1, threshold.length - 1);
          }
          if (
            value &&
            typeof value === 'string' &&
            value.substring(0, 1) === '{' &&
            value.substring(value.length - 1, value.length) === '}'
          ) {
            value = value.substring(1, value.length - 1);
          }
          eventDesc = this.translateService.instant('EVENT.event_description.custom_event_detail', {
            param1: eventName,
            param2: threshold,
            param3: value,
            param4: eventDescription
          });
        } else {
          eventDesc = this.translateService.instant('EVENT.event_description.custom_event_trigger');
        }
        break;
      case EventType.customEventRecovered:
        if (
          threshold &&
          typeof threshold === 'string' &&
          threshold.substring(0, 1) === '{' &&
          threshold.substring(threshold.length - 1, threshold.length) === '}'
        ) {
          threshold = threshold.substring(1, threshold.length - 1);
        }
        if (
          value &&
          typeof value === 'string' &&
          value.substring(0, 1) === '{' &&
          value.substring(value.length - 1, value.length) === '}'
        ) {
          value = value.substring(1, value.length - 1);
        }
        if (eventName !== undefined && eventDescription !== undefined) {
          eventDesc = this.translateService.instant('EVENT.event_description.custom_event_recovery_detail', {
            param1: eventName,
            param2: threshold,
            param3: value,
            param4: eventDescription
          });
        } else {
          eventDesc = this.translateService.instant('EVENT.event_description.custom_event_recovery');
        }
        break;
      case EventType.availabilityDown:
        eventDesc = this.translateService.instant('EVENT.event_description.availability_down');
        break;
      case EventType.availabilityDownRecovery:
        eventDesc = this.translateService.instant('EVENT.event_description.availability_down_recovery');
        break;
      case EventType.trunkPortLinkUp:
        eventDesc = this.translateService.instant('EVENT.event_description.trunk_port_link_up', {
          portindex: port,
          param: value
        });
        break;
      case EventType.trunkPortLinkDown:
        eventDesc = this.translateService.instant('EVENT.event_description.trunk_port_link_down', {
          portindex: port,
          param: value
        });
        break;
      case EventType.trunkPortLinkDownRecovery:
      case EventType.trunkPortLinkUpRecovery:
        eventDesc = this.translateService.instant('EVENT.event_description.trunk_port_link_down_recovery', {
          portindex: port,
          param: value
        });
        break;
      case EventType.licenseNotEnough:
        eventDesc = this.translateService.instant('EVENT.event_description.license_not_enough', {
          portindex: port,
          param: value
        });
        break;
      case EventType.diskSpaceNotEnough:
        eventDesc = this.translateService.instant('EVENT.event_description.disk_space_not_enough', {
          portindex: port,
          param: value
        });
        break;
      case EventType.diskSpaceLessThanMinimum:
        eventDesc = this.translateService.instant('EVENT.event_description.disk_space_not_enough', {
          portindex: port,
          param: value
        });
        break;
      case EventType.allEventsCleared:
        eventDesc = this.translateService.instant('EVENT.event_description.all_event_clear', {
          portindex: port,
          param: value
        });
        break;
      case EventType.ieiABC02Warning:
        switch (Number(value)) {
          case usbEventType.usbNoEnoughSpace:
            eventDesc = this.translateService.instant('EVENT.event_description.abc02_warning', {
              param: this.translateService.instant('EVENT.event_description.abc_space_warning')
            });
            break;
          case usbEventType.usbUnAuthDevice:
            eventDesc = this.translateService.instant('EVENT.event_description.abc02_warning', {
              param: this.translateService.instant('EVENT.event_description.abc_unauthorized_warning')
            });
            break;
          case usbEventType.usbExportConfigFail:
            eventDesc = this.translateService.instant('EVENT.event_description.abc02_warning', {
              param: this.translateService.instant('EVENT.event_description.abc_config_warning')
            });
            break;
          case usbEventType.usbExportLogFail:
            eventDesc = this.translateService.instant('EVENT.event_description.abc02_warning', {
              param: this.translateService.instant('EVENT.event_description.abc_log_warning')
            });
            break;
          case usbEventType.usbAutoImportFail:
            eventDesc = this.translateService.instant('EVENT.event_description.abc02_warning', {
              param: this.translateService.instant('EVENT.event_description.abc_auto_import_warning')
            });
            break;
          case usbEventType.usbIsAttached:
            eventDesc = this.translateService.instant('EVENT.event_description.abc02_warning', {
              param: this.translateService.instant('EVENT.event_description.abc_attache_warning')
            });
            break;
          case usbEventType.usbIsDetached:
            eventDesc = this.translateService.instant('EVENT.event_description.abc02_warning', {
              param: this.translateService.instant('EVENT.event_description.abc_detache_warning')
            });
            break;
          default:
            eventDesc = this.translateService.instant('EVENT.event_description.abc02_warning', {
              param: this.translateService.instant('EVENT.event_description.abc_unknow_warning')
            });
        }
        break;
      case EventType.ieiTurboringMasterMismatch:
        switch (Number(value)) {
          case turboRingMasterStatus.turboRingMasterMatch:
            eventDesc = this.translateService.instant('EVENT.event_description.turbo_ring_master_match', {
              param: this.translateService.instant('EVENT.event_description.abc_unknow_warning')
            });
            break;
          case turboRingMasterStatus.turboRingMasterMismatch:
            eventDesc = this.translateService.instant('EVENT.event_description.turbo_ring_master_mismatch', {
              param: this.translateService.instant('EVENT.event_description.abc_unknow_warning')
            });
            break;
          default:
            eventDesc = this.translateService.instant('EVENT.event_description.turbo_ring_master_unknow', {
              param: this.translateService.instant('EVENT.event_description.abc_unknow_warning')
            });
            break;
        }
        break;
      case EventType.ieiUserLoginSuccess:
        eventDesc = this.translateService.instant('EVENT.event_description.user_login_success', { username: trapOID });
        break;
      case EventType.ieiUserLoginFail:
        eventDesc = this.translateService.instant('EVENT.event_description.user_login_fail');
        break;
      case EventType.ieiLoggingCapacity:
        eventDesc = this.translateService.instant('EVENT.event_description.logging_capacity');
        break;
      case EventType.ieiUserInfoChg:
        eventDesc = this.translateService.instant('EVENT.event_description.event_user_info_change', {
          trapoid: trapOID
        });
        break;
      case EventType.ieiConfigImport:
        eventDesc = this.translateService.instant('EVENT.event_description.event_config_import');
        break;
      case EventType.ieiPHRFunctionFail:
        eventDesc = this.translateService.instant('EVENT.event_description.event_prp_function_fail');
        break;
      case EventType.portTrafficOverLoad:
        eventDesc = this.translateService.instant('EVENT.event_description.port_traffic_overload', {
          portIndex: port,
          percent: value
        });
        break;
      case EventType.portLoopDetected:
        eventDesc = this.translateService.instant('EVENT.event_description.port_loop_detected');
        break;
      case EventType.inputBandwidthDownToThreshold:
        if (port !== null) {
          const cValue = parseFloat(value) / 10000.0;
          const thresholdValue = parseFloat(threshold) / 10000.0;
          eventDesc = this.translateService.instant(
            'EVENT.event_description.input_bandwidth_under_threshold_with_port',
            { portIndex: port, currentValue: cValue, threshold: thresholdValue }
          );
        } else {
          eventDesc = this.translateService.instant('EVENT.event_description.input_bandwidth_under_threshold');
        }
        break;
      case EventType.inputBandwidthDownToThresholdRecovery:
        const recoverInputValue = parseFloat(value) / 10000.0;
        const recoveryInputThreshold = parseFloat(threshold) / 10000.0;
        if (recoveryInputThreshold === 0) {
          eventDesc = this.translateService.instant(
            'EVENT.event_description.input_bandwidth_under_threshold_disabled',
            { portIndex: port }
          );
        } else {
          eventDesc = this.translateService.instant(
            'EVENT.event_description.input_bandwidth_under_threshold_recovery',
            { portIndex: port, currentValue: recoverInputValue, threshold: recoveryInputThreshold }
          );
        }
        break;
      case EventType.inputBandwidthOverThreshold:
        if (port !== null) {
          const overInputValue = parseFloat(value) / 10000.0;
          const overInputThreshold = parseFloat(threshold) / 10000.0;
          eventDesc = this.translateService.instant(
            'EVENT.event_description.input_bandwidth_over_threshold_with_port',
            { portIndex: port, currentValue: overInputValue, threshold: overInputThreshold }
          );
        } else {
          eventDesc = this.translateService.instant('EVENT.event_description.input_bandwidth_over_threshold');
        }
        break;
      case EventType.inputBandwidthOverThresholdRecovery:
        const recoverOverInputValue = parseFloat(value) / 10000.0;
        const recoveryOverInputThreshold = parseFloat(threshold) / 10000.0;
        if (recoveryOverInputThreshold === 0) {
          eventDesc = this.translateService.instant('EVENT.event_description.input_bandwidth_over_threshold_disabled', {
            portIndex: port
          });
        } else {
          eventDesc = this.translateService.instant('EVENT.event_description.input_bandwidth_over_threshold_recovery', {
            portIndex: port,
            currentValue: recoverOverInputValue,
            threshold: recoveryOverInputThreshold
          });
        }
        break;
      case EventType.inputPacketErrorOverThreshold:
        if (port !== null) {
          const errorValue = parseFloat(value) / 10000.0;
          const errThreshold = parseFloat(threshold) / 10000.0;
          eventDesc = this.translateService.instant(
            'EVENT.event_description.input_packet_error_over_threshold_with_port',
            { portIndex: port, currentValue: errorValue, threshold: errThreshold }
          );
        } else {
          eventDesc = this.translateService.instant('EVENT.event_description.input_packet_error_over_threshold');
        }
        break;
      case EventType.inputPacketErrorOverThresholdRecovery:
        const errorRecoveryValue = parseFloat(value) / 10000.0;
        const errRecoveryThreshold = parseFloat(threshold) / 10000.0;
        if (errRecoveryThreshold === 0) {
          eventDesc = this.translateService.instant(
            'EVENT.event_description.input_packet_error_over_threshold_disabled',
            { portIndex: port }
          );
        } else {
          eventDesc = this.translateService.instant(
            'EVENT.event_description.input_packet_error_over_threshold_recovery',
            { portIndex: port, currentValue: errorRecoveryValue, threshold: errRecoveryThreshold }
          );
        }
        break;
      case EventType.outputBandwidthDownToThreshold:
        if (port !== null) {
          const cUnderValue = parseFloat(value) / 10000.0;
          const underThreshold = parseFloat(threshold) / 10000.0;
          eventDesc = this.translateService.instant(
            'EVENT.event_description.output_bandwidth_under_threshold_with_port',
            { portIndex: port, currentValue: cUnderValue, threshold: underThreshold }
          );
        } else {
          eventDesc = this.translateService.instant('EVENT.event_description.output_bandwidth_under_threshold');
        }
        break;
      case EventType.outputBandwidthDownToThresholdRecovery:
        const recoveryOutputValue = parseFloat(value) / 10000.0;
        const recoveryOutputThreshold = parseFloat(threshold) / 10000.0;
        if (recoveryOutputThreshold === 0) {
          eventDesc = this.translateService.instant(
            'EVENT.event_description.output_bandwidth_under_threshold_disabled',
            { portIndex: port }
          );
        } else {
          eventDesc = this.translateService.instant(
            'EVENT.event_description.output_bandwidth_under_threshold_recovery',
            { portIndex: port, currentValue: recoveryOutputValue, threshold: recoveryOutputThreshold }
          );
        }
        break;
      case EventType.outputBandwidthOverThreshold:
        if (port !== null) {
          const cValue = parseFloat(value) / 10000.0;
          const thresholdValue = parseFloat(threshold) / 10000.0;
          eventDesc = this.translateService.instant(
            'EVENT.event_description.output_bandwidth_over_threshold_with_port',
            { portIndex: port, currentValue: cValue, threshold: thresholdValue }
          );
        } else {
          eventDesc = this.translateService.instant('EVENT.event_description.output_bandwidth_over_threshold');
        }
        break;
      case EventType.outputBandwidthOverThresholdRecovery:
        const outputValue = parseFloat(value) / 10000.0;
        const outputThreshold = parseFloat(threshold) / 10000.0;
        if (outputThreshold === 0) {
          eventDesc = this.translateService.instant(
            'EVENT.event_description.output_bandwidth_over_threshold_disabled',
            { portIndex: port }
          );
        } else {
          eventDesc = this.translateService.instant(
            'EVENT.event_description.output_bandwidth_over_threshold_recovery',
            { portIndex: port, currentValue: outputValue, threshold: outputThreshold }
          );
        }
        break;
      case EventType.outputPacketErrorOverThreshold:
        if (port !== null) {
          const errorValue = parseFloat(value) / 10000.0;
          const errThreshold = parseFloat(threshold) / 10000.0;
          eventDesc = this.translateService.instant(
            'EVENT.event_description.output_packet_error_over_threshold_with_port',
            { portIndex: port, currentValue: errorValue, threshold: errThreshold }
          );
        } else {
          eventDesc = this.translateService.instant('EVENT.event_description.output_packet_error_over_threshold');
        }
        break;
      case EventType.outputPacketErrorOverThresholdRecovery:
        const errRecoveryValue = parseFloat(value) / 10000.0;
        const errThresholdValue = parseFloat(threshold) / 10000.0;
        if (errThresholdValue === 0) {
          eventDesc = this.translateService.instant(
            'EVENT.event_description.output_packet_error_over_threshold_disabled',
            { portIndex: port }
          );
        } else {
          eventDesc = this.translateService.instant(
            'EVENT.event_description.output_packet_error_over_threshold_recovery',
            { portIndex: port, currentValue: errRecoveryValue, threshold: errThresholdValue }
          );
        }
        break;
      case EventType.ieiRemoteAuthSuccess:
        if (+value === authServerType.tacacsServer) {
          eventDesc = this.translateService.instant('EVENT.event_description.login_tacas_success');
        } else if (+value === authServerType.radiusServer) {
          eventDesc = this.translateService.instant('EVENT.event_description.login_radius_success');
        }
        break;
      case EventType.ieiRemoteAuthFail:
        if (+value === authServerType.tacacsServer) {
          eventDesc = this.translateService.instant('EVENT.event_description.login_tacas_fail');
        } else if (+value === authServerType.radiusServer) {
          eventDesc = this.translateService.instant('EVENT.event_description.login_radius_fail');
        }
        break;
      case EventType.edrDDOSAttack:
        eventDesc = this.translateService.instant('EVENT.event_description.event_ddos_attack');
        break;
      case EventType.edrEventDDOSAttackRecovery:
        eventDesc = this.translateService.instant('EVENT.event_description.event_ddos_attack_recovery');
        break;
      case EventType.edrEventFirewallAttack:
        eventDesc = this.translateService.instant('EVENT.event_description.event_firewall_attack');
        break;
      case EventType.edrEventFirewallAttackRecovery:
        eventDesc = this.translateService.instant('EVENT.event_description.event_firewall_attack_recovery');
        break;
      case EventType.edrEventTrustedAccessAttack:
        eventDesc = this.translateService.instant('EVENT.event_description.event_trusted_access_attack');
        break;
      case EventType.edrEventTrustedAccessAttackRecovery:
        eventDesc = this.translateService.instant('EVENT.event_description.event_trusted_access_attack_recovery');
        break;
      case EventType.edrEventTooManyLoginFailure:
        eventDesc = this.translateService.instant('EVENT.event_description.event_too_many_login_failure');
        break;
      case EventType.edrEventTooManyLoginFailureRecovery:
        eventDesc = this.translateService.instant('EVENT.event_description.event_too_many_login_failure_recovery');
        break;
      case EventType.eventPDOverCurrent:
        eventDesc = this.translateService.instant('EVENT.event_description.event_pd_over_current', { portIndex: port });
        break;
      case EventType.eventPDCheck_Fail:
        eventDesc = this.translateService.instant('EVENT.event_description.event_pd_check_fail');
        break;
      case EventType.eventPDPowerOn:
        eventDesc = this.translateService.instant('EVENT.event_description.event_pd_power_on', { portIndex: port });
        break;
      case EventType.eventPDPowerOff:
        eventDesc = this.translateService.instant('EVENT.event_description.event_pd_power_off', { portIndex: port });
        break;
      case EventType.ieiEDRFirewallPolicy:
        eventDesc = this.translateService.instant('EVENT.event_description.firewall_policy_violation', {
          trapoid: trapOID
        });
        break;
      case EventType.ieiFiberWarning:
        eventDesc = this.translateService.instant('EVENT.event_description.iei_fiber_warning');
        break;
      case EventType.ieiMACStickyViolation:
        eventDesc = this.translateService.instant('EVENT.event_description.mac_sticky_violation');
        break;
      case EventType.eventNportSyslogOverThreshold:
        eventDesc = this.translateService.instant('EVENT.event_description.nport_syslog_over_threshold');
        break;
      case EventType.eventExceedThreshold:
        eventDesc = this.translateService.instant('EVENT.event_description.nport_syslog_over_threshold');
        break;
      case EventType.eventPSEFetBad:
        eventDesc = this.translateService.instant('EVENT.event_description.pse_fet_bad');
        break;
      case EventType.eventPSEOverTemp:
        eventDesc = this.translateService.instant('EVENT.event_description.pse_over_temp');
        break;
      case EventType.eventPSEVEEUVLO:
        eventDesc = this.translateService.instant('EVENT.event_description.pse_veeuvlo');
        break;
      case EventType.eventLinuxUserLoginSuccess:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_user_login_success', {
          username: user,
          interface: trapDetail
        });
        break;
      case EventType.eventLinuxUserLoginLockout:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_user_login_lockout', {
          username: user,
          param: value
        });
        break;
      case EventType.eventLinuxAccountSettingChange:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_account_setting_change', {
          username: user
        });
        break;
      case EventType.eventLinuxSSLCerChange:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_ssl_cer_change');
        break;
      case EventType.eventLinuxPasswordChange:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_password_change', {
          username: user
        });
        break;
      case EventType.eventLinuxConfigChange:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_config_change', {
          modules: trapDetail,
          username: user
        });
        break;
      case EventType.eventLinuxConfigImport:
        let importResult = '';
        if (+value === 1) {
          importResult = this.translateService.instant('EVENT.event_description.event_linux_config_import_succeed', {});
        } else if (+value === 2) {
          importResult = this.translateService.instant('EVENT.event_description.event_linux_config_import_failed', {});
        }
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_config_import', {
          successOrFail: importResult,
          username: user
        });
        break;
      case EventType.eventLinuxLogCapacityThreshold:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_topology_change', {
          param: value
        });
        break;
      case EventType.eventLinuxPowerON:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_power_on', { index: value });
        break;
      case EventType.eventLinuxPowerOFF:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_power_off', { index: value });
        break;
      case EventType.eventLinuxDION:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_di_on', { index: value });
        break;
      case EventType.eventLinuxDIOFF:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_di_off', { index: value });
        break;
      case EventType.eventLinuxPortShutdownByRatelimit:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_port_shutdown_by_ratelimit', {
          portIndex: port
        });
        break;
      case EventType.eventLinuxPortRecoveryByRatelimit:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_port_recovery_by_ratelimit', {
          portIndex: port
        });
        break;
      case EventType.eventLinuxPortShutdownBySecurity:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_port_shutdown_by_security', {
          portIndex: port
        });
        break;
      case EventType.eventLinuxTopologyChange:
        if (trapDetail === undefined) {
          eventDesc = this.translateService.instant('EVENT.event_description.event_linux_topology_change');
        } else {
          eventDesc = this.translateService.instant('EVENT.event_description.event_linux_topology_change_by_type', {
            topologyType: trapDetail
          });
        }
        break;
      case EventType.eventLinuxCOUPLINGChange:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_coupling_change');
        break;
      case EventType.eventLinuxMasterChange:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_master_change', {
          index: value
        });
        break;
      case EventType.eventLinuxMasterMismatch:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_master_mismatch', {
          index: value
        });
        break;
      case EventType.eventIEEERSTPTopologyChange:
        eventDesc = this.translateService.instant('EVENT.event_description.event_ieee_rstp_topology_change');
        break;
      case EventType.eventIEEERSTPRootChange:
        eventDesc = this.translateService.instant('EVENT.event_description.event_ieee_rstp_root_change');
        break;
      case EventType.eventLinuxRSTPMigration:
        jsonObj = JSON.parse(trapDetail);
        const originTopologyObj = jsonObj.originTopology;
        const changeTopologyObj = jsonObj.changeTopology;
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_rstp_migration', {
          portIndex: port,
          originTopology: originTopologyObj,
          changeTopology: changeTopologyObj
        });
        break;
      case EventType.eventLinuxRSTPInvalidBPDU:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_rstp_invalid_bpdu', {
          portIndex: port,
          type: trapDetail,
          value
        });
        break;
      case EventType.eventLinuxRSTPNewPortRole:
        jsonObj = JSON.parse(trapDetail);
        const originalRoleObj = jsonObj.oldrole;
        const newRoleObj = jsonObj.newrole;
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_rstp_new_port_role', {
          portIndex: port,
          originalRole: originalRoleObj,
          newRole: newRoleObj
        });
        break;
      case EventType.eventLinuxRedundantPortHealthCheck:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_redundant_port_health_check', {
          portIndex: port
        });
        break;
      case EventType.eventLinuxDUALHOMINGChange:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_dual_homing_change');
        break;
      case EventType.eventLinuxDOTlXAuthFAIL:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_dotlx_auth_fail', {
          portIndex: port,
          reason: trapDetail
        });
        break;
      case EventType.eventIEEELLDPTableChange:
        eventDesc = this.translateService.instant('EVENT.event_description.event_ieee_lldp_table_change', {
          detail: trapDetail
        });
        break;
      case EventType.eventLinuxPDPowerON:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_pd_power_on', {
          portIndex: port
        });
        break;
      case EventType.eventLinuxPDPowerOFF:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_pd_power_off', {
          portIndex: port
        });
        break;
      case EventType.eventLinuxLowInputVoltage:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_low_input_voltage');
        break;
      case EventType.eventLinuxPDOverCurrent:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_pd_over_current', {
          portIndex: port
        });
        break;
      case EventType.eventLinuxPDNoResponse:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_pd_no_response', {
          portIndex: port
        });
        break;
      case EventType.eventLinuxOverPowerBudgetLimit:
        const v = value;
        const t = threshold;
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_over_power_budget_limit', {
          value: v,
          threshold: t
        });
        break;
      case EventType.eventLinuxPowerDetectionFail:
        jsonObj = JSON.parse(trapDetail);
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_power_detection_fail', {
          portIndex: port,
          devicetype: this.getDeviceTypeByJsonStr(jsonObj.devicetype),
          suggestion: this.getConfigSuggestionByJsonStr(jsonObj.suggestion)
        });
        break;
      case EventType.eventV3TrapParseError:
        eventDesc = this.translateService.instant('EVENT.event_description.event_v3_trap_parse_error');
        break;
      case EventType.eventV3TrapParseErrorRecovery:
        eventDesc = this.translateService.instant('EVENT.event_description.event_v3_trap_parse_error_recovery');
        break;
      case EventType.eventLinuxRMONTrapIsRaising:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_RMON_trap_is_raising');
        break;
      case EventType.eventLinuxRMONTrapIsFalling:
        eventDesc = this.translateService.instant('EVENT.event_description.event_linux_RMON_trap_is_falling');
        break;
      case EventType.eventSfpTxBelow:
        eventDesc = this.translateService.instant('EVENT.event_description.event_sfp_tx_below', {
          portIndex: port,
          currentdB: (parseFloat(value) / 10).toFixed(2),
          thresholddB: (parseFloat(threshold) / 10).toFixed(2)
        });
        break;
      case EventType.eventSfpTxBelowRecovery:
        eventDesc = this.translateService.instant('EVENT.event_description.event_sfp_tx_below_recovery', {
          portIndex: port,
          recoverydB: (parseFloat(value) / 10).toFixed(2)
        });
        break;
      case EventType.eventSfpRxBelow:
        eventDesc = this.translateService.instant('EVENT.event_description.event_sfp_rx_below', {
          portIndex: port,
          currentdB: (parseFloat(value) / 10).toFixed(2),
          thresholddB: (parseFloat(threshold) / 10).toFixed(2)
        });
        break;
      case EventType.eventSfpRxBelowRecovery:
        eventDesc = this.translateService.instant('EVENT.event_description.event_sfp_rx_below_recovery', {
          portIndex: port,
          recoverydB: (parseFloat(value) / 10).toFixed(2)
        });
        break;
      case EventType.eventSfpTempOver:
        eventDesc = this.translateService.instant('EVENT.event_description.event_sfp_temp_over', {
          portIndex: port,
          currentTemp: (parseFloat(value) / 10).toFixed(2),
          thresholdTemp: (parseFloat(threshold) / 10).toFixed(2)
        });
        break;
      case EventType.eventSfpTempOverRecovery:
        eventDesc = this.translateService.instant('EVENT.event_description.event_sfp_temp_over_recovery', {
          portIndex: port,
          recoveryTemp: (parseFloat(value) / 10).toFixed(2)
        });
        break;
      case EventType.eventSfpVoltageOver:
        eventDesc = this.translateService.instant('EVENT.event_description.event_sfp_voltage_over', {
          portIndex: port,
          currentVoltage: (parseFloat(value) / 10).toFixed(2),
          thresholdVoltage: (parseFloat(threshold) / 10).toFixed(2)
        });
        break;
      case EventType.eventSfpVoltageOverRecovery:
        eventDesc = this.translateService.instant('EVENT.event_description.event_sfp_voltage_over_recovery', {
          portIndex: port,
          recoveryVoltage: (parseFloat(value) / 10).toFixed(2)
        });
        break;
      case EventType.eventSfpVoltageBelow:
        eventDesc = this.translateService.instant('EVENT.event_description.event_sfp_voltage_below', {
          portIndex: port,
          currentVoltage: (parseFloat(value) / 10).toFixed(2),
          thresholdVoltage: (parseFloat(threshold) / 10).toFixed(2)
        });
        break;
      case EventType.eventSfpVoltageBelowRecovery:
        eventDesc = this.translateService.instant('EVENT.event_description.event_sfp_voltage_below_recovery', {
          portIndex: port,
          recoveryVoltage: (parseFloat(value) / 10).toFixed(2)
        });
        break;
      case EventType.gooseHealthy:
        eventDesc = this.translateService.instant('EVENT.event_description.goose_healthy', {
          display: display ? ': ' + display : ''
        });
        break;
      case EventType.usercodeRevoke:
        eventDesc = this.translateService.instant('EVENT.event_description.usercode_revoke');
        break;
      case EventType.eventTrackingStatusChanged:
        eventDesc = this.translateService.instant('EVENT.event_description.event_tracking_status_changed');
        break;
      case EventType.eventTrackingVrrpStatusChanged:
        eventDesc = this.translateService.instant('EVENT.event_description.event_tracking_vrrp_status_changed');
        break;
      case EventType.eventTrackingStaticRouteStatusChanged:
        eventDesc = this.translateService.instant('EVENT.event_description.event_tracking_static_route_status_changed');
        break;
      case EventType.eventTrackingPortEnabledStatus:
        eventDesc = this.translateService.instant('EVENT.event_description.event_tracking_port_enabled_status');
        break;
      case EventType.eventEpsIsOn:
        eventDesc = this.translateService.instant('EVENT.event_description.event_eps_is_on');
        break;
      case EventType.eventEpsIsOff:
        eventDesc = this.translateService.instant('EVENT.event_description.event_eps_is_off');
        break;
      case EventType.eventDyingGasp:
        eventDesc = this.translateService.instant('EVENT.event_description.event_dying_gasp');
        break;
      case EventType.gooseTimeout:
        eventDesc = this.translateService.instant('EVENT.event_description.goose_timeout_with_value', {
          display: ': ' + display
        });
        break;
      case EventType.gooseTampered:
        eventDesc = this.translateService.instant('EVENT.event_description.goose_tampered_with_value', {
          display: ': ' + display
        });
        break;
      case EventType.gooseResume:
        if (display) {
          eventDesc = this.translateService.instant('EVENT.event_description.goose_healthy_with_value', {
            display: display ? ': ' + display : ''
          });
        } else {
          eventDesc = this.translateService.instant('EVENT.event_description.goose_healthy');
        }
        break;
      case EventType.eventLogClearedTrapEventInfo:
        eventDesc = this.translateService.instant('EVENT.event_description.event_log_cleared_trap_event_info', {
          user: user,
          ip: display,
          interface: value
        });
        break;
      case EventType.poEOnInfo:
        eventDesc = this.translateService.instant('EVENT.event_description.poe_on_info');
        break;
      case EventType.poEOffInfo:
        eventDesc = this.translateService.instant('EVENT.event_description.poe_off_info');
        break;
      case EventType.networkLatency:
        eventDesc = this.translateService.instant('EVENT.event_description.network_latency');
        break;
      case EventType.outOfMemory:
        eventDesc = this.translateService.instant('EVENT.event_description.out_of_memory');
        break;
      case EventType.insufficientDiskSpace:
        eventDesc = this.translateService.instant('EVENT.event_description.insufficient_disk_space');
        break;
      case EventType.highCpuLoading:
        eventDesc = this.translateService.instant('EVENT.event_description.high_cpu_loading');
        break;
      case EventType.eventMXviewServerLicenseLimit:
        eventDesc = isLicenseManagedByCentral
          ? this.translateService.instant('EVENT.event_description.mxview_server_license_limit')
          : this.translateService.instant('EVENT.event_description.license_limitation_reached');
        break;
      case EventType.firmwareVersionRelease:
        eventDesc = this.translateService.instant('EVENT.event_description.firmware_version_release');
        break;
      case EventType.ptpGrandmasterChanged:
        eventDesc = this.translateService.instant('EVENT.event_description.ptp_grandmaster_changed');
        break;
      case EventType.ptpSyncStatusChanged:
        eventDesc = this.translateService.instant('EVENT.event_description.ptp_sync_status_changed');
        break;
      case EventType.fanModuleMalfunction:
        eventDesc = this.translateService.instant('EVENT.event_description.fan_module_malfunction');
        break;
      case EventType.powerModuleFanMalfunction:
        eventDesc = this.translateService.instant('EVENT.event_description.power_module_fan_malfunction');
        break;
      case EventType.systemTemperatureExceedsThreshold:
        eventDesc = this.translateService.instant('EVENT.event_description.system_temperature_exceeds_threshold');
        break;
      case EventType.thermalSensorComponentOverheatDetected:
        eventDesc = this.translateService.instant('EVENT.event_description.thermal_sensor_component_overheat_detected');
        break;
      case EventType.powerHasBeenCutDueToOverheating:
        eventDesc = this.translateService.instant('EVENT.event_description.power_has_been_cut_due_to_overheating');
        break;
      case EventType.overheatProtectionNowActiveForPowerModule:
        eventDesc = this.translateService.instant(
          'EVENT.event_description.overheat_protection_now_active_for_power_module',
          { param: value }
        );
        break;
      case EventType.vrrpMasterChanged:
        eventDesc = this.translateService.instant('EVENT.event_description.vrrp_master_changed');
        break;
      case EventType.firmwareUpgraded:
        eventDesc = this.translateService.instant('EVENT.event_description.firmware_upgraded');
        break;
      case EventType.mstpTopologyChanged:
        eventDesc = this.translateService.instant('EVENT.event_description.mstp_topology_changed');
        break;
      case EventType.newRootBridgeSelectedInTopology:
        eventDesc = this.translateService.instant('EVENT.event_description.new_root_bridge_selected_in_topology');
        break;
      case EventType.newPortRoleSelected:
        eventDesc = this.translateService.instant('EVENT.event_description.new_port_role_selected');
        break;
      case EventType.ospfDesignatedRouterChanged:
        eventDesc = this.translateService.instant('EVENT.event_description.ospf_designated_router_changed');
        break;
      case EventType.ospfDesignatedRouterInterfaceAndAdjacencyChanged:
        eventDesc = this.translateService.instant(
          'EVENT.event_description.ospf_designated_router_interface_and_adjacency_changed'
        );
        break;
      case EventType.interfaceSetAsOspfDesignatedRouter:
        eventDesc = this.translateService.instant('EVENT.event_description.interface_set_as_ospf_designated_router');
        break;
      case EventType.cliButtonEvent:
        if (result) {
          const eventDescKey = this.mapCliExecuteteResultEvent(result, cliname);
          if (eventDescKey) {
            eventDesc = this.translateService.instant(`EVENT.event_description.${eventDescKey}`, {
              user,
              sourceIP: ip,
              cliName: cliname
            });
          }
        }
        break;
      case EventType.rogueDeviceDetected:
        eventDesc = this.translateService.instant('EVENT.event_description.unknown_device_detected');
        break;
      case EventType.passwordAutomaticallyChanged:
        if (status === 'all failed' || +status.split(',')[1].split(':')[1].trim() || false)
          eventDesc = this.translateService.instant('EVENT.event_description.password_automatically_changed_failed');
        else
          eventDesc = this.translateService.instant('EVENT.event_description.password_automatically_changed_success');
        break;
      case EventType.temporaryAccountEvent:
        if (status === 'activation successfully')
          eventDesc = this.translateService.instant('EVENT.event_description.temporary_account_activate_success', {
            ip
          });
        else if (status === 'deactivation successfully')
          eventDesc = this.translateService.instant('EVENT.event_description.temporary_account_deactivate_success', {
            ip
          });
        break;
      case EventType.accountAuditEvent:
        eventDesc = this.translateService.instant('EVENT.event_description.account_audit_mismatch');
        break;
      case EventType.accountAuditFailedEvent:
        eventDesc = this.translateService.instant('EVENT.event_description.account_audit_failed', { ip });
        break;
      case EventType.passwordAutomationScheduled:
        eventDesc = this.translateService.instant('EVENT.event_description.password_automation_scheduled');
        break;
      case EventType.accountAuditBaselineEvent:
        if (status === 'match')
          eventDesc = this.translateService.instant('EVENT.event_description.account_audit_match');
        else eventDesc = this.translateService.instant('EVENT.event_description.account_audit_baseline_failed');
        break;
      case EventType.EVENT_SERIAL_DEVICE_PORT_TX:
        eventDesc = this.translateService.instant('EVENT.event_description.event_message_serial_device_port_tx', {
          portnum: port,
          min: last / 60
        });
        break;
      case EventType.EVENT_SERIAL_DEVICE_PORT_RX:
        eventDesc = this.translateService.instant('EVENT.event_description.event_message_serial_device_port_rx', {
          portnum: port,
          min: last / 60
        });
        break;
      case EventType.EVENT_SERIAL_DEVICE_PORT_RXTX:
        eventDesc = this.translateService.instant('EVENT.event_description.event_message_serial_device_port_rxtx', {
          portnum: port,
          min: last / 60
        });
        break;
      case EventType.EVENT_SERIAL_DEVICE_PORT_FRAME:
        eventDesc = this.translateService.instant('EVENT.event_description.event_message_serial_device_port_frame', {
          portnum: port
        });
        break;
      case EventType.EVENT_SERIAL_DEVICE_PORT_PARITY:
        eventDesc = this.translateService.instant('EVENT.event_description.event_message_serial_device_port_parity', {
          portnum: port
        });
        break;
      case EventType.EVENT_SERIAL_DEVICE_PORT_OVERRUN:
        eventDesc = this.translateService.instant('EVENT.event_description.event_message_serial_device_port_overrun', {
          portnum: port
        });
        break;
      case EventType.EVENT_SERIAL_DEVICE_PORT_BREAK:
        eventDesc = this.translateService.instant('EVENT.event_description.event_message_serial_device_port_break', {
          portnum: port
        });
        break;
      case EventType.EVENT_SERIAL_DEVICE_PORT_TX_RECOVERY:
        eventDesc = this.translateService.instant(
          'EVENT.event_description.event_message_serial_device_port_tx_recovery',
          { portnum: port }
        );
        break;
      case EventType.EVENT_SERIAL_DEVICE_PORT_RX_RECOVERY:
        eventDesc = this.translateService.instant(
          'EVENT.event_description.event_message_serial_device_port_rx_recovery',
          { portnum: port }
        );
        break;
      case EventType.EVENT_SERIAL_DEVICE_PORT_RXTX_RECOVERY:
        eventDesc = this.translateService.instant(
          'EVENT.event_description.event_message_serial_device_port_rxtx_recovery',
          { portnum: port }
        );
        break;
      case EventType.EVENT_SERIAL_DEVICE_PORT_FRAME_RECOVERY:
        eventDesc = this.translateService.instant(
          'EVENT.event_description.event_message_serial_device_port_any_recovery',
          { portnum: port }
        );
        break;
      case EventType.EVENT_SERIAL_DEVICE_PORT_PARITY_RECOVERY:
        eventDesc = this.translateService.instant(
          'EVENT.event_description.event_message_serial_device_port_any_recovery',
          { portnum: port }
        );
        break;
      case EventType.EVENT_SERIAL_DEVICE_PORT_OVERRUN_RECOVERY:
        eventDesc = this.translateService.instant(
          'EVENT.event_description.event_message_serial_device_port_any_recovery',
          { portnum: port }
        );
        break;
      case EventType.EVENT_SERIAL_DEVICE_PORT_BREAK_RECOVERY:
        eventDesc = this.translateService.instant(
          'EVENT.event_description.event_message_serial_device_port_any_recovery',
          { portnum: port }
        );
        break;
      case EventType.phrPortTimediff:
        eventDesc = this.translateService.instant('EVENT.event_description.phr_port_timediff');
        break;
      case EventType.phrPortWrongLan:
        eventDesc = this.translateService.instant('EVENT.event_description.phr_port_wrong_lan');
        break;
      case EventType.mrpMultipleEvent:
        eventDesc = this.translateService.instant('EVENT.event_description.mrp_multiple_event');
        break;
      case EventType.mrpRingOpenEvent:
        eventDesc = this.translateService.instant('EVENT.event_description.mrp_ring_open_event');
        break;
      case EventType.portPDShotCircuited:
        eventDesc = this.translateService.instant('EVENT.event_description.port_pd_short_circuited', { portnum: port });
        break;
      case EventType.l3FirewallPolicyViolation:
        eventDesc = this.translateService.instant('EVENT.event_description.l3_firewall_policy_violation');
        break;
      case EventType.recorvedL3FirewallPolicyViolation:
        eventDesc = this.translateService.instant('EVENT.event_description.recorved_l3_firewall_policy_violation');
        break;
      case EventType.deviceLockdownViolation:
        eventDesc = this.translateService.instant('EVENT.event_description.device_lockdown_violation');
        break;
      case EventType.recorvedDeviceLockdownViolation:
        eventDesc = this.translateService.instant('EVENT.event_description.recorved_device_lockdown_violation');
        break;
      case EventType.opcuaServerStart:
        eventDesc = this.translateService.instant('EVENT.event_description.opcua_server_start');
        break;
      case EventType.opcuaServerStop:
        eventDesc = this.translateService.instant('EVENT.event_description.opcua_server_stop');
        break;
      case EventType.syslogServerStart:
        eventDesc = this.translateService.instant('EVENT.event_description.syslog_server_start');
        break;
      case EventType.syslogServerStop:
        eventDesc = this.translateService.instant('EVENT.event_description.syslog_server_stop');
        break;
      case EventType.portLoopDetect:
        eventDesc = this.translateService.instant('EVENT.event_description.port_loop_detect', { portnum: port });
        break;
      case EventType.portLoopDetectResolved:
        eventDesc = this.translateService.instant('EVENT.event_description.port_loop_detect_resolved', {
          portnum: port
        });
        break;
      case EventType.backgroundScan:
        eventDesc = this.translateService.instant('EVENT.event_description.background_scan_found', { ip });
        break;
      case EventType.ipConflictDetected:
        if (!conflicts) break;

        eventDesc =
          this.translateService.instant('EVENT.event_description.ip_conflict_detected', { ip: source_ip }) + '\n\r';
        eventDesc =
          eventDesc +
          JSON.parse(conflicts)
            .map(conflict => {
              return `- MAC: ${conflict.mac}, NIC: ${conflict.nic}`;
            })
            .join('\n');

        break;
      case EventType.ipConflictRecovery:
        eventDesc = this.translateService.instant('EVENT.event_description.ip_conflict_recovery');
        break;
      case EventType.ipConflictDetectedFailed:
        eventDesc = this.translateService.instant('EVENT.event_description.ip_conflict_detected_failed');
        break;
      case EventType.ipsLicensePointsConsumed:
        eventDesc = this.translateService.instant('EVENT.event_description.ips_license_points_consumed', {
          consumed: value,
          available: trapDetail
        });
        break;
      case EventType.ipsLicensePointsRunningOut:
        eventDesc = this.translateService.instant('EVENT.event_description.ips_license_points_running_out', {
          available: value,
          days: trapDetail
        });
        break;
      default:
        eventDesc = this.translateService.instant('EVENT.event_description.event_not_found_type', {
          eventType: eventType
        });
    }
    return eventDesc;
  }

  mapCliExecuteteResultEvent(status: string, cliName: string): string {
    switch (status) {
      case 'Started':
        return cliName ? 'cli_saved_script_event' : 'cli_script_event';
      case 'Button Clicked':
        return 'cli_button_event_start';
      case 'Completely done':
        return 'cli_button_event_all_finished';
      case 'Partially completed':
        return 'cli_button_event_all_partially_finished';
      case 'All failed':
        return 'cli_button_event_all_failed';
      default:
        return '';
    }
  }

  private getDeviceTypeByJsonStr(jsonInt: number): string {
    let ret = '';
    switch (jsonInt) {
      case 0:
        ret = this.translateService.instant(
          'EVENT.event_description.event_linux_power_detection_fail_devietype_noPresent'
        );
        break;
      case 1:
        ret = 'legacy';
        break;
      case 2:
        ret = 'dot3af';
        break;
      case 3:
        ret = 'dot3at';
        break;
      case 4:
        ret = 'dot3bt';
        break;
      case 5:
        ret = 'nic';
        break;
      case 6:
        ret = this.translateService.instant(
          'EVENT.event_description.event_linux_power_detection_fail_devietype_unknown'
        );
        break;
      case 7:
        ret = this.translateService.instant('EVENT.event_description.event_linux_power_detection_fail_devietype_na');
        break;
      case 8:
        ret = 'dot3btss';
        break;
      case 9:
        ret = 'dot3btds';
        break;
    }
    return ret;
  }

  private getConfigSuggestionByJsonStr(jsonInt: number): string {
    let ret = '';

    switch (jsonInt) {
      case 0:
        ret = this.translateService.instant('EVENT.event_description.event_linux_power_detection_fail_suggestion_no');
        break;
      case 1:
        ret = this.translateService.instant(
          'EVENT.event_description.event_linux_power_detection_fail_suggestion_enable_POE'
        );
        break;
      case 2:
        ret = this.translateService.instant(
          'EVENT.event_description.event_linux_power_detection_fail_suggestion_disable_POE'
        );
        break;
      case 3:
        ret = this.translateService.instant(
          'EVENT.event_description.event_linux_power_detection_fail_suggestion_select_auto'
        );
        break;
      case 4:
        ret = this.translateService.instant(
          'EVENT.event_description.event_linux_power_detection_fail_suggestion_select_high_power'
        );
        break;
      case 5:
        ret = this.translateService.instant(
          'EVENT.event_description.event_linux_power_detection_fail_suggestion_select_force'
        );
        break;
      case 6:
        ret = this.translateService.instant(
          'EVENT.event_description.event_linux_power_detection_fail_suggestion_enable_legacy'
        );
        break;
      case 7:
        ret = this.translateService.instant(
          'EVENT.event_description.event_linux_power_detection_fail_suggestion_rais_EPS_voltage'
        );
        break;
    }
    return ret;
  }
  private registerEventBus(): Subject<any> {
    this.eventBus = this.eventbus.on().subscribe((message: BroadcastEvent) => {
      if (message.datatype === DataType.EVENT) {
        this.triggerEventSubject.next(message);
      }
    });
    return this.triggerEventSubject;
  }

  queryDeviceData(siteId: string, deviceIp?: string): any[] {
    return this.dataRepository.getDataByIp(DataType.DEVICE, siteId, deviceIp);
  }

  unsubscribeTrigger(): void {
    if (this.eventBus !== undefined) {
      this.eventBus.unsubscribe();
    }
  }

  getDashboardEventData(
    siteId: string,
    eventType: number,
    queryStartTime?: number,
    queryEndTime?: number,
    groupBy?: string
  ): Observable<any> {
    return this.dataService.getDashBoardEvent(siteId, eventType, queryStartTime, queryEndTime, groupBy);
  }

  clearAllEvent(siteId, category?: number): Promise<any> {
    return this.dataService.deleteAllEvent(siteId, category);
  }

  getRowSeverity(row): string {
    let severityClass = '';
    let tableRowHeightClass = '';
    switch (row.severity) {
      case Severity.EVENT_SEVERITY_INFO:
        severityClass = 'info-severity';
        break;
      case Severity.EVENT_SEVERITY_WARNING:
        severityClass = 'warning-severity';
        break;
      case Severity.EVENT_SEVERITY_CRITICAL:
        severityClass = 'critical-severity';
        break;
      case Severity.EVENT_SEVERITY_SYSTEM_INFO:
        severityClass = 'system-info-severity';
        break;
      default:
        severityClass = 'info-severity';
    }
    const tableRowHeight = localStorage.getItem('tableRowHeight');
    if (tableRowHeight === '3') {
      tableRowHeightClass = 'table-row-height-dense';
    } else if (tableRowHeight === '2') {
      tableRowHeightClass = 'table-row-height-medium';
    } else {
      tableRowHeightClass = 'table-row-height-default';
    }
    return severityClass + ' ' + tableRowHeightClass;
  }

  getTableRowHeight(): string {
    const tableRowHeight = localStorage.getItem('tableRowHeight');
    if (tableRowHeight === '3') {
      return 'table-row-height-dense';
    } else if (tableRowHeight === '2') {
      return 'table-row-height-medium';
    } else {
      return 'table-row-height-default';
    }
  }

  getTableHeaderRowHeight(): string {
    const tableRowHeight = localStorage.getItem('tableRowHeight');
    if (tableRowHeight === '3') {
      return 'table-row-header-height-dense';
    } else if (tableRowHeight === '2') {
      return 'table-row-header-height-medium';
    } else {
      return 'table-row-header-height-default';
    }
  }
}
