/* eslint-disable @typescript-eslint/naming-convention, no-underscore-dangle, id-blacklist, id-match */
export class EventTableDisplayUnit {
  ack: boolean;
  id: number;
  source: string;
  sourceip: string;
  deviceAlias: string;
  description: string;
  time: string;
  severity: number;
  siteName: string;
  siteID: string;
  deviceId: string;
  trapdetail: string;
  ack_time: string;
}

export enum rateLimitType {
  off = 1,
  on = 2,
}

export enum databaseBackupStatus {
  success = 0,
  fail = 1,
}

export enum fiberStatus {
  fiberTXLowPowerWarning = 2,
  fiberRXLowPowerWarning = 3,
  fiberTXHightPowerWarning = 4,
  fiberTemppertureWarning = 5,
}

export enum prpHSRPort {
  portA = 1,
  portB = 2,
  interLink = 3,
}

export enum usbEventType {
  usbNoEnoughSpace = 1,
  usbUnAuthDevice = 2,
  usbExportConfigFail = 3,
  usbExportLogFail = 4,
  usbAutoImportFail = 5,
  usbIsAttached = 6,
  usbIsDetached = 7,
}

export enum turboRingMasterStatus {
  turboRingMasterMatch = 1,
  turboRingMasterMismatch = 2,
}

export enum authServerType {
  tacacsServer = 1,
  radiusServer = 2,
}

export enum EventType {
  portLinkUpEvent = 0x00020002,
  portLinkDownEvent = 0x00020001,
  portLinkDownRecovery = 0x80020001,
  portLinkUpRecovery = 0x80020002,
  vpnLinkDown = 0x00030001,
  vpnLinkUp = 0x00030002,
  vpnLinkDownRecovery = 0x80030001,
  fiberWarning = 0x00030003,
  icmpUnreachablue = 0x00010001,
  icmpReachablue = 0x80010001,
  icmpPacketlossOverThreshold = 0x00010002,
  icmpPacketlossOverThresholdRecovery = 0x80010002,
  icmpPacketlossOverCritialThreshold = 0x00010003,
  icmpPacketlossOverCritialThresholdRecovery = 0x80010003,
  devicePowerOffToOn = 0x10010006,
  devicePowerOnToOff = 0x00010006,
  devicePowerUp = 0x80010006,
  turboringTopologyChanged = 0x10710001,
  turboringCouplingportChanged = 0x10710002,
  turboringMasterChanged = 0x10710003,
  deviceConfigurationChanged = 0x10710004,
  deviceFirmwareUpdated = 0x10710005,
  deviceTrapReceived = 0x10710006,
  deviceInformReceived = 0x10710007,
  rateLimitedOn = 0x10710008,
  lldpChanged = 0x10710009,
  deviceSNMPunreachable = 0x00010007,
  deviceSNMPreachable = 0x80010007,

  portTrafficOverLoad = 0x10720001,
  portLoopDetected = 0x10720002,

  coldStart = 0x10010008,
  warmStart = 0x10010009,
  authFail = 0x1001000a,
  dIOn = 0x1001000b,
  dIOff = 0x1001000c,

  /* MOXA IW event start */
  iwClientJoined = 0x10f10006,
  iwClientLeft = 0x10f10007,
  /* MOXA IW event end */

  eventMxviewJobStart = 0x10000022,
  eventMxviewJobDone = 0x1000001a,
  usercodeRevoke = 0x1000001b,
  eventMxviewSMSSucess = 0x1000000c,
  eventMxviewSMSFAIL = 0x1000000d,
  eventMxviewAutoTopologyFinished = 0x10000011,
  eventMxviewAutoTopologyStarted = 0x10000012,
  eventMxviewServerStart = 0x10000013,
  eventMxviewDbBackupEvent = 0x10000014,
  eventMxviewUserLoginSuccessful = 0x10000015,
  eventMxviewUserLoginFailed = 0x10000016,
  eventMxviewUserBadLogin = 0x10000017,
  eventMxviewUserLogout = 0x10000018,
  eventMXviewUserLockout = 0x10000019,
  eventNetworkLatency = 0x1000001f,

  powerTypeDanger = 0x0001000e,
  powerTypeDangerRecover = 0x8001000e,

  customEventTriggered = 0x00010100,
  customEventRecovered = 0x80010100,

  availabilityDown = 0x0001000d,
  availabilityDownRecovery = 0x8001000d,
  trunkPortLinkUp = 0x00020004,
  trunkPortLinkUpRecovery = 0x80020004,
  trunkPortLinkDown = 0x00020003,
  trunkPortLinkDownRecovery = 0x80020003,
  licenseNotEnough = 0x1000000e,
  diskSpaceNotEnough = 0x1000000f,
  diskSpaceLessThanMinimum = 0x10000010,
  allEventsCleared = 0x1000000b,

  ieiABC02Warning = 0x10710012,
  ieiTurboringMasterMismatch = 0x10710013,
  ieiUserLoginSuccess = 0x10710014,
  ieiUserLoginFail = 0x10710015,
  ieiLoggingCapacity = 0x10710016,
  ieiUserInfoChg = 0x10710017,
  ieiConfigImport = 0x10710018,
  ieiPHRFunctionFail = 0x10710019,
  ieiEDRFirewallPolicy = 0x1071001a,
  ieiFiberWarning = 0x10720003,
  ieiMACStickyViolation = 0x1071001b,
  ieiRemoteAuthSuccess = 0x1071001c,
  ieiRemoteAuthFail = 0x1071001d,
  // for EDR security event
  edrDDOSAttack = 0x00010010,
  edrEventDDOSAttackRecovery = 0x80010010,
  edrEventFirewallAttack = 0x00010011,
  edrEventFirewallAttackRecovery = 0x80010011,
  edrEventTrustedAccessAttack = 0x00010012,
  edrEventTrustedAccessAttackRecovery = 0x80010012,
  edrEventTooManyLoginFailure = 0x00010013,
  edrEventTooManyLoginFailureRecovery = 0x80010013,
  // for input/output bandwidth threshold
  inputBandwidthOverThreshold = 0x00020005,
  inputBandwidthOverThresholdRecovery = 0x80020005,
  inputBandwidthDownToThreshold = 0x00020006,
  inputBandwidthDownToThresholdRecovery = 0x80020006,
  outputBandwidthOverThreshold = 0x00020007,
  outputBandwidthOverThresholdRecovery = 0x80020007,
  outputBandwidthDownToThreshold = 0x00020008,
  outputBandwidthDownToThresholdRecovery = 0x80020008,
  inputPacketErrorOverThreshold = 0x00020009,
  inputPacketErrorOverThresholdRecovery = 0x80020009,
  outputPacketErrorOverThreshold = 0x0002000a,
  outputPacketErrorOverThresholdRecovery = 0x8002000a,
  // for PoE event
  eventPDOverCurrent = 0x1071000a,
  eventPDCheck_Fail = 0x1071000b,
  eventPDPowerOn = 0x1071000c,
  eventPDPowerOff = 0x1071000d,
  eventExceedThreshold = 0x1071000e,
  eventPSEFetBad = 0x1071000f,
  eventPSEOverTemp = 0x10710010,
  eventPSEVEEUVLO = 0x10710011,

  // for linux switch
  eventLinuxUserLoginSuccess = 0x1071001e,
  eventLinuxUserLoginLockout = 0x1071001f,
  eventLinuxAccountSettingChange = 0x10710020,
  eventLinuxSSLCerChange = 0x10710021,
  eventLinuxPasswordChange = 0x10710022,
  eventLinuxConfigChange = 0x10710023,
  eventLinuxConfigImport = 0x10710024,
  eventLinuxLogCapacityThreshold = 0x10710025,
  eventLinuxPowerON = 0x10710026,
  eventLinuxPowerOFF = 0x10710027,
  eventLinuxDION = 0x10710028,
  eventLinuxDIOFF = 0x10710029,
  eventLinuxPortShutdownByRatelimit = 0x1071002a,
  eventLinuxPortRecoveryByRatelimit = 0x1071002b,
  eventLinuxPortShutdownBySecurity = 0x1071002c,
  eventLinuxTopologyChange = 0x1071002d,
  eventLinuxCOUPLINGChange = 0x1071002e,
  eventLinuxMasterChange = 0x1071002f,
  eventLinuxMasterMismatch = 0x10710030,
  eventIEEERSTPTopologyChange = 0x10710031,
  eventIEEERSTPRootChange = 0x10710032,
  eventLinuxRSTPMigration = 0x10710033,
  eventLinuxRSTPInvalidBPDU = 0x10710034,
  eventLinuxRSTPNewPortRole = 0x10710035,
  eventLinuxRedundantPortHealthCheck = 0x10710036,
  eventLinuxDUALHOMINGChange = 0x10710037,
  eventLinuxDOTlXAuthFAIL = 0x10710038,
  eventIEEELLDPTableChange = 0x10710039,
  eventLinuxPDPowerON = 0x1071003a,
  eventLinuxPDPowerOFF = 0x1071003b,
  eventLinuxLowInputVoltage = 0x1071003c,
  eventLinuxPDOverCurrent = 0x1071003d,
  eventLinuxPDNoResponse = 0x1071003e,
  eventLinuxOverPowerBudgetLimit = 0x1071003f,
  eventLinuxPowerDetectionFail = 0x10710040,
  eventLinuxRMONTrapIsRaising = 0x10710041,
  eventLinuxRMONTrapIsFalling = 0x10710042,
  // for v3 trap event
  eventV3TrapParseError = 0x00010008,
  eventV3TrapParseErrorRecovery = 0x80010008,

  // for Nport trap
  eventNportSyslogOverThreshold = 0x10a10001,

  eventSfpTxBelow = 0x00020010,
  eventSfpRxBelow = 0x00020011,
  eventSfpTempOver = 0x00020012,
  eventSfpVoltageOver = 0x00020013,
  eventSfpVoltageBelow = 0x00020014,
  eventSfpTxBelowRecovery = 0x80020010,
  eventSfpRxBelowRecovery = 0x80020011,
  eventSfpTempOverRecovery = 0x80020012,
  eventSfpVoltageOverRecovery = 0x80020013,
  eventSfpVoltageBelowRecovery = 0x80020014,

  // GOOSE event
  gooseTimeout = 0x00010015,
  gooseTampered = 0x00010016,
  gooseResume = 0x80010015,
  gooseHealthy = 0x80010016,

  // PT-G7828 Trap
  eventTrackingStatusChanged = 0x10710043,
  eventTrackingVrrpStatusChanged = 0x10710044,
  eventTrackingStaticRouteStatusChanged = 0x10710045,
  eventTrackingPortEnabledStatus = 0x10710046,
  eventEpsIsOn = 0x10710047,
  eventEpsIsOff = 0x10710048,
  eventDyingGasp = 0x10710049,

  //AWK-3252A Trap
  eventLogClearedTrapEventInfo = 0x10f10008,
  poEOnInfo = 0x00f10009,
  poEOffInfo = 0x00f1000a,

  //MXview One Server Alert
  networkLatency = 0x1000001f,
  outOfMemory = 0x1000001c,
  insufficientDiskSpace = 0x1000001e,
  highCpuLoading = 0x1000001d,
  eventMXviewServerLicenseLimit = 0x10000020,

  firmwareVersionRelease = 268435489,

  //MRX Trap
  ptpGrandmasterChanged = 0x10710050,
  ptpSyncStatusChanged = 0x10710051,
  fanModuleMalfunction = 0x10718852,
  powerModuleFanMalfunction = 0x10710053,
  systemTemperatureExceedsThreshold = 0x10710054,
  thermalSensorComponentOverheatDetected = 0x10710055,
  powerHasBeenCutDueToOverheating = 0x10710056,
  overheatProtectionNowActiveForPowerModule = 0x10710057,
  vrrpMasterChanged = 0x10710058,
  firmwareUpgraded = 0x10710059,
  mstpTopologyChanged = 0x10710069,
  newRootBridgeSelectedInTopology = 0x1071006a,
  newPortRoleSelected = 0x1071006b,
  ospfDesignatedRouterChanged = 0x1071006c,
  ospfDesignatedRouterInterfaceAndAdjacencyChanged = 0x1071006d,
  interfaceSetAsOspfDesignatedRouter = 0x1071006e,
  cliButtonEvent = *********,
  rogueDeviceDetected = 0x10000023,
  passwordAutomaticallyChanged = 0x10000026,
  temporaryAccountEvent = 0x10000027,
  accountAuditEvent = 0x10000028,
  accountAuditFailedEvent = 0x10000029,
  passwordAutomationScheduled = 0x10000024,
  accountAuditBaselineEvent = 0x10000038,
  accountAudit1 = *********,
  accountAudit2 = *********,
  backgroundScan = 0x1000003B,
  ipConflictDetected = 0x0001001B,
  ipConflictRecovery = 0x8001001B,
  ipConflictDetectedFailed = 0x1000003A,

  EVENT_SERIAL_DEVICE_PORT_TX = 0x10a10002,
  EVENT_SERIAL_DEVICE_PORT_RX = 0x10a10004,
  EVENT_SERIAL_DEVICE_PORT_RXTX = 0x10a10006,
  EVENT_SERIAL_DEVICE_PORT_FRAME = 0x10a10008,
  EVENT_SERIAL_DEVICE_PORT_PARITY = 0x10a1000a,
  EVENT_SERIAL_DEVICE_PORT_OVERRUN = 0x10a1000c,
  EVENT_SERIAL_DEVICE_PORT_BREAK = 0x10a1000e,
  EVENT_SERIAL_DEVICE_PORT_TX_RECOVERY = 0x10a10003,
  EVENT_SERIAL_DEVICE_PORT_RX_RECOVERY = 0x10a10005,
  EVENT_SERIAL_DEVICE_PORT_RXTX_RECOVERY = 0x10a10007,
  EVENT_SERIAL_DEVICE_PORT_FRAME_RECOVERY = 0x10a10009,
  EVENT_SERIAL_DEVICE_PORT_PARITY_RECOVERY = 0x10a1000b,
  EVENT_SERIAL_DEVICE_PORT_OVERRUN_RECOVERY = 0x10a1000d,
  EVENT_SERIAL_DEVICE_PORT_BREAK_RECOVERY = 0x10a1000f,
  phrPortTimediff = 0x1071006f,
  phrPortWrongLan = 0x10710070,
  mrpMultipleEvent = 0x10710071,
  mrpRingOpenEvent = 0x10710072,
  portPDShotCircuited = 0x1071004a,
  l3FirewallPolicyViolation = 0x00010019,
  recorvedL3FirewallPolicyViolation = 0x80010019,
  deviceLockdownViolation = 0x0001001a,
  recorvedDeviceLockdownViolation = 0x8001001a,
  opcuaServerStart = 0x10000005,
  opcuaServerStop = 0x10000006,
  syslogServerStart = 0x10000007,
  syslogServerStop = 0x10000008,
  portLoopDetect = 0x00010018,
  portLoopDetectResolved = 0x80010018,

  // IPS License Management Events
  ipsLicensePointsConsumed = 0x1000002a,
  ipsLicensePointsRunningOut = 0x1000002b,
}

export class Page {
  // The number of elements in the page
  size = 0;
  // The total number of elements
  totalElements = 0;
  // The total number of pages
  totalPages = 0;
  // The current page number
  pageNumber = 0;
}
