
SRS (What to do)
- 對系統來說
  - 預設會有一個管理者帳號 admin 並且不能停用及刪除
  - 預設會有一個角色 admin 並且不能刪除
  - 預設會有一個權限 account management 並且不能刪除
  - 帳號 admin 固定會有角色 admin，並且不能被變便
  - 角色 admin 固定會權限 account management 並且不能被移除

- 身為擁有權限 account management 的使用者
  - 可以看到並進入 Account Management 頁面
  - 可以在此頁面看到所有帳號的清單
    - 顯示欄位
      - Account, 可排序
      - Role, 可排序
      - Activate Status, 可排序
      - Display Name, 可排序
      - Email, 可排序
      - 功能選單，包含
        - 停用，點選後跳出確認對話框
        - 刪除，點選後跳出確認對話框
  - 可以透過篩選功能查詢帳號
    - 篩選爛位
      - Account: 模糊比對
      - Role: 下拉可複選
      - Display Name: 模糊比對
      - Email: 模糊比對
  - 可以新增其它非 admin 帳號
  - 可以針對單一其它非 admin 帳號帳號進行修改、停用、刪除
    - 新增或修改時可以修改密碼、角色、電子郵件等欄位

- 身為以帳號 admin 登入的使用者
  - 可以修改 admin
    - 與非 admin 帳號同但不包含角色
  
- 任何帳號
  - 一旦該帳號被停用或刪除將無法再登入
  - 已登入中的帳號被停用或刪除時，該使用者無法進行後續操作並即刻登出，導回登入畫面


SDS (How to do)
- 系統設計
  - Table schema, ERD
  - API Spec

- 頁面 Design (Figma link)
  - 系統在 System Menu > Mamagement > Account Management 掛載選單進入點
  - 進入後首頁以清單呈現所有帳號
    - 預設每頁5筆擷取資料
    - 帳號清單
      - 顯示欄位對應 api payload
        - account, 可排序
        - role, 可排序
        - activateStatus, 可排序
        - displayName, 可排序
        - email, 可排序
      - 篩選爛位，對應 api query parameters
        account: 模糊比對
        role: 下拉可複選
        displayName: 模糊比對
        email: 模糊比對
    - 點選建立彈出帳號建立編輯對話框
    - 點選停用跳出確認對話框
      - disabled: 該筆資料為 admin 但使用者並非以帳號 admin 登入
    - 點選刪除跳出確認對話框
      - disabled: 該筆資料為 admin 但使用者並非以帳號 admin 登入
  - 帳號建立編輯對話框
    - 欄位規格定義
      account, string, null, 4-32, ....
      password, string, null, 4-64, ....
      role, ...., 資料來源: api endpoint...
      activateStatus, ...
      displayName, ...
      email, ...
  - 帳號停用對話框
    - 確認後呼叫 api endpoint ...
  - 帳號刪除對話框
    - 確認後呼叫 api endpoint ...
 