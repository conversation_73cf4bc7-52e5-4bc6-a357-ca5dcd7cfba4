<div>
  <h3 mat-dialog-title>
    {{ 'script_automation.execute_button' | translate }}
    <span *ngIf="!showExecuteResult">: {{ dialogData.button.name }}</span>
  </h3>
  <div mat-dialog-content>
    <ng-container *ngIf="!showExecuteResult">
      <div class="alert-info">
        <div *ngIf="panelOptions.affectedDevice.length > 0">
          <span style="margin: 8px 0">
            <mat-icon fontIcon="info" />
            <span>
              {{
                'script_automation.affected_devices_info_2'
                  | translate : { affectedDevices: panelOptions.affectedDevice.length ?? 0 }
              }}:
            </span>
          </span>
          <ul>
            <li *ngFor="let device of panelOptions.affectedDevice">{{ device.alias }}</li>
          </ul>
        </div>
        <div *ngIf="missingDeviceList.length > 0">
          <span style="margin: 8px 0">
            <mat-icon *ngIf="panelOptions.affectedDevice.length <= 0" fontIcon="info"></mat-icon>
            <span
              style="margin: 8px 0"
              [ngStyle]="{ 'padding-left': panelOptions.affectedDevice.length <= 0 ? '0' : '24px' }"
            >
              {{ 'script_automation.device_missing' | translate }}:
            </span>
          </span>
          <ul>
            <li *ngFor="let device of missingDeviceList">{{ device }}</li>
          </ul>
        </div>
      </div>
      <div style="margin: 8px 0">{{ 'script_automation.confirm_proceed' | translate }}</div>
      <form *ngIf="panelOptions.isPrivilege" [formGroup]="form" class="flex-column">
        <mat-form-field class="username" [style.width.px]="180">
          <input
            matInput
            id="input-username"
            maxlength="32"
            placeholder="{{ 'LOGIN.username' | translate }}"
            formControlName="username"
            required
          />
          <mat-error *ngIf="form.controls['username'].hasError('required')">
            {{ 'ADD_DEVICE.field_required' | translate }}</mat-error
          >
        </mat-form-field>
        <mat-form-field class="password suffix-form-field" [style.width.px]="180">
          <input
            matInput
            id="input-password"
            maxlength="63"
            placeholder="{{ 'LOGIN.password' | translate }}"
            formControlName="password"
            [type]="hidePassword ? 'password' : 'text'"
            required
          />
          <button type="button" mat-icon-button type="button" (click)="hidePassword = !hidePassword">
            <mat-icon> {{ hidePassword ? 'visibility_off' : 'visibility' }} </mat-icon>
          </button>
          <mat-error *ngIf="form.controls['password'].hasError('required')">
            {{ 'ADD_DEVICE.field_required' | translate }}</mat-error
          >
        </mat-form-field>
      </form>
    </ng-container>
  </div>
  <ng-container *ngIf="showExecuteResult">
    <div class="dialog-information">
      <div class="bold-text">
        <mat-icon class="healthy-color">warning</mat-icon>{{ 'script_automation.execute_button_hint' | translate }}
      </div>
      <div>{{ 'script_automation.execute_button_info' | translate }}</div>
    </div>
    <div class="devices-table">
      <app-execute-cli-table [deviceTableData]="deviceTableData" />
    </div>
  </ng-container>
  <div mat-dialog-actions align="end">
    <button
      *ngIf="!showExecuteResult"
      id="dailog-button-execute"
      mat-raised-button
      class="critical-button"
      (click)="onExecute()"
      [disabled]="form.invalid && panelOptions.isPrivilege"
    >
      {{ 'BUTTON.confirm' | translate }}
    </button>
    <button *ngIf="!showExecuteResult" mat-button id="dialog-button-cancel" color="primary" mat-dialog-close>
      {{ 'BUTTON.cancel' | translate }}
    </button>
    <button
      *ngIf="showExecuteResult && hasReceivedAllStatuses"
      mat-button
      id="dialog-button-close"
      color="primary"
      (click)="onClose()"
    >
      {{ 'BUTTON.close' | translate }}
    </button>
  </div>
</div>
