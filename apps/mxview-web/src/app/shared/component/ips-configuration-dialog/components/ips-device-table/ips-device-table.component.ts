import { ChangeDetectionStrategy, Component, Input, OnChanges, SimpleChanges } from "@angular/core";
import { SharedModule } from "@mxview-web/app/shared/shared.module";
import { executionStatus, IpsItem } from "../../models/ips-configuration.model";
import { MatLegacyTableDataSource as MatTableDataSource } from '@angular/material/legacy-table';

@Component({
  selector: 'app-ips-device-table',
  imports: [SharedModule],
  standalone: true,
  styleUrls: ['./ips-device-table.component.scss'],
  template: `
  <table mat-table [dataSource]="dataSource">
    <ng-container matColumnDef="ip">
      <th mat-header-cell *matHeaderCellDef>{{'execute_cli_object.ip'|translate }}</th>
      <td mat-cell *matCellDef="let row">{{row.ip}}</td>
    </ng-container>

    <ng-container matColumnDef="alias">
      <th mat-header-cell *matHeaderCellDef>{{'execute_cli_object.alias'|translate }}</th>
      <td mat-cell *matCellDef="let row">{{row.alias}}</td>
    </ng-container>

    <ng-container matColumnDef="model">
      <th mat-header-cell *matHeaderCellDef>{{'execute_cli_object.model'|translate }}</th>
      <td mat-cell *matCellDef="let row">{{row.model}}</td>
    </ng-container>

    <ng-container matColumnDef="exeStatus">
      <th mat-header-cell *matHeaderCellDef>{{'ips_configuration.th-execution-status'|translate }}</th>
      <td mat-cell *matCellDef="let row" [class.status-failed]="isFailed(row.exeStatus)">{{row.exeStatusDisplay}}</td>
    </ng-container>

    <tr
      mat-header-row
      *matHeaderRowDef="displayColumns"
    ></tr>
    <tr mat-row *matRowDef="let row; columns: displayColumns"></tr>
  </table>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class IpsDeviceTableComponent implements OnChanges {
  @Input()
  list!: IpsItem[];

  @Input()
  isApply = false;

  dataSource: MatTableDataSource<IpsItem> = new MatTableDataSource();
  displayColumns: string[] = ['ip', 'alias', 'model'];

  isFailed(status: string):boolean {
    return executionStatus.Failed === status;
  }

  ngOnChanges(changes: SimpleChanges): void {
    const {list, isApply} = changes;

    if(list){
      this.dataSource.data = list.currentValue;
    }

    if(isApply && isApply.currentValue) {
      this.displayColumns = [...this.displayColumns, 'exeStatus'];
    }
  }

}
