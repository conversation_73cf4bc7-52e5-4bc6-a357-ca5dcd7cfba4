import { inject, Injectable } from '@angular/core';
import { ComponentStore } from '@ngrx/component-store';
import { ApplyIpsConfig, executeCliFailureType, executionStatus, IpsConfigForApi, IpsConfigState, IpsItem, WsExecuteStatus } from '../models/ips-configuration.model';
import { DeviceUnit } from '@mxview-web/app/shared/Service/mx-platform/DataDef/DevicesDataDefs';
import { catchError, Observable, of, switchMap, tap } from 'rxjs';
import { IpsConfigurationService } from '../services/ips-configuration.service';
import { MatLegacySnackBar as MatSnackBar } from '@angular/material/legacy-snack-bar';
import { TranslateService } from '@ngx-translate/core';
import { TriggerService } from '@mxview-web/app/shared/Service/mx-platform/Service/TriggerService';
import { AppState } from '@mxview-web/app/app.service';
import { GlobalEvent, GlobalEventType } from '@mxview-web/app/global.event';

const _initState: IpsConfigState  = {
  deviceList: [],
  isApply: false,
  isExecutionDone: false,
  isExecutionFail: false,
  isLoading: false
};

@Injectable({providedIn: 'any'})
export class IpsConfigurationStore extends ComponentStore<IpsConfigState> {
  readonly #service = inject(IpsConfigurationService);
  readonly #snackbar = inject(MatSnackBar);
  readonly #translate = inject(TranslateService);
  readonly #triggerService = inject( TriggerService);
  readonly #appState = inject(AppState);

  constructor() {
    super(_initState);
  }
  readonly deviceList$: Observable<IpsItem[]> = this.select((state) => state.deviceList);

  readonly setIsApply = this.updater((state, isApply: boolean) => ({ ...state, isApply }));
  readonly setIsLoading = this.updater((state, isLoading: boolean) => ({ ...state, isLoading }));
  readonly setIsExecutionDone = this.updater((state, isExecutionDone: boolean) => ({ ...state, isExecutionDone }));
  readonly setIsExecutionFail = this.updater((state, isExecutionFail: boolean) => ({ ...state, isExecutionFail }));
  readonly setDeviceList = this.updater((state, data: DeviceUnit[]) => {
    const deviceList: IpsItem[] =  data.map(m => ({
      ip: m.ip,
      alias: m.alias,
      model: m.model,
      exeStatus: executionStatus.NotSent,
      exeStatusDisplay: ''
    } ))
    return {...state, deviceList};
  })
  readonly setApplyedDeviceInProgress = this.updater((state, applyList: IpsItem[]) => {
    const newDevicesList = [...state.deviceList].map(origin => {
      const applyedDevice = applyList.find(f => f.ip === origin.ip);
      if(applyedDevice){
        return {...origin, exeStatus: executionStatus.InProgress, exeStatusDisplay: this.getStatus(executionStatus.InProgress)}
      }
      return origin
    });
    return {...state, deviceList: [...newDevicesList]}
  })
  readonly updateDeviceListState = this.updater((state, deviceList: IpsItem[]) => ({...state, deviceList}))

  readonly applyIpsConfig = this.effect((value: Observable<ApplyIpsConfig>) =>
    value.pipe(
      tap(() => this.toggleLoading(true)),
      switchMap((newValue) =>
        this.#service.postIpsConfig$(this.convertApplyIpsConfigToApi(newValue)).pipe(
          tap(() => {
            this.setApplyedDeviceInProgress(newValue.deviceList)
            this.toggleLoading(false);
            this.setIsApply(true);
          }),
          catchError((err) => {
              if (err.error.message !== 'built-in command execution process is running') {
                this.handleError('device_management.execute_fail')
              }
              return of(null)
          })
        ),
      )
    )
  );

  readonly wsMessage = this.#triggerService.wsMessage.pipe(
    tap(wsData => {
      if (!wsData) {
        return;
      }
      if (wsData['built_in_command']) {
        const statusList:WsExecuteStatus[]  = wsData['built_in_command'];
        const selectedDevices = [...this.get().deviceList];
        const newDevicesList: IpsItem[] = selectedDevices.map(device => {
          const devcieStatusInfos = statusList.filter(f => f.ip === device.ip);
          if(devcieStatusInfos.length > 0){
            const info = devcieStatusInfos[devcieStatusInfos.length -1];
            if(info.status === executionStatus.Failed){
              this.setIsExecutionFail(true);
            }
            return {
              ...device,
              exeStatus: info.status,
              exeStatusDisplay: this.getStatus(info.status, info.result)
            }
          }
          return {...device}
        })

        this.updateDeviceListState(newDevicesList)
      }
    })
  );

  // convertor
  private convertApplyIpsConfigToApi(vm: ApplyIpsConfig): IpsConfigForApi{
    return {
      target: vm.deviceList.map((m,i) => ({ip:m.ip, order: i+1})),
      meta: 'IPS_OPERATION_MODE',
      json: {
        ips: vm.enableIps,
        operation_mode: vm.operationMode
      },
      command: '0',
      stop: false
    }
  }

  // handler
  private toggleLoading = (show: boolean) => {
    this.#appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show }));
  };

  private handleError = (message: string) => {
    this.#snackbar.open(this.#translate.instant(message), this.#translate.instant('BUTTON.close'), {
      panelClass: ['error'],
    });
  };

  private getStatus(status: string, result?: string): string {
    const statusMapping: { [key: string]: string } = {
      [executionStatus.NotSent]: 'not_sent',
      [executionStatus.InProgress]: 'in_progress',
      [executionStatus.Finished]: 'finished',
      [executionStatus.Failed]: 'failed',
    };
    const ErrorMappings: { [key: string]: string } = {
      [executeCliFailureType.LoginFailure]: 'cli_error.login_failure',
      [executeCliFailureType.SSHNotSupported]: 'cli_error.ssh_not_supported',
      [executeCliFailureType.ConnectionFailure]: 'cli_error.connection_failure',
      [executeCliFailureType.HandshakeFailure]: 'cli_error.handshake_failure',
      [executeCliFailureType.ReachMaximumSsid]: 'cli_error.reach_maximum_ssid',
      [executeCliFailureType.PortLimit]: 'cli_error.port_limit',
      [executeCliFailureType.SNMPConfigurationMismatch]: 'cli_error.smmp_configuration_mismatch',
      [executeCliFailureType.UnableToSetPort]: 'cli_error.unable_to_set_port',
      [executeCliFailureType.CliSessionTimeout]: 'cli_session_timeout',
      [executeCliFailureType.UnknownError]: 'cli_error.unknown_error',
      [executeCliFailureType.LicenseNotActivated]: 'license_not_activated_for_ips',
      [executeCliFailureType.IpsNotSupport]: 'ips_not_support'
    };

    const unknownError = 'cli_error.unknown_error';
    let statusText = statusMapping[status] || unknownError;
    let resultText = '';
    let param = '';

    if (status === executionStatus.InProgress) {
      statusText = executionStatus.InProgress;
      resultText = '';
    } else if (status === executionStatus.Finished) {
      statusText = executionStatus.Finished;
      resultText = '';
    } else if (status === executionStatus.IpNotFound) {
      statusText = executionStatus.NotSent;
      resultText = ErrorMappings[executeCliFailureType.ConnectionFailure];
    } else if (status === executionStatus.Failed) {
      for (const errorType in ErrorMappings) {
        if (result.includes(errorType)) {
          resultText = ErrorMappings[errorType];
          break;
        }
      }
      if (resultText === '') {
        resultText = ErrorMappings[executeCliFailureType.UnknownError];
      } else {
        if (
          [
            executeCliFailureType.LoginFailure,
            executeCliFailureType.ConnectionFailure,
            executeCliFailureType.HandshakeFailure,
          ].includes(resultText as executeCliFailureType)
        ) {
          statusText = executionStatus.NotSent;
        } else if (
          [
            executeCliFailureType.ReachMaximumSsid,
            executeCliFailureType.PortLimit,
            executeCliFailureType.SNMPConfigurationMismatch,
            executeCliFailureType.UnableToSetPort,
            executeCliFailureType.UnknownError,
          ].includes(resultText as executeCliFailureType)
        ) {
          statusText = executionStatus.Failed;
        }
      }
      if (resultText === ErrorMappings[executeCliFailureType.ReachMaximumSsid]) {
        const regex = /\((\d+)\)/;
        const match = result.match(regex);
        if (match) {
          param = match[1].toString();
        }
      }
      if (resultText === ErrorMappings[executeCliFailureType.PortLimit]) {
        const regex = /under (\d+)/;
        const match = result.match(regex);
        if (match) {
          param = match[1].toString();
        }
      }
      if (resultText === ErrorMappings[executeCliFailureType.UnableToSetPort]) {
        const regex = /\{error port selection:(.+?)\}/;
        const match = result.match(regex);
        if (match) {
          param = match[1].toString();
        }
      }
    }
    statusText = statusText ? this.#translate.instant('execute_cli_object.' + statusMapping[statusText]) : '';
    resultText = resultText ? this.#translate.instant('execute_cli_object.' + resultText, { param }) : '';
    return resultText ? `${statusText}(${resultText})` : statusText;
  }
}
