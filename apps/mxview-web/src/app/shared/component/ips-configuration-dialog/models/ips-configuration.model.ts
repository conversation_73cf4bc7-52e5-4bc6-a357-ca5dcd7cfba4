export interface IpsConfigState {
  deviceList: IpsItem[];
  isApply: boolean;
  isExecutionDone: boolean;
  isExecutionFail: boolean;
  isLoading: boolean;
}

export interface IpsItem {
  ip: string;
  alias: string;
  model: string;
  exeStatus: string;
  exeStatusDisplay: string;
}

export interface ApplyIpsConfig {
  deviceList: IpsItem[];
  enableIps: IpsTypes;
  operationMode: IpsOperationModeTypes;
}

export interface IpsConfigForApi {
  target: Target[];
  meta: string;
  json: IpsInfo;
  command: string;
  stop: boolean;
}


export type IpsTypes ='enable' | 'disable';
export type IpsOperationModeTypes =  'prevention' | 'detection';
 interface IpsInfo {
  ips: IpsTypes;
  operation_mode: IpsOperationModeTypes;
}

 interface Target {
  ip: string;
  order: number;
}


export interface WsExecuteStatus {
  site_id: string;
  ip: string;
  meta: string;
  status: string;
  result: string;
}


export enum executionStatus {
  NotSent = 'Not sent',
  InProgress = 'in progress',
  Finished = 'done',
  Failed = 'failed',
  IpNotFound = 'ip not found',
}

export enum executeCliFailureType {
  LoginFailure = 'login failure',
  SSHNotSupported = 'failed to connect SSH client',
  ConnectionFailure = 'connection failure',
  HandshakeFailure = 'handshake failure',
  ReachMaximumSsid = 'reached the maximum number of SSIDs',
  PortLimit = 'The number of Port Security',
  SNMPConfigurationMismatch = 'fail when set properties',
  CliSessionTimeout = 'cli session timeout',
  UnableToSetPort = 'error port selection',
  UnknownError = 'Unknown Error',
  LicenseNotActivated = 'Cannot enable or disable IPS feature - license not activated',
  IpsNotSupport = 'not support this feature on this platform'
}
