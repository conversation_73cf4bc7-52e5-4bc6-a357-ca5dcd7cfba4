import { Inject, Injectable, Injector, NgZone } from '@angular/core';
import { Router } from '@angular/router';

import { TranslateService } from '@ngx-translate/core';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, Observable, Subject, forkJoin } from 'rxjs';

import { EventLogService } from '../../pages/firewall-policy-management/event-log.service';
import {
  License,
  LicenseAction,
  LicenseCategory,
  LicenseRemarkType,
  LicenseStatus,
} from '../../pages/firewall-policy-management/license.model';
import { SessionTimerService } from '../../shared/security-service/session-timer.service';
import { EulaComponent } from '../eula/eula.component';
import { PostService } from '../http/post.service';
import { SnackBarService } from '../security-service/snack-bar.service';
import { SystemInfo } from '../shared.model';
import { SimpleDialogService } from '../simple-dialog/simple-dialog.service';
import { SocketService } from '../socket/socket.service';
import { BulletinData, BulletinType } from './bulletin.model';
import { AutoEventLogRefresh, PreferenceModel } from './preference.model';
import { Role, RoleName, User } from './user.model';
import {License as IpsLicense} from '../../pages/license/models/license.model'

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  user = new BehaviorSubject<User>(null);
  userPreference = new BehaviorSubject<PreferenceModel>(null);
  isMainLicenseValid = new BehaviorSubject<boolean>(null);

  private _redirect: boolean;
  private _isLogin = new Subject<boolean>();
  private _nsmLicense = new BehaviorSubject<License>(null);
  private _ipsLicense = new BehaviorSubject<IpsLicense>(null);
  private systemInfo: SystemInfo;

  constructor(
    @Inject(Injector) private _injector: Injector,
    // private _activatedRoute: ActivatedRoute,
    private _postService: PostService,
    private _router: Router,
    private _sessionTimerService: SessionTimerService,
    private _simpleDialogService: SimpleDialogService,
    private _socketService: SocketService,
    private _snackBar: SnackBarService,
    private _translate: TranslateService,
    private _eventLogService: EventLogService,
    private ngZone: NgZone
  ) {}

  // Need to get ToastrService from injector rather than constructor injection to avoid cyclic dependency error
  private get _toast(): ToastrService {
    return this._injector.get(ToastrService);
  }

  get nsmLicense(): License {
    return this._nsmLicense.getValue();
  }

  set nsmLicense(value: License) {
    this._nsmLicense.next(value);
  }

  set ipsLicense(value: IpsLicense) {
    this._ipsLicense.next(value);
  }

  get SystemInfo(): SystemInfo {
    return this.systemInfo;
  }

  login(username: string, password: string): void {
    this._postService.sendPost('login', { username, password }).subscribe(data => {
      this._redirect = true;
      this.createUser(data.user, data.token);
    });
  }

  autoLogin(): void {
    const token = sessionStorage.getItem('token');
    if (!token) {
      this._router.navigate(['/login']).then();
      return;
    }

    this._postService.sendGet('users/me').subscribe(data => {
      this._redirect = false;
      this.createUser(data, token);
    });
  }

  logout(): void {
    this.user.next(null);
    this._isLogin.next(false);
    this._sessionTimerService.stop();
    this._eventLogService.stopEventTimer();
    this._simpleDialogService.closeAll();
    this._toast.clear();
    this._socketService.disconnectSocket();
    this.ngZone.run(() => {
      this._router.navigate(['/login']);
    });
  }

  loginStatus(): Observable<boolean> {
    return this._isLogin.asObservable();
  }

  clearNsmRebind(): void {
    this._nsmLicense.next(new License(LicenseCategory.NSM, this._nsmLicense.getValue().status, ''));
  }

  private createUser(userData: any, token: string): void {
    const userRole: Role = {
      roleId: userData.role.roleId,
      description: userData.role.description,
      name: userData.role.name,
      permissionList: userData.role.permissionList,
    };
    const newUser = new User(
      userData.userId,
      userData.username,
      userData.description,
      userData.isRoot,
      userData.resetMode,
      userRole,
      token,
      userData.autoLogoutTime,
      userData.signEULA
    );
    this.user.next(newUser);

    sessionStorage.clear();

    // 如果 EULA 沒有簽署，不能進入系統。
    if (!userData.signEULA) {
      this.signEula().subscribe(signEulaResult => {
        if (signEulaResult) {
          this.updateUserEULA(signEulaResult);
          this.getLicenseAndPreference();
        } else {
          this.logout();
        }
      });
    } else {
      this.getLicenseAndPreference();
    }
  }

  /**
   * sign EULA
   */
  signEula(): Observable<boolean> {
    this._simpleDialogService.openLDialog({
      component: EulaComponent,
    });

    return this._simpleDialogService.dialogRef.afterClosed();
  }

  updateUserEULA(signEULA: boolean): void {
    const currentUser = this.user.getValue();

    this.user.next(
      new User(
        currentUser.userId,
        currentUser.username,
        currentUser.description,
        currentUser.isRoot,
        currentUser.resetMode,
        currentUser.role,
        currentUser.token,
        currentUser.autoLogoutTime,
        signEULA
      )
    );
  }

  private getLicenseAndPreference(): void {
    forkJoin([
      this._postService.sendGet('system/preference'),
      this._postService.sendGet('system/bulletin'),
      this._postService.sendGet('licenses/overview'),
      this._postService.sendGetHaveInterface<SystemInfo>('system/info'),
    ]).subscribe(
      ([preferenceData, bulletinData, licensesData, systemInfo]: [
        {
          autoLogoutTime: number;
          autoRefreshTime: number;
          eventLog: AutoEventLogRefresh;
        },
        BulletinData[],
        any,
        SystemInfo
      ]) => {
        this.userPreference.next(
          new PreferenceModel(preferenceData.autoLogoutTime, preferenceData.autoRefreshTime, preferenceData.eventLog)
        );
        this._sessionTimerService.autoLogoutTime = preferenceData.autoLogoutTime;
        this.systemInfo = systemInfo;
        if (systemInfo.upgradeFirmware) {
          this._toast.warning(
            this._translate.instant('pages.system.license.remarkUpdateFw', {
              version: systemInfo?.firmwareVersion ?? '',
            })
          );
        }
        bulletinData.map(data => {
          this._toast.error(this._translate.instant('general.bulletin.' + BulletinType[data.msgType]));
        });
        for (const item of licensesData) {
          const licenseContent = item.overview;
          if (item.category === LicenseCategory.NSM) {
            this.nsmLicense = new License(LicenseCategory.NSM, licenseContent.status, licenseContent.action);
            this.isMainLicenseValid.next(licenseContent.status === LicenseStatus.Valid);
            if (licenseContent.action === LicenseAction.Rebind) {
              this.rebindNSM(licenseContent);
            }
            this.showRemark(
              licenseContent.remark,
              this._translate.instant('pages.system.license.nsm'),
              licenseContent.duration
            );
          } else if (item.category === LicenseCategory.IPS) {
            this.ipsLicense = new IpsLicense(LicenseCategory.IPS, licenseContent.status, licenseContent.action);
            // if (licenseContent.action === LicenseAction.Rebind) {
            //   this.rebindIPS(licenseContent);
            // }
            this.showRemark(licenseContent.remark, LicenseCategory.IPS, licenseContent.duration);
          }
        }
        this._isLogin.next(true);
        if (this._redirect) {
          this._router
            .navigate(['/pages/dashboard'])
            .then(() => this._snackBar.openSuccessSnackBar('login.loginSuccess'));
        }
        this._sessionTimerService.start();
      }
    );
  }

  updatePreference(preference: PreferenceModel): void {
    this.userPreference.next(preference);
  }

  preference(): Observable<PreferenceModel> {
    return this.userPreference.asObservable();
  }

  updateSystemInfo(systemInfo: SystemInfo): void {
    this.systemInfo = systemInfo;
  }

  private rebindNSM(licenseContent: any): void {
    if (this.user.getValue().role.name === RoleName.Admin) {
      this._redirect = false;
      this._router.navigate(['/pages/device-deployment']).then(() => {
        this._simpleDialogService.openSDialog({
          title: 'general.dialog.warning',
          content: this._translate.instant('pages.system.license.nsmNodeErrorMsg', {
            nodes: licenseContent.node,
            devices: licenseContent.usedNode,
          }),
          showLeftBtn: false,
        });
        this._snackBar.openSuccessSnackBar('login.loginSuccess');
      });
    } else {
      this.logout();
      this._simpleDialogService.openSDialog({
        title: 'general.dialog.warning',
        content: this._translate.instant('pages.system.license.nodeErrorMsg', {
          category: LicenseCategory.NSM,
        }),
        showLeftBtn: false,
      });
    }
  }

  private rebindIPS(licenseContent: any): void {
    if (this.user.getValue().role.name === RoleName.Admin) {
      this._redirect = false;
      this._router
        .navigate(['/pages/system/license'], {
          queryParams: { tab: 'ips' },
        })
        .then(() =>
          this._simpleDialogService.openSDialog({
            title: 'general.dialog.warning',
            content: this._translate.instant('pages.system.license.ipsNodeErrorMsg', {
              nodes: licenseContent.node,
              devices: licenseContent.usedNode,
            }),
            showLeftBtn: false,
          })
        );
    } else {
      this._simpleDialogService.openSDialog({
        title: 'general.dialog.warning',
        content: this._translate.instant('pages.system.license.nodeErrorMsg', {
          category: LicenseCategory.IPS,
        }),
        showLeftBtn: false,
      });
    }
  }

  private showRemark(remarkType: string[] = [], category: string, duration: number): void {
    if (remarkType.includes(LicenseRemarkType.Expiring)) {
      this._toast.warning(
        this._translate.instant('pages.system.license.remarkExpiringMsg', {
          category: category,
          days: duration,
        })
      );
    }
    if (remarkType.includes(LicenseRemarkType.Expired)) {
      this._toast.warning(
        this._translate.instant('pages.system.license.remarkExpiredMsg', {
          category: category,
        })
      );
    }

    if (remarkType.includes(LicenseRemarkType.Insufficient) && category !== LicenseCategory.NSM) {
      this._toast.warning(
        this._translate.instant('pages.system.license.insufficientMsg', {
          category: category,
        })
      );
    }
  }
}
