import {
  Attribute,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { SelectionModel } from '@angular/cdk/collections';
import { MatLegacyPaginator as MatPaginator } from '@angular/material/legacy-paginator';
import { MatLegacyTableDataSource as MatTableDataSource } from '@angular/material/legacy-table';
import { MatSidenav } from '@angular/material/sidenav';
import { MatSort } from '@angular/material/sort';

import { LangChangeEvent, TranslateService } from '@ngx-translate/core';
import * as _ from 'lodash';

import { AppState } from '../../../app.service';
import { GlobalEvent, GlobalEventType } from '../../../global.event';
import { GlobalService } from '../../../shared/Service/global-services';
import { NetworkService } from '../network.service';
import {
  Action,
  DataType,
  Severity,
  TopologyUpdateDefine,
  SecurityAddonState,
} from './../../../shared/Service/mx-platform/DataDef/DataTypeDefinitions';
import { DeviceUnit } from './../../../shared/Service/mx-platform/DataDef/DevicesDataDefs';
import { DataService } from './../../../shared/Service/mx-platform/Service/DataService';
import { MxCommandBarComponent } from './../../../shared/component/mx-command-bar/mx-command-bar.component';
import { convertIpToNumber } from '@mxview-web/app/shared/util/convertIp'

@Component({
  selector: 'app-mx-device-table',
  styleUrls: ['./device.component.scss'],
  templateUrl: './device.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DeviceTableComponent implements OnInit, OnChanges, OnDestroy {
  @Input() sideNav: MatSidenav;
  @Input() commandBar: MxCommandBarComponent;
  @Input() siteId: any;
  @Input() groupId: any;
  @Input() fullMode = false;
  @Input() onSelectedSingleDevice: DeviceUnit;
  @Input() deviceSeverity: string;
  @Input() isFromDeviceManagement!: boolean;
  @Output() deviceSelected = new EventEmitter<any>();
  @Output() deviceNotSelected = new EventEmitter<any>();
  @Output() deviceMultiSelected = new EventEmitter<any>();
  @Output() deviceOnFocused = new EventEmitter<any>();
  @Output() refreshDeviceData = new EventEmitter<any>();

  @ViewChild('deviceTableSort') deviceTableSort: MatSort;
  @ViewChild('deviceTablePaginator') deviceTablePaginator: MatPaginator;
  deviceTableDisplayedColumns: string[] = [
    'select',
    'alias',
    'model',
    'ip-address',
    'mac-address',
    'firmware-version',
    'location',
    'dummy',
  ];
  deviceTableDataSource: MatTableDataSource<any> = new MatTableDataSource();
  deviceTableData = [];
  deviceTableDataLength;
  devicePanelData = [];
  devicePanelPageSize = 20;
  selection = new SelectionModel<any>(true, []);
  // for ngx-table
  public deviceList = [];
  public isShowDevicePropertiesPanel = false;
  @ViewChild('filterInput') filterInput: any;
  @ViewChild('inforCellTemplate') inforTemplate: any;

  severityItems = [];
  showQueryPanel = false;
  querySeverity: Severity;
  sdsSysObjectIds = [
    // '.*******.4.1.8691.23.1.1',
    // '.*******.4.1.8691.23.1.3',
    // '.*******.4.1.8691.23.1.2',
    '.*******.4.1.8691.23.1',
  ];
  hasSecurityAddon = false;

  private selectedSiteId: string;
  private selectedGroupId: string;
  private isInit = false;
  private selectedDevice: DeviceUnit;
  private isInQueryMode = false;
  private isFromDashboard = false;

  constructor(
    public globalService: GlobalService,
    private appState: AppState,
    private networkService: NetworkService,
    private translateService: TranslateService,
    private dataService: DataService,
    private cdr: ChangeDetectorRef,
    @Attribute('format') format
  ) {
    translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.setTableConfig();
    });
    this.isInit = true;
  }

  ngOnInit(): void {
    this.setTableConfig();
    this.hasSecurityAddon = this.globalService.siteInfo.now.addons.includes('nsm');

    if (this.hasSecurityAddon) {
      this.deviceTableDisplayedColumns.splice(2, 0, 'mxsec_flag');
    }

    // Setup custom data accessor to ensure MAC addresses are always formatted
    this.deviceTableDataSource.data = [];
    this.deviceTableDataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'ip-address':
          return convertIpToNumber(item.ip);
        case 'mac-address':
          // Format the MAC before using it for sorting
          return this.getFormattedMACAddress(item.mac);
        case 'firmware-version':
          return item.firmwareVersion;
        default:
          return item[property];
      }
    };
  }

  ngOnChanges(changes): void {
    if (changes.deviceSeverity !== undefined && changes.deviceSeverity.currentValue !== undefined) {
      switch (changes.deviceSeverity.currentValue) {
        case 'critical':
          this.querySeverity = Severity.EVENT_SEVERITY_CRITICAL;
          break;
        case 'warning':
          this.querySeverity = Severity.EVENT_SEVERITY_WARNING;
          break;
        case 'information':
          this.querySeverity = Severity.EVENT_SEVERITY_INFO;
          break;
        default:
          this.querySeverity = undefined;
      }
      this.selectedSiteId = localStorage.getItem('siteId');
      this.selectedGroupId = undefined;
      this.isFromDashboard = true;
      if (this.selectedSiteId === '-1') {

        this.selectedGroupId = '-1';
        this.updateDeviceTableData(undefined, undefined, true);
      } else {
        this.updateDeviceTableData(this.selectedSiteId, undefined, true);
      }
    } else {
      if (changes.siteId !== undefined || changes.groupId !== undefined) {
        this.selectedSiteId = localStorage.getItem('siteId');
        this.selectedGroupId = localStorage.getItem('groupId');
        if (this.siteId && this.groupId !== undefined) {
          this.selectedSiteId = this.siteId;
          this.selectedGroupId = this.groupId;
        } else {
          this.selectedSiteId = localStorage.getItem('siteId');
          this.selectedGroupId = localStorage.getItem('groupId');
        }
        if (this.selectedSiteId === '-1') {
          this.selectedGroupId = '-1';
          this.updateDeviceTableData();
        } else {
          this.updateDeviceTableData(this.selectedSiteId, Number(this.selectedGroupId));
        }
      }
    }
  }

  updateDeviceTableData(siteId?: string, groupId?: number, fromDashboard?: boolean): void {
    setTimeout(() => {
      this.deviceList.length = 0;
      this.cdr.markForCheck();
      // get all device data
      let siteDeviceData;
      let devicePromise;
      if (this.selectedSiteId === '-1') {
        devicePromise = this.dataService.getDevice();
      } else {
        devicePromise = this.dataService.getDevice(this.selectedSiteId);
      }
      Promise.all([devicePromise])
        .then(result => {
          siteDeviceData = result[0];
          if (siteDeviceData instanceof Array) {
            if (groupId !== undefined && !this.isFromDeviceManagement) {
              this.deviceList = _.filter(siteDeviceData, device => {
                return device.group === groupId;
              });
            } else {
              this.deviceList = siteDeviceData;
            }

            for (const element of this.deviceList) {
              const device = element;
              if (groupId !== undefined && device.group !== groupId) {
                continue;
              }
              if (element.device_components !== undefined && element.device_components.firmwareVersion !== undefined) {
                device.firmwareVersion = element.device_components.firmwareVersion.status;
              } else {
                device.firmwareVersion = '';
              }
              // if ((element.device_components !== undefined) && (element.device_components.Availability !== undefined)) {
              //   device.availability = element.device_components.Availability.status;
              // } else {
              //   device.availability = '';
              // }
              device.mac = this.globalService.formatMACAddress(element.mac);
              device.location = element.location;
              device.id = element.id;
              device.site_id = element.site_id;
              if (
                element.device_components !== undefined &&
                element.device_components.enableAutoIpConfig !== undefined
              ) {
                device.enableAutoConfig = element.device_components.enableAutoIpConfig.status;
              }
            }

            if (this.isFromDashboard) {
              if (this.querySeverity) {
                this.deviceList = _.filter(this.deviceList, device => {
                  return device.severity === this.querySeverity;
                });
              }
            }
            this.deviceList = [...this.globalService.sortIp(this.deviceList)];
            this.deviceTableData = this.deviceList;
            this.devicePanelData = this.deviceTableData.slice(0, this.devicePanelPageSize);
            this.deviceTableDataLength = this.deviceTableData.length;
            this.deviceTableDataSource.sort = this.deviceTableSort;
            this.deviceTableDataSource.paginator = this.deviceTablePaginator;
            this.deviceTableDataSource.sortingDataAccessor = (item, property) => {
              switch (property) {
                case 'ip-address':
                  return convertIpToNumber(item.ip);
                case 'mac-address':
                  return this.getFormattedMACAddress(item.mac);
                case 'firmware-version':
                  return item.firmwareVersion;
                default:
                  return item[property];
              }
            };
            this.deviceTableDataSource.data = this.deviceTableData;
            this.selection.clear();
            this.setupTableFilter();
            this.cdr.markForCheck();
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            if (this.isInit) {
              if (this.isFromDashboard) {
                this.isInit = false;
              } else {
                this.subscribeUpdatedData();
                this.isInit = false;
              }
            }
          }
        })
        .catch(() => {
          console.log('cant get device data in device table');
        });
    }, 50);
  }

  setTableConfig(): void {
    this.severityItems.push({
      value: Severity.EVENT_SEVERITY_CRITICAL,
      display: this.translateService.instant('EVENT.severity.critical'),
    });
    this.severityItems.push({
      value: Severity.EVENT_SEVERITY_WARNING,
      display: this.translateService.instant('EVENT.severity.warning'),
    });
    this.severityItems.push({
      value: Severity.EVENT_SEVERITY_INFO,
      display: this.translateService.instant('EVENT.severity.information'),
    });
  }

  ipComparator(propA, propB): number {
    console.log('Sorting IP Comparator', propA, propB);
    // Just a simple sort function comparisons
    if (this.globalService.ip2int(propA) < this.globalService.ip2int(propB)) {
      return -1;
    }

    if (this.globalService.ip2int(propA) > this.globalService.ip2int(propB)) {
      return 1;
    }
  }

  addOrUpdateFromDeviceTable(updateDeviceData: any): void {
    let isFindInPage = false;
    if (this.isInQueryMode) {
      return;
    }
    for (const element of updateDeviceData.deviceData) {
      let groupId = 0;
      let siteId = '';
      if (this.selectedSiteId === '-1' && this.selectedGroupId === '-1') {
        groupId = -1;
        siteId = element.site_id;
      } else {
        groupId = element.group;
        siteId = this.selectedSiteId;
      }
      if (this.selectedSiteId !== '-1') {
        if (element.site_id !== this.selectedSiteId) {
          continue;
        }
      }

      // means user change devices to another group so delete devices in this group
      if (groupId !== Number(this.selectedGroupId) && !this.isFromDeviceManagement) {
        this.removeFromDeviceTable(updateDeviceData);
        continue;
      }
      const original = _.filter(this.deviceList, device => {
        return device.id === element.id && device.site_id === siteId;
      });

      if (original[0] === undefined && (groupId === Number(this.selectedGroupId) || this.isFromDeviceManagement)) {
        isFindInPage = true;
        const device = element;
        device.alias = element.alias;
        device.ip = element.ip;
        device.location = element.location;
        device.mac = this.globalService.formatMACAddress(element.mac);
        device.id = element.id;
        if (element.device_components !== undefined) {
          if (element.device_components.firmwareVersion !== undefined) {
            device.firmwareVersion = element.device_components.firmwareVersion.status;
          }
        }
        // if (element.device_components !== undefined) {
        //   if (element.device_components.Availability !== undefined) {
        //     device.availability = element.device_components.Availability.status;
        //   }
        // }
        device.severity = element.severity;
        device.site_id = element.site_id;
        this.deviceList.push(device);
        this.deviceTableDataSource.data = this.deviceList;
        continue;
      }

      if (groupId === Number(this.selectedGroupId) || this.isFromDeviceManagement) {
        let index1 = -1;
        index1 = _.findIndex(this.deviceList, device => {
          return device.id === element.id && device.site_id === siteId;
        });
        if (this.deviceList.length > 0 && index1 !== -1) {
          // if ((updateDeviceData.triggerMessage.datatype === DataType.AVAILABILITY)
          //   && (updateDeviceData.triggerMessage.data.key3 !== undefined)
          //   && this.deviceList[index1].device_components.Availability !== undefined
          //   && this.deviceList[index1].device_components.Availability.status !== undefined
          //   && (this.deviceList[index1].device_components.Availability.status !== updateDeviceData.triggerMessage.data.key3)) {
          //   this.deviceList[index1].availability = updateDeviceData.triggerMessage.data.key3;
          //   this.deviceList[index1].device_components.Availability.status = updateDeviceData.triggerMessage.data.key3;
          //   this.refreshDeviceData.emit(this.deviceList[index1]);
          // }
          this.deviceList[index1].model = element.model;
          if (element.alias !== undefined && element.alias !== null) {
            this.deviceList[index1].alias = element.alias;
            this.deviceList[index1].alias = this.deviceList[index1].alias;
          } else {
            this.deviceList[index1].alias = element.alias;
          }
          this.deviceList[index1].location = element.location;
          this.deviceList[index1].status = element.status;
          this.deviceList[index1].ip = element.ip;
          this.deviceList[index1].mac = this.globalService.formatMACAddress(element.mac);
          this.deviceList[index1].icon_id = element.icon_id;
          this.deviceList[index1].redundancy = element.redundancy;
          this.deviceList[index1].router_role = element.router_role;
          this.deviceList[index1].type = element.type;
          this.deviceList[index1].forceId = element.forceId;
          this.deviceList[index1].neighborDeviceCount = element.neighborDeviceCount;
          this.deviceList[index1].device_group = element.device_group;
          if (element.device_components !== undefined) {
            this.deviceList[index1].device_components = element.device_components;
            if (element.device_components.firmwareVersion !== undefined) {
              this.deviceList[index1].firmwareVersion = element.device_components.firmwareVersion.status;
            }
            // if (element.device_components.Availability !== undefined) {
            //   this.deviceList[index1].availability = element.device_components.Availability.status;
            // }
          }
          this.deviceList[index1].severity = element.severity;
          this.deviceTableDataSource.data = this.deviceList;
        }
      }
    }
  }

  removeFromDeviceTable(updateDeviceData: any): void {
    const isFindInPage = false;
    if (updateDeviceData.triggerMessage.data !== undefined) {
      const index = _.findIndex(this.deviceList, device => {
        return device.id === updateDeviceData.triggerMessage.data.key2;
      });

      if (index !== -1) {
        this.deviceList = _.reject(this.deviceList, device => {
          return device.id === updateDeviceData.triggerMessage.data.key2;
        });
        this.selection.clear();
        this.deviceTableDataSource.data = this.deviceList;
        if (this.sideNav?.opened) {
          this.closeSideBar();
          this.deviceNotSelected.emit();
        }
      }
    }
  }

  subscribeUpdatedData(): void {
    // register eventBus to get updated device data, when get updated device data then put them to all device data set
    this.networkService.updateDeviceData(DataType.GROUP, DataType.AVAILABILITY).subscribe(updateDeviceData => {
      if (
        updateDeviceData.triggerMessage.datatype === DataType.DEVICE ||
        updateDeviceData.triggerMessage.datatype === DataType.AVAILABILITY
      ) {
        switch (updateDeviceData.triggerMessage.action) {
          case Action.NEW:
          case Action.UPDATE:
            this.addOrUpdateFromDeviceTable(updateDeviceData);
            break;
          case Action.REMOVE:
            this.removeFromDeviceTable(updateDeviceData);
            break;
          default:
        }
      }
    });

    this.networkService.registerTopologyventBus().subscribe(updateDeviceData => {
      if (
        updateDeviceData.triggerMessage.datatype === DataType.TOPOLOGY_UPDATE &&
        Number(updateDeviceData.triggerMessage.data.key2) === TopologyUpdateDefine.TOPOLOGY_REMOVE_ALL_DEVICE
      ) {
        this.clearAllDeviceData();
        this.deviceNotSelected.emit();
      }
    });
  }

  ngOnDestroy(): void {
    sessionStorage.setItem('isFromDashboard', '0');
    this.networkService.unsubscribeTrigger();
  }

  openSideBar(): void {
    // Close device properties
    this.sideNav?.open();
    this.commandBar.openSide();
  }

  closeSideBar(): void {
    // Close device properties
    this.sideNav?.close();
    this.commandBar.closeSide();
  }

  clearAllDeviceData(): void {
    this.deviceList.length = 0;
    this.cdr.markForCheck();
  }

  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.deviceTableDataSource.data.length;
    const selected = this.selection.selected;
    return numSelected === numRows;
  }

  masterToggle(): void {
    this.isAllSelected()
      ? this.selection.clear()
      : this.deviceTableDataSource.data.forEach(row => this.selection.select(row));
    const selected = this.selection.selected;
    this.isAllSelected() ? this.deviceMultiSelected.emit(selected) : this.deviceNotSelected.emit();
  }

  setupTableFilter(): void {
    this.deviceTableDataSource.filterPredicate = (data: any, filter: string): boolean => {
      return (
        String(data.alias).toLowerCase().indexOf(filter) !== -1 ||
        String(data.ip).toLowerCase().indexOf(filter) !== -1 ||
        String(data.mac).toLowerCase().indexOf(filter) !== -1 ||
        // String(data.availability).toLowerCase().indexOf(filter) !== -1 ||
        String(data.firmwareVersion).toLowerCase().indexOf(filter) !== -1 ||
        String(data.location).toLowerCase().indexOf(filter) !== -1
      );
    };
  }

  updateFilter(filterValue): void {
    this.deviceTableDataSource.filter = filterValue.trim().toLowerCase();
    if (this.deviceTableDataSource.filteredData.length > 0) {
      this.isInQueryMode = true;
    } else {
      this.isInQueryMode = false;
    }
  }
  onDevicePageChange(event): void {
    this.devicePanelData = this.deviceTableData.slice(
      this.devicePanelPageSize * event.pageIndex,
      this.devicePanelPageSize * event.pageIndex + this.devicePanelPageSize
    );
  }

  selectionToggle(row): void {
    this.selection.toggle(row);
    const selected = this.selection.selected;
    if (selected.length > 1) {
      this.deviceMultiSelected.emit(selected);
      if (this.sideNav?.opened) {
        this.closeSideBar();
      }
    } else if (selected.length === 1) {
      const selectDevice = selected[0];
      this.deviceSelected.emit({ device: selectDevice });
      this.dataService
        .getDevice(selectDevice.site_id, selectDevice.ip, selectDevice.id)
        .then(result => {
          if (result[0] !== undefined) {
            let index1 = -1;
            this.deviceSelected.emit({ device: result[0] });
            this.selectedDevice = result[0];
            index1 = _.findIndex(this.deviceList, device => {
              return device.id === result[0].id && device.site_id === result[0].site_id;
            });
            if (this.deviceList.length > 0 && index1 !== -1) {
              this.deviceList[index1].model = result[0].model;
              this.deviceList[index1].alias = result[0].alias;
              this.deviceList[index1].location = result[0].location;
              this.deviceList[index1].status = result[0].status;
              this.deviceList[index1].ip = result[0].ip;
              this.deviceList[index1].mac = this.globalService.formatMACAddress(result[0].mac);
              this.deviceList[index1].icon_id = result[0].icon_id;
              this.deviceList[index1].redundancy = result[0].redundancy;
              this.deviceList[index1].router_role = result[0].router_role;
              this.deviceList[index1].type = result[0].type;
              this.deviceList[index1].forceId = result[0].forceId;
              this.deviceList[index1].neighborDeviceCount = result[0].neighborDeviceCount;
              if (result[0].device_components !== undefined) {
                this.deviceList[index1].device_components = result[0].device_components;
                if (result[0].device_components.firmwareVersion !== undefined) {
                  this.deviceList[index1].firmwareVersion = result[0].device_components.firmwareVersion.status;
                } else {
                  this.deviceList[index1].firmwareVersion = '';
                }
                // if (result[0].device_components.Availability !== undefined) {
                //   this.deviceList[index1].availability = result[0].device_components.Availability.status;
                // } else {
                //   this.deviceList[index1].availability = '';
                // }
              } else {
                this.deviceList[index1].firmwareVersion = '';
                // this.deviceList[index1].availability = '';
              }
              this.deviceList[index1].severity = result[0].severity;
              this.deviceTableDataSource.data = this.deviceList;
            }
            this.showDeviceProperties();
          }
        })
        .catch(error => {
          console.log('get device properteis error=' + JSON.stringify);
        });
    } else if (selected.length === 0) {
      this.deviceNotSelected.emit();
    }
  }

  showDeviceProperties(): void {
    if (!this.sideNav?.opened && this.selectedDevice !== undefined) {
      this.openSideBar();
    }
  }

  getBorderSeverity(row): string {
    let severityClass = '';
    switch (row.severity) {
      case Severity.EVENT_SEVERITY_WARNING:
        severityClass = 'td-warning-severity';
        break;
      case Severity.EVENT_SEVERITY_CRITICAL:
        severityClass = 'td-critical-severity';
        break;
      case Severity.EVENT_SEVERITY_SYSTEM_INFO:
        severityClass = 'td-system-info-severity';
        break;
      case Severity.EVENT_SEVERITY_INFO:
      default:
        severityClass = 'td-info-severity';
    }
    return severityClass;
  }

  queryButtonClick(): void {
    this.showQueryPanel = !this.showQueryPanel;
  }

  closeQueryButtonClick(): void {
    this.showQueryPanel = false;
  }

  onClickReset(): void {
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    setTimeout(() => {
      this.isInQueryMode = false;
      if (this.isFromDashboard) {
        if (this.selectedSiteId === '-1') {
          this.selectedGroupId = '-1';
          this.subscribeUpdatedData();
          this.updateDeviceTableData(undefined, undefined, true);
        } else {
          this.updateDeviceTableData(this.selectedSiteId, undefined, true);
        }
        this.isFromDashboard = false;
      } else {
        this.deviceTableDataSource.data = this.deviceList;
      }
      this.cdr.markForCheck();
      this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
    }, 100);
    this.showQueryPanel = false;
  }

  onClickQueryButton(): void {
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    setTimeout(() => {
      this.cdr.markForCheck();
      if (this.querySeverity !== undefined) {
        const filterArray = _.filter(this.deviceList, device => {
          return device.severity === this.querySeverity;
        });
        this.deviceTableDataSource.data = filterArray;
        this.isInQueryMode = true;
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
      } else {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
      }
    }, 100);
    this.showQueryPanel = false;
  }

  exportCSV(): void {
    let csvContent =
      '"' +
      this.translateService.instant('DEVICE.properties.severity') +
      '","' +
      this.translateService.instant('DEVICE.properties.device_alias') +
      '","' +
      (this.hasSecurityAddon ? this.translateService.instant('DEVICE.properties.mxsec_flag') + '","' : '') +
      this.translateService.instant('BASIC_INFORMATION.model') +
      '","' +
      this.translateService.instant('DEVICE.properties.device_ip') +
      '","' +
      this.translateService.instant('DEVICE.properties.mac_address') +
      '","' +
      this.translateService.instant('DEVICE.properties.firmware_version') +
      '","' +
      this.translateService.instant('DEVICE.properties.location') +
      '"\r\n';

    this.deviceList.forEach(element => {
      let eventSeverity = '';
      switch (element.severity) {
        case Severity.EVENT_SEVERITY_INFO:
          eventSeverity = this.translateService.instant('EVENT.severity.information');
          break;
        case Severity.EVENT_SEVERITY_WARNING:
          eventSeverity = this.translateService.instant('EVENT.severity.warning');
          break;
        case Severity.EVENT_SEVERITY_CRITICAL:
          eventSeverity = this.translateService.instant('EVENT.severity.critical');
          break;
        case Severity.EVENT_SEVERITY_SYSTEM_INFO:
          eventSeverity = this.translateService.instant('EVENT.severity.system_information');
          break;
        default:
      }

      csvContent +=
        '"' +
        eventSeverity +
        '","' +
        this.globalService.escapeDoubleQuotes(element.alias) +
        '","' +
        (this.hasSecurityAddon ? this.getSecurityState(element.mxsec_flag) + '","' : '') +
        element.model +
        '","' +
        element.ip +
        '","' +
        element.mac +
        '","' +
        element.firmwareVersion +
        '","' +
        element.location.replace(/[\n\r]/g, '') +
        '"\r\n';
    });

    this.globalService.downloadFile(
      this.globalService.getCsvName('device_table'),
      new Blob([new Uint8Array([0xef, 0xbb, 0xbf]), csvContent], { type: 'text/plain;charset=utf-8' })
    );
  }

  getSecurityState(state: number) {
    switch (state) {
      case SecurityAddonState.REGISTERED:
        return this.translateService.instant('DEVICE.registered_devices');
      case SecurityAddonState.UNREGISTERED:
        return this.translateService.instant('DEVICE.unregistered_devices');
      case SecurityAddonState.NA:
      default:
        return this.translateService.instant('DEVICE.na');
    }
  }

  // Add a helper method to get formatted MAC address or empty string
  getFormattedMACAddress(macAddress: string): string {
    if (!macAddress) return '';

    // Check if MAC is already formatted (contains separators)
    if (macAddress.includes(':') || macAddress.includes('-')) {
      return macAddress;
    }

    // Format the MAC address
    return this.globalService.formatMACAddress(macAddress);
  }
}
