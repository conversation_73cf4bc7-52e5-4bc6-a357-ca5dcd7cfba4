<div class="deviceListBG">
  <div class="padding-right" fxLayout="row" fxLayoutAlign="space-between center">
    <div fxFlex="70">
      <button
        mat-icon-button
        id="query-panel"
        class="tool-bar-first-icon-checkbox-with-padding"
        matTooltip="{{ 'TABLE.filter' | translate }}"
        (click)="queryButtonClick()"
        *ngIf="selection.selected.length === 0"
      >
        <mat-icon class="table-icon-action">filter_list</mat-icon>
      </button>
      <button
        mat-icon-button
        id="export-menu"
        matTooltip="{{ 'TABLE.export' | translate }}"
        [matMenuTriggerFor]="exportMenu"
        *ngIf="selection.selected.length === 0"
      >
        <mat-icon class="table-icon-action">move_to_inbox</mat-icon>
      </button>
      <mat-menu #exportMenu="matMenu" [overlapTrigger]="false" id="frame-export-menu">
        <button mat-menu-item (click)="exportCSV()">
          <span>{{ 'COMBO_BOX.export_csv' | translate }}</span>
        </button>
      </mat-menu>
    </div>
    <div fxFlex="30" class="filter-input-block">
      <mat-form-field class="form-field-search">
        <mat-icon matPrefix class="icon-search">search</mat-icon>
        <input matInput placeholder="{{ 'TABLE.search' | translate }}" (keyup)="updateFilter($event.target.value)" />
      </mat-form-field>
    </div>
  </div>
  <div
    class="query-device-panel-layout"
    fxLayout="column"
    fxLayoutAlign="end"
    fxLayout.xs="column"
    *ngIf="showQueryPanel"
  >
    <div>
      <button id="query-button" (click)="closeQueryButtonClick()">
        <mat-icon id="query-icon-close">close</mat-icon>
      </button>
    </div>
    <div fxLayout="row" fxLayoutWrap fxLayoutGap="20px" fxLayoutAlign="start" fxLayout.xs="column">
      <mat-form-field>
        <mat-select
          id="select-severity-query"
          placeholder="{{ 'EVENT.table_title.severity' | translate }}"
          [(ngModel)]="querySeverity"
        >
          <mat-option
            id="option-severity-query-{{ severityitem.value }}"
            *ngFor="let severityitem of severityItems"
            [value]="severityitem.value"
          >
            {{ severityitem.display }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div class="action-bar" align="end">
      <button id="button-severity-query-reset" color="primary" mat-button (click)="onClickReset()">
        {{ 'BUTTON.reset' | translate }}
      </button>
      <button id="button-severity-query-apply" color="primary" mat-raised-button (click)="onClickQueryButton()">
        {{ 'BUTTON.apply' | translate }}
      </button>
    </div>
  </div>
  <div id="dataContent">
    <div fxShow.xs="true" fxHide="true">
      <mat-expansion-panel class="expansion-panel-wrapper" *ngFor="let row of devicePanelData">
        <mat-expansion-panel-header>
          <mat-panel-description
            >{{ 'DEVICE.properties.mac_address' | translate }}: {{ row.mac }}</mat-panel-description
          >
          <mat-panel-title>{{ 'DEVICE.properties.device_ip' | translate }}: {{ row.ip }}</mat-panel-title>
        </mat-expansion-panel-header>
        <p>{{ 'DEVICE.properties.device_alias' | translate }}: {{ row.alias }}</p>
        <p>
          {{ 'BASIC_INFORMATION.model' | translate }}: {{ row.model }}
          <mat-icon
            *ngIf="sdsSysObjectIds.includes(row.sysobjid)"
            class="form-checkbox-help-tip limited-support-hint"
            matTooltip="{{ 'device_management.limited_support' | translate }}"
            matTooltipPosition="right"
          >
            information
          </mat-icon>
        </p>
        <p>{{ 'DEVICE.properties.device_ip' | translate }}: {{ row.ip }}</p>
        <p>{{ 'DEVICE.properties.mac_address' | translate }}: {{ row.mac }}</p>
        <p>{{ 'DEVICE.properties.availability' | translate }}: {{ row.availability }}</p>
        <p>{{ 'DEVICE.properties.firmware_version' | translate }}: {{ row.firmwareVersion }}</p>
        <p>{{ 'DEVICE.properties.location' | translate }}: {{ row.location }}</p>
      </mat-expansion-panel>
      <mat-paginator
        [length]="deviceTableDataLength"
        [pageSize]="devicePanelPageSize"
        [hidePageSize]="true"
        (page)="onDevicePageChange($event)"
      >
      </mat-paginator>
    </div>
    <div fxShow="true" fxHide.xs="true">
      <div class="app-table-wrapper">
        <table mat-table #deviceTableSort="matSort" matSort [dataSource]="deviceTableDataSource">
          <ng-container matColumnDef="select">
            <th mat-header-cell *matHeaderCellDef class="severity-border">
              <mat-checkbox
                id="checkbox-device-all"
                color="primary"
                (change)="$event ? masterToggle() : null"
                [checked]="selection.hasValue() && isAllSelected()"
                [indeterminate]="selection.hasValue() && !isAllSelected()"
                [disabled]="deviceTableDataLength === 0"
              >
              </mat-checkbox>
            </th>
            <td mat-cell *matCellDef="let row" [ngClass]="getBorderSeverity(row)">
              <mat-checkbox
                color="primary"
                (click)="$event.stopPropagation()"
                id="device-checkbox-{{ row.id }}"
                (change)="$event ? selectionToggle(row) : null"
                [checked]="selection.isSelected(row)"
              >
              </mat-checkbox>
            </td>
          </ng-container>
          <ng-container matColumnDef="alias">
            <th id="th-device-alias" mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'DEVICE.properties.device_alias' | translate }}
            </th>
            <td id="td-device-alias-{{ row.id }}" mat-cell *matCellDef="let row">{{ row.alias }}</td>
          </ng-container>
          <ng-container *ngIf="hasSecurityAddon" matColumnDef="mxsec_flag">
            <th id="th-mxsec-flag" mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'DEVICE.properties.mxsec_flag' | translate }}
            </th>
            <td id="td-mxsec-flag-{{ row.id }}" mat-cell *matCellDef="let row">
              {{ getSecurityState(row.mxsec_flag) }}
            </td>
          </ng-container>
          <ng-container matColumnDef="model">
            <th id="th-model" mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'BASIC_INFORMATION.model' | translate }}
            </th>
            <td id="td-model-{{ row.id }}" mat-cell *matCellDef="let row">
              {{ row.model }}
              <mat-icon
                *ngIf="row.sysobjid.startsWith(sdsSysObjectIds)"
                class="form-checkbox-help-tip limited-support-hint"
                matTooltip="{{ 'device_management.limited_support' | translate }}"
                matTooltipPosition="right"
              >
                information
              </mat-icon>
            </td>
          </ng-container>
          <ng-container matColumnDef="ip-address">
            <th id="th-device-ip" mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'DEVICE.properties.device_ip' | translate }}
            </th>
            <td id="td-device-ip-{{ row.id }}" mat-cell *matCellDef="let row">{{ row.ip }}</td>
          </ng-container>
          <ng-container matColumnDef="mac-address">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="device-table-header">
              {{ 'DEVICE.properties.mac_address' | translate }}
            </th>
            <td mat-cell *matCellDef="let element">
              {{ getFormattedMACAddress(element.mac) }}
            </td>
          </ng-container>
          <ng-container matColumnDef="availability">
            <th id="th-device-availability" mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'DEVICE.properties.availability' | translate }}
            </th>
            <td id="td-device-availability-{{ row.id }}" mat-cell *matCellDef="let row">{{ row.availability }}</td>
          </ng-container>
          <ng-container matColumnDef="firmware-version">
            <th id="th-device-firmware-version" mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'DEVICE.properties.firmware_version' | translate }}
            </th>
            <td id="td-device-firmware-version-{{ row.id }}" mat-cell *matCellDef="let row">
              {{ row.firmwareVersion }}
            </td>
          </ng-container>
          <ng-container matColumnDef="location">
            <th id="th-device-location" mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'DEVICE.properties.location' | translate }}
            </th>
            <td id="td-device-location-{{ row.id }}" mat-cell *matCellDef="let row">{{ row.location }}</td>
          </ng-container>
          <ng-container matColumnDef="dummy">
            <th mat-header-cell *matHeaderCellDef></th>
            <td mat-cell *matCellDef="let row"></td>
          </ng-container>
          <tr
            mat-header-row
            *matHeaderRowDef="deviceTableDisplayedColumns"
            [ngClass]="globalService.getTableHeaderRowHeight()"
          ></tr>
          <tr
            mat-row
            name="tr-device"
            [ngClass]="globalService.getRowSeverity(row)"
            id="tr-device-{{ row.id }}"
            *matRowDef="let row; columns: deviceTableDisplayedColumns"
            [class.selected-row]="selection.isSelected(row)"
          ></tr>
        </table>
      </div>
      <mat-paginator
        #deviceTablePaginator
        [length]="deviceTableDataLength"
        [pageSizeOptions]="[50, 100, 200, 1000]"
        showFirstLastButtons
      >
      </mat-paginator>
    </div>
  </div>
</div>
