<div class="app-no-header-content-container">
  <div class="page-function">
    <app-mx-site-nav
      class="site-nav-layout"
      [isTopologyMode]="isTopologyView"
      (navChange)="onNavChange($event)"
      (loading)="onLoading($event)"
    ></app-mx-site-nav>
    <app-mx-command-bar
      [parentMenuData]="menuData"
      [parentCommandBarKey]="commandBarKey"
      [showGroup]="isShowGroupButton"
      [showAutomationButton]="isShowAutomationButton"
      [isWidget]="isWidget"
      [showMode]="isShowModeButton"
      [showInfo]="isShowInfoButton"
      [disabled]="disabledCommandBar"
      [isTopologyMode]="isTopologyView"
      [topologyViewMode]="topologyViewMode"
      (buttonClick)="onButtonClick($event)"
      (toggleMode)="onToggleMode($event)"
      (toggleGroupBar)="onToggleGroupBar($event)"
      (toggleAutomationButton)="onToggleAutomationButton()"
      (toggleSideBar)="onToggleSideBar($event)"
      (onToggleAutomationButtonSide)="onToggleAutomationButtonSide($event)"
    ></app-mx-command-bar>
    <mat-sidenav-container class="network-container">
      <mat-sidenav #sidenav position="end" mode="side" class="network-sidenav">
        <div *ngIf="showGroup">
          <div class="property-table">
            <mat-list-item>
              <div id="div-group-title" fxLayout="row" fxLayoutAlign="center center" class="sidenav_header">
                {{ 'NETWORK_MENU.group.title' | translate }}
              </div>
              <div
                *ngFor="let groupTraffic of groupTrafficArr"
                class="group-main realtime-event"
                id="div-group-traffic-{{ groupTraffic.groupName }}"
                [ngStyle]="groupTraffic.style"
                fxLayout="column"
              >
                <div class="group-name">{{ groupTraffic.groupName }}</div>
                <div fxLayout="row" fxLayoutAlign="space-between start">
                  <div class="group-traffic" fxLayout="row" fxLayoutAlign="start center">
                    <div class="group-traffic-icon">
                      <mat-icon svgIcon="traffic"></mat-icon>
                    </div>
                    <div
                      id="div-group-traffic-text-{{ groupTraffic.groupName }}"
                      class="group-traffic-text"
                      [style.color]="groupTraffic.color"
                    >
                      {{ groupTraffic.maxUtilization }} %
                    </div>
                  </div>
                  <button
                    id="button-group-traffic-locate-{{ groupTraffic.groupName }}"
                    mat-icon-button
                    fxHide.xs
                    id="bt-ack-single"
                    (click)="focusLink(groupTraffic.link)"
                    matTooltip="{{ 'TABLE.locate' | translate }}"
                  >
                    <mat-icon>pin_drop</mat-icon>
                  </button>
                </div>
              </div>
            </mat-list-item>
          </div>
        </div>
        <div *ngIf="!showGroup">
          <div fxLayout="column" fxLayoutAlign="start center" *ngIf="!site && !group && !device && !link">
            <div>
              <mat-icon class="not-selected-icon">announcement</mat-icon>
            </div>
            <div class="not-selected-text">{{ 'NETWORK.not_selected' | translate }}</div>
            <div class="nodes-info">
              <div class="license-nodes-text" fxFlexAlign="center">
                {{ 'LICENSE.node' | translate }}
                <span [ngClass]="{ 'red-text': networkService.showOverNodes }">{{ networkService.licenseNodes }}</span>
                /
                {{ networkService.licenseMaxNodes }}
              </div>
            </div>
          </div>
          <mat-tab-group
            id="tab-group-network"
            *ngIf="showTab"
            mat-stretch-tabs
            color="primary"
            [(selectedIndex)]="tabIndex"
          >
            <mat-tab id="tab-{{ tabName }}" label="{{ tabName }}">
              <div *ngIf="site" class="property-table">
                <mat-list-item>
                  <div class="property-table-field">
                    <div class="mat-line info-title">{{ 'SITE_PROPERTIES.information' | translate }}</div>
                    <div class="property-table-field-content">
                      <dt mat-line>
                        <span id="dt-site-name">{{ 'SITE_PROPERTIES.name' | translate }}</span>
                      </dt>
                      <dd mat-line>
                        <span id="dd-site-name">{{ site.site_name }}</span>
                      </dd>
                      <dt mat-line>
                        <span id="dt-site-description">{{ 'SITE_PROPERTIES.description' | translate }}</span>
                      </dt>
                      <dd mat-line>
                        <span id="dd-site-description">{{ site.site_description }}</span>
                      </dd>
                      <dt mat-line>
                        <span id="dt-site-devices">{{ 'SITE_PROPERTIES.devices' | translate }}</span>
                      </dt>
                      <dd mat-line>
                        <span id="dd-site-devices">
                          <span class="devices-status-circle notice-color-bg"></span>{{ siteInformationDeviceCount }} /
                          <span class="devices-status-circle warning-color-bg"></span>{{ siteWarningDeviceCount }} /
                          <span class="devices-status-circle critical-color-bg"></span>{{ siteCriticalDeviceCount }}
                        </span>
                      </dd>
                    </div>
                  </div>
                </mat-list-item>
              </div>
              <div id="div-group-properties" *ngIf="group && !group?.device_group" class="property-table">
                <mat-list-item>
                  <div class="property-table-field">
                    <div id="div-group-properties-information" class="mat-line info-title">
                      {{ 'GROUP_PROPERTIES.information' | translate }}
                    </div>
                    <div class="property-table-field-content">
                      <dt mat-line>
                        <span id="dt-group-properties-name">{{ 'GROUP_PROPERTIES.name' | translate }}</span>
                      </dt>
                      <dd mat-line>
                        <span id="dd-group-properties-name">{{ group.name }}</span>
                      </dd>
                      <dt mat-line>
                        <span id="dt-group-properties-description"
                          >{{ 'GROUP_PROPERTIES.description' | translate }}
                        </span>
                      </dt>
                      <dd mat-line>
                        <span id="dd-group-properties-description">{{ group.description }}</span>
                      </dd>
                      <dt mat-line>
                        <span id="dt-group-properties-devices">{{ 'GROUP_PROPERTIES.devices' | translate }}</span>
                      </dt>
                      <dd mat-line>
                        <span id="dd-group-properties-devices">
                          <span
                            id="span-group-properties-notice-color"
                            class="devices-status-circle notice-color-bg"
                          ></span
                          ><span id="span-group-properties-notice-count">{{ groupInformationDeviceCount }}</span> /
                          <span
                            id="span-group-properties-warning-color"
                            class="devices-status-circle warning-color-bg"
                          ></span
                          ><span id="span-group-properties-notice-count">{{ groupWarningDeviceCount }}</span> /
                          <span
                            id="span-group-properties-critical-color"
                            class="devices-status-circle critical-color-bg"
                          ></span
                          ><span id="span-group-properties-notice-count">{{ groupCriticalDeviceCount }}</span>
                        </span>
                      </dd>
                    </div>
                  </div>
                </mat-list-item>
              </div>
              <div *ngIf="device || group?.device_group" class="property-table">
                <mat-list-item>
                  <!-- GOOSE TABLE START -->
                  <div class="goose-property-table-field" *ngIf="device && device?.goose_table !== undefined">
                    <div class="goose-info-title">{{ 'DEVICE_PROPERTIES.goose_table.title' | translate }}</div>
                    <div class="property-table-field-content">
                      <ng-container *ngFor="let item of sortGooseTable(device?.goose_table)">
                        <mat-expansion-panel class="vap-node">
                          <mat-expansion-panel-header class="goose-expansion-panel-header">
                            <div fxLayout="row" fxLayoutAlign="start center">
                              <div
                                [ngClass]="{
                                  'goose-check-item': item.status === '',
                                  'goose-check-item-s': item.status === 'timeout' || item.status === 'tampered'
                                }"
                              >
                                <div class="goose-check-gocb-item" [matTooltip]="item.gocb_name">
                                  {{ item.gocb_name }}
                                </div>
                                <div class="goose-check-content-item">
                                  {{ item.app_id }} / {{ this.globalService.formatMACAddress(item.goose_address) }}
                                </div>
                              </div>
                              <div
                                *ngIf="item.status === 'timeout'"
                                class="goose-message timeout"
                                fxLayout="column"
                                fxLayoutAlign="center center"
                              >
                                {{ 'DIALOG.goose.timeout' | translate }}
                              </div>
                              <div
                                *ngIf="item.status === 'tampered'"
                                class="goose-message tampered"
                                fxLayout="column"
                                fxLayoutAlign="center center"
                              >
                                {{ 'DIALOG.goose.tampered' | translate }}
                              </div>
                            </div>
                          </mat-expansion-panel-header>
                          <ng-template matExpansionPanelContent>
                            <div>
                              <dt mat-line>
                                <span>{{ 'DEVICE_PROPERTIES.goose_table.status' | translate }}</span>
                              </dt>
                              <dd>
                                <span *ngIf="item.status === 'tampered'">{{
                                  'DEVICE_PROPERTIES.goose_table.tampered_port_status'
                                    | translate : { port: item.tampered_port }
                                }}</span>
                                <span *ngIf="item.status !== 'tampered'">{{ item.status | titlecase }}</span>
                              </dd>
                            </div>
                            <div>
                              <dt mat-line>
                                <span>{{ 'DEVICE_PROPERTIES.goose_table.port' | translate }}</span>
                              </dt>
                              <dd>
                                <span>{{ item.port }}</span>
                              </dd>
                            </div>
                            <div>
                              <dt mat-line>
                                <span>{{ 'DEVICE_PROPERTIES.goose_table.vid' | translate }}</span>
                              </dt>
                              <dd>
                                <span>{{ item.vid }}</span>
                              </dd>
                            </div>
                            <div>
                              <dt mat-line>
                                <span>{{ 'DEVICE_PROPERTIES.goose_table.app_id' | translate }}</span>
                              </dt>
                              <dd>
                                <span>{{ item.app_id }}</span>
                              </dd>
                            </div>
                            <div>
                              <dt mat-line>
                                <span>{{ 'DEVICE_PROPERTIES.goose_table.gocb_name' | translate }}</span>
                              </dt>
                              <dd>
                                <span>{{ item.gocb_name }}</span>
                              </dd>
                            </div>
                            <div>
                              <dt mat-line>
                                <span>{{ 'DEVICE_PROPERTIES.goose_table.goose_address' | translate }}</span>
                              </dt>
                              <dd>
                                <span>{{ this.globalService.formatMACAddress(item.goose_address) }}</span>
                              </dd>
                            </div>
                            <div>
                              <dt mat-line>
                                <span>{{ 'DEVICE_PROPERTIES.goose_table.rx_counter' | translate }}</span>
                              </dt>
                              <dd>
                                <span>{{ item.rx_counter }}</span>
                              </dd>
                            </div>
                            <div>
                              <dt mat-line>
                                <span>{{ 'DEVICE_PROPERTIES.goose_table.type' | translate }}</span>
                              </dt>
                              <dd>
                                <span>{{ item.type | titlecase }}</span>
                              </dd>
                            </div>
                          </ng-template>
                        </mat-expansion-panel>
                      </ng-container>
                    </div>
                  </div>
                  <!-- GOOSE TABLE END -->
                  <!-- MMS TABLE START -->
                  <div
                    class="goose-property-table-field"
                    *ngIf="device?.sysobjid?.indexOf(moxaSysobjid) === -1 && device?.mms_info !== undefined"
                  >
                    <div class="goose-info-title">{{ 'DEVICE_PROPERTIES.mms.title' | translate }}</div>
                    <div class="property-table-field-content">
                      <div *ngFor="let data of device.mms_info | keyvalue">
                        <dt mat-line>
                          <span>{{ data.key }}</span>
                        </dt>
                        <dd mat-line>
                          <span>{{ data.value }}</span>
                        </dd>
                      </div>
                    </div>
                  </div>
                  <!-- MMS TABLE END -->
                  <!-- Modbus Device Properties START -->
                  <ng-container
                    *ngIf="
                      device &&
                      device.modbus_info &&
                      device.modbus_info.modbus_enabled &&
                      (device.modbus_info.vendor || device.modbus_info.model || device.modbus_info.revision)
                    "
                  >
                    <div class="property-table-field basic-property-no-device-component">
                      <div id="div-topology-sidenav-section-title-modbus-prop" class="mat-line info-title">
                        {{ 'DEVICE_PROPERTIES.modbus_device_property.title' | translate }}
                      </div>
                      <div class="property-table-field-content">
                        <dt mat-line>
                          <span id="dt-modbus-model">{{
                            'DEVICE_PROPERTIES.modbus_device_property.model' | translate
                          }}</span>
                        </dt>
                        <dd mat-line>
                          <span id="dd-modbus-model">{{ device.modbus_info.model }}</span>
                        </dd>
                        <dt mat-line>
                          <span id="dt-modbus-vendor">{{
                            'DEVICE_PROPERTIES.modbus_device_property.vendor' | translate
                          }}</span>
                        </dt>
                        <dd mat-line>
                          <span id="dd-modbus-vendor">{{ device.modbus_info.vendor }}</span>
                        </dd>
                        <dt mat-line>
                          <span id="dt-modbus-revision"
                            >{{ 'DEVICE_PROPERTIES.modbus_device_property.revision' | translate }}
                          </span>
                        </dt>
                        <dd mat-line>
                          <span id="dd-modbus-revision">{{ device.modbus_info.revision }}</span>
                        </dd>
                      </div>
                    </div>
                  </ng-container>
                  <!-- Modbus Device Properties END -->
                  <!-- Basic Device Properties START -->
                  <div *ngIf="!group?.device_group" class="property-table-field basic-property-no-device-component">
                    <div id="div-topology-sidenav-section-title-basic-prop" class="mat-line info-title">
                      {{ 'DEVICE_PROPERTIES.basic_property.title' | translate }}
                    </div>
                    <div class="property-table-field-content">
                      <dt mat-line>
                        <span id="dt-alias">{{ 'DEVICE_PROPERTIES.basic_property.alias' | translate }}</span>
                      </dt>
                      <dd mat-line>
                        <span id="dd-alias">{{ device.alias }}</span>
                      </dd>
                      <dt mat-line>
                        <span id="dt-model">{{ 'DEVICE_PROPERTIES.basic_property.model_name' | translate }}</span>
                      </dt>
                      <dd mat-line>
                        <span id="dd-model">{{ device.model }}</span>
                      </dd>
                      <dt mat-line>
                        <span id="dt-mac">{{ 'DEVICE_PROPERTIES.basic_property.mac_address' | translate }} </span>
                      </dt>
                      <dd mat-line>
                        <span id="dd-mac">{{ globalService.formatMACAddress(device.mac) }}</span>
                      </dd>
                      <ng-container *ngIf="device?.Availability">
                        <dt mat-line>
                          <span id="dt-availability">{{
                            'DEVICE_PROPERTIES.basic_property.availability' | translate
                          }}</span>
                        </dt>
                        <dd mat-line>
                          <span id="dd-availability">{{ device.Availability }}</span>
                        </dd>
                      </ng-container>
                    </div>
                  </div>
                  <ng-container
                    *ngIf="
                      device &&
                      isShowDeviceComponents &&
                      device.device_components?.sysObjectId !== undefined &&
                      !group?.device_group
                    "
                  >
                    <div class="property-table-field basic-property-device-component">
                      <div class="property-table-field-content">
                        <dt mat-line>
                          <span id="dt-system-description">{{
                            'DEVICE_PROPERTIES.basic_property.system_description' | translate
                          }}</span>
                        </dt>
                        <dd mat-line>
                          <span id="dd-system-description">{{ device.device_components.sysDescr?.status }}</span>
                        </dd>
                        <dt mat-line>
                          <span id="dt-system-system-object-id">{{
                            'DEVICE_PROPERTIES.basic_property.system_object_id' | translate
                          }}</span>
                        </dt>
                        <dd mat-line>
                          <span id="dd-system-system-object-id">{{ device.sysobjid }}</span>
                        </dd>
                        <dt mat-line>
                          <span id="dt-system-contact">
                            {{ 'DEVICE_PROPERTIES.basic_property.system_contact' | translate }}</span
                          >
                        </dt>
                        <dd mat-line>
                          <span id="dd-system-contact">{{ device.device_components.sysContact?.status }}</span>
                        </dd>
                        <dt mat-line>
                          <span id="dt-system-name"
                            >{{ 'DEVICE_PROPERTIES.basic_property.system_name' | translate }}
                          </span>
                        </dt>
                        <dd mat-line>
                          <span id="dd-system-name">{{ device.device_components.sysName?.status }}</span>
                        </dd>
                        <dt mat-line>
                          <span id="dt-system-location">{{
                            'DEVICE_PROPERTIES.basic_property.system_location' | translate
                          }}</span>
                        </dt>
                        <dd mat-line>
                          <span id="dd-system-location">{{ device.device_components.sysLocation?.status }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.serialNumber" mat-line>
                          <span id="dt-serial-number">{{
                            'DEVICE_PROPERTIES.basic_property.serial_number' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.serialNumber" mat-line>
                          <span id="dd-serial-number">{{ device.device_components.serialNumber?.status }}</span>
                        </dd>
                        <dt *ngIf="device.device_components['serialNumber.0']" mat-line>
                          <span id="dt-serial-number-0">{{
                            'DEVICE_PROPERTIES.basic_property.serial_number' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components['serialNumber.0']" mat-line>
                          <span id="dd-serial-number-0">{{ device.device_components['serialNumber.0']?.status }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.firmwareVersion" mat-line>
                          <span id="dt-firmware-version">{{
                            'DEVICE_PROPERTIES.basic_property.fw_version' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.firmwareVersion" mat-line>
                          <span id="dd-firmware-version">{{ device.device_components.firmwareVersion?.status }}</span>
                        </dd>
                        <ng-container *ngIf="device.device_components.imageVersion">
                          <dt mat-line>
                            <span id="dt-firmware-system-version">{{
                              'DEVICE_PROPERTIES.basic_property.fw_system_version' | translate
                            }}</span>
                          </dt>
                          <dd mat-line>
                            <span id="dd-firmware-system-version">{{
                              device.device_components.imageVersion[0]?.status
                            }}</span>
                          </dd>
                        </ng-container>
                        <ng-container *ngIf="device.device_components.biosVersion">
                          <dt mat-line>
                            <span id="dt-bios-version">{{
                              'DEVICE_PROPERTIES.basic_property.bios_version' | translate
                            }}</span>
                          </dt>
                          <dd mat-line>
                            <span id="dd-bios-version">{{ device.device_components.biosVersion[0]?.status }}</span>
                          </dd>
                        </ng-container>
                        <ng-container *ngIf="device.device_components.osType">
                          <dt mat-line>
                            <span id="dt-os-type">{{ 'DEVICE_PROPERTIES.basic_property.os_type' | translate }}</span>
                          </dt>
                          <dd mat-line>
                            <span id="dd-os-type">{{ device.device_components.osType[0]?.status }}</span>
                          </dd>
                        </ng-container>
                        <dt *ngIf="device.device_components.cpuLoading" mat-line>
                          <span id="dt-cpu-loading">{{
                            'DEVICE_PROPERTIES.basic_property.cpu_loading' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.cpuLoading" mat-line>
                          <span id="dd-cpu-loading">{{
                            globalService.getDeviceProp(device.device_components.cpuLoading)
                          }}</span>
                        </dd>
                        <dt *ngIf="device.device_components['cpuLoading5s.0']" mat-line>
                          <span id="dt-cpu-loading-5s">{{
                            'DEVICE_PROPERTIES.basic_property.cpu_loading_5_seconds' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components['cpuLoading5s.0']" mat-line>
                          <span id="dd-cpu-loading-5s">{{ device.device_components['cpuLoading5s.0']?.status }}</span>
                        </dd>
                        <dt *ngIf="device.device_components['cpuLoading30s.0']" mat-line>
                          <span id="dt-cpu-loading-30s">{{
                            'DEVICE_PROPERTIES.basic_property.cpu_loading_30_seconds' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components['cpuLoading30s.0']" mat-line>
                          <span id="dd-cpu-loading-30s">{{ device.device_components['cpuLoading30s.0']?.status }}</span>
                        </dd>
                        <dt *ngIf="device.device_components['cpuLoading300s.0']" mat-line>
                          <span id="dt-cpu-loading-300s">{{
                            'DEVICE_PROPERTIES.basic_property.cpu_loading_300_seconds' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components['cpuLoading300s.0']" mat-line>
                          <span id="dd-cpu-loading-300s">{{
                            device.device_components['cpuLoading300s.0']?.status
                          }}</span>
                        </dd>
                        <dt *ngIf="device.device_components['cpuLoading5s']" mat-line>
                          <span id="dt-cpu-loading-5s">{{
                            'DEVICE_PROPERTIES.basic_property.cpu_loading_5_seconds' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components['cpuLoading5s']" mat-line>
                          <span id="dd-cpu-loading-5s">{{ device.device_components['cpuLoading5s']?.status }}</span>
                        </dd>
                        <dt *ngIf="device.device_components['cpuLoading30s']" mat-line>
                          <span id="dt-cpu-loading-30s">{{
                            'DEVICE_PROPERTIES.basic_property.cpu_loading_30_seconds' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components['cpuLoading30s']" mat-line>
                          <span id="dd-cpu-loading-30s">{{ device.device_components['cpuLoading30s']?.status }}</span>
                        </dd>
                        <dt *ngIf="device.device_components['cpuLoading300s']" mat-line>
                          <span id="dt-cpu-loading-300s">{{
                            'DEVICE_PROPERTIES.basic_property.cpu_loading_300_seconds' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components['cpuLoading300s']" mat-line>
                          <span id="dd-cpu-loading-300s">{{ device.device_components['cpuLoading300s']?.status }}</span>
                        </dd>
                        <ng-container *ngIf="device.device_components.cpuLoading1m">
                          <dt mat-line>
                            <span id="dt-cpu-utilization-60s">{{
                              'DEVICE_PROPERTIES.basic_property.cpu_utilization_60_seconds' | translate
                            }}</span>
                          </dt>
                          <dd mat-line>
                            <span id="dd-cpu-utilization-60s">{{
                              device.device_components.cpuLoading1m[0]?.status
                            }}</span>
                          </dd>
                        </ng-container>
                        <ng-container *ngIf="device.device_components.cpuLoading5m">
                          <dt mat-line>
                            <span id="dt-cpu-utilization-300s">{{
                              'DEVICE_PROPERTIES.basic_property.cpu_utilization_300_seconds' | translate
                            }}</span>
                          </dt>
                          <dd mat-line>
                            <span id="dd-cpu-utilization-300s">{{
                              device.device_components.cpuLoading5m[0]?.status
                            }}</span>
                          </dd>
                        </ng-container>
                        <ng-container *ngIf="device.device_components.cpuLoading15m">
                          <dt mat-line>
                            <span id="dt-cpu-utilization-900s">{{
                              'DEVICE_PROPERTIES.basic_property.cpu_utilization_900_seconds' | translate
                            }}</span>
                          </dt>
                          <dd mat-line>
                            <span id="dd-cpu-utilization-900s">{{
                              device.device_components.cpuLoading15m[0]?.status
                            }}</span>
                          </dd>
                        </ng-container>
                        <dt *ngIf="device.device_components['memoryUsage.0']" mat-line>
                          <span id="dt-memory-usage-0">{{
                            'DEVICE_PROPERTIES.basic_property.memory_usage_unit' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components['memoryUsage.0']" mat-line>
                          <span id="dd-memory-usage-0">{{ device.device_components['memoryUsage.0']?.status }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.memoryUtilization" mat-line>
                          <span id="dt-memory-usage">{{
                            'DEVICE_PROPERTIES.basic_property.memory_usage_unit' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.memoryUtilization" mat-line>
                          <span id="dd-memory-usage">{{
                            globalService.getDeviceProp(device.device_components.memoryUtilization)
                          }}</span>
                        </dd>
                        <ng-container *ngIf="device.device_components.partitionUsage">
                          <dt mat-line>
                            <span id="dt-disk-utilization-unit">{{
                              'DEVICE_PROPERTIES.basic_property.disk_utilization_unit' | translate
                            }}</span>
                          </dt>
                          <dd mat-line>
                            <span id="dd-disk-utilization-unit">{{
                              device.device_components.partitionUsage[0]?.status
                            }}</span>
                          </dd>
                        </ng-container>
                        <dt *ngIf="device.device_components['powerConsumption.0']" mat-line>
                          <span id="dt-power-comsumption">{{
                            'DEVICE_PROPERTIES.basic_property.power_comsumption' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components['powerConsumption.0']" mat-line>
                          <span id="dd-power-comsumption">{{
                            device.device_components['powerConsumption.0']?.status
                          }}</span>
                        </dd>
                        <dt *ngIf="device.device_components['sysUpTime']" mat-line>
                          <span id="dt-sys-up-time">{{
                            'DEVICE_PROPERTIES.basic_property.system_up_time' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components['sysUpTime']" mat-line>
                          <span id="dd-sys-up-time">{{
                            globalService.formatMinutes(device.device_components.sysUpTime.status / 6000)
                          }}</span>
                        </dd>
                      </div>
                    </div>
                    <!-- Redundancy START -->
                    <div class="property-table-field">
                      <div class="info-title" *ngIf="hasOneOfProperties(device, ['activeProtocolOfRedundancy'])">
                        {{ 'DEVICE_PROPERTIES.redundancy.title' | translate }}
                      </div>
                      <div class="property-table-field-content">
                        <dt *ngIf="device.device_components.activeProtocolOfRedundancy" mat-line>
                          <span id="dt-active-redundancy-protocol">{{
                            'DEVICE_PROPERTIES.redundancy.active_redundancy_protocol' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.activeProtocolOfRedundancy" mat-line>
                          <span id="dd-active-redundancy-protocol">{{
                            device.device_components.activeProtocolOfRedundancy?.status
                          }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.rstpEnabled" mat-line>
                          <span id="dt-rstp-enabled">{{ 'DEVICE_PROPERTIES.redundancy.rstp' | translate }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.rstpEnabled" mat-line>
                          <span id="dd-rstp-enabled">{{ device.device_components.rstpEnabled?.status }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.trv2Enabled" mat-line>
                          <span id="dt-trv2-enabled">{{ 'DEVICE_PROPERTIES.redundancy.trv2' | translate }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.trv2Enabled" mat-line>
                          <span id="dd-trv2-enabled">{{ device.device_components.trv2Enabled?.status }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.tcEnabled" mat-line>
                          <span id="dt-tc">{{ 'DEVICE_PROPERTIES.redundancy.tc' | translate }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.tcEnabled" mat-line>
                          <span id="dd-tc">{{ device.device_components.tcEnabled?.status }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.dhEnabled" mat-line>
                          <span id="dt-dh">{{ 'DEVICE_PROPERTIES.redundancy.dh' | translate }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.dhEnabled" mat-line>
                          <span id="dd-tc">{{ device.device_components.dhEnabled?.status }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.iec624393Protocol" mat-line>
                          <span id="dt-dh">{{
                            'DEVICE_PROPERTIES.redundancy.iec_624393_redundancy_protocol' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.iec624393Protocol" mat-line>
                          <span id="dd-tc">{{ device.device_components.iec624393Protocol?.status }}</span>
                        </dd>
                      </div>
                    </div>
                    <!-- Redundancy END -->
                    <!-- Power Status START -->
                    <div class="property-table-field">
                      <div
                        class="info-title"
                        *ngIf="hasOneOfProperties(device, ['power1InputStatus', 'power2InputStatus'])"
                      >
                        {{ 'DEVICE_PROPERTIES.power.title' | translate }}
                      </div>
                      <div class="property-table-field-content">
                        <dt *ngIf="device.device_components.power1InputStatus" mat-line>
                          <span id="dt-power-1-input-status">{{
                            'DEVICE_PROPERTIES.power.power_1_status' | translate
                          }}</span>
                        </dt>
                        <dd
                          *ngIf="
                            device.device_components.power1InputStatus && !propertyIsArray(device, 'power1InputStatus')
                          "
                          mat-line
                        >
                          <span id="dd-power-1-input-status">{{
                            device.device_components.power1InputStatus?.status
                          }}</span>
                        </dd>
                        <dd
                          *ngIf="
                            device.device_components.power1InputStatus && propertyIsArray(device, 'power1InputStatus')
                          "
                          mat-line
                        >
                          <span id="dd-power-1-input-status">{{
                            device.device_components.power1InputStatus[0].status
                          }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.power2InputStatus" mat-line>
                          <span id="dt-power-2-input-status">{{
                            'DEVICE_PROPERTIES.power.power_2_status' | translate
                          }}</span>
                        </dt>
                        <dd
                          *ngIf="
                            device.device_components.power2InputStatus && !propertyIsArray(device, 'power2InputStatus')
                          "
                          mat-line
                        >
                          <span id="dd-power-2-input-status">{{
                            device.device_components.power2InputStatus?.status
                          }}</span>
                        </dd>
                        <dd
                          *ngIf="
                            device.device_components.power2InputStatus && propertyIsArray(device, 'power2InputStatus')
                          "
                          mat-line
                        >
                          <span id="dd-power-2-input-status">{{
                            device.device_components.power2InputStatus[0].status
                          }}</span>
                        </dd>
                      </div>
                    </div>
                    <!-- Power Status END -->
                    <!-- Wireless Information START -->
                    <div *ngIf="device.device_components?.devOperationMode" class="property-table-field">
                      <div class="info-title">{{ 'DEVICE_PROPERTIES.wireless.title' | translate }}</div>
                      <div class="property-table-field-content">
                        <ng-template
                          *ngIf="device.device_components?.devOperationMode?.length > 0; else devOperationModeElse"
                          ngFor
                          let-devOperationMode
                          let-i="index"
                          [ngForOf]="device.device_components?.devOperationMode"
                        >
                          <dt>
                            <span id="dt-operation-mode"
                              >{{ 'IW.Title.operation_mode' | translate }}.{{ devOperationMode?.index }}</span
                            >
                          </dt>
                          <dd>
                            <span *ngIf="devOperationMode?.status === 'client'" id="dd-operation-mode">{{
                              'IW.Title.client' | translate
                            }}</span>
                            <span *ngIf="devOperationMode?.status === 'ap'" id="dd-operation-mode">{{
                              'IW.Title.ap' | translate
                            }}</span>
                            <span *ngIf="devOperationMode?.status === 'clientRouter'" id="dd-operation-mode">{{
                              'IW.Title.client_router' | translate
                            }}</span>
                            <span *ngIf="devOperationMode?.status === 'master'" id="dd-operation-mode">{{
                              'IW.Title.master' | translate
                            }}</span>
                            <span *ngIf="devOperationMode?.status === 'slave'" id="dd-operation-mode">{{
                              'IW.Title.slave' | translate
                            }}</span>
                            <span *ngIf="devOperationMode?.status === 'disable'" id="dd-operation-mode">{{
                              'IW.Title.disable' | translate
                            }}</span>
                          </dd>
                        </ng-template>
                        <ng-template #devOperationModeElse>
                          <dt>
                            <span id="dt-operation-mode">{{ 'IW.Title.operation_mode' | translate }}</span>
                          </dt>
                          <dd>
                            <span
                              *ngIf="device.device_components?.devOperationMode?.status === 'client'"
                              id="dd-operation-mode"
                              >{{ 'IW.Title.client' | translate }}</span
                            >
                            <span
                              *ngIf="device.device_components?.devOperationMode?.status === 'ap'"
                              id="dd-operation-mode"
                              >{{ 'IW.Title.ap' | translate }}</span
                            >
                            <span
                              *ngIf="device.device_components?.devOperationMode?.status === 'clientRouter'"
                              id="dd-operation-mode"
                              >{{ 'IW.Title.client_router' | translate }}</span
                            >
                            <span
                              *ngIf="device.device_components?.devOperationMode?.status === 'master'"
                              id="dd-operation-mode"
                            >
                              {{ 'IW.Title.master' | translate }}
                            </span>
                            <span
                              *ngIf="device.device_components?.devOperationMode?.status === 'slave'"
                              id="dd-operation-mode"
                            >
                              {{ 'IW.Title.slave' | translate }}
                            </span>
                            <span *ngIf="device.device_components?.devOperationMode?.status === 'disable'">{{
                              'IW.Title.disable' | translate
                            }}</span>
                          </dd>
                        </ng-template>
                        <ng-template
                          *ngIf="device.device_components?.devRfType?.length > 0"
                          ngFor
                          let-devRfType
                          let-i="index"
                          [ngForOf]="device.device_components?.devRfType"
                        >
                          <dt>
                            <span id="dt-rf-type"
                              >{{ 'DEVICE_PROPERTIES.wireless.rf_type' | translate }}.{{ devRfType?.index }}</span
                            >
                          </dt>
                          <dd>
                            <span id="dd-rf-type">{{ devRfType?.status }}</span>
                          </dd>
                        </ng-template>
                        <ng-template
                          *ngIf="device.device_components?.devTxPowerdBm?.length > 0"
                          ngFor
                          let-devTxPowerdBm
                          let-i="index"
                          [ngForOf]="device.device_components?.devTxPowerdBm"
                        >
                          <dt>
                            <span id="dt-dev-tx-power-dbm"
                              >{{ 'IW.Title.tx_power_unit' | translate }}.{{ devTxPowerdBm?.index }}</span
                            >
                          </dt>
                          <dd>
                            <span id="dd-dev-tx-power-dbm">{{ devTxPowerdBm?.status }}</span>
                          </dd>
                        </ng-template>
                        <ng-template
                          *ngIf="device.device_components?.wlanBSSID?.length > 0"
                          ngFor
                          let-wlanBSSID
                          let-i="index"
                          [ngForOf]="device.device_components?.wlanBSSID"
                        >
                          <dt>
                            <span id="dt-bssid">{{ 'IW.Title.BSSID' | translate }}.{{ wlanBSSID?.index }}</span>
                          </dt>
                          <dd>
                            <span id="dd-bssid">{{ wlanBSSID?.status }}</span>
                          </dd>
                        </ng-template>
                        <ng-template
                          *ngIf="device.device_components?.wlanChannel?.length > 0"
                          ngFor
                          let-wlanChannel
                          let-i="index"
                          [ngForOf]="device.device_components.wlanChannel"
                        >
                          <dt>
                            <span id="dt-channel">{{ 'IW.Title.channel' | translate }}.{{ wlanChannel?.index }}</span>
                          </dt>
                          <dd>
                            <span id="dd-channel">{{ wlanChannel?.status }}</span>
                          </dd>
                        </ng-template>
                        <ng-container
                          *ngIf="
                            globalService.getDeviceProp(device.device_components.devOperationMode) === 'client' ||
                            globalService.getDeviceProp(device.device_components.devOperationMode) === 'clientRouter'
                          "
                        >
                          <ng-template
                            *ngIf="device.device_components?.wirelessStatusNoiseLevel?.length > 0"
                            ngFor
                            let-wirelessStatusNoiseLevel
                            let-i="index"
                            [ngForOf]="device.device_components?.wirelessStatusNoiseLevel"
                          >
                            <dt>
                              <span id="dt-noise-level"
                                >{{ 'IW.Title.noise_floor_unit' | translate }}.{{
                                  wirelessStatusNoiseLevel?.index
                                }}</span
                              >
                            </dt>
                            <dd>
                              <span id="dd-noise-level">{{ wirelessStatusNoiseLevel?.status }}</span>
                            </dd>
                          </ng-template>
                          <ng-template
                            *ngIf="
                              device.device_components?.wirelessStatusRSSI?.length > 0;
                              else wirelessStatusRSSIElse
                            "
                            ngFor
                            let-wirelessStatusRSSI
                            let-i="index"
                            [ngForOf]="device.device_components?.wirelessStatusRSSI"
                          >
                            <dt>
                              <span id="dt-rssi"
                                >{{ 'IW.Title.RSSI' | translate }}.{{ wirelessStatusRSSI?.index }}</span
                              >
                            </dt>
                            <dd>
                              <span id="dd-rssi">{{ wirelessStatusRSSI?.status }}</span>
                            </dd>
                          </ng-template>
                          <ng-template #wirelessStatusRSSIElse>
                            <dt>
                              <span id="dt-rssi">{{ 'IW.Title.RSSI' | translate }}</span>
                            </dt>
                            <dd>
                              <span id="dd-rssi">{{ device.device_components?.wirelessStatusRSSI?.status }}</span>
                            </dd>
                          </ng-template>
                          <ng-template
                            *ngIf="
                              device.device_components['wirelessStatusSNR-A']?.length > 0;
                              else wirelessStatusSnrAElse
                            "
                            ngFor
                            let-wirelessStatusSnrA
                            let-i="index"
                            [ngForOf]="device.device_components['wirelessStatusSNR-A']"
                          >
                            <dt>
                              <span id="dt-snr-a"
                                >{{ 'IW.Title.SNR_A' | translate }}.{{ wirelessStatusSnrA?.index }}</span
                              >
                            </dt>
                            <dd>
                              <span id="dd-snr-a">{{ wirelessStatusSnrA?.status }}</span>
                            </dd>
                          </ng-template>
                          <ng-template #wirelessStatusSnrAElse>
                            <dt>
                              <span id="dt-snr-a">{{ 'IW.Title.SNR_A' | translate }}</span>
                            </dt>
                            <dd>
                              <span id="dd-snr-a">{{ device.device_components['wirelessStatusSNR-A']?.status }}</span>
                            </dd>
                          </ng-template>
                          <ng-template
                            *ngIf="
                              device.device_components['wirelessStatusSNR-B']?.length > 0;
                              else wirelessStatusSnrBElse
                            "
                            ngFor
                            let-wirelessStatusSnrB
                            let-i="index"
                            [ngForOf]="device.device_components['wirelessStatusSNR-B']"
                          >
                            <dt>
                              <span id="dt-snr-b"
                                >{{ 'IW.Title.SNR_B' | translate }}.{{ wirelessStatusSnrB?.index }}</span
                              >
                            </dt>
                            <dd>
                              <span id="dd-snr-b">{{ wirelessStatusSnrB?.status }}</span>
                            </dd>
                          </ng-template>
                          <ng-template #wirelessStatusSnrBElse>
                            <ng-container *ngIf="device.device_components['wirelessStatusSNR-B']">
                              <dt>
                                <span id="dt-snr-b">{{ 'IW.Title.SNR_B' | translate }}</span>
                              </dt>
                              <dd>
                                <span id="dd-snr-b">{{ device.device_components['wirelessStatusSNR-B']?.status }}</span>
                              </dd>
                            </ng-container>
                          </ng-template>
                          <ng-template
                            *ngIf="
                              device.device_components?.wlanConnectionTime?.length > 0;
                              else wlanConnectionTimeElse
                            "
                            ngFor
                            let-wlanConnectionTime
                            let-i="index"
                            [ngForOf]="device.device_components?.wlanConnectionTime"
                          >
                            <dt>
                              <span id="dt-wlan-connection-time"
                                >{{ 'IW.Title.connected' | translate }}.{{ wlanConnectionTime?.index }}</span
                              >
                            </dt>
                            <dd>
                              <span id="dd-wlan-connection-time">{{
                                globalService.formatMinutes(wlanConnectionTime?.status / 6000)
                              }}</span>
                            </dd>
                          </ng-template>
                          <ng-template #wlanConnectionTimeElse>
                            <dt>
                              <span id="dt-wlan-connection-time">{{ 'IW.Title.connected' | translate }}</span>
                            </dt>
                            <dd>
                              <span id="dd-wlan-connection-time">{{
                                globalService.formatMinutes(device.device_components?.wlanConnectionTime?.status / 6000)
                              }}</span>
                            </dd>
                          </ng-template>
                          <ng-template
                            *ngIf="device.device_components?.wlanSignal?.length > 0; else wlanSignalElse"
                            ngFor
                            let-wlanSignal
                            let-i="index"
                            [ngForOf]="device.device_components.wlanSignal"
                          >
                            <dt>
                              <span id="dt-signal"
                                >{{ 'IW.Title.signal_level' | translate }}.{{ wlanSignal?.index }}</span
                              >
                            </dt>
                            <dd>
                              <span id="dd-signal">{{ wlanSignal?.status }}</span>
                            </dd>
                          </ng-template>
                          <ng-template #wlanSignalElse>
                            <ng-template
                              *ngIf="
                                globalService.getDeviceProp(device.device_components.devOperationMode) === 'client' ||
                                globalService.getDeviceProp(device.device_components.devOperationMode) ===
                                  'clientRouter'
                              "
                            >
                              <dt>
                                <span id="dt-signal">{{ 'IW.Title.signal_level' | translate }}</span>
                              </dt>
                              <dd>
                                <span id="dd-signal">{{ device.device_components.wlanSignal.status }}</span>
                              </dd>
                            </ng-template>
                          </ng-template>
                          <ng-template
                            *ngIf="device.device_components?.wlanTxRate?.length > 0; else wlanTxRateElse"
                            ngFor
                            let-wlanTxRate
                            let-i="index"
                            [ngForOf]="device.device_components.wlanTxRate"
                          >
                            <dt>
                              <span id="dt-tx-rate"
                                >{{ 'IW.Title.tx_rate_unit' | translate }}.{{ wlanTxRate?.index }}</span
                              >
                            </dt>
                            <dd>
                              <span id="dd-tx-rate">{{ wlanTxRate?.status }}</span>
                            </dd>
                          </ng-template>
                          <ng-template #wlanTxRateElse>
                            <dt>
                              <span id="dt-tx-rate">{{ 'IW.Title.tx_rate_unit' | translate }}</span>
                            </dt>
                            <dd>
                              <span id="dd-tx-rate">{{ device.device_components.wlanTxRate?.status }}</span>
                            </dd>
                          </ng-template>
                        </ng-container>
                        <mat-expansion-panel
                          *ngIf="device.device_components?.vapAuthType?.length > 0; else vapAuthTypeElse"
                          class="vap-node"
                        >
                          <mat-expansion-panel-header>
                            <mat-panel-title>
                              {{ 'DEVICE_PROPERTIES.wireless.vapAuthType' | translate }}
                            </mat-panel-title>
                          </mat-expansion-panel-header>
                          <div
                            *ngFor="let vapAuthType of device.device_components.vapAuthType"
                            class="property-table-field-content"
                          >
                            <dt>
                              <span id="dt-vap-auth-type-{{ vapAuthType?.index }}"
                                >{{ 'DEVICE_PROPERTIES.wireless.vapAuthType' | translate }}.{{
                                  vapAuthType?.index
                                }}</span
                              >
                            </dt>
                            <dd>
                              <span id="dd-vap-auth-type-{{ vapAuthType?.index }}">{{ vapAuthType?.status }}</span>
                            </dd>
                          </div>
                        </mat-expansion-panel>
                        <ng-template #vapAuthTypeElse>
                          <dt>
                            <span id="dt-vap-auth-type">{{
                              'DEVICE_PROPERTIES.wireless.vapAuthType' | translate
                            }}</span>
                          </dt>
                          <dd>
                            <span id="dd-vap-auth-type">{{ device.device_components.vapAuthType?.status }}</span>
                          </dd>
                        </ng-template>
                        <mat-expansion-panel
                          *ngIf="device.device_components?.vapMgmtEncryption?.length > 0"
                          class="vap-node"
                        >
                          <mat-expansion-panel-header>
                            <mat-panel-title>
                              {{ 'DEVICE_PROPERTIES.wireless.vap_mgmt_encryption' | translate }}
                            </mat-panel-title>
                          </mat-expansion-panel-header>
                          <div
                            *ngFor="let vapMgmtEncryption of device.device_components.vapMgmtEncryption"
                            class="property-table-field-content"
                          >
                            <dt>
                              <span id="dt-vap-mgmt-encryption-{{ vapMgmtEncryption?.index }}"
                                >{{ 'DEVICE_PROPERTIES.wireless.vap_mgmt_encryption' | translate }}.{{
                                  vapMgmtEncryption?.index
                                }}</span
                              >
                            </dt>
                            <dd>
                              <span id="dd-vap-mgmt-encryption-{{ vapMgmtEncryption?.index }}">{{
                                vapMgmtEncryption?.status
                              }}</span>
                            </dd>
                          </div>
                        </mat-expansion-panel>
                        <mat-expansion-panel
                          *ngIf="device.device_components?.vapWpaEncrypt?.length > 0"
                          class="vap-node"
                        >
                          <mat-expansion-panel-header>
                            <mat-panel-title>
                              {{ 'DEVICE_PROPERTIES.wireless.vap_wpa_encrypt' | translate }}
                            </mat-panel-title>
                          </mat-expansion-panel-header>
                          <div
                            *ngFor="let vapWpaEncrypt of device.device_components.vapWpaEncrypt"
                            class="property-table-field-content"
                          >
                            <dt>
                              <span id="dt-vap-wpa-encrypt-{{ vapWpaEncrypt?.index }}"
                                >{{ 'DEVICE_PROPERTIES.wireless.vap_wpa_encrypt' | translate }}.{{
                                  vapWpaEncrypt?.index
                                }}</span
                              >
                            </dt>
                            <dd>
                              <span id="dd-vap-wpa-encrypt-{{ vapWpaEncrypt?.index }}">{{
                                vapWpaEncrypt?.status
                              }}</span>
                            </dd>
                          </div>
                        </mat-expansion-panel>
                      </div>
                    </div>
                    <!-- Wireless Information END -->
                    <!-- Cellular Information START -->
                    <div *ngIf="device.device_components?.cellularMode" class="property-table-field">
                      <div class="info-title">{{ 'DEVICE_PROPERTIES.cellular.title' | translate }}</div>
                      <div class="property-table-field-content">
                        <dt mat-line>
                          <span id="dt-cellular-mode">{{
                            'DEVICE_PROPERTIES.cellular.cellular_mode' | translate
                          }}</span>
                        </dt>
                        <dd mat-line>
                          <span id="dd-cellular-mode">{{ device.device_components.cellularMode?.status }}</span>
                        </dd>
                        <dt mat-line>
                          <span id="dt-cellular-carrier">{{
                            'DEVICE_PROPERTIES.cellular.cellular_carrier' | translate
                          }}</span>
                        </dt>
                        <dd mat-line>
                          <span id="dd-cellular-carrier">{{ device.device_components.cellularCarrier?.status }}</span>
                        </dd>
                        <dt mat-line>
                          <span id="dt-cellular-signal">{{
                            'DEVICE_PROPERTIES.cellular.cellular_signal' | translate
                          }}</span>
                        </dt>
                        <dd mat-line>
                          <span id="dd-cellular-signal">{{ device.device_components.cellularSignal?.status }}</span>
                        </dd>
                        <dt mat-line>
                          <span id="dt-cellular-cellular-ip-address">{{
                            'DEVICE_PROPERTIES.cellular.cellular_ip_address' | translate
                          }}</span>
                        </dt>
                        <dd mat-line>
                          <span id="dd-cellular-cellular-ip-address">{{
                            device.device_components.cellularIpAddress?.status
                          }}</span>
                        </dd>
                        <dt mat-line>
                          <span id="dt-cellular-imei">{{ 'DEVICE_PROPERTIES.cellular.imei' | translate }}</span>
                        </dt>
                        <dd mat-line>
                          <span id="dd-cellular-imei">{{ device.device_components.imei?.status }}</span>
                        </dd>
                        <dt mat-line>
                          <span id="dt-cellular-imsi">{{ 'DEVICE_PROPERTIES.cellular.imsi' | translate }}</span>
                        </dt>
                        <dd mat-line>
                          <span id="dd-cellular-imsi">{{ device.device_components.imsi?.status }}</span>
                        </dd>
                      </div>
                    </div>
                    <!-- Cellular Information END -->
                    <!-- Port Information START -->
                    <div class="property-table-field">
                      <div class="info-title">{{ 'DEVICE_PROPERTIES.port.title' | translate }}</div>
                      <div class="property-table-field-content">
                        <dt mat-line>
                          <span id="dt-if-number">{{ 'DEVICE_PROPERTIES.port.if_number' | translate }}</span>
                        </dt>
                        <dd mat-line>
                          <span id="dd-if-number">{{ device.device_components.ifNumber?.status }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.numberOfPorts" mat-line>
                          <span id="dt-number-of-ports">{{
                            'DEVICE_PROPERTIES.port.number_of_ports' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.numberOfPorts" mat-line>
                          <span id="dd-number-of-ports">{{ device.device_components.numberOfPorts?.status }}</span>
                        </dd>
                        <ng-template
                          *ngIf="device?.device_components?.ifOperStatus?.length > 0"
                          ngFor
                          let-ifOperStatus
                          let-i="index"
                          [ngForOf]="device?.device_components?.ifOperStatus"
                        >
                          <dt>
                            <span id="dt-if-oper-status">
                              {{ 'DEVICE_PROPERTIES.port.interface' | translate }}.{{ ifOperStatus?.index }}
                              <!-- 用 +ifOperStatus?.index - 1 來取得 poe 相關的資訊 -->
                              <mat-icon
                                class="poe-power-icon"
                                *ngIf="
                                  showPoeInfo(ifOperStatus) &&
                                  device?.device_components?.poePortConsumption &&
                                  device?.device_components?.poePortConsumption[+ifOperStatus?.index - 1]
                                "
                              >
                                flash_on</mat-icon
                              >
                            </span>
                          </dt>
                          <dd *ngIf="device?.device_components?.portSpeed; else notShowPortSpeed">
                            <span id="dd-if-oper-status"
                              >{{ ifOperStatus?.status
                              }}<span
                                *ngIf="
                                  device?.device_components?.portName[i] &&
                                  device?.device_components?.portName[i]?.status !== ''
                                "
                              >
                                / {{ device?.device_components?.portName[i]?.status }}</span
                              >
                              / {{ device?.device_components?.ifSpeed[i]?.statusEx }} /
                              {{ device?.device_components?.portSpeed[i]?.status }}</span
                            >
                          </dd>
                          <ng-template #notShowPortSpeed>
                            <dd>
                              <span id="dd-if-oper-status-not-show-port-speed"
                                >{{ ifOperStatus?.status }} /
                                <ng-template *ngIf="device?.device_components?.portName">
                                  {{ device?.device_components?.portName[i]?.status }}
                                </ng-template>
                                {{ device?.device_components?.ifSpeed[i]?.statusEx }}
                              </span>
                            </dd>
                          </ng-template>
                          <dd
                            *ngIf="device?.device_components?.ifType && device.device_components.ifType.length > 0"
                            class="dd-margin-top"
                          >
                            <span id="dd-if-type"
                              >{{ device?.device_components?.ifType[i]?.status }} /{{
                                device?.device_components?.ifDescr[i]?.status
                              }}</span
                            >
                          </dd>
                          <dd
                            class="dd-margin-top"
                            *ngIf="
                              device?.device_components?.poePortConsumption &&
                              device?.device_components?.poePortConsumption[+ifOperStatus?.index - 1] &&
                              showPoeInfo(ifOperStatus)
                            "
                          >
                            <span id="dd-poe"
                              >{{ device?.device_components?.poePortConsumption[+ifOperStatus?.index - 1]?.status }}W /
                              {{ device?.device_components?.poePortVoltage[+ifOperStatus?.index - 1]?.status }}V /
                              {{ device?.device_components?.poePortCurrent[+ifOperStatus?.index - 1]?.status }}mA
                            </span>
                          </dd>
                          <dd class="dd-margin-top" *ngIf="showSFPInfo(device, i)">
                            <span id="dd-sfp" [innerHTML]="formatSFPInfo(device, i)"></span>
                          </dd>
                          <dd
                            class="dd-margin-top"
                            *ngIf="
                              showPoeInfo(ifOperStatus) &&
                              device?.device_components?.poePowerOutputMode &&
                              device?.device_components?.poePowerOutputMode[+ifOperStatus?.index - 1]
                            "
                          >
                            <span id="dd-poe-power-output-mode"
                              >{{ 'DEVICE_PROPERTIES.port.poe_power_output_mode' | translate }}
                              :
                              {{
                                device?.device_components?.poePowerOutputMode[+ifOperStatus?.index - 1]?.status
                              }}</span
                            >
                          </dd>
                          <dd
                            class="dd-margin-top"
                            *ngIf="
                              showPoeInfo(ifOperStatus) &&
                              device?.device_components?.poePortLegacyPdDetect &&
                              device?.device_components?.poePortLegacyPdDetect[+ifOperStatus?.index - 1]
                            "
                          >
                            <span id="dd-poe-port-legacy-pd-detect"
                              >{{ 'DEVICE_PROPERTIES.port.poe_power_legacy_pd_detect' | translate }} :
                              {{ device?.device_components?.poePortLegacyPdDetect[+ifOperStatus?.index - 1]?.status }}
                            </span>
                          </dd>
                          <dd
                            class="dd-margin-top"
                            *ngIf="
                              showPoeInfo(ifOperStatus) &&
                              device?.device_components?.poePortClass &&
                              device?.device_components?.poePortClass[+ifOperStatus?.index - 1]
                            "
                          >
                            <span id="dd-poe-port-class"
                              >{{ 'DEVICE_PROPERTIES.port.poe_port_class' | translate }} :
                              {{ device?.device_components?.poePortClass[+ifOperStatus?.index - 1]?.status }}</span
                            >
                          </dd>
                        </ng-template>
                      </div>
                    </div>
                    <!-- Port Information END -->
                    <!-- IPsec Status START -->
                    <div class="property-table-field">
                      <div class="info-title" *ngIf="hasOneOfProperties(device, ['ipsecStatusName'])">
                        {{ 'DEVICE_PROPERTIES.ipsec.title' | translate }}
                      </div>
                      <div class="property-table-field-content">
                        <ng-container *ngFor="let ipSecName of device.device_components.ipsecStatusName; let i = index">
                          <ng-container *ngIf="ipSecName !== null">
                            <ng-container *ngIf="device.device_components?.ipsecStatusName">
                              <dt mat-line>
                                <span id="dt-ipsec-name">{{ 'DEVICE_PROPERTIES.ipsec.name' | translate }}.{{ i }}</span>
                              </dt>
                              <dd mat-line>
                                <span id="dt-ipsec-name">{{ ipSecName.status }}</span>
                              </dd>
                            </ng-container>
                            <ng-container *ngIf="device.device_components?.ipsecStatusLocSubnet">
                              <dt mat-line>
                                <span id="dt-local-subnet"
                                  >{{ 'DEVICE_PROPERTIES.ipsec.local_subnet' | translate }}.{{ i }}</span
                                >
                              </dt>
                              <dd mat-line>
                                <span id="dd-local-subnet">{{
                                  device.device_components?.ipsecStatusLocSubnet[i]?.status
                                }}</span>
                              </dd>
                            </ng-container>
                            <ng-container *ngIf="device.device_components?.ipsecStatusLocGateway">
                              <dt mat-line>
                                <span id="dt-local-gateway"
                                  >{{ 'DEVICE_PROPERTIES.ipsec.local_gateway' | translate }}.{{ i }}</span
                                >
                              </dt>
                              <dd mat-line>
                                <span id="dd-local-gateway">{{
                                  device.device_components?.ipsecStatusLocGateway[i]?.status
                                }}</span>
                              </dd>
                            </ng-container>
                            <ng-container *ngIf="device.device_components?.ipsecStatusRemSubnet">
                              <dt mat-line>
                                <span id="dt-remote-subnet"
                                  >{{ 'DEVICE_PROPERTIES.ipsec.remote_subnet' | translate }}.{{ i }}</span
                                >
                              </dt>
                              <dd mat-line>
                                <span id="dd-remote-subnet">{{
                                  device.device_components?.ipsecStatusRemSubnet[i]?.status
                                }}</span>
                              </dd>
                            </ng-container>
                            <ng-container *ngIf="device.device_components?.ipsecStatusRemGateway">
                              <dt mat-line>
                                <span id="dt-remote-gateway"
                                  >{{ 'DEVICE_PROPERTIES.ipsec.remote_gateway' | translate }}.{{ i }}</span
                                >
                              </dt>
                              <dd mat-line>
                                <span id="dd-remote-gateway">{{
                                  device.device_components?.ipsecStatusRemGateway[i]?.status
                                }}</span>
                              </dd>
                            </ng-container>
                            <ng-container *ngIf="device.device_components?.ipsecStatusPhase1">
                              <dt mat-line>
                                <span id="dt-phase-1-status">
                                  {{ 'DEVICE_PROPERTIES.ipsec.phase_1_status' | translate }}.{{ i }}</span
                                >
                              </dt>
                              <dd mat-line>
                                <span id="dd-phase-1-status">{{
                                  device.device_components?.ipsecStatusPhase1[i]?.status
                                }}</span>
                              </dd>
                            </ng-container>
                            <ng-container *ngIf="device.device_components.ipsecStatusPhase2">
                              <dt mat-line>
                                <span id="dt-phase-2-status"
                                  >{{ 'DEVICE_PROPERTIES.ipsec.phase_2_status' | translate }}.{{ i }}</span
                                >
                              </dt>
                              <dd mat-line>
                                <span id="dd-phase-2-status">{{
                                  device.device_components?.ipsecStatusPhase2[i]?.status
                                }}</span>
                              </dd>
                            </ng-container>
                            <ng-container *ngIf="device.device_components?.ipsecl2tp">
                              <dt mat-line>
                                <span id="dt-l2tp-status"
                                  >{{ 'DEVICE_PROPERTIES.ipsec.l2tp_status' | translate }}.{{ i }}</span
                                >
                              </dt>
                              <dd mat-line>
                                <span id="dd-l2tp-status">{{ device.device_components?.ipsecl2tp[i]?.status }}</span>
                              </dd>
                            </ng-container>
                          </ng-container>
                        </ng-container>
                      </div>
                    </div>
                    <!-- IPsec Status End -->
                    <!-- Management Interfaces START -->
                    <div class="property-table-field">
                      <div
                        class="info-title"
                        *ngIf="
                          hasOneOfProperties(device, [
                            'httpEnable.0',
                            'httpsEnable.0',
                            'telnetEnable.0',
                            'sshEnable.0',
                            'profinetEnable.0'
                          ])
                        "
                      >
                        {{ 'DEVICE_PROPERTIES.management_interfaces.title' | translate }}
                      </div>
                      <div class="property-table-field-content">
                        <dt *ngIf="device.device_components['httpEnable.0']" mat-line>
                          <span id="dt-http-port">{{
                            'DEVICE_PROPERTIES.management_interfaces.http_port' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components['httpEnable.0']" mat-line>
                          <span id="dd-http-port"
                            >{{ device.device_components['httpEnable.0']?.status }} /
                            {{ device.device_components['httpPort.0']?.status }}</span
                          >
                        </dd>
                        <dt *ngIf="device.device_components['httpsEnable.0']" mat-line>
                          <span id="dt-https-port">{{
                            'DEVICE_PROPERTIES.management_interfaces.https_port' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components['httpsEnable.0']" mat-line>
                          <span id="dd-https-port"
                            >{{ device.device_components['httpsEnable.0']?.status }} /
                            {{ device.device_components['httpsPort.0']?.status }}</span
                          >
                        </dd>
                        <dt *ngIf="device.device_components['telnetEnable.0']" mat-line>
                          <span id="dt-telnet-port">{{
                            'DEVICE_PROPERTIES.management_interfaces.telnet_port' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components['telnetEnable.0']" mat-line>
                          <span id="dd-telnet-port"
                            >{{ device.device_components['telnetEnable.0']?.status }} /
                            {{ device.device_components['telnetPort.0']?.status }}</span
                          >
                        </dd>
                        <dt *ngIf="device.device_components['sshEnable.0']" mat-line>
                          <span id="dt-ssh-enable">{{
                            'DEVICE_PROPERTIES.management_interfaces.ssh_port' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components['sshEnable.0']" mat-line>
                          <span id="dd-ssh-enable"
                            >{{ device.device_components['sshEnable.0']?.status }} /
                            {{ device.device_components['sshPort.0']?.status }}</span
                          >
                        </dd>
                        <dt *ngIf="device.device_components['profinetEnable.0']" mat-line>
                          <span id="dt-profinet-enable">{{
                            'DEVICE_PROPERTIES.management_interfaces.profinet_enabled' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components['profinetEnable.0']" mat-line>
                          <span id="dd-profinet-enable">{{
                            device.device_components['profinetEnable.0']?.status
                          }}</span>
                        </dd>
                      </div>
                    </div>
                    <!-- Management Interfaces END -->
                    <!-- SNMP Information START -->
                    <div class="property-table-field">
                      <div
                        class="info-title"
                        *ngIf="
                          hasOneOfProperties(device, [
                            'snmpReadCommunity',
                            'snmpTrapCommunity',
                            'trapServerAddr',
                            'snmpTrap2Community',
                            'trap2ServerAddr',
                            'snmpInformEnable',
                            'snmpInformRetries',
                            'snmpInformTimeout',
                            'usnmpTrapCommunity',
                            'utrapServerAddr',
                            'usnmpTrap2Community',
                            'utrap2ServerAddr'
                          ])
                        "
                      >
                        {{ 'DEVICE_PROPERTIES.snmp.title' | translate }}
                      </div>
                      <div class="property-table-field-content">
                        <dt *ngIf="device.device_components.snmpReadCommunity" mat-line>
                          <span id="dt-read-community">{{ 'DEVICE_PROPERTIES.snmp.read_community' | translate }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.snmpReadCommunity" mat-line>
                          <span id="dd-read-community">{{ device.device_components.snmpReadCommunity?.status }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.snmpTrapCommunity" mat-line>
                          <span id="dt-snmp-trap-community-1">{{
                            'DEVICE_PROPERTIES.snmp.1st_trap_community' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.snmpTrapCommunity" mat-line>
                          <span id="dd-snmp-trap-community-1">{{
                            device.device_components.snmpTrapCommunity?.status ||
                              device.device_components.snmpTrapCommunity[0]?.status
                          }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.trapServerAddr" mat-line>
                          <span id="dt-trap-server-addr-1">{{
                            'DEVICE_PROPERTIES.snmp.trap_server_address_1' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.trapServerAddr" mat-line>
                          <span id="dd-trap-server-addr-1">{{
                            device.device_components.trapServerAddr?.status ||
                              device.device_components.trapServerAddr[0]?.status
                          }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.snmpTrap2Community" mat-line>
                          <span id="dt-trap-server-community-2">{{
                            'DEVICE_PROPERTIES.snmp.2nd_trap_server_community' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.snmpTrap2Community" mat-line>
                          <span id="dd-trap-server-community-2">{{
                            device.device_components.snmpTrap2Community?.status ||
                              device.device_components.snmpTrap2Community[0]?.status
                          }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.trap2ServerAddr" mat-line>
                          <span id="dt-trap-server-addr-2">{{
                            'DEVICE_PROPERTIES.snmp.trap_server_address_2' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.trap2ServerAddr" mat-line>
                          <span id="dd-trap-server-addr-2">{{
                            device.device_components.trap2ServerAddr?.status ||
                              device.device_components.trap2ServerAddr[0]?.status
                          }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.usnmpTrapCommunity" mat-line>
                          <span id="dt-snmp-trap-community-1">{{
                            'DEVICE_PROPERTIES.snmp.1st_trap_community' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.usnmpTrapCommunity" mat-line>
                          <span id="dd-snmp-trap-community-1">{{
                            device.device_components.usnmpTrapCommunity?.status
                          }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.utrapServerAddr" mat-line>
                          <span id="dt-trap-server-addr-1">{{
                            'DEVICE_PROPERTIES.snmp.trap_server_address_1' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.utrapServerAddr" mat-line>
                          <span id="dd-trap-server-addr-1">{{ device.device_components.utrapServerAddr?.status }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.usnmpTrap2Community" mat-line>
                          <span id="dt-trap-server-community-2">{{
                            'DEVICE_PROPERTIES.snmp.2nd_trap_server_community' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.usnmpTrap2Community" mat-line>
                          <span id="dd-trap-server-community-2">{{
                            device.device_components.usnmpTrap2Community?.status
                          }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.utrap2ServerAddr" mat-line>
                          <span id="dt-trap-server-addr-2">{{
                            'DEVICE_PROPERTIES.snmp.trap_server_address_2' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.utrap2ServerAddr" mat-line>
                          <span id="dd-trap-server-addr-2">{{
                            device.device_components.utrap2ServerAddr?.status
                          }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.snmpInformEnable" mat-line>
                          <span id="dt-inform-enabled">{{ 'DEVICE_PROPERTIES.snmp.inform_enabled' | translate }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.snmpInformEnable" mat-line>
                          <span id="dd-inform-enabled">{{ device.device_components.snmpInformEnable?.status }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.snmpInformRetries" mat-line>
                          <span id="dt-inform-retries">{{ 'DEVICE_PROPERTIES.snmp.inform_retries' | translate }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.snmpInformRetries" mat-line>
                          <span id="dd-inform-retries">{{ device.device_components.snmpInformRetries?.status }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.snmpInformTimeout" mat-line>
                          <span id="dt-inform-timeout">{{ 'DEVICE_PROPERTIES.snmp.inform_timeout' | translate }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.snmpInformTimeout" mat-line>
                          <span id="dd-inform-timeout">{{ device.device_components.snmpInformTimeout?.status }}</span>
                        </dd>
                      </div>
                    </div>
                    <!-- SNMP Information END -->
                    <!-- Other Device Properties START -->
                    <div *ngIf="isShowOtherDeviceProperties" class="property-table-field">
                      <div id="div-device-properties" class="info-title">
                        {{ 'DEVICE_PROPERTIES.other_device_properties.title' | translate }}
                      </div>
                      <div class="property-table-field-content">
                        <dt *ngIf="device.device_components.macAddress" mat-line>
                          <span id="dt-mac-address">{{
                            'DEVICE_PROPERTIES.other_device_properties.mac_address' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.macAddress" mat-line>
                          <span id="dd-mac-address">{{ device.device_components.macAddress?.status }}</span>
                        </dd>
                        <ng-template
                          *ngIf="device?.device_components?.ipAdEntAddr?.length > 0"
                          ngFor
                          let-ipAdEntAddr
                          let-i="index"
                          [ngForOf]="device?.device_components?.ipAdEntAddr"
                        >
                          <dt>
                            <span id="dt-ip-ad-ent-addr"
                              >{{ 'DEVICE_PROPERTIES.other_device_properties.ip_ad_ent_addr' | translate }}.{{
                                ipAdEntAddr.status
                              }}</span
                            >
                          </dt>
                          <dd>
                            <span id="dd-ip-ad-ent-addr">{{ ipAdEntAddr?.status }}</span>
                          </dd>
                        </ng-template>
                        <dt *ngIf="device.device_components.modelName" mat-line>
                          <span id="dt-model-name">{{
                            'DEVICE_PROPERTIES.other_device_properties.model_name' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.modelName" mat-line>
                          <span id="dd-model-name">{{ device.device_components.modelName?.status }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.enableAutoIpConfig" mat-line>
                          <span id="dt-auto-ip-config">{{
                            'DEVICE_PROPERTIES.other_device_properties.auto_ip_config' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.enableAutoIpConfig" mat-line>
                          <span id="dd-auto-ip-config">{{ device.device_components.enableAutoIpConfig?.status }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.IpAddr" mat-line>
                          <span id="dt-ip-address">{{
                            'DEVICE_PROPERTIES.other_device_properties.ip_address' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.IpAddr" mat-line>
                          <span id="dd-ip-address">{{ device.device_components.IpAddr?.status }} </span>
                        </dd>
                        <dt *ngIf="device.device_components.IpMask" mat-line>
                          <span id="dt-net-mask">{{
                            'DEVICE_PROPERTIES.other_device_properties.netmask' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.IpMask" mat-line>
                          <span id="dd-net-mask">{{ device.device_components.IpMask?.status }} </span>
                        </dd>
                        <dt *ngIf="device.device_components.defaultGateway" mat-line>
                          <span id="dt-default-gateway">{{
                            'DEVICE_PROPERTIES.other_device_properties.default_gateway' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.defaultGateway" mat-line>
                          <span id="dd-default-gateway">{{ device.device_components.defaultGateway?.status }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.dnsServer1IpAddr" mat-line>
                          <span id="dt-dns-1-ip-address">{{
                            'DEVICE_PROPERTIES.other_device_properties.dns_1_ip_address' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.dnsServer1IpAddr" mat-line>
                          <span id="dd-dns-1-ip-address">{{ device.device_components.dnsServer1IpAddr?.status }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.dnsServer2IpAddr" mat-line>
                          <span id="dt-dns-2-ip-address">{{
                            'DEVICE_PROPERTIES.other_device_properties.dns_2_ip_address' | translate
                          }}</span>
                        </dt>
                        <dd *ngIf="device.device_components.dnsServer2IpAddr" mat-line>
                          <span id="dd-dns-1-ip-address">{{ device.device_components.dnsServer2IpAddr?.status }}</span>
                        </dd>
                        <!-- this loop is used for Adding all other properties not hardcoded in this page -->
                        <ng-container *ngFor="let resource of displayedDeviceComponentsToArray(device)">
                          <ng-container
                            *ngIf="device.device_components[resource] && !propertyIsArray(device, resource)"
                          >
                            <dt mat-line>
                              <span id="dt-resource">{{ resource }}</span>
                            </dt>
                            <dd mat-line>
                              <span id="dd-resource">{{ device.device_components[resource].status }}</span>
                            </dd>
                          </ng-container>
                          <ng-container *ngIf="device.device_components[resource] && propertyIsArray(device, resource)">
                            <ng-container *ngFor="let row of device.device_components[resource]">
                              <dt id="dt-row-status">
                                <span>{{ resource }}.{{ row.index }}</span>
                              </dt>
                              <dd>
                                <span id="dd-row-status">{{ row.status }}</span>
                              </dd>
                            </ng-container>
                          </ng-container>
                        </ng-container>
                        <dt *ngIf="device.device_components.monitorCurrentMode" mat-line>
                          <span id="dt-monitor-current-mode">
                            {{ 'DEVICE_PROPERTIES.other_device_properties.monitor_current_mode' | translate }}
                          </span>
                        </dt>
                        <dd *ngIf="device.device_components.monitorCurrentMode" mat-line>
                          <span id="dd-monitor-current-mode">{{
                            device.device_components['monitorCurrentMode'][0].status
                          }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.monitorDownStreamRate" mat-line>
                          <span id="dt-monitor-down-stream-rate">
                            {{ 'DEVICE_PROPERTIES.other_device_properties.monitor_down_stream_rate' | translate }}
                          </span>
                        </dt>
                        <dd *ngIf="device.device_components.monitorDownStreamRate" mat-line>
                          <span id="dd-monitor-down-stream-rate">{{
                            device.device_components['monitorDownStreamRate'][0].status
                          }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.monitorSNR" mat-line>
                          <span id="dt-monitor-snr">
                            {{ 'DEVICE_PROPERTIES.other_device_properties.monitor_snr' | translate }}
                          </span>
                        </dt>
                        <dd *ngIf="device.device_components.monitorSNR" mat-line>
                          <span id="dd-monitor-snr">{{ device.device_components['monitorSNR'][0].status }}</span>
                        </dd>
                        <dt *ngIf="device.device_components.monitorUpStreamRate" mat-line>
                          <span id="dt-monitor-up-stream-rate">
                            {{ 'DEVICE_PROPERTIES.other_device_properties.monitor_up_stream_rate' | translate }}
                          </span>
                        </dt>
                        <dd *ngIf="device.device_components.monitorUpStreamRate" mat-line>
                          <span id="dd-monitor-up-stream-rate">{{
                            device.device_components['monitorUpStreamRate'][0].status
                          }}</span>
                        </dd>
                      </div>
                    </div>
                    <!-- Other Device Properties END -->
                  </ng-container>
                  <!-- Basic Device Properties END -->
                  <ng-container *ngIf="group?.device_group">
                    <div class="property-table-field basic-property-device-component">
                      <mat-form-field style="width: 100%">
                        <mat-select
                          placeholder="{{ 'DEVICE_PROPERTIES.selected_module' | translate }}"
                          (selectionChange)="onSelectionChange()"
                          [(value)]="selectedIopacModule"
                          (blur)="onBlur()"
                        >
                          <mat-option [value]="0">None</mat-option>
                          <mat-option *ngFor="let iopacDevice of iopacDevices" [value]="iopacDevice.id">
                            {{ iopacDevice.name }}: {{ iopacDevice.ip }}
                          </mat-option>
                        </mat-select>
                      </mat-form-field>
                      <div *ngIf="!networkService.selectedIopacDevice; else deviceInfo" class="not-selected">
                        <div>
                          <mat-icon class="not-selected-icon">announcement</mat-icon>
                        </div>
                        <div class="not-selected-text">{{ 'DEVICE_PROPERTIES.not_selected' | translate }}</div>
                      </div>
                      <ng-template #deviceInfo>
                        <div id="div-topology-sidenav-section-title-basic-prop" class="mat-line info-title">
                          {{ 'DEVICE_PROPERTIES.basic_property.title' | translate }}
                        </div>
                        <div class="property-table-field-content">
                          <dt mat-line>
                            <span id="dt-alias">{{ 'DEVICE_PROPERTIES.basic_property.alias' | translate }}</span>
                          </dt>
                          <dd mat-line>
                            <span id="dd-alias">{{ networkService.selectedIopacDevice.alias }}</span>
                          </dd>
                          <dt mat-line>
                            <span id="dt-model">{{ 'DEVICE_PROPERTIES.basic_property.model_name' | translate }}</span>
                          </dt>
                          <dd mat-line>
                            <span id="dd-model">{{ networkService.selectedIopacDevice.model }}</span>
                          </dd>
                          <dt mat-line>
                            <span id="dt-mac">{{ 'DEVICE_PROPERTIES.basic_property.mac_address' | translate }} </span>
                          </dt>
                          <dd mat-line>
                            <span id="dd-mac">{{
                              globalService.formatMACAddress(networkService.selectedIopacDevice.mac)
                            }}</span>
                          </dd>
                          <ng-container *ngIf="networkService.selectedIopacDevice?.Availability">
                            <dt mat-line>
                              <span id="dt-availability">{{
                                'DEVICE_PROPERTIES.basic_property.availability' | translate
                              }}</span>
                            </dt>
                            <dd mat-line>
                              <span id="dd-availability">{{ networkService.selectedIopacDevice.Availability }}</span>
                            </dd>
                          </ng-container>
                          <dt mat-line>
                            <span id="dt-system-description">{{
                              'DEVICE_PROPERTIES.basic_property.system_description' | translate
                            }}</span>
                          </dt>
                          <dd mat-line>
                            <span id="dd-system-description">{{
                              networkService.selectedIopacDevice.device_components.sysDescr?.status
                            }}</span>
                          </dd>
                          <dt mat-line>
                            <span id="dt-system-system-object-id">{{
                              'DEVICE_PROPERTIES.basic_property.system_object_id' | translate
                            }}</span>
                          </dt>
                          <dd mat-line>
                            <span id="dd-system-system-object-id">{{
                              networkService.selectedIopacDevice.sysobjid
                            }}</span>
                          </dd>
                          <dt mat-line>
                            <span id="dt-system-contact">
                              {{ 'DEVICE_PROPERTIES.basic_property.system_contact' | translate }}</span
                            >
                          </dt>
                          <dd mat-line>
                            <span id="dd-system-contact">{{
                              networkService.selectedIopacDevice.device_components.sysContact?.status
                            }}</span>
                          </dd>
                          <dt mat-line>
                            <span id="dt-system-name"
                              >{{ 'DEVICE_PROPERTIES.basic_property.system_name' | translate }}
                            </span>
                          </dt>
                          <dd mat-line>
                            <span id="dd-system-name">{{
                              networkService.selectedIopacDevice.device_components.sysName?.status
                            }}</span>
                          </dd>
                          <dt mat-line>
                            <span id="dt-system-location">{{
                              'DEVICE_PROPERTIES.basic_property.system_location' | translate
                            }}</span>
                          </dt>
                          <dd mat-line>
                            <span id="dd-system-location">{{
                              networkService.selectedIopacDevice.device_components.sysLocation?.status
                            }}</span>
                          </dd>
                          <dt *ngIf="networkService.selectedIopacDevice.device_components.serialNumber" mat-line>
                            <span id="dt-serial-number">{{
                              'DEVICE_PROPERTIES.basic_property.serial_number' | translate
                            }}</span>
                          </dt>
                          <dd *ngIf="networkService.selectedIopacDevice.device_components.serialNumber" mat-line>
                            <span id="dd-serial-number">{{
                              networkService.selectedIopacDevice.device_components.serialNumber?.status
                            }}</span>
                          </dd>
                          <dt *ngIf="networkService.selectedIopacDevice.device_components['serialNumber.0']" mat-line>
                            <span id="dt-serial-number-0">{{
                              'DEVICE_PROPERTIES.basic_property.serial_number' | translate
                            }}</span>
                          </dt>
                          <dd *ngIf="networkService.selectedIopacDevice.device_components['serialNumber.0']" mat-line>
                            <span id="dd-serial-number-0">{{
                              networkService.selectedIopacDevice.device_components['serialNumber.0']?.status
                            }}</span>
                          </dd>
                          <dt *ngIf="networkService.selectedIopacDevice.device_components.firmwareVersion" mat-line>
                            <span id="dt-firmware-version">{{
                              'DEVICE_PROPERTIES.basic_property.fw_version' | translate
                            }}</span>
                          </dt>
                          <dd *ngIf="networkService.selectedIopacDevice.device_components.firmwareVersion" mat-line>
                            <span id="dd-firmware-version">{{
                              networkService.selectedIopacDevice.device_components.firmwareVersion?.status
                            }}</span>
                          </dd>
                          <ng-container *ngIf="networkService.selectedIopacDevice.device_components.imageVersion">
                            <dt mat-line>
                              <span id="dt-firmware-system-version">{{
                                'DEVICE_PROPERTIES.basic_property.fw_system_version' | translate
                              }}</span>
                            </dt>
                            <dd mat-line>
                              <span id="dd-firmware-system-version">{{
                                networkService.selectedIopacDevice.device_components.imageVersion[0]?.status
                              }}</span>
                            </dd>
                          </ng-container>
                          <ng-container *ngIf="networkService.selectedIopacDevice.device_components.biosVersion">
                            <dt mat-line>
                              <span id="dt-bios-version">{{
                                'DEVICE_PROPERTIES.basic_property.bios_version' | translate
                              }}</span>
                            </dt>
                            <dd mat-line>
                              <span id="dd-bios-version">{{
                                networkService.selectedIopacDevice.device_components.biosVersion[0]?.status
                              }}</span>
                            </dd>
                          </ng-container>
                          <ng-container *ngIf="networkService.selectedIopacDevice.device_components.osType">
                            <dt mat-line>
                              <span id="dt-os-type">{{ 'DEVICE_PROPERTIES.basic_property.os_type' | translate }}</span>
                            </dt>
                            <dd mat-line>
                              <span id="dd-os-type">{{
                                networkService.selectedIopacDevice.device_components.osType[0]?.status
                              }}</span>
                            </dd>
                          </ng-container>
                          <dt *ngIf="networkService.selectedIopacDevice.device_components.cpuLoading" mat-line>
                            <span id="dt-cpu-loading">{{
                              'DEVICE_PROPERTIES.basic_property.cpu_loading' | translate
                            }}</span>
                          </dt>
                          <dd *ngIf="networkService.selectedIopacDevice.device_components.cpuLoading" mat-line>
                            <span id="dd-cpu-loading">{{
                              globalService.getDeviceProp(
                                networkService.selectedIopacDevice.device_components.cpuLoading
                              )
                            }}</span>
                          </dd>
                          <dt *ngIf="networkService.selectedIopacDevice.device_components['cpuLoading5s.0']" mat-line>
                            <span id="dt-cpu-loading-5s">{{
                              'DEVICE_PROPERTIES.basic_property.cpu_loading_5_seconds' | translate
                            }}</span>
                          </dt>
                          <dd *ngIf="networkService.selectedIopacDevice.device_components['cpuLoading5s.0']" mat-line>
                            <span id="dd-cpu-loading-5s">{{
                              networkService.selectedIopacDevice.device_components['cpuLoading5s.0']?.status
                            }}</span>
                          </dd>
                          <dt *ngIf="networkService.selectedIopacDevice.device_components['cpuLoading30s.0']" mat-line>
                            <span id="dt-cpu-loading-30s">{{
                              'DEVICE_PROPERTIES.basic_property.cpu_loading_30_seconds' | translate
                            }}</span>
                          </dt>
                          <dd *ngIf="networkService.selectedIopacDevice.device_components['cpuLoading30s.0']" mat-line>
                            <span id="dd-cpu-loading-30s">{{
                              networkService.selectedIopacDevice.device_components['cpuLoading30s.0']?.status
                            }}</span>
                          </dd>
                          <dt *ngIf="networkService.selectedIopacDevice.device_components['cpuLoading300s.0']" mat-line>
                            <span id="dt-cpu-loading-300s">{{
                              'DEVICE_PROPERTIES.basic_property.cpu_loading_300_seconds' | translate
                            }}</span>
                          </dt>
                          <dd *ngIf="networkService.selectedIopacDevice.device_components['cpuLoading300s.0']" mat-line>
                            <span id="dd-cpu-loading-300s">{{
                              networkService.selectedIopacDevice.device_components['cpuLoading300s.0']?.status
                            }}</span>
                          </dd>
                          <dt *ngIf="networkService.selectedIopacDevice.device_components['cpuLoading5s']" mat-line>
                            <span id="dt-cpu-loading-5s">{{
                              'DEVICE_PROPERTIES.basic_property.cpu_loading_5_seconds' | translate
                            }}</span>
                          </dt>
                          <dd *ngIf="networkService.selectedIopacDevice.device_components['cpuLoading5s']" mat-line>
                            <span id="dd-cpu-loading-5s">{{
                              networkService.selectedIopacDevice.device_components['cpuLoading5s']?.status
                            }}</span>
                          </dd>
                          <dt *ngIf="networkService.selectedIopacDevice.device_components['cpuLoading30s']" mat-line>
                            <span id="dt-cpu-loading-30s">{{
                              'DEVICE_PROPERTIES.basic_property.cpu_loading_30_seconds' | translate
                            }}</span>
                          </dt>
                          <dd *ngIf="networkService.selectedIopacDevice.device_components['cpuLoading30s']" mat-line>
                            <span id="dd-cpu-loading-30s">{{
                              networkService.selectedIopacDevice.device_components['cpuLoading30s']?.status
                            }}</span>
                          </dd>
                          <dt *ngIf="networkService.selectedIopacDevice.device_components['cpuLoading300s']" mat-line>
                            <span id="dt-cpu-loading-300s">{{
                              'DEVICE_PROPERTIES.basic_property.cpu_loading_300_seconds' | translate
                            }}</span>
                          </dt>
                          <dd *ngIf="networkService.selectedIopacDevice.device_components['cpuLoading300s']" mat-line>
                            <span id="dd-cpu-loading-300s">{{
                              networkService.selectedIopacDevice.device_components['cpuLoading300s']?.status
                            }}</span>
                          </dd>
                          <ng-container *ngIf="networkService.selectedIopacDevice.device_components.cpuLoading1m">
                            <dt mat-line>
                              <span id="dt-cpu-utilization-60s">{{
                                'DEVICE_PROPERTIES.basic_property.cpu_utilization_60_seconds' | translate
                              }}</span>
                            </dt>
                            <dd mat-line>
                              <span id="dd-cpu-utilization-60s">{{
                                networkService.selectedIopacDevice.device_components.cpuLoading1m[0]?.status
                              }}</span>
                            </dd>
                          </ng-container>
                          <ng-container *ngIf="networkService.selectedIopacDevice.device_components.cpuLoading5m">
                            <dt mat-line>
                              <span id="dt-cpu-utilization-300s">{{
                                'DEVICE_PROPERTIES.basic_property.cpu_utilization_300_seconds' | translate
                              }}</span>
                            </dt>
                            <dd mat-line>
                              <span id="dd-cpu-utilization-300s">{{
                                networkService.selectedIopacDevice.device_components.cpuLoading5m[0]?.status
                              }}</span>
                            </dd>
                          </ng-container>
                          <ng-container *ngIf="networkService.selectedIopacDevice.device_components.cpuLoading15m">
                            <dt mat-line>
                              <span id="dt-cpu-utilization-900s">{{
                                'DEVICE_PROPERTIES.basic_property.cpu_utilization_900_seconds' | translate
                              }}</span>
                            </dt>
                            <dd mat-line>
                              <span id="dd-cpu-utilization-900s">{{
                                networkService.selectedIopacDevice.device_components.cpuLoading15m[0]?.status
                              }}</span>
                            </dd>
                          </ng-container>
                          <dt *ngIf="networkService.selectedIopacDevice.device_components['memoryUsage.0']" mat-line>
                            <span id="dt-memory-usage-0">{{
                              'DEVICE_PROPERTIES.basic_property.memory_usage_unit' | translate
                            }}</span>
                          </dt>
                          <dd *ngIf="networkService.selectedIopacDevice.device_components['memoryUsage.0']" mat-line>
                            <span id="dd-memory-usage-0">{{
                              networkService.selectedIopacDevice.device_components['memoryUsage.0']?.status
                            }}</span>
                          </dd>
                          <dt *ngIf="networkService.selectedIopacDevice.device_components.memoryUtilization" mat-line>
                            <span id="dt-memory-usage">{{
                              'DEVICE_PROPERTIES.basic_property.memory_usage_unit' | translate
                            }}</span>
                          </dt>
                          <dd *ngIf="networkService.selectedIopacDevice.device_components.memoryUtilization" mat-line>
                            <span id="dd-memory-usage">{{
                              globalService.getDeviceProp(
                                networkService.selectedIopacDevice.device_components.memoryUtilization
                              )
                            }}</span>
                          </dd>
                          <ng-container *ngIf="networkService.selectedIopacDevice.device_components.partitionUsage">
                            <dt mat-line>
                              <span id="dt-disk-utilization-unit">{{
                                'DEVICE_PROPERTIES.basic_property.disk_utilization_unit' | translate
                              }}</span>
                            </dt>
                            <dd mat-line>
                              <span id="dd-disk-utilization-unit">{{
                                networkService.selectedIopacDevice.device_components.partitionUsage[0]?.status
                              }}</span>
                            </dd>
                          </ng-container>
                          <dt
                            *ngIf="networkService.selectedIopacDevice.device_components['powerConsumption.0']"
                            mat-line
                          >
                            <span id="dt-power-comsumption">{{
                              'DEVICE_PROPERTIES.basic_property.power_comsumption' | translate
                            }}</span>
                          </dt>
                          <dd
                            *ngIf="networkService.selectedIopacDevice.device_components['powerConsumption.0']"
                            mat-line
                          >
                            <span id="dd-power-comsumption">{{
                              networkService.selectedIopacDevice.device_components['powerConsumption.0']?.status
                            }}</span>
                          </dd>
                          <dt *ngIf="networkService.selectedIopacDevice.device_components['sysUpTime']" mat-line>
                            <span id="dt-sys-up-time">{{
                              'DEVICE_PROPERTIES.basic_property.system_up_time' | translate
                            }}</span>
                          </dt>
                          <dd *ngIf="networkService.selectedIopacDevice.device_components['sysUpTime']" mat-line>
                            <span id="dd-sys-up-time">
                              {{
                                globalService.formatMinutes(
                                  networkService.selectedIopacDevice.device_components.sysUpTime.status / 6000
                                )
                              }}
                            </span>
                          </dd>
                        </div>
                        <!-- Redundancy START -->
                        <div>
                          <div
                            class="info-title"
                            *ngIf="
                              hasOneOfProperties(networkService.selectedIopacDevice, ['activeProtocolOfRedundancy'])
                            "
                          >
                            {{ 'DEVICE_PROPERTIES.redundancy.title' | translate }}
                          </div>
                          <div class="property-table-field-content">
                            <dt
                              *ngIf="networkService.selectedIopacDevice.device_components.activeProtocolOfRedundancy"
                              mat-line
                            >
                              <span id="dt-active-redundancy-protocol">{{
                                'DEVICE_PROPERTIES.redundancy.active_redundancy_protocol' | translate
                              }}</span>
                            </dt>
                            <dd
                              *ngIf="networkService.selectedIopacDevice.device_components.activeProtocolOfRedundancy"
                              mat-line
                            >
                              <span id="dd-active-redundancy-protocol">{{
                                networkService.selectedIopacDevice.device_components.activeProtocolOfRedundancy?.status
                              }}</span>
                            </dd>
                            <dt *ngIf="networkService.selectedIopacDevice.device_components.rstpEnabled" mat-line>
                              <span id="dt-rstp-enabled">{{ 'DEVICE_PROPERTIES.redundancy.rstp' | translate }}</span>
                            </dt>
                            <dd *ngIf="networkService.selectedIopacDevice.device_components.rstpEnabled" mat-line>
                              <span id="dd-rstp-enabled">{{
                                networkService.selectedIopacDevice.device_components.rstpEnabled?.status
                              }}</span>
                            </dd>
                            <dt *ngIf="networkService.selectedIopacDevice.device_components.trv2Enabled" mat-line>
                              <span id="dt-trv2-enabled">{{ 'DEVICE_PROPERTIES.redundancy.trv2' | translate }}</span>
                            </dt>
                            <dd *ngIf="networkService.selectedIopacDevice.device_components.trv2Enabled" mat-line>
                              <span id="dd-trv2-enabled">{{
                                networkService.selectedIopacDevice.device_components.trv2Enabled?.status
                              }}</span>
                            </dd>
                            <dt *ngIf="networkService.selectedIopacDevice.device_components.tcEnabled" mat-line>
                              <span id="dt-tc">{{ 'DEVICE_PROPERTIES.redundancy.tc' | translate }}</span>
                            </dt>
                            <dd *ngIf="networkService.selectedIopacDevice.device_components.tcEnabled" mat-line>
                              <span id="dd-tc">{{
                                networkService.selectedIopacDevice.device_components.tcEnabled?.status
                              }}</span>
                            </dd>
                            <dt *ngIf="networkService.selectedIopacDevice.device_components.dhEnabled" mat-line>
                              <span id="dt-dh">{{ 'DEVICE_PROPERTIES.redundancy.dh' | translate }}</span>
                            </dt>
                            <dd *ngIf="networkService.selectedIopacDevice.device_components.dhEnabled" mat-line>
                              <span id="dd-tc">{{
                                networkService.selectedIopacDevice.device_components.dhEnabled?.status
                              }}</span>
                            </dd>
                            <dt *ngIf="networkService.selectedIopacDevice.device_components.iec624393Protocol" mat-line>
                              <span id="dt-dh">{{
                                'DEVICE_PROPERTIES.redundancy.iec_624393_redundancy_protocol' | translate
                              }}</span>
                            </dt>
                            <dd *ngIf="networkService.selectedIopacDevice.device_components.iec624393Protocol" mat-line>
                              <span id="dd-tc">{{
                                networkService.selectedIopacDevice.device_components.iec624393Protocol?.status
                              }}</span>
                            </dd>
                          </div>
                        </div>
                        <!-- Redundancy END -->
                        <!-- Power Status START -->
                        <div>
                          <div
                            class="info-title"
                            *ngIf="
                              hasOneOfProperties(networkService.selectedIopacDevice, [
                                'power1InputStatus',
                                'power2InputStatus'
                              ])
                            "
                          >
                            {{ 'DEVICE_PROPERTIES.power.title' | translate }}
                          </div>
                          <div class="property-table-field-content">
                            <dt *ngIf="networkService.selectedIopacDevice.device_components.power1InputStatus" mat-line>
                              <span id="dt-power-1-input-status">{{
                                'DEVICE_PROPERTIES.power.power_1_status' | translate
                              }}</span>
                            </dt>
                            <dd
                              *ngIf="
                                networkService.selectedIopacDevice.device_components.power1InputStatus &&
                                !propertyIsArray(networkService.selectedIopacDevice, 'power1InputStatus')
                              "
                              mat-line
                            >
                              <span id="dd-power-1-input-status">{{
                                networkService.selectedIopacDevice.device_components.power1InputStatus?.status
                              }}</span>
                            </dd>
                            <dd
                              *ngIf="
                                networkService.selectedIopacDevice.device_components.power1InputStatus &&
                                propertyIsArray(networkService.selectedIopacDevice, 'power1InputStatus')
                              "
                              mat-line
                            >
                              <span id="dd-power-1-input-status">{{
                                networkService.selectedIopacDevice.device_components.power1InputStatus[0].status
                              }}</span>
                            </dd>
                            <dt *ngIf="networkService.selectedIopacDevice.device_components.power2InputStatus" mat-line>
                              <span id="dt-power-2-input-status">{{
                                'DEVICE_PROPERTIES.power.power_2_status' | translate
                              }}</span>
                            </dt>
                            <dd
                              *ngIf="
                                networkService.selectedIopacDevice.device_components.power2InputStatus &&
                                !propertyIsArray(networkService.selectedIopacDevice, 'power2InputStatus')
                              "
                              mat-line
                            >
                              <span id="dd-power-2-input-status">{{
                                networkService.selectedIopacDevice.device_components.power2InputStatus?.status
                              }}</span>
                            </dd>
                            <dd
                              *ngIf="
                                networkService.selectedIopacDevice.device_components.power2InputStatus &&
                                propertyIsArray(networkService.selectedIopacDevice, 'power2InputStatus')
                              "
                              mat-line
                            >
                              <span id="dd-power-2-input-status">{{
                                networkService.selectedIopacDevice.device_components.power2InputStatus[0].status
                              }}</span>
                            </dd>
                          </div>
                        </div>
                        <!-- Power Status END -->
                        <!-- Wireless Information START -->
                        <div
                          *ngIf="networkService.selectedIopacDevice.device_components?.devOperationMode"
                          class="property-table-field"
                        >
                          <div class="info-title">{{ 'DEVICE_PROPERTIES.wireless.title' | translate }}</div>
                          <div class="property-table-field-content">
                            <ng-template
                              *ngIf="
                                networkService.selectedIopacDevice.device_components?.devOperationMode?.length > 0;
                                else devOperationModeElse
                              "
                              ngFor
                              let-devOperationMode
                              let-i="index"
                              [ngForOf]="networkService.selectedIopacDevice.device_components?.devOperationMode"
                            >
                              <dt>
                                <span id="dt-operation-mode"
                                  >{{ 'IW.Title.operation_mode' | translate }}.{{ devOperationMode?.index }}</span
                                >
                              </dt>
                              <dd>
                                <span *ngIf="devOperationMode?.status === 'client'" id="dd-operation-mode">{{
                                  'IW.Title.client' | translate
                                }}</span>
                                <span *ngIf="devOperationMode?.status === 'ap'" id="dd-operation-mode">{{
                                  'IW.Title.ap' | translate
                                }}</span>
                                <span *ngIf="devOperationMode?.status === 'clientRouter'" id="dd-operation-mode">{{
                                  'IW.Title.client_router' | translate
                                }}</span>
                                <span *ngIf="devOperationMode?.status === 'disable'" id="dd-operation-mode">{{
                                  'IW.Title.disable' | translate
                                }}</span>
                              </dd>
                            </ng-template>
                            <ng-template #devOperationModeElse>
                              <dt>
                                <span id="dt-operation-mode">{{ 'IW.Title.operation_mode' | translate }}</span>
                              </dt>
                              <dd>
                                <span
                                  *ngIf="
                                    networkService.selectedIopacDevice.device_components?.devOperationMode?.status ===
                                    'client'
                                  "
                                  id="dd-operation-mode"
                                  >{{ 'IW.Title.client' | translate }}</span
                                >
                                <span
                                  *ngIf="
                                    networkService.selectedIopacDevice.device_components?.devOperationMode?.status ===
                                    'ap'
                                  "
                                  id="dd-operation-mode"
                                  >{{ 'IW.Title.ap' | translate }}</span
                                >
                                <span
                                  *ngIf="
                                    networkService.selectedIopacDevice.device_components?.devOperationMode?.status ===
                                    'clientRouter'
                                  "
                                  id="dd-operation-mode"
                                  >{{ 'IW.Title.client_router' | translate }}</span
                                >
                                <span
                                  *ngIf="
                                    networkService.selectedIopacDevice.device_components?.devOperationMode?.status ===
                                    'disable'
                                  "
                                  >{{ 'IW.Title.disable' | translate }}</span
                                >
                              </dd>
                            </ng-template>
                            <ng-template
                              *ngIf="networkService.selectedIopacDevice.device_components?.devRfType?.length > 0"
                              ngFor
                              let-devRfType
                              let-i="index"
                              [ngForOf]="networkService.selectedIopacDevice.device_components?.devRfType"
                            >
                              <dt>
                                <span id="dt-rf-type"
                                  >{{ 'DEVICE_PROPERTIES.wireless.rf_type' | translate }}.{{ devRfType?.index }}</span
                                >
                              </dt>
                              <dd>
                                <span id="dd-rf-type">{{ devRfType?.status }}</span>
                              </dd>
                            </ng-template>
                            <ng-template
                              *ngIf="networkService.selectedIopacDevice.device_components?.devTxPowerdBm?.length > 0"
                              ngFor
                              let-devTxPowerdBm
                              let-i="index"
                              [ngForOf]="networkService.selectedIopacDevice.device_components?.devTxPowerdBm"
                            >
                              <dt>
                                <span id="dt-dev-tx-power-dbm"
                                  >{{ 'IW.Title.tx_power_unit' | translate }}.{{ devTxPowerdBm?.index }}</span
                                >
                              </dt>
                              <dd>
                                <span id="dd-dev-tx-power-dbm">{{ devTxPowerdBm?.status }}</span>
                              </dd>
                            </ng-template>
                            <ng-template
                              *ngIf="networkService.selectedIopacDevice.device_components?.wlanBSSID?.length > 0"
                              ngFor
                              let-wlanBSSID
                              let-i="index"
                              [ngForOf]="networkService.selectedIopacDevice.device_components?.wlanBSSID"
                            >
                              <dt>
                                <span id="dt-bssid">{{ 'IW.Title.BSSID' | translate }}.{{ wlanBSSID?.index }}</span>
                              </dt>
                              <dd>
                                <span id="dd-bssid">{{ wlanBSSID?.status }}</span>
                              </dd>
                            </ng-template>
                            <ng-template
                              *ngIf="networkService.selectedIopacDevice.device_components?.wlanChannel?.length > 0"
                              ngFor
                              let-wlanChannel
                              let-i="index"
                              [ngForOf]="networkService.selectedIopacDevice.device_components.wlanChannel"
                            >
                              <dt>
                                <span id="dt-channel"
                                  >{{ 'IW.Title.channel' | translate }}.{{ wlanChannel?.index }}</span
                                >
                              </dt>
                              <dd>
                                <span id="dd-channel">{{ wlanChannel?.status }}</span>
                              </dd>
                            </ng-template>
                            <ng-container
                              *ngIf="
                                globalService.getDeviceProp(
                                  networkService.selectedIopacDevice.device_components.devOperationMode
                                ) === 'client' ||
                                globalService.getDeviceProp(
                                  networkService.selectedIopacDevice.device_components.devOperationMode
                                ) === 'clientRouter'
                              "
                            >
                              <ng-template
                                *ngIf="
                                  networkService.selectedIopacDevice.device_components?.wirelessStatusNoiseLevel
                                    ?.length > 0
                                "
                                ngFor
                                let-wirelessStatusNoiseLevel
                                let-i="index"
                                [ngForOf]="
                                  networkService.selectedIopacDevice.device_components?.wirelessStatusNoiseLevel
                                "
                              >
                                <dt>
                                  <span id="dt-noise-level"
                                    >{{ 'IW.Title.noise_floor_unit' | translate }}.{{
                                      wirelessStatusNoiseLevel?.index
                                    }}</span
                                  >
                                </dt>
                                <dd>
                                  <span id="dd-noise-level">{{ wirelessStatusNoiseLevel?.status }}</span>
                                </dd>
                              </ng-template>
                              <ng-template
                                *ngIf="
                                  networkService.selectedIopacDevice.device_components?.wirelessStatusRSSI?.length > 0;
                                  else wirelessStatusRSSIElse
                                "
                                ngFor
                                let-wirelessStatusRSSI
                                let-i="index"
                                [ngForOf]="networkService.selectedIopacDevice.device_components?.wirelessStatusRSSI"
                              >
                                <dt>
                                  <span id="dt-rssi"
                                    >{{ 'IW.Title.RSSI' | translate }}.{{ wirelessStatusRSSI?.index }}</span
                                  >
                                </dt>
                                <dd>
                                  <span id="dd-rssi">{{ wirelessStatusRSSI?.status }}</span>
                                </dd>
                              </ng-template>
                              <ng-template #wirelessStatusRSSIElse>
                                <dt>
                                  <span id="dt-rssi">{{ 'IW.Title.RSSI' | translate }}</span>
                                </dt>
                                <dd>
                                  <span id="dd-rssi">{{
                                    networkService.selectedIopacDevice.device_components?.wirelessStatusRSSI?.status
                                  }}</span>
                                </dd>
                              </ng-template>
                              <ng-template
                                *ngIf="
                                  networkService.selectedIopacDevice.device_components['wirelessStatusSNR-A']?.length >
                                    0;
                                  else wirelessStatusSnrAElse
                                "
                                ngFor
                                let-wirelessStatusSnrA
                                let-i="index"
                                [ngForOf]="networkService.selectedIopacDevice.device_components['wirelessStatusSNR-A']"
                              >
                                <dt>
                                  <span id="dt-snr-a"
                                    >{{ 'IW.Title.SNR_A' | translate }}.{{ wirelessStatusSnrA?.index }}</span
                                  >
                                </dt>
                                <dd>
                                  <span id="dd-snr-a">{{ wirelessStatusSnrA?.status }}</span>
                                </dd>
                              </ng-template>
                              <ng-template #wirelessStatusSnrAElse>
                                <dt>
                                  <span id="dt-snr-a">{{ 'IW.Title.SNR_A' | translate }}</span>
                                </dt>
                                <dd>
                                  <span id="dd-snr-a">{{
                                    networkService.selectedIopacDevice.device_components['wirelessStatusSNR-A']?.status
                                  }}</span>
                                </dd>
                              </ng-template>
                              <ng-template
                                *ngIf="
                                  networkService.selectedIopacDevice.device_components['wirelessStatusSNR-B']?.length >
                                    0;
                                  else wirelessStatusSnrBElse
                                "
                                ngFor
                                let-wirelessStatusSnrB
                                let-i="index"
                                [ngForOf]="networkService.selectedIopacDevice.device_components['wirelessStatusSNR-B']"
                              >
                                <dt>
                                  <span id="dt-snr-b"
                                    >{{ 'IW.Title.SNR_B' | translate }}.{{ wirelessStatusSnrB?.index }}</span
                                  >
                                </dt>
                                <dd>
                                  <span id="dd-snr-b">{{ wirelessStatusSnrB?.status }}</span>
                                </dd>
                              </ng-template>
                              <ng-template #wirelessStatusSnrBElse>
                                <ng-container
                                  *ngIf="networkService.selectedIopacDevice.device_components['wirelessStatusSNR-B']"
                                >
                                  <dt>
                                    <span id="dt-snr-b">{{ 'IW.Title.SNR_B' | translate }}</span>
                                  </dt>
                                  <dd>
                                    <span id="dd-snr-b">{{
                                      networkService.selectedIopacDevice.device_components['wirelessStatusSNR-B']
                                        ?.status
                                    }}</span>
                                  </dd>
                                </ng-container>
                              </ng-template>
                              <ng-template
                                *ngIf="
                                  networkService.selectedIopacDevice.device_components?.wlanConnectionTime?.length > 0;
                                  else wlanConnectionTimeElse
                                "
                                ngFor
                                let-wlanConnectionTime
                                let-i="index"
                                [ngForOf]="networkService.selectedIopacDevice.device_components?.wlanConnectionTime"
                              >
                                <dt>
                                  <span id="dt-wlan-connection-time"
                                    >{{ 'IW.Title.connected' | translate }}.{{ wlanConnectionTime?.index }}</span
                                  >
                                </dt>
                                <dd>
                                  <span id="dd-wlan-connection-time">{{
                                    globalService.formatMinutes(wlanConnectionTime?.status / 6000)
                                  }}</span>
                                </dd>
                              </ng-template>
                              <ng-template #wlanConnectionTimeElse>
                                <dt>
                                  <span id="dt-wlan-connection-time">{{ 'IW.Title.connected' | translate }}</span>
                                </dt>
                                <dd>
                                  <span id="dd-wlan-connection-time">{{
                                    globalService.formatMinutes(
                                      networkService.selectedIopacDevice.device_components?.wlanConnectionTime?.status /
                                        6000
                                    )
                                  }}</span>
                                </dd>
                              </ng-template>
                              <ng-template
                                *ngIf="
                                  networkService.selectedIopacDevice.device_components?.wlanSignal?.length > 0;
                                  else wlanSignalElse
                                "
                                ngFor
                                let-wlanSignal
                                let-i="index"
                                [ngForOf]="networkService.selectedIopacDevice.device_components.wlanSignal"
                              >
                                <dt>
                                  <span id="dt-signal"
                                    >{{ 'IW.Title.signal_level' | translate }}.{{ wlanSignal?.index }}</span
                                  >
                                </dt>
                                <dd>
                                  <span id="dd-signal">{{ wlanSignal?.status }}</span>
                                </dd>
                              </ng-template>
                              <ng-template #wlanSignalElse>
                                <ng-template
                                  *ngIf="
                                    globalService.getDeviceProp(
                                      networkService.selectedIopacDevice.device_components.devOperationMode
                                    ) === 'client' ||
                                    globalService.getDeviceProp(
                                      networkService.selectedIopacDevice.device_components.devOperationMode
                                    ) === 'clientRouter'
                                  "
                                >
                                  <dt>
                                    <span id="dt-signal">{{ 'IW.Title.signal_level' | translate }}</span>
                                  </dt>
                                  <dd>
                                    <span id="dd-signal">{{
                                      networkService.selectedIopacDevice.device_components.wlanSignal.status
                                    }}</span>
                                  </dd>
                                </ng-template>
                              </ng-template>
                              <ng-template
                                *ngIf="
                                  networkService.selectedIopacDevice.device_components?.wlanTxRate?.length > 0;
                                  else wlanTxRateElse
                                "
                                ngFor
                                let-wlanTxRate
                                let-i="index"
                                [ngForOf]="networkService.selectedIopacDevice.device_components.wlanTxRate"
                              >
                                <dt>
                                  <span id="dt-tx-rate"
                                    >{{ 'IW.Title.tx_rate_unit' | translate }}.{{ wlanTxRate?.index }}</span
                                  >
                                </dt>
                                <dd>
                                  <span id="dd-tx-rate">{{ wlanTxRate?.status }}</span>
                                </dd>
                              </ng-template>
                              <ng-template #wlanTxRateElse>
                                <dt>
                                  <span id="dt-tx-rate">{{ 'IW.Title.tx_rate_unit' | translate }}</span>
                                </dt>
                                <dd>
                                  <span id="dd-tx-rate">{{
                                    networkService.selectedIopacDevice.device_components.wlanTxRate?.status
                                  }}</span>
                                </dd>
                              </ng-template>
                            </ng-container>
                            <mat-expansion-panel
                              *ngIf="
                                networkService.selectedIopacDevice.device_components?.vapAuthType?.length > 0;
                                else vapAuthTypeElse
                              "
                              class="vap-node"
                            >
                              <mat-expansion-panel-header>
                                <mat-panel-title>
                                  {{ 'DEVICE_PROPERTIES.wireless.vapAuthType' | translate }}
                                </mat-panel-title>
                              </mat-expansion-panel-header>
                              <div
                                *ngFor="
                                  let vapAuthType of networkService.selectedIopacDevice.device_components.vapAuthType
                                "
                                class="property-table-field-content"
                              >
                                <dt>
                                  <span id="dt-vap-auth-type-{{ vapAuthType?.index }}"
                                    >{{ 'DEVICE_PROPERTIES.wireless.vapAuthType' | translate }}.{{
                                      vapAuthType?.index
                                    }}</span
                                  >
                                </dt>
                                <dd>
                                  <span id="dd-vap-auth-type-{{ vapAuthType?.index }}">{{ vapAuthType?.status }}</span>
                                </dd>
                              </div>
                            </mat-expansion-panel>
                            <ng-template #vapAuthTypeElse>
                              <dt>
                                <span id="dt-vap-auth-type">{{
                                  'DEVICE_PROPERTIES.wireless.vapAuthType' | translate
                                }}</span>
                              </dt>
                              <dd>
                                <span id="dd-vap-auth-type">{{
                                  networkService.selectedIopacDevice.device_components.vapAuthType?.status
                                }}</span>
                              </dd>
                            </ng-template>
                            <mat-expansion-panel
                              *ngIf="
                                networkService.selectedIopacDevice.device_components?.vapMgmtEncryption?.length > 0
                              "
                              class="vap-node"
                            >
                              <mat-expansion-panel-header>
                                <mat-panel-title>
                                  {{ 'DEVICE_PROPERTIES.wireless.vap_mgmt_encryption' | translate }}
                                </mat-panel-title>
                              </mat-expansion-panel-header>
                              <div
                                *ngFor="
                                  let vapMgmtEncryption of networkService.selectedIopacDevice.device_components
                                    .vapMgmtEncryption
                                "
                                class="property-table-field-content"
                              >
                                <dt>
                                  <span id="dt-vap-mgmt-encryption-{{ vapMgmtEncryption?.index }}"
                                    >{{ 'DEVICE_PROPERTIES.wireless.vap_mgmt_encryption' | translate }}.{{
                                      vapMgmtEncryption?.index
                                    }}</span
                                  >
                                </dt>
                                <dd>
                                  <span id="dd-vap-mgmt-encryption-{{ vapMgmtEncryption?.index }}">{{
                                    vapMgmtEncryption?.status
                                  }}</span>
                                </dd>
                              </div>
                            </mat-expansion-panel>
                            <mat-expansion-panel
                              *ngIf="networkService.selectedIopacDevice.device_components?.vapWpaEncrypt?.length > 0"
                              class="vap-node"
                            >
                              <mat-expansion-panel-header>
                                <mat-panel-title>
                                  {{ 'DEVICE_PROPERTIES.wireless.vap_wpa_encrypt' | translate }}
                                </mat-panel-title>
                              </mat-expansion-panel-header>
                              <div
                                *ngFor="
                                  let vapWpaEncrypt of networkService.selectedIopacDevice.device_components
                                    .vapWpaEncrypt
                                "
                                class="property-table-field-content"
                              >
                                <dt>
                                  <span id="dt-vap-wpa-encrypt-{{ vapWpaEncrypt?.index }}"
                                    >{{ 'DEVICE_PROPERTIES.wireless.vap_wpa_encrypt' | translate }}.{{
                                      vapWpaEncrypt?.index
                                    }}</span
                                  >
                                </dt>
                                <dd>
                                  <span id="dd-vap-wpa-encrypt-{{ vapWpaEncrypt?.index }}">{{
                                    vapWpaEncrypt?.status
                                  }}</span>
                                </dd>
                              </div>
                            </mat-expansion-panel>
                          </div>
                        </div>
                        <!-- Wireless Information END -->
                        <!-- Cellular Information START -->
                        <div
                          *ngIf="networkService.selectedIopacDevice.device_components?.cellularMode"
                          class="property-table-field"
                        >
                          <div class="info-title">{{ 'DEVICE_PROPERTIES.cellular.title' | translate }}</div>
                          <div class="property-table-field-content">
                            <dt mat-line>
                              <span id="dt-cellular-mode">{{
                                'DEVICE_PROPERTIES.cellular.cellular_mode' | translate
                              }}</span>
                            </dt>
                            <dd mat-line>
                              <span id="dd-cellular-mode">{{
                                networkService.selectedIopacDevice.device_components.cellularMode?.status
                              }}</span>
                            </dd>
                            <dt mat-line>
                              <span id="dt-cellular-carrier">{{
                                'DEVICE_PROPERTIES.cellular.cellular_carrier' | translate
                              }}</span>
                            </dt>
                            <dd mat-line>
                              <span id="dd-cellular-carrier">{{
                                networkService.selectedIopacDevice.device_components.cellularCarrier?.status
                              }}</span>
                            </dd>
                            <dt mat-line>
                              <span id="dt-cellular-signal">{{
                                'DEVICE_PROPERTIES.cellular.cellular_signal' | translate
                              }}</span>
                            </dt>
                            <dd mat-line>
                              <span id="dd-cellular-signal">{{
                                networkService.selectedIopacDevice.device_components.cellularSignal?.status
                              }}</span>
                            </dd>
                            <dt mat-line>
                              <span id="dt-cellular-cellular-ip-address">{{
                                'DEVICE_PROPERTIES.cellular.cellular_ip_address' | translate
                              }}</span>
                            </dt>
                            <dd mat-line>
                              <span id="dd-cellular-cellular-ip-address">{{
                                networkService.selectedIopacDevice.device_components.cellularIpAddress?.status
                              }}</span>
                            </dd>
                            <dt mat-line>
                              <span id="dt-cellular-imei">{{ 'DEVICE_PROPERTIES.cellular.imei' | translate }}</span>
                            </dt>
                            <dd mat-line>
                              <span id="dd-cellular-imei">{{
                                networkService.selectedIopacDevice.device_components.imei?.status
                              }}</span>
                            </dd>
                            <dt mat-line>
                              <span id="dt-cellular-imsi">{{ 'DEVICE_PROPERTIES.cellular.imsi' | translate }}</span>
                            </dt>
                            <dd mat-line>
                              <span id="dd-cellular-imsi">{{
                                networkService.selectedIopacDevice.device_components.imsi?.status
                              }}</span>
                            </dd>
                          </div>
                        </div>
                        <!-- Cellular Information END -->
                        <!-- Port Information START -->
                        <div class="info-title">{{ 'DEVICE_PROPERTIES.port.title' | translate }}</div>
                        <div class="property-table-field-content">
                          <dt mat-line>
                            <span id="dt-if-number">{{ 'DEVICE_PROPERTIES.port.if_number' | translate }}</span>
                          </dt>
                          <dd mat-line>
                            <span id="dd-if-number">{{
                              networkService.selectedIopacDevice.device_components.ifNumber?.status
                            }}</span>
                          </dd>
                          <dt *ngIf="networkService.selectedIopacDevice.device_components.numberOfPorts" mat-line>
                            <span id="dt-number-of-ports">{{
                              'DEVICE_PROPERTIES.port.number_of_ports' | translate
                            }}</span>
                          </dt>
                          <dd *ngIf="networkService.selectedIopacDevice.device_components.numberOfPorts" mat-line>
                            <span id="dd-number-of-ports">{{
                              networkService.selectedIopacDevice.device_components.numberOfPorts?.status
                            }}</span>
                          </dd>
                          <ng-template
                            *ngIf="networkService.selectedIopacDevice?.device_components?.ifOperStatus?.length > 0"
                            ngFor
                            let-ifOperStatus
                            let-i="index"
                            [ngForOf]="networkService.selectedIopacDevice?.device_components?.ifOperStatus"
                          >
                            <dt>
                              <span id="dt-if-oper-status">
                                {{ 'DEVICE_PROPERTIES.port.interface' | translate }}.{{ ifOperStatus?.index }}
                                <!-- 用 +ifOperStatus?.index - 1 來取得 poe 相關的資訊 -->
                                <mat-icon
                                  class="poe-power-icon"
                                  *ngIf="
                                    showIopacPoeInfo(ifOperStatus) &&
                                    networkService.selectedIopacDevice?.device_components?.poePortConsumption &&
                                    networkService.selectedIopacDevice?.device_components?.poePortConsumption[
                                      +ifOperStatus?.index - 1
                                    ]
                                  "
                                >
                                  flash_on</mat-icon
                                >
                              </span>
                            </dt>
                            <dd
                              *ngIf="
                                networkService.selectedIopacDevice?.device_components?.portSpeed;
                                else notShowPortSpeed
                              "
                            >
                              <span id="dd-if-oper-status"
                                >{{ ifOperStatus?.status
                                }}<span
                                  *ngIf="
                                    networkService.selectedIopacDevice?.device_components?.portName[i] &&
                                    networkService.selectedIopacDevice?.device_components?.portName[i]?.status !== ''
                                  "
                                >
                                  /
                                  {{ networkService.selectedIopacDevice?.device_components?.portName[i]?.status }}</span
                                >
                                / {{ networkService.selectedIopacDevice?.device_components?.ifSpeed[i]?.statusEx }} /
                                {{ networkService.selectedIopacDevice?.device_components?.portSpeed[i]?.status }}</span
                              >
                            </dd>
                            <ng-template #notShowPortSpeed>
                              <dd>
                                <span id="dd-if-oper-status-not-show-port-speed"
                                  >{{ ifOperStatus?.status }} /
                                  <ng-template *ngIf="networkService.selectedIopacDevice?.device_components?.portName">
                                    {{ networkService.selectedIopacDevice?.device_components?.portName[i]?.status }}
                                  </ng-template>
                                  {{ networkService.selectedIopacDevice?.device_components?.ifSpeed[i]?.statusEx }}
                                </span>
                              </dd>
                            </ng-template>
                            <dd
                              *ngIf="
                                networkService.selectedIopacDevice?.device_components?.ifType &&
                                networkService.selectedIopacDevice.device_components.ifType.length > 0
                              "
                              class="dd-margin-top"
                            >
                              <span id="dd-if-type"
                                >{{ networkService.selectedIopacDevice?.device_components?.ifType[i]?.status }} /{{
                                  networkService.selectedIopacDevice?.device_components?.ifDescr[i]?.status
                                }}</span
                              >
                            </dd>
                            <dd
                              class="dd-margin-top"
                              *ngIf="
                                networkService.selectedIopacDevice?.device_components?.poePortConsumption &&
                                networkService.selectedIopacDevice?.device_components?.poePortConsumption[
                                  +ifOperStatus?.index - 1
                                ] &&
                                showIopacPoeInfo(ifOperStatus)
                              "
                            >
                              <span id="dd-poe"
                                >{{
                                  networkService.selectedIopacDevice?.device_components?.poePortConsumption[
                                    +ifOperStatus?.index - 1
                                  ]?.status
                                }}W /
                                {{
                                  networkService.selectedIopacDevice?.device_components?.poePortVoltage[
                                    +ifOperStatus?.index - 1
                                  ]?.status
                                }}V /
                                {{
                                  networkService.selectedIopacDevice?.device_components?.poePortCurrent[
                                    +ifOperStatus?.index - 1
                                  ]?.status
                                }}mA
                              </span>
                            </dd>
                            <dd class="dd-margin-top" *ngIf="showSFPInfo(networkService.selectedIopacDevice, i)">
                              <span
                                id="dd-sfp"
                                [innerHTML]="formatSFPInfo(networkService.selectedIopacDevice, i)"
                              ></span>
                            </dd>
                            <dd
                              class="dd-margin-top"
                              *ngIf="
                                showIopacPoeInfo(ifOperStatus) &&
                                networkService.selectedIopacDevice?.device_components?.poePowerOutputMode &&
                                networkService.selectedIopacDevice?.device_components?.poePowerOutputMode[
                                  +ifOperStatus?.index - 1
                                ]
                              "
                            >
                              <span id="dd-poe-power-output-mode"
                                >{{ 'DEVICE_PROPERTIES.port.poe_power_output_mode' | translate }}
                                :
                                {{
                                  networkService.selectedIopacDevice?.device_components?.poePowerOutputMode[
                                    +ifOperStatus?.index - 1
                                  ]?.status
                                }}</span
                              >
                            </dd>
                            <dd
                              class="dd-margin-top"
                              *ngIf="
                                showIopacPoeInfo(ifOperStatus) &&
                                networkService.selectedIopacDevice?.device_components?.poePortLegacyPdDetect &&
                                networkService.selectedIopacDevice?.device_components?.poePortLegacyPdDetect[
                                  +ifOperStatus?.index - 1
                                ]
                              "
                            >
                              <span id="dd-poe-port-legacy-pd-detect"
                                >{{ 'DEVICE_PROPERTIES.port.poe_power_legacy_pd_detect' | translate }} :
                                {{
                                  networkService.selectedIopacDevice?.device_components?.poePortLegacyPdDetect[
                                    +ifOperStatus?.index - 1
                                  ]?.status
                                }}
                              </span>
                            </dd>
                            <dd
                              class="dd-margin-top"
                              *ngIf="
                                showIopacPoeInfo(ifOperStatus) &&
                                networkService.selectedIopacDevice?.device_components?.poePortClass &&
                                networkService.selectedIopacDevice?.device_components?.poePortClass[
                                  +ifOperStatus?.index - 1
                                ]
                              "
                            >
                              <span id="dd-poe-port-class"
                                >{{ 'DEVICE_PROPERTIES.port.poe_port_class' | translate }} :
                                {{
                                  networkService.selectedIopacDevice?.device_components?.poePortClass[
                                    +ifOperStatus?.index - 1
                                  ]?.status
                                }}</span
                              >
                            </dd>
                          </ng-template>
                        </div>
                        <!-- Port Information END -->
                        <!-- IPsec Status START -->
                        <ng-container *ngIf="networkService.selectedIopacDevice.device_components?.ipsecStatusName">
                          <div class="info-title">
                            {{ 'DEVICE_PROPERTIES.ipsec.title' | translate }}
                          </div>
                          <div class="property-table-field">
                            <div class="property-table-field-content">
                              <ng-container
                                *ngFor="
                                  let ipSecName of networkService.selectedIopacDevice.device_components.ipsecStatusName;
                                  let i = index
                                "
                              >
                                <ng-container *ngIf="ipSecName !== null">
                                  <ng-container
                                    *ngIf="networkService.selectedIopacDevice.device_components?.ipsecStatusName"
                                  >
                                    <dt mat-line>
                                      <span id="dt-ipsec-name"
                                        >{{ 'DEVICE_PROPERTIES.ipsec.name' | translate }}.{{ i }}</span
                                      >
                                    </dt>
                                    <dd mat-line>
                                      <span id="dt-ipsec-name">{{ ipSecName.status }}</span>
                                    </dd>
                                  </ng-container>
                                  <ng-container
                                    *ngIf="networkService.selectedIopacDevice.device_components?.ipsecStatusLocSubnet"
                                  >
                                    <dt mat-line>
                                      <span id="dt-local-subnet"
                                        >{{ 'DEVICE_PROPERTIES.ipsec.local_subnet' | translate }}.{{ i }}</span
                                      >
                                    </dt>
                                    <dd mat-line>
                                      <span id="dd-local-subnet">{{
                                        networkService.selectedIopacDevice.device_components?.ipsecStatusLocSubnet[i]
                                          ?.status
                                      }}</span>
                                    </dd>
                                  </ng-container>
                                  <ng-container
                                    *ngIf="networkService.selectedIopacDevice.device_components?.ipsecStatusLocGateway"
                                  >
                                    <dt mat-line>
                                      <span id="dt-local-gateway"
                                        >{{ 'DEVICE_PROPERTIES.ipsec.local_gateway' | translate }}.{{ i }}</span
                                      >
                                    </dt>
                                    <dd mat-line>
                                      <span id="dd-local-gateway">{{
                                        networkService.selectedIopacDevice.device_components?.ipsecStatusLocGateway[i]
                                          ?.status
                                      }}</span>
                                    </dd>
                                  </ng-container>
                                  <ng-container
                                    *ngIf="networkService.selectedIopacDevice.device_components?.ipsecStatusRemSubnet"
                                  >
                                    <dt mat-line>
                                      <span id="dt-remote-subnet"
                                        >{{ 'DEVICE_PROPERTIES.ipsec.remote_subnet' | translate }}.{{ i }}</span
                                      >
                                    </dt>
                                    <dd mat-line>
                                      <span id="dd-remote-subnet">{{
                                        networkService.selectedIopacDevice.device_components?.ipsecStatusRemSubnet[i]
                                          ?.status
                                      }}</span>
                                    </dd>
                                  </ng-container>
                                  <ng-container
                                    *ngIf="networkService.selectedIopacDevice.device_components?.ipsecStatusRemGateway"
                                  >
                                    <dt mat-line>
                                      <span id="dt-remote-gateway"
                                        >{{ 'DEVICE_PROPERTIES.ipsec.remote_gateway' | translate }}.{{ i }}</span
                                      >
                                    </dt>
                                    <dd mat-line>
                                      <span id="dd-remote-gateway">{{
                                        networkService.selectedIopacDevice.device_components?.ipsecStatusRemGateway[i]
                                          ?.status
                                      }}</span>
                                    </dd>
                                  </ng-container>
                                  <ng-container
                                    *ngIf="networkService.selectedIopacDevice.device_components?.ipsecStatusPhase1"
                                  >
                                    <dt mat-line>
                                      <span id="dt-phase-1-status">
                                        {{ 'DEVICE_PROPERTIES.ipsec.phase_1_status' | translate }}.{{ i }}</span
                                      >
                                    </dt>
                                    <dd mat-line>
                                      <span id="dd-phase-1-status">{{
                                        networkService.selectedIopacDevice.device_components?.ipsecStatusPhase1[i]
                                          ?.status
                                      }}</span>
                                    </dd>
                                  </ng-container>
                                  <ng-container
                                    *ngIf="networkService.selectedIopacDevice.device_components.ipsecStatusPhase2"
                                  >
                                    <dt mat-line>
                                      <span id="dt-phase-2-status"
                                        >{{ 'DEVICE_PROPERTIES.ipsec.phase_2_status' | translate }}.{{ i }}</span
                                      >
                                    </dt>
                                    <dd mat-line>
                                      <span id="dd-phase-2-status">{{
                                        networkService.selectedIopacDevice.device_components?.ipsecStatusPhase2[i]
                                          ?.status
                                      }}</span>
                                    </dd>
                                  </ng-container>
                                  <ng-container *ngIf="networkService.selectedIopacDevice.device_components?.ipsecl2tp">
                                    <dt mat-line>
                                      <span id="dt-l2tp-status"
                                        >{{ 'DEVICE_PROPERTIES.ipsec.l2tp_status' | translate }}.{{ i }}</span
                                      >
                                    </dt>
                                    <dd mat-line>
                                      <span id="dd-l2tp-status">{{
                                        networkService.selectedIopacDevice.device_components?.ipsecl2tp[i]?.status
                                      }}</span>
                                    </dd>
                                  </ng-container>
                                </ng-container>
                              </ng-container>
                            </div>
                          </div>
                        </ng-container>
                        <!-- IPsec Status End -->
                        <!-- Management Interfaces START -->
                        <div class="property-table-field">
                          <div
                            class="info-title"
                            *ngIf="
                              hasOneOfProperties(networkService.selectedIopacDevice, [
                                'httpEnable.0',
                                'httpsEnable.0',
                                'telnetEnable.0',
                                'sshEnable.0',
                                'profinetEnable.0'
                              ])
                            "
                          >
                            {{ 'DEVICE_PROPERTIES.management_interfaces.title' | translate }}
                          </div>
                          <div class="property-table-field-content">
                            <dt *ngIf="networkService.selectedIopacDevice.device_components['httpEnable.0']" mat-line>
                              <span id="dt-http-port">{{
                                'DEVICE_PROPERTIES.management_interfaces.http_port' | translate
                              }}</span>
                            </dt>
                            <dd *ngIf="networkService.selectedIopacDevice.device_components['httpEnable.0']" mat-line>
                              <span id="dd-http-port"
                                >{{ networkService.selectedIopacDevice.device_components['httpEnable.0']?.status }} /
                                {{ networkService.selectedIopacDevice.device_components['httpPort.0']?.status }}</span
                              >
                            </dd>
                            <dt *ngIf="networkService.selectedIopacDevice.device_components['httpsEnable.0']" mat-line>
                              <span id="dt-https-port">{{
                                'DEVICE_PROPERTIES.management_interfaces.https_port' | translate
                              }}</span>
                            </dt>
                            <dd *ngIf="networkService.selectedIopacDevice.device_components['httpsEnable.0']" mat-line>
                              <span id="dd-https-port"
                                >{{ networkService.selectedIopacDevice.device_components['httpsEnable.0']?.status }} /
                                {{ networkService.selectedIopacDevice.device_components['httpsPort.0']?.status }}</span
                              >
                            </dd>
                            <dt *ngIf="networkService.selectedIopacDevice.device_components['telnetEnable.0']" mat-line>
                              <span id="dt-telnet-port">{{
                                'DEVICE_PROPERTIES.management_interfaces.telnet_port' | translate
                              }}</span>
                            </dt>
                            <dd *ngIf="networkService.selectedIopacDevice.device_components['telnetEnable.0']" mat-line>
                              <span id="dd-telnet-port"
                                >{{ networkService.selectedIopacDevice.device_components['telnetEnable.0']?.status }} /
                                {{ networkService.selectedIopacDevice.device_components['telnetPort.0']?.status }}</span
                              >
                            </dd>
                            <dt *ngIf="networkService.selectedIopacDevice.device_components['sshEnable.0']" mat-line>
                              <span id="dt-ssh-enable">{{
                                'DEVICE_PROPERTIES.management_interfaces.ssh_port' | translate
                              }}</span>
                            </dt>
                            <dd *ngIf="networkService.selectedIopacDevice.device_components['sshEnable.0']" mat-line>
                              <span id="dd-ssh-enable"
                                >{{ networkService.selectedIopacDevice.device_components['sshEnable.0']?.status }} /
                                {{ networkService.selectedIopacDevice.device_components['sshPort.0']?.status }}</span
                              >
                            </dd>
                            <dt
                              *ngIf="networkService.selectedIopacDevice.device_components['profinetEnable.0']"
                              mat-line
                            >
                              <span id="dt-profinet-enable">{{
                                'DEVICE_PROPERTIES.management_interfaces.profinet_enabled' | translate
                              }}</span>
                            </dt>
                            <dd
                              *ngIf="networkService.selectedIopacDevice.device_components['profinetEnable.0']"
                              mat-line
                            >
                              <span id="dd-profinet-enable">{{
                                networkService.selectedIopacDevice.device_components['profinetEnable.0']?.status
                              }}</span>
                            </dd>
                          </div>
                        </div>
                        <!-- Management Interfaces END -->
                        <!-- SNMP Information START -->
                        <div class="property-table-field">
                          <div
                            class="info-title"
                            *ngIf="
                              hasOneOfProperties(networkService.selectedIopacDevice, [
                                'snmpReadCommunity',
                                'snmpTrapCommunity',
                                'trapServerAddr',
                                'snmpTrap2Community',
                                'trap2ServerAddr',
                                'snmpInformEnable',
                                'snmpInformRetries',
                                'snmpInformTimeout',
                                'usnmpTrapCommunity',
                                'utrapServerAddr',
                                'usnmpTrap2Community',
                                'utrap2ServerAddr'
                              ])
                            "
                          >
                            {{ 'DEVICE_PROPERTIES.snmp.title' | translate }}
                          </div>
                          <div class="property-table-field-content">
                            <dt *ngIf="networkService.selectedIopacDevice.device_components.snmpReadCommunity" mat-line>
                              <span id="dt-read-community">{{
                                'DEVICE_PROPERTIES.snmp.read_community' | translate
                              }}</span>
                            </dt>
                            <dd *ngIf="networkService.selectedIopacDevice.device_components.snmpReadCommunity" mat-line>
                              <span id="dd-read-community">{{
                                networkService.selectedIopacDevice.device_components.snmpReadCommunity?.status
                              }}</span>
                            </dd>
                            <dt *ngIf="networkService.selectedIopacDevice.device_components.snmpTrapCommunity" mat-line>
                              <span id="dt-snmp-trap-community-1">{{
                                'DEVICE_PROPERTIES.snmp.1st_trap_community' | translate
                              }}</span>
                            </dt>
                            <dd *ngIf="networkService.selectedIopacDevice.device_components.snmpTrapCommunity" mat-line>
                              <span id="dd-snmp-trap-community-1">{{
                                networkService.selectedIopacDevice.device_components.snmpTrapCommunity?.status ||
                                  networkService.selectedIopacDevice.device_components.snmpTrapCommunity[0]?.status
                              }}</span>
                            </dd>
                            <dt *ngIf="networkService.selectedIopacDevice.device_components.trapServerAddr" mat-line>
                              <span id="dt-trap-server-addr-1">{{
                                'DEVICE_PROPERTIES.snmp.trap_server_address_1' | translate
                              }}</span>
                            </dt>
                            <dd *ngIf="networkService.selectedIopacDevice.device_components.trapServerAddr" mat-line>
                              <span id="dd-trap-server-addr-1">{{
                                networkService.selectedIopacDevice.device_components.trapServerAddr?.status ||
                                  networkService.selectedIopacDevice.device_components.trapServerAddr[0]?.status
                              }}</span>
                            </dd>
                            <dt
                              *ngIf="networkService.selectedIopacDevice.device_components.snmpTrap2Community"
                              mat-line
                            >
                              <span id="dt-trap-server-community-2">{{
                                'DEVICE_PROPERTIES.snmp.2nd_trap_server_community' | translate
                              }}</span>
                            </dt>
                            <dd
                              *ngIf="networkService.selectedIopacDevice.device_components.snmpTrap2Community"
                              mat-line
                            >
                              <span id="dd-trap-server-community-2">{{
                                networkService.selectedIopacDevice.device_components.snmpTrap2Community?.status ||
                                  networkService.selectedIopacDevice.device_components.snmpTrap2Community[0]?.status
                              }}</span>
                            </dd>
                            <dt *ngIf="networkService.selectedIopacDevice.device_components.trap2ServerAddr" mat-line>
                              <span id="dt-trap-server-addr-2">{{
                                'DEVICE_PROPERTIES.snmp.trap_server_address_2' | translate
                              }}</span>
                            </dt>
                            <dd *ngIf="networkService.selectedIopacDevice.device_components.trap2ServerAddr" mat-line>
                              <span id="dd-trap-server-addr-2">{{
                                networkService.selectedIopacDevice.device_components.trap2ServerAddr?.status ||
                                  networkService.selectedIopacDevice.device_components.trap2ServerAddr[0]?.status
                              }}</span>
                            </dd>
                            <dt
                              *ngIf="networkService.selectedIopacDevice.device_components.usnmpTrapCommunity"
                              mat-line
                            >
                              <span id="dt-snmp-trap-community-1">{{
                                'DEVICE_PROPERTIES.snmp.1st_trap_community' | translate
                              }}</span>
                            </dt>
                            <dd
                              *ngIf="networkService.selectedIopacDevice.device_components.usnmpTrapCommunity"
                              mat-line
                            >
                              <span id="dd-snmp-trap-community-1">{{
                                networkService.selectedIopacDevice.device_components.usnmpTrapCommunity?.status
                              }}</span>
                            </dd>
                            <dt *ngIf="networkService.selectedIopacDevice.device_components.utrapServerAddr" mat-line>
                              <span id="dt-trap-server-addr-1">{{
                                'DEVICE_PROPERTIES.snmp.trap_server_address_1' | translate
                              }}</span>
                            </dt>
                            <dd *ngIf="networkService.selectedIopacDevice.device_components.utrapServerAddr" mat-line>
                              <span id="dd-trap-server-addr-1">{{
                                networkService.selectedIopacDevice.device_components.utrapServerAddr?.status
                              }}</span>
                            </dd>
                            <dt
                              *ngIf="networkService.selectedIopacDevice.device_components.usnmpTrap2Community"
                              mat-line
                            >
                              <span id="dt-trap-server-community-2">{{
                                'DEVICE_PROPERTIES.snmp.2nd_trap_server_community' | translate
                              }}</span>
                            </dt>
                            <dd
                              *ngIf="networkService.selectedIopacDevice.device_components.usnmpTrap2Community"
                              mat-line
                            >
                              <span id="dd-trap-server-community-2">{{
                                networkService.selectedIopacDevice.device_components.usnmpTrap2Community?.status
                              }}</span>
                            </dd>
                            <dt *ngIf="networkService.selectedIopacDevice.device_components.utrap2ServerAddr" mat-line>
                              <span id="dt-trap-server-addr-2">{{
                                'DEVICE_PROPERTIES.snmp.trap_server_address_2' | translate
                              }}</span>
                            </dt>
                            <dd *ngIf="networkService.selectedIopacDevice.device_components.utrap2ServerAddr" mat-line>
                              <span id="dd-trap-server-addr-2">{{
                                networkService.selectedIopacDevice.device_components.utrap2ServerAddr?.status
                              }}</span>
                            </dd>
                            <dt *ngIf="networkService.selectedIopacDevice.device_components.snmpInformEnable" mat-line>
                              <span id="dt-inform-enabled">{{
                                'DEVICE_PROPERTIES.snmp.inform_enabled' | translate
                              }}</span>
                            </dt>
                            <dd *ngIf="networkService.selectedIopacDevice.device_components.snmpInformEnable" mat-line>
                              <span id="dd-inform-enabled">{{
                                networkService.selectedIopacDevice.device_components.snmpInformEnable?.status
                              }}</span>
                            </dd>
                            <dt *ngIf="networkService.selectedIopacDevice.device_components.snmpInformRetries" mat-line>
                              <span id="dt-inform-retries">{{
                                'DEVICE_PROPERTIES.snmp.inform_retries' | translate
                              }}</span>
                            </dt>
                            <dd *ngIf="networkService.selectedIopacDevice.device_components.snmpInformRetries" mat-line>
                              <span id="dd-inform-retries">{{
                                networkService.selectedIopacDevice.device_components.snmpInformRetries?.status
                              }}</span>
                            </dd>
                            <dt *ngIf="networkService.selectedIopacDevice.device_components.snmpInformTimeout" mat-line>
                              <span id="dt-inform-timeout">{{
                                'DEVICE_PROPERTIES.snmp.inform_timeout' | translate
                              }}</span>
                            </dt>
                            <dd *ngIf="networkService.selectedIopacDevice.device_components.snmpInformTimeout" mat-line>
                              <span id="dd-inform-timeout">{{
                                networkService.selectedIopacDevice.device_components.snmpInformTimeout?.status
                              }}</span>
                            </dd>
                          </div>
                        </div>
                        <!-- SNMP Information END -->
                        <!-- Other Device Properties START -->
                        <div *ngIf="isShowOtherDeviceProperties">
                          <div id="div-device-properties" class="info-title">
                            {{ 'DEVICE_PROPERTIES.other_device_properties.title' | translate }}
                          </div>
                          <div class="property-table-field-content">
                            <dt *ngIf="networkService.selectedIopacDevice.device_components.macAddress" mat-line>
                              <span id="dt-mac-address">{{
                                'DEVICE_PROPERTIES.other_device_properties.mac_address' | translate
                              }}</span>
                            </dt>
                            <dd *ngIf="networkService.selectedIopacDevice.device_components.macAddress" mat-line>
                              <span id="dd-mac-address">{{
                                networkService.selectedIopacDevice.device_components.macAddress?.status
                              }}</span>
                            </dd>
                            <ng-template
                              *ngIf="networkService.selectedIopacDevice?.device_components?.ipAdEntAddr?.length > 0"
                              ngFor
                              let-ipAdEntAddr
                              let-i="index"
                              [ngForOf]="networkService.selectedIopacDevice?.device_components?.ipAdEntAddr"
                            >
                              <dt>
                                <span id="dt-ip-ad-ent-addr"
                                  >{{ 'DEVICE_PROPERTIES.other_device_properties.ip_ad_ent_addr' | translate }}.{{
                                    ipAdEntAddr.status
                                  }}</span
                                >
                              </dt>
                              <dd>
                                <span id="dd-ip-ad-ent-addr">{{ ipAdEntAddr?.status }}</span>
                              </dd>
                            </ng-template>
                            <dt *ngIf="networkService.selectedIopacDevice.device_components.modelName" mat-line>
                              <span id="dt-model-name">{{
                                'DEVICE_PROPERTIES.other_device_properties.model_name' | translate
                              }}</span>
                            </dt>
                            <dd *ngIf="networkService.selectedIopacDevice.device_components.modelName" mat-line>
                              <span id="dd-model-name">{{
                                networkService.selectedIopacDevice.device_components.modelName?.status
                              }}</span>
                            </dd>
                            <dt
                              *ngIf="networkService.selectedIopacDevice.device_components.enableAutoIpConfig"
                              mat-line
                            >
                              <span id="dt-auto-ip-config">{{
                                'DEVICE_PROPERTIES.other_device_properties.auto_ip_config' | translate
                              }}</span>
                            </dt>
                            <dd
                              *ngIf="networkService.selectedIopacDevice.device_components.enableAutoIpConfig"
                              mat-line
                            >
                              <span id="dd-auto-ip-config">{{
                                networkService.selectedIopacDevice.device_components.enableAutoIpConfig?.status
                              }}</span>
                            </dd>
                            <dt *ngIf="networkService.selectedIopacDevice.device_components.IpAddr" mat-line>
                              <span id="dt-ip-address">{{
                                'DEVICE_PROPERTIES.other_device_properties.ip_address' | translate
                              }}</span>
                            </dt>
                            <dd *ngIf="networkService.selectedIopacDevice.device_components.IpAddr" mat-line>
                              <span id="dd-ip-address"
                                >{{ networkService.selectedIopacDevice.device_components.IpAddr?.status }}
                              </span>
                            </dd>
                            <dt *ngIf="networkService.selectedIopacDevice.device_components.IpMask" mat-line>
                              <span id="dt-net-mask">{{
                                'DEVICE_PROPERTIES.other_device_properties.netmask' | translate
                              }}</span>
                            </dt>
                            <dd *ngIf="networkService.selectedIopacDevice.device_components.IpMask" mat-line>
                              <span id="dd-net-mask"
                                >{{ networkService.selectedIopacDevice.device_components.IpMask?.status }}
                              </span>
                            </dd>
                            <dt *ngIf="networkService.selectedIopacDevice.device_components.defaultGateway" mat-line>
                              <span id="dt-default-gateway">{{
                                'DEVICE_PROPERTIES.other_device_properties.default_gateway' | translate
                              }}</span>
                            </dt>
                            <dd *ngIf="networkService.selectedIopacDevice.device_components.defaultGateway" mat-line>
                              <span id="dd-default-gateway">{{
                                networkService.selectedIopacDevice.device_components.defaultGateway?.status
                              }}</span>
                            </dd>
                            <dt *ngIf="networkService.selectedIopacDevice.device_components.dnsServer1IpAddr" mat-line>
                              <span id="dt-dns-1-ip-address">{{
                                'DEVICE_PROPERTIES.other_device_properties.dns_1_ip_address' | translate
                              }}</span>
                            </dt>
                            <dd *ngIf="networkService.selectedIopacDevice.device_components.dnsServer1IpAddr" mat-line>
                              <span id="dd-dns-1-ip-address">{{
                                networkService.selectedIopacDevice.device_components.dnsServer1IpAddr?.status
                              }}</span>
                            </dd>
                            <dt *ngIf="networkService.selectedIopacDevice.device_components.dnsServer2IpAddr" mat-line>
                              <span id="dt-dns-2-ip-address">{{
                                'DEVICE_PROPERTIES.other_device_properties.dns_2_ip_address' | translate
                              }}</span>
                            </dt>
                            <dd *ngIf="networkService.selectedIopacDevice.device_components.dnsServer2IpAddr" mat-line>
                              <span id="dd-dns-1-ip-address">{{
                                networkService.selectedIopacDevice.device_components.dnsServer2IpAddr?.status
                              }}</span>
                            </dd>
                            <!-- this loop is used for Adding all other properties not hardcoded in this page -->
                            <ng-container
                              *ngFor="
                                let resource of displayedDeviceComponentsToArray(networkService.selectedIopacDevice)
                              "
                            >
                              <ng-container
                                *ngIf="
                                  networkService.selectedIopacDevice.device_components[resource] &&
                                  !propertyIsArray(device, resource)
                                "
                              >
                                <dt mat-line>
                                  <span id="dt-resource">{{ resource }}</span>
                                </dt>
                                <dd mat-line>
                                  <span id="dd-resource">{{
                                    networkService.selectedIopacDevice.device_components[resource].status
                                  }}</span>
                                </dd>
                              </ng-container>
                              <ng-container
                                *ngIf="
                                  networkService.selectedIopacDevice.device_components[resource] &&
                                  propertyIsArray(device, resource)
                                "
                              >
                                <ng-container
                                  *ngFor="let row of networkService.selectedIopacDevice.device_components[resource]"
                                >
                                  <dt id="dt-row-status">
                                    <span>{{ resource }}.{{ row.index }}</span>
                                  </dt>
                                  <dd>
                                    <span id="dd-row-status">{{ row.status }}</span>
                                  </dd>
                                </ng-container>
                              </ng-container>
                            </ng-container>
                            <dt
                              *ngIf="networkService.selectedIopacDevice.device_components.monitorCurrentMode"
                              mat-line
                            >
                              <span id="dt-monitor-current-mode">
                                {{ 'DEVICE_PROPERTIES.other_device_properties.monitor_current_mode' | translate }}
                              </span>
                            </dt>
                            <dd
                              *ngIf="networkService.selectedIopacDevice.device_components.monitorCurrentMode"
                              mat-line
                            >
                              <span id="dd-monitor-current-mode">{{
                                networkService.selectedIopacDevice.device_components['monitorCurrentMode'][0].status
                              }}</span>
                            </dd>
                            <dt
                              *ngIf="networkService.selectedIopacDevice.device_components.monitorDownStreamRate"
                              mat-line
                            >
                              <span id="dt-monitor-down-stream-rate">
                                {{ 'DEVICE_PROPERTIES.other_device_properties.monitor_down_stream_rate' | translate }}
                              </span>
                            </dt>
                            <dd
                              *ngIf="networkService.selectedIopacDevice.device_components.monitorDownStreamRate"
                              mat-line
                            >
                              <span id="dd-monitor-down-stream-rate">{{
                                networkService.selectedIopacDevice.device_components['monitorDownStreamRate'][0].status
                              }}</span>
                            </dd>
                            <dt *ngIf="networkService.selectedIopacDevice.device_components.monitorSNR" mat-line>
                              <span id="dt-monitor-snr">
                                {{ 'DEVICE_PROPERTIES.other_device_properties.monitor_snr' | translate }}
                              </span>
                            </dt>
                            <dd *ngIf="networkService.selectedIopacDevice.device_components.monitorSNR" mat-line>
                              <span id="dd-monitor-snr">{{
                                networkService.selectedIopacDevice.device_components['monitorSNR'][0].status
                              }}</span>
                            </dd>
                            <dt
                              *ngIf="networkService.selectedIopacDevice.device_components.monitorUpStreamRate"
                              mat-line
                            >
                              <span id="dt-monitor-up-stream-rate">
                                {{ 'DEVICE_PROPERTIES.other_device_properties.monitor_up_stream_rate' | translate }}
                              </span>
                            </dt>
                            <dd
                              *ngIf="networkService.selectedIopacDevice.device_components.monitorUpStreamRate"
                              mat-line
                            >
                              <span id="dd-monitor-up-stream-rate">{{
                                networkService.selectedIopacDevice.device_components['monitorUpStreamRate'][0].status
                              }}</span>
                            </dd>
                          </div>
                        </div>
                        <!-- Other Device Properties END -->
                      </ng-template>
                    </div>
                  </ng-container>
                </mat-list-item>
              </div>
              <div id="div-link-properties" *ngIf="link">
                <mat-list-item>
                  <div class="property-table-field">
                    <div id="div-link-properties-information" class="mat-line info-title">
                      {{ 'DEVICE_PROPERTIES.link.title' | translate }}
                    </div>
                    <div class="property-table-field-content">
                      <dt mat-line>
                        <span id="dt-link-properties-from">{{ 'DEVICE_PROPERTIES.link.from' | translate }}</span>
                      </dt>
                      <dd mat-line>
                        <span id="dd-link-properties-from">{{ fromIp }}</span>
                      </dd>
                      <dt *ngIf="isVPNLink" mat-line>
                        <span id="dt-vpn-link-properties-from-ip">{{
                          'DEVICE_PROPERTIES.vpn.from_ip' | translate
                        }}</span>
                      </dt>
                      <dd *ngIf="isVPNLink" mat-line>
                        <span id="dd-vpn-link-properties-from-ip">{{ link.vpn_from_ip }}</span>
                      </dd>
                      <dt *ngIf="isVPNLink" mat-line>
                        <span id="dt-vpn-link-properties-local-connection-name">{{
                          'DEVICE_PROPERTIES.vpn.local_connection_name' | translate
                        }}</span>
                      </dt>
                      <dd *ngIf="isVPNLink" mat-line>
                        <span id="dd-vpn-link-properties-local-connection-name">{{
                          link.local_vpn_connection_name
                        }}</span>
                      </dd>
                      <dd *ngIf="!isVPNLink && showFromPort" mat-line>
                        <span id="dd-link-properties-from-port"
                          >{{ 'DEVICE_PROPERTIES.link.port' | translate }} {{ link.from_port }}</span
                        >
                      </dd>
                      <dt mat-line>
                        <span id="dt-link-properties-to">{{ 'DEVICE_PROPERTIES.link.to' | translate }}</span>
                      </dt>
                      <dd mat-line>
                        <span id="dd-link-properties-to">{{ toIp }}</span>
                      </dd>
                      <dt *ngIf="isVPNLink" mat-line>
                        <span id="dt-vpn-link-properties-to-ip">{{ 'DEVICE_PROPERTIES.vpn.to_ip' | translate }}</span>
                      </dt>
                      <dd *ngIf="isVPNLink" mat-line>
                        <span id="dd-vpn-link-properties-to-ip">{{ link.vpn_to_ip }}</span>
                      </dd>
                      <dt *ngIf="isVPNLink" mat-line>
                        <span id="dt-vpn-link-properties-remote-connection-name">{{
                          'DEVICE_PROPERTIES.vpn.remote_connection_name' | translate
                        }}</span>
                      </dt>
                      <dd *ngIf="isVPNLink" mat-line>
                        <span id="dd-vpn-link-properties-remote-connection-name">{{
                          link.remote_vpn_connection_name
                        }}</span>
                      </dd>
                      <dd *ngIf="!isVPNLink && showToPort" mat-line>
                        <span id="dd-link-properties-to-port"
                          >{{ 'DEVICE_PROPERTIES.link.port' | translate }} {{ link.to_port }}</span
                        >
                      </dd>
                      <dt *ngIf="!isVPNLink && showLinkSpeed" mat-line>
                        <span id="dt-link-properties-link-speed">{{ 'DEVICE_PROPERTIES.link.speed' | translate }}</span>
                      </dt>
                      <dd *ngIf="!isVPNLink && showLinkSpeed" mat-line>
                        <span id="dd-link-properties-link-speed">{{ linkSpeed }}</span>
                      </dd>
                    </div>
                    <div id="div-link-properties-sfp-title" *ngIf="sfpLink" class="mat-line info-title">
                      {{ 'DEVICE_PROPERTIES.link.sfpTitle' | translate }}
                    </div>
                    <div *ngIf="sfpLink" class="property-table-field-content">
                      <dt mat-line>
                        <span id="dt-link-properties-sfp-from">{{ 'DEVICE_PROPERTIES.link.from' | translate }}</span>
                      </dt>
                      <dd mat-line>
                        <span id="dd-link-properties-sfp-from">{{ fromIp }}</span>
                      </dd>
                      <dt mat-line>
                        <span id="dt-link-properties-sfp-from-tx">{{ 'sfpList.tx' | translate }}</span>
                      </dt>
                      <dd mat-line>
                        <span
                          id="dd-link-properties-sfp-from-tx"
                          [ngClass]="globalService.getSFPTextColor(globalService.sfpValueTxType, +sfpLink.fromTx)"
                          >{{ sfpLink.fromTx }}</span
                        >
                      </dd>
                      <dt mat-line>
                        <span id="dt-link-properties-sfp-from-rx">{{ 'sfpList.rx' | translate }}</span>
                      </dt>
                      <dd mat-line>
                        <span
                          id="dd-link-properties-sfp-from-rx"
                          [ngClass]="globalService.getSFPTextColor(globalService.sfpValueRxType, +sfpLink.fromRx)"
                          >{{ sfpLink.fromRx }}</span
                        >
                      </dd>
                      <dt mat-line>
                        <span id="dt-link-properties-sfp-from-temperature">{{
                          'sfpList.temperature' | translate
                        }}</span>
                      </dt>
                      <dd mat-line>
                        <span
                          id="dd-link-properties-sfp-from-temperature"
                          [ngClass]="
                            globalService.getSFPTextColor(
                              globalService.sfpValueTemperatureType,
                              +sfpLink.fromTemperature
                            )
                          "
                          >{{ sfpLink.fromTemperature }}</span
                        >
                      </dd>
                      <dt mat-line>
                        <span id="dt-link-properties-sfp-from-voltage">{{ 'sfpList.voltage' | translate }}</span>
                      </dt>
                      <dd mat-line>
                        <span
                          id="dd-link-properties-sfp-from-voltage"
                          [ngClass]="
                            globalService.getSFPTextColor(globalService.sfpValueVoltageType, +sfpLink.fromVoltage)
                          "
                          >{{ sfpLink.fromVoltage }}</span
                        >
                      </dd>
                      <dt mat-line>
                        <span id="dt-link-properties-sfp-to">{{ 'DEVICE_PROPERTIES.link.to' | translate }}</span>
                      </dt>
                      <dd mat-line>
                        <span id="dd-link-properties-sfp-to">{{ toIp }}</span>
                      </dd>
                      <dt mat-line>
                        <span id="dt-link-properties-sfp-to-tx">{{ 'sfpList.tx' | translate }}</span>
                      </dt>
                      <dd mat-line>
                        <span
                          id="dd-link-properties-sfp-to-tx"
                          [ngClass]="globalService.getSFPTextColor(globalService.sfpValueTxType, +sfpLink.toTx)"
                          >{{ sfpLink.toTx }}</span
                        >
                      </dd>
                      <dt mat-line>
                        <span id="dt-link-properties-sfp-to-rx">{{ 'sfpList.rx' | translate }}</span>
                      </dt>
                      <dd mat-line>
                        <span
                          id="dd-link-properties-sfp-to-rx"
                          [ngClass]="globalService.getSFPTextColor(globalService.sfpValueRxType, +sfpLink.toRx)"
                          >{{ sfpLink.toRx }}</span
                        >
                      </dd>
                      <dt mat-line>
                        <span id="dt-link-properties-sfp-to-tempature">{{ 'sfpList.temperature' | translate }}</span>
                      </dt>
                      <dd mat-line>
                        <span
                          id="dd-link-properties-sfp-to-tempature"
                          [ngClass]="
                            globalService.getSFPTextColor(globalService.sfpValueTemperatureType, +sfpLink.toTemperature)
                          "
                          >{{ sfpLink.toTemperature }}</span
                        >
                      </dd>
                      <dt mat-line>
                        <span id="dt-link-properties-sfp-to-voltage">{{ 'sfpList.voltage' | translate }}</span>
                      </dt>
                      <dd mat-line>
                        <span
                          id="dd-link-properties-sfp-to-voltage"
                          [ngClass]="
                            globalService.getSFPTextColor(globalService.sfpValueVoltageType, +sfpLink.toVoltage)
                          "
                          >{{ sfpLink.toVoltage }}</span
                        >
                      </dd>
                    </div>
                  </div>
                </mat-list-item>
              </div>
            </mat-tab>
            <mat-tab *ngIf="group?.device_group || device" label="{{ 'NETWORK.current_status.title' | translate }}">
              <div *ngIf="group?.device_group" class="property-table-field basic-property-device-component">
                <mat-form-field style="width: 100%">
                  <mat-select
                    placeholder="{{ 'DEVICE_PROPERTIES.selected_module' | translate }}"
                    (selectionChange)="onSelectionChange()"
                    [(value)]="selectedIopacModule"
                    (blur)="onBlur()"
                  >
                    <mat-option [value]="0">None</mat-option>
                    <mat-option *ngFor="let iopacDevice of iopacDevices" [value]="iopacDevice.id">
                      {{ iopacDevice.name }}: {{ iopacDevice.ip }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
              <div
                *ngIf="group?.device_group && !networkService.selectedIopacDevice; else deviceInfo"
                class="not-selected"
              >
                <div>
                  <mat-icon class="not-selected-icon">announcement</mat-icon>
                </div>
                <div class="not-selected-text">{{ 'DEVICE_PROPERTIES.not_selected' | translate }}</div>
              </div>
              <div class="property-table">
                <div *ngFor="let event of currentEvents">
                  <div
                    id="dd-current-status-{{ event?.description }}"
                    fxLayout="column"
                    fxLayoutAlign="start stretch"
                    [ngClass]="{
                      'realtime-event': true,
                      'warning-severity': event.severity === 2 ? true : false,
                      'info-severity': event.severity === 1 ? true : false,
                      'critical-severity': event.severity === 3 ? true : false
                    }"
                  >
                    <div>
                      <dd mat-line class="realtime-event-name">
                        <span id="dd-event-description">{{ event?.description }}</span>
                      </dd>
                      <button
                        *ngIf="event.isV3TrapParseErrorEvent"
                        class="realtime-event-icon"
                        mat-icon-button
                        id="bt-ack-single"
                        (click)="clearV3TrapEvent($event)"
                        fxHide.xs
                      >
                        <mat-icon>markunread</mat-icon>
                      </button>
                    </div>
                    <dt *ngIf="event.isV3TrapParseErrorEvent" mat-line class="realtime-event-instruction">
                      <span id="dt-v3-trap-event-suggestion">
                        {{ 'NETWORK.current_status.v3_trap_event_suggestion' | translate }}</span
                      >
                    </dt>
                    <dt mat-line class="realtime-event-time">
                      <span id="dd-v3-trap-event-suggestion">{{ event.timestamp }}</span>
                    </dt>
                  </div>
                </div>
                <div
                  *ngIf="
                    (currentEvents === undefined || currentEvents.length === 0) &&
                    (networkService.selectedIopacDevice || !group?.device_group)
                  "
                >
                  <div class="realtime-event">{{ 'NETWORK.current_status.no_event' | translate }}</div>
                </div>
              </div>
            </mat-tab>
            <mat-tab *ngIf="topologyViewMode === 2" label="{{ 'PREFERENCES.security_view.title' | translate }}">
              <div *ngIf="group?.device_group" class="property-table-field basic-property-device-component">
                <mat-form-field style="width: 100%">
                  <mat-select
                    placeholder="{{ 'DEVICE_PROPERTIES.selected_module' | translate }}"
                    (selectionChange)="onSelectionChange()"
                    [(value)]="selectedIopacModule"
                    (blur)="onBlur()"
                  >
                    <mat-option [value]="0">None</mat-option>
                    <mat-option *ngFor="let iopacDevice of iopacDevices" [value]="iopacDevice.id">
                      {{ iopacDevice.name }}: {{ iopacDevice.ip }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
              <div
                *ngIf="group?.device_group && !networkService.selectedIopacDevice; else securityInfo"
                class="not-selected"
              >
                <div>
                  <mat-icon class="not-selected-icon">announcement</mat-icon>
                </div>
                <div class="not-selected-text">{{ 'DEVICE_PROPERTIES.not_selected' | translate }}</div>
              </div>
              <ng-template #securityInfo>
                <div>
                  <div fxLayout="row" fxLayoutAlign="start center" class="mat-line info-title">
                    <div>
                      {{ 'PREFERENCES.security_view.device_security_level' | translate }}{{ deviceSecurityLevel }}
                    </div>
                  </div>
                  <div *ngIf="isShowAwkAlert()" fxLayout="column" fxLayoutGap="10px" class="security-view-awk-alert">
                    <div>
                      {{ 'PREFERENCES.security_view.awk_device_credentials_hint' | translate }}
                    </div>
                    <div class="awk-set-button">
                      <button mat-button (click)="onButtonClick('openAdvanceSetting')">
                        <mat-icon>settings </mat-icon>
                        <span class="set-now-button-text">{{ 'DIALOG.background_dialog.set_now' | translate }}</span>
                      </button>
                    </div>
                  </div>
                  <ng-container *ngIf="securityViewProfile === 1">
                    <div class="security-indicator-title" fxLayout="row" fxLayoutAlign="start center">
                      <span [style.background-color]="securityViewBasicColor" class="devices-status-circle"></span>
                      <div>{{ 'PREFERENCES.security_view.basic' | translate }}</div>
                    </div>
                    <div class="security-indicator-title" fxLayout="row" fxLayoutAlign="start center">
                      <span [style.background-color]="securityViewMediumColor" class="devices-status-circle"></span>
                      <div>{{ 'PREFERENCES.security_view.medium' | translate }}</div>
                    </div>
                    <div
                      class="security-indicator-title security-indicator-title-margin"
                      fxLayout="row"
                      fxLayoutAlign="start center"
                    >
                      <span [style.background-color]="securityViewHighColor" class="devices-status-circle"></span>
                      <div>{{ 'PREFERENCES.security_view.high' | translate }}</div>
                    </div>
                  </ng-container>
                  <ng-container *ngFor="let securityItem of deviceSecurityItems">
                    <div
                      class="security-items"
                      fxLayout="row"
                      fxLayoutAlign="start center"
                      matTooltip="{{ 'PREFERENCES.security_view.current_setting' | translate }} {{
                        securityItem.result
                      }}"
                      matTooltipPosition="before"
                    >
                      <div fxLayout="row" fxLayoutAlign="start center">
                        <div
                          *ngIf="securityViewProfile === 1"
                          class="security-indicator"
                          fxLayout="row"
                          fxLayoutAlign="end center"
                        >
                          <span
                            *ngIf="securityItem.baseline"
                            [style.background-color]="securityViewBasicColor"
                            class="devices-status-circle"
                          ></span>
                          <span
                            *ngIf="securityItem.l1"
                            [style.background-color]="securityViewMediumColor"
                            class="devices-status-circle"
                          ></span>
                          <span
                            *ngIf="securityItem.l2"
                            [style.background-color]="securityViewHighColor"
                            class="devices-status-circle"
                          ></span>
                        </div>
                        <dd
                          [ngClass]="{
                            'security-item': securityViewProfile === 0,
                            'security-item-with-indicator': securityViewProfile === 1
                          }"
                          class="security-item"
                        >
                          <span id="dd-security-item">{{ securityItem.item }}</span>
                        </dd>
                        <div>
                          <mat-icon class="security-enabled" *ngIf="securityItem.status === 1">verified_user</mat-icon>
                          <mat-icon class="security-unknown" *ngIf="securityItem.status === -1">help</mat-icon>
                          <mat-icon class="security-unknown" *ngIf="securityItem.status === 0">help</mat-icon>
                          <mat-icon class="security-disabled" *ngIf="securityItem.status === 2">error</mat-icon>
                        </div>
                      </div>
                    </div>
                  </ng-container>
                </div>
              </ng-template>
            </mat-tab>
          </mat-tab-group>
        </div>
      </mat-sidenav>
      <div class="main" appGrabResizable [isFullMode]="isFullNetworkMode" (resizeRecentEvent)="resizeRecentEvent()">
        <div
          class="disable-mouse-event"
          [ngClass]="{
            'topology-sidenav-content': isTopologyView,
            'device-table-sidenav-content': !isTopologyView,
            'full-network': isFullNetworkMode,
            'half-network': !isFullNetworkMode
          }"
        >
          <app-mx-topology
            id="topology"
            *ngIf="isTopologyView"
            [hasSideNav]="true"
            [trigger]="eventBus"
            [sideNav]="matSidenav"
            [commandBar]="mxCommandBar"
            [moveToDevice]="focusDevice"
            [siteId]="networkService.selectedSiteId"
            [groupId]="networkService.selectedGroupId"
            [viewMode]="topologyViewMode"
            [isEditableTopology]="topologyEditable"
            [topologyControlClass]="hasSideNav"
            [topologyText]="topologyTextObj"
            [wirelessAddon]="isWirelessAddon"
            [fullMode]="isFullNetworkMode"
            [displayAutomationButton]="displayAutomationButton"
            [isAutomationButtonBarOpen]="isAutomationButtonBarOpen"
            [groupList]="groupList"
            [widgetSize]="widgetSize"
            [recentEventHeight]="recentEventHeight"
            [isWidget]="isWidget"
            [scriptButtonList]="scriptButtonList"
            (siteNotSelected)="onSiteNotSelected($event)"
            (siteSelected)="onSiteSelected($event)"
            (siteMultiSelected)="onSiteMultiSelected($event)"
            (groupSelected)="onGroupSelected($event)"
            (groupMultiSelected)="onGroupMultiSelected($event)"
            (deviceSelected)="onDeviceSelected($event)"
            (refreshDeviceData)="onRefreshDeviceData($event)"
            (deviceMultiSelected)="onDeviceMultiSelected($event)"
            (linkSelected)="onLinkSelected($event)"
            (linkMultiSelected)="onLinkMultiSelected($event)"
            (multiObjectSelected)="onMultiObjectSelected($event)"
            (objectNotSelected)="onNotSelected()"
            (trafficViewClosed)="onTrafficViewClosed($event)"
            (securityViewClosed)="onSecurityViewClosed($event)"
            (vlanViewClosed)="onVlanViewClosed($event)"
            (refreshTrafficViewData)="refreshTrafficView($event)"
            (loading)="onLoading($event)"
            (securityViewDataComplete)="onSecurityViewDataComplete($event)"
            (exportPDF)="onExportPDF()"
            (exportCSV)="onExportCSV()"
            (showImportScdDialog)="onShowImportScdDialog()"
            (updateMenu)="onUpdateMenu($event)"
            (showGooseDialog)="onShowGooseDialog($event)"
            (showGooseResetDialog)="onShowResetGooseDialog($event)"
            (iopacSelected)="onSelectIopacModule($event)"
            (setSecurityViewData)="onSetSecurityViewData($event)"
          >
          </app-mx-topology>
          <app-mx-device-table
            *ngIf="isTopologyTableView"
            [fullMode]="isFullNetworkMode"
            [sideNav]="matSidenav"
            [commandBar]="mxCommandBar"
            [siteId]="networkService.selectedSiteId"
            [groupId]="networkService.selectedGroupId"
            [onSelectedSingleDevice]="device"
            (deviceSelected)="onDeviceSelected($event)"
            (deviceMultiSelected)="onDeviceMultiSelected($event)"
            [deviceSeverity]="deviceSeverity"
            (deviceNotSelected)="onNotSelected()"
          ></app-mx-device-table>
          <app-wireless-table-view *ngIf="isWirelessTableView && !isTopologyView"> </app-wireless-table-view>
          <app-wireless-table
            *ngIf="isWirelessTable && !isTopologyView"
            [siteId]="networkService.selectedSiteId"
            [groupId]="networkService.selectedGroupId"
            [mode]="wirelessTableOperationMode"
            (openDashboard)="onOpenWirelessDeviceDashboard($event)"
          >
          </app-wireless-table>
        </div>

        <app-mx-recent-event
          id="recent-event-layout"
          class="recent-event-layout"
          [siteId]="networkService.selectedSiteId"
          [groupId]="networkService.selectedGroupId"
          (minimizeRecentEventTable)="onMinimizeEventTable($event)"
          (focusEventDevice)="onFocusEventDevice($event)"
          (focusEventIopac)="onFocusEventIopacModule($event)"
        >
        </app-mx-recent-event>
      </div>
    </mat-sidenav-container>
  </div>
</div>
