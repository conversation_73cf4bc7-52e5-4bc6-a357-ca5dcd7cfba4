import { After<PERSON>iewInit, ChangeDetectorRef, Component, ElementRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { DomSanitizer } from '@angular/platform-browser';
import { MatIconRegistry } from '@angular/material/icon';
import {
  MatLegacyDialog as MatDialog,
  MatLegacyDialogConfig as MatDialogConfig
} from '@angular/material/legacy-dialog';
import { MatLegacySelectChange as MatSelectChange } from '@angular/material/legacy-select';
import { MatSidenav } from '@angular/material/sidenav';

import { LangChangeEvent, TranslateService } from '@ngx-translate/core';
import Big from 'big.js';
import * as _ from 'lodash';
import * as moment from 'moment';
import * as pdfMake from 'pdfmake/build/pdfmake.js';
import { Observable, forkJoin, from, fromEvent } from 'rxjs';
import { debounceTime, tap } from 'rxjs/operators';

import * as pdfFonts from '../../../assets/fonts/vfs_fonts.js';
import { AppState } from '../../app.service';
import { GlobalEvent, GlobalEventType } from '../../global.event';
import { AuthService } from '../../shared/Service/auth.service';
import { CheckCommandBarService } from '../../shared/Service/check-command-bar-key.service';
import { ErrorService } from '../../shared/Service/error.service';
import { GlobalService } from '../../shared/Service/global-services';
import { DataService } from '../../shared/Service/mx-platform/Service/DataService';
import { DeviceActionDialogComponent } from '../../shared/component/device-action-dialog/containers/index';
import {
  Group,
  WidgetSize,
  ScriptButtonList
} from '../../shared/component/display-automation-button/display-automation-button.model.js';
import { IpsConfigurationDialogComponent } from '../../shared/component/ips-configuration-dialog/containers/index';
import { MxTopologyData } from '../../shared/component/mx-topology/mx-topology-data';
import { MxTopologyComponent } from '../../shared/component/mx-topology/mx-topology.component';
import { MxTopologyService } from '../../shared/component/mx-topology/mx-topology.service';
import { SetBackgroundDialogComponent } from '../../shared/component/set-background-Dialog/set-background-Dialog.component';
import {
  extraLargeDialogConfig,
  largeDialogConfig,
  mediumDialogConfig,
  smallDialogConfig
} from '../../shared/dialog-config';
import { ActionMetaType } from '../../shared/model/cli-script';
import { CliObjectDatabaseStore } from '../cli-object-database/stores/cli-object-database.store';
import { DataType, Severity } from './../../shared/Service/mx-platform/DataDef/DataTypeDefinitions';
import {
  DeviceComponentsUnit,
  DeviceUnit,
  GooseTableUnit
} from './../../shared/Service/mx-platform/DataDef/DevicesDataDefs';
import { CurrentEventUnit } from './../../shared/Service/mx-platform/DataDef/EventDataDefs';
import { GroupUnit } from './../../shared/Service/mx-platform/DataDef/GroupDataDefs';
import {
  LinkUnit,
  SeverityThresholdUnit,
  SyncSFPUnit,
  TrafficData
} from './../../shared/Service/mx-platform/DataDef/LinkDataDefs';
import {
  AdvanceEventSettingUnit,
  TrafficColorUnit
} from './../../shared/Service/mx-platform/DataDef/PreferenceDataDefs';
import { SiteUnit } from './../../shared/Service/mx-platform/DataDef/SiteDataDefs';
import { DataRepositoryService } from './../../shared/Service/mx-platform/DataRepository/DataRepository';
import { EventBusService } from './../../shared/Service/mx-platform/Service/EventBusService';
import { MxCommandBarComponent } from './../../shared/component/mx-command-bar/mx-command-bar.component';
import { EventType } from './../../shared/component/mx-event/services/event-display-type';
import { EventService } from './../../shared/component/mx-event/services/event.service';
import { MxSiteNavComponent } from './../../shared/component/mx-site-nav/mx-site-nav.component';
import {
  TopologyViewMode,
  linkType,
  securityViewLevelText,
  wirelessRole
} from './../../shared/component/mx-topology/mx-topology.constants';
import { AddDeviceComponent } from './add-device/add-device.component';
import { AddLinkComponent } from './add-link/add-link.component';
import { AssignModelComponent } from './assign-model/assign-model.component';
import { AutoLayoutComponent } from './auto-layout/auto-layout.component';
import { AutoTopologyComponent } from './auto-topology/auto-topology.component';
import { ChangeDeviceIconDialogComponent } from './change-device-icon-dialog/change-device-icon-dialog.component';
import { ChangeGroupComponent } from './change-group/change-group.component';
import { ComputerDeviceDashboardComponent } from './computer-device-dashboard/computer-device-dashboard.component';
import { CreateGroupComponent } from './create-group/create-group.component';
import { DataNotReadyComponent } from './data-not-ready/data-not-ready.component';
import { DeleteDeviceComponent } from './delete-device/delete-device.component';
import { DeleteGroupComponent } from './delete-group/delete-group.component';
import { DeleteLinkComponent } from './delete-link/delete-link.component';
import { DeleteObjectComponent } from './delete-object/delete-object.component';
import { DeleteSiteComponent } from './delete-site/delete-site.component';
import { DeviceBasicInfoComponent } from './device-basic-information/device-basic-information.component';
import { DeviceIPConfigComponent } from './device-ip-config/device-ip-config.component';
import { DeviceLocatorComponent } from './device-locator/device-locator.component';
import { DeviceLoginAccountComponent } from './device-login-account/device-login-account.component';
import { DevicePollingSettingComponent } from './device-polling-setting/device-polling-setting.component';
import { SnmpSettingComponent } from './device-snmp-setting/device-snmp-setting.component';
import { DeviceTrapServerConfigComponent } from './device-trap-server-config/device-trap-server-config.component';
import { ExecuteCliObjectDialogComponent } from './execute-cli-object/containers/execute-cli-object-dialog/execute-cli-object-dialog.component';
import { ExportConfigComponent } from './export-config/export-config.component';
import { GooseDistinctComponent } from './goose-distinct/goose-distinct.component';
import { GooseResetComponent } from './goose-reset/goose-reset.component';
import { ImportConfigComponent } from './import-config/import-config.component';
import { ImportScdComponent } from './import-scd/import-scd.component';
import { IpSecTableDialogComponent } from './ip-sec-table-dialog/ip-sec-table-dialog.component';
import { LinkTrafficComponent } from './link-traffic/link-traffic.component';
import { MaintainGroupComponent } from './maintain-group/maintain-group.component';
import { ModifyDeviceAliasComponent } from './modify-device-alias/modify-device-alias.component';
import { GroupTraffic, SfpLink } from './network.def';
import { NetworkService } from './network.service';
import { PingDeviceComponent } from './ping-device/ping-device.component';
import { PollingIpComponent } from './polling-ip/polling-ip.component';
import { PortSettingComponent } from './port-setting/port-setting.component';
import { SerialPortMonitoringComponent } from './serial-port-monitoring/serial-port-monitoring.component';
import { SetDocumentComponent } from './set-document/set-document.component';
import { SetPortLabelComponent } from './set-port-label/set-port-label.component';
import { SeverityThresholdComponent } from './severity-threshold/severity-threshold.component';
import { SfpInfoComponent } from './sfp-info/sfp-info.component';
import { SfpListComponent } from './sfp-list/sfp-list.component';
import { SfpSyncComponent } from './sfp-sync/sfp-sync.component';
import { SiteManagementComponent } from './site-management/site-management.component';
import { TagDeviceComponent } from './tag-device/tag-device.component';
import { TagLinkComponent } from './tag-link/tag-link.component';
import { UpgradeFirmwareComponent } from './upgrade-firmware/upgrade-firmware.component';
import { WirelessDeviceDashboardComponent } from './wireless-device-dashboard/wireless-device-dashboard.component';

pdfMake.vfs = pdfFonts.pdfMake.vfs;

@Component({
  templateUrl: './network.component.html',
  styleUrls: ['./network.component.scss']
})
export class NetworkComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('sidenav', { static: true }) matSidenav: MatSidenav;
  @ViewChild(MxSiteNavComponent) mxSiteNav: MxSiteNavComponent;
  @ViewChild(MxCommandBarComponent) mxCommandBar: MxCommandBarComponent;
  @ViewChild(MxTopologyComponent) mxTopology: MxTopologyComponent;

  devicesData: DeviceUnit[];
  linksData: LinkUnit[];
  site: SiteUnit;
  group: GroupUnit;
  device: DeviceUnit;
  link: LinkUnit;
  sfpLink: SfpLink;
  deviceSecurityResult: any;
  securityViewProfile;
  securityViewProfilesData;
  securityViewHighColor;
  securityViewMediumColor;
  securityViewBasicColor;
  deviceSecurityLevel: string;
  deviceSecurityItems: any;
  isVPNLink = false;
  fromIp: any;
  toIp: any;
  linkSpeed: any;
  showFromPort = true;
  showToPort = true;
  showLinkSpeed = true;
  navSiteId;
  navGroupId;
  navGroupName;
  isShowDeviceComponents: boolean;
  isShowOtherDeviceProperties = false;
  tabIndex = 0;
  showGroup = false;
  showAutomationButton = false;
  showTab = true;
  tabName: string;
  isFullNetworkMode = false;
  focusDevice: DeviceUnit;
  previousMode: any = {};
  deviceSeverity;
  siteDeviceCount = 0;
  siteInformationDeviceCount = 0;
  siteWarningDeviceCount = 0;
  siteCriticalDeviceCount = 0;
  groupInformationDeviceCount = 0;
  groupWarningDeviceCount = 0;
  groupCriticalDeviceCount = 0;
  topologyEditable = true;
  v3TrapParseStatusError = 1;
  isV3TrapParseErrorEvent = false;
  currentEvents: any[];
  hasSideNav: boolean;
  groupsData: GroupUnit[];
  groupTrafficArr: GroupTraffic[];
  topologyTextObj;
  isWirelessAddon = false;
  // mx-command-bar
  menuData: any;
  commandBarKey = 'notSelectedMenu';
  isShowGroupButton = false;
  isShowAutomationButton = false;
  isAutomationButtonBarOpen = true;
  isShowModeButton = false;
  isShowInfoButton = false;
  disabledCommandBar = false;
  // View
  topologyViewMode;
  isTopologyView = true;
  isTopologyTableView = false;
  // isWirelessTableView is old wireless table
  isWirelessTableView = false;
  isWirelessTable = false;
  // Wireless
  wirelessTableOperationMode;

  iopacDevices: DeviceUnit[];
  selectedIopacModule = 0;

  moxaSysobjid = '.*******.4.1.8691';
  oldWirelessDevicesSysobjid = '.*******.4.1.8691.15';
  newWirelessDevicesSysobjid = '.*******.4.1.8691.600.1.300';

  onCellG4302LTE4Sysobjid = '.*******.4.1.8691.6.100.2.872480769';
  tn4908Sysobjid = '.*******.4.1.8691.6.100.2.587268097'; //TN-4908
  tn4908PoESerieSysobjid = '.*******.4.1.8691.6.100.2.587268098'; //TN-4908-PoE-Series
  tn4916PoESerieSysobjid = '.*******.4.1.8691.6.100.2.587268354'; //TN-4916-PoE-Series
  tn4916ETBN12PoE4GTXBPSysobjid = '.*******.4.1.8691.6.100.2.587268355'; //TN-4916-ETBN-12PoE-4GTXBP
  tn4908ETBN4TXBPSysobjid = '.*******.4.1.8691.6.100.2.587268099'; //TN-4908-ETBN-4TXBP
  tn4908ETBNF4GTXBPTSysobjid = '.*******.4.1.8691.6.100.2.587268100'; //TN-4908-ETBN-F-4GTXBP-T
  nPort6000Sysobjid = '.*******.4.1.8691.2.8';
  nPort6000G2Sysobjid = '.*******.4.1.8691.2.24';
  nPort5000G2Sysobjid = '.*******.4.1.8691.2.25';
  mGateMB3000Sysobjid = '.*******.4.1.8691.21.3';
  edrSysobjid = '.*******.4.1.8691.6.100.2';
  nat102 = '.*******.4.1.8691.6.100.2.855900161';
  cpuSysobjid = '.*******.4.1.8691.10.6500';
  switchSysobjid = '.*******.4.1.8691.600.1.12.1';
  enableDisableIpsOids = [
    '.*******.4.1.8691.6.100.2.855703553',
    '.*******.4.1.8691.6.100.2.855703554',
    '.*******.4.1.8691.6.100.2.855707649',
    '.*******.4.1.8691.6.100.2.855769089',
    '.*******.4.1.8691.6.100.2.855969793',
    '.*******.4.1.8691.6.100.2.587268097',
    '.*******.4.1.8691.6.100.2.587268100',
    '.*******.4.1.8691.6.100.2.587268354',
    '.*******.4.1.8691.6.100.2.587268098'
  ]

  // .*******.4.1.8691.2.20
  // .*******.4.1.8691.2.19
  // .*******.4.1.8691.2.24.6450
  // .*******.4.1.8691.2.24.6650
  // .*******.4.1.8691.2.24.6150
  // .*******.4.1.8691.2.24.6250
  // .*******.4.1.8691.2.25.5150
  // .*******.4.1.8691.2.25.5250
  // .*******.4.1.8691.2.25.5450

  ipcOids = [
    '.*******.4.1.8691.16.1.6814', // DA681C (Windows)
    '.*******.4.1.8691.16.1.6824', // DA682C (Windows)
    '.*******.4.1.8691.16.720', // DA720 (Windows)
    '.*******.4.1.8691.16.74001', // MC7400 (Windows)
    '.*******.4.1.8691.16.1.8204', // DA820C (Windows)
    '.*******.4.1.8691.16.3.24064', // V2406C (Windows)
    '.*******.4.1.8691.12.1.88', // V3210-TL1-4L-T
    '.*******.4.1.8691.12.1.89', // V3210-TL1-8L-T
    '.*******.4.1.8691.12.1.90', // V3210-TL1-8L-CT-T
    '.*******.4.1.8691.12.1.91', // V3210-TL1-8L-CT-T
    '.*******.4.1.8691.12.1.92', // V3210-TL3-4L-T
    '.*******.4.1.8691.12.1.93', // V3210-TL3-8L-T
    '.*******.4.1.8691.12.1.94', // V3210-TL3-4L-CT-T
    '.*******.4.1.8691.12.1.95', // V3210-TL3-8L-CT-T
    '.*******.4.1.8691.12.1.96', // V3210-TL5-4L-T
    '.*******.4.1.8691.12.1.97', // V3210-TL5-8L-T
    '.*******.4.1.8691.12.1.98', // V3210-TL5-4L-CT-T
    '.*******.4.1.8691.12.1.99', // V3210-TL5-8L-CT-T
    '.*******.4.1.8691.12.1.100', // V3210-TL7-4L-T
    '.*******.4.1.8691.12.1.101', // V3210-TL7-8L-T
    '.*******.4.1.8691.12.1.102', // V3210-TL7-4L-CT-T
    '.*******.4.1.8691.12.1.103', // V3210-TL7-8L-CT-T
    '.*******.4.1.8691.12.1.104', // V3210-TL7-8L-TSN-CT-T
    '.*******.4.1.8691.12.1.105', // V3404-TL1-W-CT-T
    '.*******.4.1.8691.12.1.106', // V3408-TL1-W-CT-T
    '.*******.4.1.8691.12.1.107', // V3404-TL3-W-CT-T
    '.*******.4.1.8691.12.1.108', // V3408-TL3-W-CT-T
    '.*******.4.1.8691.12.1.109', // V3404-TL5-W-CT-T
    '.*******.4.1.8691.12.1.110', // V3408-TL5-W-CT-T
    '.*******.4.1.8691.12.1.111', // V3404-TL7-W-CT-T
    '.*******.4.1.8691.12.1.112' // V3408-TL7-W-CT-T
  ];

  private securityViewData;
  private securityViewPreferenceData;
  private switchData = [];
  private awkData = [];
  private mgateData = [];
  private nportData = [];
  private nport5000Data = [];
  private unknownData = [];

  // DisplayAutomationButtonComponent
  previewMode = true;
  isWidget: boolean;
  scriptButtonList: ScriptButtonList;
  widgetSize: WidgetSize;
  groupList: Group[] = [];
  recentEventHeight: number;

  constructor(
    public cliStore: CliObjectDatabaseStore,
    public dataService: DataService,
    public dataRepository: DataRepositoryService,
    public eventBus: EventBusService,
    public eventService: EventService,
    public globalService: GlobalService,
    public networkService: NetworkService,
    public mxData: MxTopologyData,
    private auth: AuthService,
    private dialog: MatDialog,
    private appState: AppState,
    private translateService: TranslateService,
    private matIconRegistry: MatIconRegistry,
    private domSanitizer: DomSanitizer,
    private router: Router,
    private errorService: ErrorService,
    private cdr: ChangeDetectorRef,
    private elementRef: ElementRef,
    private checkCommandBarService: CheckCommandBarService,
    private topologyService: MxTopologyService
  ) {
    this.matIconRegistry.addSvgIcon(
      'qr_code',
      this.domSanitizer.bypassSecurityTrustResourceUrl('assets/img/qrcode-solid.svg')
    );
    this.matIconRegistry.addSvgIcon(
      'traffic',
      this.domSanitizer.bypassSecurityTrustResourceUrl('assets/img/tachometer-alt-solid.svg')
    );
    this.matIconRegistry.addSvgIcon(
      'file_upload',
      this.domSanitizer.bypassSecurityTrustResourceUrl('assets/img/file-upload.svg')
    );
    this.matIconRegistry.addSvgIcon('sfp', this.domSanitizer.bypassSecurityTrustResourceUrl('assets/img/sfp.svg'));
    this.networkService.resizeSubscription = fromEvent(window, 'resize')
      .pipe(debounceTime(200))
      .subscribe(() => {
        this.decideShowCommandBarOrNot();
      });
    this.menuData = this.auth.menuData;
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    // Language change, recalculate command bar
    this.translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.topologyTextObj = this.globalService.getTopologyTextObject().topologyTextObj;
    });
  }

  ngOnInit(): void {
    const groupName = localStorage.getItem('groupName');
    if (groupName?.toLowerCase().includes('iopac')) {
      localStorage.setItem('groupId', '0');
    }
    const storedState = JSON.parse(localStorage.getItem('isAutomationButtonBarOpen'));

    if (storedState !== null) {
      this.isAutomationButtonBarOpen = storedState;
    } else {
      localStorage.setItem('isAutomationButtonBarOpen', 'true');
      this.isAutomationButtonBarOpen = true;
    }

    this.networkService.initNetworkServiceData();
    this.isShowModeButton = true;
    this.isShowInfoButton = true;
    this.devicesData = this.dataRepository.getData(DataType.DEVICE);

    this.decideShowCommandBarOrNot();
    if (sessionStorage.getItem('isFromDashboard') === '1') {
      sessionStorage.setItem('isFromDashboard', '0');
      if (sessionStorage.getItem('deviceSeverity')) {
        this.deviceSeverity = sessionStorage.getItem('deviceSeverity');
        this.isTopologyView = false;
        this.onToggleMode(this.isTopologyView);
      } else if (sessionStorage.getItem('wirelessTableOperationMode')) {
        this.wirelessTableOperationMode = sessionStorage.getItem('wirelessTableOperationMode');
        sessionStorage.setItem('wirelessTableOperationMode', '');
        this.showWirelessTable();
      }
    }
    if (sessionStorage.getItem('showLoginNotification') === 'YES') {
      sessionStorage.setItem('showLoginNotification', 'NO');
      this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOGIN_NOTIFICATION, null));
    }
    this.topologyTextObj = this.globalService.getTopologyTextObject().topologyTextObj;
    this.onUpdateMenu({
      siteId: this.networkService.selectedSiteId,
      groupId: this.networkService.selectedGroupId
    });
    this.networkService.selectedSiteId = undefined;
    this.networkService.selectedGroupId = undefined;
    this.subscribeGlobalEvent();

    this.cliStore.getScriptButtonList();
    this.cliStore.state$.subscribe(d => {
      const { scriptButtonList } = d;
      this.previewMode = true;
      this.isWidget = scriptButtonList.show_button_widget;
      this.widgetSize = scriptButtonList.size;
      this.groupList = scriptButtonList.groups;
      this.scriptButtonList = scriptButtonList;
      if (scriptButtonList.groups.length) {
        this.isShowAutomationButton = true;
        this.showAutomationButton = true;
      }
    });
  }

  get displayAutomationButton() {
    return (
      this.isShowAutomationButton &&
      this.showAutomationButton &&
      this.isTopologyView &&
      (this.topologyViewMode === TopologyViewMode.none || this.topologyViewMode === TopologyViewMode.grid)
    );
  }

  updateMenuKey(commandBarKey: string) {
    const selectedDevices = this.networkService.selectedDevice
      ? [this.networkService.selectedDevice]
      : this.networkService.selectedDevices;

    const sysobjidArray = selectedDevices.map(d => d.sysobjid);
    const newMenuKey = [...this.checkCommandBarService.findMenuKey(sysobjidArray).slice().reverse()];
    const updatedMenu = _.cloneDeep(this.auth.menuData[commandBarKey]);
    const managementMenuMap = _.cloneDeep(this.auth.menuData['menuForDeviceManagementInTopology']).reduce(
      (map, menu) => {
        menu.menuData.forEach(item => {
          if (
            (selectedDevices[0]?.sysobjid?.includes(this.nPort6000G2Sysobjid) ||
              selectedDevices[0]?.sysobjid?.includes(this.nPort5000G2Sysobjid) ||
              selectedDevices[0]?.sysobjid?.includes(this.mGateMB3000Sysobjid)) &&
            item.click === 'DISABLE_UNENCRYTP_PORT'
          ) {
            return;
          }
          if (selectedDevices[0]?.sysobjid?.includes(this.switchSysobjid)) {
            return;
          }
          map[item.click] = { ...item, parentMenuName: menu.menuName };
        });
        return map;
      },
      {}
    );

    newMenuKey.forEach(key => {
      if (managementMenuMap[key]) {
        const menuItem = managementMenuMap[key];
        const targetMenu = updatedMenu.find(menu => menu.menuName === menuItem.parentMenuName);
        if (targetMenu) {
          if (!targetMenu.menuData) {
            targetMenu.menuData = [];
          }
          const titles = targetMenu.menuData.map(d => d.title);
          if (!titles.includes(menuItem.title)) {
            targetMenu.menuData.unshift({ ...menuItem });
          }
        } else {
          const parentMenu = this.auth.menuData['menuForDeviceManagementInTopology'].find(
            menu => menu.menuName === menuItem.parentMenuName
          );
          if (parentMenu) {
            const data = { ...parentMenu };
            data.menuData = [{ ...menuItem }];
            updatedMenu.unshift(data);
          }
        }
      }
    });

    const menu = this.sortByMenuName(updatedMenu);

    this.menuData[commandBarKey] = menu;
    if (
      selectedDevices?.length > 1 &&
      selectedDevices.every(d => d?.mms_info && !d?.sysobjid.includes('.*******.4.1.8691'))
    ) {
      this.menuData[commandBarKey].unshift(this.auth.menuData['groupingMenuForIED'][2]);
      this.menuData[commandBarKey].unshift(this.auth.menuData['groupingMenuForIED'][0]);
    }

    this.menuData = { ...this.menuData };
    this.commandBarKey = commandBarKey;
  }

  sortByMenuName(data) {
    const orderedMenuNames = this.auth.menuData['menuForDeviceManagementInTopology'].map(d => d.menuName);
    data.sort((a, b) => {
      const aIndex = orderedMenuNames.indexOf(a.menuName);
      const bIndex = orderedMenuNames.indexOf(b.menuName);

      if (aIndex !== -1 && bIndex !== -1) {
        return aIndex - bIndex;
      }

      if (aIndex !== -1) {
        return -1;
      }

      if (bIndex !== -1) {
        return 1;
      }

      return 0;
    });
    return data;
  }

  convertValue(json: string, index: number): number {
    let ret = 0;
    try {
      ret = +JSON.parse(json)[index];
      if (isNaN(ret)) {
        ret = 0;
      }
    } catch (error) {}
    return ret;
  }

  getSFPLinks(): SyncSFPUnit {
    const ret: SyncSFPUnit = new SyncSFPUnit();
    const links = this.dataRepository.getData(DataType.LINK);
    for (let i = 0; i < links.length; i++) {
      const link = links[i];
      if (link.from_port !== 0 && link.to_port !== 0) {
        // filter SFP links
        const fromDevice = this.dataRepository.getData(DataType.DEVICE, link.site_id, link.from_device)[0];
        const toDevice = this.dataRepository.getData(DataType.DEVICE, link.site_id, link.to_device)[0];
        const fromSfpIndex = _.findIndex(fromDevice.device_components.sfpTxPower, (fromSfp: DeviceComponentsUnit) => {
          if (fromSfp) {
            return +fromSfp.index === link.from_port;
          }
        });
        const toSfpIndex = _.findIndex(toDevice.device_components.sfpTxPower, (toSfp: DeviceComponentsUnit) => {
          if (toSfp) {
            return +toSfp.index === link.to_port;
          }
        });
        if (fromSfpIndex === -1 && toSfpIndex === -1) {
          continue;
        }
        // init severityThresholdUnit
        const severityThresholdUnit: SeverityThresholdUnit = new SeverityThresholdUnit();
        severityThresholdUnit.bandwidth_over_severity = Severity.EVENT_SEVERITY_WARNING;
        severityThresholdUnit.bandwidth_over_threshold = 0;
        severityThresholdUnit.bandwidth_below_severity = Severity.EVENT_SEVERITY_WARNING;
        severityThresholdUnit.bandwidth_below_threshold = 0;
        severityThresholdUnit.packet_error_severity = Severity.EVENT_SEVERITY_WARNING;
        severityThresholdUnit.packet_error_threshold = 0;
        severityThresholdUnit.sfp_tx_below_threshold = 0;
        severityThresholdUnit.sfp_tx_below_severity = Severity.EVENT_SEVERITY_WARNING;
        severityThresholdUnit.sfp_rx_below_threshold = 0;
        severityThresholdUnit.sfp_rx_below_severity = Severity.EVENT_SEVERITY_WARNING;
        severityThresholdUnit.sfp_volt_below_threshold = 0;
        severityThresholdUnit.sfp_volt_below_severity = Severity.EVENT_SEVERITY_WARNING;
        severityThresholdUnit.sfp_volt_over_threshold = 0;
        severityThresholdUnit.sfp_volt_over_severity = Severity.EVENT_SEVERITY_WARNING;
        severityThresholdUnit.sfp_temp_over_threshold = 0;
        severityThresholdUnit.sfp_temp_over_severity = Severity.EVENT_SEVERITY_WARNING;

        // get threshold min/max value from device
        if (fromSfpIndex !== -1) {
          severityThresholdUnit.sfp_tx_below_threshold = Math.min(
            this.convertValue(fromDevice.device_components.sfpTxPowerWarn[fromSfpIndex].status, 1),
            severityThresholdUnit.sfp_tx_below_threshold
          );
          severityThresholdUnit.sfp_rx_below_threshold = Math.min(
            this.convertValue(fromDevice.device_components.sfpRxPowerWarn[fromSfpIndex].status, 0),
            severityThresholdUnit.sfp_rx_below_threshold
          );
          severityThresholdUnit.sfp_temp_over_threshold = Math.max(
            this.convertValue(fromDevice.device_components.sfpTempWarn[fromSfpIndex].status, 0),
            severityThresholdUnit.sfp_temp_over_threshold
          );
        }

        if (toSfpIndex !== -1) {
          severityThresholdUnit.sfp_tx_below_threshold = Math.min(
            this.convertValue(toDevice.device_components.sfpTxPowerWarn[toSfpIndex].status, 1),
            severityThresholdUnit.sfp_tx_below_threshold
          );
          severityThresholdUnit.sfp_rx_below_threshold = Math.min(
            this.convertValue(toDevice.device_components.sfpRxPowerWarn[toSfpIndex].status, 0),
            severityThresholdUnit.sfp_rx_below_threshold
          );
          severityThresholdUnit.sfp_temp_over_threshold = Math.max(
            this.convertValue(toDevice.device_components.sfpTempWarn[toSfpIndex].status, 0),
            severityThresholdUnit.sfp_temp_over_threshold
          );
        }
        ret.link.push(link);
        ret.severityThresholdUnit.push(severityThresholdUnit);
      }
    }
    return ret;
  }

  subscribeGlobalEvent(): void {
    this.networkService.globalEvent = this.appState.events.subscribe((event: GlobalEvent) => {
      if (event.type === GlobalEventType.REFRESH_TOPOLOGY) {
        this.mxTopology.redrawTopology();
      } else if (event.type === GlobalEventType.CLOSE_WIRELESS_TABLE) {
        this.onButtonClick('closeWirelessTableView');
      }
    });
  }

  ngAfterViewInit(): void {
    this.resizeRecentEvent();
    this.cdr.detectChanges();
  }

  ngOnDestroy(): void {
    this.networkService.unsubscribeTrigger();
  }

  propertyIsArray(device: DeviceUnit, property: string): boolean {
    return device?.device_components[property] instanceof Array;
  }

  decideShowCommandBarOrNot(): void {
    if (sessionStorage.getItem('userMode') === 'user') {
      this.disabledCommandBar = true;
      this.topologyEditable = false;
    } else {
      this.disabledCommandBar = false;
      this.topologyEditable = true;
    }
  }

  getDevicesOfSite(siteId: string): DeviceUnit[] {
    const devices = _.filter(this.devicesData, dev => {
      if (dev.site_id === siteId) {
        return true;
      } else {
        return false;
      }
    });
    return devices;
  }

  getDevicesOfSiteBySeverity(siteId: string, severity: number): DeviceUnit[] {
    const devices = _.filter(this.devicesData, dev => {
      if (dev.site_id === siteId && dev.severity === severity) {
        return true;
      } else {
        return false;
      }
    });
    return devices;
  }

  getDevicesOfGroupBySeverity(siteId: string, groupId: number, severity: number): DeviceUnit[] {
    const devices = _.filter(this.devicesData, dev => {
      if (dev.site_id === siteId && dev.group === groupId && dev.severity === severity) {
        return true;
      } else {
        return false;
      }
    });
    return devices;
  }

  getDevicesOfGroupBySeverityRecursive(siteId: string, groupId: number, severity: number): DeviceUnit[] {
    const subgroups = [groupId];
    const allGroups = this.dataRepository.getData(DataType.GROUP, siteId);
    _.forEach(allGroups, group => {
      let parent = group.parent_id;
      while (parent !== 0) {
        // while parent is not root
        if (parent === groupId) {
          break;
        } else {
          const parentGroup = this.dataRepository.getData(DataType.GROUP, siteId, parent)[0];
          parent = parentGroup?.parent_id;
        }
      }
      if (parent === groupId) {
        subgroups.push(group.id);
      }
    });
    const devices = _.filter(this.devicesData, dev => {
      if (dev.site_id === siteId && subgroups.indexOf(dev.group) !== -1 && dev.severity === severity) {
        return true;
      } else {
        return false;
      }
    });
    return devices;
  }

  getIopacDevicesOfGroupById(siteId: string, groupId: number): Promise<DeviceUnit[]> {
    return this.networkService.getSiteDeviceData(siteId, groupId).then((groupDevices: DeviceUnit[]) => {
      this.iopacDevices = groupDevices
        .filter(device => device.module_slot)
        .map(iopac => {
          return {
            ...iopac,
            name: iopac.module_slot
          };
        })
        .filter(iopac => iopac.group === groupId);

      const ioDevice = this.iopacDevices.find(iopac => iopac.id === this.networkService.selectedIopacDevice?.id);
      this.selectedIopacModule = ioDevice ? ioDevice.id : 0;

      return this.iopacDevices;
    });
  }

  checkIfIsFromMXviewWizard(): void {
    if (sessionStorage.getItem('isFromMXviewWizard') === '1') {
      sessionStorage.setItem('isFromMXviewWizard', '0');
      smallDialogConfig.data = this.mxTopology;
      this.dialog.open(AutoLayoutComponent, smallDialogConfig);
    }
  }

  onSiteNotSelected(): void {
    this.clearAllObject();
    this.commandBarKey = 'siteNotSelectedMenu';
  }

  onSiteSelected(data: any): void {
    this.clearAllObject();
    this.showTab = true;
    this.tabName = this.translateService.instant('SITE_PROPERTIES.title');
    this.site = data;
    this.networkService.selectedSite = this.site;
    this.devicesData = this.dataRepository.getData(DataType.DEVICE);
    this.siteDeviceCount = this.getDevicesOfSite(this.site.site_id).length;
    this.siteInformationDeviceCount = this.getDevicesOfSiteBySeverity(
      this.site.site_id,
      Severity.EVENT_SEVERITY_INFO
    ).length;
    this.siteWarningDeviceCount = this.getDevicesOfSiteBySeverity(
      this.site.site_id,
      Severity.EVENT_SEVERITY_WARNING
    ).length;
    this.siteCriticalDeviceCount = this.getDevicesOfSiteBySeverity(
      this.site.site_id,
      Severity.EVENT_SEVERITY_CRITICAL
    ).length;
    if (this.networkService.selectedSite.status === false) {
      this.commandBarKey = 'siteSelectedDeleteMenu';
    } else {
      this.commandBarKey = 'siteSelectedMenu';
    }
  }

  onSiteMultiSelected(data: any): void {
    this.clearAllObject();
    this.networkService.selectedSites = data;
    let showDeleteSites = true;
    this.networkService.selectedSites.forEach(site => {
      if (site.status) {
        showDeleteSites = false;
      }
    });
    if (showDeleteSites) {
      this.commandBarKey = 'siteMultiSelectedMenu';
    } else {
      this.commandBarKey = 'siteNotSelectedMenu';
    }
  }

  // NOTE: selected event emit
  onDeviceSelected(data: {
    device: any;
    securityViewPreferenceData: any;
    deviceSecurityResult: any;
    securityProfile: any;
    isIopac: boolean;
  }): void {
    if (!data.device) return;

    let tempCommandBarKey = '';
    !data.isIopac && this.clearAllObject();
    this.showTab = true;
    this.tabName = this.translateService.instant('DEVICE_PROPERTIES.title');

    if (data.device?.v3_trap_parse_status === this.v3TrapParseStatusError) {
      this.tabIndex = 1;
    }
    // security mode
    if (this.topologyViewMode === TopologyViewMode.security) {
      this.tabIndex = 2;
      this.device = this.sortPortRelatedProp(data.device);
      this.deviceSecurityResult = data.deviceSecurityResult;
      if (data.securityViewPreferenceData !== undefined) {
        if (data.securityViewPreferenceData.length > 0) {
          this.securityViewProfile = data.securityViewPreferenceData[0].profile;
        }
      }
      if (this.securityViewProfile === 1) {
        this.securityViewHighColor = this.globalService.hexToRgba(data.securityViewPreferenceData[0].color.high);
        this.securityViewMediumColor = this.globalService.hexToRgba(data.securityViewPreferenceData[0].color.medium);
        this.securityViewBasicColor = this.globalService.hexToRgba(data.securityViewPreferenceData[0].color.basic);
      }
      this.buildSecurityItems(data.deviceSecurityResult?.security_items, data.securityProfile);
      this.deviceSecurityLevel = this.getDeviceSecurityLevel(data.deviceSecurityResult.security_level).text;
      setTimeout(() => {
        if (this.elementRef.nativeElement.querySelector('.mat-tab-header-pagination-after') !== null) {
          this.elementRef.nativeElement.querySelector('.mat-tab-header-pagination-after').click();
        }
      }, 1000);
    } else {
      this.device = this.sortPortRelatedProp(data.device);
    }
    this.networkService.selectedDevice = this.device;
    this.dataService
      .getCurrentEventOfDevice(this.networkService.selectedDevice.site_id, this.networkService.selectedDevice.ip)
      .subscribe(
        (currentEvent: CurrentEventUnit[]) => {
          this.getCurrentEvent(currentEvent);
        },
        (error: unknown) => {
          this.currentEvents = [];
        }
      );
    // check has otherDeviceProperties ?
    this.isShowOtherDeviceProperties = Object.keys(this.displayedDeviceComponentsToArray(this.device)).length !== 0;
    if (this.device.device_components && this.device.device_components.constructor === Object) {
      this.isShowDeviceComponents = true;
    } else {
      this.isShowDeviceComponents = false;
    }

    const selectedDeviceSysobjid = this.networkService.selectedDevice.sysobjid;
    if (!this.isTopologyView) {
      if (
        _.includes(this.networkService.selectedDevice.model, 'EDR') ||
        selectedDeviceSysobjid === this.onCellG4302LTE4Sysobjid ||
        selectedDeviceSysobjid === this.tn4908Sysobjid ||
        selectedDeviceSysobjid === this.tn4908PoESerieSysobjid ||
        selectedDeviceSysobjid === this.tn4916PoESerieSysobjid ||
        selectedDeviceSysobjid === this.tn4916ETBN12PoE4GTXBPSysobjid ||
        selectedDeviceSysobjid === this.tn4908ETBN4TXBPSysobjid ||
        selectedDeviceSysobjid === this.tn4908ETBNF4GTXBPTSysobjid ||
        selectedDeviceSysobjid.includes(this.edrSysobjid)
      ) {
        if (
          selectedDeviceSysobjid.includes(this.nat102) ||
          selectedDeviceSysobjid.includes(this.onCellG4302LTE4Sysobjid)
        ) {
          tempCommandBarKey = 'edrDeviceNoNsmTableSelectedMenu';
        } else if (this.enableDisableIpsOids.includes(selectedDeviceSysobjid)) {
            tempCommandBarKey = 'edrIpsDeviceTableSelectedMenu';
        } else {
            tempCommandBarKey = 'edrDeviceTableSelectedMenu';
        }
      } else if (_.includes(this.networkService.selectedDevice.model, 'ICMP')) {
        tempCommandBarKey = 'icmpSelectedMenu';
      } else if (this.networkService.selectedDevice.model.startsWith('UC')) {
        tempCommandBarKey = 'ucComputerTableSelectedMenu';
      } else if (this.isComputer()) {
        tempCommandBarKey = 'computerSelectedMenu';
      } else if (selectedDeviceSysobjid?.includes(this.cpuSysobjid)) {
        tempCommandBarKey = 'iopacCpuMenu';
      } else if (selectedDeviceSysobjid?.includes(this.switchSysobjid)) {
        tempCommandBarKey = 'iopacSwitchMenu';
      } else {
        tempCommandBarKey =
          selectedDeviceSysobjid.includes(this.nPort6000Sysobjid) ||
          selectedDeviceSysobjid.includes(this.nPort6000G2Sysobjid) ||
          selectedDeviceSysobjid.includes(this.nPort5000G2Sysobjid) ||
          selectedDeviceSysobjid.includes(this.mGateMB3000Sysobjid)
            ? 'deviceSerialPortTableSelectedMenu'
            : 'deviceTableSelectedMenu';
      }
    } else {
      if (
        _.includes(this.networkService.selectedDevice.model, 'EDR') ||
        selectedDeviceSysobjid === this.onCellG4302LTE4Sysobjid ||
        selectedDeviceSysobjid === this.tn4908Sysobjid ||
        selectedDeviceSysobjid === this.tn4908PoESerieSysobjid ||
        selectedDeviceSysobjid === this.tn4916PoESerieSysobjid ||
        selectedDeviceSysobjid === this.tn4916ETBN12PoE4GTXBPSysobjid ||
        selectedDeviceSysobjid === this.tn4908ETBN4TXBPSysobjid ||
        selectedDeviceSysobjid === this.tn4908ETBNF4GTXBPTSysobjid ||
        selectedDeviceSysobjid?.includes(this.edrSysobjid)
      ) {
        if (
          selectedDeviceSysobjid.includes(this.nat102) ||
          selectedDeviceSysobjid.includes(this.onCellG4302LTE4Sysobjid)
        ) {
          tempCommandBarKey = 'edrNoNsmSelectedMenu';
        } else if (this.enableDisableIpsOids.includes(selectedDeviceSysobjid)) {
            tempCommandBarKey = 'edrIpsSelectedMenu';
        } else {
          tempCommandBarKey = 'edrSelectedMenu';
        }
      } else if (_.includes(this.networkService.selectedDevice.model, 'ICMP')) {
        tempCommandBarKey = 'icmpSelectedMenu';
      } else if (this.networkService.selectedDevice.wireless_role && this.isWirelessAddon) {
        // 如果是 Windows 系統，而且是舊無線的設備，則顯示 Upgrade Firmware/Import Config/Export Config
        // 新無線的設備，則顯示 Upgrade Firmware
        if (localStorage.getItem('isWindowsOS') === '1') {
          if (this.device.sysobjid.indexOf(this.newWirelessDevicesSysobjid) !== -1) {
            tempCommandBarKey = 'newWirelessAddonDeviceSelectedMenu';
          } else {
            tempCommandBarKey = 'wirelessAddonDeviceSelectedMenu';
          }
        } else {
          // 如果非 Windows 系統，而且是舊無線的設備，則不顯示 Upgrade Firmware/Import Config/Export Config
          // 新無線的設備，則顯示 Upgrade Firmware
          if (this.device.sysobjid.indexOf(this.oldWirelessDevicesSysobjid) !== -1) {
            tempCommandBarKey = 'oldWirelessAddonDeviceSelectedMenu';
          } else if (this.device.sysobjid.indexOf(this.newWirelessDevicesSysobjid) !== -1) {
            tempCommandBarKey = 'newWirelessAddonDeviceSelectedMenu';
          } else {
            tempCommandBarKey = 'wirelessAddonDeviceSelectedMenu';
          }
        }
      } else if (this.networkService.selectedDevice.model?.startsWith('UC')) {
        tempCommandBarKey = 'ucComputerSelectedMenu';
      } else if (this.isComputer()) {
        tempCommandBarKey = 'computerSelectedMenu';
      } else if (this.networkService.selectedDevice.device_group) {
        tempCommandBarKey = 'iopacDeviceMenu';
      } else if (selectedDeviceSysobjid?.includes(this.cpuSysobjid)) {
        tempCommandBarKey = 'iopacCpuMenu';
      } else if (selectedDeviceSysobjid?.includes(this.switchSysobjid)) {
        tempCommandBarKey = 'iopacSwitchMenu';
      } else {
        // 如果是 Windows 系統，而且是舊無線的設備，則顯示 Upgrade Firmware/Import Config/Export Config
        // 新無線的設備，則顯示 Upgrade Firmware
        if (localStorage.getItem('isWindowsOS') === '1') {
          if (this.device.sysobjid?.indexOf(this.newWirelessDevicesSysobjid) !== -1) {
            tempCommandBarKey = 'newWirelessDeviceSelectedMenu';
          } else {
            tempCommandBarKey =
              selectedDeviceSysobjid.includes(this.nPort6000Sysobjid) ||
              selectedDeviceSysobjid.includes(this.nPort6000G2Sysobjid) ||
              selectedDeviceSysobjid.includes(this.nPort5000G2Sysobjid) ||
              selectedDeviceSysobjid.includes(this.mGateMB3000Sysobjid)
                ? 'deviceSerialPortSelectedMenu'
                : 'deviceSelectedMenu';
          }
        } else {
          // 如果非 Windows 系統，而且是舊無線的設備，則不顯示 Upgrade Firmware/Import Config/Export Config
          // 新無線的設備，則顯示 Upgrade Firmware
          if (this.device.sysobjid.indexOf(this.oldWirelessDevicesSysobjid) !== -1) {
            tempCommandBarKey = 'oldWirelessDeviceSelectedMenu';
          } else if (this.device.sysobjid.indexOf(this.newWirelessDevicesSysobjid) !== -1) {
            tempCommandBarKey = 'newWirelessDeviceSelectedMenu';
          } else if (this.device.model.substring(0, 4) === 'AWK-') {
            tempCommandBarKey = 'oldWirelessDeviceSelectedMenu';
          } else {
            tempCommandBarKey =
              selectedDeviceSysobjid.includes(this.nPort6000Sysobjid) ||
              selectedDeviceSysobjid.includes(this.nPort6000G2Sysobjid) ||
              selectedDeviceSysobjid.includes(this.nPort5000G2Sysobjid) ||
              selectedDeviceSysobjid.includes(this.mGateMB3000Sysobjid)
                ? 'deviceSerialPortSelectedMenu'
                : 'deviceSelectedMenu';
          }
        }
      }
    }
    this.updateMenuKey(tempCommandBarKey);
  }

  getCurrentEvent(events: CurrentEventUnit[]): void {
    this.currentEvents = _.clone(events);
    _.forEach(this.currentEvents, (event: CurrentEventUnit) => {
      event.timestamp = this.globalService.toDateTimeString(event.timestamp);
      if (event.description === undefined) {
        event.description = this.eventService.assignEventDescription(
          event.type,
          event.value,
          event.port,
          event.user,
          event.trapdetail,
          event.trapoid,
          event.threshold,
          event.event_name,
          event.event_description,
          event.threshold_type,
          event?.display,
          event.ip,
          event.cliname,
          event.result,
          this.globalService.toDateTimeString(event.event_time),
          event.status,
          event?.last,
          event?.conflicts
        );
      }
      if (event.type === EventType.eventV3TrapParseError) {
        event.isV3TrapParseErrorEvent = true; // distinguish v3 trap event from others
      } else {
        event.isV3TrapParseErrorEvent = false;
      }
    });
  }

  removeV3TrapErrorEventFromCurrentEvent(): void {
    this.currentEvents = _.reject(this.currentEvents, event => {
      return event.type === EventType.eventV3TrapParseError;
    });
  }

  clearV3TrapEvent(): void {
    this.dataService
      .clearV3TrapParseErrorStatus(this.device.site_id, this.device.id)
      .then(value => {
        this.removeV3TrapErrorEventFromCurrentEvent();
      })
      .catch(() => {
        this.errorService.handleNormalError(
          this.translateService.instant('NETWORK.current_status.v3_trap_event_clear_fail')
        );
      });
  }

  onRefreshDeviceData(device: DeviceUnit): void {
    if (this.device !== undefined && this.device !== null) {
      if (this.device.ip === device.ip) {
        this.device.modbus_info = device.modbus_info;
        if (device.device_components.sysObjectId !== undefined) {
          this.device = this.sortPortRelatedProp(device);
          this.networkService.selectedDevice = this.device;
        } else if (device.Availability !== undefined && this.device.Availability !== undefined) {
          this.device.Availability = device.Availability;
        }
        this.isShowOtherDeviceProperties = Object.keys(this.displayedDeviceComponentsToArray(this.device)).length !== 0;
        if (this.device.device_components && this.device.device_components.constructor === Object) {
          this.isShowDeviceComponents = true;
        } else {
          this.isShowDeviceComponents = false;
        }
        // check ifSpeed
        if (this.device.device_components && this.device.device_components.ifSpeed) {
          for (let i = 0; i < this.device.device_components.ifSpeed.length; i++) {
            const aIfSpeed = this.device.device_components.ifSpeed[i].status;
            if (aIfSpeed !== undefined) {
              if (aIfSpeed == 0) {
                this.device.device_components.ifSpeed[i].statusEx = 0 + 'M';
              } else {
                if (aIfSpeed >= 1000000000) {
                  this.device.device_components.ifSpeed[i].statusEx =
                    parseFloat((aIfSpeed / 1000000000).toFixed(3)) + 'G';
                } else if (aIfSpeed >= 1000000) {
                  this.device.device_components.ifSpeed[i].statusEx = parseFloat((aIfSpeed / 1000000).toFixed(3)) + 'M';
                } else {
                  this.device.device_components.ifSpeed[i].statusEx = aIfSpeed + 'bps';
                }
              }
            }
          }
        }
      }
    }
  }

  onDeviceMultiSelected(data: any): void {
    const isNportDevices =
      data.every(device => device.sysobjid === data[0].sysobjid) &&
      (data[0].sysobjid.includes(this.nPort6000Sysobjid) ||
        data[0].sysobjid.includes(this.nPort6000G2Sysobjid) ||
        data[0].sysobjid.includes(this.nPort5000G2Sysobjid) ||
        data[0].sysobjid.includes(this.mGateMB3000Sysobjid));
    const isExcludeNsm = data.some(
      device => device.sysobjid.includes(this.nat102) || device.sysobjid.includes(this.onCellG4302LTE4Sysobjid)
    );
    const atLeastOneNoIpsSupport = data.map(m => m.sysobjid).some(id => !this.enableDisableIpsOids.includes(id));

    // Check if the selected devices include both cpuSysobjid and switchSysobjid devices
    const hasCpuDevice = data.some(device => device.sysobjid?.includes(this.cpuSysobjid));
    const hasSwitchDevice = data.some(device => device.sysobjid?.includes(this.switchSysobjid));
    const isIopacCpuSwitchMultiSelection = hasCpuDevice && hasSwitchDevice;

    let tempCommandBarKey = '';
    this.clearAllObject();
    this.focusDevice = undefined;
    this.networkService.selectedDevices = data;

    // If both CPU and Switch devices are selected, use the special menu
    if (isIopacCpuSwitchMultiSelection) {
      tempCommandBarKey = 'iopacCpuSwitchMultiSelectedMenu';
    } else if (this.networkService.selectedDevices.length === 2) {
      if (this.isTopologyView) {
        tempCommandBarKey = isNportDevices ? 'twoDevicesSerialPortSelectedMenu' : atLeastOneNoIpsSupport? 'twoDevicesSelectedMenu':'twoIpsDevicesSelectedMenu' ;
        if (isExcludeNsm) tempCommandBarKey = 'twoDevicesNoNsmSelectedMenu';
      } else {
        tempCommandBarKey = isNportDevices ? 'deviceSerialPortTableMultiSelectedMenu' : atLeastOneNoIpsSupport?  'deviceTableMultiSelectedMenu': 'ipsDeviceTableMultiSelectedMenu';
        if (isExcludeNsm) tempCommandBarKey = 'deviceNoNsmTableMultiSelectedMenu';
      }
    } else {
      if (this.isTopologyView) {
        tempCommandBarKey = isNportDevices ? 'deviceSerialPortMultiSelectedMenu' : atLeastOneNoIpsSupport? 'deviceMultiSelectedMenu': 'ipsDeviceMultiSelectedMenu';
        if (isExcludeNsm) tempCommandBarKey = 'deviceNoNsmMultiSelectedMenu';
      } else {
        tempCommandBarKey = isNportDevices ? 'deviceSerialPortTableMultiSelectedMenu' : atLeastOneNoIpsSupport?  'deviceTableMultiSelectedMenu': 'ipsDeviceTableMultiSelectedMenu';
        if (isExcludeNsm) tempCommandBarKey = 'deviceNoNsmTableMultiSelectedMenu';
      }
    }
    this.updateMenuKey(tempCommandBarKey);
  }

  onGroupSelected(data: any): void {
    this.clearAllObject();
    this.showTab = true;
    this.tabName = this.translateService.instant('GROUP_PROPERTIES.title');
    this.group = data;

    this.networkService.selectedGroup = this.group;
    this.commandBarKey = 'groupSelectedMenu';
    this.devicesData = this.dataRepository.getData(DataType.DEVICE);
    this.groupInformationDeviceCount = this.getDevicesOfGroupBySeverityRecursive(
      this.group.site_id,
      this.group.id,
      Severity.EVENT_SEVERITY_INFO
    ).length;
    this.groupWarningDeviceCount = this.getDevicesOfGroupBySeverityRecursive(
      this.group.site_id,
      this.group.id,
      Severity.EVENT_SEVERITY_WARNING
    ).length;
    this.groupCriticalDeviceCount = this.getDevicesOfGroupBySeverityRecursive(
      this.group.site_id,
      this.group.id,
      Severity.EVENT_SEVERITY_CRITICAL
    ).length;

    if (data.device_group) {
      this.selectedIopacModule = 0;
      this.onSelectionChange();

      this.isShowDeviceComponents = true;
      this.tabName = this.translateService.instant('DEVICE_PROPERTIES.title');
      this.getIopacDevicesOfGroupById(data.site_id, data.id);
      this.menuData = {
        ...this.menuData,
        groupSelectedMenu: this.auth.menuData['iopacDeviceMenu']
      };
      if (this.topologyViewMode === TopologyViewMode.security) {
        if (this.securityViewProfile === 1) {
          this.securityViewHighColor = this.globalService.hexToRgba(this.securityViewPreferenceData[0].color.high);
          this.securityViewMediumColor = this.globalService.hexToRgba(this.securityViewPreferenceData[0].color.medium);
          this.securityViewBasicColor = this.globalService.hexToRgba(this.securityViewPreferenceData[0].color.basic);
        }
        this.tabIndex = 2;
      }
    } else {
      this.menuData = {
        ...this.menuData,
        groupSelectedMenu: [this.auth.menuData.groupingMenuForIED[1], ...this.auth.menuData.groupSelectedMenu]
      };
    }
  }

  onGroupMultiSelected(data: any): void {
    this.clearAllObject();
    this.networkService.selectedGroups = data;
    this.commandBarKey = 'groupMultiSelectedMenu';
  }

  onLinkSelected(data: any): void {
    this.clearAllObject();
    this.showTab = true;
    this.tabName = this.translateService.instant('LINK_PROPERTIES');
    this.link = data.linkObject;
    this.networkService.selectedLink = this.link;
    this.isVPNLink = this.link.local_vpn_connection_name !== '' || this.link.remote_vpn_connection_name !== '';
    if (this.link.from_port === 0) {
      this.fromIp = data.fromDeviceData.name;
      this.showFromPort = false;
    } else {
      this.fromIp = data.fromDeviceData.ip;
      this.showFromPort = true;
    }
    if (this.link.to_port === 0) {
      this.toIp = data.toDeviceData.name;
      this.showToPort = false;
    } else {
      this.toIp = data.toDeviceData.ip;
      this.showToPort = true;
    }
    // 如果是 DA-820C 的連線，則不顯示 port speed
    const da820CSysObjId = '.*******.4.1.8691.16.1.8204.1.0';
    const hasDa820CDevices =
      data?.fromDeviceData?.sysobjid === da820CSysObjId || data?.toDeviceData?.sysobjid === da820CSysObjId;
    if (this.link.speed && this.link.speed !== 0 && !hasDa820CDevices) {
      // Hide dynamic link speed until backend has this data
      if (data.dynamicWirelessTopology && (this.link.status & linkType.wireless) === linkType.wireless) {
        this.showLinkSpeed = false;
      } else {
        const bigLinkSpeed = new Big(this.link.speed);
        let speedUnit = ' Mb';
        let speed = bigLinkSpeed.div(1000000);
        // Gigabit Ethernet
        if (speed >= 1000) {
          speed = speed.div(1000);
          speedUnit = ' Gb';
        }
        this.linkSpeed = speed + speedUnit;
        this.showLinkSpeed = true;
      }
    } else {
      this.showLinkSpeed = false;
    }
    // Query SFP
    if (this.link.from_port !== 0 && this.link.to_port !== 0) {
      const fromDevice = this.dataRepository.getData(
        DataType.DEVICE,
        this.networkService.selectedSiteId,
        data.fromDeviceData.id
      )[0];
      const toDevice = this.dataRepository.getData(
        DataType.DEVICE,
        this.networkService.selectedSiteId,
        data.toDeviceData.id
      )[0];
      if (fromDevice.device_components.sfpTxPower && toDevice.device_components.sfpTxPower) {
        const fromSfpIndex = _.findIndex(fromDevice.device_components.sfpTxPower, (fromSfp: DeviceComponentsUnit) => {
          if (fromSfp) {
            return +fromSfp.index === this.link.from_port;
          }
        });
        const toSfpIndex = _.findIndex(toDevice.device_components.sfpTxPower, (toSfp: DeviceComponentsUnit) => {
          if (toSfp) {
            return +toSfp.index === this.link.to_port;
          }
        });
        if (fromSfpIndex !== -1 && toSfpIndex !== -1) {
          this.sfpLink = {
            from: data.fromDeviceData.ip,
            fromTx: this.globalService.convertEmptyDash(fromDevice.device_components.sfpTxPower[fromSfpIndex].status),
            fromRx: this.globalService.convertEmptyDash(fromDevice.device_components.sfpRxPower[fromSfpIndex].status),
            fromTemperature: this.globalService.convertEmptyDash(
              fromDevice.device_components.sfpTemperature[fromSfpIndex].status
            ),
            fromVoltage: this.globalService.convertEmptyDash(
              fromDevice.device_components.sfpVoltage[fromSfpIndex].status
            ),
            to: data.toDeviceData.ip,
            toTx: this.globalService.convertEmptyDash(toDevice.device_components.sfpTxPower[toSfpIndex].status),
            toRx: this.globalService.convertEmptyDash(toDevice.device_components.sfpRxPower[toSfpIndex].status),
            toTemperature: this.globalService.convertEmptyDash(
              toDevice.device_components.sfpTemperature[toSfpIndex].status
            ),
            toVoltage: this.globalService.convertEmptyDash(toDevice.device_components.sfpVoltage[toSfpIndex].status)
          };
        }
      } else if (fromDevice.device_components.sfpTxPower) {
        const fromSfpIndex = _.findIndex(fromDevice.device_components.sfpTxPower, (fromSfp: DeviceComponentsUnit) => {
          if (fromSfp) {
            return +fromSfp.index === this.link.from_port;
          }
        });
        if (fromSfpIndex !== -1) {
          this.sfpLink = {
            from: data.fromDeviceData.ip,
            fromTx: this.globalService.convertEmptyDash(fromDevice.device_components.sfpTxPower[fromSfpIndex].status),
            fromRx: this.globalService.convertEmptyDash(fromDevice.device_components.sfpRxPower[fromSfpIndex].status),
            fromTemperature: this.globalService.convertEmptyDash(
              fromDevice.device_components.sfpTemperature[fromSfpIndex].status
            ),
            fromVoltage: this.globalService.convertEmptyDash(
              fromDevice.device_components.sfpVoltage[fromSfpIndex].status
            ),
            to: data.toDeviceData.ip,
            toTx: this.globalService.emptyDash,
            toRx: this.globalService.emptyDash,
            toTemperature: this.globalService.emptyDash,
            toVoltage: this.globalService.emptyDash
          };
        }
      } else if (toDevice.device_components.sfpTxPower) {
        const toSfpIndex = _.findIndex(toDevice.device_components.sfpTxPower, (toSfp: DeviceComponentsUnit) => {
          if (toSfp) {
            return +toSfp.index === this.link.to_port;
          }
        });
        if (toSfpIndex !== -1) {
          this.sfpLink = {
            from: data.fromDeviceData.ip,
            fromTx: this.globalService.emptyDash,
            fromRx: this.globalService.emptyDash,
            fromTemperature: this.globalService.emptyDash,
            fromVoltage: this.globalService.emptyDash,
            to: data.toDeviceData.ip,
            toTx: this.globalService.convertEmptyDash(toDevice.device_components.sfpTxPower[toSfpIndex].status),
            toRx: this.globalService.convertEmptyDash(toDevice.device_components.sfpRxPower[toSfpIndex].status),
            toTemperature: this.globalService.convertEmptyDash(
              toDevice.device_components.sfpTemperature[toSfpIndex].status
            ),
            toVoltage: this.globalService.convertEmptyDash(toDevice.device_components.sfpVoltage[toSfpIndex].status)
          };
        }
      }
      // Set up sfp threshold, only query one time
      if (this.sfpLink) {
        forkJoin([
          from(this.dataService.getAdvanceEventThreshold(this.networkService.selectedSiteId)),
          this.dataService.getSeverityThreshold(this.networkService.selectedSiteId, this.link.id)
        ]).subscribe((value: [AdvanceEventSettingUnit, SeverityThresholdUnit[]]) => {
          this.globalService.setupSfpThreshold(
            value[0].sfp_tx_below_threshold,
            value[0].sfp_rx_below_threshold,
            value[0].sfp_volt_below_threshold,
            value[0].sfp_volt_over_threshold,
            value[0].sfp_temp_threshold,
            value[0].sfp_tx_below_severity,
            value[0].sfp_rx_below_severity,
            value[0].sfp_volt_below_severity,
            value[0].sfp_volt_over_severity,
            value[0].sfp_temp_severity,
            value[1][0].sfp_tx_below_threshold,
            value[1][0].sfp_rx_below_threshold,
            value[1][0].sfp_volt_below_threshold,
            value[1][0].sfp_volt_over_threshold,
            value[1][0].sfp_temp_over_threshold,
            value[1][0].sfp_tx_below_severity,
            value[1][0].sfp_rx_below_severity,
            value[1][0].sfp_volt_below_severity,
            value[1][0].sfp_volt_over_severity,
            value[1][0].sfp_temp_over_severity
          );
        });
      }
    }
    if (data.dynamicWirelessTopology && (this.link.status & linkType.wireless) === linkType.wireless) {
      this.commandBarKey = '';
    } else {
      if (hasDa820CDevices) {
        if (this.sfpLink) {
          this.commandBarKey = 'da820CSfpLinkSelectedMenu';
        } else {
          this.commandBarKey = 'da820CLinkSelectedMenu';
        }
      } else if (this.sfpLink) {
        this.commandBarKey = 'sfpLinkSelectedMenu';
      } else {
        this.commandBarKey = 'linkSelectedMenu';
      }
    }
  }

  onLinkMultiSelected(data: any): void {
    this.clearAllObject();
    this.networkService.selectedLinks = data;
    this.commandBarKey = 'linkMultiSelectedMenu';
  }

  onMultiObjectSelected(data: any): void {
    this.clearAllObject();
    this.networkService.selectedDevices = data.currentSelectedDevices;
    this.networkService.selectedGroups = data.currentSelectedGroups;
    this.networkService.selectedLinks = data.currentSelectedLinks;
    this.commandBarKey = 'multiObjectSelectedMenu';
  }

  onNotSelected(): void {
    this.clearAllObject();
    this.focusDevice = undefined;
    this.notSelectedKey();
    this.toggleSidenav(false);
  }

  clearAllObject(): void {
    this.showGroup = false;
    this.showTab = false;
    this.site = null;
    this.networkService.selectedSite = null;
    this.networkService.selectedSites = null;
    this.group = null;
    this.networkService.selectedGroup = null;
    this.networkService.selectedGroups = null;
    this.device = null;
    this.networkService.selectedDevice = null;
    this.networkService.selectedDevices = null;
    this.link = null;
    this.sfpLink = null;
    this.networkService.selectedLink = null;
    this.networkService.selectedLinks = null;
    this.deviceSecurityResult = null;
    this.deviceSecurityItems = null;
    this.deviceSecurityLevel = null;
    this.currentEvents = [];
    this.networkService.selectedIopacDevice = null;
  }

  onButtonClick(name: string): void {
    let dialogConfig: MatDialogConfig;
    switch (name) {
      case 'exportTopology':
        this.mxTopology.exportTopology();
        break;
      case 'openAddDeviceDialog':
        this.dialog
          .open(AddDeviceComponent, largeDialogConfig)
          .afterClosed()
          .subscribe(result => {
            if (result === 'submit') {
              this.checkScd();
            }
          });
        break;
      case 'openAddLink':
        const addLinkDialogRef = this.dialog.open(AddLinkComponent, mediumDialogConfig);
        addLinkDialogRef.afterClosed().subscribe(result => {
          if (result === 'success') {
            this.mxTopology.redrawTopology();
            this.checkScd();
          }
        });
        break;
      case 'openAssignModelDialog':
        dialogConfig = {
          width: '560px'
        };
        this.dialog.open(AssignModelComponent, dialogConfig);
        break;
      case 'openAutoLayout':
        smallDialogConfig.data = this.mxTopology;
        this.dialog.open(AutoLayoutComponent, smallDialogConfig);
        break;
      case 'openAutoTopologyDialog':
        dialogConfig = {
          width: '560px',
          disableClose: true
        };
        const autoTopologyDialogRef = this.dialog.open(AutoTopologyComponent, dialogConfig);
        autoTopologyDialogRef.afterClosed().subscribe(result => {
          if (result === 'success') {
            this.mxTopology.redrawTopology();
          }
        });
        break;
      case 'openChangeGroup':
        dialogConfig = {
          width: '300px'
        };
        const changeGroupDialogRef = this.dialog.open(ChangeGroupComponent, {
          ...dialogConfig,
          data: {
            mode: !this.networkService.selectedGroup?.device_group ? 'complete' : 'simple',
            groupId: this.networkService.selectedGroup?.device_group ? this.networkService.selectedGroup?.id : null
          }
        });
        changeGroupDialogRef.afterClosed().subscribe(result => {
          if (result !== undefined && result === 'success' && this.isTopologyView) {
            this.mxSiteNav.detectDataChange(false);
            this.mxTopology.redrawTopology();
          }
        });
        break;
      case 'openCreateGroup':
        this.dialog.open(CreateGroupComponent, mediumDialogConfig);
        break;
      case 'openDeleteDeviceDialog':
        this.dialog
          .open(DeleteDeviceComponent, smallDialogConfig)
          .afterClosed()
          .subscribe(result => {
            if (result === 'submit') {
              if (this.isTopologyView) {
                // Redraw PRP/HSR after remove device
                this.mxTopology.redrawPrpHsr();
              }
              this.checkScd();
            }
          });
        break;
      case 'openDeleteGroupDialog':
        if (this.networkService.selectedIopacDevice) {
          this.networkService.selectedDevice = this.networkService.selectedIopacDevice;
          this.dialog
            .open(DeleteDeviceComponent, smallDialogConfig)
            .afterClosed()
            .subscribe(result => {
              if (result === 'submit') {
                if (this.isTopologyView) {
                  // Redraw PRP/HSR after remove device
                  this.mxTopology.redrawPrpHsr();
                }
                this.checkScd();
                this.selectedIopacModule = 0;
                this.group = null;
                this.networkService.selectedIopacDevice = null;
              }
            });
        } else {
          this.dialog.open(DeleteGroupComponent, smallDialogConfig);
        }
        break;
      case 'openDeleteLinkDialog':
        const deleteLinkDialogRef = this.dialog.open(DeleteLinkComponent, smallDialogConfig);
        deleteLinkDialogRef.afterClosed().subscribe(result => {
          if (result === 'submit') {
            this.commandBarKey = 'notSelectedMenu';
            this.toggleSidenav(false);
            this.clearAllObject();
            this.checkScd();
          }
        });
        break;
      case 'openDeleteObjectDialog':
        this.dialog.open(DeleteObjectComponent, smallDialogConfig);
        break;
      case 'openDeleteSiteDialog':
        this.dialog.open(DeleteSiteComponent, smallDialogConfig);
        break;
      case 'openDeviceBasicInfoDialog':
        this.dialog.open(DeviceBasicInfoComponent, mediumDialogConfig);
        break;
      case 'openDeviceIPConfig':
        this.dialog.open(DeviceIPConfigComponent, mediumDialogConfig);
        break;
      case 'openDeviceLocator':
        this.dialog.open(DeviceLocatorComponent, smallDialogConfig);
        break;
      case 'openExportConfig':
        this.dialog.open(ExportConfigComponent, smallDialogConfig);
        break;
      case 'openImportConfig':
        this.dialog.open(ImportConfigComponent, smallDialogConfig);
        break;
      case 'openMaintainGroup':
        this.dialog.open(MaintainGroupComponent, largeDialogConfig);
        break;
      case 'openPacketErrorRate':
        extraLargeDialogConfig.data = 2;
        this.dialog.open(LinkTrafficComponent, extraLargeDialogConfig);
        break;
      case 'openPingDevice':
        this.dialog.open(PingDeviceComponent, mediumDialogConfig);
        break;
      case 'openPortSettingDialog':
        this.dialog.open(PortSettingComponent, mediumDialogConfig);
        break;
      case 'openPortTraffic':
        extraLargeDialogConfig.data = 1;
        this.dialog.open(LinkTrafficComponent, extraLargeDialogConfig);
        break;
      case 'openSetBackground':
        extraLargeDialogConfig.data = {
          topologyData: this.mxTopology,
          view: 'network'
        };
        this.dialog.open(SetBackgroundDialogComponent, extraLargeDialogConfig);
        break;
      case 'openSetDocument':
        this.dialog.open(SetDocumentComponent, smallDialogConfig);
        break;
      case 'openSetPortLabel':
        const data = {
          isVPNLink: this.isVPNLink,
          fromIp: this.fromIp,
          showFromPort: this.showFromPort,
          toIp: this.toIp,
          showToPort: this.showToPort
        };
        if (this.isVPNLink) {
          smallDialogConfig.data = data;
          this.dialog.open(SetPortLabelComponent, smallDialogConfig);
        } else {
          mediumDialogConfig.data = data;
          this.dialog.open(SetPortLabelComponent, mediumDialogConfig);
        }
        break;
      case 'openSeverityThreshold':
        mediumDialogConfig.data = this.sfpLink;
        this.dialog.open(SeverityThresholdComponent, mediumDialogConfig);
        break;
      case 'openSiteManagement':
        mediumDialogConfig.data = this.networkService.selectedSite;
        this.dialog.open(SiteManagementComponent, mediumDialogConfig);
        break;
      case 'openUpgradeFirmwareDialog':
        this.dialog.open(UpgradeFirmwareComponent, smallDialogConfig);
        break;
      case 'openIpsConfigurationDialog':
        this.dialog.open(IpsConfigurationDialogComponent, {
          ...extraLargeDialogConfig,
          data: this.networkService.selectedDevice
            ? [this.networkService.selectedDevice]
            : this.networkService.selectedDevices
        });
        break;
      case 'openWebConsole':
        this.globalService.openWebConsole(this.networkService.selectedSiteId, this.networkService.selectedDevice.ip);
        break;
      case 'openCliInterface':
        this.dialog.open(ExecuteCliObjectDialogComponent, {
          ...extraLargeDialogConfig,
          data: {
            dialogAction: 'openCliInterface',
            devices: this.networkService.selectedDevice
              ? [this.networkService.selectedDevice]
              : this.networkService.selectedDevices
          }
        });
        break;
      case 'openExecuteCliObject':
        this.dialog.open(ExecuteCliObjectDialogComponent, {
          ...extraLargeDialogConfig,
          data: {
            dialogAction: 'openExecuteCliObject',
            devices: this.networkService.selectedDevice
              ? [this.networkService.selectedDevice]
              : this.networkService.selectedDevices
          }
        });
        break;
      case 'refreshDevice':
        this.networkService.refreshDevice().then(() => {
          if (this.device) {
            this.onRefreshDeviceData(
              this.dataRepository.getData(DataType.DEVICE, this.device.site_id, this.device.id)[0]
            );
          }
        });
        break;
      case 'showTrafficView':
        this.topologyViewMode = TopologyViewMode.traffic;
        this.tabIndex = 0;
        this.showGroup = true;
        this.isShowGroupButton = true;
        this.refreshTrafficView();
        this.isTopologyView = true;
        this.isTopologyTableView = false;
        this.isWirelessTableView = false;
        this.isWirelessTable = false;
        this.isShowModeButton = true;
        break;
      case 'showSecurityView':
        this.topologyViewMode = TopologyViewMode.security;
        this.tabIndex = 2;
        this.isShowGroupButton = false;
        this.isTopologyView = true;
        this.isTopologyTableView = false;
        this.isWirelessTableView = false;
        this.isWirelessTable = false;
        this.isShowModeButton = true;
        break;
      case 'showVlanView':
        this.topologyViewMode = TopologyViewMode.vlan;
        this.tabIndex = 1;
        this.isShowGroupButton = false;
        this.isTopologyView = true;
        this.isTopologyTableView = false;
        this.isWirelessTableView = false;
        this.isWirelessTable = false;
        this.isShowModeButton = true;
        break;
      case 'showWirelessTableView':
        this.commandBarKey = 'wirelessTableView';
        this.isShowGroupButton = false;
        this.isTopologyView = false;
        this.isTopologyTableView = false;
        this.isWirelessTableView = true;
        this.isWirelessTable = false;
        this.isShowModeButton = true;
        break;
      case 'showWirelessTable':
        this.showWirelessTable();
        break;
      case 'showWirelessPlaybackView':
        this.router.navigate(['/pages/wireless-playback']);
        break;
      case 'openDeviceTrapConfig':
        this.dialog.open(DeviceTrapServerConfigComponent, smallDialogConfig);
        break;
      case 'openSNMPSetting':
        this.dialog.open(SnmpSettingComponent, mediumDialogConfig);
        break;
      case 'openPollingSetting':
        this.dialog.open(DevicePollingSettingComponent, mediumDialogConfig);
        break;
      case 'openDeviceLoginAccount':
        mediumDialogConfig.data = { canNotModifyUsername: this.canNotModifyUsername() };
        this.dialog.open(DeviceLoginAccountComponent, mediumDialogConfig);
        break;
      case 'openModifyDeviceAlias':
        this.dialog.open(ModifyDeviceAliasComponent, mediumDialogConfig);
        break;
      case 'openDevicePollingIpSetting':
        this.dialog.open(PollingIpComponent, smallDialogConfig);
        break;
      case 'openIpSecTable':
        const deviceData = this.networkService.selectedDevice;
        dialogConfig = {
          width: '1024px',
          data: deviceData
        };
        this.dialog.open(IpSecTableDialogComponent, dialogConfig);
        break;
      case 'openScanRange':
        this.router.navigate(['/pages/scan-range']);
        break;
      case 'alignmentTop':
        this.mxTopology.alignmentTop();
        break;
      case 'alignmentBottom':
        this.mxTopology.alignmentBottom();
        break;
      case 'alignmentLeft':
        this.mxTopology.alignmentLeft();
        break;
      case 'alignmentRight':
        this.mxTopology.alignmentRight();
        break;
      case 'openChangeDeviceIcon':
        const changeDeviceIconDialogRef = this.dialog.open(ChangeDeviceIconDialogComponent, mediumDialogConfig);
        changeDeviceIconDialogRef.afterClosed().subscribe(result => {
          this.clearAllObject();
          this.mxTopology.unselectAllObjects();
          this.commandBarKey = 'notSelectedMenu';
        });
        break;
      case 'openSfpListDialog':
        this.dialog.open(SfpListComponent, extraLargeDialogConfig);
        break;
      case 'openSfpSyncDialog':
        const links = this.getSFPLinks();
        mediumDialogConfig.data = links;
        this.dialog.open(SfpSyncComponent, mediumDialogConfig);
        break;
      case 'openWirelessDeviceDashboard':
        this.isWirelessDashboardDataReady(this.device).subscribe(value => {
          //ANCHOR - open device dashboard
          if (value) {
            this.devicesData = this.dataRepository.getData(DataType.DEVICE);
            extraLargeDialogConfig.data = {
              devicesData: this.devicesData,
              selectedDevice: this.device
            };
            this.dialog.open(WirelessDeviceDashboardComponent, extraLargeDialogConfig);
          } else {
            smallDialogConfig.data = {};
            this.dialog.open(DataNotReadyComponent, smallDialogConfig);
          }
        });
        break;
      case 'openComputerDeviceDashboard':
        // Check if computer dashboard data is ready
        if (
          this.device.device_components &&
          this.device.device_components.cpuLoading &&
          this.device.device_components.memoryUtilization &&
          this.device.device_components.partitionUsage
        ) {
          this.dialog.open(ComputerDeviceDashboardComponent, extraLargeDialogConfig);
        } else {
          smallDialogConfig.data = {};
          this.dialog.open(DataNotReadyComponent, smallDialogConfig);
        }
        break;
      case 'openSfpInfo':
        extraLargeDialogConfig.data = this.link;
        this.dialog.open(SfpInfoComponent, extraLargeDialogConfig);
        break;
      case 'closeWirelessTableView':
        if (this.networkService.selectedSiteId === '-1') {
          this.commandBarKey = 'siteNotSelectedMenu';
        } else {
          this.commandBarKey = 'notSelectedMenu';
        }
        this.isTopologyView = true;
        this.isWirelessTableView = false;
        this.isWirelessTable = false;
        this.isShowModeButton = true;
        this.isShowInfoButton = true;

        this.resetToRootGroup();
        break;
      case 'openTagDeviceDialog':
        const openTagDeviceDialogRef = this.dialog.open(TagDeviceComponent, smallDialogConfig);
        openTagDeviceDialogRef.afterClosed().subscribe(result => {
          if (result === 'submit') {
            this.mxTopology.redrawTopology();
          }
        });
        break;
      case 'openTagLinkDialog':
        this.dialog.open(TagLinkComponent, smallDialogConfig);
        break;
      case 'openImportScd':
        mediumDialogConfig.data = null;
        const openImportScdRef = this.dialog.open(ImportScdComponent, mediumDialogConfig);
        openImportScdRef.afterClosed().subscribe(result => {
          if (result === 'submit') {
            this.mxTopology.redrawTopology();
          }
        });
        break;
      case 'handleIedUngrouping':
        this.dataService
          .handleIedUngrouping({
            siteId: this.networkService.selectedGroup.site_id,
            groupId: this.networkService.selectedGroup.id
          })
          .pipe(
            tap(() => {
              const ungroupDevice = this.devicesData.filter(d => d.group === this.networkService.selectedGroup.id);

              this.mxTopology.redrawTopology({
                beTriggered: 'ungroup',
                state: 'redraw',
                data: { ungroupDevice }
              });
            })
          )
          .subscribe();
        break;
      case 'handleIedGrouping':
        this.dataService
          .handleIedGrouping({
            siteId: this.networkService.selectedDevices[0].site_id,
            deviceIds: [...this.networkService.selectedDevices.map(d => d.id)]
          })
          .pipe(
            tap(() => {
              this.onLoading(true);
              this.mxTopology.redrawTopology();
            })
          )
          .subscribe();
        break;
      case 'handleRefreshDevices':
        this.handleRefreshDevices(this.networkService.selectedDevices);
        break;
      case ActionMetaType.CHANGE_WIFI_CHANNEL:
      case ActionMetaType.ADD_WIFI_SSID:
      case ActionMetaType.DYNAMIC_MAC_STICKY:
      case ActionMetaType.REBOOT:
      case ActionMetaType.CREATE_SNAPSHOT:
      case ActionMetaType.RESTORE_TO_CREATE_SNAPSHOT:
      case ActionMetaType.MAC_STICKY_SWITCH:
      case ActionMetaType.RELEARN_MAC_STICKY:
      case ActionMetaType.DISABLE_UNUSED_PORT:
      case ActionMetaType.DISABLE_UNSECURED_CONSOLE:
        this.dialog.open(DeviceActionDialogComponent, {
          ...extraLargeDialogConfig,
          data: {
            dialogAction: name,
            devices: this.networkService.selectedDevice
              ? [this.networkService.selectedDevice]
              : this.networkService.selectedDevices,
            allDeviceList: (this.devicesData = this.dataRepository.getData(DataType.DEVICE))
          }
        });
        break;
      case 'openSerialPortMonitoring':
        this.dialog.open(SerialPortMonitoringComponent, { ...largeDialogConfig, width: '1000px' });
        break;
      case ActionMetaType.POLICY_PROFILE_DEPLOYMENT:
        this.router.navigate(['/pages/firewall-policy-management/policy-profile-deployment'], {
          queryParams: {
            mac: this.networkService.selectedDevice
              ? this.networkService.selectedDevice.mac
              : this.networkService.selectedDevices.map(d => d.mac).join('&')
          }
        });
        break;
      case ActionMetaType.SECURITY_PACKAGE_DEPLOYMENT:
        this.router.navigate(['/pages/firewall-policy-management/security-package-deployment'], {
          queryParams: {
            mac: this.networkService.selectedDevice
              ? this.networkService.selectedDevice.mac
              : this.networkService.selectedDevices.map(d => d.mac).join('&')
          }
        });
        break;
    }
    this.cdr.detectChanges();
  }

  handleRefreshDevices(selectedDevices: DeviceUnit[]) {
    selectedDevices.forEach(device => {
      this.networkService.refreshDevice(device).then(() => {
        this.onRefreshDeviceData(this.dataRepository.getData(DataType.DEVICE, device.site_id, device.id)[0]);
      });
    });
  }

  onToggleMode(topologyView: boolean): void {
    if (this.isWirelessTableView && topologyView === false) {
      return;
    } else {
      this.isWirelessTableView = false;
    }
    this.isTopologyView = topologyView;
    this.closeView();
    this.closeSideNav();
    this.notSelectedKey();
    if (this.isTopologyView) {
      this.deviceSeverity = undefined;
      this.isShowGroupButton = false;
      this.isTopologyView = true;
      this.isTopologyTableView = false;
      this.isWirelessTableView = false;
      this.isShowModeButton = true;

      // Reset to root group when switching to topology view
      this.resetToRootGroup();
    } else {
      this.commandBarKey = 'notSelectedTableMenu';
      this.isShowGroupButton = false;
      this.isTopologyView = false;
      this.isTopologyTableView = true;
      this.isWirelessTableView = false;
      this.isShowModeButton = true;
    }
  }

  onToggleGroupBar(isSideBarOpen: boolean): void {
    this.showGroup = true;
    this.toggleSidenav(isSideBarOpen);
    this.cdr.detectChanges();
  }

  onToggleAutomationButton(): void {
    this.showAutomationButton = !this.showAutomationButton;
  }

  onToggleSideBar(isSideBarOpen: boolean): void {
    this.showGroup = false;
    if (isSideBarOpen) {
      // Update the license node number if users delete devices.
      this.networkService.getLicenseStatus(this.networkService.selectedSiteId);
      this.toggleSidenav(true);
    } else {
      this.toggleSidenav(false);
    }
    this.cdr.detectChanges();
  }

  toggleSidenav(open): void {
    if (open) {
      this.matSidenav.open();
      this.hasSideNav = true;
    } else {
      this.matSidenav.close();
      this.hasSideNav = false;
    }
  }

  onNavChange(data: { siteId: string; groupId: number }): void {
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    if (this.isWirelessTable) {
      this.networkService.selectedSiteId = data.siteId;
      this.networkService.selectedGroupId = +data.groupId;
    } else {
      this.focusDevice = undefined;
      this.notSelectedKey();
      // Close side nav
      this.closeSideNav();
      // When user change site, regenerate menu again
      this.onUpdateMenu({ siteId: data.siteId, groupId: data.groupId });
    }
  }

  onUpdateMenu(value: { siteId: string; groupId: number }): void {
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    const username = localStorage.getItem('loginUser');
    this.auth.generateUiProfile(username).subscribe(
      menuData => {
        // Assign menuData to mx-command-bar
        this.menuData = menuData;
        // Assign para to mx-topology will redraw topology and set loading to false
        this.setTopologyPara(value.siteId, value.groupId);
        // Set network page parameters
        if (value.siteId === '-1' || this.networkService.selectedSiteId === '-1') {
          this.clearAllObject();
          this.commandBarKey = 'siteNotSelectedMenu';
        } else {
          this.networkService.getGlobalDeviceAccessParameters();
          this.networkService.getLicenseStatus(this.networkService.selectedSiteId);
        }
      },
      (error: unknown) => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
      }
    );
  }

  setTopologyPara(siteId: string, groupId: number): void {
    this.isWirelessAddon = _.includes(this.globalService.siteInfo.now.addons, 'wireless');
    this.networkService.selectedSiteId = siteId ? siteId : localStorage.getItem('siteId');
    this.networkService.selectedGroupId = groupId ? groupId : +localStorage.getItem('groupId');
    if (_.includes(this.globalService.siteInfo.now.addons, 'grid')) {
      this.topologyViewMode = TopologyViewMode.grid;
    } else {
      this.topologyViewMode = TopologyViewMode.none;
    }
  }

  closeSideNav(): void {
    this.matSidenav.close();
    if (this.mxCommandBar) {
      this.mxCommandBar.closeSide();
    }
  }

  onTrafficViewClosed(): void {
    this.closeView();
    this.closeSideNav();
  }

  onSecurityViewClosed(): void {
    this.closeView();
  }

  onVlanViewClosed(): void {
    this.closeView();
  }

  closeView(): void {
    if (_.includes(this.globalService.siteInfo.now.addons, 'grid')) {
      this.topologyViewMode = TopologyViewMode.grid;
    } else {
      this.topologyViewMode = TopologyViewMode.none;
    }
    this.tabIndex = 0;
    this.isShowGroupButton = false;
  }

  onMinimizeEventTable(data: boolean): void {
    this.isFullNetworkMode = data;
    this.resizeRecentEvent();
  }

  onFocusEventDevice(device: DeviceUnit): void {
    this.mxTopology.focusDevice(device);
  }

  onFocusEventIopacModule(device: DeviceUnit): void {
    this.mxTopology.focusGroup(device);
  }

  focusLink(link: LinkUnit): void {
    this.mxTopology.focusLink(link);
  }

  getSecurityEnabledValue = (securityProfile, profile, item) => {
    const profileIndex = securityProfile.findIndex(o => o.name === profile);
    if (profileIndex !== -1) {
      const checkItemIndex = securityProfile[profileIndex].checkItems.findIndex(o => o.item === item);
      return checkItemIndex !== -1 ? securityProfile[profileIndex].checkItems[checkItemIndex].enabled : false;
    }
    return false;
  };

  buildSecurityItems(securityItems, securityProfile): void {
    this.deviceSecurityItems =
      securityItems?.map(securityItem => {
        const securityItemString = this.getSecurityItemString(securityItem.item, securityItem.result);
        const baseline = this.getSecurityEnabledValue(securityProfile, securityViewLevelText.basic, securityItem.item);
        const l1 = this.getSecurityEnabledValue(securityProfile, securityViewLevelText.medium, securityItem.item);
        const l2 = this.getSecurityEnabledValue(securityProfile, securityViewLevelText.high, securityItem.item);
        return {
          status: securityItem.result,
          item: securityItemString.item,
          result: securityItemString.result,
          baseline,
          l1,
          l2
        };
      }) ?? [];
    this.deviceSecurityItems = _.sortBy(this.deviceSecurityItems, ['l2', 'l1', 'baseline']);
    this.deviceSecurityItems = _.reverse(this.deviceSecurityItems);
  }

  portInfoToFullArray(device, infoLabel: string): void {
    if (!device.device_components) return;

    if (device.device_components[infoLabel] && device.device_components['ifDescr']) {
      if (device.device_components[infoLabel].length !== device.device_components['ifDescr'].length) {
        const compareArray = _.cloneDeep(device.device_components[infoLabel]);
        if (device.device_components.sfpPort) {
          for (let i = 0; i < device.device_components['ifDescr'].length; i++) {
            // let count = 0;
            for (let j = 0; j < compareArray.length; j++) {
              if (compareArray[j] && compareArray[j].index) {
                if (+compareArray[j].index === +device.device_components['ifDescr'][i].index) {
                  break;
                } else {
                  if (j + 1 === compareArray.length) {
                    device.device_components[infoLabel].splice(i, 0, null);
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  displayedDeviceComponentsToArray(device): string[] {
    const allProperties = _.keys(device.device_components);
    this.portInfoToFullArray(device, 'sfpTemperature');
    this.portInfoToFullArray(device, 'sfpVoltage');
    this.portInfoToFullArray(device, 'sfpTxPower');
    this.portInfoToFullArray(device, 'sfpRxPower');
    this.portInfoToFullArray(device, 'sfpModel');
    this.portInfoToFullArray(device, 'sfpSN');

    // Add property which is list on template
    // 以下這些不會顯示在 Other Device Properties 中
    let properties = _.difference(allProperties, [
      'Availability',
      'sysDescr',
      'sysObjectId',
      'sysContact',
      'sysName',
      'sysLocation',
      'serialNumber.0',
      'cpuLoading5s',
      'cpuLoading30s',
      'cpuLoading300s',
      'cpuLoading5s.0',
      'cpuLoading30s.0',
      'cpuLoading300s.0',
      'memoryUsage.0',
      'powerConsumption.0',
      'numberOfPorts',
      'ifOperStatus',
      'portSpeed',
      'ifSpeed',
      'activeProtocolOfRedundancy',
      'macAddress',
      'ipAdEntAddr',
      'modelName',
      'firmwareVersion',
      'power1InputStatus',
      'power2InputStatus',
      'enableAutoIpConfig',
      'IpAddr',
      'IpMask',
      'defaultGateway',
      'dnsServer1IpAddr',
      'dnsServer2IpAddr',
      'httpEnable.0',
      'httpPort.0',
      'httpsEnable.0',
      'httpsPort.0',
      'telnetEnable.0',
      'telnetPort.0',
      'sshEnable.0',
      'sshPort.0',
      'profinetEnable.0',
      'snmpReadCommunity',
      'snmpTrapCommunity',
      'snmpTrap2Community',
      'trapServerAddr',
      'trap2ServerAddr',
      'snmpInformEnable',
      'snmpInformRetries',
      'snmpInformTimeout',
      'ifNumber',
      'Alias',
      'ModelName',
      'MAC Address',
      'ifDescr',
      'ifType',
      'portDesc',
      'portEnable',
      'portName',
      'portSubdesc',
      'poePortLegacyPdDetect',
      'poePortLegacyPdDetect',
      'poePowerOutputMode',
      'poePortConsumption',
      'poePortVoltage',
      'poePortCurrent',
      'poePortClass',
      'poePortIndex',
      'ipsecStatusName',
      'ipsecStatusLocSubnet',
      'ipsecStatusLocGateway',
      'ipsecStatusRemGateway',
      'ipsecStatusRemSubnet',
      'ipsecStatusPhase1',
      'ipsecStatusPhase2',
      'ipsecl2tp',
      'monitorCurrentMode',
      'monitorDownStreamRate',
      'monitorSNR',
      'monitorUpStreamRate',
      'Wlan Client MAC',
      'Wlan Client IP',
      'Wlan Client RSSI(dBm)',
      'Wlan Client RSSI(value)',
      'Connection Time',
      'sfpPort',
      'sfpTemperature',
      'sfpVoltage',
      'sfpTxPower',
      'sfpRxPower',
      'sfpModel',
      'sfpSN',
      'wirelessStatusNoiseLevel',
      'wirelessStatusRSSI',
      'wirelessStatusSNR-A',
      'wirelessStatusSNR-B',
      'wlanBSSID',
      'wlanChannel',
      'wlanSignal',
      'wlanTxRate',
      'vapAuthType',
      'vapMgmtEncryption',
      'vapWpaEncrypt',
      'devOperationMode',
      'devRfType',
      'devTxPowerdBm',
      'wlanConnectionTime',
      'model',
      'revision',
      'vendor',
      'sfpRxPowerWarn',
      'sfpTempWarn',
      'sfpTxPowerWarn',
      'iec624393Protocol',
      'sysUpTime',
      'usnmpTrapCommunity',
      'usnmpTrap2Community',
      'utrapServerAddr',
      'utrap2ServerAddr',
      'imageVersion',
      'biosVersion',
      'osType',
      'cpuLoading1m',
      'cpuLoading5m',
      'cpuLoading15m',
      'memoryUtilization',
      'partitionUsage',
      'ioPac6500Loc'
    ]);
    // linux platform
    properties = _.difference(properties, [
      'cpuLoading',
      'memoryUtilization',
      'rstpEnabled',
      'trv2Enabled',
      'tcEnabled',
      'dhEnabled',
      'serialNumber'
    ]);
    return properties;
  }

  hasOneOfProperties(device, properties): boolean {
    const allProperties = _.keys(device.device_components);
    if (_.intersection(properties, allProperties).length > 0) {
      return true;
    } else {
      return false;
    }
  }

  getCurrentEvents(device: DeviceUnit): Promise<any[]> {
    if (device !== undefined) {
      return new Promise((resolve, reject) => {
        this.dataService.getCurrentEventOfDevice(device.site_id, device.ip).subscribe(
          events => {
            resolve(events);
          },
          (error: unknown) => {
            reject(error);
          }
        );
      });
    } else {
      return Promise.reject('device is undefined');
    }
  }

  refreshTrafficView(trafficData?: TrafficData): void {
    const siteId = localStorage.getItem('siteId');
    if (trafficData) {
      // Receive ouput from mx-topology
      this.updateTrafficViewGroups(trafficData);
    } else {
      Promise.all([
        this.dataService.getGroup(siteId),
        this.dataService.getLink(siteId),
        this.dataService.getRealtimeTraffic(siteId, false),
        this.dataService.getTrafficPreference(siteId)
      ])
        .then(data => {
          const trafficViewData: TrafficData = {
            groups: data[0],
            links: data[1],
            realtimeTraffic: data[2],
            trafficPreference: data[3]
          };
          this.updateTrafficViewGroups(trafficViewData);
        })
        .catch(error => {
          console.log('Get data error = ' + error);
        });
    }
  }

  updateTrafficViewGroups(trafficViewData: TrafficData): void {
    this.groupTrafficArr = [];
    for (const group of trafficViewData.groups) {
      const links = _.filter(trafficViewData.links, (o: LinkUnit) => {
        return o.group === group.id;
      });
      const realtimeTraffic = _.intersectionBy(trafficViewData.realtimeTraffic, links, 'id');
      const maxInput = _.maxBy(realtimeTraffic, 'input');
      const maxOutput = _.maxBy(realtimeTraffic, 'output');
      if (!maxInput && !maxOutput) {
        continue;
      }
      let direction: 'from' | 'to';
      let utilization = 0;
      let maxRealtimeTraffic;
      if (maxInput.input > maxOutput.output) {
        if (maxInput.source === 1) {
          utilization = +(maxInput.input / 10000).toFixed(1);
          direction = 'to';
        } else if (maxInput.source === 0) {
          utilization = +(maxInput.input / 10000).toFixed(1);
          direction = 'from';
        }
        maxRealtimeTraffic = maxInput;
      } else {
        if (maxOutput.source === 1) {
          utilization = +(maxOutput.output / 10000).toFixed(1);
          direction = 'from';
        } else if (maxOutput.source === 0) {
          utilization = +(maxOutput.output / 10000).toFixed(1);
          direction = 'to';
        }
        maxRealtimeTraffic = maxOutput;
      }
      // get background color
      let trafficBorderLeftColor: string;
      let trafficBackgroundColor: string;
      const trafficColor: TrafficColorUnit[] = trafficViewData.trafficPreference[0].traffic_loads;
      for (let i = 0; i < trafficColor.length; i++) {
        if (i === 0 && utilization >= 0 && utilization < trafficColor[0].to) {
          trafficBorderLeftColor = this.globalService.hexToRgba(trafficColor[0].color);
          trafficBackgroundColor = this.globalService.hexToRgba(trafficColor[0].color, 0.1);
        } else if (i === 1 && utilization > trafficColor[0].to && utilization <= trafficColor[1].to) {
          trafficBorderLeftColor = this.globalService.hexToRgba(trafficColor[1].color);
          trafficBackgroundColor = this.globalService.hexToRgba(trafficColor[1].color, 0.1);
        } else if (i === 2 && utilization > trafficColor[1].to && utilization <= trafficColor[2].to) {
          trafficBorderLeftColor = this.globalService.hexToRgba(trafficColor[2].color);
          trafficBackgroundColor = this.globalService.hexToRgba(trafficColor[2].color, 0.1);
        } else if (i === 3 && utilization > trafficColor[2].to && utilization <= trafficColor[3].to) {
          trafficBorderLeftColor = this.globalService.hexToRgba(trafficColor[3].color);
          trafficBackgroundColor = this.globalService.hexToRgba(trafficColor[3].color, 0.1);
        } else if (i === 4 && utilization > trafficColor[3].to && utilization <= trafficColor[4].to) {
          trafficBorderLeftColor = this.globalService.hexToRgba(trafficColor[4].color);
          trafficBackgroundColor = this.globalService.hexToRgba(trafficColor[4].color, 0.1);
        }
      }
      const maxRealtimeTrafficLink = trafficViewData.links.find(item => {
        return item.id === maxRealtimeTraffic.id;
      });
      const groupTraffic: GroupTraffic = {
        id: maxRealtimeTraffic.id,
        link: maxRealtimeTrafficLink,
        groupName: group.name,
        maxUtilization: utilization,
        utilizationDirection: direction,
        color: trafficBorderLeftColor,
        style: { borderLeft: '6px solid ' + trafficBorderLeftColor, backgroundColor: trafficBackgroundColor }
      };
      this.groupTrafficArr.push(groupTraffic);
    }
    this.groupTrafficArr = this.groupTrafficArr.sort((a, b) => {
      return a.maxUtilization < b.maxUtilization ? 1 : -1;
    });
  }

  onLoading(show): void {
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show }));
    if (!show) {
      this.mxSiteNav.generateSiteNav();
    }
  }

  notSelectedKey(): void {
    if (!this.isTopologyView) {
      // If there is wirelessTableOperationMode, do not change commandBarKey
      if (!this.wirelessTableOperationMode) {
        this.commandBarKey = 'notSelectedTableMenu';
      }
    } else {
      this.commandBarKey = 'notSelectedMenu';
    }
  }

  onSecurityViewDataComplete(event): void {
    this.securityViewData = event.securityViewData;
    this.securityViewPreferenceData = event.securityViewPreferenceData;
    this.securityViewProfile = event.securityViewProfile;
    this.securityViewProfilesData = event.securityViewProfilesData;
  }

  generateSecurityViewData(): void {
    const defaultTitle = [
      this.translateService.instant('INVENTORY_REPORT.ip_address'),
      this.translateService.instant('DASHBOARD.security_level')
    ];
    let switchTitle;
    let awkTitle;
    let mgateTitle;
    let nportTitle;
    let nport5000Title;
    if (this.securityViewPreferenceData[0].profile === 0) {
      // Generate security view title array when profile is user defined
      const securityViewDataResult = [...this.globalService.sortIp(this.securityViewData[0][0].result)];
      _.forEach(securityViewDataResult, result => {
        if (result.series === 'SWITCH' && result.security_items && result.security_items.length > 0) {
          const titleArr = [];
          _.forEach(result.security_items, securityItem => {
            titleArr.push(this.getSecurityItemString(securityItem.item, securityItem.result).item);
          });
          switchTitle = defaultTitle.concat(titleArr);
        } else if (result.series === 'AWK_SERIES' && result.security_items && result.security_items.length > 0) {
          const titleArr = [];
          _.forEach(result.security_items, securityItem => {
            titleArr.push(this.getSecurityItemString(securityItem.item, securityItem.result).item);
          });
          awkTitle = defaultTitle.concat(titleArr);
        } else if (result.series === 'MGATE' && result.security_items && result.security_items.length > 0) {
          const titleArr = [];
          _.forEach(result.security_items, securityItem => {
            titleArr.push(this.getSecurityItemString(securityItem.item, securityItem.result).item);
          });
          mgateTitle = defaultTitle.concat(titleArr);
        } else if (result.series === 'NPORT' && result.security_items && result.security_items.length > 0) {
          const titleArr = [];
          _.forEach(result.security_items, securityItem => {
            titleArr.push(this.getSecurityItemString(securityItem.item, securityItem.result).item);
          });
          nportTitle = defaultTitle.concat(titleArr);
        } else if (result.series === 'NPORT5000' && result.security_items && result.security_items.length > 0) {
          const titleArr = [];
          _.forEach(result.security_items, securityItem => {
            titleArr.push(this.getSecurityItemString(securityItem.item, securityItem.result).item);
          });
          nport5000Title = defaultTitle.concat(titleArr);
        }
      });
    } else {
      switchTitle = defaultTitle.concat([
        this.translateService.instant('SECURITY_ITEM.AUTO_LOGOUT'),
        this.translateService.instant('SECURITY_ITEM.LOGIN_NOTIFICATION'),
        this.translateService.instant('SECURITY_ITEM.ENCRYPT_CONSOLE'),
        this.translateService.instant('SECURITY_ITEM.ACCOUNT_LOCKOUT'),
        this.translateService.instant('SECURITY_ITEM.ACCESSIBLE_IP_LIST'),
        this.translateService.instant('SECURITY_ITEM.PASSWORD_POLICY'),
        this.translateService.instant('SECURITY_ITEM.ENCRYPTED_CONFIG'),
        this.translateService.instant('SECURITY_ITEM.BROAD_CAST_STORM'),
        this.translateService.instant('SECURITY_ITEM.TRAPSYSLOG'),
        this.translateService.instant('SECURITY_ITEM.PASSWORD_CHANGED')
      ]);
      awkTitle = defaultTitle.concat([
        this.translateService.instant('SECURITY_ITEM.AUTO_LOGOUT'),
        this.translateService.instant('SECURITY_ITEM.LOGIN_NOTIFICATION'),
        this.translateService.instant('SECURITY_ITEM.ENCRYPT_CONSOLE'),
        this.translateService.instant('SECURITY_ITEM.ACCOUNT_LOCKOUT'),
        this.translateService.instant('SECURITY_ITEM.ACCESSIBLE_IP_LIST'),
        this.translateService.instant('SECURITY_ITEM.PASSWORD_POLICY'),
        this.translateService.instant('SECURITY_ITEM.ENCRYPTED_CONFIG'),
        this.translateService.instant('SECURITY_ITEM.TRAPSYSLOG'),
        this.translateService.instant('SECURITY_ITEM.PASSWORD_CHANGED'),
        this.translateService.instant('SECURITY_ITEM.ACCOUNT_VALIDITY'),
        this.translateService.instant('SECURITY_ITEM.WEB_CERTIFICATE')
      ]);
      mgateTitle = defaultTitle.concat([
        this.translateService.instant('SECURITY_ITEM.LOGIN_NOTIFICATION'),
        this.translateService.instant('SECURITY_ITEM.ACCOUNT_LOCKOUT'),
        this.translateService.instant('SECURITY_ITEM.ACCESSIBLE_IP_LIST'),
        this.translateService.instant('SECURITY_ITEM.PASSWORD_POLICY'),
        this.translateService.instant('SECURITY_ITEM.TRAPSYSLOG'),
        this.translateService.instant('SECURITY_ITEM.PASSWORD_CHANGED'),
        this.translateService.instant('SECURITY_ITEM.ENCRYPT_CONSOLE')
      ]);
      nportTitle = defaultTitle.concat([
        this.translateService.instant('SECURITY_ITEM.LOGIN_NOTIFICATION'),
        this.translateService.instant('SECURITY_ITEM.ACCOUNT_LOCKOUT'),
        this.translateService.instant('SECURITY_ITEM.PASSWORD_POLICY'),
        this.translateService.instant('SECURITY_ITEM.ENCRYPT_CONSOLE'),
        this.translateService.instant('SECURITY_ITEM.HIGH_SECURE_MODE'),
        this.translateService.instant('SECURITY_ITEM.ACCESSIBLE_IP_LIST'),
        this.translateService.instant('SECURITY_ITEM.TRAPSYSLOG'),
        this.translateService.instant('SECURITY_ITEM.PASSWORD_CHANGED'),
        this.translateService.instant('SECURITY_ITEM.AUTO_LOGOUT')
      ]);
      nport5000Title = defaultTitle.concat([
        this.translateService.instant('SECURITY_ITEM.LOGIN_NOTIFICATION'),
        this.translateService.instant('SECURITY_ITEM.ACCOUNT_LOCKOUT'),
        this.translateService.instant('SECURITY_ITEM.PASSWORD_POLICY'),
        this.translateService.instant('SECURITY_ITEM.ENCRYPT_CONSOLE'),
        this.translateService.instant('SECURITY_ITEM.HIGH_SECURE_MODE'),
        this.translateService.instant('SECURITY_ITEM.ACCESSIBLE_IP_LIST'),
        this.translateService.instant('SECURITY_ITEM.TRAPSYSLOG'),
        this.translateService.instant('SECURITY_ITEM.PASSWORD_CHANGED'),
        this.translateService.instant('SECURITY_ITEM.AUTO_LOGOUT')
      ]);
    }
    this.switchData = [];
    this.awkData = [];
    this.mgateData = [];
    this.nportData = [];
    this.nport5000Data = [];
    this.unknownData = [];
    this.switchData.push(switchTitle);
    this.awkData.push(awkTitle);
    this.mgateData.push(mgateTitle);
    this.nportData.push(nportTitle);
    this.nport5000Data.push(nport5000Title);
    this.unknownData.push(defaultTitle);
    const securityViewDataResult = [...this.globalService.sortIp(this.securityViewData[0][0].result)];
    _.forEach(securityViewDataResult, result => {
      if (result.series === 'SWITCH' && result.security_items && result.security_items.length > 0) {
        let data = [result.ip, this.getDeviceSecurityLevel(result.security_level)];
        _.forEach(result.security_items, securityItem => {
          data = data.concat([this.getSecurityItemString(securityItem.item, securityItem.result).result]);
        });
        this.switchData.push(data);
      } else if (
        result.series === 'AWK_SERIES' &&
        result.security_items &&
        result.security_items &&
        result.security_items.length > 0
      ) {
        let data = [result.ip, this.getDeviceSecurityLevel(result.security_level)];
        _.forEach(result.security_items, securityItem => {
          data = data.concat([this.getSecurityItemString(securityItem.item, securityItem.result).result]);
        });
        this.awkData.push(data);
      } else if (result.series === 'MGATE' && result.security_items && result.security_items.length > 0) {
        let data = [result.ip, this.getDeviceSecurityLevel(result.security_level)];
        _.forEach(result.security_items, securityItem => {
          data = data.concat([this.getSecurityItemString(securityItem.item, securityItem.result).result]);
        });
        this.mgateData.push(data);
      } else if (result.series === 'NPORT' && result.security_items && result.security_items.length > 0) {
        let data = [result.ip, this.getDeviceSecurityLevel(result.security_level)];
        _.forEach(result.security_items, securityItem => {
          data = data.concat([this.getSecurityItemString(securityItem.item, securityItem.result).result]);
        });
        this.nportData.push(data);
      } else if (result.series === 'NPORT5000' && result.security_items && result.security_items.length > 0) {
        let data = [result.ip, this.getDeviceSecurityLevel(result.security_level)];
        _.forEach(result.security_items, securityItem => {
          data = data.concat([this.getSecurityItemString(securityItem.item, securityItem.result).result]);
        });
        this.nport5000Data.push(data);
      } else {
        const data = [result.ip, this.getDeviceSecurityLevel(result.security_level)];
        this.unknownData.push(data);
      }
    });
  }

  onExportPDF(): void {
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    this.generateSecurityViewData();
    const date = new Date();
    const today = moment(date).format('YYYY-MM-DD');
    pdfMake.fonts = {
      kaiu: {
        normal: 'kaiu.ttf',
        bold: 'kaiu.ttf',
        italics: 'kaiu.ttf',
        bolditalics: 'kaiu.ttf'
      }
    };
    const content = [];
    content.push({
      text: this.translateService.instant('INVENTORY_REPORT.report_generate_day') + today,
      fontSize: 10,
      margin: [0, 10, 0, 0]
    });
    if (this.switchData.length > 1) {
      this.sortSecurityViewReportIp(this.switchData);
      content.push({
        text: this.translateService.instant('SECURITY_ITEM.SWITCH'),
        fontSize: 15,
        alignment: 'center',
        margin: [0, 10, 0, 2]
      });
      // Use the same width in Built-in Profile / User defined
      content.push({
        table: {
          widths: [
            '6.3%',
            '6%',
            '8.77%',
            '8.77%',
            '8.77%',
            '8.77%',
            '8.77%',
            '8.77%',
            '8.77%',
            '8.77%',
            '8.77%',
            '8.77%'
          ],
          body: this.switchData
        },
        layout: this.globalService.pdfTableLayout()
      });
    }
    if (this.awkData.length > 1) {
      this.sortSecurityViewReportIp(this.awkData);
      content.push({
        text: this.translateService.instant('SECURITY_ITEM.AWK_SERIES'),
        fontSize: 15,
        alignment: 'center',
        margin: [0, 10, 0, 2]
      });
      content.push({
        table: {
          widths: [
            '6.4%',
            '6%',
            '7.963%',
            '7.963%',
            '7.963%',
            '7.963%',
            '7.963%',
            '7.963%',
            '7.963%',
            '7.963%',
            '7.963%',
            '7.963%',
            '7.963%'
          ],
          body: this.awkData
        },
        layout: this.globalService.pdfTableLayout()
      });
    }
    if (this.mgateData.length > 1) {
      this.sortSecurityViewReportIp(this.mgateData);
      content.push({
        text: this.translateService.instant('SECURITY_ITEM.MGATE'),
        fontSize: 15,
        alignment: 'center',
        margin: [0, 10, 0, 2]
      });
      content.push({
        table: {
          widths: ['6.05%', '5.8%', '7%', '13.525%', '13.525%', '13.525%', '13.525%', '13.525%', '13.525%'],
          body: this.mgateData
        },
        layout: this.globalService.pdfTableLayout()
      });
    }
    if (this.nportData.length > 1) {
      this.sortSecurityViewReportIp(this.nportData);
      content.push({
        text: this.translateService.instant('SECURITY_ITEM.NPORT'),
        fontSize: 15,
        alignment: 'center',
        margin: [0, 10, 0, 2]
      });
      content.push({
        table: {
          widths: ['6.2%', '5.8%', '9.77%', '9.77%', '9.77%', '9.77%', '9.77%', '9.77%', '9.77%', '9.77%', '9.77%'],
          body: this.nportData
        },
        layout: this.globalService.pdfTableLayout()
      });
    }
    if (this.nport5000Data.length > 1) {
      this.sortSecurityViewReportIp(this.nport5000Data);
      content.push({
        text: this.translateService.instant('SECURITY_ITEM.NPORT5000'),
        fontSize: 15,
        alignment: 'center',
        margin: [0, 10, 0, 2]
      });
      content.push({
        table: {
          widths: ['6.2%', '5.8%', '9.77%', '9.77%', '9.77%', '9.77%', '9.77%', '9.77%', '9.77%', '9.77%', '9.77%'],
          body: this.nport5000Data
        },
        layout: this.globalService.pdfTableLayout()
      });
    }
    if (this.unknownData.length > 1) {
      this.sortSecurityViewReportIp(this.unknownData);
      content.push({
        text: this.translateService.instant('SECURITY_ITEM.unknown'),
        fontSize: 15,
        alignment: 'center',
        margin: [0, 10, 0, 2]
      });
      content.push({
        table: {
          widths: ['6.2%', '5.8%'],
          body: this.unknownData
        },
        layout: this.globalService.pdfTableLayout()
      });
    }

    const docDefinition = {
      pageSize: 'A4',
      pageOrientation: 'landscape',
      pageMargins: [5, 0, 5, 0],
      content,
      defaultStyle: {
        fontSize: 6,
        font: 'kaiu'
      }
    };
    const document = pdfMake.createPdf(docDefinition);
    document.getBlob(blob => {
      this.globalService.downloadFile('security-view-report_' + this.globalService.getTimeString() + '.pdf', blob);
      this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
    });
  }

  onExportCSV(): void {
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    this.generateSecurityViewData();
    let csvContent = '';
    if (this.switchData.length > 1) {
      csvContent += '"' + this.translateService.instant('SECURITY_ITEM.SWITCH') + '"\r\n';
      csvContent = this.generateCSVContent(csvContent, this.switchData);
    }
    if (this.awkData.length > 1) {
      csvContent += '"' + this.translateService.instant('SECURITY_ITEM.AWK_SERIES') + '"\r\n';
      csvContent = this.generateCSVContent(csvContent, this.awkData);
    }
    if (this.mgateData.length > 1) {
      csvContent += '"' + this.translateService.instant('SECURITY_ITEM.MGATE') + '"\r\n';
      csvContent = this.generateCSVContent(csvContent, this.mgateData);
    }
    if (this.nportData.length > 1) {
      csvContent += '"' + this.translateService.instant('SECURITY_ITEM.NPORT') + '"\r\n';
      csvContent = this.generateCSVContent(csvContent, this.nportData);
    }
    if (this.nport5000Data.length > 1) {
      csvContent += '"' + this.translateService.instant('SECURITY_ITEM.NPORT') + '"\r\n';
      csvContent = this.generateCSVContent(csvContent, this.nport5000Data);
    }
    if (this.unknownData.length > 1) {
      csvContent += '"' + this.translateService.instant('SECURITY_ITEM.unknown') + '"\r\n';
      csvContent = this.generateCSVContent(csvContent, this.unknownData);
    }
    this.globalService.downloadFile(
      this.globalService.getCsvName('security-view-report'),
      new Blob([new Uint8Array([0xef, 0xbb, 0xbf]), csvContent], { type: 'text/plain;charset=utf-8' })
    );
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
  }

  generateCSVContent(csvContent: string, data): string {
    _.forEach(data, d => {
      for (let j = 0; j < d.length; j++) {
        if (j === d.length - 1) {
          if (d[j].text) {
            // Unknown level 會跳這這個地方
            csvContent += d[j].text + '"\r\n';
          } else {
            csvContent += d[j] + '"\r\n';
          }
        } else if (j === 0) {
          csvContent += '"' + d[j] + '","';
        } else {
          if (d[j].text) {
            csvContent += d[j].text + '","';
          } else {
            csvContent += d[j] + '","';
          }
        }
      }
    });
    return (csvContent += '\r\n');
  }

  getSecurityItemString(securityItem, securityResult): { item: string; result: string } {
    const NOT_SUPPORT = 0;
    const ASSIGNED = 1;
    const NOT_ASSIGNED = 2;
    let item;
    let result;
    if (securityItem === 'AUTO_LOGOUT') {
      item = this.translateService.instant('SECURITY_ITEM.AUTO_LOGOUT');
      if (securityResult === NOT_SUPPORT) {
        result = this.translateService.instant('SECURITY_ITEM.unknown');
      } else if (securityResult === ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.enabled');
      } else if (securityResult === NOT_ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.disabled');
      } else {
        result = this.translateService.instant('SECURITY_ITEM.read_fail');
      }
    } else if (securityItem === 'LOGIN_NOTIFICATION') {
      item = this.translateService.instant('SECURITY_ITEM.LOGIN_NOTIFICATION');
      if (securityResult === NOT_SUPPORT) {
        result = this.translateService.instant('SECURITY_ITEM.unknown');
      } else if (securityResult === ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.set');
      } else if (securityResult === NOT_ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.not_set');
      } else {
        result = this.translateService.instant('SECURITY_ITEM.read_fail');
      }
    } else if (securityItem === 'ENCRYPT_CONSOLE') {
      item = this.translateService.instant('SECURITY_ITEM.ENCRYPT_CONSOLE');
      if (securityResult === NOT_SUPPORT) {
        result = this.translateService.instant('SECURITY_ITEM.unknown');
      } else if (securityResult === ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.enabled');
      } else if (securityResult === NOT_ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.disabled');
      } else {
        result = this.translateService.instant('SECURITY_ITEM.read_fail');
      }
    } else if (securityItem === 'ACCOUNT_LOCKOUT') {
      item = this.translateService.instant('SECURITY_ITEM.ACCOUNT_LOCKOUT');
      if (securityResult === NOT_SUPPORT) {
        result = this.translateService.instant('SECURITY_ITEM.unknown');
      } else if (securityResult === ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.enabled');
      } else if (securityResult === NOT_ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.disabled');
      } else {
        result = this.translateService.instant('SECURITY_ITEM.read_fail');
      }
    } else if (securityItem === 'ACCESSIBLE_IP_LIST') {
      item = this.translateService.instant('SECURITY_ITEM.ACCESSIBLE_IP_LIST');
      if (securityResult === NOT_SUPPORT) {
        result = this.translateService.instant('SECURITY_ITEM.unknown');
      } else if (securityResult === ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.enabled');
      } else if (securityResult === NOT_ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.disabled');
      } else {
        result = this.translateService.instant('SECURITY_ITEM.read_fail');
      }
    } else if (securityItem === 'PASSWORD_POLICY') {
      item = this.translateService.instant('SECURITY_ITEM.PASSWORD_POLICY');
      if (securityResult === NOT_SUPPORT) {
        result = this.translateService.instant('SECURITY_ITEM.unknown');
      } else if (securityResult === ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.enabled');
      } else if (securityResult === NOT_ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.disabled');
      } else {
        result = this.translateService.instant('SECURITY_ITEM.read_fail');
      }
    } else if (securityItem === 'ENCRYPTED_CONFIG') {
      item = this.translateService.instant('SECURITY_ITEM.ENCRYPTED_CONFIG');
      if (securityResult === NOT_SUPPORT) {
        result = this.translateService.instant('SECURITY_ITEM.unknown');
      } else if (securityResult === ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.enabled');
      } else if (securityResult === NOT_ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.disabled');
      } else {
        result = this.translateService.instant('SECURITY_ITEM.read_fail');
      }
    } else if (securityItem === 'BROAD_CAST_STORM') {
      item = this.translateService.instant('SECURITY_ITEM.BROAD_CAST_STORM');
      if (securityResult === NOT_SUPPORT) {
        result = this.translateService.instant('SECURITY_ITEM.unknown');
      } else if (securityResult === ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.enabled');
      } else if (securityResult === NOT_ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.disabled');
      } else {
        result = this.translateService.instant('SECURITY_ITEM.read_fail');
      }
    } else if (securityItem === 'NTP_SERVER') {
      item = this.translateService.instant('SECURITY_ITEM.NTP_SERVER');
      if (securityResult === NOT_SUPPORT) {
        result = this.translateService.instant('SECURITY_ITEM.unknown');
      } else if (securityResult === ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.set');
      } else if (securityResult === NOT_ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.not_set');
      } else {
        result = this.translateService.instant('SECURITY_ITEM.read_fail');
      }
    } else if (securityItem === 'TRAPSYSLOG') {
      item = this.translateService.instant('SECURITY_ITEM.TRAPSYSLOG');
      if (securityResult === NOT_SUPPORT) {
        result = this.translateService.instant('SECURITY_ITEM.unknown');
      } else if (securityResult === ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.set');
      } else if (securityResult === NOT_ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.not_set');
      } else {
        result = this.translateService.instant('SECURITY_ITEM.read_fail');
      }
    } else if (securityItem === 'PASSWORD_CHANGED') {
      item = this.translateService.instant('SECURITY_ITEM.PASSWORD_CHANGED');
      if (securityResult === NOT_SUPPORT) {
        result = this.translateService.instant('SECURITY_ITEM.unknown');
      } else if (securityResult === ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.changed');
      } else if (securityResult === NOT_ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.non-changed');
      } else {
        result = this.translateService.instant('SECURITY_ITEM.read_fail');
      }
    } else if (securityItem === 'SYSLOG') {
      item = this.translateService.instant('SECURITY_ITEM.SYSLOG');
      if (securityResult === NOT_SUPPORT) {
        result = this.translateService.instant('SECURITY_ITEM.unknown');
      } else if (securityResult === ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.set');
      } else if (securityResult === NOT_ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.not_set');
      } else {
        result = this.translateService.instant('SECURITY_ITEM.read_fail');
      }
    } else if (securityItem === 'ACCOUNT_VALIDITY') {
      item = this.translateService.instant('SECURITY_ITEM.ACCOUNT_VALIDITY');
      if (securityResult === NOT_SUPPORT) {
        result = this.translateService.instant('SECURITY_ITEM.unknown');
      } else if (securityResult === ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.set');
      } else if (securityResult === NOT_ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.not_set');
      } else {
        result = this.translateService.instant('SECURITY_ITEM.read_fail');
      }
    } else if (securityItem === 'HIGH_SECURE_MODE') {
      item = this.translateService.instant('SECURITY_ITEM.HIGH_SECURE_MODE');
      if (securityResult === NOT_SUPPORT) {
        result = this.translateService.instant('SECURITY_ITEM.unknown');
      } else if (securityResult === ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.enabled');
      } else if (securityResult === NOT_ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.disabled');
      } else {
        result = this.translateService.instant('SECURITY_ITEM.read_fail');
      }
    } else if (securityItem === 'WEB_CERTIFICATE') {
      item = this.translateService.instant('SECURITY_ITEM.WEB_CERTIFICATE');
      if (securityResult === NOT_SUPPORT) {
        result = this.translateService.instant('SECURITY_ITEM.unknown');
      } else if (securityResult === ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.enabled');
      } else if (securityResult === NOT_ASSIGNED) {
        result = this.translateService.instant('SECURITY_ITEM.disabled');
      } else {
        result = this.translateService.instant('SECURITY_ITEM.read_fail');
      }
    }
    return { item, result };
  }

  getDeviceSecurityLevel(securityLevel): { text: string; color: string } {
    let deviceSecurityLevel: { text: string; color: string } = {
      text: this.translateService.instant('PREFERENCES.security_view.unknown'),
      color: '#000000'
    };
    if (securityLevel === 'STR_IEC_62443_4_2_L2') {
      deviceSecurityLevel = {
        text: this.translateService.instant('PREFERENCES.security_view.high_text'),
        color: '#000000'
        // color: this.globalService.removeHexAlpha(this.securityViewPreferenceData[0].color.high)
      };
    } else if (securityLevel === 'STR_IEC_62443_4_2_L1') {
      deviceSecurityLevel = {
        text: this.translateService.instant('PREFERENCES.security_view.medium_text'),
        color: '#000000'
        // color: this.globalService.removeHexAlpha(this.securityViewPreferenceData[0].color.medium)
      };
    } else if (securityLevel === 'STR_GENERAL_BASELINE') {
      deviceSecurityLevel = {
        text: this.translateService.instant('PREFERENCES.security_view.basic_text'),
        color: '#000000'
        // color: this.globalService.removeHexAlpha(this.securityViewPreferenceData[0].color.basic)
      };
    } else if (securityLevel === 'STR_OPEN') {
      deviceSecurityLevel = {
        text: this.translateService.instant('PREFERENCES.security_view.open'),
        color: '#000000'
        // color: this.globalService.removeHexAlpha(this.securityViewPreferenceData[0].color.open)
      };
    } else if (securityLevel === 'STR_PASS') {
      deviceSecurityLevel = {
        text: this.translateService.instant('PREFERENCES.security_view.pass'),
        color: '#000000'
        // color: this.globalService.removeHexAlpha(this.securityViewPreferenceData[0].color.passed)
      };
    } else if (securityLevel === 'STR_NOT_PASS') {
      deviceSecurityLevel = {
        text: this.translateService.instant('PREFERENCES.security_view.not_pass'),
        color: '#000000'
        // color: this.globalService.removeHexAlpha(this.securityViewPreferenceData[0].color.failed)
      };
    }
    return deviceSecurityLevel;
  }

  showWirelessTable(): void {
    this.commandBarKey = 'wirelessTableView';
    this.isShowGroupButton = false;
    this.isTopologyView = false;
    this.isTopologyTableView = false;
    this.isWirelessTableView = false;
    this.isWirelessTable = true;
    this.isShowModeButton = false;
    this.isShowInfoButton = false;
  }

  /**
   * Resets group selection to root when clicking the Moxa logo
   * Always returns to Root group regardless of current group type
   */
  resetToRootGroup(): void {
    // Always reset to root group when clicking the Moxa logo
    this.networkService.selectedGroupId = 0;

    // Clear any selected IOPAC device
    this.networkService.selectedIopacDevice = null;
    this.selectedIopacModule = 0;

    // Reset to root group
    if (this.mxSiteNav) {
      this.mxSiteNav.selectGroup(0, 'Root');
    }

    // Redraw topology to reflect the change
    if (this.mxTopology) {
      this.mxTopology.redrawTopology();
    }
  }

  sortSecurityViewReportIp(ipAddressArray): number {
    return ipAddressArray.sort((a, b) => {
      if (isNaN(parseInt(b[0], 10))) {
        return 1;
      } else {
        a = a[0].split('.');
        b = b[0].split('.');
        for (let i = 0; i < a.length; i++) {
          a[i] = parseInt(a[i], 10);
          b[i] = parseInt(b[i], 10);
          if (a[i] < b[i]) {
            return -1;
          } else if (a[i] > b[i]) {
            return 1;
          }
        }
        return 0;
      }
    });
  }

  onOpenWirelessDeviceDashboard(device: DeviceUnit): void {
    //ANCHOR - onOpenWirelessDeviceDashboard
    this.isWirelessDashboardDataReady(device).subscribe(value => {
      if (value) {
        this.devicesData = this.dataRepository.getData(DataType.DEVICE);
        extraLargeDialogConfig.data = {
          devicesData: this.devicesData,
          selectedDevice: device
        };
        this.dialog.open(WirelessDeviceDashboardComponent, extraLargeDialogConfig);
      } else {
        smallDialogConfig.data = {};
        this.dialog.open(DataNotReadyComponent, smallDialogConfig);
      }
    });
  }

  onShowImportScdDialog(): void {
    this.onButtonClick('openImportScd');
  }

  isShowAwkAlert(): boolean {
    if (!this.networkService.selectedDevice) return;

    return (
      this.networkService.selectedDevice.model?.indexOf('AWK') !== -1 &&
      this.deviceSecurityResult?.security_level === 'STR_FAIL'
    );
  }

  isWirelessDashboardDataReady(deviceData): Observable<boolean> {
    const components = deviceData.device_components;
    return new Observable(observer => {
      let val = false;
      if (deviceData.wireless_role === wirelessRole.ap) {
        val =
          deviceData.ip &&
          deviceData.id &&
          components.firmwareVersion &&
          deviceData.model &&
          components.vapAuthType &&
          components.devTxPowerdBm &&
          components.sysUpTime &&
          components.wlanClientCount;
      } else if (deviceData.wireless_role === wirelessRole.client || deviceData.wireless_role === wirelessRole.mesh) {
        val =
          deviceData.ip &&
          deviceData.id &&
          components.firmwareVersion &&
          deviceData.model &&
          components.vapAuthType &&
          components.wlanBSSID &&
          components.wlanTxRate &&
          components.wlanConnectionTime;
      }
      observer.next(val);
      observer.complete();
    });
  }

  sortPortRelatedProp(device: DeviceUnit): DeviceUnit {
    if (!device?.device_components) return device;
    // This is prevent some properties of device_components are not sorted
    const sortKeys: string[] = [
      'ifOperStatus',
      'poePortConsumption',
      'portSpeed',
      'portName',
      'ifSpeed',
      'ifType',
      'ifDesc',
      'poePortVoltage',
      'poePortCurrent',
      'sfpTemperature',
      'sfpVoltage',
      'sfpTxPower',
      'sfpRxPower',
      'sfpModel',
      'sfpSN',
      'poePowerOutputMode',
      'poePortLegacyPdDetect',
      'poePortClass'
    ];
    _.forEach(sortKeys, (sortKey: string) => {
      if (device?.device_components[sortKey] && device?.device_components[sortKey].index) {
        device.device_components[sortKey] = _.sortBy(device.device_components[sortKey], o => o.index);
      }
    });
    return device;
  }

  showSFPInfo(device: DeviceUnit, i: number): boolean {
    const sfpFields = ['sfpTemperature', 'sfpVoltage', 'sfpTxPower', 'sfpRxPower', 'sfpModel', 'sfpSN'];
    return sfpFields.some(field => device?.device_components?.[field]?.[i]?.status !== undefined);
  }

  formatSFPInfo(device: DeviceUnit, i: number): string {
    const sfpComponents = device?.device_components;
    if (!sfpComponents) {
      return;
    }

    const parts: string[] = [];
    const temperatureText = this.translateService.instant('sfpList.temperature');
    const voltageText = this.translateService.instant('sfpList.voltage');
    const txText = this.translateService.instant('sfpList.tx');
    const rxText = this.translateService.instant('sfpList.rx');
    const modelText = this.translateService.instant('BASIC_INFORMATION.model');
    const snText = 'SN';

    if (sfpComponents.sfpTemperature && sfpComponents.sfpTemperature[i]?.status) {
      parts.push(`${temperatureText}: ${sfpComponents.sfpTemperature[i].status}`);
    }

    if (sfpComponents.sfpVoltage && sfpComponents.sfpVoltage[i]?.status) {
      if (parts.length > 0) {
        parts.push(' / ');
      }
      parts.push(`${voltageText}: ${sfpComponents.sfpVoltage[i].status}V`);
    }

    if (sfpComponents.sfpTxPower && sfpComponents.sfpTxPower[i]?.status) {
      if (parts.length > 0) {
        parts.push('<br />');
      }
      parts.push(`${txText}: ${sfpComponents.sfpTxPower[i].status}`);
    }

    if (sfpComponents.sfpRxPower && sfpComponents.sfpRxPower[i]?.status) {
      if (sfpComponents.sfpTxPower && sfpComponents.sfpTxPower[i]?.status) {
        parts.push(' / ');
      } else if (parts.length > 0) {
        parts.push('<br />');
      }
      parts.push(`${rxText}: ${sfpComponents.sfpRxPower[i].status}`);
    }

    if (sfpComponents.sfpModel && sfpComponents.sfpModel[i]?.status) {
      if (parts.length > 0) {
        parts.push('<br />');
      }
      parts.push(`${modelText}: ${sfpComponents.sfpModel[i].status}`);
    }

    if (sfpComponents.sfpSN && sfpComponents.sfpSN[i]?.status) {
      if (parts.length > 0) {
        parts.push('<br />');
      }
      parts.push(`${snText}: ${sfpComponents.sfpSN[i].status}`);
    }

    return parts.join('');
  }

  onShowGooseDialog(event: {
    siteId: string;
    status: string;
    port: number;
    ip: string;
    cbName: string;
    appId: string;
    mac: string;
  }): void {
    extraLargeDialogConfig.data = event;
    this.dialog.open(GooseDistinctComponent, extraLargeDialogConfig);
  }

  onShowResetGooseDialog(event: {
    siteId: string;
    status: string;
    port: number;
    ip: string;
    cbName: string;
    appId: string;
    mac: string;
  }): void {
    mediumDialogConfig.data = event;
    this.dialog.open(GooseResetComponent, mediumDialogConfig);
  }

  checkScd() {
    if (_.includes(this.globalService.siteInfo.now.addons, 'grid')) {
      this.dataService.scdCheck(this.networkService.selectedSiteId).subscribe(
        () => {},
        (err: unknown) => {
          const error = err as any;
          mediumDialogConfig.data = {
            errorDevices: error?.error?.devices
          };
          this.dialog
            .open(ImportScdComponent, mediumDialogConfig)
            .afterClosed()
            .subscribe(result => {
              this.mxTopology.redrawTopology();
            });
        }
      );
    }
  }

  isComputer() {
    return (
      _.includes(this.networkService.selectedDevice.model, 'DA820') ||
      _.includes(this.networkService.selectedDevice.model, 'UC-2101') ||
      _.includes(this.networkService.selectedDevice.model, 'UC-2102') ||
      _.includes(this.networkService.selectedDevice.model, 'UC-2104') ||
      _.includes(this.networkService.selectedDevice.model, 'UC-2111') ||
      _.includes(this.networkService.selectedDevice.model, 'UC-2112') ||
      _.includes(this.networkService.selectedDevice.model, 'UC-2114') ||
      _.includes(this.networkService.selectedDevice.model, 'UC-2116') ||
      _.includes(this.networkService.selectedDevice.model, 'UC-1222A') ||
      _.includes(this.networkService.selectedDevice.model, 'UC-2222A-T') ||
      _.includes(this.networkService.selectedDevice.model, 'UC-2222A-T-AP') ||
      _.includes(this.networkService.selectedDevice.model, 'UC-2222A-T-CN') ||
      _.includes(this.networkService.selectedDevice.model, 'UC-2222A-T-EU') ||
      _.includes(this.networkService.selectedDevice.model, 'UC-2222A-T-US') ||
      _.includes(this.networkService.selectedDevice.model, 'V2406C') ||
      this.isIpcOid(this.networkService.selectedDevice.sysobjid)
    );
  }

  canNotModifyUsername() {
    const models = ['DA820', 'V2406C'];
    return models.includes(this.networkService.selectedDevice.model);
  }

  sortGooseTable(gooseTable: GooseTableUnit[]): GooseTableUnit[] {
    return _.sortBy(gooseTable, ['gocb_name']);
  }

  showPoeInfo(ifOperStatus): boolean {
    if (
      this.device.device_components.poePortConsumption &&
      +ifOperStatus?.index - 1 < +this.device.device_components.poePortConsumption.length &&
      +this.device.device_components.poePortConsumption[+ifOperStatus?.index - 1].status > 0 &&
      +this.device.device_components.poePortVoltage[+ifOperStatus?.index - 1].status > 0 &&
      +this.device.device_components.poePortCurrent[+ifOperStatus?.index - 1].status > 0
    ) {
      return true;
    } else {
      return false;
    }
  }

  showIopacPoeInfo(ifOperStatus): boolean {
    if (
      this.networkService.selectedIopacDevice.device_components.poePortConsumption &&
      +ifOperStatus?.index - 1 < +this.networkService.selectedIopacDevice.device_components.poePortConsumption.length &&
      +this.networkService.selectedIopacDevice.device_components.poePortConsumption[+ifOperStatus?.index - 1].status >
        0 &&
      +this.networkService.selectedIopacDevice.device_components.poePortVoltage[+ifOperStatus?.index - 1].status > 0 &&
      +this.networkService.selectedIopacDevice.device_components.poePortCurrent[+ifOperStatus?.index - 1].status > 0
    ) {
      return true;
    } else {
      return false;
    }
  }

  resizeRecentEvent() {
    const element = document.getElementById('recent-event-layout');
    setTimeout(() => {
      this.recentEventHeight = element.clientHeight;
      this.cdr.detectChanges();
    }, 400);
  }

  onToggleAutomationButtonSide($event: boolean): void {
    this.isAutomationButtonBarOpen = $event;
  }

  isIpcOid(oid) {
    return this.ipcOids.some(prefix => oid?.includes(prefix));
  }

  onSelectionChange(): void {
    const selectedValue = this.selectedIopacModule;

    if (this.selectedIopacModule !== this.networkService.selectedIopacDevice?.id) {
      this.mxData.currentSelectedDevices = [];
      this.device = null;
      this.currentEvents = [];
    }

    if (this.topologyViewMode === TopologyViewMode.security) {
      this.handleSecurityView();
    }

    if (!this.iopacDevices) {
      console.warn('iopacDevices is null or undefined in onSelectionChange');
      this.iopacDevices = [];
    }

    if (selectedValue === 0) {
      this.networkService.selectedIopacDevice = null;
      return;
    }

    this.networkService.selectedIopacDevice = this.iopacDevices.find(device => device.id === selectedValue) || null;

    if (!this.networkService.selectedIopacDevice) {
      console.warn(`No device found with id ${selectedValue}`);
      return;
    }

    this.mxData.currentSelectedDevices.push(this.networkService.selectedIopacDevice);
    this.mxTopology.processObjectSelected();
  }

  onSelectIopacModule(event: DeviceUnit): void {
    this.networkService.selectedIopacDevice = event;
    const siteId = this.mxData.currentSelectedGroups[0]?.site_id;
    const groupId = this.mxData.currentSelectedGroups[0]?.id;

    if (!siteId || !groupId) {
      console.error('Missing site ID or group ID for Iopac device selection');
      return;
    }

    if (!this.iopacDevices) {
      this.iopacDevices = [];
    }

    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));

    this.getIopacDevicesOfGroupById(siteId, groupId)
      .then(data => {
        this.networkService.selectedIopacDevice = data.find(device => device.id === event.id) || null;
        this.mxData.currentSelectedDevices.push(this.networkService.selectedIopacDevice);
        this.mxTopology.processObjectSelected();
        this.selectedIopacModule = event.id;
      })
      .catch(error => {
        console.error('Error loading Iopac devices:', error);
        this.errorService.handleNormalError(this.translateService.instant('NETWORK.ioPac.load_error'));
      })
      .finally(() => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
      });
  }

  onBlur(): void {
    // this.selectedIopacModule = 0;
    // this.networkService.selectedIopacDevice = null;
  }

  handleSecurityView() {
    const selectedDevice = this.iopacDevices?.find(device => device.id === this.selectedIopacModule);
    if (!selectedDevice) return;

    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    this.topologyService
      .getSecurityLevel(this.networkService.selectedSiteId, [selectedDevice.ip], this.securityViewProfile)
      .then((response: any) => {
        const deviceResult = response[0].result[0] || {};
        const securityItems = deviceResult.security_items || [];

        const securityProfiles = this.getMatchingSecurityProfiles(deviceResult.series);

        this.buildSecurityItems(securityItems, securityProfiles);
        this.deviceSecurityLevel = this.getDeviceSecurityLevel(deviceResult.security_level).text;

        this.scrollToSecurityTab();
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
      });
  }

  private getMatchingSecurityProfiles(deviceSeries: string): any[] {
    if (!deviceSeries || !this.securityViewProfilesData?.[0]?.[0]?.profiles) {
      return [];
    }

    return this.securityViewProfilesData[0][0].profiles.filter(profile => profile.series === deviceSeries);
  }

  private scrollToSecurityTab(): void {
    setTimeout(() => {
      const paginationButton = this.elementRef.nativeElement.querySelector('.mat-tab-header-pagination-after');
      if (paginationButton) {
        paginationButton.click();
      }
    }, 1000);
  }
}
