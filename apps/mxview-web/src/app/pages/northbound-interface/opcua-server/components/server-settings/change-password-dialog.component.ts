import { Component, inject } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatLegacyDialogRef as MatDialogRef } from '@angular/material/legacy-dialog';

import { SharedModule } from '../../../../../shared/shared.module';

@Component({
  standalone: true,
  imports: [SharedModule],
  selector: 'app-change-password-dialog',
  template: `
    <h3 mat-dialog-title>
      <span> {{ 'OPC_UA_SERVER.change_authentication_password' | translate }}</span>
    </h3>
    <form [formGroup]="form">
      <mat-form-field class="password suffix-form-field" [style.width.px]="180">
        <input
          appMarkId="change-password-dialog"
          matInput
          id="input-server-setting-password"
          maxlength="63"
          placeholder="{{ 'LOGIN.password' | translate }}"
          formControlName="password"
          [type]="isHide ? 'password' : 'text'"
          #pwElement
        />
        <button
          appMarkId="change-password-dialog"
          type="button"
          mat-icon-button
          type="button"
          (click)="isHide = !isHide"
        >
          <mat-icon appMarkId="change-password-dialog">
            {{ isHide ? 'visibility_off' : 'visibility' }}
          </mat-icon>
        </button>
        <mat-hint align="end">{{ pwElement.value.length }} / 63</mat-hint>
      </mat-form-field>
    </form>
    <div mat-dialog-actions align="end">
      <button
        appMarkId="change-password-dialog"
        mat-button
        id="dialog-button-add-tag-close"
        color="primary"
        mat-dialog-close
      >
        {{ 'BUTTON.cancel' | translate }}
      </button>
      <button
        appMarkId="change-password-dialog"
        id="dialog-button-apply-tag"
        mat-raised-button
        color="primary"
        (click)="onApply()"
        [disabled]="!form.valid || form.pristine"
      >
        {{ 'BUTTON.change' | translate }}
      </button>
    </div>
  `,
})
export class ChangePasswordDialogComponent {
  dialogRef = inject(MatDialogRef<ChangePasswordDialogComponent>);
  fb = inject(FormBuilder);

  isHide = true;

  form: FormGroup = this.fb.group({
    password: ['', Validators.required],
  });

  onApply() {
    this.dialogRef.close(this.form.value.password);
  }
}
