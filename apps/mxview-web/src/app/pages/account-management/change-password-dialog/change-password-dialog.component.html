<h3 id="h3-change-password" mat-dialog-title>{{ dialogTitle }}</h3>
<div mat-dialog-content>
  <form [formGroup]="changePasswordForm">
    <div fxLayout="row" fxLayoutWrap fxLayout.xs="column">
      <mat-form-field
        class="suffix-form-field"
        [ngClass]="{ 'form-field-default-gap': changePasswordForm.controls['oldPassword'].hasError('pattern') }"
      >
        <input
          id="input-change-password-old-password"
          matInput
          maxlength="63"
          #newPassword
          placeholder="{{ 'ACCOUNT_MANAGEMENT.old_password' | translate }}"
          [type]="hidePassword1 ? 'password' : 'text'"
          formControlName="oldPassword"
          required
        />
        <button mat-icon-button type="button" (click)="hidePassword1 = !hidePassword1">
          <mat-icon> {{ hidePassword1 ? 'visibility_off' : 'visibility' }} </mat-icon>
        </button>
        <mat-hint align="end">{{ newPassword.value.length }} / 63</mat-hint>
        <mat-error *ngIf="changePasswordForm.controls['oldPassword'].hasError('required')">
          {{ 'ADD_DEVICE.field_required' | translate }}</mat-error
        >
        <mat-error *ngIf="changePasswordForm.controls['oldPassword'].hasError('pattern')">
          {{ 'ERROR_MESSAGE.input_invalid_password_characters' | translate }}</mat-error
        >
      </mat-form-field>
    </div>
    <div fxLayout="row" fxLayoutWrap fxLayout.xs="column">
      <mat-form-field
        class="suffix-form-field"
        [ngClass]="{ 'form-field-default-gap': changePasswordForm.controls['password'].hasError('pattern') }"
      >
        <input
          id="input-change-password-password"
          matInput
          maxlength="63"
          #newPasswordConfirm
          placeholder="{{ 'ACCOUNT_MANAGEMENT.new_password' | translate }}"
          [type]="hidePassword2 ? 'password' : 'text'"
          formControlName="password"
          required
        />
        <button mat-icon-button type="button" (click)="hidePassword2 = !hidePassword2">
          <mat-icon> {{ hidePassword2 ? 'visibility_off' : 'visibility' }} </mat-icon>
        </button>
        <mat-hint align="end">{{ newPasswordConfirm.value.length }} / 16</mat-hint>
        <mat-error *ngIf="changePasswordForm.controls['password'].hasError('required')">
          {{ 'ADD_DEVICE.field_required' | translate }}</mat-error
        >
        <mat-error *ngIf="changePasswordForm.controls['password'].hasError('pattern')">
          {{ 'ERROR_MESSAGE.input_invalid_password_characters' | translate }}</mat-error
        >
      </mat-form-field>
    </div>
  </form>
</div>
<div mat-dialog-actions align="end">
  <button id="button-change-password-cancel" mat-button mat-dialog-close color="primary">
    {{ 'BUTTON.cancel' | translate }}
  </button>
  <button
    id="button-change-password-apply"
    mat-raised-button
    color="primary"
    [disabled]="!changePasswordForm.valid"
    (click)="onChangePassword()"
  >
    {{ 'BUTTON.apply' | translate }}
  </button>
</div>
