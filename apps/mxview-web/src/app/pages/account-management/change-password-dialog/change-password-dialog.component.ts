import { Component, OnInit, Inject } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import {
  MatLegacyDialogRef as MatDialogRef,
  MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA,
} from '@angular/material/legacy-dialog';
import { MatLegacySnackBar as MatSnackBar } from '@angular/material/legacy-snack-bar';

import { TranslateService } from '@ngx-translate/core';

import { ErrorService } from '../../../shared/Service/error.service';
import { ValidatorPattern } from '../../../shared/validator/validators';
import { DataService } from './../../../shared/Service/mx-platform/Service/DataService';

@Component({
  selector: 'app-change-password-dialog',
  templateUrl: './change-password-dialog.component.html',
})
export class ChangePasswordDialogComponent implements OnInit {
  changePasswordForm: UntypedFormGroup;
  dialogTitle = '';
  hidePassword1 = true;
  hidePassword2 = true;
  private userName = '';

  constructor(
    @Inject(MAT_DIALOG_DATA) public editingData: any,
    public dialogRef: MatDialogRef<ChangePasswordDialogComponent>,
    private snackBar: MatSnackBar,
    private fBuilder: UntypedFormBuilder,
    private translateService: TranslateService,
    private dataService: DataService,
    private errorService: ErrorService
  ) {
    this.userName = editingData.userName;
    this.dialogTitle = this.translateService.instant('ACCOUNT_MANAGEMENT.change_password');
  }

  ngOnInit(): void {
    this.changePasswordForm = this.fBuilder.group({
      password: ['', Validators.compose([Validators.maxLength(63)])],
      oldPassword: ['', Validators.compose([Validators.maxLength(63)])],
    });
  }

  onChangePassword(): void {
    const userAccountSetting: any = {};
    if (this.changePasswordForm.valid) {
      this.dataService
        .updateUserPassword(
          this.userName,
          this.changePasswordForm.controls.password.value,
          this.changePasswordForm.controls.oldPassword.value
        )
        .then(() => {
          this.snackBar.open(this.translateService.instant('ACCOUNT_MANAGEMENT.update_user_success'), '', {
            duration: 3000,
          });
          this.dialogRef.close();
        })
        .catch(error => {
          console.log('change user password error=' + JSON.stringify(error));
          if (error.resultId === -102) {
            this.errorService.handleError(
              error,
              this.translateService.instant('ACCOUNT_MANAGEMENT.password_policy_mismatch')
            );
          } else {
            this.errorService.handleNormalError(this.translateService.instant('ACCOUNT_MANAGEMENT.update_user_fail'));
          }
        });
    }
  }
}
