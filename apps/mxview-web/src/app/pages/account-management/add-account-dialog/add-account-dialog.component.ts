import { Component, Inject, OnInit } from '@angular/core';
import { UntypedForm<PERSON>uilder, UntypedFormGroup, Validators } from '@angular/forms';
import {
  MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA,
  MatLegacyDialogRef as MatDialogRef,
} from '@angular/material/legacy-dialog';
import { MatLegacySnackBar as MatSnackBar } from '@angular/material/legacy-snack-bar';

import { TranslateService } from '@ngx-translate/core';
import * as _ from 'lodash';

import { AppState } from '../../../app.service';
import { ErrorService } from '../../../shared/Service/error.service';
import { UserPreferenceUnit } from '../../../shared/Service/mx-platform/DataDef/UserDataDefs';
import { DataService } from '../../../shared/Service/mx-platform/Service/DataService';
import { ValidatorPattern } from '../../../shared/validator/validators';
import { AccountDisplayType } from '../account-display-type';
import { GlobalEvent, GlobalEventType } from './../../../global.event';
import { AuthService } from './../../../shared/Service/auth.service';

interface UiProfile {
  value: string;
  str: string;
}
@Component({
  templateUrl: './add-account-dialog.component.html',
})
export class AddAccountDialogComponent implements OnInit {
  addAccountForm: UntypedFormGroup;
  allSiteList = [];
  authorityList = [];
  showSiteSelector = true;
  showOldPasswordInput = false;
  dialogTitle = '';
  uiProfileList: UiProfile[] = [];
  onlyDefaultUiProfile = false;
  showUiProfile = false;
  hidePassword1 = true;
  hidePassword2 = true;
  isAdminHimself: boolean;

  private accountDisplayType: AccountDisplayType;
  private defaultValue = 'default';
  private defaultString = 'Default';
  private nocOperatorValue = 'nocOperator';
  private nocOperatorString = 'NOC Operator';
  private nocUserValue = 'nocUser';
  private nocUserString = 'NOC User';
  private adminValue = 65537;
  private supervisorValue = 131073;

  constructor(
    @Inject(MAT_DIALOG_DATA) public dialogData: any,
    public dialogRef: MatDialogRef<AddAccountDialogComponent>,
    private appState: AppState,
    private auth: AuthService,
    private snackBar: MatSnackBar,
    private fBuilder: UntypedFormBuilder,
    private translateService: TranslateService,
    private dataService: DataService,
    private errorService: ErrorService
  ) {
    this.accountDisplayType = new AccountDisplayType(this.translateService);
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
  }

  ngOnInit(): void {
    this.authorityList = this.accountDisplayType.authority;
    this.addAccountForm = this.fBuilder.group({
      userName: ['', Validators.compose([Validators.required, Validators.pattern(ValidatorPattern.VALID_REGEX)])],
      password: ['', Validators.compose([Validators.maxLength(63)])],
      oldPassword: ['', Validators.compose([Validators.maxLength(63)])],
      authority: ['', Validators.compose([Validators.required])],
      uiProfile: ['', Validators.compose([Validators.required])],
    });
    let siteId = localStorage.getItem('siteId');
    if (siteId === '-1') {
      siteId = this.auth.autoSelectSiteId();
    }
    // if open on editing mode, send setting to dialog component
    if (this.dialogData.data) {
      this.dialogTitle = this.translateService.instant('ACCOUNT_MANAGEMENT.modify_account_dialog');
      this.showOldPasswordInput = true;
      this.addAccountForm.controls.userName.setValue(this.dialogData.data.userName);
      this.addAccountForm.controls['userName'].disable();
      const currUser = localStorage.getItem('loginUser');
      this.isAdminHimself = currUser === this.dialogData.data.userName;
      this.addAccountForm.controls.authority.setValue(this.dialogData.data.authority);
      this.updateUiProfile(this.dialogData.data.authority);
      this.dataService.getUserPreference(this.dialogData.data.userName).then(perference => {
        this.addAccountForm.controls.uiProfile.setValue(perference[0].ui_profile);
      });
    } else {
      this.dialogTitle = this.translateService.instant('ACCOUNT_MANAGEMENT.add_account_dialog');
    }
    Promise.all([this.dataService.getSite(), this.dataService.getUiProfiles(siteId)])
      .then(data => {
        this.allSiteList = data[0];
        for (const profile of data[1]) {
          let profileObj: UiProfile;
          if (profile === this.defaultValue) {
            profileObj = {
              value: this.defaultValue,
              str: this.defaultString,
            };
          } else if (profile === this.nocOperatorValue) {
            profileObj = {
              value: this.nocOperatorValue,
              str: this.nocOperatorString,
            };
          } else if (profile === this.nocUserValue) {
            profileObj = {
              value: this.nocUserValue,
              str: this.nocUserString,
            };
          }
          this.uiProfileList.push(profileObj);
        }
        if (this.uiProfileList.length === 1) {
          const index = _.findIndex(this.uiProfileList, o => {
            return o.value === this.defaultValue;
          });
          if (index !== -1) {
            this.onlyDefaultUiProfile = true;
          }
        }
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
      })
      .catch(error => {
        console.log('get all site error =' + JSON.stringify(error));
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
      });
  }

  updateUiProfile(authorityValue): void {
    if (!this.onlyDefaultUiProfile) {
      if (authorityValue === this.supervisorValue) {
        this.showUiProfile = true;
        this.addAccountForm.controls.uiProfile.setValidators([Validators.required]);
        this.addAccountForm.controls.uiProfile.updateValueAndValidity();
      } else {
        this.clearUiProfileValidators();
      }
    } else {
      this.clearUiProfileValidators();
    }
  }

  clearUiProfileValidators(): void {
    this.showUiProfile = false;
    this.addAccountForm.controls.uiProfile.clearValidators();
    this.addAccountForm.controls.uiProfile.setErrors(null);
  }

  onSelectAuthorityChange(event): void {
    if (event.source.selected) {
      this.updateUiProfile(event.source.selected.value);
    }
  }

  onAddUser(): void {
    if (this.addAccountForm.valid) {
      const userAccountSetting: any = {};
      const addUserAcountElement: any = {};
      this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
      userAccountSetting.userName = this.addAccountForm.controls.userName.value;
      userAccountSetting.password = this.addAccountForm.controls.password.value;
      userAccountSetting.authority = this.addAccountForm.controls.authority.value;
      userAccountSetting.accessibleSites = this.allSiteList.map(site => site.site_id);
      userAccountSetting.oldPassword = this.addAccountForm.controls.oldPassword.value;

      addUserAcountElement.userName = userAccountSetting.userName;
      addUserAcountElement.authority = userAccountSetting.authority;
      addUserAcountElement.accessSites = userAccountSetting.accessibleSites;

      const userPreferenceUnit = new UserPreferenceUnit();
      if (this.showUiProfile) {
        userPreferenceUnit.ui_profile = this.addAccountForm.controls.uiProfile.value;
      } else {
        userPreferenceUnit.ui_profile = this.defaultValue;
      }
      if (this.showOldPasswordInput) {
        Promise.all([
          this.dataService.updateUserPreference(userAccountSetting.userName, userPreferenceUnit),
          this.dataService.updateUserAccount(
            userAccountSetting.userName,
            userAccountSetting.password,
            userAccountSetting.authority,
            userAccountSetting.accessibleSites,
            userAccountSetting.oldPassword
          ),
        ])
          .then(() => {
            this.snackBar.open(this.translateService.instant('ACCOUNT_MANAGEMENT.update_user_success'), '', {
              duration: 3000,
            });
            this.dialogRef.close('submit');
          })
          .catch(error => {
            console.log('modify user error=' + JSON.stringify(error));
            if (error.resultId === -102) {
              if (error.message === 'The user is already existing.') {
                this.errorService.handleError(error, this.translateService.instant('ACCOUNT_MANAGEMENT.user_exist'));
              } else {
                this.errorService.handleError(
                  error,
                  this.translateService.instant('ACCOUNT_MANAGEMENT.password_policy_mismatch')
                );
              }
            } else {
              this.errorService.handleError(
                error,
                this.translateService.instant('ACCOUNT_MANAGEMENT.update_user_fail')
              );
            }
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          });
      } else {
        this.dataService
          .addUserAccount(
            userAccountSetting.userName,
            userAccountSetting.password,
            userAccountSetting.authority,
            userAccountSetting.accessibleSites
          )
          .then(() => {
            this.dataService.updateUserPreference(userAccountSetting.userName, userPreferenceUnit).then(() => {
              this.snackBar.open(this.translateService.instant('ACCOUNT_MANAGEMENT.add_user_success'), '', {
                duration: 3000,
              });
              this.dialogRef.close('submit');
            });
          })
          .catch(error => {
            console.log('modify user error=' + JSON.stringify(error));
            if (error.resultId === -102) {
              if (error.message === 'The user is already existing.') {
                this.errorService.handleError(error, this.translateService.instant('ACCOUNT_MANAGEMENT.user_exist'));
              } else {
                this.errorService.handleError(
                  error,
                  this.translateService.instant('ACCOUNT_MANAGEMENT.password_policy_mismatch')
                );
              }
            } else {
              this.errorService.handleError(
                error,
                this.translateService.instant('ACCOUNT_MANAGEMENT.update_user_fail')
              );
            }
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          });
      }
    }
  }
}
