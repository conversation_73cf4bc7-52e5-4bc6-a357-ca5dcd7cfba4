<h3 mat-dialog-title>{{ dialogTitle }}</h3>
<div mat-dialog-content>
  <form [formGroup]="addAccountForm">
    <div fxLayout="row" fxLayout.xs="column">
      <mat-form-field [ngClass]="{ 'form-field-default-gap': addAccountForm.controls['userName'].hasError('pattern') }">
        <input
          id="input-accountName"
          maxlength="32"
          matInput
          #username
          placeholder="{{ 'ACCOUNT_MANAGEMENT.username' | translate }}"
          type="text"
          formControlName="userName"
          required
        />
        <mat-hint align="end">{{ username.value.length }} / 32</mat-hint>
        <mat-error *ngIf="addAccountForm.controls['userName'].hasError('required')">
          {{ 'ADD_DEVICE.field_required' | translate }}</mat-error
        >
        <mat-error *ngIf="addAccountForm.controls['userName'].hasError('pattern')">
          {{ 'ERROR_MESSAGE.input_invalid_characters' | translate }}</mat-error
        >
      </mat-form-field>
    </div>
    <div *ngIf="showOldPasswordInput && isAdminHimself" fxLayout="row" fxLayout.xs="column">
      <mat-form-field
        class="suffix-form-field"
        [ngClass]="{ 'form-field-default-gap': addAccountForm.controls['oldPassword'].hasError('pattern') }"
      >
        <input
          id="input-oldPassword"
          matInput
          maxlength="63"
          #newPassword
          placeholder="{{ 'ACCOUNT_MANAGEMENT.old_password' | translate }}"
          [type]="hidePassword2 ? 'password' : 'text'"
          formControlName="oldPassword"
          required
        />
        <button mat-icon-button type="button" (click)="hidePassword2 = !hidePassword2">
          <mat-icon> {{ hidePassword2 ? 'visibility_off' : 'visibility' }} </mat-icon>
        </button>
        <mat-hint align="end">{{ newPassword.value.length }} / 63</mat-hint>
        <mat-error *ngIf="addAccountForm.controls['oldPassword'].hasError('required')">
          {{ 'ADD_DEVICE.field_required' | translate }}</mat-error
        >
        <mat-error *ngIf="addAccountForm.controls['oldPassword'].hasError('pattern')">
          {{ 'ERROR_MESSAGE.input_invalid_password_characters' | translate }}</mat-error
        >
      </mat-form-field>
    </div>
    <div fxLayout="row" fxLayout.xs="column">
      <mat-form-field
        class="suffix-form-field"
        [ngClass]="{ 'form-field-default-gap': addAccountForm.controls['password'].hasError('pattern') }"
      >
        <input
          id="input-accountPassword"
          maxlength="63"
          matInput
          #newPasswordConfirm
          placeholder="{{ 'ACCOUNT_MANAGEMENT.password' | translate }}"
          [type]="hidePassword1 ? 'password' : 'text'"
          formControlName="password"
          required
        />
        <button mat-icon-button type="button" (click)="hidePassword1 = !hidePassword1">
          <mat-icon> {{ hidePassword1 ? 'visibility_off' : 'visibility' }} </mat-icon>
        </button>
        <mat-hint align="end">{{ newPasswordConfirm.value.length }} / 63</mat-hint>
        <mat-error *ngIf="addAccountForm.controls['password'].hasError('required')">
          {{ 'ADD_DEVICE.field_required' | translate }}</mat-error
        >
        <mat-error *ngIf="addAccountForm.controls['password'].hasError('pattern')">
          {{ 'ERROR_MESSAGE.input_invalid_password_characters' | translate }}</mat-error
        >
      </mat-form-field>
    </div>
    <div fxLayout="row" fxLayout.xs="column">
      <mat-form-field>
        <mat-select
          id="select-accountAuthority"
          placeholder="{{ 'ACCOUNT_MANAGEMENT.authority' | translate }}"
          (selectionChange)="onSelectAuthorityChange($event)"
          formControlName="authority"
          required
        >
          <ng-container *ngFor="let authority of authorityList">
            <mat-option
              *ngIf="dialogData.demoUser || (!dialogData.demoUser && authority.value !== 131076)"
              id="option-authority-{{ authority.displayId }}"
              [id]="authority.displayId"
              [value]="authority.value"
            >
              {{ 'ACCOUNT_MANAGEMENT.' + authority.display | translate }}
            </mat-option>
          </ng-container>
        </mat-select>
      </mat-form-field>
    </div>
    <div *ngIf="!this.onlyDefaultUiProfile && showUiProfile" fxLayout="row" fxLayout.xs="column">
      <mat-form-field>
        <mat-select
          id="select-ui-profile"
          placeholder="{{ 'ACCOUNT_MANAGEMENT.ui_profile' | translate }}"
          formControlName="uiProfile"
          required
        >
          <mat-option *ngFor="let uiProfile of uiProfileList" [id]="uiProfile" [value]="uiProfile.value">
            {{ uiProfile.str }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
  </form>
</div>
<div mat-dialog-actions align="end">
  <button mat-button id="button-add-account-cancel" mat-dialog-close color="primary">
    {{ 'BUTTON.cancel' | translate }}
  </button>
  <button
    mat-raised-button
    id="dialog-button-addUser"
    color="primary"
    (click)="onAddUser()"
    [disabled]="!addAccountForm.valid"
    *ngIf="!dialogData?.data"
  >
    {{ 'BUTTON.add' | translate }}
  </button>
  <button
    mat-raised-button
    id="dialog-button-apply"
    color="primary"
    (click)="onAddUser()"
    [disabled]="!addAccountForm.valid"
    *ngIf="dialogData?.data"
  >
    {{ 'BUTTON.apply' | translate }}
  </button>
</div>
