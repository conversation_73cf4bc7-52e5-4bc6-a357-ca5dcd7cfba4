<h3 mat-dialog-title>
  <span> {{ 'account_password.verift_title' | translate }}</span>
</h3>
<form [formGroup]="form">
  <mat-form-field class="username">
    <input
      matInput
      id="input-username"
      maxlength="32"
      placeholder="{{ 'LOGIN.username' | translate }}"
      formControlName="username"
      required
    />
    <mat-error *ngIf="form.controls['username'].hasError('required')">
      {{ 'ADD_DEVICE.field_required' | translate }}</mat-error
    >
  </mat-form-field>
  <mat-form-field class="password suffix-form-field">
    <input
      matInput
      id="input-password"
      maxlength="63"
      placeholder="{{ 'LOGIN.password' | translate }}"
      formControlName="password"
      [type]="hidePassword ? 'password' : 'text'"
      required
    />
    <button type="button" mat-icon-button type="button" (click)="hidePassword = !hidePassword">
      <mat-icon> {{ hidePassword ? 'visibility_off' : 'visibility' }} </mat-icon>
    </button>
    <mat-error *ngIf="form.controls['password'].hasError('required')">
      {{ 'ADD_DEVICE.field_required' | translate }}</mat-error
    >
  </mat-form-field>
</form>
<div mat-dialog-actions align="end">
  <button mat-button id="dialog-button-verify-cancel" color="primary" mat-dialog-close>
    {{ 'BUTTON.cancel' | translate }}
  </button>
  <button mat-raised-button id="dialog-button-verify" color="primary" [disabled]="form.invalid" (click)="onVerify()">
    {{ 'BUTTON.verify' | translate }}
  </button>
</div>
