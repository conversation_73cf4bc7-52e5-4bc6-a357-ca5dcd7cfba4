import { ChangeDetectionStrategy, Component, OnInit, ViewChild, inject } from '@angular/core';
import { Router } from '@angular/router';
import { MatLegacyDialog as MatDialog } from '@angular/material/legacy-dialog';

import { <PERSON><PERSON><PERSON><PERSON> } from '@ngneat/until-destroy';

import { NetworkComponent } from '@mxview-web/app/pages/network/network.component';

import { AuthService } from '../../../../../shared/Service/auth.service';
import { CheckCommandBarService } from '../../../../../shared/Service/check-command-bar-key.service';
import { GlobalService } from '../../../../../shared/Service/global-services';
import { DataType } from '../../../../../shared/Service/mx-platform/DataDef/DataTypeDefinitions';
import { DeviceUnit } from '../../../../../shared/Service/mx-platform/DataDef/DevicesDataDefs';
import { DataRepositoryService } from '../../../../../shared/Service/mx-platform/DataRepository/DataRepository';
import { DeviceActionDialogComponent } from '../../../../../shared/component/device-action-dialog/containers/index';
import { MxCommandBarComponent } from '../../../../../shared/component/mx-command-bar/mx-command-bar.component';
import { extraLargeDialogConfig } from '../../../../../shared/dialog-config';
import { largeDialogConfig } from '../../../../../shared/dialog-config';
import { ActionMetaType } from '../../../../../shared/model/cli-script';
import { SharedModule } from '../../../../../shared/shared.module';
import { ExecuteCliObjectDialogComponent } from '../../../../network/execute-cli-object/containers';
import { NetworkModule } from '../../../../network/network.module';
import { NetworkService } from '../../../../network/network.service';
import { SerialPortMonitoringComponent } from '../../../../network/serial-port-monitoring/serial-port-monitoring.component';

@UntilDestroy()
@Component({
  imports: [SharedModule, NetworkModule, DeviceActionDialogComponent, ExecuteCliObjectDialogComponent],
  standalone: true,
  template: `
    <div class="app-no-header-content-container">
      <div class="page-function">
        <div class="action-hint dialog-information">
          <mat-icon id="info-hint" class="form-checkbox-help-tip"> information </mat-icon
          ><span>{{ actionHint | translate }}</span>
        </div>
        <app-mx-command-bar
          [parentMenuData]="menuData"
          [parentCommandBarKey]="commandBarKey"
          (buttonClick)="onButtonClick($event)"
        ></app-mx-command-bar>
        <div class="table-container">
          <div class="device-table">
            <app-mx-device-table
              [commandBar]="mxCommandBar"
              [siteId]="''"
              [isFromDeviceManagement]="true"
              (deviceSelected)="onDeviceSelected($event)"
              (deviceMultiSelected)="onDeviceMultiSelected($event)"
              (deviceNotSelected)="onNotSelected()"
            ></app-mx-device-table>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['device-management.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DeviceManagementPageComponent implements OnInit {
  @ViewChild(MxCommandBarComponent) mxCommandBar: MxCommandBarComponent;
  networkService = inject(NetworkService);

  nPort6000Sysobjid = '.*******.4.1.8691.2.8';
  nPort6000G2Sysobjid = '.*******.4.1.8691.2.24';
  nPort5000G2Sysobjid = '.*******.4.1.8691.2.25';
  mGateMB3000Sysobjid = '.*******.4.1.8691.21.3';
  policyProfileSysobjid = '.*******.4.1.8691.6.100.2';
  securityPackageSysobjid = '.*******.4.1.8691.6.100.2';
  onCellG4302LTE4Sysobjid = '.*******.4.1.8691.6.100.2.872480769';
  nat102 = '.*******.4.1.8691.6.100.2.855900161';
  ipcOids = [
    '.*******.4.1.8691.16.1.6814', // DA681C (Windows)
    '.*******.4.1.8691.16.1.6824', // DA682C (Windows)
    '.*******.4.1.8691.16.720', // DA720 (Windows)
    '.*******.4.1.8691.16.74001', // MC7400 (Windows)
    '.*******.4.1.8691.16.1.8204', // DA820C (Windows)
    '.*******.4.1.8691.16.3.24064', // V2406C (Windows)
    '.*******.4.1.8691.12.1.88', // V3210-TL1-4L-T
    '.*******.4.1.8691.12.1.89', // V3210-TL1-8L-T
    '.*******.4.1.8691.12.1.90', // V3210-TL1-8L-CT-T
    '.*******.4.1.8691.12.1.91', // V3210-TL1-8L-CT-T
    '.*******.4.1.8691.12.1.92', // V3210-TL3-4L-T
    '.*******.4.1.8691.12.1.93', // V3210-TL3-8L-T
    '.*******.4.1.8691.12.1.94', // V3210-TL3-4L-CT-T
    '.*******.4.1.8691.12.1.95', // V3210-TL3-8L-CT-T
    '.*******.4.1.8691.12.1.96', // V3210-TL5-4L-T
    '.*******.4.1.8691.12.1.97', // V3210-TL5-8L-T
    '.*******.4.1.8691.12.1.98', // V3210-TL5-4L-CT-T
    '.*******.4.1.8691.12.1.99', // V3210-TL5-8L-CT-T
    '.*******.4.1.8691.12.1.100', // V3210-TL7-4L-T
    '.*******.4.1.8691.12.1.101', // V3210-TL7-8L-T
    '.*******.4.1.8691.12.1.102', // V3210-TL7-4L-CT-T
    '.*******.4.1.8691.12.1.103', // V3210-TL7-8L-CT-T
    '.*******.4.1.8691.12.1.104', // V3210-TL7-8L-TSN-CT-T
    '.*******.4.1.8691.12.1.105', // V3404-TL1-W-CT-T
    '.*******.4.1.8691.12.1.106', // V3408-TL1-W-CT-T
    '.*******.4.1.8691.12.1.107', // V3404-TL3-W-CT-T
    '.*******.4.1.8691.12.1.108', // V3408-TL3-W-CT-T
    '.*******.4.1.8691.12.1.109', // V3404-TL5-W-CT-T
    '.*******.4.1.8691.12.1.110', // V3408-TL5-W-CT-T
    '.*******.4.1.8691.12.1.111', // V3404-TL7-W-CT-T
    '.*******.4.1.8691.12.1.112' // V3408-TL7-W-CT-T
  ];

  commandBarKey = 'menuForDeviceManagementTitle';
  menuData = { ...this.auth.menuData };
  actionHint = '';
  selectedDevices: DeviceUnit[] = [];

  updateMenuKey() {
    this.commandBarKey = 'menuForDeviceManagement';
    const sysobjidArray = this.selectedDevices.map(d => d.sysobjid);
    const isNam = this.globalService.siteInfo.now.addons.includes('nsm');
    const menuKey = [
      ...this.checkCommandBarService.findMenuKey(sysobjidArray),
      ActionMetaType.CLI_INTERFACE,
      ActionMetaType.EXECUTE_CLI_OBJECT,
      sysobjidArray.every(device => device === sysobjidArray[0]) &&
      sysobjidArray.every(
        s =>
          s.includes(this.nPort6000Sysobjid) ||
          s.includes(this.nPort6000G2Sysobjid) ||
          s.includes(this.nPort5000G2Sysobjid) ||
          s.includes(this.mGateMB3000Sysobjid)
      )
        ? 'openSerialPortMonitoring'
        : '',
      isNam &&
      sysobjidArray.every(s => s.includes(this.policyProfileSysobjid)) &&
      !sysobjidArray.some(s => s.includes(this.onCellG4302LTE4Sysobjid) || s.includes(this.nat102))
        ? 'policyProfileDeployment'
        : '',
      isNam &&
      sysobjidArray.every(s => s.includes(this.securityPackageSysobjid)) &&
      !sysobjidArray.some(s => s.includes(this.onCellG4302LTE4Sysobjid) || s.includes(this.nat102))
        ? 'securityPackageDeployment'
        : ''
    ];
    let tempKey = this.isComputer() ? 'menuForIpcDeviceManagement' : 'menuForDeviceManagement';
    tempKey = this.getIopacMenuKey() || tempKey;
    const menuForDeviceManagement = [...this.auth.menuData[tempKey]];
    const filteredMenu = menuForDeviceManagement.map(menu => {
      const filteredMenuData = menu.menuData?.filter(item => menuKey.includes(item.click));
      let newMenu = { ...menu };
      if (filteredMenuData?.length !== 0) {
        newMenu['menuData'] = filteredMenuData;
      } else {
        newMenu = {
          title: menu.title,
          icon: menu.icon,
          click: 'close'
        };
      }
      return newMenu;
    });
    if (this.selectedDevices.length === 1) {
      filteredMenu.push(...this.auth.menuData['wenConsoleMenu']);
    }
    const iopacMenu = tempKey.startsWith('iopac') ? menuForDeviceManagement : filteredMenu
    this.commandBarKey = 'menuForDeviceManagement';
    this.menuData = { menuForDeviceManagement: iopacMenu };
  }

  onButtonClick(name: string) {
    if (this.selectedDevices.length === 0 || name === 'close') {
      return;
    }
    switch (name) {
      case 'openWebConsole':
        this.globalService.openWebConsole(this.selectedDevices[0].site_id, this.selectedDevices[0].ip);
        break;
      case ActionMetaType.CLI_INTERFACE:
      case ActionMetaType.EXECUTE_CLI_OBJECT:
        this.dialog.open(ExecuteCliObjectDialogComponent, {
          ...extraLargeDialogConfig,
          data: {
            dialogAction: name,
            devices: this.selectedDevices
          }
        });
        break;
      case 'openSerialPortMonitoring':
        this.dialog.open(SerialPortMonitoringComponent, { ...largeDialogConfig, width: '1000px' });
        break;
      case ActionMetaType.POLICY_PROFILE_DEPLOYMENT:
        this.router.navigate(['/pages/firewall-policy-management/policy-profile-deployment'], {
          queryParams: {
            mac: this.networkService.selectedDevice
              ? this.networkService.selectedDevice.mac
              : this.networkService.selectedDevices.map(d => d.mac).join('&')
          }
        });
        break;
      case ActionMetaType.SECURITY_PACKAGE_DEPLOYMENT:
        this.router.navigate(['/pages/firewall-policy-management/security-package-deployment'], {
          queryParams: {
            mac: this.networkService.selectedDevice
              ? this.networkService.selectedDevice.mac
              : this.networkService.selectedDevices.map(d => d.mac).join('&')
          }
        });
        break;
      default:
        this.dialog.open(DeviceActionDialogComponent, {
          ...extraLargeDialogConfig,
          data: {
            dialogAction: name,
            devices: this.selectedDevices,
            allDeviceList: this.dataRepository.getData(DataType.DEVICE)
          }
        });
    }
  }

  changeActionHint() {
    const deviceSelected = this.selectedDevices.length !== 0;
    if (deviceSelected) {
      this.updateMenuKey();
    } else {
      this.commandBarKey = 'menuForDeviceManagementTitle';
      this.menuData = { ...this.auth.menuData };
    }
    this.actionHint = deviceSelected ? 'device_management.select_operation' : 'device_management.select_device';
  }

  onDeviceSelected(data: { device: DeviceUnit }) {
    this.selectedDevices = [data.device];
    this.networkService.selectedDevice = data.device;
    this.networkService.selectedDevices = [];
    this.changeActionHint();
  }

  onDeviceMultiSelected(data: DeviceUnit[]) {
    this.selectedDevices = data;
    this.networkService.selectedDevices = data;
    this.networkService.selectedDevice = null;
    this.changeActionHint();
  }

  onNotSelected() {
    this.selectedDevices = [];
    this.changeActionHint();
  }

  isComputer() {
    const computerModels = [
      'DA820',
      'UC-2101',
      'UC-2102',
      'UC-2104',
      'UC-2111',
      'UC-2112',
      'UC-2114',
      'UC-2116',
      'UC-1222A',
      'UC-2222A-T',
      'UC-2222A-T-AP',
      'UC-2222A-T-CN',
      'UC-2222A-T-EU',
      'UC-2222A-T-US',
      'V2406C'
    ];

    // Check if the model includes any of the computer model strings
    if (this.networkService.selectedDevice) {
      const model = this.networkService.selectedDevice.model;
      const isComputerModel = computerModels.some(computerModel => model.includes(computerModel));
      const isIpcOid = this.ipcOids.some(oid => oid.includes(this.networkService.selectedDevice.sysobjid));

      return isComputerModel || isIpcOid;
    } else if (this.networkService.selectedDevices.length > 0) {
      const isComputerModel = this.networkService.selectedDevices.some(device =>
        computerModels.some(computerModel => device.model.includes(computerModel))
      );
      const isIpcOid = this.networkService.selectedDevices.some(device =>
        this.ipcOids.some(oid => oid.includes(device.sysobjid))
      );

      return isComputerModel || isIpcOid;
    }

    return false;
  }

  getIopacMenuKey() {
    const cpuSysobjid = '.*******.4.1.8691.10.6500';
    const switchSysobjid = '.*******.4.1.8691.600.1.12.1';

    if (this.networkService.selectedDevice) {
      const selectedDeviceSysobjid = this.networkService.selectedDevice.sysobjid;
      if (selectedDeviceSysobjid?.includes(cpuSysobjid)) {
        this.commandBarKey = 'iopacCpuMenu';
      } else if (selectedDeviceSysobjid?.includes(switchSysobjid)) {
        this.commandBarKey = 'iopacSwitchMenu';
      }
    } else if (this.networkService.selectedDevices.length > 0) {
      const selectedDeviceSysobjid = this.networkService.selectedDevices[0].sysobjid;
      if (selectedDeviceSysobjid?.includes(cpuSysobjid)) {
        this.commandBarKey = 'iopacCpuMenu';
      } else if (selectedDeviceSysobjid?.includes(switchSysobjid)) {
        this.commandBarKey = 'iopacSwitchMenu';
      }
    }

    return this.commandBarKey;
  }

  ngOnInit(): void {
    this.changeActionHint();
  }

  constructor(
    private auth: AuthService,
    private dialog: MatDialog,
    private router: Router,
    private checkCommandBarService: CheckCommandBarService,
    public globalService: GlobalService,
    public dataRepository: DataRepositoryService
  ) {}
}
