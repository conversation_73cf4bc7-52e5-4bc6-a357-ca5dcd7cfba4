import {
  <PERSON><PERSON><PERSON>,
  ChangeDetectionStrategy,
  ChangeDetector<PERSON>ef,
  Component,
  OnDestroy,
  OnInit,
  ViewChild,
  ElementRef,
  AfterViewInit,
} from '@angular/core';
import { Router } from '@angular/router';

import { TranslateService } from '@ngx-translate/core';
import { DisplayGrid, GridType, GridsterConfig, GridsterItem, GridsterItemComponentInterface } from 'angular-gridster2';
import * as _ from 'lodash';
import { DateTime } from 'luxon';
import * as moment from 'moment';
import { Subject } from 'rxjs';

import { GlobalEvent, GlobalEventType } from '../../../global.event';
import { AuthService as NSMAuthService } from '../../../shared/auth/auth.service';
import { PostService } from '../../../shared/http/post.service';
import { SharedService } from '../../../shared/shared.service';
import { EventLogCategory, FirewallLogTable } from '../../event/event.model';
import { EventTabIndex } from '../../event/event.model';
import { FirewallLogOption } from '../../event/security-event/security-event.model';
import { License, LicenseCategory } from '../../firewall-policy-management/license.model';
import { License as IpsLicense} from '../../license/models/license.model'
import {
  ChartName,
  ChartStatus,
  ChartTimelineInterface,
  ChartUnit,
  defaultChartMap,
  FirewallTableKey,
  GetLogStaticInfoResponse,
  StaticLogInfoKey,
} from '../dashboard.model';
import { AppState } from './../../../app.service';

@Component({
  selector: 'app-security-summary',
  templateUrl: './security-summary.component.html',
  styleUrls: ['./security-summary.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SecuritySummaryComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('groupStatusChart') groupStatusChart: ElementRef;

  transPage = 'pages.dashboard.';
  gridsterOptions: GridsterConfig;
  isEditMode = false;
  dashboardChartMap = new Map<number, ChartUnit[]>();
  chartDataMap = new Map<string, { [key: string]: any }[]>();
  chartTimelineDataMap = new Map<string, any>();
  chartStatusMap = new Map<string, ChartStatus>();
  chartName = ChartName;
  colorScheme: any = {};
  loader = {};
  isMainLicenseValid: boolean = this._authService.isMainLicenseValid.getValue();

  selectedFirewallLog: FirewallLogOption = {
    key: FirewallLogTable.IPS.toUpperCase(),
    value: FirewallLogTable.IPS,
  };
  firewallLogTable = FirewallLogTable;

  selectedTimeByType = new Map<string, any>(
    [
      ChartName.FirewallLogByTimeline,
      ChartName.AuditLogByTimeline,
      ChartName.DeviceLogByTimeline,
      ChartName.VPNLogByTimeline,
    ].map(item => [item, { value: 24, label: 'Past 24 hours' }])
  );
  timeOptions = [
    { value: 24, label: 'Past 24 hours' },
    {
      value: 30 * 24,
      label: 'Past 30 days',
    },
  ];

  securitySummaryLastUpdate: string;
  dashboard = [
    {
      view: [520, 220],
      firewallColumn: ChartName.L3SrcIp,
      firewallKey: FirewallTableKey.L3POLICY,
      key: ChartName.L3SrcIp,
      name: this.translate.instant('DASHBOARD.l3SrcIp'),
      redirectUrl: '/pages/event/all-event',
      noDataOverlay: true,
    },
    {
      view: [520, 220],
      firewallColumn: ChartName.L3DestIp,
      firewallKey: FirewallTableKey.L3POLICY,
      key: ChartName.L3DestIp,
      name: this.translate.instant('DASHBOARD.l3DestIp'),
      redirectUrl: '/pages/event/all-event',
      noDataOverlay: true,
    },
    {
      view: [520, 220],
      firewallColumn: ChartName.IpsSrcIp,
      firewallKey: FirewallTableKey.IPS,
      key: ChartName.IpsSrcIp,
      name: this.translate.instant('DASHBOARD.ipsSrcIp'),
      redirectUrl: '/pages/event/all-event',
      noDataOverlay: true,
    },
    {
      view: [520, 220],
      firewallColumn: ChartName.IpsDestIp,
      firewallKey: FirewallTableKey.IPS,
      key: ChartName.IpsDestIp,
      name: this.translate.instant('DASHBOARD.ipsDestIp'),
      redirectUrl: '/pages/event/all-event',
      noDataOverlay: true,
    },
    {
      view: [520, 220],
      firewallColumn: ChartName.DpiSrcIp,
      firewallKey: FirewallTableKey.DPI,
      key: ChartName.DpiSrcIp,
      name: this.translate.instant('DASHBOARD.dpiSrcIp'),
      redirectUrl: '/pages/event/all-event',
      noDataOverlay: true,
    },
    {
      view: [520, 220],
      firewallColumn: ChartName.DpiDestIp,
      firewallKey: FirewallTableKey.DPI,
      key: ChartName.DpiDestIp,
      name: this.translate.instant('DASHBOARD.dpiDestIp'),
      redirectUrl: '/pages/event/all-event',
      noDataOverlay: true,
    },
    {
      view: [520, 220],
      firewallColumn: ChartName.AdpSrcIp,
      firewallKey: FirewallTableKey.ADP,
      key: ChartName.AdpSrcIp,
      name: this.translate.instant('DASHBOARD.adpSrcIp'),
      redirectUrl: '/pages/event/all-event',
      noDataOverlay: true,
    },
    {
      view: [520, 220],
      firewallColumn: ChartName.AdpDestIp,
      firewallKey: FirewallTableKey.ADP,
      key: ChartName.AdpDestIp,
      name: this.translate.instant('DASHBOARD.adpDestIp'),
      redirectUrl: '/pages/event/all-event',
      noDataOverlay: true,
    },
  ];

  overlayStyle = {
    width: '97.5%',
    height: '288px',
  };

  private _notifyDestroy = new Subject<void>();
  private _refreshInterval: any;
  private _refreshTimeGap: number = 30 * 1000;
  private _activateDashboard = 1;
  private _fixedColWidth = 200;
  private _fixedRowHeight = 80;
  private _gridsterMargin = 20;
  private _licenseOrder = [LicenseCategory.NSM, LicenseCategory.IPS];
  private _FirewallLogChartArray = [
    ChartName.L3Severity,
    ChartName.L3SrcIp,
    ChartName.L3DestIp,
    ChartName.DpiSeverity,
    ChartName.DpiSrcIp,
    ChartName.DpiDestIp,
    ChartName.AdpSrcIp,
    ChartName.AdpDestIp,
    ChartName.IpsSeverity,
    ChartName.IpsSrcIp,
    ChartName.IpsDestIp,
    ChartName.IpsCategory,
  ];
  private _timelineChartArray = [
    ChartName.FirewallLogByTimeline,
    ChartName.AuditLogByTimeline,
    ChartName.DeviceLogByTimeline,
    ChartName.VPNLogByTimeline,
  ];

  sharedService = this._sharedService;

  constructor(
    private appState: AppState,
    // private auth: AuthService,
    private cdr: ChangeDetectorRef,
    // private dataRepository: DataRepositoryService,
    // private dataService: DataService,
    private router: Router,
    private translate: TranslateService,
    // public globalService: GlobalService,
    // private errorService: ErrorService,
    private _sharedService: SharedService,
    private _authService: NSMAuthService,
    private _postService: PostService,
    private _zone: NgZone
  ) {
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
  }

  get FirewallTableKey() {
    return FirewallTableKey;
  }

  get EventTabIndex() {
    return EventTabIndex;
  }

  getChartLists(): void {
    this.colorScheme = {
      domain: this._sharedService.colors,
    };

    this._postService.sendGet('dashboards').subscribe(data => {
      if (data.length === 0) {
        this.updateDashboardChartMap({ dashboardId: 1 });
      } else {
        data.forEach(item => {
          this.updateDashboardChartMap(item);
        });
      }
      this.cdr.markForCheck();
    });
  }

  /**
   * 設定自動更新的計時器
   */
  setRefreshTimer(): void {
    this._zone.runOutsideAngular(() => {
      this._refreshInterval = setInterval(() => {
        this.getAllChartData(true);
      }, this._refreshTimeGap);
    });
  }

  getAllChartData(skipLoading?: boolean): void {
    this.dashboardChartMap.get(this._activateDashboard)?.forEach(data => {
      this.loader[data.key] = !skipLoading;
      this.cdr.markForCheck();
      if (this.chartStatusMap.get(data.key) === ChartStatus.Waiting) {
        return;
      } else {
        this.chartStatusMap.set(data.key, ChartStatus.Waiting);
      }
      switch (data.key) {
        // case ChartName.GroupStatus:
        //   this.getGroupStatus();
        //   break;
        case ChartName.CellularConnection:
          this.getCellularConnection();
          break;
        case ChartName.ConnectionType:
          this.getConnectionType();
          break;
        case ChartName.SystemStatus:
          this.getSystemStatus();
          break;
        case ChartName.LicenseUsage:
          this.getLicenseUsage();
          break;
        case ChartName.L3SrcIp:
        case ChartName.L3DestIp:
        case ChartName.DpiSrcIp:
        case ChartName.DpiDestIp:
        case ChartName.AdpSrcIp:
        case ChartName.AdpDestIp:
        case ChartName.IpsSrcIp:
        case ChartName.IpsDestIp:
          this.getFirewallLogs(data.key);
          break;
        case ChartName.FirewallLogByTimeline:
          this.selectedTimeByType.set(
            data.key,
            this.timeOptions.find(item => item.value === data.time)
          );
          this.selectedFirewallLog = {
            key: data.firewallEventType.toUpperCase(),
            value: data.firewallEventType,
          };
          this.getTimelineLogs(data.key);
          break;
        case ChartName.AuditLogByTimeline:
        case ChartName.DeviceLogByTimeline:
        case ChartName.VPNLogByTimeline:
          this.selectedTimeByType.set(
            data.key,
            this.timeOptions.find(item => item.value === data.time)
          );
          this.getTimelineLogs(data.key);
          break;
      }
    });

    this.securitySummaryLastUpdate = moment().format('YYYY-MM-DD HH:mm:ss');
  }

  getConnectionType(): void {
    this._postService.sendGetWithParma('groups', { lite: true, mode: 'wan' }, true).subscribe(data => {
      const total = data.map(item => item.value).reduce((sum: number, item: number) => sum + item, 0);

      this.chartDataMap.set(
        ChartName.ConnectionType,
        data.map(item => {
          return {
            name: this.translate.instant(this.transPage + ChartName.ConnectionType + '.' + item.name),
            value: item.value,
            extra: {
              total: total,
              unit: this.translate.instant('general.unit.percent'),
            },
          };
        })
      );
      this.reloadChart(ChartName.ConnectionType);
    });
  }

  getCellularConnection(): void {
    this._postService.sendGetWithParma('groups', { lite: true, mode: 'quality' }, true).subscribe(data => {
      const total = data.map(item => item.value).reduce((sum: number, item: number) => sum + item, 0);

      this.chartDataMap.set(
        ChartName.CellularConnection,
        data.map(item => {
          return {
            name: this.translate.instant(this.transPage + ChartName.CellularConnection + '.' + item.name),
            value: item.value,
            extra: {
              total: total,
              unit: this.translate.instant('general.unit.percent'),
            },
          };
        })
      );
      this.reloadChart(ChartName.CellularConnection);
    });
  }

  getSystemStatus(): void {
    this._postService.sendGet('system/status').subscribe(data => {
      this.chartDataMap.set(
        ChartName.SystemStatus,
        data.map(item => {
          return {
            name: this.translate.instant(this.transPage + ChartName.SystemStatus + '.' + item.name),
            value: item.used / item.total,
            extra: {
              total: item.total,
              used: item.used,
              unit: item.unit === '' ? '' : ' ' + item.unit,
            },
          };
        })
      );
      this.reloadChart(ChartName.SystemStatus);
    });
  }

  getLicenseUsage(): void {
    this._postService.sendGet('licenses/overview').subscribe(data => {
      const licenseUsageData = [];
      this._licenseOrder.forEach(item => {
        const licenseObj = data.find(x => x.category === item);
        let categoryName = this.translate.instant('pages.system.license.nsm');
        if (licenseObj) {
          if (licenseObj.category === LicenseCategory.NSM) {
            this._authService.nsmLicense = new License(
              LicenseCategory.NSM,
              licenseObj.overview.status,
              licenseObj.overview.action
            );
          } else if (licenseObj.category === LicenseCategory.IPS) {
            categoryName = licenseObj.category;
            this._authService.ipsLicense = new IpsLicense(
              LicenseCategory.IPS,
              licenseObj.overview.status,
              licenseObj.overview.action
            );
          }
          licenseUsageData.push({
            name: categoryName,
            value: licenseObj.overview.usedNode,
            extra: {
              total: licenseObj.overview.node,
            },
          });
        }
      });
      this.chartDataMap.set(ChartName.LicenseUsage, licenseUsageData);
      this.reloadChart(ChartName.LicenseUsage);
    });
  }

  getFirewallLogs(chart: ChartName): void {
    this._postService
      .sendGetWithParma(
        'logs/top',
        {
          table: defaultChartMap.get(chart)?.firewallKey,
          column: defaultChartMap.get(chart)?.firewallColumn,
        },
        true
      )
      .subscribe(data => {
        this.dashboardChartMap.set(
          this._activateDashboard,
          this.dashboardChartMap.get(this._activateDashboard).map(dashboard => {
            if (dashboard.key !== chart) return dashboard;

            return {
              ...dashboard,
              noDataOverlay: data.length === 0,
            } as ChartUnit;
          })
        );
        this.chartDataMap.set(chart, data);
        this.reloadChart(chart);
      });
  }

  /**
   * 取得時間軸的 log 資料
   * @param chart ChartName
   */
  getTimelineLogs(chart: ChartName): void {
    const param: any = {
      category: defaultChartMap.get(chart)?.staticLogInfoCategory,
      time: this.selectedTimeByType.get(chart)?.value,
    };
    if (chart === ChartName.FirewallLogByTimeline) {
      param.type = this.selectedFirewallLog.key;
    }

    this._postService
      .sendGetWithParmaHaveInterface<GetLogStaticInfoResponse>('logs/statistic', param, true)
      .subscribe(data => {
        const chartDisplayData = {
          name: '',
          series: data.data.map(seriesData => {
            let time;
            if (param.time === 24) {
              time = this._sharedService.transHourMin(seriesData.time);
            } else {
              time = this._sharedService.transDateHourMinute(seriesData.time);
            }
            return {
              name: time,
              value: seriesData.count,
              data: seriesData,
              timeZone: param.time === 24 ? 'hour' : 'day',
            };
          }),
        };
        this.chartTimelineDataMap.set(chart, [chartDisplayData]);
        this.reloadChart(chart);
      });
  }

  setDefaultDashboard(): void {
    this._postService
      .sendPost('dashboards', {
        widgetList: [...defaultChartMap.values()]
          .filter(data => data.key === ChartName.SystemStatus || data.key === ChartName.LicenseUsage)
          .map(data => {
            return {
              key: data.key,
              grid: {
                ...data.grid,
                x: data.key === ChartName.LicenseUsage ? 3 : 0,
                y: 0,
              },
            };
          }),
      })
      .subscribe(data => {
        this.updateDashboardChartMap(data);
      });
  }

  updateDashboardChartMap(item: { [key: string]: any }): void {
    this.dashboardChartMap.set(
      item.dashboardId,
      this.dashboard.map(data => {
        return {
          key: data.key,
          view: data.view as [number, number],
          firewallKey: data.firewallKey,
          firewallColumn: data.firewallColumn,
          name: this.translate.instant(data.name),
          redirectUrl: data.redirectUrl,
          noDataOverlay: data.noDataOverlay,
        } as ChartUnit;
      })
    );

    this._activateDashboard = item.dashboardId;
    this.getAllChartData();
  }

  updateDashboardSetting(dashboardId: number): void {
    const timelineChartArray = [
      ChartName.AuditLogByTimeline,
      ChartName.DeviceLogByTimeline,
      ChartName.VPNLogByTimeline,
      ChartName.FirewallLogByTimeline,
    ];

    const updatedList = this.dashboardChartMap.get(this._activateDashboard).map(data => {
      if (timelineChartArray.includes(data.key)) {
        return {
          ...data,
          key: data.key,
          grid: data.grid,
        };
      } else {
        return {
          key: data.key,
          grid: data.grid,
        };
      }
    });

    this._postService
      .sendPut('dashboards/' + dashboardId, {
        widgetList: updatedList,
      })
      .subscribe(data => this.updateDashboardChartMap(data));
  }

  deleteDashboardById(dashboardId: number): void {
    this._postService.deletePosts('dashboards/' + dashboardId).subscribe();
  }

  reloadChart(chart: ChartName): void {
    this.chartStatusMap.set(chart, ChartStatus.Success);
    this.cdr.markForCheck();
    setTimeout(() => {
      this.loader[chart] = false;
    }, 1000);
  }

  /* gridster */
  removeItem($event: MouseEvent | TouchEvent, item: ChartUnit): void {
    $event.preventDefault();
    $event.stopPropagation();
    this.dashboardChartMap
      .get(this._activateDashboard)
      .splice(this.dashboardChartMap.get(this._activateDashboard).indexOf(item), 1);
  }

  toggleEditMode(edit: boolean): void {
    clearInterval(this._refreshInterval);
    this.isEditMode = edit;
    this.gridsterOptions.displayGrid = edit ? DisplayGrid.Always : DisplayGrid.None;
    this.gridsterOptions.resizable.enabled = edit;
    this.gridsterOptions.draggable.enabled = edit;
    if (this.gridsterOptions.api && this.gridsterOptions.api.optionsChanged) {
      this.gridsterOptions.api.optionsChanged();
    }
    if (!edit) {
      this.setRefreshTimer();
      this.updateDashboardSetting(this._activateDashboard);
    }
  }

  redirect(
    url: string,
    key?: FirewallTableKey,
    column?: ChartName,
    tab?: EventTabIndex,
    search?: string | number
  ): void {
    let filter = '';

    if (column) filter = column.includes('SrcIp') ? 'srcIp' : 'dstIp';
    const endTime = moment().unix();
    const startTime = moment().subtract(24, 'hours').unix();

    this.router.navigate([url], { queryParams: { key, tab, search, column: filter, startTime, endTime } });
  }

  redirectTimeLine(
    url: string,
    eventLogTabKey?: EventLogCategory,
    key?: StaticLogInfoKey,
    value?: ChartTimelineInterface
  ) {
    let startDate;
    let startTime;
    let endDate;
    let endTime;
    if (value.timeZone === 'day') {
      const timeAdd = DateTime.fromISO(value.data.time).plus({ hours: 32 });
      startDate = this._sharedService.transDate(value.data.time);
      startTime = this._sharedService.transHourMin(value.data.time);
      endDate = this._sharedService.transDate(timeAdd.toISODate());
      endTime = this._sharedService.transHourMin(timeAdd.toISODate());
    }

    if (value.timeZone === 'hour') {
      startDate = this._sharedService.transDate(DateTime.fromISO(value.data.time).toISODate());
      startTime = this._sharedService.transHourMin(value.data.time);
      endDate = this._sharedService.transDate(DateTime.fromISO(value.data.time).plus({ hours: 1 }).toISODate());
      endTime = DateTime.fromISO(value.data.time).plus({ hours: 1 }).toFormat('HH:mm');
    }

    this.router.navigate([
      url,
      { tab: eventLogTabKey, key: key?.toUpperCase(), startDate, startTime, endDate, endTime },
    ]);
  }

  setGroupStatusLabel(name: string): string {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const self: any = this;
    const data = self.series.find(x => x.name === name);
    if (data) {
      return `${data.name}: ${data.extra.online} / ${data.value}`;
    } else {
      return name;
    }
  }

  setPercentLabel(name: string): string {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const self: any = this;
    const data = self.series?.find(x => x.name === name);
    if (data) {
      return `${data.name}: ${Math.round((100 * data.value) / data.extra.total)} ${data.extra.unit} (${data.value} / ${
        data.extra.total
      })`;
    } else {
      return name;
    }
  }

  /**
   * 更新 firewall event log 種類
   */
  onFirewallLogMenuClick(type: { key: string; value: FirewallLogTable }, chartName: ChartName): void {
    this.selectedFirewallLog = type;
    this.loader[chartName] = true;
    this.getTimelineLogs(chartName);

    const updatedList = this.dashboardChartMap.get(this._activateDashboard);
    updatedList.map(data => {
      if (data.key === chartName) {
        data.firewallEventType = type.value;
      }
    });
    this._postService
      .sendPut(
        'dashboards/' + this._activateDashboard,
        {
          widgetList: updatedList,
        },
        true
      )
      .subscribe();
  }

  /**
   * 更新時間區間
   */
  onTimeOptionClick(time, chartName: ChartName): void {
    this.selectedTimeByType.set(chartName, time);
    this.loader[chartName] = true;
    this.getTimelineLogs(chartName);

    const updatedList = this.dashboardChartMap.get(this._activateDashboard);
    updatedList.map(data => {
      if (data.key === chartName) {
        data.time = time.value;
      }
    });
    this._postService
      .sendPut(
        'dashboards/' + this._activateDashboard,
        {
          widgetList: updatedList,
        },
        true
      )
      .subscribe();
  }

  setLicenseUsageValue(obj: { [key: string]: any }): string {
    return `${obj.value} / ${obj.data.extra.total}`;
  }

  isFirewallChart(chartName: ChartName): boolean {
    return this._FirewallLogChartArray.includes(chartName);
  }

  isTimelineChart(chartName: ChartName): boolean {
    return this._timelineChartArray.includes(chartName);
  }

  onClickBar(event: { [key: string]: string | number }, key?: FirewallTableKey, column?: ChartName): void {
    this.redirect('pages/event/all-event', key, column, EventTabIndex.Cybersecurity, event.name);
  }

  refresh(): void {
    this.chartDataMap.clear();
    this.getAllChartData();
  }

  xAxisFormat(value: string): string {
    if (+value % 1 > 0) return '';
    return value;
  }

  ngOnInit(): void {
    this.getChartLists();
  }

  ngAfterViewInit(): void {
    this.setRefreshTimer();
  }

  ngOnDestroy(): void {
    clearInterval(this._refreshInterval);
    this._notifyDestroy.next();
    this._notifyDestroy.complete();
  }
}
