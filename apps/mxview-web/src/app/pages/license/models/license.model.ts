import { Devices } from "../../firewall-policy-management/device-group/device-group.model";


export interface LicenseList {
  name: LicenseCategory | string;
  duration: number | string;
  startTime: string;
  endTime: string;
  updatedTime: string;
  node: number | string; // total node
  licenseNode: number | string;
  usedNode: number | string;
  status: LicenseStatus;
  statusRow?: LicenseStatus | string;
  type: LicenseType | string;
  typeRow?: LicenseType | string;
  activationCode: string;
  isOverview: boolean | string;
  version: number;
  elapsed: number;
  isCustomizedLicense: boolean;
}

export interface LicenseDevices extends Devices {
  licenseStatus: LicenseStatus | string;
  isValidIPSDevice: boolean;
}

export interface DeviceLicenseUnit {
  category: string;
  endTime: number;
  status: LicenseStatus;
}

export enum LicenseCategory {
  NSM = 'NSM',
  IPS = 'IPS',
  IPS_MGMT = 'IPS-MGMT',
  DPI = 'DPI',
  LICENSE_TRANSFER = 'LICENSE_TRANSFER'
}

export enum LicenseType {
  New = 'NEW',
  Trial = 'TRIAL',
  Cv = 'CV'
}

export enum LicenseStatus {
  Valid = 'Valid',
  RunOut = 'Run-out',
  Expired = 'Expired',
  NotActivated = 'Not Activated',
  Upcoming = 'Upcoming',
  Reclaimed = 'Reclaimed',
  Depleted = 'Depleted',
  Deactivated = 'Deactivated',
  NoLicense = 'No License'
}

export enum LicenseAction {
  Activate = 'activate',
  Deactivate = 'deactivate',
  Rebind = 'rebind',
  ClearRebind = 'clear_rebind'
}

export enum LicenseRemarkType {
  Expired = 'expired',
  Expiring = 'expiring',
  Insufficient = 'insufficient'
}

export enum LicenseDateType {
  Permanent = 'Permanent'
}

export class License {
  constructor(
    private _category: LicenseCategory,
    private _status: LicenseStatus,
    private _action: LicenseAction | ''
  ) {}

  get category(): LicenseCategory {
    return this._category;
  }

  get status(): LicenseStatus {
    return this._status;
  }

  get action(): LicenseAction {
    return <LicenseAction>this._action;
  }
}

export interface DisableDeactivateButton {
  depleted: boolean;
  deactivated: boolean;
  noLicense: boolean;
}
