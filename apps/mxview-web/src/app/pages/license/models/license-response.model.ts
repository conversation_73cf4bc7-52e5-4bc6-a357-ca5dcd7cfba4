import { LicenseAction, LicenseStatus } from "./license.model";

export interface LicenseResponse {
  data: LicenseResponseData[];
  serverId: string;
}

export interface LicenseResponseData {
  category: string;
  list: Overview[];
  overview: Overview;
}

export interface Overview {
  action: LicenseAction;
  activationCode: string;
  category: string;
  createdAt: Date;
  description: string;
  duration: number;
  elapsed: number;
  endAt: Date;
  endTime: number;
  isOverview: boolean;
  licenseId: number;
  licenseNode: number;
  node: number;
  remark: string[];
  startAt: Date;
  startTime: number;
  status: LicenseStatus;
  type: string;
  updatedAt: Date;
  updatedTime: number;
  usedNode: number;
  version: 1 | 2 | 3;
}
