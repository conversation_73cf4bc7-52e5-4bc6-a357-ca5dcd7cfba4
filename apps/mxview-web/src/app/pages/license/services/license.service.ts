import { Injectable } from '@angular/core';

import { Observable, Subject } from 'rxjs';
import { LicenseResponse } from '../models/license-response.model';
import { SystemSettingsTime } from '../models/time.model';


@Injectable({
  providedIn: 'root'
})
export class LicenseService {
  licenseStore = new Subject<LicenseResponse>();
  systemInfo = new Subject<SystemSettingsTime>();

  /**
   * 1: Old version
   * 2: New version
   * 3: New version with new features
   * @type {(1 | 2 | 3)}
   * @memberof LicenseService
   */
  ipsVersion: 1 | 2 | 3;

  constructor() {}

  setLicenseData(data: LicenseResponse) {
    this.licenseStore.next(data);
  }

  setIpsVersion(version: 1 | 2 | 3) {
    this.ipsVersion = version;
  }

  setSystemInfo(data: SystemSettingsTime) {
    this.systemInfo.next(data);
  }

  getLicenseData(): Observable<LicenseResponse> {
    return this.licenseStore.asObservable();
  }

  getIpsVersion(): number {
    return this.ipsVersion;
  }

  getSystemInfo(): Observable<SystemSettingsTime> {
    return this.systemInfo.asObservable();
  }
}
