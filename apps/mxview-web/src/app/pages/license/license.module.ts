import { NgModule } from '@angular/core';
import { ClipboardModule } from '@angular/cdk/clipboard';

import { SharedModule } from '../../shared/shared.module';
import { LicenseRoutingModule, routedComponents } from './license.routing';
import * as fromComponents from './components';
import * as fromContainers from './containers';

@NgModule({
  imports: [LicenseRoutingModule, SharedModule, ...fromComponents.standaloneComponents,ClipboardModule ],
  declarations: [
    ...routedComponents,
    ...fromComponents.components,
    ...fromContainers.containers
  ],
})
export class LicenseModule {}
