<app-overview-ips
  [overViewData]="overviewData.get(licenseCategory.IPS)"
  [serverId]="serverId"
  [notification]="notification"
  (reloadLicense)="refreshLicense($event)"
></app-overview-ips>
<app-history-ips
  [serverId]="serverId"
  [overViewData]="overviewData.get(licenseCategory.IPS)"
  [historyData]="historyData.get(licenseCategory.IPS)"
  (refreshLicense)="refreshLicense($event)"
></app-history-ips>
<app-deactivated-licenses [deactivation]="deactivation" [serverId]="serverId" (refresh)="refreshLicense($event)">
</app-deactivated-licenses>
<app-device-license
  [overViewData]="overviewData.get(licenseCategory.IPS)"
  (refreshLicense)="refreshLicense($event)"
></app-device-license>
