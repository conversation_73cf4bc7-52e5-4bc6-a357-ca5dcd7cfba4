import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit, signal, WritableSignal } from "@angular/core";
import { License, LicenseCategory, LicenseList, LicenseStatus, LicenseType } from "../../models/license.model";
import { LicenseNotification } from "../../components/license-notification-dialog/license-notification.model";
import { forkJoin } from "rxjs";
import { PostService } from "@mxview-web/app/shared/http/post.service";
import { LicenseResponse } from "../../models/license-response.model";
import { SystemSettingsTime } from "../../models/time.model";
import { TransferDeactivation } from "../../components/deactivated-licenses/deactivated-licenses.model";
import { LicenseService } from "../../services/license.service";
import { SystemInfo } from "@mxview-web/app/shared/shared.model";
import { AuthService } from "@mxview-web/app/shared/auth/auth.service";
import { TranslateService } from "@ngx-translate/core";
import { SharedService } from "@mxview-web/app/shared/shared.service";
import { getIPSStatus, getLicenseType } from "../../license.helper";

@Component({
  selector: 'app-ips-license-tab',
  templateUrl: './ips-license-tab.component.html',
  styleUrls: ['./ips-license-tab.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class IpsLicenseTabComponent implements OnInit {
  licenseCategory = LicenseCategory;
  overviewData = new Map<string, LicenseList>();
  historyData = new Map<string, LicenseList[]>();
  isNsmRebind: boolean = false;
  serverId: string;
  showLicenseMigrate: WritableSignal<boolean> = signal(false);
  notification: LicenseNotification;
  deactivation: TransferDeactivation[] = [];

  constructor(
    private _postService: PostService,
    private _licenseService: LicenseService,
    private _authService: AuthService,
    private _translate: TranslateService,
    private _sharedService: SharedService,
    private _cdr: ChangeDetectorRef,
  ){}
  ngOnInit(): void {
      this.getAllLicense();
  }

  refreshLicense(data: string | any): void {
    if (data === 'refresh') {
      this.getAllLicense();
    } else {
      this.setLicenseData(data);
    }
  }

  private getAllLicense(): void {
    forkJoin({
      license: this._postService.sendGetHaveInterface<LicenseResponse>('licenses'),
      systemInfo: this._postService.sendGetHaveInterface<SystemSettingsTime>('system/time'),
      notification: this._postService.sendGetHaveInterface<LicenseNotification>('licenses/notification'),
      deactivation: this._postService.sendGetHaveInterface<TransferDeactivation[]>('licenses/transfer/deactivation')
    }).subscribe(({ license, systemInfo, notification, deactivation }) => {
      this.serverId = license.serverId;
      this._licenseService.setSystemInfo(systemInfo);
      this._licenseService.setLicenseData(license);
      this.notification = notification;
      this.deactivation = deactivation;
      this.setLicenseData(license);

      this.showLicenseMigrate.set(
        license.data
          .map(item => item.list)
          .reduce((acc, val) => acc.concat(val), [])
          .filter(item => item.version === 1 || item.version === 2)
          .filter(item => {
            const diffUTC = item.endTime - systemInfo.time;
            const diffDays = Math.floor(diffUTC / (60 * 60 * 24));
            return diffDays > 0;
          }).length > 0
      );

      this.updateSystemInfo();
    });
  }

  private setLicenseData(result: LicenseResponse): void {
    this.historyData.clear();
    this.overviewData.clear();

    const ipsItem = result.data.find(item => item.category === LicenseCategory.IPS);
    if (ipsItem) {
      const overview = ipsItem.overview;
      this._authService.ipsLicense = new License(LicenseCategory.IPS, overview.status, overview.action);
      this._licenseService.setIpsVersion(overview.version);

      this.overviewData.set(ipsItem.category, this.getListData(overview));
      this.historyData.set(
        ipsItem.category,
        ipsItem.list.map(data => this.getListData(data))
      );
    }

    this._cdr.markForCheck();
  }

  private updateSystemInfo(): void {
    this._postService.sendGetHaveInterface<SystemInfo>('system/info').subscribe(data => {
      this._authService.updateSystemInfo(data);
    });
  }

  private setLicenseType(type: LicenseType, status: LicenseStatus, startAt: string): LicenseType | string {
    if (status === LicenseStatus.Upcoming) {
      return type + ' (Effective from ' + this._sharedService.transDate(startAt) + ')';
    } else {
      return type;
    }
  }


  private getListData(data) {
    return {
      name:
        data.category === LicenseCategory.IPS
          ? this._translate.instant(`pages.system.license.ipsMgmt`)
          : this._translate.instant(`pages.system.license.${data.category.toLowerCase()}`),
      duration: data.duration,
      elapsed: data.elapsed,
      updatedTime: this._sharedService.transDateTime(data.updatedAt),
      createTime: this._sharedService.transDateTime(data.createdAt),
      startTime: this._sharedService.transDateTime(data.startAt),
      endTime: this._sharedService.transDateTime(data.endAt),
      node: data.node,
      licenseNode: data.licenseNode,
      usedNode: data.usedNode,
      status: data.category === LicenseCategory.NSM ? data.status : getIPSStatus(data, this._translate),
      statusRow: data.status,
      type:
        data.category === LicenseCategory.NSM
          ? this.setLicenseType(data.type, data.status, data.startAt)
          : getLicenseType(data, this._translate),
      typeRow: data.type,
      activationCode: data.activationCode,
      isOverview: data.isOverview,
      isCustomizedLicense: data?.type === LicenseType.Cv,
      version: data.version
    };
  }
}

