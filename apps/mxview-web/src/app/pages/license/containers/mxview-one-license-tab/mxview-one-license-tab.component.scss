@import '../../../../../assets/mx-service/font-style.scss';

.main-card {
  margin-bottom: 10px !important;

  .license-img {
    display: inline-block;
    min-width: 128px;
  }

  .main-card-action {
    margin-bottom: 0;
    //margin-top: 20px;
    margin-left: 0;
  }
}

.card-title-icon {
  position: absolute;
  right: 20px;
  top: 14px;
}

.small-card {
  width: 284px;
  height: 302px;
  overflow-x: unset;

  .mat-card-header {
    width: inherit;
  }
  .mat-card-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    word-break: break-word;
    text-align: justify;
    margin-bottom: 0px;
    width: inherit;
  }

  .small-card-action {
    height: auto;
    width: inherit;
    position: absolute;
    bottom: 26px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;

    .action-button {
      white-space: pre-line;
      width: 162px;
      height: 36px;
    }
  }
}

.mat-expansion-panel {
  margin-bottom: 10px;
}

.license-container {
  padding-bottom: 30px;

  .license-content {
    border-left-style: solid;
    border-width: 5px;
    border-color: rgba(0, 135, 135, 0.5);
  }

  .deactivated-license-content {
    border-left-style: solid;
    border-width: 5px;
    border-color: #ccc;
  }

  .expired-license-content {
    border-left-style: solid;
    border-width: 5px;
    border-color: #ffcbcb;
  }

  .license-text {
    padding-left: 12px;
    word-break: break-all;
  }
}

.red-text {
  color: $critical-color !important;
  font-weight: 500;
}

.yellow-text {
  color: $warning-color;
  font-weight: 500;
}

.over-nodes-warning {
  padding-left: 5px;
}

.day-count {
  font-size: 72px;
  position: absolute;
  left: 50%;
  top: 45%;
  transform: translate(-50%, -45%);
  color: $moxa-green;
}

.days {
  font-size: 20px;
  position: absolute;
  left: 50%;
  top: 65%;
  transform: translate(-50%, -65%);
}

.addon-card {
  min-width: 200px;
}

.deactivated-license-node {
  padding-top: 10px;
}
