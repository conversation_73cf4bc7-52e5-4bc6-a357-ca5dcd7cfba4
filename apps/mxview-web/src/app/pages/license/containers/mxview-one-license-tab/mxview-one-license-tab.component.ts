import { ChangeDetectionStrategy, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from "@angular/core";
import { MatLegacyDialog as MatDialog } from '@angular/material/legacy-dialog';
import { MatLegacySnackBar as MatSnackBar } from '@angular/material/legacy-snack-bar';
import { MatLegacyTooltip as MatTooltip } from '@angular/material/legacy-tooltip';
import { NavigationCancel, Router } from "@angular/router";
import { AppState } from "@mxview-web/app/app.service";
import { GlobalEvent, GlobalEventType } from "@mxview-web/app/global.event";
import { largeDialogConfig, mediumDialogConfig, smallDialogConfig } from "@mxview-web/app/shared/dialog-config";
import { LicensesErrorCode } from "@mxview-web/app/shared/Service/error-code-definitions";
import { ErrorService } from "@mxview-web/app/shared/Service/error.service";
import { GlobalService } from "@mxview-web/app/shared/Service/global-services";
import { <PERSON>donsLicenseUnit, AddonType, DeactivatedLicenseUnit, RegisteredLicenseUnit } from "@mxview-web/app/shared/Service/mx-platform/DataDef/LicenseDataDefs";
import { DataRepositoryService } from "@mxview-web/app/shared/Service/mx-platform/DataRepository/DataRepository";
import { DataService } from "@mxview-web/app/shared/Service/mx-platform/Service/DataService";
import {  TranslateService } from "@ngx-translate/core";
import { forkJoin, Observable, Subject, Subscription, takeUntil } from "rxjs";
import { AddLicenseComponent, ChangeAdapterComponent, DeactivationComponent, LicenseTypeComponent, ReactivationComponent, RelaunchGatewayComponent } from "../../components";
import * as _ from 'lodash';
import * as moment from 'moment';
import { DataType } from "@mxview-web/app/shared/Service/mx-platform/DataDef/DataTypeDefinitions";
import { SiteUnit } from "@mxview-web/app/shared/Service/mx-platform/DataDef/SiteDataDefs";
import { UserPreferenceUnit } from "@mxview-web/app/shared/Service/mx-platform/DataDef/UserDataDefs";


const LICENSE_MODES = {
  NONE: 0,
  FULL: 1,
  FREE: 2,
  TRIAL: 3,
  ERROR: 16,
};

const LICENSE_ERRORS = {
  OK: LicensesErrorCode.LICENSE_ERROR_OK,
  TOO_MANY_DEVICES: LicensesErrorCode.LICENSE_ERROR_TOO_MANY_DEVICES,
  TOO_MANY_DEVICES_TIMEOUT: LicensesErrorCode.LICENSE_ERROR_TOO_MANY_DEVICES_TIMEOUT,
  TRIAL_NOT_ACTIVATED_YET: LicensesErrorCode.LICENSE_ERROR_TRIAL_NOT_ACTIVATED_YET,
  LICENSE_OVER_2000: LicensesErrorCode.LICENSE_ERROR_LICENSE_OVER_2000,
  TRIAL_EXPIRED: LicensesErrorCode.LICENSE_ERROR_TRIAL_EXPIRED,
};

interface AddonLicenseUnit {
  state: string;
  license: string;
  showState: boolean;
  showTrialCard: boolean;
  showTrialDays: boolean;
  warningLicenseState: boolean;
  badLicenseState: boolean;
  startTrialDisabled: boolean;
  trialRemaining: number;
  availability: boolean;
  licenseMode: number;
  addon_type: number;
  errno: number;
  license_mode: number;
  nodes: number;
  trial_enable: boolean;
  trial_remaining: number;
}

@Component({
  selector: 'app-mxview-one-license-tab',
  templateUrl: './mxview-one-license-tab.component.html',
  styleUrls: ['./mxview-one-license-tab.component.scss'],
})
export class MxviewOneLicenseTabComponent implements OnInit, OnDestroy {

  constructor(
    private appState: AppState,
    private dataRepository: DataRepositoryService,
    private dataService: DataService,
    private dialog: MatDialog,
    private router: Router,
    private snackBar: MatSnackBar,
    private translateService: TranslateService,
    private errorService: ErrorService,
    private globalService: GlobalService
  ) {
    this.translateService.onLangChange.subscribe(() => {
      this.state = this.errorService.handleLicenseError(this.errno);
      this.wirelessAddon.license = this.translateService.instant('LICENSE.license_none');
      this.powerAddon.license = this.translateService.instant('LICENSE.license_none');
      switch (this.licenseMode) {
        case 0:
          this.license = this.translateService.instant('LICENSE.license_none');
          break;
        case 1:
          this.license = this.translateService.instant('LICENSE.license_authorized');
          break;
        case 2:
          this.license = this.translateService.instant('LICENSE.license_free');
          break;
        case 3:
          this.license = this.translateService.instant('LICENSE.license_trial');
          break;
        case 16:
          this.license = this.translateService.instant('LICENSE.license_none');
      }
      this.updateAddonData(this.addonData);
    });
  }
  @ViewChild('tooltip') tooltip: MatTooltip;
  routerSubscription: Subscription;
  destroy$ = new Subject();
  licenseMode: number;
  license: string;
  state: string;
  errno: number;
  maxNodes: number;
  nodes: number;
  trialRemaining: number;
  startTrialDisabled = false;
  showState = false;
  showOverNodes = false;
  showLicenses = false;
  showDeactivatedLicenses = false;
  showTrialCard = true;
  showTrialButton = true;
  showTrialDays = false;
  showOlderLicenses = false;
  badLicenseState = false;
  warningLicenseState = false;
  licenses: RegisteredLicenseUnit[];
  deactivatedLicenses: DeactivatedLicenseUnit[];
  olderLicenses: any[] = [];
  wirelessAddon: AddonLicenseUnit;
  powerAddon: AddonLicenseUnit;
  securityAddon: AddonLicenseUnit;
  isLicenseManagedByCentral = false;
  private siteId;
  private addonData;

  static copyTextToClipboard(text): boolean {
    const txtArea = document.createElement('textarea');
    txtArea.style.position = 'fixed';
    txtArea.style.top = '0';
    txtArea.style.left = '0';
    txtArea.style.opacity = '0';
    txtArea.value = text;
    document.body.appendChild(txtArea);
    txtArea.select();
    try {
      const successful = document.execCommand('copy');
      if (successful) {
        document.body.removeChild(txtArea);
        return true;
      }
    } catch (err) {
      document.body.removeChild(txtArea);
      console.log('Unable to copy');
    }
    document.body.removeChild(txtArea);
    return false;
  }

  get AddonType(): typeof AddonType {
    return AddonType;
  }

  ngOnInit(): void {
    localStorage.setItem('showLicensePage', '0');
    if (localStorage.getItem('showKeepTrial') === '1') {
      this.showTrialButton = true;
    } else if (localStorage.getItem('showKeepTrial') === '0') {
      this.showTrialButton = false;
    }

    this.routerSubscription = this.router.events.pipe(takeUntil(this.destroy$)).subscribe(val => {
      if (val instanceof NavigationCancel && localStorage.getItem('showLicensePage') === '3') {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleNormalError(this.translateService.instant('LICENSE.site_license_invalid'));
      }
    });
    this.initData();
  }

  initData(siteId?: string): void {
    this.licenseMode = undefined;
    this.license = undefined;
    this.state = undefined;
    this.errno = undefined;
    this.maxNodes = undefined;
    this.nodes = undefined;
    this.trialRemaining = undefined;
    this.startTrialDisabled = false;
    this.showState = false;
    this.showOverNodes = false;
    this.showLicenses = false;
    this.showDeactivatedLicenses = false;
    this.showTrialCard = true;
    this.showTrialButton = true;
    this.showTrialDays = false;
    this.showOlderLicenses = false;
    this.badLicenseState = false;
    this.warningLicenseState = false;
    this.licenses = [];
    this.deactivatedLicenses = [];
    this.olderLicenses = [];
    const addonInit = {
      state: '',
      license: this.translateService.instant('LICENSE.license_none'),
      showState: false,
      showTrialCard: false,
      showTrialDays: false,
      warningLicenseState: false,
      badLicenseState: false,
      startTrialDisabled: false,
      trialRemaining: 0,
      addon_type: 0,
      errno: 0,
      // license_mode: 0,
      nodes: 0,
      trial_enable: false,
      alsetrial_remaining: 0,
      // availability: false, // is available addon
    };
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    this.wirelessAddon = _.clone(addonInit);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    this.powerAddon = _.clone(addonInit);
    this.securityAddon = _.clone(addonInit)
    this.siteId = siteId || localStorage.getItem('siteId');
    this.getLicenseStatus();
  }

  getLicenseStatus(): void {
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));

    const request = [
      this.dataService.getLicenseStatus(this.siteId),
      this.dataService.getDeactivatedLicense(this.siteId),
      this.dataService.getOlderLicenseCode(this.siteId),
      this.dataService.getSite(),
      this.dataService.getMode(),
    ];

    forkJoin(request).subscribe(
      (values: any) => {
        const license = values[0][0];
        this.licenseMode = license.license_mode;
        this.errno = license.errno;
        this.maxNodes = license.max_nodes || 0;
        this.nodes = license.nodes || 0;
        this.trialRemaining = license.trial_remaining;
        this.isLicenseManagedByCentral = values[4].mode === 'central_local';
        localStorage.setItem('isLicenseManagedByCentral', values[4].mode === 'central_local' ? '1' : '0');

        this.handleErrors();
        this.handleLicenseMode(values);

        // Get deactivated license
        if (values[1].length > 0) {
          this.deactivatedLicenses = values[1];
          this.showDeactivatedLicenses = this.deactivatedLicenses.length !== 0;
        }

        // Get older license code
        if (values[2] !== undefined && values[2].length > 0) {
          this.olderLicenses = values[2];
          this.showOlderLicenses = true;
        } else {
          this.showOlderLicenses = false;
        }

        this.handleSiteData(values[3]);
      },
      error => {
        this.errorService.handleNormalError(error.message);
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
      }
    );
  }

  setAddonLicense(licenseInfo: AddonLicenseUnit, addonName: string): void {
    const addonLicenseMode = licenseInfo.license_mode;
    const addonTrialRemaining = licenseInfo.trial_remaining;
    const errno =
      addonLicenseMode === 3 && addonTrialRemaining <= 0
        ? LicensesErrorCode.LICENSE_ERROR_TRIAL_EXPIRED
        : licenseInfo.errno;
    const addon = this[addonName];

    addon.trialRemaining = addonTrialRemaining;
    addon.state = this.errorService.handleLicenseError(errno);
    addon.licenseMode = addonLicenseMode;
    addon.addon_type = licenseInfo.addon_type;
    addon.nodes = licenseInfo.nodes;
    addon.trial_enable = licenseInfo.trial_enable;
    addon.availability = licenseInfo.availability;

    if (errno === LICENSE_ERRORS.OK) {
      this.handleNoError(addon, addonLicenseMode, addonTrialRemaining);
    } else {
      this.handleError(addon, errno);
    }
  }

  startAddonTrial(addonType: number): void {
    this.startTrialDisabled = true;
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    this.dataService
      .startAddonsTrial(this.siteId, {
        license_type: addonType,
      })
      .subscribe(
        data => {
          setTimeout(() => {
            smallDialogConfig.data = {
              timer: addonType === AddonType.Nsm ? 30 : 10,
            };
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            const relaunchDialog = this.dialog.open(RelaunchGatewayComponent, smallDialogConfig);
            relaunchDialog.afterClosed().subscribe(() => {
              window.location.reload();
            });
          }, 5000);
        },
        error => {
          this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          this.errorService.handleNormalError(error.message);
        }
      );
  }

  getLicenseErrnoString(errno): string {
    switch (errno) {
      case -5:
        return this.translateService.instant('LICENSE.state_usercode_not_match_adapter');
      case -6:
        return this.translateService.instant('LICENSE.state_license_expired');
      default:
        return this.translateService.instant('LICENSE.state_general_error');
    }
  }

  getLicenseTypeString(type): string {
    switch (type) {
      case 0:
        return '';
      case 1:
        return 'Upgrade';
      default:
        return '';
    }
  }

  getRegisteredLicense(): void {
    this.dataService.getRegisteredLicense(this.siteId).subscribe(regLicense => {
      this.licenses = _.filter(regLicense, item => !(item.duration && item.remaining < 0));
      if (this.licenses.length > 0) {
        this.showLicenses = true;
      }
      this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
    });
  }

  startFreeTrial(): void {
    this.startTrialDisabled = true;
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    this.dataService.startTrial(this.siteId).subscribe(
      () => {
        this.startTrialDisabled = false;
        this.snackBar.open(this.translateService.instant('LICENSE.start_free_trial_success'), '', { duration: 3000 });
        this.setCacheLanguageToBackend().subscribe({
          complete: () => {
            setTimeout(() => {
              window.location.reload();
            }, 2000);
          },
        });
      },
      () => {
        this.startTrialDisabled = false;
        this.errorService.handleNormalError(this.translateService.instant('LICENSE.start_free_trial_fail'));
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
      }
    );
  }

  keepTrial(): void {
    this.showTrialButton = false;
    localStorage.setItem('showKeepTrial', '0');
    const deviceList = this.dataRepository.getData(DataType.DEVICE, this.siteId);
    if (!deviceList || deviceList.length !== 0) {
      localStorage.setItem('activateMenu', '/pages/network');
      this.router.navigate(['/pages/network']);
    }
  }

  openAddLicenseDialog(): void {
    const dialogRef = this.dialog.open(AddLicenseComponent, largeDialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.activateDialogClose(result, false);
      }
    });
  }

  openChangeAdapterDialog(): void {
    mediumDialogConfig.data = null;
    const dialogRef = this.dialog.open(ChangeAdapterComponent, mediumDialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result === 'success') {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
        setTimeout(() => {
          this.getLicenseStatus();
        }, 2000);
      }
    });
  }

  openLicenseTypeDialog(): void {
    this.dialog.open(LicenseTypeComponent, largeDialogConfig);
  }

  openReactivateDialog(deactivatedLicense?: DeactivatedLicenseUnit, deactivationCode?: string): void {
    largeDialogConfig.data = deactivationCode ? deactivationCode : null;
    const dialogRef = this.dialog.open(ReactivationComponent, largeDialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.activateDialogClose(result, false, deactivatedLicense.license_type);
      }
    });
  }

  activateDialogClose(result: any, isDeactivate: boolean, addonType?: number): void {
    if (result === 'success') {
      window.location.reload();
    } else if (result !== 'fail') {
      if (result === 'relaunch') {
        smallDialogConfig.data = {
          can_deactivate: true,
          isDeactivate,
          timer: addonType === AddonType.Nsm ? 30 : 10,
        };
      } else if (result === 'relaunch_cannot_deactivate') {
        smallDialogConfig.data = {
          can_deactivate: false,
          isDeactivate,
          timer: addonType === AddonType.Nsm ? 30 : 10,
        };
      }
      this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
      this.setCacheLanguageToBackend().subscribe({
        complete: () => {
          setTimeout(() => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            const relaunchDialog = this.dialog.open(RelaunchGatewayComponent, smallDialogConfig);
            relaunchDialog.afterClosed().subscribe(() => {
              window.location.reload();
            });
          }, 5000);
        },
      });
    }
  }

  deactivate(licenseFile: string, license: RegisteredLicenseUnit): void {
    smallDialogConfig.data = licenseFile;
    const dialogRef = this.dialog.open(DeactivationComponent, smallDialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.activateDialogClose(result, true, license.license_type);
      }
    });
  }

  getDate(time): string {
    return moment.unix(time).format('YYYY-MM-DD HH:mm:ss');
  }

  connectTrigger(siteData: SiteUnit[]): void {
    this.createSiteInfo(siteData);
    const sitesData = this.dataRepository.getData(DataType.SITE);
    if (sitesData.length === 0) {
      window.location.reload();
    } else {
      this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
    }
  }

  copyToClipboard(text: string): void {
    const result = MxviewOneLicenseTabComponent.copyTextToClipboard(text);
    if (result) {
      this.snackBar.open(this.translateService.instant('LICENSE.copied_to_clipboard'), '', { duration: 3000 });
    }
  }

  downloadDeactivationCode(deactivationCode): void {
    const blob = new Blob([deactivationCode], { type: 'application/txt' });
    this.globalService.downloadFile('deactivationCode.txt', blob);
  }

  ngOnDestroy(): void {
    if (this.routerSubscription !== undefined) {
      this.routerSubscription.unsubscribe();
    }
    this.destroy$.next(null);
    this.destroy$.complete();
  }

  setCacheLanguageToBackend(): Observable<null> {
    // 在授權啟用成功後，如果發現 localStorage 的語言與後端不一樣，則將 localStorage 的語言設到後端
    return new Observable(observer => {
      const language = localStorage.getItem('language');
      const username = localStorage.getItem('loginUser');
      this.dataService.getUserPreference(username).then(values => {
        if (language !== values[0].language) {
          const userPreferenceUnit = new UserPreferenceUnit();
          userPreferenceUnit.language = language;
          this.dataService
            .updateUserPreference(username, userPreferenceUnit)
            .then(() => {
              observer.complete();
            })
            .catch(error => {
              console.log('handleError:' + JSON.stringify(error));
              observer.complete();
            });
        } else {
          observer.complete();
        }
      });
    });
  }

  updateAddonData(addonData: any): void {
    if (addonData.addon_info.length <= 0) return;

    addonData.addon_info.forEach((item: any) => {
      switch (item.addon_type) {
        case 4:
          this.setAddonLicense(item, 'wirelessAddon');
          break;
        case 5:
          this.setAddonLicense(item, 'powerAddon');
          break;
        case 6:
          this.setAddonLicense(item, 'securityAddon');
          break;
      }
    });
  }

  createSiteInfo(siteData) {
    // 如果 siteInfo 為空則創建，避免進到 Wizard 時創建 AutoTopologyShareComponent 發生錯誤
    if (!this.globalService.siteInfo) {
      this.globalService.createAddonsList(siteData, this.siteId);
    }
  }

  toggleAddonTrial(addonType: number): void {
    const req = [
      {
        addon_type: AddonType.Wireless,
        enable: addonType === AddonType.Wireless ? !this.wirelessAddon.trial_enable : this.wirelessAddon.trial_enable,
      },
      {
        addon_type: AddonType.Power,
        enable: addonType === AddonType.Power ? !this.powerAddon.trial_enable : this.powerAddon.trial_enable,
      },
      {
        addon_type: AddonType.Nsm,
        enable: addonType === AddonType.Nsm ? !this.securityAddon.trial_enable : this.securityAddon.trial_enable,
      },
    ];

    this.startTrialDisabled = true;
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    this.dataService.setAddonLicenseTrial(this.siteId, req).subscribe(
      () => {
        setTimeout(() => {
          smallDialogConfig.data = {
            timer: addonType === AddonType.Nsm ? 30 : 10,
          };
          this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          const relaunchDialog = this.dialog.open(RelaunchGatewayComponent, smallDialogConfig);
          relaunchDialog.afterClosed().subscribe(() => {
            window.location.reload();
          });
        }, 5000);
      },
      error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleNormalError(error.message);
      }
    );
  }

  onWirelessAddonChanged(): void {
    this.toggleAddonTrial(AddonType.Wireless);
  }

  onPowerAddonChanged(): void {
    this.toggleAddonTrial(AddonType.Power);
  }

  onSecurityAddonChanged(): void {
    this.toggleAddonTrial(AddonType.Nsm);
  }

  shouldShowWirelessAddon(): boolean {
    return (
      !this.isLicenseManagedByCentral &&
      this.licenseMode !== 0 &&
      (this.licenseMode !== 1 || this.powerAddon.licenseMode === 0) &&
      this.wirelessAddon.licenseMode !== 1
    );
  }

  shouldShowPowerAddon(): boolean {
    return (
      !this.isLicenseManagedByCentral &&
      this.licenseMode !== 0 &&
      (this.licenseMode !== 1 || this.wirelessAddon.licenseMode === 0) &&
      this.powerAddon.licenseMode !== 1
    );
  }

  shouldShowSecurityAddon(): boolean {
    return !this.isLicenseManagedByCentral && this.licenseMode !== 0 && this.securityAddon.licenseMode !== 1;
  }

  private handleErrors(): void {
    if (
      (!this.isLicenseManagedByCentral && this.errno === LICENSE_ERRORS.TOO_MANY_DEVICES) ||
      this.errno === LICENSE_ERRORS.TOO_MANY_DEVICES_TIMEOUT
    ) {
      this.showOverNodes = true;
      setTimeout(() => {
        if (this.tooltip) {
          this.tooltip.show();
        }
      }, 100);
    } else {
      this.showOverNodes = false;
    }

    if (this.errno === LICENSE_ERRORS.OK) {
      this.showState = false;
      localStorage.setItem('showLicensePage', '0');
    } else {
      this.showState = true;
      localStorage.setItem('showLicensePage', '1');
    }

    if (this.isLicenseManagedByCentral && this.errno === LICENSE_ERRORS.TRIAL_NOT_ACTIVATED_YET) {
      this.errno = LicensesErrorCode.LICENSE_ERROR_ALL_LICENSES_INVALIDATED;
    }

    this.badLicenseState = true;
    this.state = this.errorService.handleLicenseError(this.errno);

    switch (this.errno) {
      case LICENSE_ERRORS.OK:
        this.badLicenseState = false;
        break;
      case LICENSE_ERRORS.LICENSE_OVER_2000:
        this.badLicenseState = false;
        this.warningLicenseState = true;
        break;
      case LICENSE_ERRORS.TOO_MANY_DEVICES:
      case LICENSE_ERRORS.TOO_MANY_DEVICES_TIMEOUT:
        localStorage.setItem('showLicensePage', '0');
        break;
    }
  }

  private handleLicenseMode(values: any[]): void {
    switch (this.licenseMode) {
      case LICENSE_MODES.NONE:
        this.handleNoLicense(values);
        break;
      case LICENSE_MODES.FULL:
        this.handleFullLicense(values);
        break;
      case LICENSE_MODES.FREE:
        this.handleFreeLicense(values);
        break;
      case LICENSE_MODES.TRIAL:
        this.handleTrialLicense(values);
        break;
      case LICENSE_MODES.ERROR:
        this.handleErrorLicense();
        break;
    }
  }

  private handleNoLicense(values: any[]): void {
    this.license = this.translateService.instant('LICENSE.license_none');
    this.showLicenses = false;
    this.showTrialCard = this.trialRemaining !== -1;
    this.startTrialDisabled = this.trialRemaining === 0;
    this.showTrialDays = false;
    this.wirelessAddon.showTrialCard = false;
    this.powerAddon.showTrialCard = false;
    this.securityAddon.showTrialCard = false;
    this.createSiteInfo(values[3]);
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
  }

  private handleFullLicense(values: any[]): void {
    if (this.errno !== LICENSE_ERRORS.TOO_MANY_DEVICES && this.errno !== LICENSE_ERRORS.TOO_MANY_DEVICES_TIMEOUT) {
      this.connectTrigger(values[3]);
    }
    this.license = this.translateService.instant('LICENSE.license_authorized');
    this.showLicenses = true;
    this.showTrialCard = false;
    this.showTrialDays = false;
    this.getRegisteredLicense();
  }

  private handleFreeLicense(values: any[]): void {
    this.createSiteInfo(values[3]);
    const sitesData = this.dataRepository.getData(DataType.SITE);
    if (sitesData.length === 0) {
      // 超過 30 分鐘的刪除時間，/api/devices, api/groups, api/links/site/ 會回 403 Forbidden，重開 MXview 會重置 30 分鐘的刪除時間
    }
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
    this.connectTrigger(values[3]);
    this.license = this.translateService.instant('LICENSE.license_free');
    this.showLicenses = true;
    this.showTrialCard = false;
    this.showTrialDays = false;
    this.getRegisteredLicense();
  }

  private handleTrialLicense(values: any[]): void {
    if (this.errno === LICENSE_ERRORS.OK) {
      this.connectTrigger(values[3]);
    }
    this.license = this.translateService.instant('LICENSE.license_trial');
    this.showLicenses = true;
    this.showTrialCard = true;
    this.showTrialDays = true;
    this.getRegisteredLicense();
  }

  private handleErrorLicense(): void {
    this.license = this.translateService.instant('LICENSE.license_none');
    this.showLicenses = true;
    this.showTrialCard = this.trialRemaining !== -1;
    this.showTrialDays = false;
    this.getRegisteredLicense();
  }

  private handleSiteData(siteData: any[]): void {
    if (siteData !== undefined) {
      _.forEach(siteData, data => {
        if (data.site_id === this.siteId) {
          const availableAddons = data.available_addons || undefined;
          if (availableAddons !== undefined) {
            if (_.includes(availableAddons, 'wireless')) {
              if (this.licenseMode !== LICENSE_MODES.NONE && this.licenseMode !== LICENSE_MODES.ERROR) {
                this.wirelessAddon.showTrialCard = true;
              }
            }
            if (_.includes(availableAddons, 'grid')) {
              if (this.licenseMode !== LICENSE_MODES.NONE && this.licenseMode !== LICENSE_MODES.ERROR) {
                this.powerAddon.showTrialCard = true;
              }
            }
            this.dataService.getAddonLicenseBySite(this.siteId).subscribe((addonData: AddonsLicenseUnit) => {
              if (!addonData) return;

              this.addonData = addonData;
              this.updateAddonData(this.addonData);
            });
          } else {
            this.wirelessAddon.showTrialCard = false;
            this.powerAddon.showTrialCard = false;
          }
        }
      });
    }
  }

  private handleNoError(addon: AddonLicenseUnit, addonLicenseMode: number, addonTrialRemaining: number): void {
    addon.badLicenseState = false;
    addon.warningLicenseState = false;
    addon.showState = false;

    if (
      this.licenseMode === LICENSE_MODES.FULL ||
      this.licenseMode === LICENSE_MODES.FREE ||
      this.licenseMode === LICENSE_MODES.TRIAL
    ) {
      switch (addonLicenseMode) {
        case LICENSE_MODES.NONE:
          addon.license = this.translateService.instant('LICENSE.license_none');
          addon.showTrialCard = addonTrialRemaining === 0 || addonTrialRemaining === -1;
          break;
        case LICENSE_MODES.FULL:
          addon.showTrialCard = false;
          addon.license = this.translateService.instant('LICENSE.license_authorized');
          break;
        case LICENSE_MODES.TRIAL:
          addon.showTrialCard = true;
          addon.showTrialDays = addonTrialRemaining > 0;
          addon.license = this.translateService.instant('LICENSE.license_trial');
          break;
      }
    }
  }

  private handleError(addon: AddonLicenseUnit, errno: number): void {
    addon.badLicenseState = true;
    addon.warningLicenseState = false;
    addon.showState = true;
    addon.showTrialCard = false;

    if (
      errno === LICENSE_ERRORS.TRIAL_EXPIRED &&
      (this.licenseMode === LICENSE_MODES.FULL ||
        this.licenseMode === LICENSE_MODES.FREE ||
        this.licenseMode === LICENSE_MODES.TRIAL)
    ) {
      addon.showTrialCard = true;
      addon.showTrialDays = false;
      addon.startTrialDisabled = true;
    }
  }
}
