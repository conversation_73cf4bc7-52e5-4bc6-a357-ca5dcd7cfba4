<mat-card class="main-card">
  <mat-card-header>
    <mat-card-title class="card-title">{{ 'LICENSE.mxview' | translate }}</mat-card-title>
  </mat-card-header>
  <mat-card-content>
    <div fxLayout="column" fxLayoutGap="12px">
      <div fxLayout="row" fxLayoutAlign="none" fxLayoutGap="12px">
        <div class="license-img">
          <img src="assets/img/license.png" />
        </div>
        <div fxLayout="column" fxLayoutGap="12px" class="addon-card">
          <div id="div-license-title" class="card-title">
            {{ 'LICENSE.license_type.node_base_title' | translate }}
          </div>
          <div *ngIf="!isLicenseManagedByCentral" id="div-license-mode">
            {{ 'PREFERENCES.sms_config.mode' | translate }}: {{ license }}
          </div>
          <div *ngIf="showState">
            {{ 'LICENSE.state' | translate }}
            <span
              id="span-license-state"
              [ngClass]="{ 'red-text': badLicenseState, 'yellow-text': warningLicenseState }"
            >
              {{ state }}
            </span>
          </div>
          <div fxLayout="row">
            <div fxFlexAlign="center">
              {{ 'LICENSE.current_nodes' | translate }}
              <span id="span-license-current-nodes" [ngClass]="{ 'red-text': showOverNodes }">{{ nodes }}</span>
            </div>
            <div *ngIf="showOverNodes">
              <mat-icon
                #tooltip="matTooltip"
                color="warn"
                class="over-nodes-warning"
                matTooltip="{{ 'LICENSE.trial_over_nodes' | translate }}"
                matTooltipPosition="after"
                >warning
              </mat-icon>
            </div>
          </div>
          <div fxLayout="row">
            <div fxFlexAlign="center">
              {{ 'LICENSE.licensed_nodes' | translate }}
              <span id="span-license-max-nodes" [ngClass]="{ 'red-text': showOverNodes }">{{ maxNodes }}</span>
            </div>
          </div>
        </div>
        <div
          *ngIf="powerAddon.trialRemaining < 0 || licenseMode !== 1"
          fxLayout="column"
          fxLayoutGap="12px"
          class="addon-card"
        >
          <div id="div-wireless-title" class="card-title">
            {{ 'LICENSE.license_type.wireless_addon' | translate }}
          </div>
          <div id="div-wireless-mode">{{ 'PREFERENCES.sms_config.mode' | translate }}: {{ wirelessAddon.license }}</div>
          <div *ngIf="wirelessAddon.showState">
            {{ 'LICENSE.state' | translate }}
            <span
              id="span-wireless-state"
              [ngClass]="{
                'red-text': wirelessAddon.badLicenseState,
                'yellow-text': wirelessAddon.warningLicenseState
              }"
              >{{ wirelessAddon.state }}</span
            >
          </div>
        </div>
        <div
          *ngIf="wirelessAddon.trialRemaining < 0 || licenseMode !== 1"
          fxLayout="column"
          fxLayoutGap="12px"
          class="addon-card gap-left-32"
        >
          <div id="div-grid-title" class="card-title">{{ 'LICENSE.license_type.power_addon' | translate }}</div>
          <div id="div-grid-mode">{{ 'PREFERENCES.sms_config.mode' | translate }}: {{ powerAddon.license }}</div>
          <div *ngIf="powerAddon.showState">
            {{ 'LICENSE.state' | translate }}
            <span
              id="span-grid-mode"
              [ngClass]="{ 'red-text': powerAddon.badLicenseState, 'yellow-text': powerAddon.warningLicenseState }"
              >{{ powerAddon.state }}</span
            >
          </div>
        </div>
        <div fxLayout="column" fxLayoutGap="12px" class="addon-card gap-left-32">
          <div id="div-security-title" class="card-title">
            {{ 'LICENSE.license_type.security_addon' | translate }}
          </div>
          <div id="div-security-mode">{{ 'PREFERENCES.sms_config.mode' | translate }}: {{ securityAddon.license }}</div>
          <div *ngIf="powerAddon.showState">
            {{ 'LICENSE.state' | translate }}
            <span
              id="span-security-mode"
              [ngClass]="{ 'red-text': powerAddon.badLicenseState, 'yellow-text': powerAddon.warningLicenseState }"
              >{{ securityAddon.state }}</span
            >
          </div>
        </div>
      </div>
      <div *ngIf="!isLicenseManagedByCentral">
        <a id="a-link" class="hyper-link" href="http://license.moxa.com/" target="_blank"
          >{{ 'LICENSE.license_site' | translate }}
          <mat-icon class="link-icon" color="primary">open_in_new </mat-icon>
        </a>
        <mat-card-actions class="main-card-action">
          <button mat-raised-button color="primary" id="btn-add-license-dialog" (click)="openAddLicenseDialog()">
            {{ 'LICENSE.add_new_license.title' | translate }}
          </button>
          <button mat-button color="primary" id="license-type-dialog" (click)="openLicenseTypeDialog()">
            {{ 'LICENSE.license_type.title' | translate }}
          </button>
        </mat-card-actions>
      </div>
    </div>
  </mat-card-content>
</mat-card>
<mat-expansion-panel
  *ngIf="!isLicenseManagedByCentral && showLicenses && licenses.length > 0"
  id="expansion-panel-licenses"
>
  <mat-expansion-panel-header>
    <mat-panel-title>
      {{ 'LICENSE.licenses' | translate }}
    </mat-panel-title>
  </mat-expansion-panel-header>
  <div *ngFor="let license of licenses" class="license-container">
    <div class="license-content" fxLayout="row" fxLayoutAlign="none">
      <div class="license-text" fxFlex="96">
        <div *ngIf="license.license_type === 2 && license.addon_type === undefined">
          {{ 'LICENSE.license_type.title' | translate }}: {{ 'LICENSE.license_type.trial_title' | translate }}
        </div>
        <!-- 如果 Trial 過期，valid 為變成 false，則顯示紅色文字 -->
        <div *ngIf="license.license_type === 0 && license.addon_type === undefined">
          {{ 'LICENSE.license_type.title' | translate }}: {{ 'LICENSE.license_type.node_base_title' | translate }}
        </div>
        <div *ngIf="license.addon_type === 4">
          {{ 'LICENSE.license_type.title' | translate }}: {{ 'LICENSE.license_type.wireless_addon' | translate }}
        </div>
        <div *ngIf="license.addon_type === 5">
          {{ 'LICENSE.license_type.title' | translate }}: {{ 'LICENSE.license_type.power_addon' | translate }}
        </div>
        <div *ngIf="license.addon_type === 6">
          {{ 'LICENSE.license_type.title' | translate }}: {{ 'LICENSE.license_type.security_addon' | translate }}
        </div>
        <div
          *ngIf="license.duration >= 0 && license.license_type === 2"
          [ngClass]="{ 'red-text': license.duration === 0 }"
        >
          {{ 'LICENSE.duration' | translate }}: {{ license.duration }} {{ 'LICENSE.trial_day' | translate }}
        </div>
        <div *ngIf="license.max_nodes !== undefined">
          {{ 'LICENSE.licensed_node' | translate }}: {{ license.max_nodes }}
        </div>
        <div>{{ 'LICENSE.activation_code' | translate }}: {{ license.license_file }}</div>
        <div>{{ 'LICENSE.license_start' | translate }}: {{ getDate(license.activated_time) }}</div>
        <div *ngIf="license.valid === false">
          {{ 'LICENSE.reason' | translate }}: {{ getLicenseErrnoString(license.reason) }}
        </div>
      </div>
      <div class="license-action" fxFlexAlign="end">
        <button
          *ngIf="
            (license.can_deactivate !== undefined && license.can_deactivate === 0) ||
            license.can_deactivate === 1 ||
            (license.can_deactivate === undefined && license.license_type !== 2)
          "
          mat-raised-button
          color="accent"
          class="license-action-button"
          id="button-deactivate-license"
          (click)="deactivate(license.license_file, license)"
        >
          {{ 'LICENSE.deactivate' | translate }}
        </button>
      </div>
    </div>
  </div>
</mat-expansion-panel>
<mat-expansion-panel *ngIf="!isLicenseManagedByCentral && showDeactivatedLicenses" id="expansion-deactivated-licenses">
  <mat-expansion-panel-header>
    <mat-panel-title>
      {{ 'LICENSE.deactivated_licenses' | translate }}
    </mat-panel-title>
  </mat-expansion-panel-header>
  <div *ngFor="let deactivatedLicense of deactivatedLicenses" class="license-container">
    <div class="deactivated-license-content" fxLayout="row" fxLayoutAlign="none">
      <div class="license-text" fxFlex="96">
        <div *ngIf="deactivatedLicense.addon_type === 4" id="deactivate-license-addon-type-wireless">
          {{ 'LICENSE.license_type.wireless_addon' | translate }}
        </div>
        <div *ngIf="deactivatedLicense.addon_type === 5" id="deactivate-license-addon-type-grid">
          {{ 'LICENSE.license_type.power_addon' | translate }}
        </div>
        <div *ngIf="deactivatedLicense.addon_type === 6" id="deactivate-license-addon-type-security">
          {{ 'LICENSE.license_type.security_addon' | translate }}
        </div>
        <div *ngIf="deactivatedLicense.max_nodes !== undefined" class="deactivated-license-node">
          {{ 'LICENSE.licensed_node' | translate }}: {{ deactivatedLicense.max_nodes }}
        </div>
        <div>
          {{ 'LICENSE.deactivation_code' | translate }}:
          {{ deactivatedLicense.deactivation_code }}
          <button
            mat-icon-button
            matTooltip="{{ 'BUTTON.save' | translate }}"
            id="btn-download-deactivation-code"
            (click)="downloadDeactivationCode(deactivatedLicense.deactivation_code)"
          >
            <mat-icon>save</mat-icon>
          </button>
        </div>
        <div>{{ 'LICENSE.license_start' | translate }}: {{ getDate(deactivatedLicense.deactivated_time) }}</div>
      </div>
      <div fxFlexAlign="end">
        <button
          mat-raised-button
          color="primary"
          id="btn-reactive-dialog"
          (click)="openReactivateDialog(deactivatedLicense, deactivatedLicense.deactivation_code)"
        >
          {{ 'LICENSE.reactivate_license.title_abbr' | translate }}
        </button>
      </div>
    </div>
  </div>
</mat-expansion-panel>
<mat-expansion-panel *ngIf="showOlderLicenses">
  <mat-expansion-panel-header>
    <mat-panel-title>
      {{ 'LICENSE.older_license' | translate }}
    </mat-panel-title>
  </mat-expansion-panel-header>
  <div *ngFor="let oldLicense of olderLicenses" class="license-container">
    <div class="deactivated-license-content" fxLayout="row" fxLayoutAlign="none">
      <div class="license-text" fxFlex="96">
        <div>{{ 'LICENSE.older_license' | translate }}: {{ oldLicense.license }}</div>
        <div>{{ 'LICENSE.older_license_nodes' | translate }}: {{ oldLicense.device_limit }}</div>
      </div>
      <div fxFlexAlign="end">
        <button
          mat-raised-button
          color="accent"
          id="btn-copy-old-license"
          (click)="copyToClipboard(oldLicense.license)"
        >
          {{ 'LICENSE.copy_older_license_code' | translate }}
        </button>
      </div>
    </div>
  </div>
</mat-expansion-panel>
<div fxLayout="row" fxLayout.xs="column" fxLayoutAlign="none" fxLayoutGap="20px">
  <mat-card class="small-card" *ngIf="!isLicenseManagedByCentral && (licenseMode === 0 || licenseMode === 3)">
    <mat-card-header>
      <mat-card-title id="card-free-trial-title" *ngIf="!showTrialDays" class="card-title"
        >{{ 'LICENSE.free_trial' | translate }}
      </mat-card-title>
      <mat-card-title id="card-trial-remaining-title" *ngIf="showTrialDays" class="card-title"
        >{{ 'LICENSE.trial_remaining' | translate }}
      </mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div *ngIf="!showTrialDays; else nodeTrialDays">
        {{ 'LICENSE.free_trial_desc' | translate }}
      </div>
      <ng-template #nodeTrialDays>
        <div id="div-trial-remaining" class="day-count">{{ trialRemaining }}</div>
        <div class="days">{{ 'LICENSE.trial_day' | translate }}</div>
      </ng-template>
    </mat-card-content>
    <mat-card-actions class="small-card-action">
      <div *ngIf="!showTrialDays">
        <button
          mat-raised-button
          color="primary"
          class="action-button"
          id="btn-start-trial"
          (click)="startFreeTrial()"
          [disabled]="startTrialDisabled"
        >
          {{ 'LICENSE.start_free_trial' | translate }}
        </button>
      </div>
    </mat-card-actions>
  </mat-card>
  <network-addon-card
    *ngIf="shouldShowWirelessAddon()"
    [addon]="wirelessAddon"
    addonTitle="LICENSE.license_type.wireless_addon"
    addonTrialText="LICENSE.wireless_addon_trial"
    [licenseMode]="licenseMode"
    [trialRemaining]="trialRemaining"
    [otherAddon]="powerAddon"
    [addonType]="AddonType.Wireless"
    (addonChanged)="onWirelessAddonChanged($event)"
    (startTrial)="startAddonTrial($event)"
  />
  <network-addon-card
    *ngIf="shouldShowPowerAddon()"
    [addon]="powerAddon"
    addonTitle="LICENSE.license_type.power_addon"
    addonTrialText="LICENSE.power_addon_trial"
    [licenseMode]="licenseMode"
    [trialRemaining]="trialRemaining"
    [otherAddon]="wirelessAddon"
    [addonType]="AddonType.Power"
    (addonChanged)="onPowerAddonChanged($event)"
    (startTrial)="startAddonTrial($event)"
  />
  <network-addon-card
    *ngIf="shouldShowSecurityAddon()"
    [addon]="securityAddon"
    addonTitle="LICENSE.license_type.security_addon"
    addonTrialText="LICENSE.security_addon_trial"
    [licenseMode]="licenseMode"
    [trialRemaining]="trialRemaining"
    [otherAddon]="null"
    [addonType]="AddonType.Nsm"
    (addonChanged)="onSecurityAddonChanged($event)"
    (startTrial)="startAddonTrial($event)"
  />
  <mat-card *ngIf="!isLicenseManagedByCentral" class="small-card">
    <mat-card-header>
      <mat-card-title id="card-reactivate-license-title" class="card-title">
        {{ 'LICENSE.reactivate_license.title' | translate }}
      </mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div>
        <div>{{ 'LICENSE.reactivate_license.intro' | translate }}</div>
      </div>
    </mat-card-content>
    <mat-card-actions class="small-card-action">
      <div>
        <button
          mat-raised-button
          class="action-button"
          color="accent"
          id="btn-reactivate-card-dialog"
          (click)="openReactivateDialog()"
        >
          {{ 'LICENSE.reactivate_license.title_abbr' | translate }}
        </button>
      </div>
    </mat-card-actions>
  </mat-card>
</div>
