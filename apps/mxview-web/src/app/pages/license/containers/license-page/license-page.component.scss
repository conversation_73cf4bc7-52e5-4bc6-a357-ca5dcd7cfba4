@import '../../../../../assets/mx-service/font-style.scss';

.app-content-container {
  overflow-x: scroll;
}

.gap-top {
  margin-top: 18px;
}

.service-mode-hint {
  color: black;
  background-color: $warning-color;
  font-size: 18px;
  font-weight: bold;
  padding: 5px 12px 5px 12px;
}

.gap-bottom {
  margin-bottom: 18px;
}

:host ::ng-deep .mat-mdc-slide-toggle.mat-accent {
  --mdc-switch-selected-handle-color: #008787;
  // --mdc-switch-unselected-handle-color
}

:host ::ng-deep .mdc-switch:enabled .mdc-switch__track::after {
  background-color: #0087878a !important;
}
