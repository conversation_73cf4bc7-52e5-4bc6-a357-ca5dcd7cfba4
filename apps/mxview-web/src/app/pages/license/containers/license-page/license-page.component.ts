
import { Component, OnInit } from '@angular/core';
import { DataService } from '@mxview-web/app/shared/Service/mx-platform/Service/DataService';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';


@UntilDestroy()
@Component({
  templateUrl: './license-page.component.html',
  styleUrls: ['./license-page.component.scss'],
})
export class LicensePageComponent implements OnInit {
  constructor(private dataService: DataService) {}
  isLicenseManagedByCentral = false;


  ngOnInit(): void {
      this.dataService.getMode().pipe(untilDestroyed(this)).subscribe(value => this.isLicenseManagedByCentral = value.mode === 'central_local')
  }

}
