<div class="app-content-container">
  <ng-container *ngIf="isLicenseManagedByCentral">
    <div class="gap-top" fxLayout="row" fxLayoutAlign="center center">
      <div class="service-mode-hint">{{ 'LICENSE.managed_by_central' | translate }}</div>
    </div>
    <div class="page-title gap-bottom">{{ 'LICENSE.title' | translate }}</div>
  </ng-container>
  <h1 *ngIf="!isLicenseManagedByCentral" class="page-title">{{ 'LICENSE.title' | translate }}</h1>

  <mat-tab-group>
    <mat-tab [label]="'LICENSE.mxview' | translate">
      <ng-template matTabContent>
        <app-mxview-one-license-tab></app-mxview-one-license-tab>
      </ng-template>
    </mat-tab>
    <mat-tab [label]="'LICENSE.ips' | translate">
      <ng-template matTabContent>
        <app-ips-license-tab></app-ips-license-tab>
      </ng-template>
    </mat-tab>
  </mat-tab-group>
</div>
