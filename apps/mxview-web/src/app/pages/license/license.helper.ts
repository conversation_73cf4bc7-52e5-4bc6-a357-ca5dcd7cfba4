import { TranslateService } from '@ngx-translate/core';

import { DisableDeactivateButton, LicenseList, LicenseStatus, LicenseType } from './models/license.model';
import { Overview } from '../firewall-policy-management/system/license-response.model';

export const validIpsDevice = 'Valid (IPS-DEVICE)';

export const getIPSStatus = (license: Overview, translate: TranslateService) => {
  if (license.status === LicenseStatus.Valid) {
    return translate.instant('pages.license.ips_tab.valid');
  }

  if (license.status === LicenseStatus.Deactivated) {
    return translate.instant('pages.license.ips_tab.deactivated');
  }

  return translate.instant('pages.license.ips_tab.depleted');
};

export const disableDeactivateButtonStatus = (license: LicenseList): DisableDeactivateButton => {
  const { typeRow, statusRow } = license || {};

  if (!typeRow || !statusRow) {
    return { noLicense: true, depleted: false, deactivated: false };
  }

  const isTrialOrNew = typeRow === LicenseType.Trial || typeRow === LicenseType.New;
  const isTrialNewOrCv = isTrialOrNew || typeRow === LicenseType.Cv;

  if (isTrialOrNew && statusRow === LicenseStatus.Depleted) {
    return { depleted: true, deactivated: false, noLicense: false };
  }

  if (isTrialNewOrCv && statusRow === LicenseStatus.Deactivated) {
    return { depleted: false, deactivated: true, noLicense: false };
  }

  return { depleted: false, deactivated: false, noLicense: false };
};

export const getLicenseType = (license: Overview, translate: TranslateService) => {
  if (license.type === LicenseType.New && license.status === LicenseStatus.Valid) {
    return translate.instant('pages.license.ips_tab.standard');
  }

  if (license.type === LicenseType.Cv && license.status === LicenseStatus.Valid) {
    return translate.instant('pages.license.ips_tab.customized');
  }

  if (license.type === LicenseType.Trial && license.status === LicenseStatus.Valid) {
    return translate.instant('pages.license.ips_tab.trial');
  }

  if (license.type === LicenseType.Trial && license.status === LicenseStatus.Depleted) {
    return translate.instant('pages.license.ips_tab.trialDepleted');
  }

  if (license.type === LicenseType.New && license.status === LicenseStatus.Reclaimed) {
    return translate.instant('pages.license.ips_tab.reclaimed');
  }

  if (license.type === LicenseType.New && license.status === LicenseStatus.Depleted) {
    return translate.instant('pages.license.ips_tab.standardRunOut');
  }

  if (license.type === LicenseType.Cv && license.status === LicenseStatus.Expired) {
    return translate.instant('pages.license.ips_tab.customizedExpired');
  }

  if (license.type === LicenseType.Trial && license.status === LicenseStatus.Deactivated) {
    return translate.instant('pages.license.ips_tab.trialDeactivated');
  }

  if (license.type === LicenseType.New && license.status === LicenseStatus.Deactivated) {
    return translate.instant('pages.license.ips_tab.standardDeactivated');
  }

  if (license.type === LicenseType.Cv && license.status === LicenseStatus.Deactivated) {
    return translate.instant('pages.license.ips_tab.customizedDeactivated');
  }
  if (license.endTime === 0) {
    return translate.instant('pages.license.ips_tab.noLicense');
  }

  return '---';
};
