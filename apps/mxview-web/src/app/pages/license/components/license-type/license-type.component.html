<h3 mat-dialog-title>{{ 'LICENSE.license_type.title' | translate }}</h3>
<div fxLayout="column" fxLayoutAlign="space-between stretch">
  <div fxLayout="row" class="type-card">
    <div fxLayout="row" fxLayoutAlign="center center" fxFlex="250px" class="card-title">
      {{ 'LICENSE.license_type.trial_title' | translate }}
    </div>
    <div>{{ 'LICENSE.license_type.trial_intro' | translate }}</div>
  </div>
  <div fxLayout="row" class="type-card">
    <div fxLayout="row" fxLayoutAlign="center center" fxFlex="250px" class="card-title">
      {{ 'LICENSE.license_type.node_base_title' | translate }}
    </div>
    <div>{{ 'LICENSE.license_type.node_base_intro' | translate }}</div>
  </div>
  <div fxLayout="row" class="type-card">
    <div fxLayout="row" fxLayoutAlign="center center" fxFlex="250px" class="card-title">
      {{ 'LICENSE.license_type.wireless_addon' | translate }}
    </div>
    <div>{{ 'LICENSE.license_type.wireless_intro' | translate }}</div>
  </div>
  <div fxLayout="row" class="type-card">
    <div fxLayout="row" fxLayoutAlign="center center" fxFlex="250px" class="card-title">
      {{ 'LICENSE.license_type.power_addon' | translate }}
    </div>
    <div>{{ 'LICENSE.license_type.power_intro' | translate }}</div>
  </div>
  <div fxLayout="row" class="type-card">
    <div fxLayout="row" fxLayoutAlign="center center" fxFlex="250px" class="card-title">
      {{ 'LICENSE.license_type.security_addon' | translate }}
    </div>
    <div>{{ 'LICENSE.license_type.security_intro' | translate }}</div>
  </div>
</div>
<div mat-dialog-actions align="end">
  <button mat-button mat-dialog-close color="primary">{{ 'BUTTON.close' | translate }}</button>
</div>
