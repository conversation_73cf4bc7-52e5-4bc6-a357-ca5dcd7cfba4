import { Component, Inject, OnInit } from '@angular/core';
import {
  MatLegacyDialogRef as MatDialogRef,
  MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA,
} from '@angular/material/legacy-dialog';
import { MatLegacySnackBar as MatSnackBar } from '@angular/material/legacy-snack-bar';

import { TranslateService } from '@ngx-translate/core';

import { AppState } from '../../../../app.service';
import { GlobalEvent, GlobalEventType } from '../../../../global.event';
import { ErrorService } from '../../../../shared/Service/error.service';
import { DataService } from '../../../../shared/Service/mx-platform/Service/DataService';

@Component({
  templateUrl: 'deactivation.component.html',
})
export class DeactivationComponent implements OnInit {
  okDisabled = false;
  private siteId;

  constructor(
    public dialogRef: MatDialogRef<DeactivationComponent>,
    private appState: AppState,
    private dataService: DataService,
    private snackBar: MatSnackBar,
    private translateService: TranslateService,
    private errorService: ErrorService,
    @Inject(MAT_DIALOG_DATA) public license: any
  ) {}

  ngOnInit(): void {
    this.siteId = localStorage.getItem('siteId');
  }

  deactivate(): void {
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    this.okDisabled = true;
    this.snackBar.open(this.translateService.instant('LICENSE.deactivating'));
    this.dataService.deleteLicense(this.siteId, this.license).subscribe(
      result => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.okDisabled = false;
        this.snackBar.open(this.translateService.instant('LICENSE.deactivate_success'), '', { duration: 3000 });
        if (result && result[0] && result[0].wait) {
          /**
           * can_deactivate
           *  0: can deactivate
           *  1: Free Addon (can deactivate but will disappear => no record)
           *  2: Expired
           */
          if (result[0].can_deactivate === 0 || result[0].can_deactivate === 1) {
            this.dialogRef.close('relaunch');
          } else {
            this.dialogRef.close('relaunch_cannot_deactivate');
          }
        } else {
          this.dialogRef.close('success');
        }
      },
      () => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.okDisabled = false;
        this.errorService.handleNormalError(this.translateService.instant('LICENSE.deactivate_fail'));
        this.dialogRef.close('fail');
      }
    );
  }
}
