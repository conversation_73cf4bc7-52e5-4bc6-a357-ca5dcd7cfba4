import { Component, inject } from '@angular/core';
import { MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA, MatLegacyDialogRef as MatDialogRef } from '@angular/material/legacy-dialog';
import { SharedModule } from '@mxview-web/app/shared/shared.module';


@Component({
  selector: 'app-device-license-register-dialog',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './device-license-register-dialog.component.html',
  styleUrls: ['./device-license-register-dialog.component.scss']
})
export class DeviceLicenseRegisterDialogComponent {
  dialogData = inject(MAT_DIALOG_DATA);
  dialogRef = inject(MatDialogRef);

  selectedNum = this.dialogData.data.selection.length;

  haveValidIpsLicenseDevice = this.dialogData.data.selection.some(data => data.isValidIPSDevice);
}
