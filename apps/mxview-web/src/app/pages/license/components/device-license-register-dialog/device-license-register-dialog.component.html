<app-simple-dialog>
  <span dia-title>{{ 'pages.license.ips_tab.setDeviceLicense' | translate }}</span>
  <div dia-content>
    <div class="selected-number">
      {{ 'general.dialog.isSelected' | translate : { number: selectedNum } }}
    </div>

    <ng-container *ngIf="haveValidIpsLicenseDevice; else noValidIpsLicenseDevice">
      <div class="info-warning-msg">
        <div>
          <mat-icon class="info-icon">warning</mat-icon>
        </div>
        <div>
          {{ 'pages.license.ips_tab.registerValidIPSLicenseInfo' | translate }}
          <br />
          {{ 'pages.license.ips_tab.setLicenseMsg' | translate }}
        </div>
      </div>
    </ng-container>
    <ng-template #noValidIpsLicenseDevice>
      <div>{{ 'pages.license.ips_tab.setLicenseMsg' | translate }}</div>
    </ng-template>
  </div>

  <button dia-rightBtn [mat-dialog-close]="true" color="primary" mat-raised-button>
    {{ 'BUTTON.register' | translate }}
  </button>
</app-simple-dialog>
