import { Component, OnInit, ChangeDetectionStrategy } from '@angular/core';
import { UntypedFormControl, Validators } from '@angular/forms';
import { MatLegacyDialog as MatDialog, MatLegacyDialogRef as MatDialogRef } from '@angular/material/legacy-dialog';
import { MatLegacySnackBar as MatSnackBar } from '@angular/material/legacy-snack-bar';

import { TranslateService } from '@ngx-translate/core';

import { AppState } from '../../../../../app.service';
import { GlobalEvent, GlobalEventType } from '../../../../../global.event';
import { ErrorService } from '../../../../../shared/Service/error.service';
import { DataService } from '../../../../../shared/Service/mx-platform/Service/DataService';

@Component({
  selector: 'app-activation',
  templateUrl: 'activation.component.html',
  styleUrls: ['./activation.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ActivationComponent implements OnInit {
  activationFormControl = new UntypedFormControl('', [Validators.required]);
  private siteId: string;

  constructor(
    public dialogRef: MatDialogRef<ActivationComponent>,
    private appState: AppState,
    private dataService: DataService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private errorService: ErrorService,
    private translateService: TranslateService
  ) {}

  ngOnInit(): void {
    this.siteId = localStorage.getItem('siteId');
  }

  activate(): void {
    this.snackBar.open(this.translateService.instant('SNACK_BAR.saving'));
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    this.dataService.setLicense(this.siteId, this.activationFormControl.value).subscribe(
      result => {
        if (result && result[0] && result[0].wait) {
          setTimeout(() => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            this.snackBar.open(this.translateService.instant('LICENSE.add_success'), null, { duration: 3000 });
            /**
             * can_deactivate
             *  0: can deactivate
             *  1: Free Addon (can deactivate but will disappear => no record)
             *  2: Expired
             */
            if (result[0].can_deactivate === 0 && result[0].can_deactivate === 1) {
              this.dialogRef.close('relaunch');
            } else {
              this.dialogRef.close('relaunch_cannot_deactivate');
            }
          }, 5000);
        } else {
          this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          this.dialogRef.close('success');
        }
      },
      error => {
        if (error.resultId) {
          let errorCode = error.resultId;
          if (error.resultId === -510) {
            errorCode = error.message.errno;
          }
          this.snackBar.open(this.errorService.handleLicenseError(errorCode), 'Close', { panelClass: ['error'] });
        } else {
          this.errorService.handleNormalError(this.translateService.instant('LICENSE.add_fail'));
        }
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.dialogRef.close('fail');
      }
    );
  }
}
