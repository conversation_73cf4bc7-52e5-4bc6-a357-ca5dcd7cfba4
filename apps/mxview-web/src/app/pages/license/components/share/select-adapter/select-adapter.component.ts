import { Component, OnInit, ChangeDetectionStrategy } from '@angular/core';
import { UntypedFormControl, Validators } from '@angular/forms';

import { AppState } from '../../../../../app.service';
import { GlobalEvent, GlobalEventType } from '../../../../../global.event';
import { NetworkAdapterUnit } from '../../../../../shared/Service/mx-platform/DataDef/LicenseDataDefs';
import { DataService } from '../../../../../shared/Service/mx-platform/Service/DataService';

@Component({
  selector: 'app-select-adapter',
  templateUrl: 'select-adapter.component.html',
  styleUrls: ['./select-adapter.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SelectAdapterComponent implements OnInit {
  adapterFormControl = new UntypedFormControl('', [Validators.required]);
  adapters: NetworkAdapterUnit[] = [];
  private siteId: string;
  private userCode: string;
  private userCodeData = {
    previousUserCode: '',
    currentUserCode: '',
    clickChangeBtnTimeStamp: 0,
  };

  constructor(private appState: AppState, private dataService: DataService) {}

  ngOnInit(): void {
    this.siteId = localStorage.getItem('siteId');
    this.dataService.getNetworkAdapters(this.siteId).subscribe(
      adapters => {
        this.adapters = adapters;
      },
      error => {
        console.log('error to get license');
      }
    );
  }

  submitAdapterChange(): Promise<any> {
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    return new Promise<void>((resolve, reject) => {
      this.dataService.setMacAddress(this.siteId, this.adapterFormControl.value).subscribe(
        res => {
          this.dataService.getUserCode(this.siteId).subscribe(usercode => {
            this.setUserCodeData(usercode[0].usercode);
            this.userCode = usercode[0].usercode;
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            resolve();
          });
        },
        error => {
          console.log('error apply network mac address');
          this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          reject();
        }
      );
    });
  }

  setUserCodeData(newUserCode): void {
    // userCodeData is for debugging
    this.userCodeData.previousUserCode = this.userCode;
    this.userCodeData.clickChangeBtnTimeStamp = Date.now();
    this.userCodeData.currentUserCode = newUserCode;
    localStorage.setItem('userCodeData', JSON.stringify(this.userCodeData));
  }
}
