<div class="padding-bottom">{{ 'LICENSE.network_adapter.select_adapters_desc' | translate }}</div>
<mat-form-field class="form-full-width">
  <mat-select
    id="select-adapters"
    placeholder="{{ 'LICENSE.network_adapter.select_adapters' | translate }}"
    [formControl]="adapterFormControl"
    required
  >
    <mat-option id="option-adapters-{{ i }}" *ngFor="let adapter of adapters; let i = index" [value]="adapter.address">
      {{ adapter.name }}
    </mat-option>
  </mat-select>
</mat-form-field>
