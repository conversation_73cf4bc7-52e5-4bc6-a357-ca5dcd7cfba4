import { ChangeDetectionStrategy, Component, effect, inject, OnInit } from '@angular/core';
import { RouterLink } from '@angular/router';
import { NonNullableFormBuilder, Validators } from '@angular/forms';
import { toSignal } from '@angular/core/rxjs-interop';
import { MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA } from '@angular/material/legacy-dialog';
import { MatLegacyRadioModule as MatRadioModule } from '@angular/material/legacy-radio';

import { TranslateService } from '@ngx-translate/core';


import { Frequency } from './license-notification-dialog.def';
import { LicenseNotification } from './license-notification.model';
import { SharedModule } from '@mxview-web/app/shared/shared.module';
import { PostService } from '@mxview-web/app/shared/http/post.service';
import { SnackBarService } from '@mxview-web/app/shared/security-service/snack-bar.service';
import { SimpleDialogService } from '@mxview-web/app/shared/simple-dialog/simple-dialog.service';

interface DialogData {
  data: { isCustomizedLicense: boolean; hasEmailSetting: boolean; notification: LicenseNotification };
}

@Component({
  selector: 'app-license-notification-dialog',
  standalone: true,
  imports: [SharedModule, MatRadioModule, RouterLink],
  templateUrl: './license-notification-dialog.component.html',
  styleUrls: ['./license-notification-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LicenseNotificationDialogComponent implements OnInit {
  readonly #dialogData = inject<DialogData>(MAT_DIALOG_DATA);
  readonly #fb = inject(NonNullableFormBuilder);
  readonly #translate = inject(TranslateService);
  readonly #postService = inject(PostService);
  readonly #snackBar = inject(SnackBarService);
  readonly #simpleDialogService = inject(SimpleDialogService);

  readonly frequencyOption = [
    {
      value: Frequency.Daily,
      text: this.#translate.instant('pages.license.ips_tab.daily')
    },
    {
      value: Frequency.Weekly,
      text: this.#translate.instant('pages.license.ips_tab.weekly')
    },
    {
      value: Frequency.Monthly,
      text: this.#translate.instant('pages.license.ips_tab.monthly')
    }
  ];

  form = this.#fb.group({
    enabled: this.#fb.control(true, Validators.required),
    value: this.#fb.control(7, [Validators.required, Validators.min(7), Validators.max(180)]),
    email: this.#fb.control('', [Validators.required, Validators.email]),
    frequency: this.#fb.control(Frequency.Monthly, Validators.required)
  });

  readonly title = this.#dialogData.data.isCustomizedLicense
    ? 'pages.license.ips_tab.licenseExpiryNotification'
    : 'pages.license.ips_tab.licenseRunOutNotification';

  readonly statusValueChange = toSignal(this.form.get('enabled').valueChanges, {
    initialValue: true
  });

  readonly hasSettingEmail = this.#dialogData.data.hasEmailSetting;

  constructor() {
    effect(() => {
      const status = this.statusValueChange();
      this.#hideFormControl(status);
    });
  }
  ngOnInit(): void {
    const notification = this.#dialogData.data.notification;
    if (notification.id !== 0) {
      this.form.patchValue({
        enabled: notification.enabled,
        value: notification.value,
        email: notification.email,
        frequency: notification.frequency
      });
    }
  }

  onSubmit() {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }

    this.#postService.sendPut('licenses/notification', this.form.getRawValue()).subscribe(() => {
      this.#simpleDialogService.close(true);
      this.#snackBar.openSuccessSnackBar('pages.license.ips_tab.updateNotification');
    });
  }

  #hideFormControl(show: boolean) {
    if (show) {
      this.form.get('value').enable();
      this.form.get('email').enable();
      this.form.get('frequency').enable();
    } else {
      this.form.get('value').disable();
      this.form.get('email').disable();
      this.form.get('frequency').disable();
    }
  }
}
