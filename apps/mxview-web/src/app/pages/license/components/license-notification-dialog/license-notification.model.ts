import { Frequency } from './license-notification-dialog.def';

export interface LicenseNotification {
  id: number;
  enabled: boolean;
  value: number;
  email: string;
  frequency: Frequency;
  condition: Condition;
  createdAt: string;
  updatedAt: string;
}

interface Condition {
  type: string;
  minimum: number;
  maximum: number;
}

export interface SystemEmail {
  address: string;
  authAccount: string;
  authEnable: boolean;
  authPassword: string;
  port: number;
  provider: string;
  requiredSsl: boolean;
  sender: string;
}
