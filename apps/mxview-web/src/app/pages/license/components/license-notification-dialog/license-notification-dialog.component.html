<app-simple-dialog>
  <span dia-title>{{ title | translate }}</span>

  <div dia-content>
    <ng-container *ngIf="hasSettingEmail; else noSMTP">
      <form [formGroup]="form">
        <div class="radio-btn-field" fxLayout="column" fxLayoutGap="8px">
          <mat-label>{{ 'pages.license.ips_tab.status' | translate }} *</mat-label>
          <mat-radio-group fxLayout="row" color="primary" formControlName="enabled" fxLayoutGap="72px">
            <mat-radio-button [value]="true">{{ 'general.common.enabled' | translate }}</mat-radio-button>
            <mat-radio-button [value]="false">{{ 'general.common.disabled' | translate }}</mat-radio-button>
          </mat-radio-group>
        </div>

        <ng-container *ngIf="form.controls.enabled.value">
          <div fxLayout="column" fxLayoutGap="8px">
            <mat-label class="form-field-title">{{ 'pages.license.ips_tab.condition' | translate }}</mat-label>
            <mat-form-field class="form-field-md">
              <input
                appNumberOnly
                matInput
                placeholder="{{ 'pages.license.ips_tab.below' | translate }}"
                formControlName="value"
              />
              <mat-hint align="start"> 7 - 180 </mat-hint>
              <mat-hint align="end">{{ 'pages.license.ips_tab.daysRemaining' | translate }}</mat-hint>
              <mat-error *ngIf="form.get('value').hasError('required')">
                {{ 'validators.required' | translate }}
              </mat-error>
            </mat-form-field>
          </div>

          <div>
            <mat-form-field class="form-field-md">
              <input
                matInput
                placeholder="{{ 'pages.license.ips_tab.emailRecipient' | translate }}"
                formControlName="email"
              />
              <mat-error *ngIf="form.get('email').hasError('email')">
                {{ 'validators.invalidEmail' | translate }}
              </mat-error>
              <mat-error *ngIf="form.get('email').hasError('required')">
                {{ 'validators.required' | translate }}
              </mat-error>
            </mat-form-field>
          </div>

          <div>
            <mat-form-field class="form-field-md">
              <mat-label>{{ 'pages.license.ips_tab.frequency' | translate }}</mat-label>
              <mat-select formControlName="frequency">
                <mat-option *ngFor="let frequency of frequencyOption" [value]="frequency.value">{{
                  frequency.text
                }}</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </ng-container>
      </form>
    </ng-container>

    <ng-template #noSMTP>
      <div class="info-warning-msg">
        <div>
          <mat-icon class="info-icon">warning</mat-icon>
        </div>
        <div class="warning-msg">
          {{ 'pages.license.ips_tab.navigatorSMTP1' | translate
          }}<a
            [routerLink]="[
              '/pages/system-settings',
              {
                tab: 'email'
              }
            ]"
            mat-dialog-close
            >{{ 'pages.license.ips_tab.navigatorSMTP2' | translate }}</a
          >
          {{ 'pages.license.ips_tab.navigatorSMTP3' | translate }}
        </div>
      </div>
    </ng-template>
  </div>

  <button *ngIf="hasSettingEmail" dia-rightBtn color="primary" mat-raised-button (click)="onSubmit()">
    {{ 'BUTTON.apply' | translate }}
  </button>
</app-simple-dialog>
