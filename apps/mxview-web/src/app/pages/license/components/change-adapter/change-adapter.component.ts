import { Component, OnInit, ViewChild, ChangeDetectionStrategy, ChangeDetectorRef, AfterViewInit } from '@angular/core';
import { MatLegacyDialogRef as MatDialogRef } from '@angular/material/legacy-dialog';

import { SelectAdapterComponent } from '../share/select-adapter/select-adapter.component';

@Component({
  templateUrl: 'change-adapter.component.html',
  styleUrls: ['./change-adapter.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ChangeAdapterComponent implements OnInit {
  @ViewChild('selectAdapter') selectAdapter: SelectAdapterComponent;

  adapterTimer: number;
  showAdaptersAlert = true;
  adapterDisabled = true;
  adapterValid = false;
  private adapterCountDown: any;
  private adapterDiscountTime = 15;

  constructor(public dialogRef: MatDialogRef<ChangeAdapterComponent>, private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    this.adapterTimer = this.adapterDiscountTime;
    this.adapterCountDown = setInterval(() => {
      this.adapterTimer--;
      if (this.adapterTimer === 0) {
        this.adapterDisabled = false;
        window.clearInterval(this.adapterCountDown);
      }
      this.cdr.markForCheck();
    }, 1000);
  }

  showNetworkAdapter(): void {
    this.showAdaptersAlert = false;
    this.cdr.markForCheck();
  }

  onSubmitAdapterChange(): void {
    this.selectAdapter.submitAdapterChange().then(
      () => {
        this.dialogRef.close('success');
      },
      () => {
        this.dialogRef.close('fail');
      }
    );
  }
}
