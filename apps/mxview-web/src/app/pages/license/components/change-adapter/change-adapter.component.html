<ng-container *ngIf="showAdaptersAlert">
  <h3 mat-dialog-title>{{ 'LICENSE.network_adapter.change_network_adapter' | translate }}</h3>
  <div mat-dialog-content>
    <div class="dialog-alert" fxLayout="column" fxLayoutGap="14px">
      <div fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="16px">
        <mat-icon>warning</mat-icon>
        <div class="bold-text">
          {{ 'LICENSE.network_adapter.change_network_adapter_alert_1' | translate }}
        </div>
      </div>
      <div>
        {{ 'LICENSE.network_adapter.change_network_adapter_alert_2' | translate }}
      </div>
    </div>
  </div>
  <div mat-dialog-actions align="end">
    <button
      id="button-deactive-license-confirm"
      mat-button
      color="warn"
      (click)="showNetworkAdapter()"
      [disabled]="adapterDisabled"
    >
      <span>{{ 'BUTTON.confirm' | translate }}</span>
      <span *ngIf="adapterTimer !== 0"> ({{ adapterTimer }})</span>
    </button>
    <button mat-raised-button color="primary" mat-dialog-close>{{ 'BUTTON.cancel' | translate }}</button>
  </div>
</ng-container>
<ng-container *ngIf="!showAdaptersAlert">
  <h3 mat-dialog-title>{{ 'LICENSE.network_adapter.select_adapters' | translate }}</h3>
  <div mat-dialog-content>
    <app-select-adapter #selectAdapter (submitAdapterChange)="onSubmitAdapterChange()"></app-select-adapter>
  </div>
  <div mat-dialog-actions align="end">
    <button mat-button mat-dialog-close color="primary">{{ 'BUTTON.cancel' | translate }}</button>
    <button
      mat-raised-button
      color="primary"
      id="btn-adapter-change"
      [disabled]="!selectAdapter.adapterFormControl.valid"
      (click)="onSubmitAdapterChange()"
    >
      {{ 'BUTTON.apply' | translate }}
    </button>
  </div>
</ng-container>
