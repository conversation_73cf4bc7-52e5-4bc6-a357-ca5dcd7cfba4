<mat-card class="small-card">
  <mat-card-header>
    <mat-card-title class="card-title">
      <span>{{ addonTitle | translate }}</span>
    </mat-card-title>
  </mat-card-header>
  <mat-card-content>
    <div *ngIf="addon.licenseMode === 3 && licenseMode === 1">
      <div id="div-trial-remaining" class="day-count">{{ addon.trialRemaining >= 0 ? addon.trialRemaining : 0 }}</div>
      <div class="days">{{ 'LICENSE.trial_day' | translate }}</div>
    </div>
    <div *ngIf="addon.licenseMode !== 3 || licenseMode === 3">
      {{ addonTrialText | translate }}
    </div>
  </mat-card-content>
  <mat-card-actions class="small-card-action">
    <mat-slide-toggle
      #toggle
      *ngIf="licenseMode !== 1 && trialRemaining > 0"
      [checked]="addon.trial_enable"
      (change)="onAddonChanged($event)"
      [disabled]="otherAddon?.trial_enable"
      [id]="'toggle-start-trial-' + addonType"
    >
      {{ (toggle.checked ? 'LICENSE.enabled' : 'LICENSE.disabled') | translate }}
    </mat-slide-toggle>
    <button
      *ngIf="licenseMode === 1 && addon.licenseMode !== 3"
      mat-raised-button
      color="primary"
      class="action-button"
      [id]="'btn-start-trial-' + addonType"
      (click)="onStartTrial()"
      [disabled]="addon.trialRemaining <= 0 && addon.licenseMode !== 0"
    >
      {{ 'LICENSE.start_free_trial' | translate }}
    </button>
  </mat-card-actions>
</mat-card>
