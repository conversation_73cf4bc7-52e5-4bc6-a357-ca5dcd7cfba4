@import '../../../../../assets/mx-service/font-style.scss';

.small-card {
  width: 284px;
  height: 302px;
  overflow-x: unset;

  .mat-mdc-card-header {
    width: inherit;
  }

  .mat-mdc-card-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    word-break: break-word;
    text-align: justify;
    margin-bottom: 0px;
    width: inherit;
  }

  .small-card-action {
    height: auto;
    width: inherit;
    position: absolute;
    bottom: 26px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;

    .action-button {
      white-space: pre-line;
      width: 162px;
      height: 36px;
    }
  }
}

.card-title {
  font-size: 1.2em;
  font-weight: bold;
}

.day-count {
  font-size: 72px;
  position: absolute;
  left: 50%;
  top: 45%;
  transform: translate(-50%, -45%);
  color: $moxa-green;
}

.days {
  font-size: 20px;
  position: absolute;
  left: 50%;
  top: 65%;
  transform: translate(-50%, -65%);
}
