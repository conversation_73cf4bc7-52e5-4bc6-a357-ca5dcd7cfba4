import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';

@Component({
  selector: 'network-addon-card',
  templateUrl: './addon-card.component.html',
  styleUrls: ['./addon-card.component.scss'],
})
export class AddonCardComponent implements OnInit {
  @Input() addon: any;
  @Input() addonTitle: string;
  @Input() addonTrialText: string;
  @Input() licenseMode: number;
  @Input() trialRemaining: number;
  @Input() otherAddon: any;
  @Input() addonType: number;

  @Output() addonChanged = new EventEmitter<any>();
  @Output() startTrial = new EventEmitter<number>();

  onAddonChanged(event: any): void {
    this.addonChanged.emit(event);
  }

  onStartTrial(): void {
    this.startTrial.emit(this.addonType);
  }

  ngOnInit(): void {
    return
  }
}
