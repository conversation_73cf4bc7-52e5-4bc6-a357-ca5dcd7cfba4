<mat-card>
  <mat-card-header>
    <mat-card-title>{{ 'pages.license.ips_tab.ipsLicensedDevicesManagement' | translate }}</mat-card-title>
  </mat-card-header>
  <mat-card-content>
    <div class="table-tool-bar" fxLayout="row" fxLayoutAlign="space-between center">
      <div fxLayout="row" fxLayoutAlign="start center">
        <button
          (click)="toggleShowAllRows()"
          mat-icon-button
          [matTooltip]="
            showAllRows
              ? ('pages.license.ips_tab.showSelectedDevices' | translate)
              : ('pages.license.ips_tab.showAllDevices' | translate)
          "
        >
          <mat-icon [svgIcon]="showAllRows ? 'selectAll' : 'select'"></mat-icon>
        </button>

        <div *ngIf="selection.selected.length > 0" [matTooltip]="getSetLicenseTooltip()">
          <button [disabled]="isSetDeviceLicenseDisabled()" (click)="setDeviceLicense()" mat-icon-button>
            <mat-icon class="table-icon-action" svgIcon="certified"></mat-icon>
          </button>
        </div>

        <div *ngIf="selection.selected.length > 0" [matTooltip]="getRemoveLicenseTooltip()">
          <button [disabled]="isRemoveDeviceLicenseDisabled()" (click)="removeDeviceLicense()" mat-icon-button>
            <mat-icon class="table-icon-action" svgIcon="uncertified"></mat-icon>
          </button>
        </div>

        <button (click)="getAllDevices()" mat-icon-button matTooltip="{{ 'general.table.refresh' | translate }}">
          <mat-icon>refresh</mat-icon>
        </button>
        <app-selected-chip
          [selectedNum]="selection.selected.length"
          [allNum]="getAllNum()"
          (selectAll)="masterToggle($event)"
          (clearAll)="masterToggle($event)"
        ></app-selected-chip>
      </div>
      <mat-form-field fxFlex="25">
        <mat-icon matPrefix>search</mat-icon>
        <input [formControl]="search" matInput placeholder="{{ 'general.table.search' | translate }}" />
      </mat-form-field>
    </div>
    <table [dataSource]="dataSource" mat-table matSort matSortActive="deviceName" matSortDirection="asc">
      <ng-container matColumnDef="select">
        <th *matHeaderCellDef mat-header-cell>
          <mat-checkbox
            [disabled]="isSelectOnPageDisabled()"
            [checked]="selection.hasValue() && isAllOnPageSelected()"
            [indeterminate]="isSelectOnPageIndeterminate()"
            (change)="$event ? selectOnPage() : null"
            color="primary"
          >
          </mat-checkbox>
        </th>
        <td *matCellDef="let row" mat-cell>
          <mat-checkbox
            [checked]="selection.isSelected(row)"
            [disabled]="licenseCheckboxDisable(row)"
            [matTooltipDisabled]="licenseTooltipDisable(row)"
            (change)="$event ? selection.toggle(row) : null"
            (click)="$event.stopPropagation()"
            color="primary"
            [matTooltip]="licenseTooltip(row)"
          >
          </mat-checkbox>
        </td>
      </ng-container>

      <ng-container matColumnDef="deviceName">
        <th *matHeaderCellDef mat-header-cell mat-sort-header>{{ 'general.common.deviceName' | translate }}</th>
        <td *matCellDef="let element" mat-cell>{{ element.deviceName }}</td>
      </ng-container>

      <ng-container matColumnDef="status">
        <th *matHeaderCellDef mat-header-cell mat-sort-header>{{ 'general.common.status' | translate }}</th>
        <td *matCellDef="let element" mat-cell>
          <div [ngClass]="element.status"></div>
        </td>
      </ng-container>

      <ng-container matColumnDef="location">
        <th *matHeaderCellDef mat-header-cell mat-sort-header>{{ 'pages.license.ips_tab.location' | translate }}</th>
        <td *matCellDef="let element" mat-cell>{{ element.location }}</td>
      </ng-container>

      <ng-container matColumnDef="productModel">
        <th *matHeaderCellDef mat-header-cell mat-sort-header>{{ 'general.common.productModel' | translate }}</th>
        <td *matCellDef="let element" mat-cell>{{ element.productModel }}</td>
      </ng-container>

      <ng-container matColumnDef="serialNumber">
        <th *matHeaderCellDef mat-header-cell mat-sort-header>
          {{ 'pages.license.ips_tab.serialNumber' | translate }}
        </th>
        <td *matCellDef="let element" mat-cell>{{ element.serialNumber }}</td>
      </ng-container>

      <ng-container matColumnDef="mac">
        <th *matHeaderCellDef mat-header-cell mat-sort-header>{{ 'pages.license.ips_tab.mac' | translate }}</th>
        <td *matCellDef="let element" mat-cell>{{ element.mac }}</td>
      </ng-container>

      <ng-container matColumnDef="firmwareVersion">
        <th *matHeaderCellDef mat-header-cell mat-sort-header>
          {{ 'pages.license.ips_tab.firmwareVersion' | translate }}
        </th>
        <td *matCellDef="let element" mat-cell>
          <div class="firmware-group">
            <span> {{ element.firmwareVersion }}</span>
            <mat-icon
              class="primary-icon"
              *ngIf="checkVersionIsLessThan(element.firmwareVersion, firmwareVersion)"
              matTooltipPosition="after"
              matTooltip="{{ 'pages.license.ips_tab.updateFw' | translate : { version: firmwareVersion } }}"
              >info
            </mat-icon>
          </div>
        </td>
      </ng-container>

      <ng-container matColumnDef="group">
        <th *matHeaderCellDef mat-header-cell mat-sort-header>
          {{ 'pages.license.ips_tab.securityGroup' | translate }}
        </th>
        <td *matCellDef="let element" mat-cell>{{ element.group }}</td>
      </ng-container>

      <ng-container matColumnDef="licenseStatus">
        <th *matHeaderCellDef mat-header-cell mat-sort-header>
          {{ 'pages.license.ips_tab.ipsLicenseStatus' | translate }}
        </th>
        <td *matCellDef="let element" mat-cell>
          {{ element.licenseStatus }}
        </td>
      </ng-container>
      <ng-container matColumnDef="licenseStatusInfo">
        <th *matHeaderCellDef mat-header-cell></th>
        <td *matCellDef="let element" mat-cell>
          <mat-icon
            *ngIf="element.isValidIPSDevice"
            class="primary-icon"
            matTooltipPosition="after"
            matTooltip="{{ 'pages.license.ips_tab.validIpsDeviceInfo' | translate }}"
            >info
          </mat-icon>
        </td>
      </ng-container>

      <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
      <tr *matRowDef="let row; columns: displayedColumns" [class.selected-row]="selection.isSelected(row)" mat-row></tr>
    </table>
    <mat-paginator [pageSizeOptions]="[10, 20, 50]" showFirstLastButtons></mat-paginator>
  </mat-card-content>
</mat-card>
