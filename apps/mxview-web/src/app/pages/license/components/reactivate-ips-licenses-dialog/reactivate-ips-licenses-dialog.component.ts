import { Component, inject } from '@angular/core';
import { ClipboardModule } from '@angular/cdk/clipboard';


import { TransferDeactivation } from '../deactivated-licenses/deactivated-licenses.model';
import { SharedModule } from '@mxview-web/app/shared/shared.module';
import { SimpleDialogService } from '@mxview-web/app/shared/simple-dialog/simple-dialog.service';

@Component({
  selector: 'app-reactivate-ips-licenses-dialog',
  standalone: true,
  imports: [SharedModule, ClipboardModule],
  templateUrl: './reactivate-ips-licenses-dialog.component.html',
  styleUrls: ['./reactivate-ips-licenses-dialog.component.scss']
})
export class ReactivateIpsLicensesDialogComponent {
  licenseSiteUrl = 'http://license.moxa.com/';
  readonly #simpleDialogService = inject(SimpleDialogService);

  readonly receivedData: TransferDeactivation = this.#simpleDialogService.getReceivedData().element;
  readonly serverId = this.#simpleDialogService.getReceivedData().serverId;
}
