<app-simple-dialog>
  <span dia-title>{{ 'pages.license.ips_tab.reactivateIPSLicensesTitle' | translate }}</span>
  <div dia-content>
    <mat-horizontal-stepper linear>
      <mat-step>
        <ng-template matStepLabel>{{ 'pages.license.ips_tab.copyDeactivateCode' | translate }}</ng-template>
        <div class="outer-box">
          <div fxLayout="row" fxLayoutAlign="start center">
            <span>{{ 'pages.license.ips_tab.deactivationCode' | translate }}: {{ receivedData.deactivationCode }}</span>
            <button
              [cdkCopyToClipboard]="receivedData.deactivationCode"
              mat-icon-button
              matTooltip="{{ 'BUTTON.copy' | translate }}"
            >
              <mat-icon>content_copy</mat-icon>
            </button>
          </div>
        </div>
        <div fxLayout="row" fxLayoutAlign="end start">
          <button [mat-dialog-close]="false" color="primary" mat-button>
            {{ 'BUTTON.cancel' | translate }}
          </button>
          <button mat-raised-button matStepperNext color="primary">{{ 'BUTTON.next' | translate }}</button>
        </div>
      </mat-step>

      <mat-step>
        <ng-template matStepLabel>{{ 'pages.license.ips_tab.loginLicenseSite' | translate }}</ng-template>
        <ol>
          <li>
            {{ 'pages.license.ips_tab.addLicenseStep1' | translate }}
            <a class="hyper-link" href="{{ licenseSiteUrl }}" target="_blank">
              {{ 'pages.license.ips_tab.licenseSite' | translate }}
              <mat-icon class="link-icon" color="primary">open_in_new</mat-icon>
            </a>
            <span>.</span>
          </li>
          <li>{{ 'pages.license.ips_tab.reactivateLicenseStep2' | translate }}</li>
          <li>
            {{ 'pages.license.ips_tab.reactivateLicenseStep3' | translate }}
            <ul>
              <li>{{ 'pages.license.ips_tab.oldUUID' | translate }}{{ serverId }}</li>
              <li>{{ 'pages.license.ips_tab.newUUID' | translate }}</li>
              <li>{{ 'pages.license.ips_tab.deactivateCode' | translate }}{{ receivedData.deactivationCode }}</li>
            </ul>
          </li>
        </ol>
        <div fxLayout="row" fxLayoutAlign="end start">
          <button mat-button color="primary" matStepperPrevious>{{ 'BUTTON.back' | translate }}</button>
          <button color="primary" mat-raised-button matStepperNext>{{ 'BUTTON.next' | translate }}</button>
        </div>
      </mat-step>

      <mat-step>
        <ng-template matStepLabel>{{ 'pages.license.ips_tab.activate' | translate }}</ng-template>
        <div class="outer-box">
          {{ 'pages.license.ips_tab.activateIntroPre' | translate }}
          <a class="hyper-link" href="{{ licenseSiteUrl }}" target="_blank">
            {{ 'pages.license.ips_tab.licenseSite' | translate }}
          </a>
          <span>,</span>
          {{ 'pages.license.ips_tab.reactivateLicenseStep4' | translate }}
        </div>
        <div fxLayout="row" fxLayoutAlign="end start">
          <button mat-button color="primary" matStepperPrevious>{{ 'BUTTON.back' | translate }}</button>
          <button color="primary" mat-raised-button [mat-dialog-close]="true">
            {{ 'BUTTON.finish' | translate }}
          </button>
        </div>
      </mat-step>
    </mat-horizontal-stepper>
  </div>
</app-simple-dialog>
