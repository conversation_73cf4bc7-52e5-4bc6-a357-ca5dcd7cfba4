<mat-card>
  <mat-card-header>
    <mat-card-title>{{ 'pages.license.ips_tab.deactivateLicense' | translate }}</mat-card-title>
  </mat-card-header>
  <mat-card-content>
    <div class="table-tool-bar" fxLayout="row" fxLayoutAlign="space-between center">
      <div fxLayout="row" fxLayoutAlign="start center">
        <button
          (click)="refresh.emit('refresh')"
          mat-icon-button
          matTooltip="{{ 'general.table.refresh' | translate }}"
        >
          <mat-icon>refresh</mat-icon>
        </button>
      </div>
      <mat-form-field fxFlex="25">
        <mat-icon matPrefix>search</mat-icon>
        <input [formControl]="search" matInput placeholder="{{ 'general.table.search' | translate }}" />
      </mat-form-field>
    </div>
    <table [dataSource]="dataSource" mat-table matSort matSortActive="deviceName" matSortDirection="asc">
      <ng-container matColumnDef="updateDate">
        <th *matHeaderCellDef mat-header-cell mat-sort-header>{{ 'pages.license.ips_tab.updateDate' | translate }}</th>
        <td *matCellDef="let element" mat-cell>{{ element.updateDate }}</td>
      </ng-container>

      <ng-container matColumnDef="deactivationCode">
        <th *matHeaderCellDef mat-header-cell mat-sort-header>
          {{ 'pages.license.ips_tab.deactivationCode' | translate }}
        </th>
        <td *matCellDef="let element" mat-cell>{{ element.deactivationCode }}</td>
      </ng-container>

      <ng-container matColumnDef="action">
        <th *matHeaderCellDef mat-header-cell mat-sort-header></th>
        <td *matCellDef="let element" mat-cell>
          <button mat-flat-button color="primary" (click)="reActivate(element)">
            {{ 'pages.license.ips_tab.reActivate' | translate }}
          </button>
        </td>
      </ng-container>

      <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
      <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
    </table>
    <mat-paginator [pageSizeOptions]="[10, 20, 50]" showFirstLastButtons></mat-paginator>
  </mat-card-content>
</mat-card>
