import { Component, effect, EventEmitter, inject, Input, Output, signal, ViewChild } from '@angular/core';
import { NonNullableFormBuilder } from '@angular/forms';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatLegacyPaginator as MatPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';


import { ReactivateIpsLicensesDialogComponent } from '../reactivate-ips-licenses-dialog/reactivate-ips-licenses-dialog.component';
import { TransferDeactivation } from './deactivated-licenses.model';
import { SharedModule } from '@mxview-web/app/shared/shared.module';
import { SimpleDialogService } from '@mxview-web/app/shared/simple-dialog/simple-dialog.service';
import { SharedService } from '@mxview-web/app/shared/shared.service';

@Component({
  selector: 'app-deactivated-licenses',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './deactivated-licenses.component.html',
  styleUrls: ['./deactivated-licenses.component.scss']
})
export class DeactivatedLicensesComponent {
  @Input() serverId: string;

  @Input() set deactivation(value: TransferDeactivation[]) {
    this.deactivationSignal.set(value);
  }
  readonly deactivationSignal = signal([]);

  @Output() refresh = new EventEmitter();

  readonly #fb = inject(NonNullableFormBuilder);
  readonly #simpleDialogService = inject(SimpleDialogService);
  readonly #sharedService = inject(SharedService);

  search = this.#fb.control('');
  searchValue = toSignal(this.search.valueChanges, {
    initialValue: ''
  });

  dataSource = new MatTableDataSource();
  displayedColumns = ['updateDate', 'deactivationCode', 'action'];

  @ViewChild(MatSort) set matSort(sort: MatSort) {
    if (sort) {
      this.dataSource.sort = sort;
    }
  }
  @ViewChild(MatPaginator) set matPaginator(paginator: MatPaginator) {
    if (paginator) {
      this.dataSource.paginator = paginator;
    }
  }

  constructor() {
    effect(() => {
      const searchValue = this.searchValue();
      this.dataSource.filter = searchValue.trim().toLowerCase();
    });

    effect(() => {
      const deactivation = this.deactivationSignal();
      this.dataSource.data = deactivation.map(element => {
        return {
          ...element,
          updateDate: this.#sharedService.transDateTime(element.createdAt)
        };
      });
    });
  }

  reActivate(element: TransferDeactivation) {
    this.#simpleDialogService.openLDialog({
      component: ReactivateIpsLicensesDialogComponent,
      stepper: true,
      data: { element, serverId: this.serverId }
    });
  }
}
