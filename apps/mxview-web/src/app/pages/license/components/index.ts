import { AddLicenseDialogComponent } from "./add-license-dialog/add-license-dialog.component";
import { AddLicenseComponent } from "./add-license/add-license.component";
import { AddonCardComponent } from "./addon-card/addon-card.component";
import { ChangeAdapterComponent } from "./change-adapter/change-adapter.component";
import { DeactivateLicenseDialogComponent } from "./deactivate-license-dialog/deactivate-license-dialog.component";
import { DeactivatedLicensesComponent } from "./deactivated-licenses/deactivated-licenses.component";
import { DeactivationComponent } from "./deactivation/deactivation.component";
import { DeviceLicenseRegisterDialogComponent } from "./device-license-register-dialog/device-license-register-dialog.component";
import { DeviceLicenseComponent } from "./device-license/device-license.component";
import { HistoryIpsComponent } from "./history-ips/history-ips.component";
import { LicenseNotificationDialogComponent } from "./license-notification-dialog/license-notification-dialog.component";
import { LicenseTypeComponent } from "./license-type/license-type.component";
import { OverviewIpsComponent } from "./overview-ips/overview-ips.component";
import { ReactivateIpsLicensesDialogComponent } from "./reactivate-ips-licenses-dialog/reactivate-ips-licenses-dialog.component";
import { ReactivationComponent } from "./reactivation/reactivation.component";
import { RelaunchGatewayComponent } from "./relaunch-gateway/relaunch-gateway.component";
import { ActivationComponent } from "./share/activate-component/activation.component";
import { SelectAdapterComponent } from "./share/select-adapter/select-adapter.component";

export const components: any[] = [
  AddLicenseComponent,
  AddonCardComponent,
  ChangeAdapterComponent,
  DeactivationComponent,
  LicenseTypeComponent,
  ReactivationComponent,
  RelaunchGatewayComponent,
  ActivationComponent,
  SelectAdapterComponent,
  AddLicenseDialogComponent,
  DeviceLicenseComponent
];

export const standaloneComponents: any[] = [
  DeactivateLicenseDialogComponent,
  DeactivatedLicensesComponent,
  DeviceLicenseRegisterDialogComponent,
  HistoryIpsComponent,
  OverviewIpsComponent,
  ReactivateIpsLicensesDialogComponent,
  LicenseNotificationDialogComponent
]


export * from './add-license/add-license.component';
export * from './addon-card/addon-card.component';
export * from './change-adapter/change-adapter.component';
export * from './deactivation/deactivation.component';
export * from './license-type/license-type.component';
export * from './reactivation/reactivation.component';
export * from './relaunch-gateway/relaunch-gateway.component';
export * from './share/activate-component/activation.component';
export * from './share/select-adapter/select-adapter.component';
export * from './deactivate-license-dialog/deactivate-license-dialog.component';
export * from './deactivated-licenses/deactivated-licenses.component';
export * from './history-ips/history-ips.component';
export * from './overview-ips/overview-ips.component';
export * from './reactivate-ips-licenses-dialog/reactivate-ips-licenses-dialog.component';
export * from './add-license-dialog/add-license-dialog.component';
export * from './license-notification-dialog/license-notification-dialog.component'