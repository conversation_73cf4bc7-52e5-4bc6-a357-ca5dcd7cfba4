import { ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA } from '@angular/material/legacy-dialog';

@Component({
  templateUrl: './relaunch-gateway.component.html',
  styleUrls: ['./relaunch-gateway.component.scss'],
})
export class RelaunchGatewayComponent implements OnInit {
  relaunchTimer: number;
  relaunchDiscountTime = 10;
  private relaunchCountDown: any;

  constructor(@Inject(MAT_DIALOG_DATA) public data: any, private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    this.relaunchDiscountTime = this.data.timer ? this.data.timer : 10;
    this.relaunchTimer = this.relaunchDiscountTime;
    this.relaunchCountDown = setInterval(() => {
      this.relaunchTimer--;
      if (this.relaunchTimer === 0) {
        clearInterval(this.relaunchCountDown);
      }
      this.cdr.markForCheck();
    }, 1000);
  }
}
