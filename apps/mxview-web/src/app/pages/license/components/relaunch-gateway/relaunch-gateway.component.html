<h3 mat-dialog-title *ngIf="!data.isDeactivate">{{ 'LICENSE.relaunch.activating' | translate }}</h3>
<h3 mat-dialog-title *ngIf="data.isDeactivate">{{ 'LICENSE.deactivating' | translate }}</h3>
<div mat-dialog-content>
  <p>{{ 'LICENSE.relaunch.active_note' | translate : { timer: relaunchDiscountTime } }}</p>
  <p *ngIf="data.can_deactivate">{{ 'LICENSE.relaunch.cannot_deactivate' | translate }}</p>
</div>
<div mat-dialog-actions align="end">
  <button mat-button mat-dialog-close color="primary" [disabled]="relaunchTimer > 0" id="btn-relaunch">
    {{ 'BUTTON.ok' | translate }}
    <span *ngIf="relaunchTimer !== 0">({{ relaunchTimer }})</span>
  </button>
</div>
