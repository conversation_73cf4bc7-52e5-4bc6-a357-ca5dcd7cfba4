import { Component, inject } from '@angular/core';
import { MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA, MatLegacyDialogRef as MatDialogRef } from '@angular/material/legacy-dialog';

import { LicenseAction } from '../../models/license.model';
import { SharedModule } from '@mxview-web/app/shared/shared.module';
import { PostService } from '@mxview-web/app/shared/http/post.service';
import { SnackBarService } from '@mxview-web/app/shared/security-service/snack-bar.service';


@Component({
  selector: 'app-deactivate-license-dialog',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './deactivate-license-dialog.component.html',
  styleUrls: ['./deactivate-license-dialog.component.scss']
})
export class DeactivateLicenseDialogComponent {
  dialogData = inject(MAT_DIALOG_DATA);
  dialogRef = inject(MatDialogRef);
  readonly #postService = inject(PostService);
  readonly #snackBarService = inject(SnackBarService);

  deactivateLicense() {
    this.#postService.sendPost('licenses/transfer', { action: LicenseAction.Deactivate }).subscribe(data => {
      this.dialogRef.close(data);
      this.#snackBarService.openSuccessSnackBar('pages.license.ips_tab.deactivateIpsLicenses');
    });
  }
}
