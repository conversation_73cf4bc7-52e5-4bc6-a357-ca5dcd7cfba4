import { Component, computed, EventEmitter, inject, Input, Output, signal, WritableSignal } from '@angular/core';
import { MatBadgeModule } from '@angular/material/badge';

import { filter, map } from 'rxjs';


import { AddLicenseDialogComponent } from '../add-license-dialog/add-license-dialog.component';
import { LicenseNotificationDialogComponent } from '../license-notification-dialog/license-notification-dialog.component';
import { LicenseNotification, SystemEmail } from '../license-notification-dialog/license-notification.model';
import { LicenseCategory, LicenseList, LicenseStatus } from '../../models/license.model';
import { SharedModule } from '@mxview-web/app/shared/shared.module';
import { SharedService } from '@mxview-web/app/shared/shared.service';
import { SimpleDialogService } from '@mxview-web/app/shared/simple-dialog/simple-dialog.service';
import { PostService } from '@mxview-web/app/shared/http/post.service';

@Component({
  selector: 'app-overview-ips',
  standalone: true,
  imports: [SharedModule, MatBadgeModule],
  templateUrl: './overview-ips.component.html',
  styleUrls: ['./overview-ips.component.scss']
})
export class OverviewIpsComponent {
  @Input() serverId: string;
  @Input() set overViewData(value: LicenseList) {
    this.ipsLicense.set(value);
  }
  readonly ipsLicense: WritableSignal<LicenseList> = signal(null);

  @Input() set notification(value: LicenseNotification) {
    this.notificationSignal.set(value);
  }
  notificationSignal = signal<LicenseNotification>(null);

  readonly isCustomizedLicense = computed(() => this.ipsLicense()?.isCustomizedLicense);

  @Output() reloadLicense = new EventEmitter();

  readonly LicenseStatus = LicenseStatus;

  readonly #sharedService = inject(SharedService);
  readonly #simpleDialogService = inject(SimpleDialogService);
  readonly #postService = inject(PostService);

  readonly noData = this.#sharedService.noData;

  readonly hasSMTPConfiguration = computed(() => {
    const notification = this.notificationSignal();
    return notification?.id !== 0;
  });

  addLicense(): void {
    this.#simpleDialogService.openLDialog({
      component: AddLicenseDialogComponent,
      stepper: true,
      data: {
        category: LicenseCategory.IPS,
        serverId: this.serverId
      }
    });

    this.#simpleDialogService
      .rightBtnAction()
      .pipe(filter(data => !!data))
      .subscribe(data => {
        this.reloadLicense.emit(data);
      });
  }

  notificationLicense(): void {
    this.#postService
      .sendGetHaveInterface<SystemEmail>('system/email')
      .pipe(map(data => data?.address !== ''))
      .subscribe(data => {
        this.#simpleDialogService.openSDialog({
          component: LicenseNotificationDialogComponent,
          data: {
            isCustomizedLicense: this.isCustomizedLicense(),
            hasEmailSetting: data,
            notification: this.notificationSignal()
          }
        });

        this.#simpleDialogService
          .rightBtnAction()
          .pipe(filter(response => !!response))
          .subscribe(() => {
            this.reloadLicense.emit('refresh');
          });
      });
  }

  calculateRunOutDays(license: LicenseList): string {
    if (!license) {
      return this.noData;
    }

    const node = +license.node;
    const usedNode = +license.usedNode;

    if (usedNode === 0 || node === 0) {
      return this.#sharedService.transDate(new Date().toDateString());
    }

    const runOutDays = Math.floor(node / usedNode);
    const currentDate = new Date();
    const estimatedRunOutDate = new Date(currentDate.setDate(currentDate.getDate() + runOutDays));

    return this.#sharedService.transDate(estimatedRunOutDate.toDateString());
  }
}
