<mat-card>
  <mat-card-header>
    <mat-card-title>{{ 'pages.license.ips_tab.overview' | translate }}</mat-card-title>
  </mat-card-header>
  <mat-card-content>
    <div fxLayout="row" fxLayoutAlign="start start">
      <div fxFlex="20">
        <img src="assets/img/license.png" />
        <dd mat-line>
          <a class="hyper-link" href="http://license.moxa.com/" target="_blank">
            {{ 'pages.license.ips_tab.moxaLicenseSite' | translate }}
            <mat-icon class="link-icon" color="primary">open_in_new</mat-icon>
          </a>
        </dd>
      </div>
      <div fxFlex="20">
        <div class="info-block">
          <span>{{ 'pages.license.ips_tab.name' | translate }} </span>
          <span>
            {{ ipsLicense()?.name || 'pages.license.ips_tab.ips' | translate }}
          </span>
        </div>
      </div>
      <div fxFlex="20">
        <div class="info-block">
          <span>{{ 'pages.license.ips_tab.availablePoints' | translate }}</span>
          <span *ngIf="isCustomizedLicense(); else isNotCustomizedLicensePoint">{{
            'pages.license.ips_tab.unlimited' | translate
          }}</span>
          <ng-template #isNotCustomizedLicensePoint>
            <span [class.text-red]="ipsLicense()?.node === 0">{{
              ipsLicense()?.status !== LicenseStatus.Expired ? ipsLicense()?.node ?? noData : noData
            }}</span>
          </ng-template>
        </div>

        <ng-container *ngIf="isCustomizedLicense(); else isNotCustomizedLicense">
          <div class="info-block">
            <span>{{ 'pages.license.ips_tab.expiryDate' | translate }}</span>
          </div>
          <span [class.text-red]="ipsLicense()?.status === LicenseStatus.Expired">{{
            ipslicense()?.endtime || nodata
          }}</span>
        </ng-container>

        <ng-template #isNotCustomizedLicense>
          <div class="info-block">
            <span>{{ 'pages.license.ips_tab.dailyConsumePoints' | translate }}</span>
            <span>{{
              ipsLicense()?.status !== LicenseStatus.Expired ? ipsLicense()?.usedNode ?? noData : noData
            }}</span>
          </div>

          <div class="info-block">
            <span>{{ 'pages.license.ips_tab.estimatedPointsRunOutDate' | translate }}</span>
            <span>{{ calculateRunOutDays(ipsLicense()) }}</span>
          </div>
        </ng-template>
      </div>

      <div fxFlex="20">
        <div class="info-block">
          <span>{{ 'general.common.status' | translate }}</span>
          <span>{{ ipsLicense()?.status || noData }}</span>
        </div>

        <div class="info-block">
          <span>{{ 'pages.license.ips_tab.serverId' | translate }}</span>
          <span>{{ serverId || noData }}</span>
        </div>
      </div>
    </div>
    <div fxLayout="row" fxLayoutGap="20px">
      <button (click)="addLicense()" color="primary" mat-raised-button>
        {{ 'pages.license.ips_tab.addNewLicense' | translate }}
      </button>
      <button
        [matBadgeHidden]="hasSMTPConfiguration()"
        matBadge="1"
        matBadgeColor="warn"
        (click)="notificationLicense()"
        color="primary"
        mat-raised-button
      >
        {{
          (isCustomizedLicense()
            ? 'pages.license.ips_tab.licenseExpiryNotification'
            : 'pages.license.ips_tab.licenseRunOutNotification'
          ) | translate
        }}
      </button>
    </div>
  </mat-card-content>
</mat-card>
