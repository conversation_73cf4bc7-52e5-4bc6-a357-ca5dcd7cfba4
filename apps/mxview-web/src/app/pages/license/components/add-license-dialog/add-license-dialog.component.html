<app-simple-dialog>
  <span dia-title>{{ 'pages.license.ips_tab.addNewLicense' | translate }}</span>
  <div dia-content>
    <mat-horizontal-stepper linear>
      <mat-step>
        <ng-template matStepLabel>{{ 'pages.license.ips_tab.loginLicenseSite' | translate }}</ng-template>
        <ol>
          <li>
            {{ 'pages.license.ips_tab.addLicenseStep1' | translate }}
            <a class="hyper-link" href="{{ licenseSiteUrl }}" target="_blank">
              {{ 'pages.license.ips_tab.licenseSite' | translate }}
              <mat-icon class="link-icon" color="primary">open_in_new</mat-icon>
            </a>
            <span>.</span>
          </li>
          <li>{{ 'pages.license.ips_tab.addLicenseStep2' | translate : { licenseTypeName: licenseTypeName } }}</li>
          <li>{{ 'pages.license.ips_tab.addLicenseStep3' | translate }}</li>
        </ol>
        <div fxLayout="row" fxLayoutAlign="end start">
          <button [mat-dialog-close]="false" color="primary" mat-button>
            {{ 'BUTTON.cancel' | translate }}
          </button>
          <button color="primary" mat-raised-button matStepperNext>{{ 'BUTTON.next' | translate }}</button>
        </div>
      </mat-step>

      <mat-step>
        <ng-template matStepLabel>{{ 'pages.license.ips_tab.copyId' | translate }}</ng-template>
        <div class="outer-box">
          {{ 'pages.license.ips_tab.copyIntro' | translate }}
          <a class="hyper-link" href="{{ licenseSiteUrl }}" target="_blank">
            {{ 'pages.license.ips_tab.licenseSite' | translate }}
          </a>
          <span>.</span>
          <div>
            <span>{{ 'pages.license.ips_tab.serverId' | translate }}: {{ receivedData.serverId }}</span>
            <button
              [cdkCopyToClipboard]="receivedData.serverId"
              mat-icon-button
              matTooltip="{{ 'BUTTON.copy' | translate }}"
            >
              <mat-icon>content_copy</mat-icon>
            </button>
          </div>
        </div>
        <div fxLayout="row" fxLayoutAlign="end start">
          <button mat-button color="primary" matStepperPrevious>{{ 'BUTTON.back' | translate }}</button>
          <button mat-raised-button matStepperNext color="primary">{{ 'BUTTON.next' | translate }}</button>
        </div>
      </mat-step>

      <mat-step>
        <ng-template matStepLabel>{{ 'pages.license.ips_tab.activate' | translate }}</ng-template>
        <div class="outer-box">
          {{ 'pages.license.ips_tab.activateIntroPre' | translate }}
          <a class="hyper-link" href="{{ licenseSiteUrl }}" target="_blank">
            {{ 'pages.license.ips_tab.licenseSite' | translate }}
          </a>
          <span>.</span>
          <div>
            <mat-form-field style="width: 620px">
              <textarea
                [errorStateMatcher]="errorMatcher"
                [formControl]="formControl"
                style="word-break: break-all"
                rows="3"
                matInput
                placeholder="{{ 'pages.license.ips_tab.activationCode' | translate }}"
              >
              </textarea>
              <mat-error *ngIf="formControl.hasError('required')"> {{ 'validators.required' | translate }} </mat-error>
            </mat-form-field>
          </div>
        </div>
        <div fxLayout="row" fxLayoutAlign="end start">
          <button mat-button color="primary" matStepperPrevious>{{ 'BUTTON.back' | translate }}</button>
          <button
            [disabled]="formControl.invalid || !formControl.dirty"
            (click)="activateLicense()"
            color="primary"
            mat-raised-button
          >
            {{ 'BUTTON.apply' | translate }}
          </button>
        </div>
      </mat-step>
    </mat-horizontal-stepper>
  </div>
</app-simple-dialog>
