import { Component, Inject } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA } from '@angular/material/legacy-dialog';
import { MatLegacySnackBar as MatSnackBar } from '@angular/material/legacy-snack-bar';
import { AppState } from '@mxview-web/app/app.service';
import { GlobalEvent, GlobalEventType } from '@mxview-web/app/global.event';

import { PostService } from '@mxview-web/app/shared/http/post.service';
import { ErrorMatcher } from '@mxview-web/app/shared/security-validator/error.matcher';
import { SimpleDialogService } from '@mxview-web/app/shared/simple-dialog/simple-dialog.service';
import { TranslateService } from '@ngx-translate/core';
import { finalize } from 'rxjs';

interface DialogData {
  data: {
    category: string;
    serverId: string;
  };
}

@Component({
  selector: 'app-add-license-dialog',
  templateUrl: './add-license-dialog.component.html',
  styleUrls: ['./add-license-dialog.component.scss']
})
export class AddLicenseDialogComponent {
  errorMatcher = new ErrorMatcher();
  formControl = new FormControl('', Validators.required);
  receivedData = this._simpleDialogService.getReceivedData();
  licenseSiteUrl = 'http://license.moxa.com/';
  licenseTypeName = this.dialogData.data?.category === 'NSM' ? 'MXsecurity' : 'Security Package';

  constructor(
    private _postService: PostService,
    private _simpleDialogService: SimpleDialogService,
    private _snackBar: MatSnackBar,
    private translateService: TranslateService,
    private appState: AppState,
    @Inject(MAT_DIALOG_DATA)
    public dialogData: DialogData
  ) {}

  activateLicense(): void {
    this._snackBar.open(this.translateService.instant('SNACK_BAR.saving'));
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    this._postService
      .sendPost('licenses', {
        category: this._simpleDialogService.getReceivedData().category,
        activationCode: this.formControl.value
      }).pipe(
        finalize(() =>
          this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }))
        )
      )
      .subscribe(data => {
        this._simpleDialogService.close(data);
        this._snackBar.open(this.translateService.instant('pages.license.ips_tab.addLicenseSuccess'));
      });
  }
}
