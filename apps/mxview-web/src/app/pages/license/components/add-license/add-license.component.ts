import { Component, ViewChild, ChangeDetectionStrategy, ChangeDetectorRef, OnInit, Inject } from '@angular/core';
import {
  MatLegacyDialogRef as MatDialogRef,
  MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA,
} from '@angular/material/legacy-dialog';

import { AppState } from '../../../../app.service';
import { GlobalEvent, GlobalEventType } from '../../../../global.event';
import { ActivationComponent } from '../share/activate-component/activation.component';
import { SelectAdapterComponent } from '../share/select-adapter/select-adapter.component';
import { GlobalService } from '../../../../shared/Service/global-services';
import { DataService } from '../../../../shared/Service/mx-platform/Service/DataService';

@Component({
  templateUrl: 'add-license.component.html',
  styleUrls: ['./add-license.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AddLicenseComponent implements OnInit {
  @ViewChild('selectAdapter') selectAdapter: SelectAdapterComponent;
  @ViewChild('activation') activation: ActivationComponent;

  userCode: string;
  showSelectAdapter = false;
  private siteId;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialogRef: MatDialogRef<AddLicenseComponent>,
    private appState: AppState,
    private dataService: DataService,
    private cdr: ChangeDetectorRef,
    private globalService: GlobalService
  ) {
    this.dialogRef.disableClose = true;
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
  }

  ngOnInit(): void {
    this.siteId = localStorage.getItem('siteId');
    this.dataService.getUserCode(this.siteId).subscribe(
      values => {
        this.userCode = values[0].usercode;
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
      },
      () => {
        // No User Code
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
      }
    );
  }

  copy(): void {
    const text = this.userCode;
    this.globalService.copy(text);
  }

  onSubmitAdapterChange(): void {
    this.selectAdapter.submitAdapterChange().then(() => {
      const userCodeData = JSON.parse(localStorage.getItem('userCodeData'));
      this.userCode = userCodeData.currentUserCode;
      this.cdr.detectChanges();
    });
  }

  onActivateLicense(): void {
    this.activation.activate();
  }
}
