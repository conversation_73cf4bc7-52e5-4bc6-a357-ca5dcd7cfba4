<h3 mat-dialog-title>{{ 'LICENSE.add_new_license.title' | translate }}</h3>
<mat-horizontal-stepper linear>
  <mat-step label="{{ 'LICENSE.add_new_license.login_license_site' | translate }}">
    <div fxLayout="column" class="loginStep">
      <div>
        {{ 'LICENSE.add_new_license.license_site_step_1_pre' | translate }}
        <a class="hyper-link" href="http://license.moxa.com/" target="_blank">
          {{ 'LICENSE.add_new_license.license_site_step_1_link' | translate }}
          <mat-icon class="link-icon" color="primary">open_in_new</mat-icon>
        </a>
        {{ 'LICENSE.add_new_license.license_site_step_1_suf' | translate }}
      </div>
      <div>{{ 'LICENSE.add_new_license.license_site_step_2' | translate }}</div>
      <div>{{ 'LICENSE.add_new_license.license_site_step_3' | translate }}</div>
      <div fxLayout="column" fxLayoutAlign="center center">
        <div class="code-block" fxLayout="column" fxLayoutAlign="center start">
          <div>Your registration code (Type: MXview One NEW)</div>
          <div>
            <span>(Model name: </span>
            <span class="code-color">LIC-MXviewOne-NEW-XN-SR</span>
            <span>) is</span>
          </div>
          <div class="code-color">xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx</div>
        </div>
      </div>
    </div>
    <div mat-dialog-actions align="end">
      <button id="button-license-close-1" mat-button color="primary" mat-dialog-close>
        {{ 'BUTTON.close' | translate }}
      </button>
      <button mat-raised-button color="primary" id="btn-license-next" matStepperNext>
        {{ 'BUTTON.next' | translate }}
      </button>
    </div>
  </mat-step>
  <mat-step *ngIf="showSelectAdapter" label="{{ 'LICENSE.add_new_license.select_network_adapter' | translate }}">
    <app-select-adapter #selectAdapter></app-select-adapter>
    <div mat-dialog-actions align="end">
      <button id="button-license-close-2" mat-button color="primary" mat-dialog-close>
        {{ 'BUTTON.close' | translate }}
      </button>
      <button
        mat-raised-button
        color="primary"
        [disabled]="!selectAdapter.adapterFormControl.valid"
        (click)="onSubmitAdapterChange()"
        id="btn-adapter-change"
        matStepperNext
      >
        {{ 'BUTTON.next' | translate }}
      </button>
    </div>
  </mat-step>
  <mat-step label="{{ 'LICENSE.add_new_license.copy_user_code' | translate }}">
    <div class="loginStep">
      <div>
        {{ 'LICENSE.add_new_license.copy_user_code_intro_pre' | translate }}
        <a class="hyper-link" href="http://license.moxa.com/" target="_blank">
          {{ 'LICENSE.add_new_license.copy_user_code_intro_link' | translate }}
          <mat-icon class="link-icon" color="primary">open_in_new</mat-icon>
        </a>
        {{ 'LICENSE.add_new_license.copy_user_code_intro_suf' | translate }}
      </div>
      <div class="user-code-div" fxLayout="row" fxLayoutGap="5px">
        <div id="div-user-code">{{ 'LICENSE.user_code' | translate }}{{ userCode }}</div>
        <button mat-icon-button matTooltip="{{ 'BUTTON.copy' | translate }}" (click)="copy()" id="btn-copy-user-code">
          <mat-icon>content_copy</mat-icon>
        </button>
      </div>
      <div mat-dialog-actions align="end">
        <button id="button-license-close-3" mat-button color="primary" mat-dialog-close>
          {{ 'BUTTON.close' | translate }}
        </button>
        <button mat-raised-button color="primary" id="btn-user-code-next" matStepperNext>
          {{ 'BUTTON.next' | translate }}
        </button>
      </div>
    </div>
  </mat-step>
  <mat-step label="{{ 'LICENSE.add_new_license.activate' | translate }}">
    <app-activation #activation></app-activation>
    <div mat-dialog-actions align="end">
      <button id="button-license-close-4" mat-button color="primary" mat-dialog-close>
        {{ 'BUTTON.close' | translate }}
      </button>
      <button
        mat-raised-button
        color="primary"
        id="btn-active-license"
        [disabled]="!activation.activationFormControl.valid"
        (click)="onActivateLicense()"
      >
        {{ 'BUTTON.apply' | translate }}
      </button>
    </div>
  </mat-step>
</mat-horizontal-stepper>
