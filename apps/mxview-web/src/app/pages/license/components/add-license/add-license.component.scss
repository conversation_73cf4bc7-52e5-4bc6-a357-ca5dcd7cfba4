@import '../../../../../assets/mx-service/font-style.scss';

.loginStep {
  line-height: 22px;

  .user-type {
    list-style-type: disc;
    margin: 0;
  }

  .code-block {
    border: 2px solid rgba(0, 0, 0, 0.12);
    margin-top: 10px;
    font-size: 18px;
    padding: 10px;
    border-radius: 5px;
  }

  .code-color {
    color: #4472c4;
  }
}

.user-code-div {
  align-items: center;
}

:host ::ng-deep {
  .mat-stepper-horizontal {
    padding-left: 8px;
    padding-right: 8px;
  }

  .mat-horizontal-stepper-header {
    flex-direction: column;
    flex: 1;
    height: 104px !important;
    padding-left: 16px !important;
    padding-right: 16px !important;
    pointer-events: none !important;
  }

  .mat-horizontal-stepper-header-container {
    padding-bottom: 16px;
  }

  .mat-horizontal-stepper-header .mat-step-icon,
  .mat-horizontal-stepper-header .mat-step-icon-not-touched {
    margin-top: 24px;
    margin-right: 0 !important;
  }

  .mat-step-label {
    margin-top: 16px;
    width: 100%;
    word-break: break-word;
    white-space: pre-wrap;
    text-align: center;
  }

  .mat-step-text-label {
    text-align: center;
    text-overflow: initial;
  }

  .mat-stepper-horizontal-line {
    margin-top: -32px !important;
    max-width: 32px;
  }
}

.copy-icon {
  vertical-align: sub;
  font-size: 16px;
  height: 16px !important;
  width: 16px !important;
}
