<h3 mat-dialog-title>{{ 'LICENSE.reactivate_license.title' | translate }}</h3>
<mat-horizontal-stepper linear>
  <mat-step label="{{ 'LICENSE.reactivate_license.login_license_site' | translate }}">
    <div fxLayout="column" class="loginStep">
      <div>
        {{ 'LICENSE.reactivate_license.license_site_step_1_pre' | translate }}
        <a class="hyper-link" href="http://license.moxa.com/" target="_blank">
          {{ 'LICENSE.reactivate_license.license_site_step_1_link' | translate }}
          <mat-icon class="link-icon" color="primary">open_in_new</mat-icon>
        </a>
        {{ 'LICENSE.reactivate_license.license_site_step_1_suf' | translate }}
      </div>
      <div>{{ 'LICENSE.reactivate_license.license_site_step_2' | translate }}</div>
      <div>{{ 'LICENSE.reactivate_license.license_site_step_3' | translate }}</div>
    </div>
    <div mat-dialog-actions align="end">
      <button mat-button color="primary" mat-dialog-close>{{ 'BUTTON.close' | translate }}</button>
      <button mat-raised-button color="primary" id="btn-reactivate-next" matStepperNext>
        {{ 'BUTTON.next' | translate }}
      </button>
    </div>
  </mat-step>
  <mat-step label="{{ 'LICENSE.reactivate_license.copy_deactivate_code' | translate }}">
    <div fxLayout="column" fxLayoutGap="8px">
      <div>
        {{ 'LICENSE.reactivate_license.copy_deactivate_code_intro_pre' | translate }}
        <a class="hyper-link" href="http://license.moxa.com/" target="_blank">
          {{ 'LICENSE.reactivate_license.copy_deactivate_code_intro_link' | translate }}
        </a>
        {{ 'LICENSE.reactivate_license.copy_deactivate_code_intro_suf' | translate }}
      </div>
      <div *ngIf="showDeactivationCode" fxLayout="row" fxLayoutGap="5px">
        <div fxflex class="white-space">{{ 'LICENSE.deactivation_code' | translate }}:</div>
        <div id="div-deactivate-code" class="word-break">
          {{ deactivationCode }}
          <button
            mat-icon-button
            matTooltip="{{ 'BUTTON.copy' | translate }}"
            id="btn-copy-deactivate-code"
            (click)="copy(deactivationCode)"
          >
            <mat-icon>filter_none</mat-icon>
          </button>
        </div>
      </div>
    </div>
    <div mat-dialog-actions align="end">
      <button mat-button color="primary" mat-dialog-close>{{ 'BUTTON.close' | translate }}</button>
      <button mat-raised-button color="primary" id="btn-copy-deactivate-code-next" matStepperNext>
        {{ 'BUTTON.next' | translate }}
      </button>
    </div>
  </mat-step>
  <mat-step label="{{ 'LICENSE.reactivate_license.copy_user_code' | translate }}">
    <div>
      {{ 'LICENSE.reactivate_license.copy_user_code_intro_pre' | translate }}
      <a class="hyper-link" href="http://license.moxa.com/" target="_blank">
        {{ 'LICENSE.reactivate_license.copy_user_code_intro_link' | translate }}
      </a>
      {{ 'LICENSE.reactivate_license.copy_user_code_intro_suf' | translate }}
    </div>
    <div class="user-code-div" fxLayout="row" fxLayoutGap="5px">
      <div id="div-user-code">{{ 'LICENSE.user_code' | translate }}{{ userCode }}</div>
      <button
        mat-icon-button
        matTooltip="{{ 'BUTTON.copy' | translate }}"
        id="btn-copy-user-code"
        (click)="copy(userCode)"
      >
        <mat-icon>filter_none</mat-icon>
      </button>
    </div>
    <div mat-dialog-actions align="end">
      <button mat-button color="primary" mat-dialog-close>{{ 'BUTTON.close' | translate }}</button>
      <button mat-raised-button color="primary" id="btn-copy-user-code-next" matStepperNext>
        {{ 'BUTTON.next' | translate }}
      </button>
    </div>
  </mat-step>
  <mat-step label="{{ 'LICENSE.reactivate_license.activate' | translate }}">
    <app-activation #activation></app-activation>
    <div mat-dialog-actions align="end">
      <button mat-button color="primary" mat-dialog-close>{{ 'BUTTON.close' | translate }}</button>
      <button
        mat-raised-button
        color="primary"
        [disabled]="!activation.activationFormControl.valid"
        id="btn-activate-license"
        (click)="onActivateLicense()"
      >
        {{ 'BUTTON.apply' | translate }}
      </button>
    </div>
  </mat-step>
</mat-horizontal-stepper>
