@import '../../../../../assets/mx-service/font-style.scss';
@import '../../../../../assets/mx-service/shared.scss';

.loginStep {
  line-height: 22px;
}

.user-code-div {
  align-items: center;
}

.word-break {
  word-break: break-word;
}

.white-space {
  white-space: nowrap;
}

:host ::ng-deep {
  .mat-stepper-horizontal {
    padding-left: 8px;
    padding-right: 8px;
  }

  .mat-horizontal-stepper-header {
    flex-direction: column;
    flex: 1;
    height: 109px !important;
    padding-left: 16px !important;
    padding-right: 16px !important;
    pointer-events: none !important;
  }

  .mat-horizontal-stepper-header-container {
    padding-bottom: 16px;
  }

  .mat-horizontal-stepper-header .mat-step-icon,
  .mat-horizontal-stepper-header .mat-step-icon-not-touched {
    margin-top: 24px;
    margin-right: 0 !important;
  }

  .mat-step-label {
    margin-top: 16px;
    width: 100%;
    word-break: break-word;
    white-space: pre-wrap;
    text-align: center;
  }

  .mat-step-text-label {
    text-align: center;
    text-overflow: initial;
  }

  .mat-stepper-horizontal-line {
    margin-top: -32px !important;
    max-width: 32px;
  }
}
