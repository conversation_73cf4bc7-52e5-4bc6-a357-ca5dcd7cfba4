import { Component, OnInit, ViewChild, ChangeDetectionStrategy, Inject } from '@angular/core';
import {
  MatLegacyDialogRef as MatDialogRef,
  MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA,
} from '@angular/material/legacy-dialog';

import { AppState } from '../../../../app.service';
import { GlobalEvent, GlobalEventType } from '../../../../global.event';
import { GlobalService } from '../../../../shared/Service/global-services';
import { ActivationComponent } from '../share/activate-component/activation.component';
import { DataService } from '../../../../shared/Service/mx-platform/Service/DataService';

@Component({
  templateUrl: 'reactivation.component.html',
  styleUrls: ['./reactivation.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReactivationComponent implements OnInit {
  @ViewChild('activation') activation: ActivationComponent;

  userCode: string;
  showDeactivationCode = false;
  private siteId: string;

  constructor(
    public dialogRef: MatDialogRef<ReactivationComponent>,
    private appState: AppState,
    private dataService: DataService,
    private globalService: GlobalService,
    @Inject(MAT_DIALOG_DATA) public deactivationCode: any
  ) {
    this.dialogRef.disableClose = true;
    if (deactivationCode) {
      this.showDeactivationCode = true;
    }
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
  }

  ngOnInit(): void {
    this.siteId = localStorage.getItem('siteId');
    this.dataService.getUserCode(this.siteId).subscribe(
      values => {
        this.userCode = values[0].usercode;
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
      },
      error => {
        // No User Code
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
      }
    );
  }

  copy(text): void {
    this.globalService.copy(text);
  }

  onActivateLicense(): void {
    this.activation.activate();
  }
}
