import { ModuleWithProviders } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { LicensePageComponent } from './containers';

const routes: Routes = [
  {
    path: '',
    component: LicensePageComponent,
  },
];

export const routedComponents = [LicensePageComponent];

export const LicenseRoutingModule: ModuleWithProviders<RouterModule> = RouterModule.forChild(routes);
