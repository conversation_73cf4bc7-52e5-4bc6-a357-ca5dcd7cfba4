<div
  class="login-outerframe"
  ngClass="login-background"
  [ngClass.xs]="{ 'login-background': false, 'login-background-xs': true }"
  fxLayout="row"
  [fxLayoutAlign]="layoutAlign"
>
  <div class="inner-frame" fxFlexLayout="row" fxFlexAlign="center center" #innerFrame>
    <div class="logo-frame" fxLayout="row" fxLayoutAlign="center center">
      <img src="/../assets/img/logo.svg" fxFlexLayout="column" />
    </div>
    <div class="input-block">
      <form [formGroup]="loginForm">
        <mat-form-field class="username">
          <input
            matInput
            id="input-userName"
            maxlength="32"
            placeholder="{{ 'LOGIN.username' | translate }}"
            formControlName="userName"
            (keyup.enter)="login()"
          />
          <mat-error *ngIf="loginForm.controls['userName'].hasError('required')">
            {{ 'ADD_DEVICE.field_required' | translate }}</mat-error
          >
        </mat-form-field>
        <mat-form-field class="password suffix-form-field">
          <input
            matInput
            id="input-password"
            maxlength="63"
            placeholder="{{ 'LOGIN.password' | translate }}"
            formControlName="password"
            [type]="hidePassword ? 'password' : 'text'"
            (keyup.enter)="login()"
          />
          <button type="button" mat-icon-button type="button" (click)="hidePassword = !hidePassword">
            <mat-icon> {{ hidePassword ? 'visibility_off' : 'visibility' }} </mat-icon>
          </button>
          <mat-error *ngIf="loginForm.controls['password'].hasError('required')">
            {{ 'ADD_DEVICE.field_required' | translate }}</mat-error
          >
        </mat-form-field>
      </form>
      <div *ngIf="accountReachLimit" class="login-error-msg">{{ 'LOGIN.account_reach_limit' | translate }}</div>
      <div *ngIf="showLoginFailGeneralMessage" class="login-error-msg">{{ 'LOGIN.error' | translate }}</div>
      <div *ngIf="showLoginFailMessage" class="login-error-msg">{{ loginFailMessage }}</div>
      <button mat-raised-button id="button-login" color="primary" class="bt-login" (click)="login()">
        {{ 'LOGIN.sign_in' | translate }}
      </button>
    </div>
    <div class="logo" fxFlexAlign="center" fxLayoutAlign="center center">
      <img src="/../assets/img/logo_w.png" />
    </div>
    <div class="login-msg">{{ loginMessage }}</div>
    <div *ngIf="isNotChrome" class="right-anno-block xs mat-body-1">
      Please use <a href="https://www.google.com/chrome">Chrome</a> to improve your experience.
    </div>
    <div class="right-anno-block mat-body-1">Copyright Moxa, Inc. All Rights Reserved.</div>
  </div>
</div>
