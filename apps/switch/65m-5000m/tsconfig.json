{"extends": "../../../tsconfig.base.json", "files": [], "include": [], "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.spec.json"}, {"path": "./tsconfig.editor.json"}], "compilerOptions": {"target": "es2022", "forceConsistentCasingInFileNames": true, "strict": false, "noImplicitOverride": false, "noPropertyAccessFromIndexSignature": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": true}, "angularCompilerOptions": {"enableI18nLegacyMessageIdFormat": false, "strictInjectionParameters": true, "strictInputAccessModifiers": true, "strictTemplates": false}}