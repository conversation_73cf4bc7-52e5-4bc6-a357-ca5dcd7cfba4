<?xml version="1.0" encoding="UTF-8"?>
<Device version="3.0">

  <!--
value: 0x00      = grey/black
       0x01-0x0F = green
       0x11-0x1F = amber
       0x21-0x2F = red
-->
  <Information Name="EDS-4012-8P-4GS" Location="0, 0" image="EDS-4012-8P-4GS.png">
    <!-- System LED -->
    <LEDLayout>
      <LED name="STATE" LocationX="147" LocationY="238" Width="9" Height="8" Shape="Rect">
        <Status>
          <value status="off" color="grey"/>
          <value status="on" color="green"/>
          <value status="blink1Hz" color="green"/>
          <value status="blink4Hz" color="green"/>
          <value status="on" color="red"/>
          <value status="blink1Hz" color="red"/>
          <value status="blink4Hz" color="red"/>
        </Status>
      </LED>
      <LED name="FAULT" LocationX="147" LocationY="247" Width="9" Height="8" Shape="Rect">
        <Status>
          <value status="off" color="grey"/>
          <value status="on" color="red"/>
          <value status="blink1Hz" color="red"/>
          <value status="blink4Hz" color="red"/>
        </Status>
      </LED>
      <LED name="PWR1" LocationX="147" LocationY="256" Width="9" Height="8" Shape="Rect">
        <Status>
          <value status="off" color="grey"/>
          <value status="on" color="amber"/>
          <value status="blink1Hz" color="amber"/>
          <value status="blink4Hz" color="amber"/>
        </Status>
      </LED>
      <LED name="PWR2" LocationX="147" LocationY="265" Width="9" Height="8" Shape="Rect">
        <Status>
          <value status="off" color="grey"/>
          <value status="on" color="amber"/>
          <value status="blink1Hz" color="amber"/>
          <value status="blink4Hz" color="amber"/>
        </Status>
      </LED>
      <LED name="Master" LocationX="147" LocationY="274" Width="9" Height="8" Shape="Rect">
        <Status>
          <value status="off" color="grey"/>
          <value status="on" color="green"/>
          <value status="blink1Hz" color="green"/>
          <value status="blink4Hz" color="green"/>
        </Status>
      </LED>
      <LED name="Coupler" LocationX="147" LocationY="283" Width="9" Height="8" Shape="Rect">
        <Status>
          <value status="off" color="grey"/>
          <value status="on" color="green"/>
          <value status="blink1Hz" color="green"/>
          <value status="blink4Hz" color="green"/>
        </Status>
      </LED>
      <LED name="SYNC" LocationX="147" LocationY="292" Width="9" Height="8" Shape="Rect">
        <Status>
          <value status="off" color="grey"/>
          <value status="on" color="green"/>
          <value status="blink1Hz" color="green"/>
          <value status="blink4Hz" color="green"/>
          <value status="on" color="amber"/>
          <value status="blink1Hz" color="amber"/>
          <value status="blink4Hz" color="amber"/>
        </Status>
      </LED>
    </LEDLayout>
  </Information>
  <!-- Port LED -->
  <Module>
    <Port Label="G1">
      <Type>
        <value mapname="SFP" fill_X="26" fill_Y="337" fill_Width="32" fill_Height="42"/>
      </Type>
      <!-- SFP Link LED -->
      <PortLED>
        <LED type="SFP">
          <Position fill_X="58" fill_Y="339" fill_Width="9" fill_Height="9"></Position>
        </LED>
      </PortLED>
    </Port>
    <Port Label="G2">
      <Type>
        <value mapname="SFP" fill_X="66" fill_Y="337" fill_Width="32" fill_Height="42"/>
      </Type>
      <!-- SFP Link LED -->
      <PortLED>
        <LED type="SFP">
          <Position fill_X="58" fill_Y="368" fill_Width="9" fill_Height="9"></Position>
        </LED>
      </PortLED>
    </Port>
    <Port Label="G3">
      <Type>
        <value mapname="SFP" fill_X="26" fill_Y="290" fill_Width="32" fill_Height="42"/>
      </Type>
      <!-- SFP Link LED -->
      <PortLED>
        <LED type="SFP">
          <Position fill_X="58" fill_Y="292" fill_Width="9" fill_Height="9"></Position>
        </LED>
      </PortLED>
    </Port>
    <Port Label="G4">
      <Type>
        <value mapname="SFP" fill_X="66" fill_Y="290" fill_Width="32" fill_Height="42"/>
      </Type>
      <!-- SFP Link LED -->
      <PortLED>
        <LED type="SFP">
          <Position fill_X="58" fill_Y="321" fill_Width="9" fill_Height="9"></Position>
        </LED>
      </PortLED>
    </Port>
    <Port Label="1">
      <Type>
        <value mapname="RJ45" fill_X="23" fill_Y="153" fill_Width="33" fill_Height="38"/>
      </Type>
      <!-- PoE LED -->
      <PortLED>
        <LED type="PoE">
          <Position fill_X="107" fill_Y="292" fill_Width="7" fill_Height="8"></Position>
          <Status>
            <value color="green" status="on" descKey="poebt" />
            <value color="amber" status="on" descKey="poebt" />
            <value color="amber" status="blink4Hz" />
            <value color="red" status="on" />
            <value color="red" status="blink4Hz" />
            <value status="off" />
          </Status>
        </LED>
      </PortLED>
    </Port>
    <Port Label="2">
      <Type>
        <value mapname="RJ45" fill_X="65" fill_Y="153" fill_Width="33" fill_Height="38"/>
      </Type>
      <!-- PoE LED -->
      <PortLED>
        <LED type="PoE">
          <Position fill_X="114" fill_Y="292" fill_Width="7" fill_Height="8"></Position>
          <Status>
            <value color="green" status="on" descKey="poebt" />
            <value color="amber" status="on" descKey="poebt" />
            <value color="amber" status="blink4Hz" />
            <value color="red" status="on" />
            <value color="red" status="blink4Hz" />
            <value status="off" />
          </Status>
        </LED>
      </PortLED>
    </Port>
    <Port Label="3">
      <Type>
        <value mapname="RJ45" fill_X="23" fill_Y="107" fill_Width="33" fill_Height="38"/>
      </Type>
      <!-- PoE LED -->
      <PortLED>
        <LED type="PoE">
          <Position fill_X="107" fill_Y="283" fill_Width="7" fill_Height="8"></Position>
          <Status>
            <value color="green" status="on" descKey="poebt" />
            <value color="amber" status="on" descKey="poebt" />
            <value color="amber" status="blink4Hz" />
            <value color="red" status="on" />
            <value color="red" status="blink4Hz" />
            <value status="off" />
          </Status>
        </LED>
      </PortLED>
    </Port>
    <Port Label="4">
      <Type>
        <value mapname="RJ45" fill_X="65" fill_Y="107" fill_Width="33" fill_Height="38"/>
      </Type>
      <!-- PoE LED -->
      <PortLED>
        <LED type="PoE">
          <Position fill_X="114" fill_Y="283" fill_Width="7" fill_Height="8"></Position>
          <Status>
            <value color="green" status="on" descKey="poebt" />
            <value color="amber" status="on" descKey="poebt" />
            <value color="amber" status="blink4Hz" />
            <value color="red" status="on" />
            <value color="red" status="blink4Hz" />
            <value status="off" />
          </Status>
        </LED>
      </PortLED>
    </Port>
    <Port Label="5">
      <Type>
        <value mapname="RJ45" fill_X="23" fill_Y="61" fill_Width="33" fill_Height="38"/>
      </Type>
      <!-- PoE LED -->
      <PortLED>
        <LED type="PoE">
          <Position fill_X="107" fill_Y="274" fill_Width="7" fill_Height="8"></Position>
          <Status>
            <value color="green" status="on" descKey="poebt" />
            <value color="amber" status="on" descKey="poebt" />
            <value color="amber" status="blink4Hz" />
            <value color="red" status="on" />
            <value color="red" status="blink4Hz" />
            <value status="off" />
          </Status>
        </LED>
      </PortLED>
    </Port>
    <Port Label="6">
      <Type>
        <value mapname="RJ45" fill_X="65" fill_Y="61" fill_Width="33" fill_Height="38"/>
      </Type>
      <!-- PoE LED -->
      <PortLED>
        <LED type="PoE">
          <Position fill_X="114" fill_Y="274" fill_Width="7" fill_Height="8"></Position>
          <Status>
            <value color="green" status="on" descKey="poebt" />
            <value color="amber" status="on" descKey="poebt" />
            <value color="amber" status="blink4Hz" />
            <value color="red" status="on" />
            <value color="red" status="blink4Hz" />
            <value status="off" />
          </Status>
        </LED>
      </PortLED>
    </Port>
    <Port Label="7">
      <Type>
        <value mapname="RJ45" fill_X="23" fill_Y="14" fill_Width="33" fill_Height="38"/>
      </Type>
      <!-- PoE LED -->
      <PortLED>
        <LED type="PoE">
          <Position fill_X="107" fill_Y="265" fill_Width="7" fill_Height="8"></Position>
          <Status>
            <value color="green" status="on" descKey="poebt" />
            <value color="amber" status="on" descKey="poebt" />
            <value color="amber" status="blink4Hz" />
            <value color="red" status="on" />
            <value color="red" status="blink4Hz" />
            <value status="off" />
          </Status>
        </LED>
      </PortLED>
    </Port>
    <Port Label="8">
      <Type>
        <value mapname="RJ45" fill_X="65" fill_Y="14" fill_Width="33" fill_Height="38"/>
      </Type>
      <!-- PoE LED -->
      <PortLED>
        <LED type="PoE">
          <Position fill_X="114" fill_Y="265" fill_Width="7" fill_Height="8"></Position>
          <Status>
            <value color="green" status="on" descKey="poebt" />
            <value color="amber" status="on" descKey="poebt" />
            <value color="amber" status="blink4Hz" />
            <value color="red" status="on" />
            <value color="red" status="blink4Hz" />
            <value status="off" />
          </Status>
        </LED>
      </PortLED>
    </Port>
  </Module>
</Device>
