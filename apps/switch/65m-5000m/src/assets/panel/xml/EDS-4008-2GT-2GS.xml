<?xml version="1.0" encoding="UTF-8"?>
<Device version="3.0">

  <!--
value: 0x00      = grey/black
       0x01-0x0F = green
       0x11-0x1F = amber
       0x21-0x2F = red
-->
  <Information Name="EDS-4008-2GT-2GS" Location="0, 0" image="EDS-4008-2GT-2GS.png">
    <!-- System LED -->
    <LEDLayout>
      <LED name="STATE" LocationX="148" LocationY="238" Width="9" Height="7" Shape="Rect">
        <Status>
          <value status="off" color="grey"/>
          <value status="on" color="green"/>
          <value status="blink1Hz" color="green"/>
          <value status="blink4Hz" color="green"/>
          <value status="on" color="red"/>
          <value status="blink1Hz" color="red"/>
          <value status="blink4Hz" color="red"/>
        </Status>
      </LED>
      <LED name="FAULT" LocationX="148" LocationY="247" Width="9" Height="7" Shape="Rect">
        <Status>
          <value status="off" color="grey"/>
          <value status="on" color="red"/>
          <value status="blink1Hz" color="red"/>
          <value status="blink4Hz" color="red"/>
        </Status>
      </LED>
      <LED name="PWR1" LocationX="148" LocationY="256" Width="9" Height="7" Shape="Rect">
        <Status>
          <value status="off" color="grey"/>
          <value status="on" color="amber"/>
          <value status="blink1Hz" color="amber"/>
          <value status="blink4Hz" color="amber"/>
        </Status>
      </LED>
      <LED name="PWR2" LocationX="148" LocationY="266" Width="9" Height="7" Shape="Rect">
        <Status>
          <value status="off" color="grey"/>
          <value status="on" color="amber"/>
          <value status="blink1Hz" color="amber"/>
          <value status="blink4Hz" color="amber"/>
        </Status>
      </LED>
      <LED name="Master" LocationX="148" LocationY="275" Width="9" Height="7" Shape="Rect">
        <Status>
          <value status="off" color="grey"/>
          <value status="on" color="green"/>
          <value status="blink1Hz" color="green"/>
          <value status="blink4Hz" color="green"/>
        </Status>
      </LED>
      <LED name="Coupler" LocationX="148" LocationY="284" Width="9" Height="7" Shape="Rect">
        <Status>
          <value status="off" color="grey"/>
          <value status="on" color="green"/>
          <value status="blink1Hz" color="green"/>
          <value status="blink4Hz" color="green"/>
        </Status>
      </LED>
      <LED name="SYNC" LocationX="148" LocationY="293" Width="9" Height="7" Shape="Rect">
        <Status>
          <value status="off" color="grey"/>
          <value status="on" color="green"/>
          <value status="blink1Hz" color="green"/>
          <value status="blink4Hz" color="green"/>
          <value status="on" color="amber"/>
          <value status="blink1Hz" color="amber"/>
          <value status="blink4Hz" color="amber"/>
        </Status>
      </LED>
    </LEDLayout>
  </Information>
  <!-- Port LED -->
  <Module>
    <Port Label="G1">
      <Type>
        <value mapname="SFP" fill_X="27" fill_Y="290" fill_Width="32" fill_Height="42"/>
      </Type>
      <!-- SFP Link LED -->
      <PortLED>
        <LED type="SFP">
          <Position fill_X="58" fill_Y="293" fill_Width="9" fill_Height="9"></Position>
        </LED>
      </PortLED>
    </Port>
    <Port Label="G2">
      <Type>
        <value mapname="SFP" fill_X="67" fill_Y="290" fill_Width="32" fill_Height="42"/>
      </Type>
      <!-- SFP Link LED -->
      <PortLED>
        <LED type="SFP">
          <Position fill_X="58" fill_Y="321" fill_Width="9" fill_Height="9"></Position>
        </LED>
      </PortLED>
    </Port>
    <Port Label="G3">
      <Type>
        <value mapname="RJ45" fill_X="24" fill_Y="152" fill_Width="33" fill_Height="38"/>
      </Type>
    </Port>
    <Port Label="G4">
      <Type>
        <value mapname="RJ45" fill_X="66" fill_Y="152" fill_Width="33" fill_Height="38"/>
      </Type>
    </Port>
    <Port Label="1">
      <Type>
        <value mapname="RJ45" fill_X="24" fill_Y="61" fill_Width="33" fill_Height="38"/>
      </Type>
    </Port>
    <Port Label="2">
      <Type>
        <value mapname="RJ45" fill_X="66" fill_Y="61" fill_Width="33" fill_Height="38"/>
      </Type>
    </Port>
    <Port Label="3">
      <Type>
        <value mapname="RJ45" fill_X="24" fill_Y="14" fill_Width="33" fill_Height="38"/>
      </Type>
    </Port>
    <Port Label="4">
      <Type>
        <value mapname="RJ45" fill_X="66" fill_Y="14" fill_Width="33" fill_Height="38"/>
      </Type>
    </Port>
  </Module>
</Device>
