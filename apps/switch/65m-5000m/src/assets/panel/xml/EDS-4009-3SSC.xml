<?xml version="1.0" encoding="UTF-8"?>
<Device version="3.0">

  <!--
value: 0x00      = grey/black
       0x01-0x0F = green
       0x11-0x1F = amber
       0x21-0x2F = red
-->
  <Information Name="EDS-4009-3SSC" Location="0, 0" image="EDS-4009-3SSC.png">
    <!-- System LED -->
    <LEDLayout>
      <LED name="STATE" LocationX="148" LocationY="238" Width="9" Height="8" Shape="Rect">
        <Status>
          <value status="off" color="grey"/>
          <value status="on" color="green"/>
          <value status="blink1Hz" color="green"/>
          <value status="blink4Hz" color="green"/>
          <value status="on" color="red"/>
          <value status="blink1Hz" color="red"/>
          <value status="blink4Hz" color="red"/>
        </Status>
      </LED>
      <LED name="FAULT" LocationX="148" LocationY="247" Width="9" Height="8" Shape="Rect">
        <Status>
          <value status="off" color="grey"/>
          <value status="on" color="red"/>
          <value status="blink1Hz" color="red"/>
          <value status="blink4Hz" color="red"/>
        </Status>
      </LED>
      <LED name="PWR1" LocationX="148" LocationY="256" Width="9" Height="8" Shape="Rect">
        <Status>
          <value status="off" color="grey"/>
          <value status="on" color="amber"/>
          <value status="blink1Hz" color="amber"/>
          <value status="blink4Hz" color="amber"/>
        </Status>
      </LED>
      <LED name="PWR2" LocationX="148" LocationY="265" Width="9" Height="8" Shape="Rect">
        <Status>
          <value status="off" color="grey"/>
          <value status="on" color="amber"/>
          <value status="blink1Hz" color="amber"/>
          <value status="blink4Hz" color="amber"/>
        </Status>
      </LED>
      <LED name="Master" LocationX="148" LocationY="274" Width="9" Height="8" Shape="Rect">
        <Status>
          <value status="off" color="grey"/>
          <value status="on" color="green"/>
          <value status="blink1Hz" color="green"/>
          <value status="blink4Hz" color="green"/>
        </Status>
      </LED>
      <LED name="Coupler" LocationX="148" LocationY="283" Width="9" Height="8" Shape="Rect">
        <Status>
          <value status="off" color="grey"/>
          <value status="on" color="green"/>
          <value status="blink1Hz" color="green"/>
          <value status="blink4Hz" color="green"/>
        </Status>
      </LED>
      <LED name="SYNC" LocationX="148" LocationY="292" Width="9" Height="8" Shape="Rect">
        <Status>
          <value status="off" color="grey"/>
          <value status="on" color="green"/>
          <value status="blink1Hz" color="green"/>
          <value status="blink4Hz" color="green"/>
          <value status="on" color="amber"/>
          <value status="blink1Hz" color="amber"/>
          <value status="blink4Hz" color="amber"/>
        </Status>
      </LED>
    </LEDLayout>
  </Information>
  <!-- Port LED -->
  <Module>
    <Port Label="1">
      <Type>
        <value mapname="SC" fill_X="27" fill_Y="304" fill_Width="28" fill_Height="73"/>
      </Type>
      <PortLED>
        <LED type="SC">
          <Position fill_X="18" fill_Y="336" fill_Width="8" fill_Height="8"></Position>
        </LED>
      </PortLED>
    </Port>
    <Port Label="2">
      <Type>
        <value mapname="SC" fill_X="27" fill_Y="229" fill_Width="28" fill_Height="73"/>
      </Type>
      <PortLED>
        <LED type="SC">
          <Position fill_X="18" fill_Y="262" fill_Width="8" fill_Height="8"></Position>
        </LED>
      </PortLED>
    </Port>
    <Port Label="3">
      <Type>
        <value mapname="SC" fill_X="27" fill_Y="155" fill_Width="28" fill_Height="73"/>
      </Type>
      <PortLED>
        <LED type="SC">
          <Position fill_X="18" fill_Y="188" fill_Width="8" fill_Height="8"></Position>
        </LED>
      </PortLED>
    </Port>
    <Port Label="4">
      <Type>
        <value mapname="RJ45" fill_X="25" fill_Y="106" fill_Width="33" fill_Height="38"/>
      </Type>
    </Port>
    <Port Label="5">
      <Type>
        <value mapname="RJ45" fill_X="66" fill_Y="106" fill_Width="33" fill_Height="38"/>
      </Type>
    </Port>
    <Port Label="6">
      <Type>
        <value mapname="RJ45" fill_X="25" fill_Y="61" fill_Width="33" fill_Height="38"/>
      </Type>
    </Port>
    <Port Label="7">
      <Type>
        <value mapname="RJ45" fill_X="66" fill_Y="61" fill_Width="33" fill_Height="38"/>
      </Type>
    </Port>
    <Port Label="8">
      <Type>
        <value mapname="RJ45" fill_X="25" fill_Y="14" fill_Width="33" fill_Height="38"/>
      </Type>
    </Port>
    <Port Label="9">
      <Type>
        <value mapname="RJ45" fill_X="66" fill_Y="14" fill_Width="33" fill_Height="38"/>
      </Type>
    </Port>
  </Module>
</Device>
