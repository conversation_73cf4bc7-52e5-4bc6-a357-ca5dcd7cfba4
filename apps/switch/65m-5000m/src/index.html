<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>MOXA</title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, max-age=0, must-revalidate" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta http-equiv="expires" content="0" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <base href="/" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="icon" type="image/x-icon" href="favicon.ico" />
    <style>
      body {
        width: 100%;
        height: 100%;
      }

      .preloader {
        width: 280px;
        height: 100px;
        position: absolute;
        left: 50%;
        top: 50%;
        margin-left: -140px;
        margin-top: -50px;
        text-align: center;
      }

      .preloader .logo {
        width: 100%;
      }

      /* For IE unsupport message page */
      #ie-unsupport-page {
        display: none;
      }

      .ie-unsupport-container {
        background-image: url('./assets/img/bg_login.png');
        background-color: #0e0f23;
        background-position: top center;
        background-repeat: no-repeat;
        height: 100vh;
        overflow: hidden;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
      }

      .ie-unsupport-frame {
        width: 420px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }

      .ie-unsupport-frame > .logo-frame {
        margin-bottom: 64px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }

      .ie-unsupport-frame > .logo-frame > img {
        width: 320px;
        display: flex;
        flex-direction: column;
      }

      .ie-unsupport-frame > .logo-frame > img:last-child {
        width: 150px;
        margin-top: 10px;
      }

      p.single-line-title {
        text-align: center;
        font-size: 20px;
        font-weight: bold;
        margin-top: 0;
        color: #ffcd80;
      }

      p.browser-message {
        width: 420px;
        position: static;
        height: 160px;
        text-align: center;
        font-size: 16px;
        color: #ffcd80;
      }

      p.copy-right {
        margin-top: 5px;
        width: 320px;
        color: #ffffff;
        text-align: center;
        font-size: 14px;
      }
    </style>
  </head>

  <body>
    <app-root>
      <div class="preloader" id="preloader">
        <img class="logo" src="./assets/img/MoxaLogo_Green_Version.png" />
      </div>
    </app-root>
    <div id="ie-unsupport-page">
      <div class="ie-unsupport-container">
        <div class="ie-unsupport-frame">
          <div class="logo-frame">
            <img src="./assets/img/logo_w.png" />
            <img src="./assets/img/mx-nos-h.png" />
          </div>
          <p class="single-line-title">Internet Explorer 11 is no longer supported</p>
          <p class="browser-message">
            As Internet Explorer 11 is being officially discontinued, Internet Explorer 11 is no longer supported. Using
            outdated or unsupported browsers can put your data at risk. Please switch to another supported browser such
            as Microsoft Edge or Google Chrome.
          </p>
          <p class="copy-right">Copyright © 2024 Moxa, Inc. All Rights Reserved.</p>
        </div>
      </div>
    </div>
  </body>
  <script>
    var isBrowserIe = function () {
      var ua = window.navigator.userAgent;
      var msie = ua.indexOf('MSIE ');

      if (msie > 0 || !!navigator.userAgent.match(/Trident.*rv\:11\./)) {
        // Internet Explorer
        return true;
      } else {
        return false;
      }
    };

    var preloader = document.getElementById('preloader');
    var ieUnsupportPage = document.getElementById('ie-unsupport-page');
    if (isBrowserIe()) {
      preloader.style.display = 'none';
      ieUnsupportPage.style.display = 'block';

      // remove loaded scripts (vendor.js, main.js, polyfills.js)
      var allScripts = document.getElementsByTagName('script');
      while (allScripts.length > 1) {
        if (allScripts[0].getAttribute('src')) {
          allScripts[0].parentNode.removeChild(allScripts[0]);
        } else {
          allScripts[1].parentNode.removeChild(allScripts[1]);
        }
      }
    } else {
      ieUnsupportPage.style.display = 'none';
      preloader.style.display = 'block';
    }
  </script>
</html>
