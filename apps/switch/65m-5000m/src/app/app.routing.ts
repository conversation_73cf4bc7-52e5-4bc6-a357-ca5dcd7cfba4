import { ModuleWithProviders } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { AuthGuard } from './pages/auth-guard.service';

const appRoutes: Routes = [
  { path: '', redirectTo: 'login', pathMatch: 'full' },
  {
    path: 'pages',
    loadChildren: () => import('./pages/pages.module').then(m => m.PagesModule),
    canActivate: [AuthGuard],
  },
  {
    path: 'login/:mode',
    loadChildren: () => import('@switch-web/components/login/src/lib/login.module').then(m => m.LoginModule),
  },
  {
    path: 'login',
    loadChildren: () => import('@switch-web/components/login/src/lib/login.module').then(m => m.LoginModule),
  },
  // otherwise redirect to page not found
  { path: '**', redirectTo: 'pages/404' },
];

// eslint-disable-next-line max-len
export const AppRoutingModule: ModuleWithProviders<any> = RouterModule.forRoot(appRoutes, {
  useHash: true,
  enableTracing: false,
});
