import { APP_INITIALIZER, Injector, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { LOCATION_INITIALIZED } from '@angular/common';
// Translate
import { HTTP_INTERCEPTORS, HttpClient, HttpClientModule } from '@angular/common/http';
import { FormsModule } from '@angular/forms';
import { MAT_TOOLTIP_DEFAULT_OPTIONS } from '@angular/material/tooltip';

import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';

import { AppState } from '@switch-web/shared/app/app.service';
import { PagesService } from '@switch-web/shared/pages/pages.service';
import { CombinationTableService } from '@switch-web/shared/assets/mx-service/combination-table/combination-table.service';
import { GenerateDocService } from '@switch-web/shared/assets/mx-service/generate-doc.service';
import { MxStyleService } from '@switch-web/shared/assets/mx-service/style/mx-style.service';
import { AutoSaveModeDialogModule } from '@switch-web/shared/component/auto-save-mode-dialog/auto-save-mode-dialog.module';
import { BrowseFileDialogModule } from '@switch-web/shared/component/browse-file-dialog/browse-file-dialog.module';
import { BrowseModeDialogModule } from '@switch-web/shared/component/browse-mode-dialog/browse-mode-dialog.module';
import { FactoryDefaultDialogModule } from '@switch-web/shared/component/factory-default-dialog/factory-default-dialog.module';
import { LanguageDialogModule } from '@switch-web/shared/component/language-dialog/language-dialog.module';
import { LocatorDialogModule } from '@switch-web/shared/component/locator-dialog/locator-dialog.module';
import { LogoutDialogModule } from '@switch-web/shared/component/logout-dialog/logout-dialog.module';
import { MxLoadingModule } from '@switch-web/shared/component/mx-loading/mx-loading.module';
import { NoExternalDevWarningDialogModule } from '@switch-web/shared/component/no-external-dev-warning-dialog/no-external-dev-warning-dialog.module';
import { RebootDialogModule } from '@switch-web/shared/component/reboot-dialog/reboot-dialog.module';
import { SessionExpiredDialogModule } from '@switch-web/shared/component/session-expired-dialog/session-expired-dialog.module';
import { StartupConfigDialogModule } from '@switch-web/shared/component/startup-config-dialog/startup-config-dialog.module';
import { AuthData } from '@switch-web/shared/service/auth-data.service';
import { AuthTokenInterceptor } from '@switch-web/shared/service/auth-token.interceptor';
import { AuthService } from '@switch-web/shared/service/auth.service';
import { ErrorService } from '@switch-web/shared/service/error.service';
import { LanguageService } from '@switch-web/shared/service/language.service';
import { UtilsService } from '@switch-web/shared/service/utils.service';

import { AppComponent } from './app.component';
import { AppRoutingModule } from './app.routing';
import { AuthGuard } from './pages/auth-guard.service';
import { PagesModule } from './pages/pages.module';

// AoT requires an exported function for factories
export function createTranslateLoader(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

export function appInitializerFactory(translate: TranslateService, injector: Injector) {
  return () =>
    new Promise<any>((resolve: any) => {
      const localLang = !localStorage.getItem('sw_language') ? 'en-US' : localStorage.getItem('sw_language');
      const locationInitialized = injector.get(LOCATION_INITIALIZED, Promise.resolve(null));
      locationInitialized.then(() => {
        localStorage.setItem('sw_language', localLang);
        translate.setDefaultLang(localLang);
        translate.use(localLang).subscribe(() => {
          resolve(null);
        });
      });
    });
}

@NgModule({
  declarations: [AppComponent],
  imports: [
    AppRoutingModule,
    BrowserModule,
    BrowserAnimationsModule,
    FormsModule,
    HttpClientModule,
    PagesModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: createTranslateLoader,
        deps: [HttpClient],
      },
    }),
    AutoSaveModeDialogModule,
    BrowseFileDialogModule,
    NoExternalDevWarningDialogModule,
    BrowseModeDialogModule,
    SessionExpiredDialogModule,
    RebootDialogModule,
    LogoutDialogModule,
    StartupConfigDialogModule,
    FactoryDefaultDialogModule,
    LocatorDialogModule,
    LanguageDialogModule,
    MxLoadingModule,
  ],
  providers: [
    {
      provide: APP_INITIALIZER,
      useFactory: appInitializerFactory,
      deps: [TranslateService, Injector],
      multi: true,
    },
    AppState,
    AuthService,
    AuthData,
    AuthGuard,
    ErrorService,
    UtilsService,
    PagesService,
    LanguageService,
    GenerateDocService,
    MxStyleService,
    CombinationTableService,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthTokenInterceptor,
      multi: true,
    },
    {
      provide: MAT_TOOLTIP_DEFAULT_OPTIONS,
      useValue: {
        disableTooltipInteractivity: true,
      },
    },
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
