@import 'mx-service/style/font-style.scss';

.app-toolbar-filler {
  flex: 1 1 auto;
}

mat-sidenav-layout.m2app-dark {
  background: black;
}

.app-content {
  padding: 20px;
}

.app-content mat-card {
  margin: 20px;
}

.app-sidenav {
  padding: 10px;
  min-width: 100px;
}

.app-content mat-checkbox {
  margin: 10px;
}

body {
  margin: 0 !important;
}

.app-action {
  display: inline-block;
  position: fixed;
  bottom: 20px;
  right: 20px;
}

.app-spinner {
  height: 30px;
  width: 30px;
  display: inline-block;
}

.app-list {
  border: 1px solid rgba(0, 0, 0, 0.12);
  width: 350px;
  margin: 20px;
}

.app-progress {
  margin: 20px;
}
