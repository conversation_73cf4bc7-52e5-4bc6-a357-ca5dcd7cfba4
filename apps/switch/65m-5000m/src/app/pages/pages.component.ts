import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  HostListener,
  On<PERSON><PERSON><PERSON>,
  OnInit,
} from '@angular/core';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { FlatTreeControl } from '@angular/cdk/tree';
import { MatLegacyDialog as MatDialog } from '@angular/material/legacy-dialog';
import { MatTreeFlatDataSource, MatTreeFlattener } from '@angular/material/tree';

import { cloneDeep, findIndex, findLastIndex, slice } from 'lodash-es';
import { interval, Subscription } from 'rxjs';

import * as environment from '@switch-web/shared/environments/environment';
import { AppState } from '@switch-web/shared/app/app.service';
import { GlobalEvent, GlobalEventType } from '@switch-web/shared/app/global.event';
import { allMenu } from '@switch-web/shared/pages/pages.def';
import { PagesService, TreeNode } from '@switch-web/shared/pages/pages.service';
import { AutoSaveModeDialogComponent } from '@switch-web/shared/component/auto-save-mode-dialog/auto-save-mode-dialog.component';
import { BrowseModeDialogComponent } from '@switch-web/shared/component/browse-mode-dialog/browse-mode-dialog.component';
import { FactoryDefaultDialogComponent } from '@switch-web/shared/component/factory-default-dialog/factory-default-dialog.component';
import { LanguageDialogComponent } from '@switch-web/shared/component/language-dialog/language-dialog.component';
import { LocatorDialogComponent } from '@switch-web/shared/component/locator-dialog/locator-dialog.component';
import { LogoutDialogComponent } from '@switch-web/shared/component/logout-dialog/logout-dialog.component';
import { RebootDialogComponent } from '@switch-web/shared/component/reboot-dialog/reboot-dialog.component';
import { StartupConfigDialogComponent } from '@switch-web/shared/component/startup-config-dialog/startup-config-dialog.component';
import { smallDialogConfig } from '@switch-web/shared/dialog/dialog-config.service';
import { AuthService } from '@switch-web/shared/service/auth.service';
import { ErrorService } from '@switch-web/shared/service/error.service';
import { LanguageService } from '@switch-web/shared/service/language.service';
import { UtilsService } from '@switch-web/shared/service/utils.service';

import { routes } from './pages.routing';

@Component({
  templateUrl: './pages.component.html',
  styleUrls: ['./pages.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PagesComponent implements OnInit, AfterViewInit, OnDestroy {
  currentAutoSaveMode: boolean;
  currentBrowseMode: boolean;
  authUsername;
  // save icon flag
  applyFlag = false;
  opened = true;
  navMode = 'side';
  screenWidth: number;
  modelName: string;
  autoLogout: number;
  prevMouseEventTime = 0;

  // menu tree object
  treeControl;
  treeFlattener;
  treeDataSource;

  private menuTreeRaw;
  private globalEvent: Subscription;
  /*
   * Heartbeat is used to provide the session being released when the browser is closed but the user is not logged out
   * Session timeout is based on auto-logout value
   */
  private heartbeatInterval = 30000;
  private sessionInterval = 5000;
  private heartbeatIntervalObj: Subscription;
  private sessionIntervalObj;
  @HostListener('window:mousemove', [])
  @HostListener('window:mouseup', [])
  @HostListener('window:keyup', [])
  @HostListener('window:wheel', [])
  userMove() {
    this.updateActivityTiming();
  }

  constructor(
    public auth: AuthService,
    public pages: PagesService,
    private appState: AppState,
    private dialog: MatDialog,
    private errorService: ErrorService,
    private http: HttpClient,
    private router: Router,
    private utils: UtilsService,
    private cdr: ChangeDetectorRef,
    public languageService: LanguageService
  ) {
    this.auth.authNodes = {};
    this.auth.canAccessUrls = [];
    this.menuTreeRaw = this.pages.generateMenuTreeData(allMenu, routes[0].children);

    // tree declaration
    this.treeControl = new FlatTreeControl<TreeNode>(
      node => node.level,
      node => node.expandable
    );
    this.treeFlattener = new MatTreeFlattener(
      this.pages.treeTransformer,
      node => node.level,
      node => node.expandable,
      node => node.children
    );
    this.treeDataSource = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener);
    this.treeDataSource.data = cloneDeep(this.menuTreeRaw);

    // get current save mode
    if (localStorage.getItem('mx_autoSaveMode') === 'true') {
      this.currentAutoSaveMode = true;
    } else {
      this.currentAutoSaveMode = false;
    }

    // get current browse mode
    if (localStorage.getItem('mx_advancedMode') === 'true') {
      this.currentBrowseMode = true;
    } else {
      this.currentBrowseMode = false;
    }

    // get login username
    this.authUsername = localStorage.getItem('username');
    this.applyFlag = localStorage.getItem('mx_applyFlag') === 'true';

    this.screenWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
    if (this.screenWidth < 1024) {
      this.opened = false;
    }
    window.onresize = () => {
      // set screenWidth on screen size change
      this.screenWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
      if (this.screenWidth < 1024) {
        this.opened = false;
      } else {
        this.opened = true;
      }
    };
    this.initHeartbeat();
    this.checkSessionTimeout();
    this.initInterval();
    this.updateActivityTiming();
  }

  ngOnInit(): void {
    this.subscribeGlobalEvent();
    // Get model name
    if (!this.modelName) {
      const options = this.utils.createStaticOptions();
      this.http.get(environment.apiHost + '/static/modelName', options).subscribe(modelName => {
        this.modelName = modelName;
        localStorage.setItem('modelName', modelName);
        this.cdr.markForCheck();
      });
    }
    this.http.get(environment.uriRequestURL + '/setting/data/loginPolicy/autoLogout').subscribe(
      (data: any) => {
        this.autoLogout = data;
      },
      error => {
        this.errorService.handleError(error);
      }
    );
  }

  ngAfterViewInit(): void {
    this.activateMenu();
  }

  ngOnDestroy(): void {
    if (this.globalEvent !== undefined) {
      this.globalEvent.unsubscribe();
    }
    if (this.sessionIntervalObj !== undefined) {
      clearInterval(this.sessionIntervalObj);
    }
    if (this.heartbeatIntervalObj) {
      this.heartbeatIntervalObj.unsubscribe();
    }
  }

  /* Menu */
  toggleMenu(): void {
    this.opened = !this.opened;
  }

  filterNodes(filterText: string): void {
    this.treeFilter(filterText);
    if (filterText !== '') {
      this.treeControl.expandAll();
    } else {
      this.treeControl.collapseAll();
    }
  }

  treeFilter(filterText: string): void {
    const treeData = cloneDeep(this.menuTreeRaw);
    const matchAliasName = [];
    const key = filterText.toLowerCase();

    this.pages.nodeAlias.forEach(node => {
      const isAlias = node.alias.find(alias => {
        return alias === key;
      });
      if (isAlias !== undefined) {
        matchAliasName.push(node.name);
      }
    });

    const filterMetd = node => {
      if (node.children && node.children.length !== 0) {
        /* Parent Node */
        if (node.name.toLowerCase().indexOf(key) !== -1) {
          return true;
        } else {
          return (node.children = node.children.filter(filterMetd)).length;
        }
      } else {
        /* Children Node */
        // Check match name by alias
        if (matchAliasName.length > 0) {
          for (const aliasNode of matchAliasName) {
            if (node.name === aliasNode) {
              return true;
            }
          }
        }
        // Check match name by key name
        return node.name.toLowerCase().indexOf(key) !== -1;
      }
    };
    const filteredTreeData = treeData.filter(filterMetd);
    this.treeControl = new FlatTreeControl<TreeNode>(
      node => node.level,
      node => node.expandable
    );
    this.treeFlattener = new MatTreeFlattener(
      this.pages.treeTransformer,
      node => node.level,
      node => node.expandable,
      node => node.children
    );
    this.treeDataSource = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener);
    this.treeDataSource.data = filteredTreeData;
  }

  getNodeLevel(node: TreeNode): string {
    const level = this.treeFlattener.getLevel(node);
    return 'level-' + level;
  }

  /* Profile Menu Function */
  openLanguage(): void {
    const dialogRef = this.dialog.open(LanguageDialogComponent, smallDialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result === 'confirm') {
        window.location.reload();
      }
    });
  }

  changeBrowseMode(): void {
    const dialogRef = this.dialog.open(BrowseModeDialogComponent, smallDialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        window.location.reload();
      }
    });
  }

  changeAutoSaveMode(): void {
    const dialogRef = this.dialog.open(AutoSaveModeDialogComponent, smallDialogConfig);
    dialogRef.afterClosed().subscribe(saveMode => {
      if (saveMode) {
        this.currentAutoSaveMode = saveMode === 'true';
        if (saveMode === 'true') {
          this.applyFlag = false;
          localStorage.setItem('mx_applyFlag', 'false');
        }
      }
    });
  }

  locator(): void {
    this.dialog.open(LocatorDialogComponent, smallDialogConfig);
  }

  restartMachine(): void {
    this.dialog.open(RebootDialogComponent, smallDialogConfig);
  }

  factoryDefault(): void {
    this.dialog.open(FactoryDefaultDialogComponent, smallDialogConfig);
  }

  logout(): void {
    const dialogRef = this.dialog.open(LogoutDialogComponent, smallDialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        this.router.navigateByUrl('/login/redirected');
      }
    });
  }

  /* toolbar function */
  applyToStartupConfig(): void {
    this.dialog.open(StartupConfigDialogComponent, smallDialogConfig);
  }

  private subscribeGlobalEvent(): void {
    this.globalEvent = this.appState.events.subscribe((event: GlobalEvent) => {
      if (event.type === GlobalEventType.SAVED && this.applyFlag) {
        this.applyFlag = false;
        localStorage.setItem('mx_applyFlag', 'false');
      } else if (event.type === GlobalEventType.UNSAVED && !this.applyFlag) {
        this.applyFlag = true;
        localStorage.setItem('mx_applyFlag', 'true');
      } else if (event.type === GlobalEventType.AUTO_LOGOUT_TIME) {
        this.autoLogout = event.value;
      }
    });
  }

  private activateMenu(): void {
    const currentUrl = this.router.url;
    const nodeIndex = findIndex(this.treeControl.dataNodes, (node: TreeNode) => node.href === currentUrl);
    if (nodeIndex === -1) {
      return;
    }
    const expandNode = (lastNodeIndex: number): void => {
      const nodeLevel = this.treeControl.dataNodes[lastNodeIndex].level;
      if (nodeLevel === 0) {
        return;
      }
      const nodeArray = slice(this.treeControl.dataNodes, 0, lastNodeIndex);
      const expandNodeIndex = findLastIndex(nodeArray, (node: TreeNode) => node.level === nodeLevel - 1);
      this.treeControl.expand(this.treeControl.dataNodes[expandNodeIndex]);
      if (this.treeControl.dataNodes[expandNodeIndex].level !== 0) {
        expandNode(expandNodeIndex);
      }
    };
    expandNode(nodeIndex);
  }

  /* session timeout and heartbeat */
  private updateActivityTiming(): void {
    this.auth.lastActivityTiming = Date.now();
  }

  private initHeartbeat(): void {
    this.heartbeatIntervalObj = interval(this.heartbeatInterval).subscribe(() => {
      this.sendHeartbeat();
    });
  }

  private sendHeartbeat(): void {
    this.http.post(environment.uriRequestURL + '/auth/heartbeat', '').subscribe(
      () => {},
      error => {
        this.errorService.handleError(error);
      }
    );
  }

  private initInterval(): void {
    this.sessionIntervalObj = setInterval(() => {
      this.checkSessionTimeout();
    }, this.sessionInterval);
  }

  private checkSessionTimeout(): void {
    if (this.autoLogout && this.autoLogout > 0) {
      const now = Date.now();
      const timeleft = this.auth.lastActivityTiming + this.autoLogout * 60 * 1000;
      const diff = timeleft - now;
      const isTimeout = diff < 0;
      if (isTimeout) {
        this.auth.openSessionExpiredDialog(true);
      }
    }
  }
}
