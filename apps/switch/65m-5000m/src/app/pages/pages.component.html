<mat-toolbar color="primary">
  <div class="logo" [routerLink]="['/pages/dashboard']">
    <img src="/../assets/img/logo_w.png" alt="Moxa Logo" height="30px" />
  </div>
  <div class="model-name mat-subheading-1" ngClass.xs="model-name-xs">{{ modelName }}</div>
  <div class="user-profile mat-subheading-2" ngClass.xs="user-profile-xs" fxFlex="625px" fxFlex.xs="180px">
    <button
      class="start-up-save-tip"
      *ngIf="applyFlag && !currentAutoSaveMode"
      (click)="applyToStartupConfig()"
      mat-icon-button
    >
      <mat-icon
        >save
        <mat-icon class="icon-heading">info</mat-icon>
      </mat-icon>
      <span class="start-up-save-tip-content">{{ 'general.top_nav.config_change.start_up_save_tip' | translate }}</span>
    </button>
    <span class="user-greeting" fxHide.lt-md="true">{{
      'general.top_nav.user_profile.greeting' | translate : { username: authUsername }
    }}</span>
    <span
      class="browse-mode"
      *ngIf="currentBrowseMode"
      matTooltip="{{ 'general.top_nav.user_profile.advanced_mode_tooltip' | translate }}"
    >
      {{ 'general.top_nav.user_profile.advanced_mode' | translate }}
    </span>
    <span
      class="browse-mode"
      *ngIf="!currentBrowseMode"
      matTooltip="{{ 'general.top_nav.user_profile.standard_mode_tooltip' | translate }}"
    >
      {{ 'general.top_nav.user_profile.standard_mode' | translate }}
    </span>
    <button id="button-profile-menu" [matMenuTriggerFor]="profilemenu" mat-icon-button>
      <mat-icon>more_vert</mat-icon>
    </button>
    <mat-menu #profilemenu="matMenu" [overlapTrigger]="false">
      <div id="frame-profile-menu">
        <button id="button-menu-language" (click)="openLanguage()" mat-menu-item>
          <mat-icon>public</mat-icon>
          <span>{{ 'general.top_nav.user_profile.change_language' | translate }}</span>
        </button>
        <button id="button-change-mode" (click)="changeBrowseMode()" mat-menu-item>
          <mat-icon>school</mat-icon>
          <span>{{ 'general.top_nav.user_profile.change_mode' | translate }}</span>
        </button>
        <button
          *ngIf="currentAutoSaveMode"
          [disabled]="auth.role === 'user'"
          (click)="changeAutoSaveMode()"
          mat-menu-item
        >
          <mat-icon>save</mat-icon>
          <span matTooltip="{{ 'general.top_nav.user_profile.disable_auto_save_hint' | translate }}">
            {{ 'general.top_nav.user_profile.disable_auto_save' | translate }}</span
          >
        </button>
        <button
          *ngIf="!currentAutoSaveMode"
          [disabled]="auth.role === 'user'"
          (click)="changeAutoSaveMode()"
          mat-menu-item
        >
          <mat-icon>save</mat-icon>
          <span>{{ 'general.top_nav.user_profile.enable_auto_save' | translate }}</span>
        </button>
        <button (click)="locator()" mat-menu-item>
          <mat-icon>my_location</mat-icon>
          <span>{{ 'general.top_nav.user_profile.locator' | translate }}</span>
        </button>
        <button *ngIf="auth.role !== 'user'" (click)="restartMachine()" mat-menu-item>
          <mat-icon>settings_power</mat-icon>
          <span>{{ 'general.top_nav.restart_machine.confirmation_title' | translate }}</span>
        </button>
        <button *ngIf="auth.role === 'admin'" (click)="factoryDefault()" mat-menu-item>
          <mat-icon>settings_backup_restore</mat-icon>
          <span>{{ 'general.top_nav.user_profile.reset_factory_default' | translate }}</span>
        </button>
        <button id="logout" (click)="logout()" mat-menu-item>
          <mat-icon>logout</mat-icon>
          <span>{{ 'general.top_nav.logout.confirmation_title' | translate }}</span>
        </button>
      </div>
    </mat-menu>
  </div>
</mat-toolbar>
<mat-icon class="app-icon-button" (click)="toggleMenu()">menu</mat-icon>
<mat-sidenav-container>
  <mat-sidenav class="app-sidenav" [opened]="opened" [mode]="screenWidth < 1024 ? 'over' : 'side'" name="sidenav">
    <div class="jump-block">
      <mat-form-field class="form-field-search search">
        <mat-icon class="icon-search" matPrefix>search</mat-icon>
        <input
          (keyup)="filterNodes($event.target.value)"
          matInput
          placeholder="{{ 'general.menu_tree.jump_page_placeholder' | translate }}"
        />
      </mat-form-field>
    </div>
    <div class="menu-block">
      <mat-tree class="menu-tree" [dataSource]="treeDataSource" [treeControl]="treeControl">
        <!-- This is the tree node template for leaf nodes -->
        <mat-tree-node
          class="menu-node"
          #nodeRouterLink="routerLinkActive"
          *matTreeNodeDef="let node"
          [matTreeNodePaddingIndent]="20"
          [ngClass]="getNodeLevel(node)"
          matTreeNodePadding
          routerLink="{{ node.href }}"
          routerLinkActive="node-active"
        >
          <div fxLayoutAlign="start center">
            <div class="menu-line"></div>
            <img class="menu-icon" *ngIf="node.icon && !nodeRouterLink.isActive" src="{{ node.icon }}" />
            <img class="menu-icon" *ngIf="node.iconActivate && nodeRouterLink.isActive" src="{{ node.iconActivate }}" />
            {{ node.name }}
          </div>
        </mat-tree-node>
        <!-- This is the tree node template for expandable nodes -->
        <mat-tree-node
          class="menu-node"
          *matTreeNodeDef="let node; when: pages.hasChild"
          [matTreeNodePaddingIndent]="20"
          [ngClass]="getNodeLevel(node)"
          matTreeNodePadding
          matTreeNodeToggle
        >
          <div fxLayoutAlign="start center">
            <div class="menu-line"></div>
            <img class="menu-icon" *ngIf="node.icon" src="{{ node.icon }}" />
            {{ node.name }}
          </div>
          <mat-icon class="mat-icon-rtl-mirror menu-icon">
            {{ treeControl.isExpanded(node) ? 'expand_less' : 'expand_more' }}
          </mat-icon>
        </mat-tree-node>
      </mat-tree>
    </div>
  </mat-sidenav>
  <div class="bg-content-header"></div>
  <div class="app-content" [ngClass]="{ 'menu-actived': opened === true }" fxLayout="row">
    <ng-container *ngIf="modelName"><router-outlet></router-outlet></ng-container>
  </div>
</mat-sidenav-container>
