@import 'mx-service/style/color.scss';
@import 'mx-service/style/font-style.scss';
@import 'mx-service/style/shared.scss';

.search {
  width: 200px;
  color: $text-white;
  bottom: -6px;
}

.nohref {
  color: $text-white;
}

.app-icon-button {
  position: absolute;
  top: 20px;
  left: 10px;
  cursor: pointer;
  color: $text-white;
}

.menu-block {
  height: calc(100vh - 145px);
  clear: both;
  color: $sidemenu-text-color !important;
  overflow-x: hidden;
  overflow-y: auto;
}

.bg-content-header {
  height: 140px;
}

.app-content {
  background-color: transparent;
  margin: 0; // padding: 20px;
  height: calc(100vh - 204px);
  min-height: calc(100vh - 204px);
  position: relative;
  top: -140px;
}

.sidenav-container {
  height: calc(100vh - 64px);

  .sidenav {
    background-color: $sidemenu-left-bg-color;
    color: $text-white;
    width: 260px;
    box-sizing: border-box;
  }
}

// menu-tree
.mat-tree {
  background: $sidemenu-left-bg-color;

  .menu-node {
    position: relative;
    box-sizing: border-box;
    width: 250px;
    min-height: 40px;
    justify-content: space-between;
    align-items: center;
    // border-left is equals to width of class 'menu-line'
    border-left: 6px solid transparent;
    @extend .text-content;
    color: $text-white;

    .menu-icon {
      padding: 8px;
    }

    .menu-line {
      position: absolute;
      left: -6px;
      width: 6px;
      height: 40px;
    }

    &.level-1 {
      background-color: #535353;

      .menu-line {
        background-color: #535353;
      }
    }

    &.level-2 {
      background-color: #5c5c5c;

      .menu-line {
        background-color: #5c5c5c;
      }
    }

    &.level-3 {
      background-color: #646464;

      .menu-line {
        background-color: #646464;
      }
    }

    &:hover {
      background-color: #777777 !important;

      .menu-line {
        background-color: #777777 !important;
      }
    }

    &.node-active {
      background-color: $text-white !important;
      color: $moxa-green;
      font-weight: 600;

      &:hover {
        background-color: $text-white !important;
      }

      .menu-line {
        background-color: $moxa-green !important;
      }
    }
  }
}

:host ::ng-deep {
  .mat-toolbar {
    background-color: $top-nav-bg-color !important;
    color: $text-white !important;

    .logo {
      position: relative;
      top: 3px;
      left: 30px;
      cursor: pointer;

      img {
        width: 140px;
        height: 20px;
      }
    }

    .model-name {
      position: relative;
      top: 13px;
      left: 36px;
    }

    .model-name-xs {
      position: absolute;
      left: 46px;
      top: 32px;
      font-size: 11px;
      padding-top: 4px;
      padding-left: 30px;
    }

    .mat-subheading-1 {
      font: 400 18px/24px Roboto, 'Helvetica Neue', sans-serif;
      margin: 0 0 16px;
    }

    .mat-icon {
      color: $text-white !important;
    }

    .user-greeting {
      color: $text-white !important;
      line-height: 40px;
      display: inline-block;
      border-right: 1px solid $text-white;
      padding-right: 20px;
      margin-left: 10px;
    }

    .browse-mode {
      margin-left: 10px;
      line-height: 40px;
    }

    .user-profile {
      position: absolute;
      top: 10px;
      right: 10px;
      padding-left: 10px;
      color: #fff;
      min-width: 180px !important;
      text-align: right;
    }

    .user-profile-xs {
      position: absolute;
      top: 10px;
      right: 25px;
      padding-left: 10px;
      color: #fff;
      min-width: 30px !important;
    }

    .icon-heading {
      width: 16px;
      height: 16px;
      font-size: 16px;
      position: absolute;
      top: -2px;
      left: 24px;
      color: #f5a623 !important;
    }

    .start-up-save-tip-content {
      display: inline-block;
      width: 210px;
      border-radius: 3px;
      background-color: rgba(0, 0, 0, 0.7);
      position: absolute;
      top: 44px;
      left: -180px;
      white-space: initial;
      z-index: 10;
      text-align: left;
      padding: 10px;
      line-height: 20px;
      @include text-wrap;
    }
  }

  // SideNav
  .app-sidenav {
    box-sizing: border-box;
    height: calc(100vh - 44px);
    min-height: calc(100vh - 44px);
    background-color: $sidemenu-left-bg-color;
    box-shadow: 2px 2px 2px #ddd;
    min-width: 250px;
    padding: 0px !important;

    .node-wrapper {
      background-color: $sidemenu-left-bg-color !important;
    }

    .jump-block {
      background-color: #727171;
      padding-left: 25px;
      padding-top: 5px;
      color: #fff;

      .mat-input-element {
        color: #fff !important;
      }

      .mat-form-field {
        color: #fff !important;
      }

      .mat-form-field-label {
        color: #fff !important;
      }

      .mat-form-field-underline {
        background-color: #fff !important;
      }
    }
  }
}
