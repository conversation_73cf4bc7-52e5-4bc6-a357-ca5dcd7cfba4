import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot } from '@angular/router';

import { findIndex } from 'lodash-es';
import { Observable } from 'rxjs';

import { allMenu } from '@switch-web/shared/pages/pages.def';
import { PagesService } from '@switch-web/shared/pages/pages.service';
import { AuthService } from '@switch-web/shared/service/auth.service';

import { routes } from './pages.routing';

@Injectable()
export class AuthGuard {
  constructor(private auth: AuthService, private router: Router, private pages: PagesService) {}

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> | boolean {
    if (this.auth.mxToken) {
      if (this.auth.canAccessUrls.length !== 0) {
        if (this.isAccessUrl(state.url, this.auth.canAccessUrls)) {
          this.auth.curruntPageUrl = state.url;
          return true;
        } else {
          return false;
        }
      } else {
        this.auth.authNodes = {};
        this.auth.canAccessUrls = [];
        this.pages.generateMenuTreeData(allMenu, routes[0].children);
        return this.isAccessUrl(state.url, this.auth.canAccessUrls);
      }
    } else {
      // not logged in so redirect to login page
      this.router.navigate(['/login']);
      return false;
    }
  }

  isAccessUrl(url, accessUrls): boolean {
    const index = findIndex(accessUrls, (accessUrl: any) => {
      return accessUrl === url;
    });
    return index === -1 ? false : true;
  }
}
