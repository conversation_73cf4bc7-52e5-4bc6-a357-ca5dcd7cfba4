import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  HostListener,
  On<PERSON><PERSON>roy,
  ViewChild,
} from '@angular/core';
import { NavigationCancel, NavigationEnd, NavigationError, NavigationStart, Router } from '@angular/router';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatLegacyDialog as MatDialog } from '@angular/material/legacy-dialog';
import { MatLegacySnackBar as MatSnackBar } from '@angular/material/legacy-snack-bar';

import { tap } from 'rxjs';

import { AppState } from '@switch-web/shared/app/app.service';
import { GlobalEvent, GlobalEventType } from '@switch-web/shared/app/global.event';
import { PagesService } from '@switch-web/shared/pages/pages.service';
import { MxStyleService } from '@switch-web/shared/assets/mx-service/style/mx-style.service';
import { MxLoadingComponent } from '@switch-web/shared/component/mx-loading/mx-loading.component';
import { AuthData } from '@switch-web/shared/service/auth-data.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AppComponent implements OnDestroy {
  @ViewChild('loading') loading: MxLoadingComponent;
  private globalEvent;
  private loadingState = false;
  private loadingTimeout;
  private fakeProgress;

  @HostListener('window:keyup.esc') onKeyUp() {
    this.dialog.closeAll();
  }

  @HostListener('window:beforeunload', ['$event']) unloadHandler(event: Event) {
    sessionStorage.setItem('mx_token', this.authData.mxToken);
    sessionStorage.setItem('mx_lastActivityTiming', String(this.authData.lastActivityTiming));
    sessionStorage.setItem('mx_readLoginRecords', String(this.authData.readLoginRecords));
    sessionStorage.setItem('mx_loginRecords', JSON.stringify(this.authData.loginRecords));
    sessionStorage.setItem('mx_curruntPageUrl', this.authData.curruntPageUrl);
    sessionStorage.setItem('mx_notSupportPoE', JSON.stringify(this.pages.notSupportPoE));
  }

  constructor(
    private appState: AppState,
    private router: Router,
    private cdr: ChangeDetectorRef,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private authData: AuthData,
    private pages: PagesService,
    private mxStyleService: MxStyleService
  ) {
    this.router.events
      .pipe(
        tap(event => {
          this.navigationInterceptor(event);
        }),
        takeUntilDestroyed()
      )
      .subscribe();

    // Subscribe loading service
    this.globalEvent = this.appState.events.subscribe((event: GlobalEvent) => {
      this.loadingController(event);
    });
  }

  loadingController(event: GlobalEvent): void {
    if (event.type === GlobalEventType.LOADING) {
      if (event.value.show) {
        if (this.loadingState) {
          return;
        }
        this.loading.start();
        if (event.value.title) {
          this.loading.title = event.value.title;
          if (event.value.fakeProgress) {
            this.setFakeProgressInterval();
          }
        } else {
          this.loading.title = '';
        }
        this.loadingState = true;
      } else {
        clearTimeout(this.loadingTimeout);
        clearInterval(this.fakeProgress);
        this.loading.complete();
        this.loadingState = false;
        this.loading.title = '';
      }
    }
  }

  setFakeProgressInterval(): void {
    const targetNum = Math.floor(Math.random() * 10) + 90;
    let num = 0;

    this.fakeProgress = setInterval(() => {
      const sec = Math.floor(Math.random() * 15);
      num += sec;
      if (num >= targetNum) {
        num = targetNum;
        this.loading.title = num + '%';
        clearInterval(this.fakeProgress);
      } else {
        this.loading.title = num + '%';
      }
      this.cdr.markForCheck();
    }, 100);
  }

  // Shows and hides the loading spinner during RouterEvent changes
  navigationInterceptor(event) {
    if (event instanceof NavigationStart) {
      if (!this.router.navigated) {
        // refrsh browser
        if (sessionStorage.getItem('mx_token') !== null && sessionStorage.getItem('mx_token') !== 'null') {
          this.authData.mxToken = sessionStorage.getItem('mx_token');
          this.authData.lastActivityTiming = parseInt(sessionStorage.getItem('mx_lastActivityTiming'), 10);
          this.authData.readLoginRecords = sessionStorage.getItem('mx_readLoginRecords') === 'true';
          // For getting data after firmware upgrade force refresh
          this.authData.loginRecords = JSON.parse(sessionStorage.getItem('mx_loginRecords'));
          this.authData.curruntPageUrl = sessionStorage.getItem('mx_curruntPageUrl');
          this.pages.notSupportPoE = JSON.parse(sessionStorage.getItem('mx_notSupportPoE'));
          sessionStorage.removeItem('mx_token');
          sessionStorage.removeItem('mx_lastActivityTiming');
          sessionStorage.removeItem('mx_readLoginRecords');
          sessionStorage.removeItem('mx_loginRecords');
          sessionStorage.removeItem('mx_curruntPageUrl');
          sessionStorage.removeItem('mx_notSupportPoE');
        }
      }

      this.snackBar.dismiss();
      this.dialog.closeAll();
      if (event.url !== '/' && event.url !== '/pages/dashboard') {
        this.loadingController({ type: GlobalEventType.LOADING, value: { show: true } });
      }
    }
    if (event instanceof NavigationEnd) {
      // Do not set the loading completed here, each page component should control loading when the URI request is completed
      // this.loadingController({ type: GlobalEventType.LOADING, value: { show: false } });
    }

    // Set loading state to false in both of the below events to hide the spinner in case a request fails
    if (event instanceof NavigationCancel) {
      this.loadingController({ type: GlobalEventType.LOADING, value: { show: false } });
    }
    if (event instanceof NavigationError) {
      this.loadingController({ type: GlobalEventType.LOADING, value: { show: false } });
    }
  }

  ngOnDestroy(): void {
    if (this.globalEvent !== undefined) {
      this.globalEvent.unsubscribe();
    }
  }
}
