{"name": "65m-5000m", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/switch/65m-5000m/src", "prefix": "app", "targets": {"build": {"executor": "@angular-devkit/build-angular:browser", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/switch/65m-5000m", "index": "apps/switch/65m-5000m/src/index.html", "main": "apps/switch/65m-5000m/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/switch/65m-5000m/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["apps/switch/65m-5000m/src/assets/panel", {"glob": "favicon.ico", "input": "libs/switch/", "output": "/"}, {"glob": "**/*", "input": "libs/switch/shared/src/lib/assets/i18n", "output": "./assets/i18n"}, {"glob": "**/*", "input": "libs/switch/shared/src/lib/assets/img", "output": "./assets/img"}, {"glob": "**/*", "input": "libs/switch/shared/src/lib/assets/fonts/roboto", "output": "./assets/fonts/roboto"}, {"glob": "**/*", "input": "libs/switch/shared/src/lib/assets/mx-service/style/img", "output": "./assets/mx-service/style/img"}], "styles": ["libs/switch/styles.scss", "libs/switch/shared/src/lib/assets/scss/material-table.scss", "libs/switch/shared/src/lib/assets/fonts/roboto/roboto.css", "libs/switch/shared/src/lib/assets/mx-service/style/default-theme.scss", "libs/switch/shared/src/lib/assets/mx-service/style/material-override.scss"], "stylePreprocessorOptions": {"includePaths": ["libs/switch/shared/src/lib/assets"]}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "7kb"}], "fileReplacements": [{"replace": "libs/switch/shared/src/lib/environments/environment.ts", "with": "libs/switch/shared/src/lib/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@angular-devkit/build-angular:dev-server", "options": {"proxyConfig": "apps/switch/65m-5000m/proxy.config.js"}, "configurations": {"production": {"browserTarget": "65m-5000m:build:production"}, "development": {"browserTarget": "65m-5000m:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "65m-5000m:build"}}, "lint": {"executor": "@nx/linter:eslint", "options": {"lintFilePatterns": ["apps/switch/65m-5000m/**/*.ts", "apps/switch/65m-5000m/**/*.html"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/apps/switch/65m-5000m"], "options": {"jestConfig": "apps/switch/65m-5000m/jest.config.ts", "passWithNoTests": true}}, "semantic-release": {"executor": "nx:run-commands", "options": {"command": "pnpm semantic-release"}}}, "tags": ["switch"]}