import { ModuleWithProviders } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { PageClass } from '@switch-web/shared/pages/pages.def';

import { PagesComponent } from './pages.component';

const Page = new PageClass();

export const routes: Routes = [
  {
    path: 'pages',
    component: PagesComponent,
    children: [
      { path: '', redirectTo: 'pages', pathMatch: 'full' },
      {
        path: '404',
        loadChildren: () =>
          import('@switch-web/components/page-not-found/src/lib/page-not-found.module').then(m => m.PageNotFoundModule),
      },
      {
        path: Page.Dashboard.path,
        loadChildren: () =>
          import('@switch-web/components/dashboard/src/lib/dashboard.module').then(m => m.DashboardModule),
        data: {
          notSupport: ['oob', 'intelTemp'],
          systemInfo: [
            'productModel',
            'productRevision',
            'deviceName',
            'serialNumber',
            'location',
            'firmwareVersion',
            'l3IpAddress',
            'systemUptime',
            'macAddress',
            'externalStorage',
            'redundantProtocol',
          ],
        },
      },
      {
        path: Page.InfoSetting.path,
        loadChildren: () =>
          import('@switch-web/components/system-information/src/lib/system-information.module').then(
            m => m.SystemInformationModule
          ),
      },
      {
        path: Page.FirmwareUpgrade.path,
        loadChildren: () =>
          import('@switch-web/components/firmware-upgrade/src/lib/firmware-upgrade.module').then(
            m => m.FirmwareUpgradeModule
          ),
        data: {
          notSupport: ['microSD'],
        },
      },
      {
        path: Page.ConfigBkRes.path,
        loadChildren: () =>
          import('@switch-web/components/config-bk-res/src/lib/config-bk-res.module').then(m => m.ConfigBkResModule),
        data: {
          notSupport: ['microSD', 'configName', 'optionalEncryptPassword'],
        },
      },
      {
        path: Page.UserAccount.path,
        loadChildren: () => import('@switch-web/components/account/src/lib/account.module').then(m => m.AccountModule),
        data: {
          tableSize: {
            userAccount: {
              max: 32,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.OnlineAccount.path,
        loadChildren: () =>
          import('@switch-web/components/online-accounts/src/lib/online-accounts.module').then(
            m => m.OnlineAccountsModule
          ),
      },
      {
        path: Page.PasswordPolicy.path,
        loadChildren: () =>
          import('@switch-web/components/password-policy/src/lib/password-policy.module').then(
            m => m.PasswordPolicyModule
          ),
      },
      {
        path: Page.UserInterface.path,
        loadChildren: () =>
          import('@switch-web/components/user-interface/src/lib/user-interface.module').then(
            m => m.UserInterfaceModule
          ),
      },
      {
        path: Page.HardwareInterface.path,
        loadChildren: () =>
          import('@switch-web/components/hardware-interface/src/lib/hardware-interface.module').then(
            m => m.HardwareInterfaceModule
          ),
        data: {
          notSupport: ['dip', 'microSD', 'oob', 'lcm'],
        },
      },
      {
        path: Page.Snmp.path,
        loadChildren: () => import('@switch-web/components/snmp/src/lib/snmp.module').then(m => m.SnmpModule),
        data: {
          tableSize: {
            userAccountTable: {
              max: 5,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.SystemTime.path,
        loadChildren: () => import('@switch-web/components/time/src/lib/time.module').then(m => m.TimeModule),
        data: {
          notSupport: ['queryInterval'],
          tableSize: {
            authenticationKey: {
              max: 10,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.NtpSever.path,
        loadChildren: () =>
          import('@switch-web/components/ntp-server/src/lib/ntp-server.module').then(m => m.NtpServerModule),
      },
      {
        path: Page.TimeSync.path,
        loadChildren: () =>
          import('@switch-web/components/time-sync/src/lib/time-sync.module').then(m => m.TimeSyncModule),
        data: {
          notSupport: ['mdsModule', 'profile8021as', 'profile8021asRev', 'neighborPropDelay', 'syncLocked'],
        },
      },
      {
        path: Page.PortSetting.path,
        loadChildren: () =>
          import('@switch-web/components/port-setting/src/lib/port-setting.module').then(m => m.PortSettingModule),
      },
      {
        path: Page.LinkupDelay.path,
        loadChildren: () =>
          import('@switch-web/components/linkup-delay/src/lib/linkup-delay.module').then(m => m.LinkupDelayModule),
      },
      {
        path: Page.LinkAggregation.path,
        loadChildren: () => import('@switch-web/components/la/src/lib/la.module').then(m => m.LaModule),
      },
      {
        path: Page.Poe.path,
        loadChildren: () => import('@switch-web/components/poe/src/lib/poe.module').then(m => m.PoeModule),
        data: {
          notSupport: ['poeBt'],
          tableSize: {
            schedulingRuleTable: {
              max: 20,
              min: 0,
            },
          },
          parameter: {
            systemPowerBudget: {
              max: 720,
              min: 30,
            },
            portPowerAllocation: {
              max: 36,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.Vlan.path,
        loadChildren: () => import('@switch-web/components/vlan/src/lib/vlan.module').then(m => m.VlanModule),
        data: {
          notSupport: ['mgmtVlan', 'vlanUnaware'],
          tableSize: {
            vlanTable: {
              max: 256,
              min: 1,
            },
          },
        },
      },
      {
        path: Page.Garp.path,
        loadChildren: () => import('@switch-web/components/garp/src/lib/garp.module').then(m => m.GarpModule),
      },
      {
        path: Page.StaticUnicast.path,
        loadChildren: () =>
          import('@switch-web/components/unicast-table/src/lib/unicast-table.module').then(m => m.UnicastTableModule),
        data: {
          tableSize: {
            unicastTable: {
              max: 256,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.MacAddressTable.path,
        loadChildren: () =>
          import('@switch-web/components/mac-address-table/src/lib/mac-address-table.module').then(
            m => m.MacAddressTableModule
          ),
        data: {
          tableSize: {
            macAddressTable: {
              max: 16384,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.Classification.path,
        loadChildren: () =>
          import('@switch-web/components/classification/src/lib/classification.module').then(
            m => m.ClassificationModule
          ),
      },
      {
        path: Page.RateLimit.path,
        loadChildren: () =>
          import('@switch-web/components/rate-limit/src/lib/rate-limit.module').then(m => m.RateLimitModule),
      },
      {
        path: Page.Scheduler.path,
        loadChildren: () =>
          import('@switch-web/components/scheduler/src/lib/scheduler.module').then(m => m.SchedulerModule),
      },
      {
        path: Page.EgressShaper.path,
        loadChildren: () =>
          import('@switch-web/components/egress-shaper/src/lib/egress-shaper.module').then(m => m.EgressShaperModule),
      },
      {
        path: Page.Igmp.path,
        loadChildren: () => import('@switch-web/components/igmp/src/lib/igmp.module').then(m => m.IgmpModule),
        data: {
          tableSize: {
            vlanTable: {
              max: 256,
              min: 0,
            },
            vlanGroupMemberTable: {
              max: 1024,
              min: 0,
            },
            vlanMulticastForwardingTable: {
              max: 1024,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.Gmrp.path,
        loadChildren: () => import('@switch-web/components/gmrp/src/lib/gmrp.module').then(m => m.GmrpModule),
      },
      {
        path: Page.StaticMulticast.path,
        loadChildren: () =>
          import('@switch-web/components/multicast-table/src/lib/multicast-table.module').then(
            m => m.MulticastTableModule
          ),
        data: {
          tableSize: {
            multicastTable: {
              max: 512,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.L3Interface.path,
        loadChildren: () =>
          import('@switch-web/components/l3-interface/src/lib/l3-interface.module').then(m => m.L3InterfaceModule),
        data: {
          tableSize: {
            vlanIfTable: {
              max: 256,
              min: 0,
            },
            loopbackIfTable: {
              max: 4,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.SpanningTree.path,
        loadChildren: () => import('@switch-web/components/rstp/src/lib/rstp.module').then(m => m.RstpModule),
        data: {
          notSupport: ['globalEnable'],
          tableSize: {
            mstInstTable: {
              max: 16,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.TurboRingV2.path,
        loadChildren: () =>
          import('@switch-web/components/turbo-ring-v2/src/lib/turbo-ring-v2.module').then(m => m.TurboRingV2Module),
        data: {
          notSupport: ['dip', 'drc', 'couplingLa'],
        },
      },
      {
        path: Page.TurboChain.path,
        loadChildren: () =>
          import('@switch-web/components/turbo-chain/src/lib/turbo-chain.module').then(m => m.TurboChainModule),
      },
      {
        path: Page.Mrp.path,
        loadChildren: () => import('@switch-web/components/mrp/src/lib/mrp.module').then(m => m.MrpModule),
      },
      {
        path: Page.MultiDualHoming.path,
        loadChildren: () =>
          import('@switch-web/components/multi-dualhoming/src/lib/multi-dualhoming.module').then(
            m => m.MultiDualhomingModule
          ),
      },
      {
        path: Page.MultiCoupling.path,
        loadChildren: () =>
          import('@switch-web/components/multi-coupling/src/lib/multi-coupling.module').then(
            m => m.MultiCouplingModule
          ),
        data: {
          notSupport: ['prpHsr'],
        },
      },
      {
        path: Page.Vrrp.path,
        loadChildren: () => import('@switch-web/components/vrrp/src/lib/vrrp.module').then(m => m.VrrpModule),
        data: {
          tableSize: {
            vrrpTable: {
              max: 40,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.Tracking.path,
        loadChildren: () =>
          import('@switch-web/components/tracking/src/lib/tracking.module').then(m => m.TrackingModule),
        data: {
          tableSize: {
            trackingTable: {
              max: 16,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.DhcpRelay.path,
        loadChildren: () =>
          import('@switch-web/components/dhcp-relay/src/lib/dhcp-relay.module').then(m => m.DhcpRelayModule),
      },
      {
        path: Page.DnsSetting.path,
        loadChildren: () =>
          import('@switch-web/components/dns-setting/src/lib/dns-setting.module').then(m => m.DnsSettingModule),
      },
      {
        path: Page.StaticRoute.path,
        loadChildren: () =>
          import('@switch-web/components/static-route/src/lib/static-route.module').then(m => m.StaticRouteModule),
        data: {
          tableSize: {
            staticRouteTable: {
              max: 128,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.OspfSetting.path,
        loadChildren: () =>
          import('@switch-web/components/ospf-setting/src/lib/ospf-setting.module').then(m => m.OspfSettingModule),
        data: {
          tableSize: {
            areaTable: {
              max: 64,
              min: 0,
            },
            nbrTable: {
              max: 64,
              min: 0,
            },
            areaAggregateTable: {
              max: 192,
              min: 0,
            },
            virtIfTable: {
              max: 128,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.OspfStatus.path,
        loadChildren: () =>
          import('@switch-web/components/ospf-status/src/lib/ospf-status.module').then(m => m.OspfStatusModule),
      },
      {
        path: Page.RoutingTable.path,
        loadChildren: () =>
          import('@switch-web/components/routing-table/src/lib/routing-table.module').then(m => m.RoutingTableModule),
        data: {
          tableSize: {
            routingTable: {
              max: 3000,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.PimDm.path,
        loadChildren: () => import('@switch-web/components/pim-dm/src/lib/pim-dm.module').then(m => m.PimDmModule),
        data: {
          tableSize: {
            pimDmTable: {
              max: 256,
              min: 0,
            },
            neighborTable: {
              max: 120,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.PimSmSetting.path,
        loadChildren: () =>
          import('@switch-web/components/pim-sm-setting/src/lib/pim-sm-setting.module').then(m => m.PimSmSettingModule),
        data: {
          tableSize: {
            pimSmTable: {
              max: 256,
              min: 0,
            },
            staticRpTable: {
              max: 16,
              min: 0,
            },
            candidateRpTable: {
              max: 16,
              min: 0,
            },
            pimSsmTable: {
              max: 8,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.PimSmStatus.path,
        loadChildren: () =>
          import('@switch-web/components/pim-sm-status/src/lib/pim-sm-status.module').then(m => m.PimSmStatusModule),
        data: {
          tableSize: {
            pimSmTable: {
              max: 256,
              min: 0,
            },
            neighborTable: {
              max: 256,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.MulticastLocalRoute.path,
        loadChildren: () =>
          import('@switch-web/components/multi-local-route/src/lib/multi-local-route.module').then(
            m => m.MultiLocalRouteModule
          ),
        data: {
          tableSize: {
            vlanRouteTable: {
              max: 16,
              min: 0,
            },
            aclRuleTable: {
              max: 16,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.MulticastRoutingTable.path,
        loadChildren: () =>
          import('@switch-web/components/multicast-routing-table/src/lib/multicast-routing-table.module').then(
            m => m.MulticastRoutingTableModule
          ),
        data: {
          tableSize: {
            routingTable: {
              max: 1048,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.LoginPolicy.path,
        loadChildren: () =>
          import('@switch-web/components/login-policy/src/lib/login-policy.module').then(m => m.LoginPolicyModule),
      },
      {
        path: Page.TrustAccess.path,
        loadChildren: () =>
          import('@switch-web/components/trust-access/src/lib/trust-access.module').then(m => m.TrustAccessModule),
        data: {
          tableSize: {
            trustIpTable: {
              max: 20,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.SshSsl.path,
        loadChildren: () => import('@switch-web/components/ssh-ssl/src/lib/ssh-ssl.module').then(m => m.SshSslModule),
      },
      {
        path: Page.Ieee8021x.path,
        loadChildren: () =>
          import('@switch-web/components/ieee8021x/src/lib/auth-8021x-setting.module').then(
            m => m.Auth8021xSettingModule
          ),
        data: {
          notSupport: ['macBased'],
          tableSize: {
            localDatabase: {
              max: 64,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.MacAuthBypass.path,
        loadChildren: () => import('@switch-web/components/mab/src/lib/mab.module').then(m => m.MabModule),
        data: {
          tableSize: {
            localDatabase: {
              max: 1024,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.MacSec.path,
        loadChildren: () => import('@switch-web/components/mac-sec/src/lib/mac-sec.module').then(m => m.MacSecModule),
      },
      {
        path: Page.PortSecurity.path,
        loadChildren: () =>
          import('@switch-web/components/port-security/src/lib/port-security.module').then(m => m.PortSecurityModule),
        data: {
          tableSize: {
            addressTable: {
              max: 1024,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.StormControl.path,
        loadChildren: () =>
          import('@switch-web/components/storm-control/src/lib/storm-control.module').then(m => m.StormControlModule),
        data: {
          parameter: {
            minThreshold: 625,
          },
        },
      },
      {
        path: Page.Acl.path,
        loadChildren: () => import('@switch-web/components/acl/src/lib/acl.module').then(m => m.AclModule),
        data: {
          notSupport: ['allPort'],
          tableSize: {
            ipAclTable: {
              max: 16,
              min: 0,
            },
            ipAclRuleTable: {
              max: 10,
              min: 0,
            },
            macAclTable: {
              max: 16,
              min: 0,
            },
            macAclRuleTable: {
              max: 10,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.LoopProtection.path,
        loadChildren: () =>
          import('@switch-web/components/loop-protection/src/lib/loop-protection.module').then(
            m => m.LoopProtectionModule
          ),
      },
      {
        path: Page.BindingDatabase.path,
        loadChildren: () =>
          import('@switch-web/components/binding-database/src/lib/binding-database.module').then(
            m => m.BindingDatabaseModule
          ),
        data: {
          tableSize: {
            ipBindingTable: {
              max: 256,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.DhcpSnooping.path,
        loadChildren: () =>
          import('@switch-web/components/dhcp-snooping/src/lib/dhcp-snooping.module').then(m => m.DhcpSnoopingModule),
      },
      {
        path: Page.IpSourceGuard.path,
        loadChildren: () =>
          import('@switch-web/components/ip-source-guard/src/lib/ip-source-guard.module').then(
            m => m.IpSourceGuardModule
          ),
      },
      {
        path: Page.DynamicArpInspection.path,
        loadChildren: () => import('@switch-web/components/dai/src/lib/dai.module').then(m => m.DaiModule),
      },
      {
        path: Page.LoginAuth.path,
        loadChildren: () =>
          import('@switch-web/components/login-authentication/src/lib/login-authentication.module').then(
            m => m.LoginAuthenticationModule
          ),
      },
      {
        path: Page.Radius.path,
        loadChildren: () => import('@switch-web/components/radius/src/lib/radius.module').then(m => m.RadiusModule),
      },
      {
        path: Page.Tacacs.path,
        loadChildren: () =>
          import('@switch-web/components/tacacs-server/src/lib/tacacs-server.module').then(
            m => m.TacacsServerSettingModule
          ),
        data: {
          parameter: {
            maxTimeout: 180,
          },
        },
      },
      {
        path: Page.Utilization.path,
        loadChildren: () =>
          import('@switch-web/components/utilization/src/lib/utilization.module').then(m => m.UtilizationModule),
      },
      {
        path: Page.FiberCheck.path,
        loadChildren: () =>
          import('@switch-web/components/fiber-check/src/lib/fiber-check.module').then(m => m.FiberCheckModule),
      },
      {
        path: Page.ModuleInfo.path,
        loadChildren: () =>
          import('@switch-web/components/module-information/src/lib/module-information.module').then(
            m => m.ModuleInformationModule
          ),
      },
      {
        path: Page.Statistics.path,
        loadChildren: () =>
          import('@switch-web/components/statistics/src/lib/statistics.module').then(m => m.StatisticsModule),
      },
      {
        path: Page.Lldp.path,
        loadChildren: () => import('@switch-web/components/lldp/src/lib/lldp.module').then(m => m.LldpModule),
      },
      {
        path: Page.Arp.path,
        loadChildren: () => import('@switch-web/components/arp/src/lib/arp.module').then(m => m.ArpModule),
        data: {
          tableSize: {
            arpTable: {
              max: 3000,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.RspanPortMirror.path,
        loadChildren: () =>
          import('@switch-web/components/rspan-port-mirror/src/lib/rspan-port-mirror.module').then(
            m => m.RspanPortMirrorModule
          ),
      },
      {
        path: Page.Ping.path,
        loadChildren: () => import('@switch-web/components/ping/src/lib/ping.module').then(m => m.PingModule),
      },
      {
        path: Page.EventLog.path,
        loadChildren: () =>
          import('@switch-web/components/event-log/src/lib/event-log.module').then(m => m.EventLogModule),
        data: {
          notSupport: ['microSD'],
          tableSize: {
            eventLogTable: {
              max: 10000,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.EventNotification.path,
        loadChildren: () =>
          import('@switch-web/components/event-notification/src/lib/event-notification.module').then(
            m => m.EventNotificationModule
          ),
      },
      {
        path: Page.Syslog.path,
        loadChildren: () =>
          import('@switch-web/components/syslog/src/lib/syslog.module').then(m => m.SyslogSettingModule),
      },
      {
        path: Page.SnmpTrapInfo.path,
        loadChildren: () =>
          import('@switch-web/components/snmp-trap/src/lib/snmp-trap.module').then(m => m.SnmpTrapModule),
        data: {
          tableSize: {
            hostTable: {
              max: 2,
              min: 0,
            },
            userAccountTable: {
              max: 1,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.EmailSetting.path,
        loadChildren: () =>
          import('@switch-web/components/email-settings/src/lib/email-settings.module').then(
            m => m.EmailSettingsModule
          ),
      },
      {
        path: Page.RelayAlarm.path,
        loadChildren: () =>
          import('@switch-web/components/relay-output/src/lib/relay-output.module').then(m => m.RelayOutputModule),
      },
      {
        path: Page.Mms.path,
        loadChildren: () => import('@switch-web/components/mms/src/lib/mms.module').then(m => m.MmsModule),
      },
      {
        path: Page.GooseCheck.path,
        loadChildren: () =>
          import('@switch-web/components/goose-check/src/lib/goose-check.module').then(m => m.GooseCheckModule),
        data: {
          tableSize: {
            gooseCheckTable: {
              max: 100,
              min: 0,
            },
          },
        },
      },
      {
        path: Page.ModbusTcp.path,
        loadChildren: () =>
          import('@switch-web/components/modbus-tcp/src/lib/modbus-tcp.module').then(m => m.ModbusTcpModule),
      },
      {
        path: Page.EthernetIp.path,
        loadChildren: () =>
          import('@switch-web/components/ethernet-ip/src/lib/ethernet-ip.module').then(m => m.EthernetIpModule),
      },
      {
        path: Page.Profinet.path,
        loadChildren: () =>
          import('@switch-web/components/profinet/src/lib/profinet.module').then(m => m.ProfinetModule),
      },
    ],
  },
];

export const PagesRoutingModule: ModuleWithProviders<any> = RouterModule.forChild(routes);
