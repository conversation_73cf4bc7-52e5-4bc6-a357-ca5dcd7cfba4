import { ChangeDetectionStrategy, Component, EventEmitter, inject, Input, Output, ViewChild } from '@angular/core';
import { MatLegacyPaginator as MatPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';

import { TranslateService } from '@ngx-translate/core';

interface TableData {
  tableTitle: any;
  tableContent: any[];
}

@Component({
  selector: 'network-summary',
  templateUrl: './summary.component.html',
  styleUrls: ['./summary.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SummaryComponent {
  @Input() set summaryData(data) {
    this.dataSource.data = data;
  }
  @Output() refresh = new EventEmitter();

  @ViewChild(MatSort) set matSort(sort: MatSort) {
    this.dataSource.sort = sort;
  }
  @ViewChild(MatPaginator) set tablePaginator(paginator: MatPaginator) {
    this.dataSource.paginator = paginator;
  }

  displayColumn = [
    'statusDisplay',
    'assetTypeDisplay',
    'ipAddress',
    'macAddress',
    'vendorName',
    'productModelName',
    'osVersion',
    'swFwVersion',
    'firstSeen',
    'lastSeen'
  ];
  dataSource: MatTableDataSource<any> = new MatTableDataSource();
  #translate = inject(TranslateService);

  updateSearch(input: string): void {
    this.dataSource.filter = input.trim().toLowerCase();
  }

  export() {
    this.#generateCSV({
      tableTitle: {
        statusDisplay: this.#translate.instant('features.janus.current_asset_status'),
        assetTypeDisplay: this.#translate.instant('features.janus.asset_type'),
        ipAddress: this.#translate.instant('features.janus.ip_address'),
        macAddress: this.#translate.instant('features.janus.mac_address'),
        vendorName: this.#translate.instant('features.janus.vendor_name'),
        productModelName: this.#translate.instant('features.janus.product_model_name'),
        osVersion: this.#translate.instant('features.janus.os_version'),
        swFwVersion: this.#translate.instant('features.janus.sw_fw_version'),
        firstSeen: this.#translate.instant('features.janus.first_seen'),
        lastSeen: this.#translate.instant('features.janus.last_seen')
      },
      tableContent: this.dataSource.data
    });
  }

  #generateCSV(tableData: TableData): void {
    const csvContent = [
      Object.values(tableData.tableTitle)
        .map(title => `"${title}"`)
        .join(','),
      ...tableData.tableContent.map(row =>
        Object.keys(tableData.tableTitle)
          .map(key => `"${row[key]}"`)
          .join(',')
      )
    ].join('\r\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = window.URL.createObjectURL(blob);
    const navigator = window.navigator as any;
    if (navigator.msSaveOrOpenBlob) {
      navigator.msSaveBlob(blob, 'Asset_Recognition.csv');
    } else {
      const a = document.createElement('a');
      a.href = url;
      a.download = 'Asset_Recognition.csv';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
    window.URL.revokeObjectURL(url);
  }
}
