import { AfterViewInit, ChangeDetectionStrategy, Component, ViewChild } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import {
  FormArray,
  FormControl,
  FormGroup,
  UntypedFormControl,
  UntypedFormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators
} from '@angular/forms';
import { SelectionModel } from '@angular/cdk/collections';
import { MatDialog } from '@angular/material/dialog';
import { MatLegacyPaginator as MatPaginator } from '@angular/material/legacy-paginator';
import { MatLegacyTableDataSource as MatTableDataSource } from '@angular/material/legacy-table';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatSort } from '@angular/material/sort';

import { TranslateService } from '@ngx-translate/core';
import { cloneDeep, find, forEach, orderBy } from 'lodash-es';
import { EMPTY, Observable, catchError, concat, forkJoin, of, switchMap } from 'rxjs';

import * as environment from '@mx-ros-web/environments/environment';
import { AngularElementService } from '@mx-ros-web/shared/Service/angular-element.service';
import { ErrorService } from '@mx-ros-web/shared/Service/error.service';
import { FetchService } from '@mx-ros-web/shared/Service/fetch.service';
import { mediumDialogConfig, smallDialogConfig } from '@mx-ros-web/shared/dialog-config.service';

import { JanusCmd, PkgAction, PkgId, getCliFileCmdObj } from '../package-control.def';
import { JanusPackageControlService } from '../package-control.service';
import {
  ActionType,
  AvOpMode,
  CategoryDataType,
  CommonProfileType,
  Dnp3ObjType,
  Dnp3ProfileType,
  DpOpMethod,
  DpOpMode,
  EipObjType,
  EipProfileType,
  FileType,
  FinsObjType,
  FinsProfileType,
  GooseObjType,
  GooseProfileType,
  GpOpMethod,
  GpOpMode,
  IdsOptionArrayType,
  IdsOptionType,
  Iec104ObjType,
  Iec104ProfileType,
  IpsOpMode,
  LogDestination,
  MelsecObjType,
  MelsecProfileType,
  MmsObjType,
  MmsProfileType,
  ModbusObjType,
  ModbusProfileType,
  OpcuaObjType,
  OpcuaProfileType,
  S7ObjType,
  S7PlusObjType,
  S7PlusProfileType,
  S7ProfileType,
  TrdpObjType,
  TrdpProfileType,
  gManualPrfIdBase,
  gPrfIdBase
} from './configuration.def';
import { ConfigurationService } from './configuration.service';
import { EventSettingModel } from './model/EvtSetting.model';
import { SRV_VLAN_Table } from './model/SRV_VLAN.model';
import { SRV_VPLAN_Table } from './model/SRV_VPLAN.model';
import { ObjectDeleteDialogComponent } from './object-delete-dialog/object-delete-dialog';
import { ObjectSettingDialogComponent } from './object-setting-dialog/object-setting-dialog';
import { ProfileDeleteDialogComponent } from './profile-delete-dialog/profile-delete-dialog';
import { ProfileSettingDialogComponent } from './profile-setting-dialog/profile-setting-dialog';
import { TouDialogComponent } from './tou-dialog/tou-dialog';

@Component({
  templateUrl: './configuration.component.html',
  styleUrls: ['./configuration.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ConfigurationComponent implements AfterViewInit {
  get FileType() {
    return FileType;
  }
  get ActionType() {
    return ActionType;
  }
  get IpsOpMode() {
    return IpsOpMode;
  }
  get AvOpMode() {
    return AvOpMode;
  }
  get DpOpMode() {
    return DpOpMode;
  }
  get GpOpMode() {
    return GpOpMode;
  }
  get DpOpMethod() {
    return DpOpMethod;
  }
  get GpOpMethod() {
    return GpOpMethod;
  }

  ipsSupport = 0;
  avSupport = 0;
  dpSupport = 0;
  gpSupport = 0;
  featureSupport = 0;
  eventLogSupport = false;

  @ViewChild('objectTableSort') objectTableSort: MatSort;
  @ViewChild('objectTablePaginator') objectTablePaginator: MatPaginator;
  objectTableDisplayedColumns = [
    'select',
    'edit',
    'janus-object-name',
    'janus-category',
    'janus-profile-name',
    'dummy'
  ];
  objectTableDataSource: MatTableDataSource<any> = new MatTableDataSource();
  objectSelection = new SelectionModel<any>(true, []);
  objectTableData = [];
  objectTablePanelData = [];
  objectTablePanelPageSize = 20;
  readonly objectLimitation = 64;

  @ViewChild('profileTableSort') profileTableSort: MatSort;
  @ViewChild('profileTablePaginator') profileTablePaginator: MatPaginator;
  profileTableDisplayedColumns = ['select', 'edit', 'janus-profile-name', 'janus-category', 'dummy'];
  profileTableDataSource: MatTableDataSource<any> = new MatTableDataSource();
  profileSelection = new SelectionModel<any>(true, []);
  profileTableData = [];
  profileTablePanelData = [];
  profileTablePanelPageSize = 20;
  readonly profileLimitation = 50;

  logActionList = [
    { text: this.translate.instant('features.janus.syslog'), value: LogDestination.SYSLOG },
    { text: this.translate.instant('features.janus.trap'), value: LogDestination.TRAP },
    { text: this.translate.instant('features.janus.local_storage'), value: LogDestination.LOCAL_STORAGE }
    // { text: this.translate.instant('features.janus.relay'), value: LogDestination.RELAY },
  ];

  severityOption = [
    {
      value: 0,
      text: this.translate.instant('general.common_severity.emergency')
    },
    {
      value: 1,
      text: this.translate.instant('general.common_severity.alert')
    },
    {
      value: 2,
      text: this.translate.instant('general.common_severity.critical')
    },
    {
      value: 3,
      text: this.translate.instant('general.common_severity.error')
    },
    {
      value: 4,
      text: this.translate.instant('general.common_severity.warning')
    },
    {
      value: 5,
      text: this.translate.instant('general.common_severity.notice')
    },
    {
      value: 6,
      text: this.translate.instant('general.common_severity.informational')
    },
    {
      value: 7,
      text: this.translate.instant('general.common_severity.debug')
    }
  ];

  bkResForm = new UntypedFormGroup({
    type: new UntypedFormControl(FileType.CONFIG, Validators.required),
    fileSelection: new UntypedFormControl(null)
  });

  globalForm = new UntypedFormGroup({
    ipsEnable: new UntypedFormControl(null, Validators.required),
    ipsOpMode: new UntypedFormControl(null, Validators.required),
    idsEnable: new FormControl(null, Validators.required),
    idsPortListArray: new FormArray(
      [],
      [this.uniquePortValidator(), this.uniqueVLANValidator(), this.notAllManagementVlanValidator()]
    ),
    avEnable: new UntypedFormControl(null, Validators.required),
    avOpMode: new UntypedFormControl(null, Validators.required),
    avCloudConfirm: new UntypedFormControl(null),
    avCloud: new UntypedFormControl(null, Validators.required),
    dpEnable: new UntypedFormControl(null, Validators.required),
    dpOpMode: new UntypedFormControl(null, Validators.required),
    dpEvtEnable: new FormControl({ value: null, disabled: true }, Validators.required),
    dpEvtSeverity: new FormControl({ value: null, disabled: true }, Validators.required),
    dpEvtActionList: new FormControl({ value: [], disabled: true }),
    gpEnable: new UntypedFormControl(null, Validators.required),
    gpOpMode: new UntypedFormControl(null, Validators.required),
    gpOpMethod: new UntypedFormControl(null, Validators.required),
    enforcementEnable: new UntypedFormControl(null, Validators.required),
    action: new UntypedFormControl(null, Validators.required),
    debugEnable: new UntypedFormControl(null, Validators.required),
    // ngfor protocolDisplay
    // naming: {protocol}Enable, {protocol}Adp, {protocol}Port
    modbusEnable: new UntypedFormControl(null, Validators.required),
    modbusAdp: new UntypedFormControl(null, Validators.required),
    modbusPort: new UntypedFormControl('', [
      Validators.required,
      Validators.pattern(
        '^([1-9]|[1-9][0-9]|[1-9][0-9]{2}|[1-9][0-9]{3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])(,([1-9]|[1-9][0-9]|[1-9][0-9]{2}|[1-9][0-9]{3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))*$'
      )
    ]),
    dnp3Enable: new UntypedFormControl(null, Validators.required),
    dnp3Adp: new UntypedFormControl(null, Validators.required),
    dnp3Port: new UntypedFormControl('', [
      Validators.required,
      Validators.pattern(
        '^([1-9]|[1-9][0-9]|[1-9][0-9]{2}|[1-9][0-9]{3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])(,([1-9]|[1-9][0-9]|[1-9][0-9]{2}|[1-9][0-9]{3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))*$'
      )
    ]),
    iec104Enable: new UntypedFormControl(null, Validators.required),
    iec104Adp: new UntypedFormControl(null, Validators.required),
    iec104Port: new UntypedFormControl('', [
      Validators.required,
      Validators.pattern(
        '^([1-9]|[1-9][0-9]|[1-9][0-9]{2}|[1-9][0-9]{3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])(,([1-9]|[1-9][0-9]|[1-9][0-9]{2}|[1-9][0-9]{3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))*$'
      )
    ]),
    mmsEnable: new UntypedFormControl(null, Validators.required),
    mmsPort: new UntypedFormControl('', [
      Validators.required,
      Validators.pattern(
        '^([1-9]|[1-9][0-9]|[1-9][0-9]{2}|[1-9][0-9]{3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])(,([1-9]|[1-9][0-9]|[1-9][0-9]{2}|[1-9][0-9]{3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))*$'
      )
    ]),
    gooseEnable: new UntypedFormControl(null, Validators.required),
    eipEnable: new UntypedFormControl(null, Validators.required),
    eipAdp: new UntypedFormControl(null, Validators.required),
    eipPort: new UntypedFormControl('', [
      Validators.required,
      Validators.pattern(
        '^([1-9]|[1-9][0-9]|[1-9][0-9]{2}|[1-9][0-9]{3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])(,([1-9]|[1-9][0-9]|[1-9][0-9]{2}|[1-9][0-9]{3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))*$'
      )
    ]),
    finsEnable: new UntypedFormControl(null, Validators.required),
    finsAdp: new UntypedFormControl(null, Validators.required),
    finsPort: new UntypedFormControl('', [
      Validators.required,
      Validators.pattern(
        '^([1-9]|[1-9][0-9]|[1-9][0-9]{2}|[1-9][0-9]{3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])(,([1-9]|[1-9][0-9]|[1-9][0-9]{2}|[1-9][0-9]{3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))*$'
      )
    ]),
    s7Enable: new UntypedFormControl(null, Validators.required),
    s7Adp: new UntypedFormControl(null, Validators.required),
    s7Port: new UntypedFormControl('', [
      Validators.required,
      Validators.pattern(
        '^([1-9]|[1-9][0-9]|[1-9][0-9]{2}|[1-9][0-9]{3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])(,([1-9]|[1-9][0-9]|[1-9][0-9]{2}|[1-9][0-9]{3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))*$'
      )
    ]),
    trdpEnable: new UntypedFormControl(null, Validators.required),
    trdpPort: new UntypedFormControl('', [
      Validators.required,
      Validators.pattern(
        '^([1-9]|[1-9][0-9]|[1-9][0-9]{2}|[1-9][0-9]{3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])(,([1-9]|[1-9][0-9]|[1-9][0-9]{2}|[1-9][0-9]{3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))*$'
      )
    ]),
    opcuaEnable: new UntypedFormControl(null, Validators.required),
    opcuaAdp: new UntypedFormControl(null, Validators.required),
    opcuaPort: new UntypedFormControl('', [
      Validators.required,
      Validators.pattern(
        '^([1-9]|[1-9][0-9]|[1-9][0-9]{2}|[1-9][0-9]{3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])(,([1-9]|[1-9][0-9]|[1-9][0-9]{2}|[1-9][0-9]{3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))*$'
      )
    ]),
    melsecEnable: new UntypedFormControl(null, Validators.required),
    melsecAdp: new UntypedFormControl(null, Validators.required),
    melsecPort: new UntypedFormControl('', [
      Validators.required,
      Validators.pattern(
        '^([1-9]|[1-9][0-9]|[1-9][0-9]{2}|[1-9][0-9]{3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])(,([1-9]|[1-9][0-9]|[1-9][0-9]{2}|[1-9][0-9]{3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))*$'
      )
    ]),
    s7plusEnable: new UntypedFormControl(null, Validators.required),
    s7plusAdp: new UntypedFormControl(null, Validators.required),
    s7plusPort: new UntypedFormControl('', [
      Validators.required,
      Validators.pattern(
        '^([1-9]|[1-9][0-9]|[1-9][0-9]{2}|[1-9][0-9]{3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])(,([1-9]|[1-9][0-9]|[1-9][0-9]{2}|[1-9][0-9]{3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))*$'
      )
    ]),

    dpiEvtEnable: new FormControl({ value: null, disabled: true }, Validators.required),
    dpiEvtSeverity: new FormControl({ value: null, disabled: true }, Validators.required),
    dpiEvtActionList: new FormControl({ value: [], disabled: true }),

    ipsEvtEnable: new FormControl({ value: null, disabled: true }, Validators.required),
    ipsEvtSeverity: new FormControl({ value: null, disabled: true }, Validators.required),
    ipsEvtActionList: new FormControl({ value: [], disabled: true })
  });

  idsPortListArray = this.globalForm.get('idsPortListArray') as FormArray;
  vlanRange = {
    max: 4094,
    min: 1
  };
  noPermission: boolean;
  public protocolDisplay: string[] = [];
  private avCloudChecked = false;
  private localFile = null;
  private categoryList: JanusCmd[] = [];
  private profileTmpList = {
    all: [] as CommonProfileType[],
    modbus: [] as ModbusProfileType[],
    dnp3: [] as Dnp3ProfileType[],
    mms: [] as MmsProfileType[],
    iec104: [] as Iec104ProfileType[],
    goose: [] as GooseProfileType[],
    eip: [] as EipProfileType[],
    fins: [] as FinsProfileType[],
    s7: [] as S7ProfileType[],
    trdp: [] as TrdpProfileType[],
    opcua: [] as OpcuaProfileType[],
    melsec: [] as MelsecProfileType[],
    s7plus: [] as S7PlusProfileType[]
  };

  // object-setting-dialog option data
  private mmsDef = { choice: [], leaf: [], cmd: [] };
  private gooseDef = { choice: [], leaf: [], cmd: [] };
  private dnp3Def = { appFunc: [] };
  private iec104Def = { cot: [], typeId: [] };
  private trdpDef = { msgType: [], comId: [] };

  // cmd minor id
  private readonly minQryAppFwSt = 2;
  private readonly minEnforcementInfo = 301;
  private readonly minSetEnforcement = 300;
  private readonly minEnforcementAct = 2001;
  private readonly minEnforcementSt = 2000;
  // ips
  private readonly minQryIpsSt = 1;
  private readonly minSetIpsSt = 2;
  private readonly minSetIpsOpMode = 3;
  // ids
  private readonly minSetIdsEnable = 101;
  private readonly minSetPortVlan = 102;
  // av
  private readonly minQryAvSt = 1;
  private readonly minSetAvSt = 2;
  private readonly minSetAvOpMode = 3;
  private readonly minSetAvCloud = 4;
  // dp
  private readonly minQryDpSt = 1;
  private readonly minSetDpSt = 2;
  private readonly minSetDpOpMode = 3;
  private readonly minSetDpOpMethod = 4;
  private readonly minSetDpEvtEnable = 501;
  private readonly minSetDpEvtSeverity = 502;
  private readonly minSetDpEvtAction = 503;
  // gp
  private readonly minQryGpSt = 1;
  private readonly minSetGpSt = 2;
  private readonly minSetGpOpMode = 3;
  private readonly minSetGpOpMethod = 4;
  // debug
  private readonly minSetDbgSt = 1;
  private readonly minQryDbgSt = 2;
  // modbus
  private readonly minSetMbSrvPort = 1;
  private readonly minSetMbSt = 2;
  private readonly minQryMbSt = 3;
  private readonly minAdpMbEn = 4;
  private readonly minQryMbTmpltList = 1100;
  private readonly minQryMbAppObjList = 2100;
  // dnp3
  private readonly minSetDnp3SrvPort = 1;
  private readonly minSetDnp3St = 2;
  private readonly minQryDnp3St = 3;
  private readonly minAdpDnp3En = 4;
  private readonly minQryDnp3TmpltList = 1100;
  private readonly minQryDnp3AppObjList = 2100;
  // iec-104
  private readonly minSetIec104SrvPort = 1;
  private readonly minSetIec104St = 2;
  private readonly minQryIec104St = 3;
  private readonly minAdpIec104En = 4;
  private readonly minQryIec104TmpltList = 1100;
  private readonly minQryIec104AppObjList = 2100;
  // mms
  private readonly minSetMmsSrvPort = 1;
  private readonly minSetMmsSt = 2;
  private readonly minQryMmsSt = 3;
  private readonly minQryMmsTmpltList = 1100;
  private readonly minQryMmsAppObjList = 2100;
  // goose
  private readonly minSetGooseSrvPort = 1;
  private readonly minSetGooseSt = 2;
  private readonly minQryGooseSt = 3;
  private readonly minAdpGooseEn = 4;
  private readonly minQryGooseTmpltList = 1100;
  private readonly minQryGooseAppObjList = 2100;
  // eip
  private readonly minSetEipSrvPort = 1;
  private readonly minSetEipSt = 2;
  private readonly minQryEipSt = 3;
  private readonly minAdpEipEn = 4;
  private readonly minQryEipTmpltList = 1100;
  private readonly minQryEipAppObjList = 2100;
  // fins
  private readonly minSetFinsSrvPort = 1;
  private readonly minSetFinsSt = 2;
  private readonly minQryFinsSt = 3;
  private readonly minAdpFinsEn = 4;
  private readonly minQryFinsTmpltList = 1100;
  private readonly minQryFinsAppObjList = 2100;
  // s7
  private readonly minSetS7SrvPort = 1;
  private readonly minSetS7St = 2;
  private readonly minQryS7St = 3;
  private readonly minAdpS7En = 4;
  private readonly minQryS7TmpltList = 1100;
  private readonly minQryS7AppObjList = 2100;
  // trdp
  private readonly minSetTrdpSrvPort = 1;
  private readonly minSetTrdpSt = 2;
  private readonly minQryTrdpSt = 3;
  private readonly minQryTrdpTmpltList = 1100;
  private readonly minQryTrdpAppObjList = 2100;
  // opcua
  private readonly minSetOpcuaSrvPort = 1;
  private readonly minSetOpcuaSt = 2;
  private readonly minQryOpcuaSt = 3;
  private readonly minAdpOpcuaEn = 4;
  private readonly minQryOpcuaTmpltList = 1100;
  private readonly minQryOpcuaAppObjList = 2100;
  // melsec
  private readonly minSetMelsecSrvPort = 1;
  private readonly minSetMelsecSt = 2;
  private readonly minQryMelsecSt = 3;
  private readonly minAdpMelsecEn = 4;
  private readonly minQryMelsecTmpltList = 1100;
  private readonly minQryMelsecAppObjList = 2100;
  // s7plus
  private readonly minSetS7PlusSrvPort = 1;
  private readonly minSetS7PlusSt = 2;
  private readonly minQryS7PlusSt = 3;
  private readonly minAdpS7PlusEn = 4;
  private readonly minQryS7PlusTmpltList = 1100;
  private readonly minQryS7PlusAppObjList = 2100;

  private readonly minImport = 200;
  private readonly minExport = 201;
  private readonly cmdCliSpInput = 'janusCliMike';
  private readonly getSupportFWREvent = 2;

  idsPortOptionList: IdsOptionType[] = [];
  idsPortArrayOptionList: IdsOptionArrayType[] = [];
  // SRV_VLAN 原本的資料
  cacheSRVVlan: SRV_VLAN_Table[] = [];
  // SRV_VPLAN 原本的資料
  cacheSRVVplan: SRV_VPLAN_Table[] = [];
  // 平台已經建立的 vlan
  cachePlatformVlan: SRV_VLAN_Table[] = [];
  // 上次被選擇的 vlan
  cacheSelectedVlan: string[] = [];
  // 用來 cache service define 的資料
  cacheServiceDefine: any = {};
  managementVlanId: number = null;

  constructor(
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private angularEleService: AngularElementService,
    private http: HttpClient,
    private translate: TranslateService,
    private errorService: ErrorService,
    private fetch: FetchService,
    private pkgService: JanusPackageControlService,
    private configurationService: ConfigurationService
  ) {
    this.noPermission = this.pkgService.noPermission;
  }

  ngAfterViewInit(): void {
    this.setupTableSetting();
    this.initialGlobalData();
  }

  initialGlobalData(): void {
    // asp: genGetCliJsonCmd
    const cliFileCmdBody = this.pkgService.cliGenCmd([getCliFileCmdObj]);
    const cliFileGetBody = {
      transmitData: this.pkgService.makeDataSend(
        PkgId.PKG_JANUS,
        PkgAction.GET_PKG_DATA,
        JSON.stringify(cliFileCmdBody)
      )
    };
    this.http
      .post(`${environment.uriRequestURL}/pkg_web/show_web?SRV=SRV_PKG`, cliFileGetBody)
      .subscribe((data: any) => {
        const result = JSON.parse(this.pkgService.parsePkgData(data).body);
        this.pkgService.janusCliDef = result.janusCli[0][JanusCmd.FILE][0].cmd.data.janusCli;

        concat(
          this.refreshGlobalData(),
          this.getEvtSetting(),
          this.getPortOptionList(),
          this.refreshObjectData(),
          this.removeControlNotSupport()
        ).subscribe({
          complete: () => {
            this.angularEleService.emitLoading(false);
          },
          error: error => {
            this.angularEleService.emitLoading(false);
            this.errorService.handleError(error);
          }
        });
      });
  }

  getPortOptionList(): Observable<any> {
    return new Observable(observer => {
      this.configurationService
        .getServiceDefine()
        .pipe(
          switchMap(serviceDefine => {
            this.cacheServiceDefine = serviceDefine.result;
            this.vlanRange.max = this.cacheServiceDefine.SRV_DEVICE_LOCKDOWN ? 4093 : 4094;
            return forkJoin({
              rj45PortOptionList: this.configurationService.getRJ45PortOptionList(),
              srvVlan: this.cacheServiceDefine.SRV_VLAN ? this.configurationService.getSRV_VLAN() : of([]),
              srvVplan: this.cacheServiceDefine.SRV_VPLAN ? this.configurationService.getSRV_VPLAN() : of([])
            });
          })
        )
        .subscribe(
          data => {
            this.cacheSRVVlan = data.srvVlan;
            this.cacheSRVVplan = data.srvVplan;
            const usedVlan = this.cacheSelectedVlan.map(vlan => +vlan.split('-')[1]);
            this.cachePlatformVlan = data.srvVlan.filter(vlan => !usedVlan.includes(vlan.vlanid));

            // 有些型號沒有 vlan 的概念，所以他們設定預設值就好
            if (this.cacheServiceDefine.SRV_VLAN && this.cacheServiceDefine.SRV_VPLAN) {
              // 在 management vlan 的 port 不能被全部選走
              this.managementVlanId = data.srvVlan[0].vlanid;

              const inManagementVlanPort = data.srvVlan
                .filter(vlan => vlan.vlanid === this.managementVlanId)
                .map(vlan =>
                  vlan.port
                    .map((portValue, index) => (portValue !== 0 ? index : -1))
                    .filter(index => index !== -1)
                    .filter(index => data.rj45PortOptionList.some((_, portIndex) => portIndex === index))
                    .map(index => index + 1)
                )
                .reduce((acc, curr) => acc.concat(curr), []);

              let startVlanId = 1001;

              // 轉換已選 VLAN 為集合，加快查找

              // 已經選擇的 port
              const portOptionArray = this.cacheSelectedVlan.map(selectVlan => {
                const [port, vlan] = selectVlan.split('-');
                const portLabel = data.rj45PortOptionList[+port - 1].Label;
                const portDisplay = portLabel.startsWith('port') ? portLabel.replace('port', '') : portLabel;
                return {
                  port,
                  portDisplay: portDisplay,
                  vlan,
                  inManagementVlan: inManagementVlanPort.includes(+port)
                };
              });

              // 找出未選擇但屬於 Management VLAN 的 Port
              const selectedPorts = new Set(this.cacheSelectedVlan.map(vlan => vlan.split('-')[0]));
              const removedSelectedPort = inManagementVlanPort.filter(port => !selectedPorts.has(`${port}`));

              // 生成未選擇 Port 的 VLAN
              const existingVlanIds = new Set(data.srvVlan.map(vlan => vlan.vlanid));
              const additionalPortOptions = removedSelectedPort.map(port => {
                while (existingVlanIds.has(startVlanId)) {
                  startVlanId++;
                }
                const portLabel = data.rj45PortOptionList[port - 1].Label;
                const portDisplay = portLabel.startsWith('port') ? portLabel.replace('port', '') : portLabel;
                const portOption = {
                  port: `${port}`,
                  portDisplay: portDisplay,
                  vlan: `${startVlanId}`,
                  inManagementVlan: true
                };
                startVlanId++;
                return portOption;
              });

              this.idsPortArrayOptionList = [...portOptionArray, ...additionalPortOptions].sort(
                (a, b) => a.port - b.port
              );
            } else {
              if (data.rj45PortOptionList.find(port => port.PortInterface === 'wan2')) {
                this.idsPortArrayOptionList = [{ port: 'WAN2', vlan: '', inManagementVlan: false }];
              }

              if (data.rj45PortOptionList.find(port => port.PortInterface === 'mgmt')) {
                this.idsPortArrayOptionList = [{ port: 'MGMT', vlan: '', inManagementVlan: false }];
              }
            }

            observer.complete();
          },
          error => observer.error(error)
        );
    });
  }

  refreshGlobalData(): Observable<any> {
    return new Observable(observer => {
      this.bkResForm.get('fileSelection').reset();

      this.localFile = null;

      const ipsCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.GET_PKG_DATA,
        majorId: JanusCmd.IPS,
        minorId: this.minQryIpsSt
      });
      const avCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.GET_PKG_DATA,
        majorId: JanusCmd.AV,
        minorId: this.minQryAvSt
      });
      const dpCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.GET_PKG_DATA,
        majorId: JanusCmd.DP,
        minorId: this.minQryDpSt
      });
      const gpCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.GET_PKG_DATA,
        majorId: JanusCmd.GP,
        minorId: this.minQryGpSt
      });
      const fwCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.GET_PKG_DATA,
        majorId: JanusCmd.FEATURE,
        minorId: this.minQryAppFwSt
      });
      const enforcementCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.GET_PKG_DATA,
        majorId: JanusCmd.FEATURE,
        minorId: this.minEnforcementInfo
      });
      const mbCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.GET_PKG_DATA,
        majorId: JanusCmd.MB,
        minorId: this.minQryMbSt
      });
      const dnp3CliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.GET_PKG_DATA,
        majorId: JanusCmd.DNP3,
        minorId: this.minQryDnp3St
      });
      const iec104CliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.GET_PKG_DATA,
        majorId: JanusCmd.IEC104,
        minorId: this.minQryIec104St
      });
      const mmsCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.GET_PKG_DATA,
        majorId: JanusCmd.MMS,
        minorId: this.minQryMmsSt
      });
      const gooseCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.GET_PKG_DATA,
        majorId: JanusCmd.GOOSE,
        minorId: this.minQryGooseSt
      });
      const eipCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.GET_PKG_DATA,
        majorId: JanusCmd.EIP,
        minorId: this.minQryEipSt
      });
      const finsCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.GET_PKG_DATA,
        majorId: JanusCmd.FINS,
        minorId: this.minQryFinsSt
      });
      const s7CliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.GET_PKG_DATA,
        majorId: JanusCmd.S7,
        minorId: this.minQryS7St
      });
      const trdpCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.GET_PKG_DATA,
        majorId: JanusCmd.TRDP,
        minorId: this.minQryTrdpSt
      });
      const opcuaCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.GET_PKG_DATA,
        majorId: JanusCmd.OPCUA,
        minorId: this.minQryOpcuaSt
      });
      const melsecCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.GET_PKG_DATA,
        majorId: JanusCmd.MELSEC,
        minorId: this.minQryMelsecSt
      });
      const s7plusCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.GET_PKG_DATA,
        majorId: JanusCmd.S7PLUS,
        minorId: this.minQryS7PlusSt
      });
      const dbgCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.GET_PKG_DATA,
        majorId: JanusCmd.DBG,
        minorId: this.minQryDbgSt
      });
      const evtSettingCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.GET_PKG_DATA,
        majorId: JanusCmd.INFO,
        minorId: this.getSupportFWREvent
      });
      const globalCliCmdBody = this.pkgService.cliGenCmd([
        ipsCliCmdObj,
        avCliCmdObj,
        dpCliCmdObj,
        gpCliCmdObj,
        fwCliCmdObj,
        enforcementCliCmdObj,
        mbCliCmdObj,
        dnp3CliCmdObj,
        iec104CliCmdObj,
        mmsCliCmdObj,
        gooseCliCmdObj,
        eipCliCmdObj,
        finsCliCmdObj,
        s7CliCmdObj,
        trdpCliCmdObj,
        opcuaCliCmdObj,
        melsecCliCmdObj,
        s7plusCliCmdObj,
        dbgCliCmdObj,
        evtSettingCmdObj
      ]);
      const globalCliGetBody = {
        transmitData: this.pkgService.makeDataSend(
          PkgId.PKG_JANUS,
          PkgAction.GET_PKG_DATA,
          JSON.stringify(globalCliCmdBody)
        )
      };
      this.http.post(`${environment.uriRequestURL}/pkg_web/show_web?SRV=SRV_PKG`, globalCliGetBody).subscribe(
        data => {
          const result = JSON.parse(this.pkgService.parsePkgData(data).body);

          this.categoryList = [];
          this.protocolDisplay = [];
          forEach(result.janusCli, cli => {
            const majorId = +Object.keys(cli)[0];
            const minorId = cli[majorId][0].cmd.minor;
            const cliData = cli[majorId][0].cmd.data;
            const ret = cli[majorId][0].cmd.ret;
            if (this.pkgService.isError(+ret)) {
              this.snackBar.open(
                `Error query: ${this.pkgService.getError(ret)}`,
                this.translate.instant('general.button.close'),
                { panelClass: ['error'] }
              );

              return false;
            }
            switch (majorId) {
              case JanusCmd.IPS: {
                this.ipsSupport = 1;
                this.cacheSelectedVlan = cliData['port-vlan'] ? this.base0Port(cliData['port-vlan']) : [];
                this.idsPortListArray.clear();

                this.cacheSelectedVlan.forEach(selectVlan => {
                  const port = selectVlan.split('-')[0];
                  const vlan = selectVlan.split('-')[1];

                  this.idsPortListArray.push(
                    new FormGroup({
                      port: new FormControl(port, [Validators.required]),
                      vlan: new FormControl(vlan, [
                        Validators.required,
                        Validators.max(this.vlanRange.max),
                        Validators.min(this.vlanRange.min)
                      ])
                    })
                  );
                });
                this.globalForm.patchValue({
                  ipsEnable: (cliData.tdts === 1 || cliData.lcsec === 1) && cliData.enable === 1,
                  ipsOpMode: cliData.opmode,
                  idsEnable: cliData.enableIds
                });
                break;
              }
              case JanusCmd.AV:
                this.avSupport = 1;
                this.globalForm.patchValue({
                  avEnable: cliData.lcsec === 1 && cliData.enable === 1,
                  avOpMode: cliData.opmode === 1 ? AvOpMode.INLINE : AvOpMode.OFFLINE,
                  avCloud: cliData.cloud,
                  avCloudConfirm: cliData.cloud
                });
                if (0 !== cliData.cloud) {
                  this.avCloudToggle();
                  this.globalForm.get('avCloud').enable();
                }
                break;
              case JanusCmd.DP:
                this.dpSupport = 1;
                this.globalForm.patchValue({
                  dpEnable: cliData.enable === 1,
                  dpOpMode: cliData.opmode === 1 ? DpOpMode.INLINE : DpOpMode.OFFLINE
                });
                break;
              case JanusCmd.GP:
                this.gpSupport = 1;
                this.globalForm.patchValue({
                  gpEnable: cliData.lcsec === 1 && cliData.enable === 1,
                  gpOpMode: cliData.opmode === 1 ? GpOpMode.INLINE : GpOpMode.OFFLINE,
                  gpOpMethod: cliData.opmethod === 1 ? GpOpMethod.BL : GpOpMethod.WL
                });
                break;
              case JanusCmd.FEATURE:
                this.featureSupport = 1;
                if (minorId === this.minEnforcementInfo) {
                  this.globalForm.patchValue({
                    enforcementEnable: cliData.enable === 1,
                    action: cliData.action
                  });
                }
                break;
              case JanusCmd.MB:
                this.globalForm.patchValue({
                  modbusEnable: cliData.enable === 1,
                  modbusAdp: cliData.adp === 1,
                  modbusPort: cliData.ports
                });
                if (cliData.enable !== -1) {
                  this.categoryList.push(JanusCmd.MB);
                  this.protocolDisplay.push('modbus');
                }
                break;
              case JanusCmd.DNP3:
                this.globalForm.patchValue({
                  dnp3Enable: cliData.enable === 1,
                  dnp3Adp: cliData.adp === 1,
                  dnp3Port: cliData.ports
                });
                if (cliData.enable !== -1) {
                  this.categoryList.push(JanusCmd.DNP3);
                  this.protocolDisplay.push('dnp3');
                }
                break;
              case JanusCmd.IEC104:
                this.globalForm.patchValue({
                  iec104Enable: cliData.enable === 1,
                  iec104Adp: cliData.adp === 1,
                  iec104Port: cliData.ports
                });
                if (cliData.enable !== -1) {
                  this.categoryList.push(JanusCmd.IEC104);
                  this.protocolDisplay.push('iec104');
                }
                break;
              case JanusCmd.MMS:
                this.globalForm.patchValue({
                  mmsEnable: cliData.enable === 1,
                  mmsPort: cliData.ports
                });
                if (cliData.enable !== -1) {
                  this.categoryList.push(JanusCmd.MMS);
                  this.protocolDisplay.push('mms');
                }
                break;
              case JanusCmd.GOOSE:
                this.globalForm.patchValue({
                  gooseEnable: cliData.enable === 1
                });
                if (cliData.enable !== -1) {
                  this.categoryList.push(JanusCmd.GOOSE);
                  this.protocolDisplay.push('goose');
                }
                break;
              case JanusCmd.EIP:
                this.globalForm.patchValue({
                  eipEnable: cliData.enable === 1,
                  eipAdp: cliData.adp === 1,
                  eipPort: cliData.ports
                });
                if (cliData.enable !== -1) {
                  this.categoryList.push(JanusCmd.EIP);
                  this.protocolDisplay.push('eip');
                }
                break;
              case JanusCmd.FINS:
                this.globalForm.patchValue({
                  finsEnable: cliData.enable === 1,
                  finsAdp: cliData.adp === 1,
                  finsPort: cliData.ports
                });
                if (cliData.enable !== -1) {
                  this.categoryList.push(JanusCmd.FINS);
                  this.protocolDisplay.push('fins');
                }
                break;
              case JanusCmd.S7:
                this.globalForm.patchValue({
                  s7Enable: cliData.enable === 1,
                  s7Adp: cliData.adp === 1,
                  s7Port: cliData.ports
                });
                if (cliData.enable !== -1) {
                  this.categoryList.push(JanusCmd.S7);
                  this.protocolDisplay.push('s7');
                }
                break;
              case JanusCmd.TRDP:
                this.globalForm.patchValue({
                  trdpEnable: cliData.enable === 1,
                  trdpPort: cliData.ports
                });
                if (cliData.enable !== -1) {
                  this.categoryList.push(JanusCmd.TRDP);
                  this.protocolDisplay.push('trdp');
                }
                break;
              case JanusCmd.OPCUA:
                this.globalForm.patchValue({
                  opcuaEnable: cliData.enable === 1,
                  opcuaAdp: cliData.adp === 1,
                  opcuaPort: cliData.ports
                });
                if (cliData.enable !== -1) {
                  this.categoryList.push(JanusCmd.OPCUA);
                  this.protocolDisplay.push('opcua');
                }
                break;
              case JanusCmd.MELSEC:
                this.globalForm.patchValue({
                  melsecEnable: cliData.enable === 1,
                  melsecAdp: cliData.adp === 1,
                  melsecPort: cliData.ports
                });
                if (cliData.enable !== -1) {
                  this.categoryList.push(JanusCmd.MELSEC);
                  this.protocolDisplay.push('melsec');
                }
                break;
              case JanusCmd.S7PLUS:
                this.globalForm.patchValue({
                  s7plusEnable: cliData.enable === 1,
                  s7plusAdp: cliData.adp === 1,
                  s7plusPort: cliData.ports
                });
                if (cliData.enable !== -1) {
                  this.categoryList.push(JanusCmd.S7PLUS);
                  this.protocolDisplay.push('s7plus');
                }
                break;
              case JanusCmd.DBG:
                this.globalForm.get('debugEnable').setValue(cliData.enable === 1);
                break;
              case JanusCmd.INFO:
                this.eventLogSupport = cliData?.fwrEvtSetting;
            }
          });
          observer.complete();
        },
        error => {
          observer.error(error);
        }
      );
    });
  }

  getEvtSetting(): Observable<any> {
    return new Observable(observer => {
      if (!this.eventLogSupport) {
        return observer.complete();
      }

      this.configurationService.getEvtSetting().subscribe(data => {
        if (this.ipsSupport) {
          this.globalForm.get('ipsEvtEnable').enable();
          this.globalForm.get('ipsEvtSeverity').enable();
          this.globalForm.get('ipsEvtActionList').enable();
        }
        if (this.dpSupport) {
          this.globalForm.get('dpEvtEnable').enable();
          this.globalForm.get('dpEvtSeverity').enable();
          this.globalForm.get('dpEvtActionList').enable();
        }
        if (this.featureSupport) {
          this.globalForm.get('dpiEvtEnable').enable();
          this.globalForm.get('dpiEvtSeverity').enable();
          this.globalForm.get('dpiEvtActionList').enable();
        }
        // 取得IPS/DPI事件設定
        this.globalForm.patchValue({
          ipsEvtEnable: data?.ips?.evtEnable,
          ipsEvtSeverity: data?.ips?.evtSeverity,
          ipsEvtActionList: this.extractLogDestinations(data?.ips),
          dpiEvtEnable: data?.dpi?.evtEnable,
          dpiEvtSeverity: data?.dpi?.evtSeverity,
          dpiEvtActionList: this.extractLogDestinations(data?.dpi),
          dpEvtEnable: data?.domainProtect?.evtEnable,
          dpEvtSeverity: data?.domainProtect?.evtSeverity,
          dpEvtActionList: this.extractLogDestinations(data?.domainProtect)
        });
        observer.complete();
      });
    });
  }

  refreshObjectData(): Observable<any> {
    return new Observable(observer => {
      const cliCmdObjList = [];
      let mmsDefObserable: Observable<any> = EMPTY;
      let gooseDefObserable: Observable<any> = EMPTY;
      let dnp3DefObserable: Observable<any> = EMPTY;
      let iec104DefObserable: Observable<any> = EMPTY;
      let trdpDefObserable: Observable<any> = EMPTY;

      if (this.categoryList.find(id => id === JanusCmd.MB)) {
        const modbusTmpCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.MB,
          minorId: this.minQryMbTmpltList
        });
        const modbusCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.MB,
          minorId: this.minQryMbAppObjList
        });
        cliCmdObjList.push(modbusTmpCliCmdObj);
        cliCmdObjList.push(modbusCliCmdObj);
      }
      if (this.categoryList.find(id => id === JanusCmd.DNP3)) {
        const dnp3TmpCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.DNP3,
          minorId: this.minQryDnp3TmpltList
        });
        const dnp3CliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.DNP3,
          minorId: this.minQryDnp3AppObjList
        });
        cliCmdObjList.push(dnp3TmpCliCmdObj);
        cliCmdObjList.push(dnp3CliCmdObj);

        const minDataTransDnp3DefJson = 10;
        const dnp3DefCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.FILE,
          minorId: minDataTransDnp3DefJson
        });
        const dnp3DefCliCmdBody = this.pkgService.cliGenCmd([dnp3DefCliCmdObj]);
        const dnp3DefCliGetBody = {
          transmitData: this.pkgService.makeDataSend(
            PkgId.PKG_JANUS,
            PkgAction.GET_PKG_DATA,
            JSON.stringify(dnp3DefCliCmdBody)
          )
        };
        dnp3DefObserable = this.http.post(
          `${environment.uriRequestURL}/pkg_web/show_web?SRV=SRV_PKG`,
          dnp3DefCliGetBody
        );
        forkJoin({
          dnp3Def: dnp3DefObserable
        }).subscribe(
          data => {
            if (data.dnp3Def) {
              const dnp3DefResult = JSON.parse(this.pkgService.parsePkgData(data.dnp3Def).body);
              const dnp3Def: { appFunc: any } = dnp3DefResult.janusCli[0][JanusCmd.FILE][0].cmd.data;
              forEach(dnp3Def, (optList, optionType) => {
                forEach(optList, (opt, index) => {
                  this.dnp3Def[optionType][+index] = opt;
                });
              });
            }
          },
          error => {
            observer.error(error);
          }
        );
      }
      if (this.categoryList.find(id => id === JanusCmd.IEC104)) {
        const iec104TmpCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.IEC104,
          minorId: this.minQryIec104TmpltList
        });
        const iec104CliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.IEC104,
          minorId: this.minQryIec104AppObjList
        });
        cliCmdObjList.push(iec104TmpCliCmdObj);
        cliCmdObjList.push(iec104CliCmdObj);

        const minDataTransIec104DefJson = 11;
        const iec104DefCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.FILE,
          minorId: minDataTransIec104DefJson
        });
        const iec104DefCliCmdBody = this.pkgService.cliGenCmd([iec104DefCliCmdObj]);
        const iec104DefCliGetBody = {
          transmitData: this.pkgService.makeDataSend(
            PkgId.PKG_JANUS,
            PkgAction.GET_PKG_DATA,
            JSON.stringify(iec104DefCliCmdBody)
          )
        };
        iec104DefObserable = this.http.post(
          `${environment.uriRequestURL}/pkg_web/show_web?SRV=SRV_PKG`,
          iec104DefCliGetBody
        );
        forkJoin({
          iec104Def: iec104DefObserable
        }).subscribe(
          data => {
            if (data.iec104Def) {
              const iec104DefResult = JSON.parse(this.pkgService.parsePkgData(data.iec104Def).body);
              const iec104Def: { cot: any; typeId: any } = iec104DefResult.janusCli[0][JanusCmd.FILE][0].cmd.data;
              forEach(iec104Def, (optList, optionType) => {
                forEach(optList, (opt, index) => {
                  this.iec104Def[optionType][+index] = opt;
                });
              });
            }
          },
          error => {
            observer.error(error);
          }
        );
      }
      if (this.categoryList.find(id => id === JanusCmd.MMS)) {
        const mmsTmpCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.MMS,
          minorId: this.minQryMmsTmpltList
        });
        const mmsCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.MMS,
          minorId: this.minQryMmsAppObjList
        });
        cliCmdObjList.push(mmsTmpCliCmdObj);
        cliCmdObjList.push(mmsCliCmdObj);

        const minDataTransMmsDefJson = 2;
        const mmsDefCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.FILE,
          minorId: minDataTransMmsDefJson
        });
        const mmsDefCliCmdBody = this.pkgService.cliGenCmd([mmsDefCliCmdObj]);
        const mmsDefCliGetBody = {
          transmitData: this.pkgService.makeDataSend(
            PkgId.PKG_JANUS,
            PkgAction.GET_PKG_DATA,
            JSON.stringify(mmsDefCliCmdBody)
          )
        };
        mmsDefObserable = this.http.post(`${environment.uriRequestURL}/pkg_web/show_web?SRV=SRV_PKG`, mmsDefCliGetBody);
        forkJoin({
          mmsDef: mmsDefObserable
        }).subscribe(
          data => {
            if (data.mmsDef) {
              const mmsDefResult = JSON.parse(this.pkgService.parsePkgData(data.mmsDef).body);
              const mmsDef: { choice: any; leaf: any; cmd: any } = mmsDefResult.janusCli[0][JanusCmd.FILE][0].cmd.data;
              forEach(mmsDef, (optList, optionType) => {
                forEach(optList, (opt, index) => {
                  this.mmsDef[optionType][+index] = opt;
                });
              });
            }
          },
          error => {
            observer.error(error);
          }
        );
      }
      if (this.categoryList.find(id => id === JanusCmd.GOOSE)) {
        const gooseTmpCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.GOOSE,
          minorId: this.minQryGooseTmpltList
        });
        const gooseCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.GOOSE,
          minorId: this.minQryGooseAppObjList
        });
        cliCmdObjList.push(gooseTmpCliCmdObj);
        cliCmdObjList.push(gooseCliCmdObj);

        const minDataTransGooseDefJson = 9;
        const gooseDefCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.FILE,
          minorId: minDataTransGooseDefJson
        });
        const gooseDefCliCmdBody = this.pkgService.cliGenCmd([gooseDefCliCmdObj]);
        const gooseDefCliGetBody = {
          transmitData: this.pkgService.makeDataSend(
            PkgId.PKG_JANUS,
            PkgAction.GET_PKG_DATA,
            JSON.stringify(gooseDefCliCmdBody)
          )
        };
        gooseDefObserable = this.http.post(
          `${environment.uriRequestURL}/pkg_web/show_web?SRV=SRV_PKG`,
          gooseDefCliGetBody
        );
        forkJoin({
          gooseDef: gooseDefObserable
        }).subscribe(
          data => {
            if (data.gooseDef) {
              const gooseDefResult = JSON.parse(this.pkgService.parsePkgData(data.gooseDef).body);
              const gooseDef: { choice: any; leaf: any; cmd: any } =
                gooseDefResult.janusCli[0][JanusCmd.FILE][0].cmd.data;
              forEach(gooseDef, (optList, optionType) => {
                forEach(optList, (opt, index) => {
                  this.gooseDef[optionType][+index] = opt;
                });
              });
            }
          },
          error => {
            observer.error(error);
          }
        );
      }

      if (this.categoryList.find(id => id === JanusCmd.EIP)) {
        const eipTmpCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.EIP,
          minorId: this.minQryEipTmpltList
        });
        const eipCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.EIP,
          minorId: this.minQryEipAppObjList
        });
        cliCmdObjList.push(eipTmpCliCmdObj);
        cliCmdObjList.push(eipCliCmdObj);
      }
      if (this.categoryList.find(id => id === JanusCmd.FINS)) {
        const finsTmpCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.FINS,
          minorId: this.minQryFinsTmpltList
        });
        const finsCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.FINS,
          minorId: this.minQryFinsAppObjList
        });
        cliCmdObjList.push(finsTmpCliCmdObj);
        cliCmdObjList.push(finsCliCmdObj);
      }
      if (this.categoryList.find(id => id === JanusCmd.S7)) {
        const s7TmpCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.S7,
          minorId: this.minQryS7TmpltList
        });
        const s7CliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.S7,
          minorId: this.minQryS7AppObjList
        });
        cliCmdObjList.push(s7TmpCliCmdObj);
        cliCmdObjList.push(s7CliCmdObj);
      }
      if (this.categoryList.find(id => id === JanusCmd.TRDP)) {
        const trdpTmpCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.TRDP,
          minorId: this.minQryTrdpTmpltList
        });
        const trdpCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.TRDP,
          minorId: this.minQryTrdpAppObjList
        });
        cliCmdObjList.push(trdpTmpCliCmdObj);
        cliCmdObjList.push(trdpCliCmdObj);

        const minDataTransTrdpDefJson = 12;
        const trdpDefCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.FILE,
          minorId: minDataTransTrdpDefJson
        });
        const trdpDefCliCmdBody = this.pkgService.cliGenCmd([trdpDefCliCmdObj]);
        const trdpDefCliGetBody = {
          transmitData: this.pkgService.makeDataSend(
            PkgId.PKG_JANUS,
            PkgAction.GET_PKG_DATA,
            JSON.stringify(trdpDefCliCmdBody)
          )
        };
        trdpDefObserable = this.http.post(
          `${environment.uriRequestURL}/pkg_web/show_web?SRV=SRV_PKG`,
          trdpDefCliGetBody
        );
        forkJoin({
          trdpDef: trdpDefObserable
        }).subscribe(
          data => {
            if (data.trdpDef) {
              const trdpDefResult = JSON.parse(this.pkgService.parsePkgData(data.trdpDef).body);
              const trdpDef: { msgType: any; comId: any } = trdpDefResult.janusCli[0][JanusCmd.FILE][0].cmd.data;
              forEach(trdpDef, (optList, optionType) => {
                forEach(optList, (opt, index) => {
                  this.trdpDef[optionType][+index] = opt;
                });
              });
            }
          },
          error => {
            observer.error(error);
          }
        );
      }
      if (this.categoryList.find(id => id === JanusCmd.OPCUA)) {
        const opcuaTmpCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.OPCUA,
          minorId: this.minQryOpcuaTmpltList
        });
        const opcuaCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.OPCUA,
          minorId: this.minQryOpcuaAppObjList
        });
        cliCmdObjList.push(opcuaTmpCliCmdObj);
        cliCmdObjList.push(opcuaCliCmdObj);
      }
      if (this.categoryList.find(id => id === JanusCmd.MELSEC)) {
        const melsecTmpCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.MELSEC,
          minorId: this.minQryOpcuaTmpltList
        });
        const melsecCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.MELSEC,
          minorId: this.minQryOpcuaAppObjList
        });
        cliCmdObjList.push(melsecTmpCliCmdObj);
        cliCmdObjList.push(melsecCliCmdObj);
      }
      if (this.categoryList.find(id => id === JanusCmd.S7PLUS)) {
        const s7plusTmpCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.S7PLUS,
          minorId: this.minQryOpcuaTmpltList
        });
        const s7plusCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.GET_PKG_DATA,
          majorId: JanusCmd.S7PLUS,
          minorId: this.minQryOpcuaAppObjList
        });
        cliCmdObjList.push(s7plusTmpCliCmdObj);
        cliCmdObjList.push(s7plusCliCmdObj);
      }

      const objectCliCmdBody = this.pkgService.cliGenCmd(cliCmdObjList);
      const objectCliGetBody = {
        transmitData: this.pkgService.makeDataSend(
          PkgId.PKG_JANUS,
          PkgAction.GET_PKG_DATA,
          JSON.stringify(objectCliCmdBody)
        )
      };

      forkJoin({
        object: this.http.post(`${environment.uriRequestURL}/pkg_web/show_web?SRV=SRV_PKG`, objectCliGetBody)
      }).subscribe(
        data => {
          const objectResult = JSON.parse(this.pkgService.parsePkgData(data.object).body);
          const emptyData: CategoryDataType = {
            modbus: [],
            dnp3: [],
            iec104: [],
            mms: [],
            goose: [],
            eip: [],
            fins: [],
            s7: [],
            trdp: [],
            opcua: [],
            melsec: [],
            s7plus: []
          };
          const objRequests: CategoryDataType = cloneDeep(emptyData);
          const tmpRequests: CategoryDataType = cloneDeep(emptyData);
          const tmpFileNameList: CategoryDataType = cloneDeep(emptyData);
          const objFileNameList: CategoryDataType = cloneDeep(emptyData);

          forEach(objectResult.janusCli, cli => {
            const majorId = +Object.keys(cli)[0];
            const minorId = cli[majorId][0].cmd.minor;
            const cliData = cli[majorId][0].cmd.data;
            const ret = +cli[majorId][0].cmd.ret;
            if (this.pkgService.isError(ret)) {
              this.snackBar.open(
                `Error query:  ${this.pkgService.getError(ret)}`,
                this.translate.instant('general.button.close'),
                { panelClass: ['error'] }
              );
              return false;
            }

            let tmpList;
            let objList;
            const noCache = '?no_cache=1';
            const filePath = `${environment.apiHost}/pkg/Janus/Janus/conf/`;
            switch (majorId) {
              case JanusCmd.MB:
                if (minorId === this.minQryMbTmpltList) {
                  tmpList = cliData ? cliData.split(',').filter(n => n) : [];
                  forEach(tmpList, fileName => {
                    tmpRequests.modbus.push(this.fetch.getJson(filePath + fileName + noCache));
                    tmpFileNameList.modbus.push(fileName);
                  });
                } else {
                  // this.minQryMbAppObjList
                  objList = cliData ? cliData.split(',').filter(n => n) : [];
                  forEach(objList, fileName => {
                    objRequests.modbus.push(this.fetch.getJson(filePath + fileName + noCache));
                    objFileNameList.modbus.push(fileName);
                  });
                }
                break;
              case JanusCmd.DNP3:
                if (minorId === this.minQryDnp3TmpltList) {
                  tmpList = cliData ? cliData.split(',').filter(n => n) : [];
                  forEach(tmpList, fileName => {
                    tmpRequests.dnp3.push(this.fetch.getJson(filePath + fileName + noCache));
                    tmpFileNameList.dnp3.push(fileName);
                  });
                } else {
                  // this.minQryDnp3AppObjList
                  objList = cliData ? cliData.split(',').filter(n => n) : [];
                  forEach(objList, fileName => {
                    objRequests.dnp3.push(this.fetch.getJson(filePath + fileName + noCache));
                    objFileNameList.dnp3.push(fileName);
                  });
                }
                break;
              case JanusCmd.IEC104:
                if (minorId === this.minQryIec104TmpltList) {
                  tmpList = cliData ? cliData.split(',').filter(n => n) : [];
                  forEach(tmpList, fileName => {
                    tmpRequests.iec104.push(this.fetch.getJson(filePath + fileName + noCache));
                    tmpFileNameList.iec104.push(fileName);
                  });
                } else {
                  // this.minQryIec104AppObjList
                  objList = cliData ? cliData.split(',').filter(n => n) : [];
                  forEach(objList, fileName => {
                    objRequests.iec104.push(this.fetch.getJson(filePath + fileName + noCache));
                    objFileNameList.iec104.push(fileName);
                  });
                }
                break;
              case JanusCmd.MMS:
                if (minorId === this.minQryMmsTmpltList) {
                  tmpList = cliData ? cliData.split(',').filter(n => n) : [];
                  forEach(tmpList, fileName => {
                    tmpRequests.mms.push(this.fetch.getJson(filePath + fileName + noCache));
                    tmpFileNameList.mms.push(fileName);
                  });
                } else {
                  // this.minQryMmsAppObjList
                  objList = cliData ? cliData.split(',').filter(n => n) : [];
                  forEach(objList, fileName => {
                    objRequests.mms.push(this.fetch.getJson(filePath + fileName + noCache));
                    objFileNameList.mms.push(fileName);
                  });
                }
                break;
              case JanusCmd.GOOSE:
                if (minorId === this.minQryGooseTmpltList) {
                  tmpList = cliData ? cliData.split(',').filter(n => n) : [];
                  forEach(tmpList, fileName => {
                    tmpRequests.goose.push(this.fetch.getJson(filePath + fileName + noCache));
                    tmpFileNameList.goose.push(fileName);
                  });
                } else {
                  // this.minQryGooseAppObjList
                  objList = cliData ? cliData.split(',').filter(n => n) : [];
                  forEach(objList, fileName => {
                    objRequests.goose.push(this.fetch.getJson(filePath + fileName + noCache));
                    objFileNameList.goose.push(fileName);
                  });
                }
                break;
              case JanusCmd.EIP:
                if (minorId === this.minQryEipTmpltList) {
                  tmpList = cliData ? cliData.split(',').filter(n => n) : [];
                  forEach(tmpList, fileName => {
                    tmpRequests.eip.push(this.fetch.getJson(filePath + fileName + noCache));
                    tmpFileNameList.eip.push(fileName);
                  });
                } else {
                  // this.minQryEipAppObjList
                  objList = cliData ? cliData.split(',').filter(n => n) : [];
                  forEach(objList, fileName => {
                    objRequests.eip.push(this.fetch.getJson(filePath + fileName + noCache));
                    objFileNameList.eip.push(fileName);
                  });
                }
                break;
              case JanusCmd.FINS:
                if (minorId === this.minQryFinsTmpltList) {
                  tmpList = cliData ? cliData.split(',').filter(n => n) : [];
                  forEach(tmpList, fileName => {
                    tmpRequests.fins.push(this.fetch.getJson(filePath + fileName + noCache));
                    tmpFileNameList.fins.push(fileName);
                  });
                } else {
                  // this.minQryFinsAppObjList
                  objList = cliData ? cliData.split(',').filter(n => n) : [];
                  forEach(objList, fileName => {
                    objRequests.fins.push(this.fetch.getJson(filePath + fileName + noCache));
                    objFileNameList.fins.push(fileName);
                  });
                }
                break;
              case JanusCmd.S7:
                if (minorId === this.minQryS7TmpltList) {
                  tmpList = cliData ? cliData.split(',').filter(n => n) : [];
                  forEach(tmpList, fileName => {
                    tmpRequests.s7.push(this.fetch.getJson(filePath + fileName + noCache));
                    tmpFileNameList.s7.push(fileName);
                  });
                } else {
                  // this.minQryS7AppObjList
                  objList = cliData ? cliData.split(',').filter(n => n) : [];
                  forEach(objList, fileName => {
                    objRequests.s7.push(this.fetch.getJson(filePath + fileName + noCache));
                    objFileNameList.s7.push(fileName);
                  });
                }
                break;
              case JanusCmd.TRDP:
                if (minorId === this.minQryTrdpTmpltList) {
                  tmpList = cliData ? cliData.split(',').filter(n => n) : [];
                  forEach(tmpList, fileName => {
                    tmpRequests.trdp.push(this.fetch.getJson(filePath + fileName + noCache));
                    tmpFileNameList.trdp.push(fileName);
                  });
                } else {
                  // this.minQryTrdpAppObjList
                  objList = cliData ? cliData.split(',').filter(n => n) : [];
                  forEach(objList, fileName => {
                    objRequests.trdp.push(this.fetch.getJson(filePath + fileName + noCache));
                    objFileNameList.trdp.push(fileName);
                  });
                }
                break;
              case JanusCmd.OPCUA:
                if (minorId === this.minQryOpcuaTmpltList) {
                  tmpList = cliData ? cliData.split(',').filter(n => n) : [];
                  forEach(tmpList, fileName => {
                    tmpRequests.opcua.push(this.fetch.getJson(filePath + fileName + noCache));
                    tmpFileNameList.opcua.push(fileName);
                  });
                } else {
                  // this.minQryOpcuaAppObjList
                  objList = cliData ? cliData.split(',').filter(n => n) : [];
                  forEach(objList, fileName => {
                    objRequests.opcua.push(this.fetch.getJson(filePath + fileName + noCache));
                    objFileNameList.opcua.push(fileName);
                  });
                }
                break;
              case JanusCmd.MELSEC:
                if (minorId === this.minQryMelsecTmpltList) {
                  tmpList = cliData ? cliData.split(',').filter(n => n) : [];
                  forEach(tmpList, fileName => {
                    tmpRequests.melsec.push(this.fetch.getJson(filePath + fileName + noCache));
                    tmpFileNameList.melsec.push(fileName);
                  });
                } else {
                  // this.minQryMelsecAppObjList
                  objList = cliData ? cliData.split(',').filter(n => n) : [];
                  forEach(objList, fileName => {
                    objRequests.melsec.push(this.fetch.getJson(filePath + fileName + noCache));
                    objFileNameList.melsec.push(fileName);
                  });
                }
                break;
              case JanusCmd.S7PLUS:
                if (minorId === this.minQryS7PlusTmpltList) {
                  tmpList = cliData ? cliData.split(',').filter(n => n) : [];
                  forEach(tmpList, fileName => {
                    tmpRequests.s7plus.push(this.fetch.getJson(filePath + fileName + noCache));
                    tmpFileNameList.s7plus.push(fileName);
                  });
                } else {
                  // this.minQryS7PlusAppObjList
                  objList = cliData ? cliData.split(',').filter(n => n) : [];
                  forEach(objList, fileName => {
                    objRequests.s7plus.push(this.fetch.getJson(filePath + fileName + noCache));
                    objFileNameList.s7plus.push(fileName);
                  });
                }
                break;
            }
          });

          Promise.all([
            this.getTmpJsonFile(tmpRequests, tmpFileNameList),
            this.getObjJsonFile(objRequests, objFileNameList)
          ])
            .then(() => {
              this.profileTmpList.all = orderBy(this.profileTmpList.all, ['id'], ['asc']);
              forEach(this.objectTableData, object => {
                let profile;
                switch (object.category) {
                  case JanusCmd.MB:
                    profile = find(this.profileTmpList.modbus, prf => prf.fileName === object.profileFileName);
                    break;
                  case JanusCmd.DNP3:
                    profile = find(this.profileTmpList.dnp3, prf => prf.fileName === object.profileFileName);
                    break;
                  case JanusCmd.IEC104:
                    profile = find(this.profileTmpList.iec104, prf => prf.fileName === object.profileFileName);
                    break;
                  case JanusCmd.MMS:
                    profile = find(this.profileTmpList.mms, prf => prf.fileName === object.profileFileName);
                    break;
                  case JanusCmd.GOOSE:
                    profile = find(this.profileTmpList.goose, prf => prf.fileName === object.profileFileName);
                    break;
                  case JanusCmd.EIP:
                    profile = find(this.profileTmpList.eip, prf => prf.fileName === object.profileFileName);
                    break;
                  case JanusCmd.FINS:
                    profile = find(this.profileTmpList.fins, prf => prf.fileName === object.profileFileName);
                    break;
                  case JanusCmd.S7:
                    profile = find(this.profileTmpList.s7, prf => prf.fileName === object.profileFileName);
                    break;
                  case JanusCmd.TRDP:
                    profile = find(this.profileTmpList.trdp, prf => prf.fileName === object.profileFileName);
                    break;
                  case JanusCmd.OPCUA:
                    profile = find(this.profileTmpList.opcua, prf => prf.fileName === object.profileFileName);
                    break;
                  case JanusCmd.MELSEC:
                    profile = find(this.profileTmpList.melsec, prf => prf.fileName === object.profileFileName);
                    break;
                  case JanusCmd.S7PLUS:
                    profile = find(this.profileTmpList.s7plus, prf => prf.fileName === object.profileFileName);
                    break;
                }

                if (profile.id < gManualPrfIdBase) {
                  object.profileName = profile.name;
                } else {
                  object.profileName = this.translate.instant('features.janus.manual');
                }
              });

              this.objectTablePanelData = this.objectTableData.slice(0, this.objectTablePanelPageSize);
              this.objectTableDataSource.data = this.objectTableData;
              this.objectSelection.clear();
              this.profileTableData = [];
              forEach(this.profileTmpList.all, profile => {
                if (profile.id > gPrfIdBase && profile.id < gManualPrfIdBase) {
                  this.profileTableData.push(profile);
                }
              });
              this.profileTablePanelData = this.profileTableData.slice(0, this.profileTablePanelPageSize);
              this.profileTableDataSource.data = this.profileTableData;
              this.profileSelection.clear();
              observer.complete();
            })
            .catch(error => {
              observer.error(error);
            });
        },
        error => {
          observer.error(error);
        }
      );
    });
  }

  removeControlNotSupport(): Observable<any> {
    return new Observable(observer => {
      if (this.ipsSupport !== 1) {
        this.globalForm.removeControl('ipsEnable');
        this.globalForm.removeControl('ipsOpMode');
        this.globalForm.removeControl('idsEnable');
        this.globalForm.removeControl('idsPortListArray');
      }

      if (this.avSupport !== 1) {
        this.globalForm.removeControl('avEnable');
        this.globalForm.removeControl('avOpMode');
        this.globalForm.removeControl('avCloud');
      }

      if (this.dpSupport !== 1) {
        this.globalForm.controls.dpEnable.disable();
        this.globalForm.controls.dpOpMode.disable();
        this.globalForm.controls.dpEvtEnable.disable();
        this.globalForm.controls.dpEvtSeverity.disable();
        this.globalForm.controls.dpEvtActionList.disable();
      }

      if (this.gpSupport !== 1) {
        this.globalForm.removeControl('gpEnable');
        this.globalForm.removeControl('gpOpMode');
        this.globalForm.removeControl('gpOpMethod');
      }

      if (this.featureSupport !== 1) {
        this.globalForm.removeControl('enforcementEnable');
        this.globalForm.removeControl('action');
        this.globalForm.removeControl('debugEnable');
      }

      if (this.featureSupport !== 1 || this.protocolDisplay.indexOf('modbus') === -1) {
        this.globalForm.removeControl('modbusEnable');
        this.globalForm.removeControl('modbusAdp');
        this.globalForm.removeControl('modbusPort');
      }

      if (this.featureSupport !== 1 || this.protocolDisplay.indexOf('dnp3') === -1) {
        this.globalForm.removeControl('dnp3Enable');
        this.globalForm.removeControl('dnp3Adp');
        this.globalForm.removeControl('dnp3Port');
      }

      if (this.featureSupport !== 1 || this.protocolDisplay.indexOf('iec104') === -1) {
        this.globalForm.removeControl('iec104Enable');
        this.globalForm.removeControl('iec104Adp');
        this.globalForm.removeControl('iec104Port');
      }

      if (this.featureSupport !== 1 || this.protocolDisplay.indexOf('mms') === -1) {
        this.globalForm.removeControl('mmsEnable');
        this.globalForm.removeControl('mmsPort');
      }

      if (this.featureSupport !== 1 || this.protocolDisplay.indexOf('goose') === -1) {
        this.globalForm.removeControl('gooseEnable');
      }

      if (this.featureSupport !== 1 || this.protocolDisplay.indexOf('eip') === -1) {
        this.globalForm.removeControl('eipEnable');
        this.globalForm.removeControl('eipAdp');
        this.globalForm.removeControl('eipPort');
      }

      if (this.featureSupport !== 1 || this.protocolDisplay.indexOf('fins') === -1) {
        this.globalForm.removeControl('finsEnable');
        this.globalForm.removeControl('finsAdp');
        this.globalForm.removeControl('finsPort');
      }

      if (this.featureSupport !== 1 || this.protocolDisplay.indexOf('s7') === -1) {
        this.globalForm.removeControl('s7Enable');
        this.globalForm.removeControl('s7Adp');
        this.globalForm.removeControl('s7Port');
      }

      if (this.featureSupport !== 1 || this.protocolDisplay.indexOf('trdp') === -1) {
        this.globalForm.removeControl('trdpEnable');
        this.globalForm.removeControl('trdpAdp');
        this.globalForm.removeControl('trdpPort');
      }

      if (this.featureSupport !== 1 || this.protocolDisplay.indexOf('opcua') === -1) {
        this.globalForm.removeControl('opcuaEnable');
        this.globalForm.removeControl('opcuaAdp');
        this.globalForm.removeControl('opcuaPort');
      }

      if (this.featureSupport !== 1 || this.protocolDisplay.indexOf('melsec') === -1) {
        this.globalForm.removeControl('melsecEnable');
        this.globalForm.removeControl('melsecAdp');
        this.globalForm.removeControl('melsecPort');
      }

      if (this.featureSupport !== 1 || this.protocolDisplay.indexOf('s7plus') === -1) {
        this.globalForm.removeControl('s7plusEnable');
        this.globalForm.removeControl('s7plusAdp');
        this.globalForm.removeControl('s7plusPort');
      }

      observer.complete();
    });
  }

  /* Global Settings */
  onFileTypeChange(type: FileType): void {
    switch (type) {
      case FileType.CONFIG:
      case FileType.POLICY:
        this.bkResForm.get('fileSelection').enable();
        break;
      case FileType.DEBUG_INFO:
        this.bkResForm.get('fileSelection').disable();
        break;
    }
  }

  saveImportSelection(event): void {
    // append file name to input field
    this.bkResForm.get('fileSelection').setValue(event.target.files[0].name);
    this.localFile = event.target.files[0];
    event.target.value = '';
  }

  onBackup(): void {
    let exportCliCmdObj;
    let majorId;
    let fileName;

    switch (this.bkResForm.get('type').value) {
      case FileType.CONFIG:
        exportCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.POST_PKG_DATA,
          majorId: JanusCmd.FEATURE,
          minorId: this.minExport,
          dataVal: this.cmdCliSpInput
        });
        majorId = JanusCmd.FEATURE;
        fileName = 'JanusConf.tar.gz';
        break;
      case FileType.POLICY:
        exportCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.POST_PKG_DATA,
          majorId: JanusCmd.POLICY,
          minorId: this.minExport,
          dataVal: this.cmdCliSpInput
        });
        majorId = JanusCmd.POLICY;
        fileName = 'JanusPolicy.tar.gz';
        break;
      case FileType.DEBUG_INFO:
        exportCliCmdObj = this.pkgService.cliGenCmdObj({
          ctrlId: PkgAction.POST_PKG_DATA,
          majorId: JanusCmd.DBG,
          minorId: this.minExport,
          dataVal: this.cmdCliSpInput
        });
        majorId = JanusCmd.DBG;
        fileName = 'JanusDebug.tar.gz';
        break;
    }

    const cliCmdBody = this.pkgService.cliGenCmd([exportCliCmdObj]);
    const cliGetBody = {
      transmitData: this.pkgService.makeDataSend(PkgId.PKG_JANUS, PkgAction.POST_PKG_DATA, JSON.stringify(cliCmdBody))
    };
    this.http.post(`${environment.uriRequestURL}/pkg_web/show_web?SRV=SRV_PKG`, cliGetBody).subscribe(
      data => {
        const result = JSON.parse(this.pkgService.parsePkgData(data).body);
        const ret = result.janusCli[0][majorId][0].cmd.ret;
        if (this.pkgService.isError(ret)) {
          this.snackBar.open(
            `Export : ${this.pkgService.getError(ret)}`,
            this.translate.instant('general.button.close'),
            { panelClass: ['error'] }
          );
          return;
        }

        this.fetch.getFile(`${environment.apiHost}/pkg/Janus/Janus/web/res/${fileName}.json?no_cache=1`).then(blob => {
          const objectURL = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.download = fileName;
          link.href = objectURL;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        });

        this.angularEleService.emitLoading(false);
        this.snackBar.open(this.translate.instant('response_handler.backup_success'), '', {
          duration: 3000
        });
      },
      error => {
        this.angularEleService.emitLoading(false);
        this.errorService.handleError(error);
      }
    );
  }

  onRestore(): void {
    if (this.bkResForm.invalid) {
      this.bkResForm.markAllAsTouched();
      return;
    }
    const requests = [];
    // upload file
    const formData: FormData = new FormData();
    formData.append('binary', this.localFile);
    requests.push(this.http.post(environment.uriRequestURL + '/pkg_web/pkgUpload?SRV=SRV_PKG', formData));

    // import
    let importCliCmdObj;
    let majorId;
    if (this.bkResForm.get('type').value === FileType.CONFIG) {
      importCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.FEATURE,
        minorId: this.minImport,
        dataVal: this.cmdCliSpInput
      });
      majorId = JanusCmd.FEATURE;
    } else {
      // FileType.POLICY
      importCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.POLICY,
        minorId: this.minImport,
        dataVal: this.cmdCliSpInput
      });
      majorId = JanusCmd.POLICY;
    }
    const cliCmdBody = this.pkgService.cliGenCmd([importCliCmdObj]);
    const cliGetBody = {
      transmitData: this.pkgService.makeDataSend(PkgId.PKG_JANUS, PkgAction.POST_PKG_DATA, JSON.stringify(cliCmdBody))
    };
    requests.push(this.http.post(`${environment.uriRequestURL}/pkg_web/show_web?SRV=SRV_PKG`, cliGetBody));
    requests.push(this.refreshGlobalData());

    let ret;

    const lastSelected = this.globalForm.controls.idsPortListArray.value.map(port => {
      return `${port.port}-${port.vlan}`;
    });
    this.angularEleService.emitLoading(true);
    concat(...requests).subscribe({
      next: (data: any) => {
        if (data.body) {
          const result = JSON.parse(this.pkgService.parsePkgData(data).body);
          ret = result.janusCli[0][majorId][0].cmd.ret;
        }
      },
      complete: () => {
        if (this.pkgService.isError(+ret)) {
          this.snackBar.open(
            `Import : ${this.pkgService.getError(ret)}`,
            this.translate.instant('general.button.close'),
            { panelClass: ['error'] }
          );
        }
        setTimeout(() => {
          this.refreshObjectData().subscribe({
            complete: () => {
              // reset platform SRV_VLAN and SRV_VPLANs
              this.cacheSelectedVlan = lastSelected;
              this.onGlobalSubmit();
            },
            error: error => {
              this.angularEleService.emitLoading(false);
              this.errorService.handleError(error);
            }
          });
        }, 500);
      },
      error: error => {
        this.angularEleService.emitLoading(false);
        this.errorService.handleError(error);
      }
    });
  }

  onGlobalSubmit(): void {
    if (this.globalForm.invalid) {
      this.globalForm.markAllAsTouched();
      return;
    }

    // cliGenSetEnforcementCmd
    const bEf = this.globalForm.get('enforcementEnable').value;
    const efCliCmdObj = this.pkgService.cliGenCmdObj({
      ctrlId: PkgAction.POST_PKG_DATA,
      majorId: JanusCmd.FEATURE,
      minorId: this.minSetEnforcement,
      dataVal: ''
    });
    const efActCliCmdObj = this.pkgService.cliGenCmdObj({
      ctrlId: PkgAction.POST_PKG_DATA,
      majorId: JanusCmd.FEATURE,
      minorId: this.minEnforcementAct,
      dataVal: this.globalForm.get('action').value
    });
    const efEnCliCmdObj = this.pkgService.cliGenCmdObj({
      ctrlId: PkgAction.POST_PKG_DATA,
      majorId: JanusCmd.FEATURE,
      minorId: this.minEnforcementSt,
      dataVal: bEf ? 1 : 0
    });
    // cliSetDbgSt
    const dbgCliCmdObj = this.pkgService.cliGenCmdObj({
      ctrlId: PkgAction.POST_PKG_DATA,
      majorId: JanusCmd.DBG,
      minorId: this.minSetDbgSt,
      dataVal: this.globalForm.get('debugEnable').value ? 1 : 0
    });
    const cliCmdBodyList = [];

    if (this.ipsSupport === 1) {
      const ipsCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.IPS,
        minorId: this.minSetIpsSt,
        dataVal: this.globalForm.get('ipsEnable').value ? 1 : 0
      });
      const ipsOpCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.IPS,
        minorId: this.minSetIpsOpMode,
        dataVal: this.globalForm.get('ipsOpMode').value
      });
      const idsEnableCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.IPS,
        minorId: this.minSetIdsEnable,
        dataVal: this.globalForm.get('idsEnable').value
      });

      let idsPortValue = [];
      if (this.cacheServiceDefine.SRV_VLAN && this.cacheServiceDefine.SRV_VPLAN) {
        idsPortValue = this.globalForm.get('idsPortListArray').value.map(port => {
          return `${+port.port - 1}-${port.vlan}`;
        });
      } else {
        idsPortValue = this.globalForm.get('idsPortListArray').value.map(port => {
          return `${port.port}-${port.vlan}`;
        });
      }

      const idsPortCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.IPS,
        minorId: this.minSetPortVlan,
        dataVal: (idsPortValue || []).join(',')
      });

      cliCmdBodyList.push(ipsCliCmdObj);
      cliCmdBodyList.push(ipsOpCliCmdObj);
      cliCmdBodyList.push(idsEnableCliCmdObj);
      cliCmdBodyList.push(idsPortCliCmdObj);
    }

    if (this.avSupport === 1) {
      const avCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.AV,
        minorId: this.minSetAvSt,
        dataVal: this.globalForm.get('avEnable').value ? 1 : 0
      });
      const avOpCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.AV,
        minorId: this.minSetAvOpMode,
        dataVal: this.globalForm.get('avOpMode').value
      });
      const avCloudCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.AV,
        minorId: this.minSetAvCloud,
        dataVal: this.globalForm.get('avCloud').value && true === this.avCloudChecked ? 1 : 0
      });

      cliCmdBodyList.push(avCliCmdObj);
      cliCmdBodyList.push(avOpCliCmdObj);
      cliCmdBodyList.push(avCloudCliCmdObj);
    }

    if (this.dpSupport === 1) {
      const dpCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.DP,
        minorId: this.minSetDpSt,
        dataVal: this.globalForm.controls.dpEnable.value ? 1 : 0
      });
      const dpOpCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.DP,
        minorId: this.minSetDpOpMode,
        dataVal: this.globalForm.controls.dpOpMode.value
      });

      cliCmdBodyList.push(dpCliCmdObj);
      cliCmdBodyList.push(dpOpCliCmdObj);
    }
    if (this.gpSupport === 1) {
      const gpCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.GP,
        minorId: this.minSetGpSt,
        dataVal: this.globalForm.get('gpEnable').value ? 1 : 0
      });
      const gpOpCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.GP,
        minorId: this.minSetGpOpMode,
        dataVal: this.globalForm.get('gpOpMode').value
      });
      const gpMethodCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.GP,
        minorId: this.minSetGpOpMethod,
        dataVal: this.globalForm.get('gpOpMethod').value
      });

      cliCmdBodyList.push(gpCliCmdObj);
      cliCmdBodyList.push(gpOpCliCmdObj);
      cliCmdBodyList.push(gpMethodCliCmdObj);
    }

    if (this.protocolDisplay.indexOf('modbus') !== -1) {
      const mbCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.MB,
        minorId: this.minSetMbSt,
        dataVal: this.globalForm.get('modbusEnable').value ? 1 : 0
      });
      const mbAdpCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.MB,
        minorId: this.minAdpMbEn,
        dataVal: this.globalForm.get('modbusEnable').value ? (this.globalForm.get('modbusAdp').value ? 1 : 0) : 0
      });
      const mbPortCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.MB,
        minorId: this.minSetMbSrvPort,
        dataVal: this.globalForm.get('modbusPort').value
      });
      if (true === bEf) {
        cliCmdBodyList.push(mbCliCmdObj);
        cliCmdBodyList.push(mbAdpCliCmdObj);
        cliCmdBodyList.push(mbPortCliCmdObj);
      }
    }

    if (this.protocolDisplay.indexOf('dnp3') !== -1) {
      const dnp3CliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.DNP3,
        minorId: this.minSetDnp3St,
        dataVal: this.globalForm.get('dnp3Enable').value ? 1 : 0
      });
      const dnp3AdpCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.DNP3,
        minorId: this.minAdpDnp3En,
        dataVal: this.globalForm.get('dnp3Enable').value ? (this.globalForm.get('dnp3Adp').value ? 1 : 0) : 0
      });
      const dnp3PortCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.DNP3,
        minorId: this.minSetDnp3SrvPort,
        dataVal: this.globalForm.get('dnp3Port').value
      });
      if (true === bEf) {
        cliCmdBodyList.push(dnp3CliCmdObj);
        cliCmdBodyList.push(dnp3AdpCliCmdObj);
        cliCmdBodyList.push(dnp3PortCliCmdObj);
      }
    }

    if (this.protocolDisplay.indexOf('iec104') !== -1) {
      const iec104CliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.IEC104,
        minorId: this.minSetIec104St,
        dataVal: this.globalForm.get('iec104Enable').value ? 1 : 0
      });
      const iec104AdpCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.IEC104,
        minorId: this.minAdpIec104En,
        dataVal: this.globalForm.get('iec104Enable').value ? (this.globalForm.get('iec104Adp').value ? 1 : 0) : 0
      });
      const iec104PortCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.IEC104,
        minorId: this.minSetIec104SrvPort,
        dataVal: this.globalForm.get('iec104Port').value
      });
      if (true === bEf) {
        cliCmdBodyList.push(iec104CliCmdObj);
        cliCmdBodyList.push(iec104AdpCliCmdObj);
        cliCmdBodyList.push(iec104PortCliCmdObj);
      }
    }

    if (this.protocolDisplay.indexOf('mms') !== -1) {
      const mmsCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.MMS,
        minorId: this.minSetMmsSt,
        dataVal: this.globalForm.get('mmsEnable').value ? 1 : 0
      });
      const mmsPortCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.MMS,
        minorId: this.minSetMmsSrvPort,
        dataVal: this.globalForm.get('mmsPort').value
      });
      if (true === bEf) {
        cliCmdBodyList.push(mmsCliCmdObj);
        cliCmdBodyList.push(mmsPortCliCmdObj);
      }
    }
    if (this.protocolDisplay.indexOf('goose') !== -1) {
      const gooseCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.GOOSE,
        minorId: this.minSetGooseSt,
        dataVal: this.globalForm.get('gooseEnable').value ? 1 : 0
      });
      if (true === bEf) {
        cliCmdBodyList.push(gooseCliCmdObj);
      }
    }

    if (this.protocolDisplay.indexOf('eip') !== -1) {
      const eipCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.EIP,
        minorId: this.minSetEipSt,
        dataVal: this.globalForm.get('eipEnable').value ? 1 : 0
      });
      const eipAdpCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.EIP,
        minorId: this.minAdpEipEn,
        dataVal: this.globalForm.get('eipEnable').value ? (this.globalForm.get('eipAdp').value ? 1 : 0) : 0
      });
      const eipPortCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.EIP,
        minorId: this.minSetEipSrvPort,
        dataVal: this.globalForm.get('eipPort').value
      });
      if (true === bEf) {
        cliCmdBodyList.push(eipCliCmdObj);
        cliCmdBodyList.push(eipAdpCliCmdObj);
        cliCmdBodyList.push(eipPortCliCmdObj);
      }
    }

    if (this.protocolDisplay.indexOf('fins') !== -1) {
      const finsCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.FINS,
        minorId: this.minSetFinsSt,
        dataVal: this.globalForm.get('finsEnable').value ? 1 : 0
      });
      const finsAdpCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.FINS,
        minorId: this.minAdpFinsEn,
        dataVal: this.globalForm.get('finsEnable').value ? (this.globalForm.get('finsAdp').value ? 1 : 0) : 0
      });
      const finsPortCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.FINS,
        minorId: this.minSetFinsSrvPort,
        dataVal: this.globalForm.get('finsPort').value
      });
      if (true === bEf) {
        cliCmdBodyList.push(finsCliCmdObj);
        cliCmdBodyList.push(finsAdpCliCmdObj);
        cliCmdBodyList.push(finsPortCliCmdObj);
      }
    }

    if (this.protocolDisplay.indexOf('s7') !== -1) {
      const s7CliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.S7,
        minorId: this.minSetS7St,
        dataVal: this.globalForm.get('s7Enable').value ? 1 : 0
      });
      const s7AdpCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.S7,
        minorId: this.minAdpS7En,
        dataVal: this.globalForm.get('s7Enable').value ? (this.globalForm.get('s7Adp').value ? 1 : 0) : 0
      });
      const s7PortCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.S7,
        minorId: this.minSetS7SrvPort,
        dataVal: this.globalForm.get('s7Port').value
      });
      if (true === bEf) {
        cliCmdBodyList.push(s7CliCmdObj);
        cliCmdBodyList.push(s7AdpCliCmdObj);
        cliCmdBodyList.push(s7PortCliCmdObj);
      }
    }

    if (this.protocolDisplay.indexOf('trdp') !== -1) {
      const trdpCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.TRDP,
        minorId: this.minSetTrdpSt,
        dataVal: this.globalForm.get('trdpEnable').value ? 1 : 0
      });
      const trdpPortCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.TRDP,
        minorId: this.minSetTrdpSrvPort,
        dataVal: this.globalForm.get('trdpPort').value
      });
      if (true === bEf) {
        cliCmdBodyList.push(trdpCliCmdObj);
        cliCmdBodyList.push(trdpPortCliCmdObj);
      }
    }

    if (this.protocolDisplay.indexOf('opcua') !== -1) {
      const opcuaCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.OPCUA,
        minorId: this.minSetOpcuaSt,
        dataVal: this.globalForm.get('opcuaEnable').value ? 1 : 0
      });
      const opcuaAdpCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.OPCUA,
        minorId: this.minAdpOpcuaEn,
        dataVal: this.globalForm.get('opcuaEnable').value ? (this.globalForm.get('opcuaAdp').value ? 1 : 0) : 0
      });
      const opcuaPortCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.OPCUA,
        minorId: this.minSetOpcuaSrvPort,
        dataVal: this.globalForm.get('opcuaPort').value
      });
      if (true === bEf) {
        cliCmdBodyList.push(opcuaCliCmdObj);
        cliCmdBodyList.push(opcuaAdpCliCmdObj);
        cliCmdBodyList.push(opcuaPortCliCmdObj);
      }
    }

    if (this.protocolDisplay.indexOf('melsec') !== -1) {
      const melsecCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.MELSEC,
        minorId: this.minSetMelsecSt,
        dataVal: this.globalForm.get('melsecEnable').value ? 1 : 0
      });
      const melsecAdpCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.MELSEC,
        minorId: this.minAdpMelsecEn,
        dataVal: this.globalForm.get('melsecEnable').value ? (this.globalForm.get('melsecAdp').value ? 1 : 0) : 0
      });
      const melsecPortCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.MELSEC,
        minorId: this.minSetMelsecSrvPort,
        dataVal: this.globalForm.get('melsecPort').value
      });
      if (true === bEf) {
        cliCmdBodyList.push(melsecCliCmdObj);
        cliCmdBodyList.push(melsecAdpCliCmdObj);
        cliCmdBodyList.push(melsecPortCliCmdObj);
      }
    }

    if (this.protocolDisplay.indexOf('s7plus') !== -1) {
      const s7plusCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.S7PLUS,
        minorId: this.minSetS7PlusSt,
        dataVal: this.globalForm.get('s7plusEnable').value ? 1 : 0
      });
      const s7plusAdpCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.S7PLUS,
        minorId: this.minAdpS7PlusEn,
        dataVal: this.globalForm.get('s7plusEnable').value ? (this.globalForm.get('s7plusAdp').value ? 1 : 0) : 0
      });
      const s7plusPortCliCmdObj = this.pkgService.cliGenCmdObj({
        ctrlId: PkgAction.POST_PKG_DATA,
        majorId: JanusCmd.S7PLUS,
        minorId: this.minSetS7PlusSrvPort,
        dataVal: this.globalForm.get('s7plusPort').value
      });
      if (true === bEf) {
        cliCmdBodyList.push(s7plusCliCmdObj);
        cliCmdBodyList.push(s7plusAdpCliCmdObj);
        cliCmdBodyList.push(s7plusPortCliCmdObj);
      }
    }
    cliCmdBodyList.push(efCliCmdObj);
    cliCmdBodyList.push(efActCliCmdObj);
    cliCmdBodyList.push(efEnCliCmdObj);
    cliCmdBodyList.push(dbgCliCmdObj);

    const cliCmdBody = this.pkgService.cliGenCmd(cliCmdBodyList);
    const cliGetBody = {
      transmitData: this.pkgService.makeDataSend(PkgId.PKG_JANUS, PkgAction.GET_PKG_DATA, JSON.stringify(cliCmdBody))
    };

    const request$ = this.http.post(`${environment.uriRequestURL}/pkg_web/show_web?SRV=SRV_PKG`, cliGetBody).pipe(
      switchMap(cliGetBody => {
        // 解析結果
        const result = JSON.parse(this.pkgService.parsePkgData(cliGetBody).body);
        const error = find(result.janusCli, obj => {
          const majorId = Object.keys(obj)[0];
          if (this.pkgService.isError(obj[majorId][0].cmd.ret)) {
            this.snackBar.open(
              `Operation Error: ${this.pkgService.getError(obj[majorId][0].cmd.ret)}`,
              this.translate.instant('general.button.close'),
              { panelClass: ['error'] }
            );
            return true;
          }
          return false;
        });

        if (error) {
          return of(null);
        }

        this.snackBar.open(this.translate.instant('response_handler.res_global_success'), '', { duration: 3000 });
        return of(result);
      }),
      catchError(error => {
        this.angularEleService.emitLoading(false);
        this.errorService.handleError(error);
        return of(null);
      })
    );
    const eventLog$ = this.eventLogSupport
      ? of({
          dpi: {
            evtEnable: this.globalForm.get('dpiEvtEnable').value,
            evtSeverity: this.globalForm.get('dpiEvtSeverity').value,
            logTrap: this.globalForm.get('dpiEvtActionList').value.includes(LogDestination.TRAP) ? 1 : 0,
            logSyslog: this.globalForm.get('dpiEvtActionList').value.includes(LogDestination.SYSLOG) ? 1 : 0,
            logFlash: this.globalForm.get('dpiEvtActionList').value.includes(LogDestination.LOCAL_STORAGE) ? 1 : 0
          },
          ips: {
            evtEnable: this.globalForm.get('ipsEvtEnable').value,
            evtSeverity: this.globalForm.get('ipsEvtSeverity').value,
            logTrap: this.globalForm.get('ipsEvtActionList').value.includes(LogDestination.TRAP) ? 1 : 0,
            logSyslog: this.globalForm.get('ipsEvtActionList').value.includes(LogDestination.SYSLOG) ? 1 : 0,
            logFlash: this.globalForm.get('ipsEvtActionList').value.includes(LogDestination.LOCAL_STORAGE) ? 1 : 0
          },
          domainProtect: {
            evtEnable: this.globalForm.controls.dpEvtEnable.value,
            evtSeverity: this.globalForm.controls.dpEvtSeverity.value,
            logTrap: this.globalForm.controls.dpEvtActionList.value.includes(LogDestination.TRAP) ? 1 : 0,
            logSyslog: this.globalForm.controls.dpEvtActionList.value.includes(LogDestination.SYSLOG) ? 1 : 0,
            logFlash: this.globalForm.controls.dpEvtActionList.value.includes(LogDestination.LOCAL_STORAGE) ? 1 : 0
          }
        }).pipe(
          switchMap((eventResponse: EventSettingModel) => this.configurationService.postEvtSetting(eventResponse)),
          catchError(error => {
            this.angularEleService.emitLoading(false);
            this.errorService.handleError(error);
            return of(null);
          })
        )
      : of(null);

    if (this.ipsSupport && this.cacheServiceDefine.SRV_VLAN && this.cacheServiceDefine.SRV_VPLAN) {
      const params = this.createSRVParam();
      const vlanConfig$ = this.configurationService.postVLANConfig(params).pipe(
        catchError(error => {
          this.angularEleService.emitLoading(false);
          this.errorService.handleError(error);
          return of(null);
        })
      );

      this.angularEleService.emitLoading(true);
      forkJoin([request$, eventLog$, vlanConfig$]).subscribe(() => {
        this.angularEleService.emitLoading(false);
        this.initialGlobalData();
      });
    } else {
      this.angularEleService.emitLoading(true);
      forkJoin([request$, eventLog$]).subscribe(() => {
        this.angularEleService.emitLoading(false);
        this.initialGlobalData();
      });
    }
  }

  avCloudToggle(): void {
    if (false === this.avCloudChecked) {
      this.avCloudChecked = true;
    } else {
      this.avCloudChecked = false;
    }
  }

  /* Object Settings */
  isAllObjectSelected(): boolean {
    const numSelected = this.objectSelection.selected.length;
    const numRows = this.objectTableDataSource.data.length;
    return numSelected === numRows;
  }

  objectMasterToggle(): void {
    this.isAllObjectSelected()
      ? this.objectSelection.clear()
      : this.objectTableDataSource.data.forEach(row => this.objectSelection.select(row));
  }

  updateObjectFilter(filterValue: string): void {
    this.objectTableDataSource.filter = filterValue.trim().toLowerCase();
  }

  createTou(): void {
    this.dialog.open(TouDialogComponent, {
      autoFocus: false
    });
  }

  createObject(): void {
    mediumDialogConfig.data = {
      categoryList: this.categoryList,
      profileTmpList: this.profileTmpList,
      objList: this.objectTableData,
      mmsDef: this.mmsDef,
      gooseDef: this.gooseDef,
      dnp3Def: this.dnp3Def,
      iec104Def: this.iec104Def,
      trdpDef: this.trdpDef
    };
    const dialogRef = this.dialog.open(ObjectSettingDialogComponent, mediumDialogConfig);

    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        setTimeout(() => {
          this.refreshObjectData().subscribe({
            complete: () => {
              this.angularEleService.emitLoading(false);
            },
            error: error => {
              this.angularEleService.emitLoading(false);
              this.errorService.handleError(error);
            }
          });
        }, 500);
      }
    });
  }

  editObject(row): void {
    mediumDialogConfig.data = {
      row: row,
      categoryList: this.categoryList,
      profileTmpList: this.profileTmpList,
      objList: this.objectTableData,
      mmsDef: this.mmsDef,
      gooseDef: this.gooseDef,
      dnp3Def: this.dnp3Def,
      iec104Def: this.iec104Def,
      trdpDef: this.trdpDef
    };
    const dialogRef = this.dialog.open(ObjectSettingDialogComponent, mediumDialogConfig);

    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        this.refreshObjectData().subscribe({
          complete: () => {
            this.angularEleService.emitLoading(false);
          },
          error: error => {
            this.angularEleService.emitLoading(false);
            this.errorService.handleError(error);
          }
        });
      }
    });
  }

  deleteObject(row?): void {
    smallDialogConfig.data = {
      selected: row ? [row] : this.objectSelection.selected,
      profileTmpList: this.profileTmpList
    };
    const dialogRef = this.dialog.open(ObjectDeleteDialogComponent, smallDialogConfig);

    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        this.refreshObjectData().subscribe({
          complete: () => {
            this.angularEleService.emitLoading(false);
          },
          error: error => {
            this.angularEleService.emitLoading(false);
            this.errorService.handleError(error);
          }
        });
      }
    });
  }

  onObjectTablePageChange(event): void {
    this.objectTablePanelData = this.objectTableData.slice(
      this.objectTablePanelPageSize * event.pageIndex,
      this.objectTablePanelPageSize * event.pageIndex + this.objectTablePanelPageSize
    );
  }

  /* Profile Settings */
  isAllProfileSelected(): boolean {
    const numSelected = this.profileSelection.selected.length;
    const numRows = this.profileTableDataSource.data.length;
    return numSelected === numRows;
  }

  profileMasterToggle(): void {
    this.isAllProfileSelected()
      ? this.profileSelection.clear()
      : this.profileTableDataSource.data.forEach(row => this.profileSelection.select(row));
  }

  updateProfileFilter(filterValue: string): void {
    this.profileTableDataSource.filter = filterValue.trim().toLowerCase();
  }

  createProfile(): void {
    mediumDialogConfig.data = {
      categoryList: this.categoryList,
      profileTmpList: this.profileTmpList,
      mmsDef: this.mmsDef,
      gooseDef: this.gooseDef,
      dnp3Def: this.dnp3Def,
      iec104Def: this.iec104Def,
      trdpDef: this.trdpDef
    };
    const dialogRef = this.dialog.open(ProfileSettingDialogComponent, mediumDialogConfig);

    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        setTimeout(() => {
          this.refreshObjectData().subscribe({
            complete: () => {
              this.angularEleService.emitLoading(false);
            },
            error: error => {
              this.angularEleService.emitLoading(false);
              this.errorService.handleError(error);
            }
          });
        }, 500);
      }
    });
  }

  editProfile(row): void {
    mediumDialogConfig.data = {
      row: row,
      categoryList: this.categoryList,
      profileTmpList: this.profileTmpList,
      mmsDef: this.mmsDef,
      gooseDef: this.gooseDef,
      dnp3Def: this.dnp3Def,
      iec104Def: this.iec104Def,
      trdpDef: this.trdpDef
    };
    const dialogRef = this.dialog.open(ProfileSettingDialogComponent, mediumDialogConfig);

    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        this.refreshObjectData().subscribe({
          complete: () => {
            this.angularEleService.emitLoading(false);
          },
          error: error => {
            this.angularEleService.emitLoading(false);
            this.errorService.handleError(error);
          }
        });
      }
    });
  }

  deleteProfile(row?): void {
    smallDialogConfig.data = {
      selected: row ? [row] : this.profileSelection.selected,
      profileTmpList: this.profileTmpList
    };
    const dialogRef = this.dialog.open(ProfileDeleteDialogComponent, smallDialogConfig);

    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        this.refreshObjectData().subscribe({
          complete: () => {
            this.angularEleService.emitLoading(false);
          },
          error: error => {
            this.angularEleService.emitLoading(false);
            this.errorService.handleError(error);
          }
        });
      }
    });
  }

  onProfileTablePageChange(event): void {
    this.profileTablePanelData = this.profileTableData.slice(
      this.profileTablePanelPageSize * event.pageIndex,
      this.profileTablePanelPageSize * event.pageIndex + this.profileTablePanelPageSize
    );
  }

  private setupTableSetting(): void {
    this.objectTableDataSource.sort = this.objectTableSort;
    this.objectTableDataSource.paginator = this.objectTablePaginator;
    this.objectTableDataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'janus-object-name':
          return item.name;
        case 'janus-category':
          return item.categoryText;
        case 'janus-profile-name':
          return item.profileName;
        default:
          return item[property];
      }
    };
    this.objectTableDataSource.filterPredicate = (data: any, filter: string): boolean => {
      return (
        String(data.name).toLowerCase().indexOf(filter) !== -1 ||
        String(data.categoryText).toLowerCase().indexOf(filter) !== -1 ||
        String(data.profileName).toLowerCase().indexOf(filter) !== -1
      );
    };

    this.profileTableDataSource.sort = this.profileTableSort;
    this.profileTableDataSource.paginator = this.profileTablePaginator;
    this.profileTableDataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'janus-category':
          return item.categoryText;
        case 'janus-profile-name':
          return item.name;
        default:
          return item[property];
      }
    };
    this.profileTableDataSource.filterPredicate = (data: any, filter: string): boolean => {
      return (
        String(data.name).toLowerCase().indexOf(filter) !== -1 ||
        String(data.categoryText).toLowerCase().indexOf(filter) !== -1
      );
    };
  }

  private getTmpJsonFile(requests: CategoryDataType, fileNameList: CategoryDataType): Promise<any> {
    this.profileTmpList.modbus = [];
    this.profileTmpList.dnp3 = [];
    this.profileTmpList.iec104 = [];
    this.profileTmpList.mms = [];
    this.profileTmpList.goose = [];
    this.profileTmpList.eip = [];
    this.profileTmpList.fins = [];
    this.profileTmpList.s7 = [];
    this.profileTmpList.trdp = [];
    this.profileTmpList.opcua = [];
    this.profileTmpList.melsec = [];
    this.profileTmpList.s7plus = [];
    this.profileTmpList.all = [];

    const modbusPromise = Promise.all(requests.modbus).then(data => {
      forEach(data, (json: any, jsonIndex) => {
        const row = json.janusTemplate;
        this.profileTmpList.modbus.push({
          id: row.index,
          name: row.name,
          category: JanusCmd.MB,
          fileName: fileNameList.modbus[jsonIndex],
          functionCode: row.func,
          andMsk: row['and-mask'],
          orMsk: row['or-mask'],
          fileNumber: row['file-number'],
          meiTp: +row['mei-type'],
          rdDevIdCd: +row['read-dev-id-code'],
          confmtylv: +row['conformity-lv'],
          objIdList: row['obj-id-list']
        });
        this.profileTmpList.all.push({
          id: row.index,
          name: row.name,
          category: JanusCmd.MB,
          categoryText: this.translate.instant('features.janus.modbus_tcp'),
          fileName: fileNameList.modbus[jsonIndex]
        });
      });
    });
    const dnp3Promise = Promise.all(requests.dnp3).then(data => {
      forEach(data, (json: any, jsonIndex) => {
        const row = json.janusTemplate;
        this.profileTmpList.dnp3.push({
          id: row.index,
          name: row.name,
          category: JanusCmd.DNP3,
          fileName: fileNameList.dnp3[jsonIndex],
          srcAddress: row.saddr,
          destAddress: row.daddr,
          appFuncCode: +row.appFc,
          group: row.gg,
          variation: row.vv
        });
        this.profileTmpList.all.push({
          id: row.index,
          name: row.name,
          category: JanusCmd.DNP3,
          categoryText: this.translate.instant('features.janus.dnp3'),
          fileName: fileNameList.dnp3[jsonIndex]
        });
      });
    });
    const iec104Promise = Promise.all(requests.iec104).then(data => {
      forEach(data, (json: any, jsonIndex) => {
        const row = json.janusTemplate;
        this.profileTmpList.iec104.push({
          id: row.index,
          name: row.name,
          category: JanusCmd.IEC104,
          fileName: fileNameList.iec104[jsonIndex],
          causeOfTrans: row.cot,
          typeIden: row.typeIden,
          originatorAddress: row.oriAddr,
          commonAddress: row.addr
        });
        this.profileTmpList.all.push({
          id: row.index,
          name: row.name,
          category: JanusCmd.IEC104,
          categoryText: this.translate.instant('features.janus.iec104'),
          fileName: fileNameList.iec104[jsonIndex]
        });
      });
    });
    const mmsPromise = Promise.all(requests.mms).then(data => {
      forEach(data, (json: any, jsonIndex) => {
        const row = json.janusTemplate;
        this.profileTmpList.mms.push({
          id: row.index,
          name: row.name,
          category: JanusCmd.MMS,
          fileName: fileNameList.mms[jsonIndex],
          cmd: +row.cmd,
          srvConfirmReq: typeof row['srv-confirm-req'] === 'string' ? row['srv-confirm-req'] : null,
          srvConfirmResp: typeof row['srv-confirm-resp'] === 'string' ? row['srv-confirm-resp'] : null,
          srvUnconfirm: typeof row['srv-unconfirm'] === 'string' ? row['srv-unconfirm'] : null
        });
        this.profileTmpList.all.push({
          id: row.index,
          name: row.name,
          category: JanusCmd.MMS,
          categoryText: this.translate.instant('features.janus.mms'),
          fileName: fileNameList.mms[jsonIndex]
        });
      });
    });
    const goosePromise = Promise.all(requests.goose).then(data => {
      forEach(data, (json: any, jsonIndex) => {
        const row = json.janusTemplate;
        this.profileTmpList.goose.push({
          id: row.index,
          name: row.name,
          category: JanusCmd.GOOSE,
          fileName: fileNameList.goose[jsonIndex],
          cmd: +row.cmd
        });
        this.profileTmpList.all.push({
          id: row.index,
          name: row.name,
          category: JanusCmd.GOOSE,
          categoryText: this.translate.instant('features.janus.goose'),
          fileName: fileNameList.goose[jsonIndex]
        });
      });
    });
    const eipPromise = Promise.all(requests.eip).then(data => {
      forEach(data, (json: any, jsonIndex) => {
        const row = json.janusTemplate;
        this.profileTmpList.eip.push({
          id: row.index,
          name: row.name,
          category: JanusCmd.EIP,
          fileName: fileNameList.eip[jsonIndex],
          cmd: row['cmd'] ? row['cmd'] : null,
          typeId: row['type-id'] ? row['type-id'] : null,
          devType: row['dev-type'] ? row['dev-type'] : null
        });
        this.profileTmpList.all.push({
          id: row.index,
          name: row.name,
          category: JanusCmd.EIP,
          categoryText: this.translate.instant('features.janus.eip'),
          fileName: fileNameList.eip[jsonIndex]
        });
      });
    });
    const finsPromise = Promise.all(requests.fins).then(data => {
      forEach(data, (json: any, jsonIndex) => {
        const row = json.janusTemplate;
        this.profileTmpList.fins.push({
          id: row.index,
          name: row.name,
          category: JanusCmd.FINS,
          fileName: fileNameList.fins[jsonIndex],
          tcpCmd: row['tcp-cmd'] ? row['tcp-cmd'] : null,
          cmdCode: row['cmd-code'] ? row['cmd-code'] : null,
          errCode: row['error-code'] ? row['error-code'] : null
        });
        this.profileTmpList.all.push({
          id: row.index,
          name: row.name,
          category: JanusCmd.FINS,
          categoryText: this.translate.instant('features.janus.fins'),
          fileName: fileNameList.fins[jsonIndex]
        });
      });
    });
    const s7Promise = Promise.all(requests.s7).then(data => {
      forEach(data, (json: any, jsonIndex) => {
        const row = json.janusTemplate;
        this.profileTmpList.s7.push({
          id: row.index,
          name: row.name,
          category: JanusCmd.S7,
          fileName: fileNameList.s7[jsonIndex],
          rosctr: row['rosctr'] ? row['rosctr'] : null,
          func: row['func'] ? row['func'] : null,
          funcgroup: row['funcgroup'] ? row['funcgroup'] : null,
          subfunc: row['subfunc'] ? row['subfunc'] : null
        });
        this.profileTmpList.all.push({
          id: row.index,
          name: row.name,
          category: JanusCmd.S7,
          categoryText: this.translate.instant('features.janus.s7'),
          fileName: fileNameList.s7[jsonIndex]
        });
      });
    });
    const trdpPromise = Promise.all(requests.trdp).then(data => {
      forEach(data, (json: any, jsonIndex) => {
        const row = json.janusTemplate;
        this.profileTmpList.trdp.push({
          id: row.index,
          name: row.name,
          category: JanusCmd.TRDP,
          fileName: fileNameList.trdp[jsonIndex],
          protoVer: row['proto-ver'] ? row['proto-ver'] : null,
          msgType: row['msg-type'] ? row['msg-type'] : null,
          comId: row['com-id'] ? row['com-id'] : null
        });
        this.profileTmpList.all.push({
          id: row.index,
          name: row.name,
          category: JanusCmd.TRDP,
          categoryText: this.translate.instant('features.janus.trdp'),
          fileName: fileNameList.trdp[jsonIndex]
        });
      });
    });
    const opcuaPromise = Promise.all(requests.opcua).then(data => {
      forEach(data, (json: any, jsonIndex) => {
        const row = json.janusTemplate;
        this.profileTmpList.opcua.push({
          id: row.index,
          name: row.name,
          category: JanusCmd.OPCUA,
          fileName: fileNameList.opcua[jsonIndex],
          srvId: row['service-id'] ? row['service-id'] : null,
          msgType: row['msg-type'] ? row['msg-type'] : null
        });
        this.profileTmpList.all.push({
          id: row.index,
          name: row.name,
          category: JanusCmd.OPCUA,
          categoryText: this.translate.instant('features.janus.opcua'),
          fileName: fileNameList.opcua[jsonIndex]
        });
      });
    });
    const melsecPromise = Promise.all(requests.melsec).then(data => {
      forEach(data, (json: any, jsonIndex) => {
        const row = json.janusTemplate;
        this.profileTmpList.melsec.push({
          id: row.index,
          name: row.name,
          category: JanusCmd.MELSEC,
          fileName: fileNameList.melsec[jsonIndex],
          cmd: row['cmd'] ? row['cmd'] : null,
          subCmd: row['sub-cmd'] ? row['sub-cmd'] : null
        });
        this.profileTmpList.all.push({
          id: row.index,
          name: row.name,
          category: JanusCmd.MELSEC,
          categoryText: this.translate.instant('features.janus.melsec'),
          fileName: fileNameList.melsec[jsonIndex]
        });
      });
    });
    const s7plusPromise = Promise.all(requests.s7plus).then(data => {
      forEach(data, (json: any, jsonIndex) => {
        const row = json.janusTemplate;
        this.profileTmpList.s7plus.push({
          id: row.index,
          name: row.name,
          category: JanusCmd.S7PLUS,
          fileName: fileNameList.s7plus[jsonIndex],
          func: row['func'] ? row['func'] : null,
          subscrbObjId: row['subscript-obj-id'] ? row['subscript-obj-id'] : null
        });
        this.profileTmpList.all.push({
          id: row.index,
          name: row.name,
          category: JanusCmd.S7PLUS,
          categoryText: this.translate.instant('features.janus.s7plus'),
          fileName: fileNameList.s7plus[jsonIndex]
        });
      });
    });

    return Promise.all([
      modbusPromise,
      dnp3Promise,
      mmsPromise,
      iec104Promise,
      goosePromise,
      eipPromise,
      finsPromise,
      s7Promise,
      trdpPromise,
      opcuaPromise,
      melsecPromise,
      s7plusPromise
    ]);
  }

  private getObjJsonFile(requests: CategoryDataType, fileNameList: CategoryDataType): Promise<any> {
    this.objectTableData = [];

    const modbusOPromise = Promise.all(requests.modbus).then(data => {
      forEach(data, (json: any, jsonIndex) => {
        const row = json.janusAppObj;
        const object: ModbusObjType = {
          id: row.index,
          name: row.name,
          category: JanusCmd.MB,
          categoryText: this.translate.instant('features.janus.modbus_tcp'),
          profileName: '',
          profileFileName: row.template,
          fileName: fileNameList.modbus[jsonIndex],
          slaveId: row['slave-id'],
          plcAddressBase1: +row.base,
          operationCtrl: +row['operation-control'],
          rangeStart: row['data-access-range-start'],
          rangeEnd: row['data-access-range-end'],
          regValueAddr: row['control-data-address-register'],
          coilValueAddr: row['control-data-address-coil'],
          regValue: row['control-data-value-register'],
          coilValue: row['control-data-value-coil']
        };
        this.objectTableData.push(object);
      });
    });
    const dnp3OPromise = Promise.all(requests.dnp3).then(data => {
      forEach(data, (json: any, jsonIndex) => {
        const row = json.janusAppObj;
        const object: Dnp3ObjType = {
          id: row.index,
          name: row.name,
          category: JanusCmd.DNP3,
          categoryText: this.translate.instant('features.janus.dnp3'),
          profileName: '',
          profileFileName: row.template,
          fileName: fileNameList.dnp3[jsonIndex]
        };
        this.objectTableData.push(object);
      });
    });
    const iec104OPromise = Promise.all(requests.iec104).then(data => {
      forEach(data, (json: any, jsonIndex) => {
        const row = json.janusAppObj;
        const object: Iec104ObjType = {
          id: row.index,
          name: row.name,
          category: JanusCmd.IEC104,
          categoryText: this.translate.instant('features.janus.iec104'),
          profileName: '',
          profileFileName: row.template,
          fileName: fileNameList.iec104[jsonIndex]
        };
        this.objectTableData.push(object);
      });
    });
    const mmsOPromise = Promise.all(requests.mms).then(data => {
      forEach(data, (json: any, jsonIndex) => {
        const row = json.janusAppObj;
        const object: MmsObjType = {
          id: row.index,
          name: row.name,
          category: JanusCmd.MMS,
          categoryText: this.translate.instant('features.janus.mms'),
          profileName: '',
          profileFileName: row.template,
          fileName: fileNameList.mms[jsonIndex],
          data01: row['data-01'] ? row['data-01'] : null,
          data02: row['data-02'] ? row['data-02'] : null,
          data03: row['data-03'] ? row['data-03'] : null,
          data04: row['data-04'] ? row['data-04'] : null,
          data05: row['data-05'] ? row['data-05'] : null,
          data06: row['data-06'] ? row['data-06'] : null,
          data07: row['data-07'] ? row['data-07'] : null,
          data08: row['data-08'] ? row['data-08'] : null,
          dataDev: row.dev ? row.dev : null,
          dataItemId: row['item-id'] ? row['item-id'] : null
        };
        this.objectTableData.push(object);
      });
    });
    const gooseOPromise = Promise.all(requests.goose).then(data => {
      forEach(data, (json: any, jsonIndex) => {
        const row = json.janusAppObj;
        const object: GooseObjType = {
          id: row.index,
          name: row.name,
          category: JanusCmd.GOOSE,
          categoryText: this.translate.instant('features.janus.goose'),
          profileName: '',
          profileFileName: row.template,
          fileName: fileNameList.goose[jsonIndex],
          data01: row['data-01'] ? row['data-01'] : null,
          data02: row['data-02'] ? row['data-02'] : null,
          data03: row['data-03'] ? row['data-03'] : null,
          data04: row['data-04'] ? row['data-04'] : null,
          data05: row['data-05'] ? row['data-05'] : null,
          data06: row['data-06'] ? row['data-06'] : null,
          data07: row['data-07'] ? row['data-07'] : null,
          data08: row['data-08'] ? row['data-08'] : null,
          appId: row['app-id'] ? row['app-id'] : null
        };
        this.objectTableData.push(object);
      });
    });
    const eipOPromise = Promise.all(requests.eip).then(data => {
      forEach(data, (json: any, jsonIndex) => {
        const row = json.janusAppObj;
        const object: EipObjType = {
          id: row.index,
          name: row.name,
          category: JanusCmd.EIP,
          categoryText: this.translate.instant('features.janus.eip'),
          profileName: '',
          profileFileName: row.template,
          fileName: fileNameList.eip[jsonIndex],
          vendorId: row['vendor-id'] ? row['vendor-id'] : null
        };
        this.objectTableData.push(object);
      });
    });
    const finsOPromise = Promise.all(requests.fins).then(data => {
      forEach(data, (json: any, jsonIndex) => {
        const row = json.janusAppObj;
        const object: FinsObjType = {
          id: row.index,
          name: row.name,
          category: JanusCmd.FINS,
          categoryText: this.translate.instant('features.janus.fins'),
          profileName: '',
          profileFileName: row.template,
          fileName: fileNameList.fins[jsonIndex],
          addrClientNode: row['addr-client-node'] ? row['addr-client-node'] : null,
          addrSrvNode: row['addr-srv-node'] ? row['addr-srv-node'] : null,
          filePos: row['file-position'] ? row['file-position'] : null,
          filePosBegin: row['file-position-begin'] ? row['file-position-begin'] : null,
          addrBegin: row['addr-begin'] ? row['addr-begin'] : null,
          recordBegin: row['record-begin'] ? row['record-begin'] : null
        };
        this.objectTableData.push(object);
      });
    });
    const s7OPromise = Promise.all(requests.s7).then(data => {
      forEach(data, (json: any, jsonIndex) => {
        const row = json.janusAppObj;
        const object: S7ObjType = {
          id: row.index,
          name: row.name,
          category: JanusCmd.S7,
          categoryText: this.translate.instant('features.janus.s7'),
          profileName: '',
          profileFileName: row.template,
          fileName: fileNameList.s7[jsonIndex]
        };
        this.objectTableData.push(object);
      });
    });
    const trdpOPromise = Promise.all(requests.trdp).then(data => {
      forEach(data, (json: any, jsonIndex) => {
        const row = json.janusAppObj;
        const object: TrdpObjType = {
          id: row.index,
          name: row.name,
          category: JanusCmd.TRDP,
          categoryText: this.translate.instant('features.janus.trdp'),
          profileName: '',
          profileFileName: row.template,
          fileName: fileNameList.trdp[jsonIndex]
        };
        this.objectTableData.push(object);
      });
    });
    const opcuaOPromise = Promise.all(requests.opcua).then(data => {
      forEach(data, (json: any, jsonIndex) => {
        const row = json.janusAppObj;
        const object: OpcuaObjType = {
          id: row.index,
          name: row.name,
          category: JanusCmd.OPCUA,
          categoryText: this.translate.instant('features.janus.opcua'),
          profileName: '',
          profileFileName: row.template,
          fileName: fileNameList.opcua[jsonIndex]
        };
        this.objectTableData.push(object);
      });
    });
    const melsecOPromise = Promise.all(requests.melsec).then(data => {
      forEach(data, (json: any, jsonIndex) => {
        const row = json.janusAppObj;
        const object: MelsecObjType = {
          id: row.index,
          name: row.name,
          category: JanusCmd.MELSEC,
          categoryText: this.translate.instant('features.janus.melsec'),
          profileName: '',
          profileFileName: row.template,
          fileName: fileNameList.melsec[jsonIndex]
        };
        this.objectTableData.push(object);
      });
    });
    const s7plusOPromise = Promise.all(requests.s7plus).then(data => {
      forEach(data, (json: any, jsonIndex) => {
        const row = json.janusAppObj;
        const object: S7PlusObjType = {
          id: row.index,
          name: row.name,
          category: JanusCmd.S7PLUS,
          categoryText: this.translate.instant('features.janus.s7plus'),
          profileName: '',
          profileFileName: row.template,
          fileName: fileNameList.s7plus[jsonIndex]
        };
        this.objectTableData.push(object);
      });
    });

    return Promise.all([
      modbusOPromise,
      dnp3OPromise,
      mmsOPromise,
      iec104OPromise,
      gooseOPromise,
      eipOPromise,
      finsOPromise,
      s7OPromise,
      trdpOPromise,
      opcuaOPromise,
      melsecOPromise,
      s7plusOPromise
    ]);
  }

  disableProtocolADP(protocol): boolean {
    const protocolEnable = protocol + 'Enable';

    return !this.globalForm.get(protocolEnable).value;
  }

  disableAvCloud(): boolean {
    if (true === this.avCloudChecked) {
      this.globalForm.get('avCloud').enable();
    } else {
      this.globalForm.get('avCloud').disable();
    }

    return this.avCloudChecked;
  }

  addIdsPort(): void {
    const canUsePort = this.idsPortArrayOptionList.filter(item =>
      this.idsPortListArray.value.every(port => port.port !== item.port)
    );
    const pkgUsedVlan = this.idsPortListArray.value.map(port => +port.vlan);
    const platformUsed = this.cachePlatformVlan.map(vlan => +vlan.vlanid);

    const usedVlan = pkgUsedVlan.concat(platformUsed);

    let startVlanId = 1001;
    while (usedVlan.includes(startVlanId)) {
      startVlanId++;
    }

    if (this.cacheServiceDefine.SRV_VLAN && this.cacheServiceDefine.SRV_VPLAN) {
      this.idsPortListArray.push(
        new FormGroup({
          port: new FormControl(canUsePort[0].port),
          vlan: new FormControl(startVlanId, [
            Validators.required,
            Validators.max(this.vlanRange.max),
            Validators.min(this.vlanRange.min)
          ])
        })
      );
    } else {
      this.idsPortListArray.push(
        new FormGroup({
          port: new FormControl(canUsePort[0].port),
          vlan: new FormControl(1, [
            Validators.required,
            Validators.max(this.vlanRange.max),
            Validators.min(this.vlanRange.min)
          ])
        })
      );
    }

    this.idsPortListArray.controls.sort((a, b) => {
      return a.value.port - b.value.port;
    });
  }

  deleteIdsPort(index: number): void {
    this.idsPortListArray.removeAt(index);
  }

  /**
   * 不能有重複的 port
   */
  private uniquePortValidator(): ValidatorFn {
    return (array: FormArray): ValidationErrors | null => {
      // 收集所有的 port 值及其索引
      const portMap = new Map<number, number[]>();
      array.controls.forEach((control, index) => {
        const port = control.get('port')?.value;
        if (port) {
          if (!portMap.has(port)) {
            portMap.set(port, []);
          }
          portMap.get(port)?.push(index);
        }
      });

      // 檢查重複的 port 值
      array.controls.forEach(control => {
        const port = control.get('port')?.value;
        const isDuplicate = portMap.get(port)?.length > 1;

        if (isDuplicate) {
          control.get('port')?.setErrors({ duplicate: true });
          control.get('port')?.markAsTouched();
        } else {
          control.get('port')?.setErrors(null);
        }
      });

      return null;
    };
  }

  /**
   * management port 不能被選光
   */
  private notAllManagementVlanValidator(): ValidatorFn {
    return (array: FormArray): ValidationErrors | null => {
      if (!this.idsPortArrayOptionList) {
        return;
      }
      const inManagementVlan = this.idsPortArrayOptionList.filter(item => item.inManagementVlan);

      const selectedPorts = this.globalForm.get('idsPortListArray').value;

      const selectedInManagementVlan = selectedPorts.filter(port =>
        this.idsPortArrayOptionList.some(item => item.port === port.port && item.inManagementVlan)
      );

      if (selectedInManagementVlan.length === inManagementVlan.length) {
        array.controls.forEach(control => {
          const port = control.get('port')?.value;
          if (inManagementVlan.some(item => item.port === port)) {
            control.get('port')?.setErrors({ allManagementVlan: true });
            control.get('port')?.markAsTouched();
          } else {
            control.get('port')?.setErrors(null);
          }
        });
      }

      return null;
    };
  }

  /**
   * 1. vlan 不能有重複
   * 2. vlan 不能被平台使用
   */
  private uniqueVLANValidator(): ValidatorFn {
    return (array: FormArray): ValidationErrors | null => {
      // 收集所有的 vlan 值及其索引
      const vlanMap = new Map<number, number[]>();
      array.controls.forEach((control, index) => {
        const vlan = +control.get('vlan')?.value;
        if (vlan !== null && vlan !== undefined) {
          if (!vlanMap.has(vlan)) {
            vlanMap.set(vlan, []);
          }
          vlanMap.get(vlan)?.push(index);
        }
      });

      if (!this.cachePlatformVlan || !this.cachePlatformVlan.length) {
        return;
      }
      const platformUsed = this.cachePlatformVlan.map(vlan => vlan.vlanid);

      // 檢查重複的 vlan 值
      array.controls.forEach(control => {
        const vlan = +control.get('vlan')?.value;
        const isDuplicate = vlanMap.get(vlan)?.length > 1;
        if (isDuplicate) {
          control.get('vlan')?.setErrors({ duplicate: true });
          control.get('vlan')?.markAsTouched();
        } else if (platformUsed.includes(vlan)) {
          control.get('vlan')?.setErrors({ usedByPlatform: true });
          control.get('vlan')?.markAsTouched();
        } else if (control.get('vlan').hasError('max') || control.get('vlan').hasError('min')) {
          control.get('vlan')?.markAsTouched();
        } else {
          control.get('vlan')?.setErrors(null);
        }
      });

      return null;
    };
  }

  /**
   * 把刪除掉的 port 設定回 management vlan
   * 把新增的 port 設定成 2
   */
  private createSRVParam(): {
    SRV_VLAN: SRV_VLAN_Table[];
    SRV_VPLAN: SRV_VPLAN_Table[];
  } {
    const selectedPortsVlan = this.globalForm.get('idsPortListArray').value.map(data => `${data.port}-${data.vlan}`);
    const deletedOfflineIds = this.cacheSelectedVlan.filter(item => !selectedPortsVlan.includes(item));
    const managementVlanId = this.managementVlanId;

    //醬刪除的還原預設值
    let SRV_VLAN = this.cacheSRVVlan;
    const SRV_VPLAN = this.cacheSRVVplan;

    //刪除
    deletedOfflineIds.forEach(value => {
      const [port, vlanId] = value.split('-').map(Number);
      // 把 port 設定回 management vlan
      const managementVlan = SRV_VLAN.find(vlan => vlan.vlanid === managementVlanId);
      if (managementVlan) {
        managementVlan.port[port - 1] = 2;
        managementVlan[`port${port - 1}`] = 2;
      }

      // 移除 VLAN
      SRV_VLAN = SRV_VLAN.filter(vlan => vlan.vlanid !== vlanId);

      // 移除 VPLAN
      SRV_VPLAN.forEach(vplan => {
        if (vplan.pvid === vlanId) {
          vplan.pvid = managementVlanId;
        }
      });
    });

    //新增
    selectedPortsVlan.forEach(value => {
      const [port, vlanId] = value.split('-').map(Number);
      const vlanExists = SRV_VLAN.some(vlan => vlan.vlanid === vlanId);

      // 把所有被選到的 port 設定成 0
      SRV_VLAN.forEach(vlan => {
        vlan.port[port - 1] = 0;
      });

      // 重新設定 port 的值
      if (!vlanExists) {
        const portCount = SRV_VLAN.find(vlan => vlan.vlanid === managementVlanId)?.port.length || 0;
        const newPortArray = new Array(portCount).fill(0);
        newPortArray[port - 1] = 2;
        SRV_VLAN.push({ vlanid: vlanId, port: newPortArray });
      } else {
        const targetVlan = SRV_VLAN.find(vlan => vlan.vlanid === vlanId);
        if (targetVlan) {
          targetVlan.port[port - 1] = 2;
        }
      }

      SRV_VLAN.forEach(vlan => {
        vlan.port.forEach((portValue, index) => {
          vlan[`port${index}`] = portValue;
        });
      });

      SRV_VPLAN[port - 1].pvid = vlanId;
    });

    return {
      SRV_VLAN: SRV_VLAN,
      SRV_VPLAN: SRV_VPLAN
    };
  }

  /**
   * 底層來的 port 是 base 0，中間邏輯都是用 base 1 處理，為了不影響原本的邏輯，這邊轉換一下
   */
  private base0Port = (portVlan: string): string[] => {
    return portVlan.split(',').map(portVlan => {
      const [port, vlan] = portVlan.split('-');
      if (!isNaN(Number(port))) {
        return `${Number(port) + 1}-${vlan}`;
      }
      return portVlan;
    });
  };

  private extractLogDestinations(logConfig) {
    const destinations: LogDestination[] = [];

    if (logConfig?.logFlash) destinations.push(LogDestination.LOCAL_STORAGE);
    if (logConfig?.logSyslog) destinations.push(LogDestination.SYSLOG);
    if (logConfig?.logTrap) destinations.push(LogDestination.TRAP);

    return destinations;
  }
}
