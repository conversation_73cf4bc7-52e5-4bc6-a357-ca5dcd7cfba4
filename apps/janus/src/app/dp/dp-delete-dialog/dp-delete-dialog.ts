import { Component, inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';

import { TranslateService } from '@ngx-translate/core';

import { AngularElementService } from '@mx-ros-web/shared/Service/angular-element.service';
import { ErrorService } from '@mx-ros-web/shared/Service/error.service';

import { JanusCmd } from '../../package-control.def';
import { JanusCliPostCmd, JanusPackageControlService } from '../../package-control.service';
import { RestService } from '../../rest.service';

@Component({
  templateUrl: './dp-delete-dialog.html'
})
export class DpDeleteDialogComponent {
  #angularEleService = inject(AngularElementService);
  #pkgService = inject(JanusPackageControlService);
  #snackBar = inject(MatSnackBar);
  #errorService = inject(ErrorService);
  #dialogRef = inject(MatDialogRef<DpDeleteDialogComponent>);
  #dialogData = inject(MAT_DIALOG_DATA);
  #translate = inject(TranslateService);
  #rest = inject(RestService);

  onDialogSubmit(): void {
    this.#angularEleService.emitLoading(true);

    const minSetWl = 5;
    const wlDelList: JanusCliPostCmd[] = this.#dialogData.selected.map(row => {
      return {
        majorId: JanusCmd.DP,
        minorId: minSetWl,
        dataVal: `@${row.domainKey}`,
        index: row.id
      };
    });
    const cliGetBody = this.#pkgService.createJanusCLIPostCmdList(wlDelList);

    this.#rest.post(cliGetBody).subscribe(
      () => {
        this.#snackBar.open(this.#translate.instant('response_handler.res_global_success'), '', {
          duration: 3000
        });
        this.#dialogRef.close('submit');
      },
      error => {
        this.#angularEleService.emitLoading(false);
        this.#errorService.handleError(error);
      }
    );
  }
}
