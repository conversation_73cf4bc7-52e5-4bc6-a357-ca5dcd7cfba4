{"name": "mx-ros-web", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "network", "sourceRoot": "apps/mx-ros-web/src", "tags": [], "targets": {"build": {"executor": "@angular-devkit/build-angular:browser", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/mx-ros/mx-ros-web", "index": "apps/mx-ros-web/src/index.html", "main": "apps/mx-ros-web/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/mx-ros-web/tsconfig.app.json", "assets": ["apps/mx-ros-web/src/favicon.ico", "apps/mx-ros-web/src/assets/i18n", "apps/mx-ros-web/src/assets/img", "apps/mx-ros-web/src/assets/web.conf.json", "apps/mx-ros-web/src/assets/zeus", "apps/mx-ros-web/src/assets/mx-service/style/img", "apps/mx-ros-web/src/assets/packages", {"followSymlinks": true, "input": "apps/mx-ros-web/src/assets/img/panel_imgs/EDF-G1002", "glob": "**/*", "output": "/assets/img/panel_imgs"}, {"followSymlinks": true, "input": "apps/mx-ros-web/src/assets/img/panel_imgs/EDR-8010", "glob": "**/*", "output": "/assets/img/panel_imgs"}, {"followSymlinks": true, "input": "apps/mx-ros-web/src/assets/img/panel_imgs/EDR-G9004", "glob": "**/*", "output": "/assets/img/panel_imgs"}, {"followSymlinks": true, "input": "apps/mx-ros-web/src/assets/img/panel_imgs/EDR-G9010", "glob": "**/*", "output": "/assets/img/panel_imgs"}, {"followSymlinks": true, "input": "apps/mx-ros-web/src/assets/img/panel_imgs/NAT-102", "glob": "**/*", "output": "/assets/img/panel_imgs"}, {"followSymlinks": true, "input": "apps/mx-ros-web/src/assets/img/panel_imgs/ONCELL-G4302", "glob": "**/*", "output": "/assets/img/panel_imgs"}, {"followSymlinks": true, "input": "apps/mx-ros-web/src/assets/img/panel_imgs/ONCELL-G4308", "glob": "**/*", "output": "/assets/img/panel_imgs"}, {"followSymlinks": true, "input": "apps/mx-ros-web/src/assets/img/panel_imgs/OnCell-G5708", "glob": "**/*", "output": "/assets/img/panel_imgs"}, {"followSymlinks": true, "input": "apps/mx-ros-web/src/assets/img/panel_imgs/TN-4900", "glob": "**/*", "output": "/assets/img/panel_imgs"}], "styles": ["node_modules/ngx-toastr/toastr.css", "node_modules/leaflet/dist/leaflet.css", "apps/mx-ros-web/src/styles.scss", "apps/mx-ros-web/src/assets/mx-service/style/default-theme.scss", "apps/mx-ros-web/src/assets/mx-service/style/material-override.scss", "apps/mx-ros-web/src/assets/scss/material-table.scss", "apps/mx-ros-web/src/assets/scss/ngx-toast-overwrite.scss", "apps/mx-ros-web/src/assets/fonts/roboto.css"], "stylePreprocessorOptions": {"includePaths": ["apps/mx-ros-web/src/assets"]}, "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "5kb", "maximumError": "7kb"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "mx-ros-web:build:production"}, "development": {"browserTarget": "mx-ros-web:build:development", "proxyConfig": "apps/mx-ros-web/proxy.config.js"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "mx-ros-web:build"}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/mx-ros-web/**/*.ts", "apps/mx-ros-web/**/*.html"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/mx-ros-web/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "semantic-release": {"executor": "nx:run-commands", "options": {"command": "pnpm semantic-release"}}, "serve-static": {"executor": "@nx/web:file-server", "options": {"buildTarget": "mx-ros-web:build"}}}}