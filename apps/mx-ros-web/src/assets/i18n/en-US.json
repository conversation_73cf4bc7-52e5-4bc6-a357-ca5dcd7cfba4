{"error_code": {"CHECKING_E_OVER_LENGTH": "Row {{index}}: {{itemName}} is over length. It must be {{min}} - {{max}}."}, "error_handler": {"consistinfo_export_failed": "Backup failed because we could not find the Consist Info file. Restore the file and try again.", "coupling_ring_error": "There can only be one ring enabled if you want to enable ring coupling.", "error_session_expired_dialog": "Your session has expired. You will be redirected to the login page.", "export_failed": "Export Failed.", "import_cert_fail": "Imported Certificate Failed", "install_fail": "Install Failed: {{ errorCode }}", "install_fail_incompatible": "Install Failed: an incompatible package version was installed.", "install_fail_not_support_model": "Install Failed: package does not support current model.", "install_fail_wrong_hash": "Install Failed: an illegal package was installed.", "install_fail_wrong_pkg": "Install Failed: wrong package was installed.", "license_expired": "License is expired.", "license_invalid": "Wrong activation code.", "trdpconfig_export_failed": "Backup failed because we could not find the TRDP Config file. Restore the file and try again.", "uninstall_fail": "Uninstall Failed: {{ errorCode }}", "upgrade_fail": "Upgrade Failed: {{ errorCode }}", "upgrade_fail_incompatible": "Upgrade Failed: an incompatible package version was upgraded.", "upgrade_fail_not_support_model": "Upgrade Failed: package does not support current model.", "upgrade_fail_wrong_hash": "Upgrade Failed: an illegal package was upgraded.", "upgrade_fail_wrong_pkg": "Upgrade Failed: wrong package was upgraded.", "upload_file_check_failed": "Package verify failed."}, "features": {"8021x": {"8021x_status": "IEEE 802.1x Status", "auth_mode": "Authentication Mode", "authentication_retry": "Authentication Retry", "authentication_retry_interval": "Authentication Retry Interval", "local_database": "Local Database", "page_title": "IEEE 802.1X", "radius_local_database": "RADIUS, Local", "reauth_port_confirm": "Are you sure you want to re-auth port {{portName}}", "retry_times": "Retry Times", "size_limitation": "The maximum number of user accounts for this device is 32.", "supplicant": "Supplicant", "title_create_account_table_msg": "Create Account <PERSON><PERSON><PERSON>", "title_edit_port_table_msg": "Port {{portName}} Settings"}, "account_management": {"admin": "Admin", "admin_disable_comfirm_desc": "There is not any enabled account. Are you sure?", "admin_disable_comfirm_title": "Disable Admin Status", "change_password_desc": "Your password doesn't meet the Password Policy. Please change your password.", "change_password_tip": "For security reasons, it is recommended to use passwords longer than 8 characters.", "expired": "Expired", "new_password": "New Password", "new_pwd_not_match": "Password not match.", "old_password": "Old Password", "page_title": "User Accounts", "password_expire": "Password Expire", "rule_size_limitation": "The maximum number of user accounts for this device is {{ size }}.", "supervisor": "Supervisor", "title_add_account_dialog_title": "Create New Account", "title_edit_account_pre_msg": "Edit Account <PERSON><PERSON><PERSON>", "user": "User"}, "arp": {"page_title": "ARP Table"}, "broadcast_forwarding": {"delete_broadcast_forwarding_desc": "Are you sure you want to delete the selected broadcast forwarding?", "page_title": "Broadcast Forwarding", "title_create_broadcast_forwarding": "Create Broadcast Forwarding", "title_delete_broadcast_forwarding": "Delete Broadcast Forwarding", "title_edit_broadcast_forwarding": "Edit Broadcast Forwarding", "udp_port_hint": "You can create range 1 to 8 UDP ports at once. For example, 67,68,520,1701 means it will listen on UDP port 67,68,520,1701"}, "cellular": {"always": "Always", "always_hint": "The system will periodically perform an Alive Check based on the configured interval.", "apn": "APN", "att": "AT&T", "authentication": "Authentication", "auto_ip_report": "Auto IP Report", "band_2G": "2G", "band_2G_1800": "GSM 1800 MHz", "band_2G_900": "E-GSM 900 MHz", "band_3G": "3G UMTS/HSPA+", "band_3G_B1_2100": "B1 (2100 MHz)", "band_3G_B19_800": "B19 (800 MHz)", "band_3G_B2_1900": "B2 (1900 MHz)", "band_3G_B3_1800": "B3 (1800 MHz)", "band_3G_B4_1700": "B4 (1700 MHz)", "band_3G_B5_850": "B5 (850 MHz)", "band_3G_B6_800": "B6 (800 MHz)", "band_3G_B8_900": "B8 (WCDMA 900 MHz)", "band_4G": "4G LTE", "band_4G_B1_2100": "B1 (2100 MHz)", "band_4G_B11_1500": "B11 (1500 MHz)", "band_4G_B12_700": "B12 (700 MHz)", "band_4G_B13_700": "B13 (700 MHz)", "band_4G_B14_700": "B14 (700 MHz)", "band_4G_B17_700": "B17 (700 MHz)", "band_4G_B18_850": "B18 (850 MHz)", "band_4G_B19_850": "B19 (850 MHz)", "band_4G_B2_1900": "B2 (1900 MHz)", "band_4G_B20_800": "B20 (800 MHz)", "band_4G_B21_1500": "B21 (1500 MHz)", "band_4G_B25_1900": "B25 (1900 MHz)", "band_4G_B26_850": "B26 (850 MHz)", "band_4G_B28_700": "B28 (700 MHz)", "band_4G_B29_700": "B29 (700 MHz)", "band_4G_B3_1800": "B3 (1800 MHz)", "band_4G_B30_2300": "B30 (2300 MHz)", "band_4G_B32_1500": "B32 (1500 MHz)", "band_4G_B34_2000": "B34 (2000 MHz)", "band_4G_B38_2600": "B38 (2600 MHz)", "band_4G_B39_1900": "B39 (1900 MHz)", "band_4G_B4_1700": "B4 (1700 MHz)", "band_4G_B40_2300": "B40 (2300 MHz)", "band_4G_B41_2500": "B41 (2500 MHz)", "band_4G_B42_3500": "B42 (3500 MHz)", "band_4G_B43_3700": "B43 (3700 MHz)", "band_4G_B46_5200": "B46 (5200 MHz)", "band_4G_B48_3500": "B48 (3500 MHz)", "band_4G_B5_850": "B5 (850 MHz)", "band_4G_B66_1700": "B66 (1700 MHz)", "band_4G_B7_2600": "B7 (2600 MHz)", "band_4G_B71_600": "B71 (600 MHz)", "band_4G_B8_900": "B8 (900 MHz)", "band_5G_NSA": "5G NSA", "band_5G_NSA_FR1_N41_2500": "N41 (2500 MHz)", "band_5G_NSA_FR1_N77_3700": "N77 (3700 MHz)", "band_5G_NSA_FR1_N78_3500": "N78 (3500 MHz)", "band_5G_NSA_FR1_N79_4700": "N79 (4700 MHz)", "band_5G_SA": "5G SA", "band_5G_SA_FR1_N1_2100": "N1 (2100 MHz)", "band_5G_SA_FR1_N12_700": "N12 (700 MHz)", "band_5G_SA_FR1_N2_1900": "N2 (1900 MHz)", "band_5G_SA_FR1_N20_800": "N20 (800 MHz)", "band_5G_SA_FR1_N25_1900": "N25 (1900 MHz)", "band_5G_SA_FR1_N28_700": "N28 (700 MHz)", "band_5G_SA_FR1_N3_1800": "N3 (1800 MHz)", "band_5G_SA_FR1_N38_2600": "N38 (2600 MHz)", "band_5G_SA_FR1_N40_2300": "N40 (2300 MHz)", "band_5G_SA_FR1_N41_2500": "N41 (2500 MHz)", "band_5G_SA_FR1_N48_3500": "N48 (3500 MHz)", "band_5G_SA_FR1_N5_850": "N5 (850 MHz)", "band_5G_SA_FR1_N66_1700": "N66 (1700 MHz)", "band_5G_SA_FR1_N7_2600": "N7 (2600 MHz)", "band_5G_SA_FR1_N71_600": "N71 (600 MHz)", "band_5G_SA_FR1_N77_3700": "N77 (3700 MHz)", "band_5G_SA_FR1_N78_3500": "N78 (3500 MHz)", "band_5G_SA_FR1_N79_4700": "N79 (4700 MHz)", "band_5G_SA_FR1_N8_900": "N8 (900 MHz)", "carrier": "Carrier", "carrier_and_sim": "Carrier and SIM", "cellular_band": "Cellular Bands", "cellular_band_type": "Cellular Band Mode", "cellular_carrier": "Cellular Carrier", "cellular_connection": "Cellular Data Connection", "cellular_information": "Cellular Information", "cellular_ip_address": "Cellular IP Address", "cellular_mode": "Cellular Mode", "cellular_module": "Cellular Module", "cellular_module_firmware": "Cellular Module Firmware", "cellular_module_information": "Cellular Module Information", "cellular_module_reset": "Cellular Module Reset", "cellular_op_mode": "Cellular Operation Mode", "cellular_reconnect": "Cellular Reconnect", "cellular_signal": "Cellular Signal", "cellular_sim": "Cellular SIM", "cellular_status": "Cellular Status", "change_sim": "Change SIM", "check_timing": "Check Timing", "connection": "Connection", "connection_alive_check": "Connectivity Check", "connection_hint": "Check if a connection can be established.", "desc_change_sim_dialog": "Are you sure you want to change to SIM {{ id }}?", "dns_settings": "DNS Settings", "docomo": "DOCOMO", "execute_times": "Attempts", "fair": "Fair", "generic": "Generic", "glink_reboot_enable_warning": "It is recommended to disable System Reboot if WAN Redundancy or VRRP is enabled.", "good": "Good", "guaran_link": "GuaranLink", "guaran_link_recovery_settings": "GuaranLink Recovery Settings", "guaranlink": "GuaranLink", "idle_transmission": "Idle Transmission", "idle_transmission_hint": "The system will perform an Alive Check when no data transmission is received for a certain time.", "idle_transmission_interval": "Idle Transmission Interval", "imei": "IMEI", "imsi": "IMSI", "in_use": "Active", "internet": "Internet", "internet_hint": "Check if it can ping the host set in GuaranLink. If GuaranLink is disabled, detection will not be possible.", "interval": "Interval", "isp_reregister": "ISP Reregister", "kddi": "KDDI", "modem": "Modem", "modem_type": "Modem Type", "mtu": "MTU", "no_signal": "No Signal", "page_title": "Cellular", "phone_number": "Phone Number", "pin": "PIN", "ping_failure_retry_times": "Ping Failure Retry Times", "ping_host": "Ping Host {{index}}", "ping_interval": "<PERSON>", "poor": "Poor", "poor_signal": "Poor Signal", "poor_signal_hint": "The system will perform an Alive Check when detecting a poor cellular signal.", "primary_dns_server": "Primary DNS Server", "priority_reorder_disable_hint": "The SIM is diabled", "recovery_action": "Recovery Action", "recovery_step": "Recovery Step", "register": "Register", "register_hint": "Check if it can connect to the base station.", "route": "Router", "rsrp": "Reference Signal Received Power(RSRP)", "rsrq": "Reference Signal Received Quality (RSRQ)", "rssi": "Received Signal Strength Indicator (RSSI)", "secondary_dns_server": "Secondary DNS Server", "serial_modem": "Serial Modem ", "signal_check_interval": "Signal Checking Interval", "signal_hint": "Check if the signal is normal.", "signal_status": "Signal Status", "signal_strength": "Signal Strength", "sim": "SIM", "sim_hint": "Check if the SIM card is invalid.", "sim_iccid": "SIM {{ id }} ICCID", "sim_phone_number": "SIM {{ id }} Phone Number", "sim_settings": "SIM Settings", "sim_status": "SIM {{ id }} Status", "singal": "Signal", "sinr": "Signal-to-interference-plus-noise Ratio (SINR)", "softbank": "SoftBank", "system_reboot": "System Reboot", "telstra": "Telstra", "title_change_sim_dialog": "Change SIM", "title_glink_setting_dialog": "Edit Recovery Action Settings", "title_sim_setting_dialog": "Edit SIM {{ id }} Settings", "verizon": "Verizon", "virtual_modem": "Virtual Modem"}, "cert_signing_request": {"common_name": "Common Name", "country_name": "Country Name (2 letter code)", "csr_generate": "CSR Generate", "csr_size_limitation": "This device only allows 10 CSRs", "email_address": "Email Address", "key_pair_generate": "Key Pair Generate", "key_pair_size": "Key Pair <PERSON>", "key_size_limitation": "This device only allows 10 key pairs", "locality_name": "Locality Name", "organization_name": "Organization Name", "organizational_unit_name": "Organizational Unit Name", "page_title": "Certificate Signing Request", "private_key": "Private Key", "state_or_province_name": "State or Province Name", "subject_alternative_name": "Subject Alternative Name", "title_create_csr_dialog_title": "Generate Certificate Signing Request", "title_create_rsa_dialog_title": "Generate RSA Key", "title_delete_csr_dialog_desc": "Are you sure you want to delete the selected certificate signing request?", "title_delete_csr_dialog_title": "Delete Certificate Signing Request", "title_delete_rsa_dialog_desc": "Are you sure you want to delete the selected RSA keys?", "title_delete_rsa_dialog_desc_with_csr": "When deleting this RSA key, its corresponding CSRs will also be deleted.", "title_delete_rsa_dialog_title": "Delete RSA Key", "title_generate_rsa_dialog_desc": "RSA key is being generated. Please wait and refresh.", "title_generate_rsa_success": "RSA key generated successfully."}, "communication_profile": {"backup": "BACKUP", "com_id": "ComID", "ecsc_status": "ECSC Status", "ecsp_sdsink": "ECSP SDSINK", "ecsp_sdsrc": "ECSP SDSRC", "ecsp_settings": "ECSP Settings", "ecsp_status": "ECSP Status", "edit_status": "Edit Status", "etb_control_service": "ETB Control Service", "expectedSourceIdentifier": "Expected Source Identifier (SID)", "import_local_consist_info": "Import Local Consist Info", "import_trdp_config": "Import TRDP Config", "local_consist_info": "Local Consist Info", "page_title": "Communication profile", "restore": "RESTORE", "sdtv2_settings": "SDTv2 Settings", "sdtv2_status": "SDTv2 Status", "source_identifier": "Source Identifier (SID)", "state": "State", "stateMachine": "State Machine", "status": "Status", "telegram": "Telegram", "trdp_config": "TRDP Config"}, "config_bk_res": {"abc02_status": "ABC02 Status", "auto_back_up": "Automatically Back Up", "auto_backup_configurations": "Auto Configuration Backup", "auto_load_config": "Automatically Restore", "auto_restore_configurations": "Auto Configuration Restore", "config_restore_fail": "Configuration <PERSON><PERSON> Fail", "configuration_name": "Configuration Name", "configuration_selection": "Configuration Selection", "enable_config_file_signature": "Configuration File Signature", "encrypt_all_information": "Encrypt all information", "encrypt_sensitive_information": "Encrypt sensitive information only", "encryption": "File Encryption", "key_string": "Key String", "page_title": "Configuration Backup and Restore", "signature_information": "Signature Information", "versioncheck": "Configuration Firmware Version Checking"}, "dashboard": {"cellular_info": "Cellular Information", "collapse_port_detail": "Collapse", "critical_hint": "An abnormality has occurred and the system is at risk of functioning abnormally in the future", "error_hint": "An abnormality has occurred, but system operations have not been affected", "event_summary_hint": "(Last 3 days)", "expand_port_detail": "Expand", "firmware_version": "Firmware Version", "lan_ip_address": "LAN IP Address", "link_down_port": "Link Down Ports", "link_up_port": "Link Up Ports", "location": "Identify Location", "model_info": "Model Information", "notice_hint": "The information denotes that the function is working correctly and the device is operating normally", "oob_ip_address": "OOB IP Address", "oob_ipv4_address": "OOB IPv4 Address", "oob_mac_address": "OOB MAC Address", "page_title": "<PERSON><PERSON>", "panel_status": "Panel Status", "panel_view": "Panel View", "product_model": "Product Model", "serial_no": "Serial Number", "system_event_summary": "System Event Summary", "system_uptime": "System Uptime", "top_5_interface_error_packet": "Top 5 Interface Error Packet", "top_5_interface_utilization": "Top 5 Interface Utilization", "view_all_system_event_logs": "View All System Event Logs", "wan_ip_address": "WAN IP Address", "wan1_ip_address": "WAN 1 IP Address", "wan1_mac_address": "WAN 1 MAC Address", "wan2_ip_address": "WAN 2 IP Address", "wan2_mac_address": "WAN 2 MAC Address", "wan3_ip_address": "WAN 3 IP Address", "wan4_ip_address": "WAN 4 IP Address", "warning_hint": "The information contains a warning/reminder and it does not affect functions or system operations"}, "ddns": {"domain_name": "Domain Name", "page_title": "Dynamic DNS", "service": "Service", "service_name": "Service Name"}, "device_lockdown": {"alert": "Device Lockdown Alert", "allow": "Allow", "auto_learned": "Auto Learning", "auto_learning_on_bootup": "Auto Learning on Startup", "block": "Block", "bootup": "Booting", "create_learning_list": "Create Learning List Entry", "desc": "When Device Lockdown is enabled, the Learning Table can't be manually configured. Please disable Device Lockdown to make modifications.", "description": "Description", "edit_learning_list": "Edit Learning List Entry", "enable_whitelist_tooltip": "Can not Enable while Learning!", "entry_from": "Entry Source", "interface": "Interface", "ip_address": "IP Address", "learning": "Learning In Progress", "learning_countdown": "Learning Countdown", "learning_done": "Learning Done", "learning_on_boot": "Learning on Boot", "learning_period": "Learning Period", "learning_status": "Learning Status", "learning_table": "Learning Table", "lockdown_mode": "Lockdown Mode", "lockdown_mode_mac_ip": "MAC + IP Address", "mac_address": "MAC Address", "manual_configured": "Manual Configuration", "network_access": "Network Access", "page_title": "Device Lockdown", "settings": "Settings", "stand_by": "Standby", "title_stop_learning_dialog_desc": "Are you sure you want to stop learning?", "title_stop_learning_dialog_desc_disable": "The allowlist will not take effect until <PERSON><PERSON> Lockdown is enabled.", "title_stop_learning_dialog_desc_enable": "The allowlist will take effect IMMEDIATELY.", "title_stop_learning_dialog_title": "Stop Learning"}, "dhcp": {"other": "Other", "client_id": "Client ID", "default_gateway": "Default Gateway", "delete_dhcp_entry_confirm_desc": "Are you sure you want to delete the selected DHCP Rule?", "delete_ip_mac_entry_confirm_desc": "Are you sure you want to delete the selected MAC-based IP assignment?", "delete_ip_port_entry_confirm_desc": "Are you sure you want to delete the selected Port-based IP assignment?", "dhcp": "DHCP", "dhcp_circuitId": "Circuit-ID", "dhcp_func_table": "DHCP Function Table", "dhcp_ip_mac_option": "DHCP/MAC-based assignment", "dhcp_opt_82": "Option 82", "dhcp_opt82": "DHCP Option 82", "dhcp_opt82_toggle": "Enable Option 82", "dhcp_relay_agent": "DHCP Relay Agent", "dhcp_relay_server": "DHCP Relay Server-{{ number }}", "dhcp_rule_size_limitation": "This device only allows {{ size }} DHCP Server Pools.", "dhcp_rule_size_limitation_general": "This device only allows {{ size }} Rules.", "dns_server_1": "DNS Server 1", "dns_server_2": "DNS Server 2", "end_ip": "Ending IP Address", "hostname": "Hostname", "hostname_hint": "The hostname represents the name of the DHCP client and will be encoded into the Option 12 tag in the DHCP offer packet.", "ip_mac_binding": "MAC-based IP Assignment", "ip_port_binding": "Port-based IP Assignment", "ip_port_binding_option": "Port-based IP assignment", "ip_range": "Pool IP Range", "lan": "LAN", "lan_ip": "LAN IP", "lease_table": "Lease Table", "lease_time": "Lease Time", "mac": "MAC", "ntp_server": "NTP Server", "page_title": "DHCP Server", "start_ip": "Starting IP Address", "time_left": "Time Left", "title_create_entry_msg": "Create DHCP Server Pool", "title_delete_entry_msg": "Delete DHCP Server Pool", "title_edit_entry_msg": "Edit DHCP Server Pool", "wan_ip": "WAN IP"}, "diagnostic_sup": {"generate_profile": "Generate Profile", "generate_profile_desc": "Provide the generated file to Moxa technical support for troubleshooting.", "module_firmware": "Module Firmware", "module_firmware_upgrade": "Module Firmware Upgrade", "page_title": "Diagnostic Support", "progress_file_unpack": "Cellular Module Firmware File Unpacking...", "progress_file_upgrade": "Cellular Module Firmware Upgrading...", "progress_file_verify": "Cellular Module Firmware File Verifying...", "result_invalid_format": "Invalid Cellular Module Firmware File Format.", "result_success": "Successfully Upgraded Cellular Firmware.", "result_upgrade_fail": "Cellular Module Firmware Upgrade Fail.", "result_verify_fail": "Cellular Module Firmware File Verify Fail.", "system_profile": "System Profile"}, "dns_server": {"delete_dns_desc": "Are you sure you want to delete the selected resource record(s)?", "delete_zone_desc": "Are you sure you want to delete the selected zone(s)?", "dns_reverse_lookup": "DNS Reverse Lookup", "dns_server_summary": "DNS Server Summary", "dns_size_limitation": "The maximum number of DNS entries is {{ size }}.", "dns_table_for_name": "DNS Table for {{ zoneName }}", "domain_name": "Domain Name", "duplicate_domain_name": "Domain name already exists.", "duplicate_hostname": "Hostname already exists.", "fqdn": "FQDN", "global": "Global", "hostname_hint": "FQDN (fully qualified domain name) is \"Hostname\".\"Domain Name\".", "page_title": "DNS Server", "title_add_dns": "Create a Zone", "title_add_dns_zone": "Create Resource Record for {{name}}", "title_delete_dns": "Delete Resource Record(s)", "title_delete_zone": "Delete Zone(s)", "title_edit_dns": "Edit {{name}} Settings", "title_edit_dns_zone": "Edit Resource Record for {{name}}", "zone_size_limitation": "The maximum number of zone entries is {{ size }}.", "zone_table": "Zone Table", "zone_table_index": "ZONE-{{index}}"}, "dos_policy": {"all": "All", "dos_log_setting": "DoS Log Settings", "dos_setting": "DoS Settings", "Flood_Protection": "Flood Protection", "limit": "Limit", "page_title": "DoS Policy", "Port_Scan_Protection": "Port Scan Protection", "Session_SYN_Protection": "Session SYN Protection", "stat1": "<PERSON><PERSON>", "stat10": "SYN-Flood", "stat11": "ARP-Flood", "stat12": "UDP-Flood", "stat2": "<PERSON><PERSON>", "stat3": "NMAP-<PERSON><PERSON>", "stat4": "SYN/FIN Scan", "stat5": "FIN Scan", "stat6": "NMAP-ID Scan", "stat7": "SYN/RST Scan", "stat8": "TCP Sessions Without SYN", "stat9": "ICMP-Flood", "TCP_sessions_without_SYN_hint": "Limitation: For asymmetric network architectures and when NAT is enabled, it is strongly advised not to enable \"TCP Sessions Without SYN\" to avoid unexpected disconnections."}, "email_settings": {"ca certificate": "CA Certificate", "disabled": "Disabled", "mail_server": "Mail Server", "page_title": "<PERSON><PERSON>s", "recipient_1": "1st Recipient Email Address", "recipient_2": "2nd Recipient Email Address", "recipient_3": "3rd Recipient Email Address", "recipient_4": "4th Recipient Email Address", "recipient_5": "5th Recipient Email Address", "sender_address": "Sender Address", "starttls_auth": "STARTTLS (Authentication Required Mode)", "starttls_no_auth": "STARTTLS (No Authentication Mode)", "tls": "TLS", "tls_enable": "TLS Enable"}, "ethernet_train_backbone": {"add_ecnDialogTitle": "Add ECN {{ecnId}}", "cn_id": "CN ID", "conn_table_valid": "ConnTableValid", "connTableCrc32": "ConnTableCrc32", "consist_network": "Consist Network", "consist_network_max": "Max allowed is {{ count }}.", "consist_uuid": "Consist UUID", "cst_orientation": "CstOrientation", "cst_uuid": "CstUUID", "delete_comfirm": "Delete Comfirm", "delete_comfirm_desc": "Are you sure you want to delete Static ID: {{ staticIDs }} ?", "direction_1": "Direction 1", "direction_2": "Direction 2", "Disable": "Disabled", "ecn_in_consist": "ECN(s) in Consist", "ecn_interface_ip_address": "ECN interface IP address", "ecn_interface_ip_address_tooltip": "Suggest to follow the rule 00001010.00xxxxxx.bbhhhhhh.hhhhhhhh/18 where xx is static CN ID, bb is backbone ID and hh is host ID.", "ecn_port_vlan_id": "ECN Port VLAN ID", "ecn_port_vlan_id_hint": "Default 1000 + static ID", "ecn_ports": "ECN Ports", "ecn_ports_tooltip": "ETB port can not be selected.", "ecn_to_etbn": "ECN to ETBN", "ecnDialogTitle": "Edit ECN {{ecnId}}", "Enable": "Enable", "etb_backbone_id": "ETB Backbone ID", "etb_port_speed": "ETB Port Speed", "etb_port_vlan_id": "ETB Port VLAN ID", "etb_port_vlan_id_hint": "1-4094, 492 is reserved", "etb_status": "ETB Status", "etb_topo_cnt": "EtbTopoCnt", "etb_topo_cnt_valid": "EtbTopoCntValid", "etbn": "ETBN", "etbn_id": "ETBN ID", "etbn_in_consist": "ETBN(s) in Consist", "etbn_line_status": "ETBN Line Status", "etbn_state": "ETBN State", "etbnInhibition": "etbn Inhibition", "hello_frame_dir1": "Hello Frame (DIR 1)", "hello_frame_dir2": "Hello Frame (DIR 2)", "inaugInhibition": "Inaug Inhibition", "inbound_interface": "Inbound Interface", "interface_ip_address": "Interface IP address", "lengthen": "Lengthen", "line": "Line", "line_status_dir1": "Line Status (DIR 1)", "line_status_dir2": "Line Status (DIR 2)", "local_consist": "Local Consist", "local_etbn": "Local ETBN", "local_etbn_info_tooltip": "The ETBN with static ID 1 will be top node when only one consist in ETB", "local_etbn_redundant_role": "Local ETBN Redundant Role", "local_etbn_static_id": "Local ETBN Static ID", "local_etbn_status": "Local ETBN Status", "mac_address": "<PERSON>dress", "memorized_etbtopocnt": "Memorized EtbTopoCnt", "orientation": "Orientation", "outbound_interface": "Outbound Interface(s)", "page_title": "<PERSON><PERSON><PERSON> Backbone", "port_mdi_mdix": "Port MDI/MDIX", "remoteInhibition": "remote Inhibition", "shorten": "<PERSON>en", "static_id": "Static ID", "subnet_id": "Subnet ID (Train Subnet)", "tcn_group_address": "TCN Group Address", "tcn_multicast_table": "TCN Multicast Table", "train_network_directory": "Train Network Directory", "ttdp_settings": "TTDP Settings", "ttdp-enable": "TTDP Enable", "uuid_info_tooltip": "User can manually assign or generate random Consist UUID", "waring": "Waring", "waring-desc": "Don't allow ecn consist number greater than or equal to consist network list size"}, "event_notification": {"action_relay": "<PERSON><PERSON>", "auth_failure": "<PERSON><PERSON> Failure", "cellular_gl_isp_reregister": "Guaranlink Triggered ISP Reregister", "cellular_gl_module_reset": "Guaranlink Triggered Cellular Module Reset", "cellular_gl_reboot": "Guaranlink Triggered System Reboot", "cellular_gl_reconnect": "GuaranLink Cellular Reconnected", "cellular_ip_change": "IP Change", "cellular_module_change": "Cellular Module Failure", "cellular_module_high_temp": "High Cellular Module Temperature", "cellular_pin_code_fail": "PIN Code Failure", "cellular_sim_detect_fail": "Detect SIM Failure", "cellular_sim_switch": "SIM Switch", "cold_start": "Cold Start", "config_change": "Configuration Changed", "coupling_topology_changed": "Coupling Topology Changed", "cpu_usage": "CPU Usage", "device_lockdown_state_change": "Device Lockdown State Change", "dhcp_error_log_trigger": "DHCP Error Log", "di_off": "Digital Input Transition (On -> Off)", "di_on": "Digital Input Transition (Off -> On)", "dot1x_auth_failure": "802.1x Authentication Failure", "duration": "Duration (Sec)", "email": "Email", "event_name": "Event Name", "fiber_check_warning": "Fiber Check Warning", "firewall_config_change": " Layer 3 - 7 Policy Changed", "firewall_policy": "Firewall Policy Changed", "firmware_upgrade_failure": "Firmware Upgrade Failure", "firmware_upgrade_success": "Firmware Upgrade Success", "group": "Group", "igmpsnooping_error_log_trigger": "IGMP Snooping Error Log", "link_off": "Link-Off", "link_on": "Link-On", "log_service_ready": "Log Service Ready", "master_mismatch": "Master Mi<PERSON>tch", "ntp": "NTP", "ntp_error_log_trigger": "NTP/SNTP Error Log", "over_allocated_power_limitation": "Over Allocated Power limitation", "over_measured_power_limitation": "Over Measured Power limitation", "page_title": "Event Notifications", "poe_fetbad": "PoE FETBad", "poe_over_temperature": "PoE Over Temperature", "poe_pd_check_fail": "PoE PD Check Fail", "poe_pd_off": "PoE PD Off", "poe_pd_on": "PoE PD On", "poe_pd_over_current": "PoE PD Over Current", "poe_vee_uvlo": "PoE VEE Uvlo", "port": "Port", "port_usage": "Port Usage", "port_usage_alarm": "Port Usage Alarm", "power_1_off2on": "Power 1 Transition (Off->On)", "power_1_on2off": "Power 1 Transition (On->Off)", "power_2_off2on": "Power 2 Transition (Off->On)", "power_2_on2off": "Power 2 Transition (On->Off)", "power_saving_end": "Power Saving End", "power_saving_start": "Power Saving Start", "power_scheduling_rile_expired": "Scheduling Rule Expired", "registered_action": "Registered Action", "relay1": "Relay1", "relay2": "Relay2", "ring_chain_rstp_topology_changed": "Ring/Chain/RSTP Topology Changed", "ring_rstp_topology_changed": "Ring/RSTP Topology Changed", "rx": "Rx", "rx_duration": "Rx Duration (Sec)", "rx_threshold": "<PERSON><PERSON> (%)", "serial_dcd_change": "Serial DCD State Changed", "serial_dsr_change": "Serial DSR State Changed", "serial_op_change": "Serial OP Mode State Changed", "severity": "Severity", "sms": "SMS", "sms_cmd_disabled": "Command Disabled", "sms_trust_num_auth_fail": "Trusted Number Authentication Failure", "sms_wrong_command": "Wrong Command", "sms_wrong_format": "Wrong Format", "sms_wrong_password": "Wrong Password", "syslog": "Syslog", "system": "System", "threshold": "<PERSON><PERSON><PERSON><PERSON> (%)", "tx": "Tx", "tx_duration": "Tx Duration (Sec)", "tx_threshold": "Tx Threshold (%)", "vpn_connected": "VPN Connected", "vpn_disconnected": "VPN Disconnected", "vrrp_state_change": "VRRP State Change", "wan_if_change": "WAN Interface Changed", "wan_if_ping_fail": "WAN Interface Ping Failure", "warm_start": "Warm Start", "warning_threshold": "Warning Threshold"}, "eventlog": {"additional_message": "Additional message", "auto_backup_of_eventlog": "Auto Event Log Backup", "capacity_warning": "Capacity Warning", "category_name": "Category Name", "clear_all_log": "Clear All Log", "clear_logs": "Clear {{ categoryName }} Log", "firewall_log": "Firewall Log", "flush_log_entry_confirmation_message": "Are you sure you want to clear {{ categoryName }} Log ?", "oversize_action": "Oversize Action", "oversize_action_overwrite": "Overwrite the oldest event log", "oversize_action_stop": "Stop recording event logs", "page_title": "Event Log", "ping_response": "Ping Response", "setting_and_backup": "Settings and Backup", "system_log": "System Log", "threshold_setting": "<PERSON><PERSON><PERSON><PERSON>", "timestamp": "Timestamp", "title_edit_eventlog": "Edit {{ capacityName }} Thr<PERSON>old Settings", "vpn_log": "VPN Log", "warning_threshold": "Warning Threshold", "dp": "Domain Protection", "domain": "Domain", "protocol": "Protocol", "asset-recognition": "Asset Recognition", "asset_ip_address": "Asset IP Address", "asset_mac_address": "Asset MAC Address", "asset_recognition_online": "The assets status is from offline to online.", "asset_recognition_offline": "The assets status is from online to offline."}, "fiber_check": {"cur_rx_pow": "Current RX Power (dBm)", "cur_temp": "Current Temperature (°C)", "cur_tx_pow": "Current TX Power (dBm)", "max_min_tx_pow": "Tx Power\n(Threshold Low/High) (dBm)", "max_temp": "Temperature Threshold (°C)", "min_rx_pow": "RX Power\n(Threshold Low/High) (dBm)", "model_name": "Model Name", "page_title": "Fiber Check", "sn": "Serial Number", "vccv": "Voltage (V)", "wavelength_nm": "Wavelength (nm)"}, "firewall_layer2_policy": {"accept": "Accept", "any_brg_member": "Any Brg Members", "delete_confirm_msg": "Are you sure you want to delete the selected Layer 2 Policy?", "destination_mac_type": "Destination MAC Type", "drop": "Drop", "ether_type_options": "EtherType Options", "ether_type_value": "EtherType Value (Hexadecimal)", "ethertype_opt": {"802_1q_virtual_lan_tagged_frame": "802.1Q Virtual LAN Tagged Frame", "appletalk": "AppleTalk", "appletalk_aarp": "Appletalk AARP", "arp": "ARP", "dec_assigned_proto": "DEC Assigned Proto", "dec_customer_use": "DEC Customer Use", "dec_diagnostics": "DEC Diagnostics", "dec_dna_dump_load": "DEC DNA Dump/Load", "dec_dna_remote_console": "DEC DNA Remote Console", "dec_dna_routing": "DEC DNA Routing", "dec_lat": "DEC LAT", "dec_systems_comms_arch": "DEC Systems Comms Arch", "frame_based_atm_transport_over_etherne": "Frame-based ATM Transport over Etherne", "frame_based_atm_transport_over_ethernet": "Frame-based ATM Transport over Ethernet", "frame_relay_arp": "Frame Relay ARP", "g8bpq_ax_25_ethernet_packet": "G8BPQ AX.25 Ethernet Packet", "ip_version_6": "IP version 6", "ipv4": "IPv4", "loopback": "Loopback", "manual": "Manual", "multiprotocol_over_atm": "MultiProtocol over ATM", "netbeui": "NetBEUI", "novell_ipx": "Novell IPX", "ppp": "PPP", "pppoe_discovery_messages": "PPPoE discovery messages", "pppoe_session_messages": "PPPoE session messages", "raw_frame_relay": "Raw Frame Relay", "trans_ether_bridging": "Trans Ether Bridging", "x25": "X.25"}, "event": "Event", "incoming_bridge_port": "Incoming Bridge Port", "outgoing_bridge_port": "Outgoing Bridge Port", "page_title": "Layer 2 Policy", "single": "Single", "size_limitation": "This device only allows 256 policies.", "source_mac_type": "Source MAC Type", "title_add_layer_2_policy": "Add Layer 2 Policy", "title_delete_layer_2_policy": "Delete Layer 2 Policy", "title_edit_layer_2_policy": "Edit Layer 2 Policy"}, "firewall_layer3_policy": {"action_profile": "Action ", "automation_profile": "Automation Profile", "delete_msg": "Are you sure you want to delete the selected policy?", "delete_title": "Delete Layer 3 Policy", "dst_ip_port": "Dst. IP:Port", "eventlog": "Event Log/Severity", "filter_mode": "Filter Mode", "firewall_event_log": "Firewall Event Log", "incoming_ifname": "Incoming Interface", "interface_from": "From Interface", "interface_to": "To Interface", "ip_filter": "IP Address Filter", "ipt_filter_create_dialog_title": "Create Index {{portIndex}}", "ipt_filter_setting_dialog_title": "Edit Index {{portIndex}}", "ipt_rule_size_limitation_general": "This device only allows {{ size }} rules.", "malformed_packets": "Malformed Packets", "outgoing_ifname": "Outgoing Interface", "page_title": "Layer 3 Policy", "src_ip_mac_bind": "Source IP MAC Binding", "src_ip_port": "Src. IP:Port", "src_mac": "Src. MAC", "src_mac_filter": "Source MAC Filter"}, "firewall_policy": {"page_title": "Policy Settings"}, "firmware_upgrade": {"check_62443": "Are you sure you want to downgrade the firmware to a non-IEC 62443-4-2 certified version?", "enable_config_file_encryption": "Configuration File Encryption", "firmware_upgrading": "Firmware Upgrading", "page_title": "Firmware Upgrade"}, "gnss": {"available_satellite": "Available Satellites", "display_map": "Dispaly Map", "gnss_client": "GNSS Client", "gnss_client_hint": "GNSS Client mode enables the device to send GNSS data to a server.", "gnss_disabled": "GNSS Disabled", "gnss_server": "GNSS Server", "gnss_server_hint": "GNSS Server mode allows clients to request GNSS data.", "host_address": "Host Address", "host_port": "Host Port", "latitude": "Latitude", "longitude": "Longitude", "map_warning": "An active Internet connection is required to view and use the interactive map.", "page_title": "GNSS", "report_format": "Report Format", "report_id": "Report ID", "report_period": "Report Period", "report_protocol": "Report Protocol", "server_port": "Server Port"}, "hardware_interface": {"advanced": "Advanced", "blinking_per_sec": "Blink once per second", "configuration_importing_and_saving": "Importing/saving configuration", "device_is_operating_normally": "Device is operating normally", "factory_default_no_configuration_change": "Factory default, no configuration changes", "fault_led": "Fault LED", "fault_led_mode_option_description": "Fault LED Mode Options At-a-glance", "led_mode": "LED Mode", "led_mode_advanced": "Advanced / Configuration Change Alarm", "led_mode_default": "Moxa Default / System Fault Alarm", "moxa_default": "<PERSON><PERSON>", "na": "N/A", "off": "Off", "on": "On", "oob_interface": "Out of Band Interface", "page_title": "Hardware Interface", "rapid_blinking_for_6_sec": "Blink rapidly for 6 seconds", "system_fault": "System Fault", "usb_function": "USB Function", "usb_unmont": "USB Function has been disabled in Hardware Interface."}, "igmp_snooping": {"act_as_querier": "Act as Querier", "auto_learned_port": "Auto Learned Multicast Router Port", "filter_mode": "Filter Mode", "forwarding_table": "Forwarding Table", "group_address": "Group Address", "group_table": "Group Table", "page_title": "IGMP Snooping", "querier": "<PERSON><PERSON>", "querier_connected_port": "Querier Connected Port", "query_interval": "Query Interval", "source_address": "Source Address", "static_multicast_port": "Static Multicast Router Port", "static_router_port": "Static Router Port", "v1_v2": "V1/V2", "v3": "V3", "vlan_setting": "VLAN Settings"}, "interface_quick_setting": {"address_information": "Address Information", "apply": "APPLY", "back": "BACK", "BR": "BR", "bridge_ip_configuration": "Bridge IP Configuration", "cellular_only": "Cellular Only", "cellular_setting": "Cellular Setting", "confirm": "Confirm", "confirm_desc": "Before applying, please check your configuration.", "connect_type": "Connect Type", "enable_dhcp_server_at_bridge_interface": "Enable DHCP Server at Bridge Interface", "enable_dhcp_server_at_lan_interface": "Enable DHCP Server at LAN Interface", "enable_n1_nat_for_bridge_interface_to_wan": "Enable N-1 NAT for Bridge Interface to WAN", "enable_n1_nat_for_lan_interface_to_wan": "Enable N-1 NAT for LAN Interface to WAN", "interface": "Interface", "ip_range_from": "IP Range From {{start}} to {{end}}", "LAN": "LAN", "lan_ip_configuration": "LAN IP Configuration", "next": "NEXT", "no_further_settings_are_required": "No further settings are required.", "offered_IP_Range_From": "Offered IP Range From {{start}} to {{end}}", "page_title": "Interface Type Quick Setting", "port_type": "Port Type", "port1": "Port 1", "port10": "Port 10", "port11": "Port 11", "port12": "Port 12", "port13": "Port 13", "port14": "Port 14", "port15": "Port 15", "port16": "Port 16", "port2": "Port 2", "port3": "Port 3", "port4": "Port 4", "port5": "Port 5", "port6": "Port 6", "port7": "Port 7", "port8": "Port 8", "port9": "Port 9", "portG1SFP": "G1", "portG2SFP": "G2", "pppoe_dialup": "PPPoE Dialup", "pptp_dialup": "PPTP Dialup", "scenario": "<PERSON><PERSON><PERSON>", "service": "Service", "WAN": "WAN", "wan_configuratio": "WAN Configuration", "wan_redundancy": "WAN Redundancy"}, "ipsec": {"advanced_setting": "Advanced Settings", "aggressive": "Aggressive", "authentication_mode": "Authentication Mode", "auto_cisco": "Auto (with Cisco)", "change_vpn_configuration": "Change VPN Configuration", "clear": "Clear", "compress": "Compress", "confindence_interval": "Confidence Interval", "confirm_desc": "System has detected that  OpenVPN client is currently enabled. If you enable IPsec, the existing OpenVPN settings will be disabled, which may result in VPN connection loss. If you still wish to enable IPsec, please click \"Confirm\".", "confirm_title": "Are you sure you want to enable IPsec?", "connect_interface": "Connect Interface", "data_exchange": "Data Exchange", "dead_peer_detection": "Dead Peer Detection", "desc_delete_ipsec": "Are you sure you want to delete the selected IPSec?", "dh_group": "DH Group", "dh_group_1": "DH 1 (modp768)", "dh_group_14": "DH 14 (modp2048)", "dh_group_15": "DH 15 (modp3072)", "dh_group_16": "DH 16 (modp4096)", "dh_group_17": "DH 17 (modp6144)", "dh_group_18": "DH 18 (modp8192)", "dh_group_2": "DH 2 (modp1024)", "dh_group_22": "DH 22 (modp1024s160)", "dh_group_23": "DH 23 (modp2048s224)", "dh_group_24": "DH 24 (modp2048s256)", "dh_group_31": "DH 31 (curve25519)", "dh_group_5": "DH 5 (modp1536)", "encryption_algorithm": "Encryption Algorithm", "extra": "Extra", "fqdn": "FQDN", "global_settings": "General", "hash_algorithm": "Hash Algorithm", "hold": "Hold", "identity_type": "Identity Type", "ike": "IKE", "ike_life_time": "IKE Lifetime", "ike_mode": "IKE Mode", "ike_version": "IKE Version", "ipsec_natt": "IPSec NAT-T", "ipsec_settings": "IPSec Connections", "ipsec_status": "IPSec Status", "key_exchange": "Key Exchange", "key_id": "Key ID", "l2tp_tunnel": "L2TP Tunnel", "local_gateway": "Local Gateway", "local_id": "Local ID", "local_network": "Local Network", "local_network_list": "Local Network List", "main": "Main", "page_title": "IPSec", "perfect_forward_secrecy": "Perfect Forward Secrecy", "phase": "Phase", "pre_shared_key": "Pre-shared Key", "prf": "PRF", "prf_sha256": "PRF SHA-256", "prf_sha384": "PRF SHA-384", "prf_sha512": "PRF SHA-512", "quick_setting": "Quick Settings", "remote_gateway": "Remote Gateway", "remote_id": "Remote ID", "remote_network": "Remote Network", "remote_network_list": "Remote Network List", "remote_vpn_gateway": "Remote VPN Gateway", "restart": "<PERSON><PERSON>", "retry_interval": "Retry Interval", "retry_interval_hint": "If Confidence Interval is less than Retry Interval, IPSec tunnel may remain in re-connecting state.", "route_mode": "Route Mode", "sa_life_time": "SA Lifetime", "security_setting": "Security Settings", "simple": "Simple", "site_to_site": "Site to Site", "size_limitation": "This device only allows {{ size }} IPSec connections.", "standard": "Standard", "start_in_initial": "Initiate Automatically", "startup_mode": "Startup Mode", "status_tooltip": "IPsec and OpenVPN cannot be enabled simultaneously.", "strong": "Strong", "title_create_ipsec": "Create IPSec Connection", "title_delete_ipsec": "Delete IPSec", "title_edit_ipsec": "Edit IPSec Connection", "tunnel_setting": "Tunnel Settings", "vpn_connection": "VPN Connection ", "vpn_event_log": "VPN Event Log", "wait_for_connecting": "Wait for Connection", "x509": "X.509", "x509_ca": "X.509 With CA"}, "l2tp_server": {"group_title": "WAN{{index}} Server Setting", "l2tp_lip": "Local IP", "l2tp_mode": "L2TP Server Mode", "l2tp_oiphi": "Offered IP: End", "l2tp_oiplo": "Offered IP: Start", "l2tp_server_setting": "Server Setting (WAN)", "l2tp_title_add_account_dialog": "Create New Account for L2TP", "l2tp_title_user_delete": "Delete L2TP User", "l2tp_user_delete_desc": "Are you sure you want to delete the selected L2TP user?", "l2tp_user_settings": "User Name Settings", "l2tp_users": "User Name", "page_title": "L2TP Server", "size_limitation": "This device only allows 10 L2TP users."}, "la": {"active": "Active", "active_member": "Active Member", "activity": "Activity", "actor": "Actor", "actor_state": "Actor State", "aggregatable": "Aggregatable", "aggregation": "Aggregation", "algorithm": "Algorithm", "collapse_all": "Collapse All", "collecting": "Collecting", "config_member_port": "Config Member Port", "config_member_port_hint": "Keep at least one port that cannot be added to a port-channel.", "configure_member": "Configure Member", "defaulted": "Defaulted", "delete_port_channel_confirm_desc_1": "Warning:", "delete_port_channel_confirm_desc_2": "Some features (like RSTP, VLAN...etc.) related to selected Link Aggregation will be set to default values.", "delete_port_channel_confirm_desc_3": "Are you sure you want to delete the selected Link Aggregation?", "distributing": "Distributing", "dmac": "DMAC", "enable": "Enable", "expand_all": "Expand All", "expired": "Expired", "false": "False", "group": "Group", "in_sync": "In Sync", "individual": "Individual", "key_channel_id": "Key (Channel ID)", "la_group_status": "LA Group Status", "la_size_limitation": "This device only allows {{ size }} port channels.", "lacp": "LACP", "lacp_mode_settings": "LACP Mode Settings", "long_time": "Long (90 sec.)", "long_timeout": "Long Timeout", "manual": "Manual", "not_collecting": "Not Collecting", "not_defaulted": "Not Defaulted", "not_distributing": "Not Distributing", "not_expired": "Not Expired", "only_select_eight": "A maximum of eight can be selected.", "out_of_sync": "Out of Sync", "page_title": "Link Aggregation", "partner": "Partner", "partner_port": "Partner Port", "partner_state": "Partner State", "partner_system_id": "Partner System ID", "passive": "Passive", "port": "Port", "port_channel": "Port Channel (Trunk)", "port_priority": "Port Priority", "setting_port_error": "LA: Invalid: Port-channel cannot be created when ports are operating at different speeds.", "setting_warning": "If you want to activate new port trunking settings, the all functions related to the trunking ports will be set to default values.", "short_time": "Short (3 sec.)", "short_timeout": "Short Timeout", "show_info": "Show Info", "smac": "SMAC", "smac_dmac": "SMAC + DMAC", "static": "Static", "synchronization": "Synchronization", "system_id": "System ID", "system_priority": "System Priority", "timeout": "Timeout", "true": "True", "trunk_group_settings": "Trunk Group Settings", "type": "Type", "wait_time": "Wait Time"}, "lan_bypass_gen3": {"auto_recovery_time": "Auto Recovery Time", "auto_recovery_time_tooltip": "It will never execute a recovery if the value is set to zero.", "confirm_desc": "If users are utilizing the LAN port to access the web console, enabling run-time bypass may disrupt the communication.", "confirm_header": "Enable System Runtime Bypass", "confirm_title": "Are you sure you want to enable System Runtime Bypass?", "disable": "Disabled", "enable": "Enabled", "mode": "Mode", "page_title": "LAN Bypass Gen3", "shutdown": "Shutdown", "shutdown_halted": "Shutdown and Halted", "system_failure_bypass_configuration": "System Failure Bypass Configuration", "system_runtime_bypass_configuration": "System Runtime Bypass Configuration"}, "layer3to7policy": {"allow_all": "Allow All", "create_policy": "Create Layer 3-7 Policy", "default_action": "Default Action", "default_action_log": "Default Action Log", "default_action_log_destination": "Default Action Log Location", "default_action_severity": "Default Action Severity", "delete_msg": "Are you sure you want to delete the selected policy?", "delete_title": "Delete Layer 3-7 Policy", "deny_all": "<PERSON><PERSON>", "dest_ip_address": "Destination IP Address", "dest_service": "Destination Service", "destination_address": "Destination Address", "destination_port": "Destination Port or Protocol", "edit_policy": "<PERSON> Layer 3-7 <PERSON>", "enforce": "Status", "enforcement": "Status", "event": "Event", "event_setting": "Global Policy Event Settings", "filter_mode": "Filter Mode", "global_setting": "Global Policy Settings", "iface_in_bridge_out": "Out-If can be Bridge Port only if In-If is Bridge Port.", "incoming_interface": "Incoming Interface", "ip_and_port_filtering": "IP and Port Filtering", "ip_and_source_mac_binding": "IP and Source MAC Binding", "ip_type_error": "Source Port IP Protocol ({{ source }}) is different from Destination Port IP Protocol ({{ dest }})", "outgoing_interface": "Outgoing Interface", "page_title": "Layer 3-7 Policy", "policy_name_exist": "Policy Name Already Exists", "protocol_Service": "Protocol and Service", "size_limitation": "The maximum number of policy for this device is {{ size }}.", "source_address": "Source Address", "source_ip_address": "Source IP Address", "source_mac_address": "Source MAC Address", "source_mac_filtering": "Source MAC Filtering", "source_port": "Source Port"}, "lfp": {"lfp_bridge_member_error": "LFP ports should be members of the bridge", "lpf_status": "<PERSON> Fault Passthrough Status", "page_title": "<PERSON> Passthrough", "status_title": "<PERSON> Fault Passthrough Status"}, "license_management": {"activate": "Activate", "activate_intro_link": "Moxa License Site", "activate_intro_pre": "Download the license from ", "activate_intro_suf": ", and paste the Activation Code here.", "activation_code": "Activation Code", "add_license_title": "Add New License", "add_new_license": "ADD NEW LICENSE", "available_points": "Available Points", "category": "Category", "copy_serial_number": "Copy Serial Number", "copy_serial_number_intro_link": "Moxa License Site", "copy_serial_number_intro_pre": "Copy the Serial Number to ", "copy_serial_number_intro_suf": ".", "cv": "CV", "daily_consume_point": "Daily Consume Point", "depleted": "Depleted", "end_date": "End Date", "estimated_points_depleted_date": "Estimated Points Depleted Date", "estimated_points_run_out_date": "Estimated Points Run-out Date", "expired": "Expired", "expired_warning": "The {{ category }} license has expired. The IPS pattern will stay at its current version and will not update with Security Package.", "expiring_warning": "The {{ category }} license expires in {{ days }} days. The IPS pattern will stay at its current version and will not update with Security Package.", "get_new_license": "Get New License Here", "history": "License History", "lic_import_result_err_combination_violation": "License activation failed – Combination violation when license import!", "lic_import_result_err_decode": "License activation failed – Invalid activation code!", "lic_import_result_err_duplicated": "License activation failed – Duplicated activation code!", "lic_import_result_err_internal": "License activation failed – Internal error!", "lic_import_result_err_invalid": "License activation failed – Invalid license!", "lic_import_result_err_out_of_graceful_period": "License activation failed – The 6-month grace period has expired!", "lic_import_result_err_point_over_limit": "License activation failed – License point over limit (3650)!", "lic_import_result_ok": "License activation OK!", "license_points": "License Points", "license_site_step_1_link": "Moxa License Site", "license_site_step_1_pre": "1. <PERSON><PERSON>", "license_site_step_1_suf": ".", "license_site_step_2": "2. Choose \"Activate a Product License\" and product type \"IPS\" on the site.", "license_site_step_3": "3. Key in the Registration Code and Serial Number on Moxa License Site. Serial Number would be get at the next step.", "license_type": "License Type", "login_license_site": "Login Moxa License Site", "new": "New", "overview": "Overview", "page_title": "License Management", "renew": "<PERSON>w", "standard": "Standard", "trial": "Trial", "update_date": "Update Date", "valid": "<PERSON><PERSON>"}, "lldp": {"lldp_bypass": "LLDP Ring Port Bypass", "nbr_id": "Nbr. ID", "nbr_port": "Nbr. Port", "nbr_port_desc": "Nbr. Port Description", "nbr_systen": "Nbr. System", "page_title": "LLDP", "transmit_interval": "Transmit Interval"}, "local_certificate": {"certificate": "Certificate", "certificate_from_csr": "Certificate From CSR", "certificate_from_p12": "Certificate From PKCS#12", "csr_common_name": "CSR Common Name", "expired_date": "Expiration Date", "import_identity_certificate": "Import Identity Certificate", "import_password": "Import Password", "issued_by": "Issued By", "issued_to": "Issued To", "label": "Label", "page_title": "Local Certificate", "public_key_length": "Key Length", "select_certificate": "Select Certificate", "size_limitation": "This device only allows  {{ size }} certificates.", "title_create_dialog_title": "Generate Certificate", "title_delete_dialog_desc": "Are you sure you want to delete the selected certificate?", "title_delete_dialog_title": "Delete Certificate"}, "login_authentication": {"authentication_protocol": "Authentication Protocol", "page_title": "Login Authentication", "radius_local": "RADIUS, Local", "tacacs_local": "TACACS+, Local"}, "login_policy": {"auth_fail_message": "Login Authentication Failure Message", "auto_logout_setting": "Auto Logout After", "failure_lockout": "Login Failure Account Lockout", "lockouttime": "Lockout Duration", "login_message": "Login Message", "page_title": "Login Policy", "retry_failure_threshold": "Login Failure Retry Threshold"}, "mac_address_table": {"aging_time": "Aging Time", "learnt_multicast": "Learnt Multicast", "learnt_unicast": "Learnt Unicast", "page_title": "MAC Address Table", "static_multicast": "Static Multicast", "static_unicast": "Static Unicast"}, "malformedPackets": {"page_title": "Malformed Packets"}, "moxa_remote_connect": {"activation": "Key Verification", "activation_key": "Activation Key", "activation_tooltip": "Activation key verification status.", "activation_type": "Activation Type", "address": "IP Address", "allowedClientList": "Allowed Client List", "apply_confirm_message": "After applying, the IP of the OnCell web console will change to the Bridge IP set on this page. Please use {{ip}} to access the web console.", "bride_member": "Bridge Member", "bridge_ip_conf_message": "Ports for MRC remote-access devices must be set to bridge mode.", "bridge_ip_configuration": "Bridge IP Configuration", "bridge_member_message": "Devices can only be remotely accessed when connected to Bridge ports.", "connected": "Connected", "connected_tooltip": "VPN tunnel status.", "controlled_by_di_on": "Controlled by DI", "controlled_by_di_on_tooltip": "Establish tunnels for remote access only when Digital Input is detected as 'On'.", "controlled_by_usb_key": "Controlled by key file from USB drive", "controlled_by_usb_key_tooltip": "Establish tunnel for remote access only when a USB drive containing the key file is inserted.", "device_name": "Local Device Name", "device_type": "Device Type", "gateway_name": "Gateway Name", "global_settings": "Global Settings", "health_check": "Connectivity Check", "import_from_usb": "Import from USB Drive", "internet": "Internet", "internet_tooltip": "Internet connectivity.", "ip_address": "IP Address", "ip_ethernet_device": "IP Ethernet Device", "l2_ethernet_device": "Layer 2 Ethernet Device", "local_device_list": "Local Device List", "manual": "Enter Activation Key", "mrc": "MRC", "mrc_cloud": "MRC Cloud", "mrc_cloud_tooltip": "Connection to the MRC Quick Link cloud server.", "mrc_information": "MRC Information", "mrc_service": "MRC Service", "mrc_status": "MRC Status", "nat_ip_address": "NAT IP", "online": "Online", "online_tooltip": "Status of link with MRC Quick Link cloud server.", "page_title": "Moxa Remote Connect", "page_title_abbr": "Moxa Remote Connect (MRC)", "permanent_connection": "Persistent Connection", "permanent_connection_tooltip": "Always establish tunnel for remote access.", "ping_check_value": "Ping Check ({{ sec }} sec.)", "port_link_value": "Port Link: {{ port }}", "portRange": "Port", "protocol": "Protocol", "protocol_set": {"ALL": "All", "ALL_ICMP": "All ICMP", "ALL_TCP": "All TCP", "ALL_UDP": "All UDP", "CUSTOM_TCP": "Custom TCP", "CUSTOM_UDP": "Custom UDP", "EIP_IO_MSG": "EtherNet/IP I/O Messaging", "EIP_MSG_TCP": "EtherNet/IP Messaging (TCP)", "EIP_MSG_UDP": "EtherNet/IP Messaging (UDP)", "HTTP": "HTTP", "HTTPS": "HTTPS", "MODBUS_TCP": "Modbus/TCP", "MODBUS_UDP": "Modbus/UDP", "RDP_TCP": "RDP-TCP", "RDP_UDP": "RDP-UDP", "SNMP": "SNMP", "SSH": "SSH", "TELNET": "Telnet"}, "serial_device": "Serial Device", "serial_status": "Serial Status", "service": "Service", "subnet_mask": "Subnet Mask", "tunnel_control": "Tunnel Control", "tunnel_control_settings": "Tunnel Control Settings", "virtual_ip": "Virtual IP"}, "multicast_forwarding_table": {"group_address": "Group Address", "inbound_byte": "Inbound Bytes", "inbound_interface": "Inbound Interface", "inbound_packet": "Inbound Packets", "outbound_interface": "Outbound Interface(s)", "page_title": "Multicast Forwarding Table", "source_address": "Source Address"}, "multicast_route_settings": {"page_title": "Multicast Route Settings", "static_multicast_route": "Static Multicast Route"}, "multicast_table": {"delete_multicast_table_desc": "Are you sure you want to delete the selected static multicast?", "multicast_mac_hint": "01:00:5E:XX:XX:XX in here is IP multicast MAC address, please activate IGMP Snooping for automatic classification.", "page_title": "Static Multicast Table", "size_limitation": "The maximum number of static multicast entries for this device is {{ size }}.", "title_create_multicast_table": "Create Static Multicast", "title_delete_multicast_table": "Delete Static Multicast", "title_edit_multicast_table": "Edit Static Multicast"}, "nat": {"double_nat": "Double NAT", "dst_ip_map_type": "Destination IP Mapping Type", "dst_port_map_type": "Destination Port Mapping Type", "dynamic": "Dynamic", "incoming_ifname": "Incoming Interface", "mode_1_1": "1-to-1", "mode_advance": "Advanced", "mode_ip_twin": "Twin IP Mapping", "mode_n_1": "N-to-1", "mode_pat": "PAT", "nat_create_dialog_title": "Create Index {{portIndex}}", "nat_loopback": "NAT Loopback", "nat_setting_dialog_title": "Edit Index {{portIndex}}", "nat_size_limitation": "The maximum number of NAT rules for this device is {{ size }}.", "ori_dst_ip_port": "Dst. IP:Port (Original Packet)", "ori_src_ip_port": "Src. IP:Port (Original Packet)", "ori_src_mac": "Src. MAC (Original Packet)", "original_packet": "Original Packet (Condition)", "outgoing_ifname": "Outgoing Interface", "outgoing_interface_ip_twins_mapping": "Outgoing Interface (Twin IP Mapping)", "page_title": "Network Address Translate", "page_title_abbr": "NAT", "range": "Range", "single": "Single", "snat_hint": "Without enabling this item, only a DNAT rule will be created.", "source_nat": "Auto Create Source NAT", "src_ip_map_type": "Source IP Mapping Type", "src_port_map_type": "Source Port Mapping Type", "tip_nat_advance_odi_secondary_ip": "If (1) your host is directly connect to router or connected via L2 switch, \n(2) Original Destination IP is in the hosts' subnet but different from incoming interface IP, \nyou may add the Original Destination IP as a Secondary IP on incoming interface \n(Network Configuration -> Interface -> Secondary IP Tab). \nSo router can receive and NAT hosts' traffic.", "tip_nat_advance_tsi_secondary_ip": "If (1) destination host of desired traffic is directly connect to router or connected via L2 switch, \n(2) Translated Source IP is in the destination host's subnet but different from outgoing interface IP, \nyou may add the Translated Source IP as a Secondary IP on outgoing interface\n(Network Configuration -> Interface -> Secondary IP Tab). \nSo router can receive and NAT destination host's traffic (the responses).", "title_delete_entry_dialog_desc": "Are you sure you want to delete the selected entry?", "title_delete_entry_dialog_title": "Delete Entry", "trans_dst_ip_port": "Dst. IP:Port (Translated Packet)", "trans_src_ip_port": "Src. IP:Port (Translated Packet)", "trans_src_mac": "Src. MAC (Translated Packet)", "translated_packet": "Translated Packet (Action)", "type_range_1": "Range-to-1", "type_subnet_1": "Subnet-to-1", "vrrp_binding": "VRRP Binding"}, "netflow": {"activate_timeout": "Active NetFlow Entry Timeout", "all": "All", "basic": "Basic", "both": "Bidirectional", "collector_ip_host_name_1": "Collector 1 IP/Host Name", "collector_ip_host_name_2": "Collector 2 IP/Host Name", "collector_port_1": "Collector 1 Port", "collector_port_2": "Collector 2 Port", "collector_settings": "Collector Settings", "create": "Create NetFlow Entry", "delete_msg": "Are you sure you want to delete the selected NetFlow entry?", "delete_title": "Delete NetFlow Entry", "destination_ip": "Destination IP", "destination_ip_filtering": "Destination IP Filter", "destination_port": "Destination Port", "edit": "Edit NetFlow Entry", "egress": "Egress", "filtered": "Filtered", "inactivate_timeout": "Inactivity Timeout", "ingress": "Ingress", "maximum_collectors": "Maximum number of collectors is {{ size }}.", "mode": "Mode", "netflow_settings": "NetFlow Settings", "page_title": "NetFlow", "port_tooltip": "Please input the designated port, or set the port to '0' to indicate no restriction.", "protocol": "Protocol", "protocol_filtering": "Protocol Filter", "sampling_rate": "Sampling Rate", "source_ip": "Source IP", "source_ip_filtering": "Source IP Filter", "source_port": "Source Port", "subnet_mask": "Subnet Mask", "tcp": "TCP", "traffic_direction": "Traffic Direction", "udp": "UDP"}, "network_interface": {"address_information": "Address Information", "adminStatus": "Admin Status", "advanced": "Advanced", "advanced_description": "In addition to establishing default firewall settings, this mode provides users with advanced options (destination address, service, protocol, port) to create whitelist settings, enabling network traffic management for DMZ applications.", "apply_dmz_warning": "After applying the settings in the DMZ Setup Wizard, any previously configured Layer 3-7 firewall policies on the device will be overwritten.", "basic": "Basic", "basic_description": "This mode will guide users in establishing the default firewall settings to achieve network traffic management for DMZ applications.", "bridge": "Bridge", "bridge_ip_configuration": "Bridge IP Configuration", "bridge_mamber": "Bridge Member", "bridge_member_hint": "If a port is configured as a bridge interface member, it cannot be used for L2 redundancy features: Turbo Ring, Turbo Chain, or RSTP", "bridge_status_hint": "When a bridge interface is disabled, the associated policies are also removed.", "bridge_type": "Bridge Type", "configuration_mode": "Configuration Mode", "connection": "Connection", "connection_mode": "Connection Mode", "connection_type": "Connection Type", "create_firewall_policy": "Create Firewall Policy", "create_firmwall_policy": "Create Firewall Policy", "create_gre_interface_entry": "Create GRE Interface Entry", "create_loopback_interface_entry": "Create Loopback Interface Entry", "create_wan_interface": "Create WAN Interface", "custom": "Custom", "delete_gre_interface_desc": "Deleting the selected interface will delete all associated settings. Delete interface and settings?", "delete_ip_interface_desc": "Are you sure you want to delete the this entry?", "delete_loopback_interface_desc": "Are you sure you want to delete the selected loopback interface?", "dhcp_opt_66_67": "DHCP Client Option 66/67", "directed_broadcast": "Directed Broadcast", "disable_port_base_hint": "Cannot modify a member of a port-based bridge whose status is disabled.", "dmz": "DMZ", "dmz_create_firewall_policy_limit": "The maximum number of user-defined rules for configuration is 20.", "dmz_interface_type_warning": "For the WAN2 interface, only one of WAN Redundancy and DMZ functionality can be enabled. Please confirm if WAN Redundancy mode has been activated.", "dmz_setup_wizard": "DMZ Setup Wizard", "dns": "DNS", "dns_settings": "DNS Settings", "dos": "DoS", "dos_policy": "DoS Policy", "dynamic_ip": "Dynamic IP", "edit_gre_interface_entry": "Edit GRE Interface Entry", "edit_loopback_interface_entry": "Edit Loopback Interface Entry", "enable_dos_ips_setting": "Enable DoS & IPS Setting", "enable_dos_ips_setting_intro_pre": "This device does not have a valid IPS license. Please go to the License Management page to confirm the current licensing information.", "encrypt": "Encrypt", "ftp": "FTP", "gateway": "Gateway", "goose_message_pass_through": "Goose Message Pass-Through", "gre_interface": "GRE Interface", "gre_interface_size_limitation": "Maximum {{ size }} GRE interfaces.", "gre_ip_address": "GRE IP Address", "host_name": "Host Name", "http": "HTTP", "https": "HTTPS", "id_hint": "An ID is used to indentify each loopback interface.", "interface_type": "Interface Type", "interface_used": "This LAN interface {{ifname}} cannot be deleted because it is configured with DHCP Option 82. Please reconfigure the interface to another type and try again.", "intrusion_prevention_system": "Intrusion Prevention System (IPS)", "ip_address": "IP Address", "ip_protocol": "IP Protocol", "ips": "IPS", "lan": "LAN", "loopback_id": "Loopback ID", "loopback_interface": "Loopback Interface", "loopback_size_limitation": "This device only allows {{ size }} loopbacks.", "mppe_encryption": "MPPE Encryption", "mtu": "MTU", "mtu_configuration": "MTU Configuration", "multi_wan_hint": "Configure as WAN1 interface, and the previous WAN settings will be overwritten", "network_service": "Network Service", "non_management_vid_warning_desc": "Cannot modify the VLAN ID as it is currently the management VLAN ID.", "operStatus": "Operation Status", "page_title": "Network Interfaces", "port": "Port", "port_base": "Port-Based", "pppoe": "PPPoE", "pppoe_dialup": "PPPoE Dialup", "pptp": "PPTP Dialup", "primary_dns_server": "Primary DNS Server", "proxy_arp": "Proxy ARP", "prp": "PRP Traffic", "remindChangeOption": "For higher security, it is recommended to turn on the DoS and IPS functions.", "secondary_dns_server": "Secondary DNS Server", "secondary_ip": "Secondary IP", "select_mode": "Select Mode", "smtp": "SMTP", "source_ip_overwrite": "Source IP Overwrite", "ssh": "SSH", "static_ip": "Static IP", "tcp": "TCP", "tcp_udp": "TCP and UDP", "telnet": "TELNET", "tertiary_dns_server": "Tertiary DNS Server", "tunnel_destination": "Tunnel Destination", "tunnel_source": "Tunnel Source", "udp": "UDP", "virtual_interface": "Virtual Interface", "vlan_interface": "VLAN Interface", "vlan_size_limitation": "This device only allows {{ size }} VLANs.", "vmac": "Virtual MAC", "wan": "WAN", "wan_delete_comfirm_desc": "Are you sure you want to delete the selected WAN interface?", "wan_delete_comfirm_title": "Delete WAN Interface", "wan_id": "WAN ID", "wan_rddt_warning_desc": "The WAN configuration has been changed. The device now use cellular as the default route interface for WAN connection. If you want to change the default route interface to Ethernet WAN, please enable the WAN Redundancy function.", "wan_rddt_warning_title": "Enable the WAN Redundancy", "wan1": "WAN1", "wan2": "WAN2", "wan2_dmz": "WAN2/DMZ", "wan2_gateway_hint": "The Gateway setting takes effect only in WAN backup mode and when the interface is enabled.", "zone_base": "Zone-Based", "zone_index": "Zone {{ index }}"}, "network_security_manager": {"page_title": "Network Security Manager"}, "ntp_server": {"client_auth": "Client Authentication", "disable_sntp": "NTP/SNTP Server cannot be enabled when the System Time Clock Source is set to SNTP.", "page_title": "NTP/SNTP Server"}, "object": {"addr": "IP Address and Subnet", "code": "Code", "create_object": "Create Object", "custom_ip_protocol": "Custom IP Protocol", "decimal": "(Decimal)", "delete_msg": "Are you sure you want to delete the selected object?", "delete_object": "Delete Object", "delete_title": "Delete Object", "detail": "Details", "edit_object": "Edit Object", "end_port": "Port: End", "icmp": "ICMP", "ip_end": "IP Address: End", "ip_protocol": "IP Protocol", "ip_range": "IP Range", "ip_start": "IP Address: Start", "ip_type": "IP Type", "is_referenced": "One or more of the selected objects are referenced", "leave_as_any": "Leave blank to represent Any", "need_selected_msg": "Select at least one item", "object_name_exist": "Object Name Already Exists", "object_reference_msg": "This object is referenced by a policy index in the following profile(s):", "object_reference_title": "Object References", "object_size_limitation": "The maximum number of objects for this device is {{ size }}.", "object_type": "Object Type", "page_title": "Object Management", "port_range": "TCP and UDP Port Range", "ref_count": "References", "select_indu_app": "Select Industrial Application Service(s)", "select_net_serv": "Select Network Service(s)", "service_port_type": "Service Port Type", "single_ip": "Single IP", "single_port": "Single TCP and UDP Port", "start_port": "Port: Start", "subnet": "Subnet", "tcp": "TCP", "tcp_udp": "TCP and UDP", "udp": "UDP", "user_serv": "User-defined Service"}, "oob_management": {"admin_status": "Admin Status", "flow_control": "Flow Control", "link_status": "Link Status", "mdi_mdix": "MDI/MDIX", "oob_management_information": "Out of Band Management Information", "page_title": "Out of Band Management"}, "open_vpn_client": {"change_vpn_configuration": "Change VPN Configuration", "confirm_desc": "System has detected that IPsec VPN is currently enabled. If you enable OpenVPN Client, the existing IPsec VPN settings will be disabled, which may result in VPN connection loss. If you still wish to enable OpenVPN Client, please click \"Confirm\".", "confirm_title": "Are you sure you want to enable OpenVPN Client?", "data_received": "Data Received / Sent", "description": "Description", "duration_time": "Duration", "import_open_vpn_profile": "Import OpenVPN Profile", "import_open_vpn_profile_tooltip": "Import OpenVPN connection profiles (.ovpn files) from your OpenVPN server or provider into the client to configure your connection.", "latest_connect": "Last Connection", "mode": "OpenVPN Mode", "open_vpn_client_ip_address": "OpenVPN Client IP Address", "open_vpn_server": "OpenVPN Server", "page_title": "OpenVPN Client", "password_optional": "Password (optional)", "settings": "Settings", "status": "Status", "status_tooltip": "IPsec and OpenVPN cannot be enabled simultaneously.", "username_optional": "Username (optional)"}, "operational_status": {"consist_class": "Consist Class", "consist_id": "Consist ID", "consist_info": "Consist Info", "consist_list": "Consist List", "consist_network_count": "Consist Network Count", "consist_network_id": "Consist Network ID", "consist_number": "Consist Number", "consist_orientation": "Consist Orientation", "consist_owner": "Consist Owner", "consist_topography_counter": "Consist Topography Counter", "consist_type": "Consist Type", "consist_uuid": "Consist UUID", "consist_vehicle_number": "Consist Vehicle Number", "etb_id": "ETB ID", "etb_list": "ETB List", "function_id": "Function ID", "function_list": "Function List", "group": "Group", "lead": "Lead", "lead_direction": "Lead Direction", "name": "Name", "operational_consist_list": "Operational Consist List", "operational_consist_number": "Operational Consist Number", "operational_consist_orientation": "Operational Consist Orientation", "operational_train_directory": "Operational Train Directory", "operational_train_orientation": "Operational Train Orientation", "operational_train_orientation_counter": "Operational Train Topography Counter", "operational_vehicle_list": "Operational Vehicle List", "operational_vehicle_number": "Operational Vehicle Number", "page_title": "Operational Status", "tcn_uri": "TCN-URI", "tcn_uri_table": "TCN-URI Table", "traction": "Traction", "train_directory": "Train Directory", "train_network_ip": "Train Network IP", "train_topography_counter": "Train Topography Counter", "train_vehicle_number": "Train Vehicle Number", "vehicle_id": "Vehicle ID", "vehicle_list": "Vehicle List", "vehicle_orientation": "Vehicle Orientation"}, "ospf": {"adv_router": "ADV Router", "age": "Age", "aggregation": "Aggregation", "aggregation_size_limitation": "The maximum number of aggregations for this device is {{ size }}.", "area": "Area", "area_id": "Area ID", "area_size_limitation": "The maximum number of areas for this device is {{ size }}.", "area_type": "Area Type", "auth_key": "Auth Key", "auth_type": "Auth Type", "connected": "Connected", "current_router_id": "Current Router ID", "current_router_id_hint": "The Router ID will be auto selected the highest Interface IP address when set Router-ID as 0.0.0.0.", "database": "Database", "dead_interval": "Dead Interval", "delete_aggregation_desc": "Are you sure you want to delete the selected aggregation?", "delete_area_desc": "Are you sure you want to delete the selected area?", "delete_interface_desc": "Are you sure you want to delete the selected interface?", "delete_vLink_desc": "Are you sure you want to delete the selected virtual link?", "hello_interval": "Hello Interval", "interface_alias": "Interface <PERSON>", "interface_name": "Interface Name", "link_id": "Link ID", "lsa_type": "LSA Type", "md5_key_id": "MD5 Key ID", "neighbor": "Neighbor", "neighbor_id": "Neighbor ID", "normal": "Normal", "nssa": "NSSA", "ospf_settings": "OSPF Settings", "ospf_status": "OSPF Status", "page_title": "OSPF", "passive_mode": "Passive Mode", "redistribute": "Redistribute", "rip": "RIP", "role": "Role", "route": "Route", "router_id": "Router ID", "simple": "Simple", "static": "Static", "stub": "<PERSON><PERSON>", "title_create_ospf_aggregation": "Create Aggregation", "title_create_ospf_area": "Create Area", "title_create_ospf_interface": "Create Interface", "title_create_ospf_vlink": "Create Virtual Link", "title_delete_ospf_aggregation": "Delete Aggregation", "title_delete_ospf_area": "Delete Area", "title_delete_ospf_interface": "Delete Area", "title_delete_ospf_vlink": "Delete Virtual Link", "title_edit_ospf_aggregation": "Edit Aggregation", "title_edit_ospf_area": "Edit Area", "title_edit_ospf_interface": "Edit Interface {{ interfaceName }}", "title_edit_ospf_vlink": "Edit Virtual Link", "virtual_link": "Virtual Link", "vLink_size_limitation": "The maximum number of virtual links for this device is {{ size }}."}, "package_control": {"feat_deps_pkg_dsec": "An abnormal status of the Network Security Package will disable all L3-L7 Policy and Session Control functions in the Firewall section.", "feat_deps_pkg_rdr": "Click REDIRECT to go to the Software Package Upgrade page to reinstall the Network Security Package.", "feat_deps_pkg_title": "Network Security Package is not Enabled", "firmware": "Firmware", "package_incompatible_desc": "Click REDIRECT to go to the Software Package Upgrade page to upgrade the following incompatible package(s):", "package_incompatible_firewall_invalid_notify": "Using an incompatible Network Security Package will disable all L3-L7 policy and session control functions in the Firewall section.", "package_incompatible_notify": "One or more installed software packages are incompatible with the current firmware version.", "package_incompatible_title": "Incompatible Package", "package_incompatible_version": "{{ packageName }}(v{{ packageVersion }})", "package_version": "Package Version", "page_title": "Software Package Management", "try_again_hint": "Please apply after few seconds!", "uninstalled": "Uninstalled"}, "password_policy": {"max_life_time": "Password Max-life-time", "minimum_length": "Minimum Length", "page_title": "Password Policy", "password_complexity_strengh_check": "Password Complexity Strength Check", "policy_enable": "Password complexity strength check", "policy_lowercase": "At least one lower case letter (a-z)", "policy_numbers": "Must contain at least one digit (0-9)", "policy_symbols": "Must contain at least one special character (~!@#$%^&*-_|:;,.<>{}[]()+=)", "policy_uppercase": "At least one upper case letter (A-Z)", "policy_uppercase_lowercase": "Must include both upper and lower case letters (A-Z, a-z)"}, "ping_response": {"add_ping_response": "Add Ping Response Policy", "allow": "Allow", "allowed_all_ping_response_interfaces": "Interfaces Allowing De<PERSON>ult <PERSON>", "default_rule_event_setting": "Ping Response Logging and Events", "deny": "<PERSON><PERSON>", "edit_ping_response": "Edit Ping Response Policy", "incoming-interface": "Incoming Interface", "interface_setting": "Allow Ping Response by <PERSON><PERSON><PERSON>\n", "ip_address_netmask": "IP Address/Netmask", "ip_type": "IP Type", "netmask": "Netmask", "page_inside_title": "Ping Response", "page_title": "Ping Response", "single_ip": "Single IP", "size_limitation": "The maximum number of Ping Response policies rules for this device is {{ size }}.", "subnet": "Subnet"}, "ping": {"ip_address_name": "IP Address/Domain Name", "page_title": "<PERSON>", "ping_result": "Ping {{ targetHost }} result"}, "poe": {"actual_power_budget": "Actual Power Budget", "actual_power_budget_hint": "The lower value between \"Actual Power Budget\" and \"System Power Budget\" will become the \"Power Budget Limit\".", "alive": "Alive", "allocated_power": "Allocated Power", "allocated_power_hint": "Calculate power budget of all ports and ensure the total allocated power is under the power budget limit.", "also_apply_port": "Apply the rule to the port", "auto_power_cutting": "Auto Power Cutting", "auto_power_cutting_hint": "Auto Power Cutting removes the lowest priority and smallest index port power output if the power consumption exceeds the system's power budget.", "avaliable_power_hint": "“Remaining Available Power” is “Maximum Input Power” minus “{{ power }}”.", "change_power_cutting_dialog_title": "Set the Auto Power Cutting", "change_power_mode_dialog_title": "Set the Power Management Mode", "check_frequency": "Check Frequency", "classification": "Classification", "configuration_suggestion": "Configuration suggestion", "consumed_power": "Consumed Power", "consumed_power_hint": "Calculate real-time power consumption of all ports.", "consumption_w": "Consumption (W)", "current_ma": "Current (mA)", "daily": "Daily", "daylight_saving_time": "Daylight Saving Time", "device_ip": "Device IP", "device_type": "Device Type", "disable_auto_power_cutting": "Are you sure you want to disable the “Auto Power Cutting”? If so, the “Power Management Mode” will become “Allocated Power” mode.", "disable_poe": "Disable PoE power output", "dot3af": "802.3 af", "dot3at": "802.3 at", "dot3bt": "802.3 bf", "dot3btds": "802.3 bt DS", "dot3btss": "802.3 bt SS", "enable_auto_power_cutting": "Are you sure you want to enable the “Auto Power Cutting”? If so, the “Power Management Mode” will become “Consumed Power” mode.", "enable_legacy": "Enable legacy PD detection", "enable_poe": "Enable PoE power output", "force": "Force", "fri": "<PERSON><PERSON>", "friday": "Friday", "high": "High", "high_power": "High Power", "legacy": "Legacy PD", "legacy_pd_detection": "Legacy PD Detection", "local_timeZone": "Local TimeZone", "low": "Low", "maximum_input_power": "Maximum Input Power", "mon": "Mon", "monday": "Monday", "na": "N/A", "nic": "NIC", "no_action": "No Action", "no_response_times": "No Response Times", "no_suggestion": "No suggestion", "not_alive": "Not alive", "not_present": "Not present", "off": "Off", "on": "On", "output_mode": "Output Mode", "over_current": "Over current", "page_title": "PoE", "pd_failure_check": "PD Failure Check", "pd_failure_check_status": "PD Failure Check Status", "poe_supported": "PoE Supported", "power_allocation": "Power Allocation", "power_budget_limit": "Power Budget Limit", "power_management_mode": "Power Management Mode", "power_output": "Power Output", "raise_eps_voltage": "Raise the external power supply voltage so that it is greater than 46 VDC", "remaining_available_power": "Remaining Power Available", "repeat_execution": "Repeat Execution", "restart_pd": "Restart PD", "rule": "Rule", "rule_name": "Rule Name", "sat": "Sat", "saturday": "Saturday", "sch_invalid_end_time": "The start time cannot exceed the end time.", "sch_rule_conflict": "PoE scheduling rule conflict.", "sch_rule_unknown_err": "PoE scheduling rule unknown error", "schedule_size_limitation": "This device only allows {{ size }} schedules.", "schedule_time": "Schedule Time", "scheduling": "Scheduling", "select_allocated_power_mode": "Are you sure you want to select the “Allocated Power” mode? If so, the “Auto Power Cutting” will be disabled.", "select_auto": "Select output mode \"Auto\"", "select_consumed_power_mode": "Are you sure you want to select the “Consumed Power” mode? If so, the “Auto Power Cutting” will be enabled.", "select_force": "Select output mode \"Force\"", "select_high_power": "Select output mode \"High power\"", "shutdown_pd": "Shut down PD", "sun": "Sun", "sunday": "Sunday", "system_power_budget": "System Power Budget", "system_power_budget_hint": "The system power budget depends on the source capability of the external power supply (EPS).", "system_status": "System Status", "system_time": "System Time", "system_time_status": "System Time Status", "thu": "<PERSON>hu", "thursday": "Thursday", "title_create_rule": "Create Rule", "title_edit_rule": "Edit Rule", "tue": "<PERSON><PERSON>", "tuesday": "Tuesday", "voltage_v": "Voltage (V)", "wed": "Wed", "wednesday": "Wednesday", "weekdays": "Weekdays", "weekend": "Weekend", "weekly": "Weekly"}, "port_mirror": {"all_streams": "All Streams", "dup_port_msg": "Mirror Destination Port cannot be one of Monitored Port.", "egress_stream": "Egress Stream", "ingress_stream": "Ingress Stream", "mirror_destination_port": "Mirror Destination Port", "monitored_port": "Monitored Port", "monitored_traffic": "Monitored Traffic", "page_title": "Port Mirroring", "port_mirror_configuration": "Port Mirroring Configuration"}, "port_setting": {"100m_full": "100M Full", "100m_half": "100M Half", "10m_full": "10M Full", "10m_half": "10M Half", "1g_full": "1G Full", "fiber_copy_to_other_port_disable": "Fiber port cannot copy configurations to other ports.", "fiber_mdi_disable": "Fiber port cannot set MDI/MDIX.", "fiber_speed_disable": "Fiber port cannot set Speed/Duplex.", "flow_control": "Flow Control", "flow_control_hint1": "Flow Control can be enabled or disabled but is only effective in full-duplex.\n", "flow_control_hint2": "Back Pressure is enabled by default but works only in half-duplex. When using the SFP ports for WAN1 or WAN2 on the EDR-G9004, Flow Control will be ineffective.", "link_status": "Link Status", "mdi": "MDI", "mdi_mdix": "MDI/MDIX", "mdix": "MDIX", "media_type": "Media Type", "page_title": "Port Settings", "port_copy_to_fiber_port_disable": " Cannot copy configurations to fiber port.", "speed_duplex": "Speed/Duplex", "speed_duplex_mode": "Speed/Duplex Mode"}, "power_management": {"add_cycle_title": "Add Cycle Rule", "add_one_time_title": "Add One-time Rule", "cycle_rule_delete_all_disable": "You must delete or disable any one-time Wake Up rules to delete this cycle rule.", "cycle_rule_size_limitation": "The device supports up to {{ size }} cycle rules.", "cycle_type": "Cycle Type", "desc_clear_log_1": "Are you sure you want to change Cycle Type to {{ cycleType }}?", "desc_clear_log_2": "Changing the Cycle Type will clear the cycle rule.", "desc_delete_cycle": "Are you sure you want to delete the selected cycle rule(s)?", "desc_delete_one_time": "Are you sure you want to delete the selected one-time rule(s)?", "di_sensing_time": "DI Sensing Time", "disable_pwr_mgmt": "Disable Power Management", "disable_pwr_mgmt_desc": "Power Management is enabled. Power Management must be disabled when performing firmware upgrades.", "edit_cycle_title": "Edit Cycle Rule", "edit_one_time_title": "Edit One-time Rule", "enable_pwr_mgmt": "Enable Power Management", "enable_pwr_mgmt_desc": "If necessary, re-enable Power Management.", "end_date": "End Date", "end_time": "End Time", "high": "High", "ignition": "Ignition", "ignition_tooltip": "Ignition feature is only applicable to hardware rev 1.1 and above.", "low": "Low", "one_time_rule": "One-time Rules", "onetime_rule_add_disable": "You must create a cycle rule first before creating or enabling one-time Wake Up rule.", "onetime_rule_size_limitation": "The device supports up to {{ size }} one-time rules.", "page_title": "Power Management", "power_saving": "Power Saving", "power_saving_delay_time": "Power Saving Delay Time", "rule_end": "Rule End", "rule_schedule": "Rule Schedule", "rule_start": "Rule Start", "scheduling": "Scheduling", "sleep_warning": "The system will enter power saving mode at {{ time }}. [Last updated time: {{ currentTime }}]", "start_date": "Start Date", "start_time": "Start Time", "title_clear_rule": "Clear Rule", "title_delete_cycle": "Delete Cycle Rule", "title_delete_one_time": "Delete One-time Rule", "uptime_warning": "System forced to leave power saving mode at least 15 minutes after system reboot.", "wake_up": "Wake Up", "wakeup_cycle_rule": "Wakeup Cycle Rules", "wakeup_di_status": "Wake Up DI Status", "weekup_end_time": "Wake Up End Time", "weekup_start_time": "Wake Up Start Time"}, "qos": {"cos": "CoS", "cos_mapping": "CoS Mapping", "create_subnet_based_configuration": "Create Subnet-Based Configuration", "dscp": "DSCP", "dscp_mapping": "DSCP Mapping", "dscp_remark": "DSCP Remark", "edit_port_based_configuration": "Edit Port Based Configuration", "edit_subnet_based_configuration": "Edit Subnet-Based Configuration", "inspect_cos": "Inspect CoS", "inspect_tos": "Inspect ToS", "key_size_limitation": "Maximum of {{ size }} Subnet-Based Configurations.", "level": "Level", "page_title": "QoS", "port_based_configuration": "Port-Based Configuration", "port_classification": "Port Classification", "priority_queue": "Priority Queue", "scheduling_mechanism": "Scheduling Mechanism", "scheduling_strict": "Strict(High Priority First Always)", "scheduling_weight": "Weight Fair(8:4:2:1)", "subnet_based_configuration": "Subnet-Based Configuration", "subnet_based_configuration_hint": "The DSCP by subnet rule does not take effect sequentially"}, "radius": {"page_title": "RADIUS Server", "radius": "RADIUS", "server_address_1": "Server Address 1", "server_address_2": "Server Address 2", "server_address_number": "Server IP Address {{ number }}", "share_key": "Shared Key", "type_eap_peap_mschapv2": "EAP-PEAP MSCHAPv2"}, "rate_limit": {"drop_packet": "Drop Packet", "egress": "Egress", "ingress": "Ingress", "ingress_action": "Ingress Action", "ingress_policy": "Ingress Policy", "limit_all": "Limit All", "limit_broadcast": "Limit Broadcast", "limit_broadcast_multicast": "Limit Broadcast, Multicast", "limit_broadcast_multicast_flooded_unicast": "Limit Broadcast, Multicast and Flooded Unicast", "not_limited": "Not Limited", "page_title": "Rate Limit", "port_disable": "Port Disable", "port_disable_period": "Port Disable Period"}, "relay": {"page_title": "<PERSON><PERSON> Cut-off"}, "resource_management": {"page_title": "Resource Management"}, "rip": {"connected": "Connected", "ospf": "OSPF", "page_title": "RIP", "redistribute": "Redistribute", "static": "Static", "title_edit_rip": "Edit RIP"}, "route_table": {"note_hint": "Multiple identical LAN IPs are configured, some routing information may not be fully displayed.", "page_title": "Routing Table"}, "rstp": {"bridge_member_hint": "If a port is configured as a bridge interface member, it cannot be used for L2 redundancy features: Turbo Ring, Turbo Chain, or RSTP", "bridge_priority": "Bridge Priority", "edge": "Edge", "false": "False", "force_edge": "Force Edge", "forward_delay_time": "Forward Delay Time", "hello_time": "Hello Time", "max_age": "Max Age", "page_title": "Spanning Tree", "path_cost": "Path Cost", "path_cost_help_info": "Path cost value will be automatically assigned according to the different port speed if the value is set to zero.", "port_state_blocking": "Blocking", "port_state_forwarding": "Forwarding", "port_state_learning": "Learning", "port_state_link_down": "Link Down", "port_state_listening": "Listening", "port_state_port_disabled": "Port Disabled", "root_information": "Root Information", "root_state": "Root State", "rstp_tr2_xor_info": "Either Spanning Tree or Turbo Ring must be enabled and cannot be enabled or disabled at the same time."}, "scats": {"auth_server": "Auth Server", "authentication_server": "Authentication Server", "controller": "Controller", "controller_information": "Controller Information", "page_title": "SCATS", "port": "Port", "primary_server_ip_address": "Primary Server IP Address", "primary_server_port": "Primary Server Port", "regional_computer": "Regional Computer", "regional_computer_information": "Regional Computer Information", "scats_hdlc_1200": "HDLC-1200", "scats_hdlc_9600": "HDLC-9600", "scats_non_hdlc_1200": "Non-HDLC-1200", "scats_non_hdlc_300": "Non-HDLC-300", "scats_service": "SCATS Service", "scats_service_disable": "To use the SCATS feature, disable the Serial Device Server feature first.", "scats_status": "SCATS Status", "secondary_server_ip_address": "Secondary Server IP Address", "secondary_server_port": "Secondary Server Port", "serial_port_configuration": "Serial Port Configuration", "site_id": "Site ID", "tertiary_server_ip_address": "Tertiary Server IP Address", "tertiary_server_port": "Tertiary Server Port", "vpn_used_tooltip": "The VPN LED is now used to indicate SCATS connection status. Please ensure all VPN services are disabled to prevent incorrect LED signals."}, "securityNotification": {"page_title": "MXview Alert Notification", "secNotiAccessViolation": "Access Violation Event Notification", "secNotiClearAllEvents": "Clear all events status", "secNotiDevLockdown": "Device Lockdown Event Notification", "secNotiDos": "DoS Attack Event Notification", "secNotiEvent": "Event", "secNotiEventSts": "Status", "secNotiFw": "Firewall Event Notification", "secNotiLoginFail": "Login Fail Event Notification", "secNotiSetting": "Security Notification Setting", "secNotiStatus": "Security Status", "secNotLayer3PolicyEvent": "Layer 3 Policy Event Notification"}, "serial_device_server": {"page_title": "Serial Device Server"}, "serial": {"any_character_inactivity_time": "Any Character/Inactivity Time", "any_character_none": "Any Character/None", "baud_rate": "Baudrate", "break_count": "Break Count", "buffering": "Buffering", "clear_log_desc": "Are you sure you want to clear the logs?", "clear_log_title": "Clear Logs", "connection_control": "Connection Control", "connection_down_settings": "Connection Down Settings", "connection_list": "Connection List", "connection_settings": "Connection Settings", "data_bits": "Data Bits", "data_packing": "Data Packing", "dcd_on_dcd_off": "DCD On/DCD Off", "dcd_on_none": "DCD On/None", "delete_destination_confirm_desc": "Are you sure you want to delete the selected entries?", "delimiter": "Delimiter", "delimiter_enable": "Delimiter {{ index }} Enable", "delimiter_index": "Delimiter {{ index }}", "delimiter_process": "Delimiter Process", "delimiter_settings": "Delimiter Settings", "dest_size_limitation": "The maximum number of destinations for this device is {{ size }}.", "destination_cmd_port": "Destination Command Port", "destination_data_port": "Destination Data Port", "destination_settings": "Destination Settings", "dsr_on_dsr_off": "DSR On/DSR Off", "dsr_on_none": "DSR On/None", "encryption_type": "Encryption Type", "end_ip_address": "Ending IP Address", "error_counter": "Error <PERSON>", "flow_control": "Flow Control", "force_transmit_interval": "Force Transmit Interval", "frame_error_count": "<PERSON><PERSON>", "interface_type": "Interface Type", "local_cmd_port": "Local Command Port", "local_data_port": "Local Data Port", "max_connection": "Max. Connections", "no_log": "No Log", "operation_mode": "Operation Mode", "operation_mode_disable": "To use the Serial Device Server feature, disable the SCATS feature first.", "overrun_count": "Overrun Count", "packet_length": "Packet Length", "page_title": "Serial", "parity": "Parity", "parity_error_count": "Parity Error Count", "port_buffering_and_log": "Port Buffering and Logs Settings", "real_com": "Real COM", "reverse_real_com": "Reverse Real COM", "rfc_2217": "RFC 2217", "rs_232": "RS-232", "rs_422": "RS-422", "rts_cts": "RTS, CTS", "rx_count": "RX Count", "rx_total_count": "RX Total Count", "secure_connection": "Secure Connection", "security_settings": "Security Settings", "serail_data_logs": "Serial Data Logs", "serial_counter": "Serial Counter", "serial_data_logs": "Serial Data Logs (64 KB)", "serial_port": "Serial Port", "serial_port_bufffering": "Serial Port Buffering (10 MB)", "serial_port_inactivity_time": "Serial Port Inactivity Time", "serial_state": "Serial State", "set_dtr_signal": "Set DTR Signal", "set_rts_signal": "Set RTS Signal", "start_ip_address": "Starting IP Address", "startup_none": "Startup/None", "stop_bits": "Stop Bits", "strip_delimiter": "Strip Delimiter", "tcp_alive_check_interval": "TCP Alive Check Interval", "tcp_client": "TCP Client", "tcp_command_port": "TCP Command Port", "tcp_data_port": "TCP Data Port", "tcp_server": "TCP Server", "title_add_dest_dialog": "Add Destination", "title_delete_destination_entry": "Delete Destination Entry", "title_edit_dest_dialog": "Edit Destination", "tx_count": "TX Count", "tx_total_count": "TX Total Count", "udp_data_port": "UDP Data Port", "wrie_2_rs_485": "2-wire-RS-485", "wrie_4_rs_485": "4-wire-RS-485", "xon_xoff": "XON, XOFF"}, "session_control": {"concurrent_tcp": "Concurrent TCP Requests", "connections_request_unit": "connections/s", "connections_unit": "connections", "create_policy": "Create Session Control Policy", "delete_msg": "Are you sure you want to delete the selected policy?", "delete_title": "Delete Session Control Policy", "dest_ip_address": "IP Address", "dest_port": "Port", "destination_ip": "Destination IP", "destination_port": "Destination Port", "drop": "Drop", "edit_policy": "Edit Session Control Policy", "enforcement": "Status", "monitor": "Monitor", "page_title": "Session Control", "policy_name_exist": "Policy Name Already Exists", "size_limitation": "The maximum number of policy for this device is {{ size }}.", "sub_title": "Network Host and Service Resource Protector", "tcp_connection_limit": "TCP Connection Limitation", "tcp_dest": "TCP Destination", "tcp_limit_error": "You must configure at least one limitation", "tcp_limit_msg": "At least one limitation is required", "total_tcp": "Total TCP Connections"}, "setting_check": {"dialog_confirm_msg": "Click \"Confirm\" if you wish to save your changes.", "dialog_title": "{{ funct_name }} Confirmation", "dialog_warning_msg": "Setting Check is enabled. Your changes will be discarded in {{ time_left }} sec(s).", "page_title": "Setting Check", "setting_check_configuration": "Setting Check Configuration", "timer": "Timer"}, "sms_settings": {"page_title": "SMS Settings", "sms_size_limitation": "The maximum number of SMS numbers for this device is {{ size }}.", "title_add_sms_entry": "Add SMS Number", "title_edit_sms_entry": "Edit SMS Number"}, "sms": {"cellular_report": "Cellular Report", "command": "Command", "country_code": "Country Code", "delete_entry_confirm_desc": "Are you sure you want to delete the selected entries?", "DO_off": "Set DO Off", "DO_on": "Set DO On", "edit_status": "Edit Status", "number": "Number", "page_title": "SMS", "remote_control_list": "Remote Control List", "send_sms": "Send SMS", "sms_message": "SMS Message", "sms_message_hint": "Special characters such as ^, \\, |, ~, [, ], {, } require two bytes", "sms_password": "SMS Password", "sms_receipt": "SMS Receipt", "sms_receipt_hint": "If enabled, the device will send a reply SMS to the sender after the device recieves a command SMS.", "sms_remote_control_warning": "For better backup, enable SMS Remote Control to manage the device via SMS when the connection is lost.", "sms_romote_control": "SMS Remote Control", "start_cellular_connect": "Cellular Start Connecting", "start_IPsec": "Start IPsec Tunnel", "stop_cellular_connect": "Cellular Stop Connecting", "stop_IPsec": "Stop IPsec Tunnel", "switch_sim": "Switch SIM", "system_restart": "System Restart", "title_add_trusted_entry": "Add Trusted Number Entry", "title_delete_trusted_entry": "Delete Trusted Number Entry", "title_edit_status_of_selected_entries": "Edit Status of Selected Entries", "title_edit_trusted_entry": "Edit Trusted Number Entry", "trusted_number_authentication": "Trusted Number Authentication", "trusted_number_list": "Trusted Number List", "trusted_number_size_limitation": "The maximum number of Trusted Number entries for this device is {{ size }}."}, "snmp_trap": {"authentication_key": "Authentication Key", "delete_snmp_trap_account_desc": "Are you sure you want to delete the selected snmp trap account?", "inform_retries": "Inform Retries", "inform_timeout": "Inform Timeout", "inform_v2": "Inform V2", "inform_v3": "Inform V3", "page_title": "SNMP Trap/Inform", "recipient_name": "Recipient IP/Name", "snmp_trap_account_size_limitation": "The maximum number of SNMP trap/inform accounts for this device is {{ size }}.", "title_delete_snmp_trap_account": "Delete Snmp Trap Account", "trap_community": "Trap Community", "trap_mode": "Trap Mode", "trap_v1": "Trap V1", "trap_v2": "Trap V2", "trap_v3": "Trap V3"}, "snmp": {"access_control": "Access Control", "community_name": "Community Name", "encryprion_key": "Encryption Key", "encryption_method": "Encryption Method", "encryption_method_hint": "Using AES encryption", "engineid": "Engine ID", "engineid_enable": "User-Defined Engine ID", "no_access": "No Access", "page_title": "SNMP", "read_only": "Read Only", "read_write": "Read Write", "snmp_account": "SNMP Account", "snmp_engineid_confirm_desc": "It is required to re-apply or change the password for every user again to let user-defined Engine ID take effect.", "snmp_engineid_confirm_title": "Set SNMP Engine ID", "snmp_engineid_hint": "2-54 hexadecimal string. The String length must be even.", "snmp_version": "SNMP Version", "snmp_version_comfirm_desc": "Are you sure you want to enable non-secure SNMP version V1, V2c?", "snmp_version_comfirm_title": "Set SNMP version", "snmp_version_hint": "To access the device via SNMPv3, the system login password must be 8 or more characters long.", "v1": "V1, V2c, V3", "v2c": "V1, V2c", "v3": "V3 only"}, "soft_lockdown_mode": {"cpu_util": "CPU utilization threshold", "cycle_enter": "Failure cycles to enter lockdown mode", "cycle_leave": "Normal cycles to leave lockdown mode", "enable": "Enable", "free_mem": "Free memory space threshold", "in_soft_lockdown_mode": "In Soft Lockdown Mode", "interval": "Status monitoring interval", "not_in_soft_lockdown_mode": "Not in Soft Lockdown Mode", "page_title": "Soft Lockdown Mode", "status_title": "Soft Lockdown Status"}, "ssh_ssl": {"auto_generate": "Auto Generate", "ca_name": "CA Name", "certificate_file": "Certificate File", "certificate_source": "Certificate Source", "choose_local_ssl_cert": "Choose local SSL Certificate", "choose_local_ssl_cert_confirm_desc": "Choose local SSL Certificate will restart the system services within 30-40 seconds. Do you want to continue?", "created_date": "Created on", "expiration_date": "Expiration Date", "export_ssl_cert": "Export SSL certificate Request", "import_ssl_cert": "Import Certificate", "local_certificate": "Local Certificate", "local_certificate_database": "Local Certificate Database", "page_title": "SSH & SSL", "regen_ssh_key": "Regenerate SSH Key", "regen_ssh_key_confirm_desc": "Regenerating the SSH key will restart the system services and will make the device temporarily unavailable. Do you want to continue?", "regen_ssl_cert": "Regenerate SSL Certificate", "regen_ssl_cert_confirm_desc": "Regenerating SSL Certificate will restart the system services within 30-40 seconds. Do you want to continue?", "ssh": "SSH", "ssh_key": "SSH Key", "ssl": "SSL", "ssl_info": "Certificate Information", "title_delete_dialog_desc": "Are you sure you want to delete the selected certificate?", "title_delete_dialog_title": "Delete Certificate"}, "static_multicast_route": {"delete_static_multicast_route_desc": "Are you sure you want to delete the selected static multicast route?", "gaddr_dup_msg": "Duplicate Group Address", "group_address": "Group Address", "inbound_interface_dup_msg": "Duplicate inbound interface for the same Group Address.", "page_title": "Static Multicast Route", "source_address": "Source Address", "source_address_type": "Source Address Type", "specify_source": "Specify Source", "static_multicast_route_size_limitation": "The maximum number of static multicast route for this device is {{ size }}.", "title_create_static_multicast": "Create Static Multicast Route", "title_delete_static_multicast_route": "Delete Static Multicast Route", "title_edit_static_multicast": "Edit Static Multicast Route", "vrrp_master_only": "VRRP-Master-Only"}, "static_route": {"delete_static_route_desc": "Are you sure you want to delete the selected static routes?", "page_title": "Static Routes", "size_limitation": "The maximum number of static route entries for this device is {{ size }}.", "title_add_static_route_dialog_title": "Create new static route", "title_delete_static_route_dialog_title": "Delete static route", "title_edit_static_route_pre_msg": "Edit static route"}, "statistics": {"all_packets": "All Packets", "bandwidth_utilization": "Bandwidth Utilization", "bandwidthUtilization": "Bandwidth Utilization", "benchmark_line": "Benchmark Line", "benchmark_line_time": "Benchmark Line - Time", "broadcast": "Broadcast", "clear_graph_desc": "Are you sure you want to clear all graph data?", "clear_table_desc": "Are you sure you want to clear all table data?", "collisionPackets": "Collision Packets", "comparison_line": "Comparison Line", "comparison_line_time": "Comparison Line - Time", "crcAlignErrorPackets": "CRC Align Error Packets", "display_type": "Display Type", "displayMode": "Display Mode", "dropPackets": "Drop Packets", "dropPacketsHint": "Maximum update time of Drop Counter is 5 sec*max port number on device.", "error_packets": "Error Packets", "excessiveCollisionPackets": "Excessive Collision Packets", "exist_warning": "Already Exist", "format_text": "[Format]\nTotal Packets + Packets in past 5 secs", "fragmentPackets": "Fragment Packets", "interface_selection": "Interface Selection", "ip_interface": "IP Interface", "jabberPackets": "Jabber Packets", "lateCollisionPackets": "Late Collision Packets", "multicast": "Multicast", "oversizePackets": "Oversize Packets", "package_type": "Package Type", "packet_counter": "Packet Counter", "packet_interface_table_title": "Packet Interface Table", "packetCounter": "Packet Counter", "page_title": "Network Statistics", "port_selection": "Port Selection", "rule_size_limitation": "The maximum number of chart line to display is {{ size }}.", "rx_error": "Rx <PERSON>", "rx_only": "Rx", "rxBroadcastPackets": "Rx Broadcast Packets", "rxMulticastPackets": "Rx Multicast Packets", "rxPausePackets": "Rx Pause Packets", "rxTotalOctets": "Rx Total Octets", "rxTotalPackets": "Rx Total Packets", "rxUnicastPackets": "Rx Unicast Packets", "selecting_visible_columns": "Selecting Visible Columns", "sniffer_mode": "Sniffer Mode", "tx_and_rx": "Tx+Rx", "tx_error": "Tx Errors", "tx_only": "Tx", "txBroadcastPackets": "Tx Broadcast Packets", "txMulticastPackets": "Tx Multicast Packets", "txTotalOctets": "Tx Total Octets", "txTotalPackets": "Tx Total Packets", "txUnicastPackets": "Tx Unicast Packets", "undersizePackets": "Undersize", "unicast": "Unicast"}, "syslog_server": {"address": "Address", "message_format": "Message Format", "page_title": "Syslog", "rfc3164": "RFC 3164", "rfc5424": "RFC 5424"}, "system_info": {"contact_information": "Contact Information", "host_name": "Host Name", "host_name_hint": "The host name is introduced for protocol usage, e.g., DHCP Option 12, where the syntax should follow the host name principles.", "page_title": "Information Settings", "system_location": "Location", "system_name": "System Name", "system_name_hint": "The system name is used for device recognition by users."}, "tacacs_server": {"page_title": "TACACS+ Server", "tacacs": "TACACS+"}, "tacacs": {"tacacs": "TACACS+"}, "time": {"authentication": "Authentication", "city": {"abu_dhabi": "Abu-Dhabi", "adelaide": "Adelaide", "alaska": "Alaska", "amsterdam": "Amsterdam", "arizona": "Arizona", "astana": "Astana", "athens": "Athens", "atlantic_time": "Atlantic-Time", "auckland": "Auckland", "azores": "Azores", "baghdad": "Baghdad", "baku": "Baku", "bangkok": "Bangkok", "beijing": "Beijing", "belgrade": "Belgrade", "bogota": "Bogota", "bombay": "Bombay", "brasilia": "Brasilia", "brisbane": "Brisbane", "brussels": "Brussels", "bucharest": "Bucharest", "buenos_aires": "Buenos-Aires", "cairo": "Cairo", "canberra": "Canberra", "caracas": "Caracas", "casablanca": "Casablanca", "central_time": "Central-Time", "colombo": "Colombo", "darwin": "<PERSON>", "eastern_time": "Eastern-Time", "ekaterinburg": "Ekaterinburg", "eniwetok": "Eniwetok", "fiji": "Fiji", "greenwich": "Greenwich", "guam": "Guam", "harare": "Harare", "hawaii": "Hawaii", "helsinki": "Helsinki", "hobart": "Hobart", "indiana": "Indiana", "islamabad": "Islamabad", "jerusalem": "Jerusalem", "kabul": "Kabul", "magadan": "<PERSON><PERSON><PERSON>", "mexico_city": "Mexico-City", "mid_atlantic": "Mid-Atlantic", "midway_island": "Midway-Island", "moscow": "Moscow", "mountain_time": "Mountain-Time", "nairobi": "Nairobi", "newfoundland": "Newfoundland", "osaka": "Osaka", "pacific_time": "Pacific-Time", "perth": "Perth", "santiago": "Santiago", "sarajevo": "Sarajevo", "saskatchewan": "Saskatchewan", "seoul": "Seoul", "singapore": "Singapore", "taipei": "Taipei", "tehran": "Tehran", "vladivostok": "Vladivostok", "yakutsk": "Yakutsk"}, "client_authentication": "Client Authentication", "clock_source": "Clock Source", "current_time": "Current Time", "date": "Date", "day": "Day", "daylight_saving": "Daylight Saving", "daylight_saving_status": "Daylight Saving Status", "delete_entry_confirm_desc": "Are you sure you want to delete the selected key string?", "disable_sntp": "SNTP cannot be set as the Clock Source in System Time when the NTP/SNTP Server is enabled.", "first_time_server": "Time Server 1", "hour": "Hour", "key_id": "Key ID", "key_size_limitation": "The maximum number of NTP authentication keys for this device is {{ size }}.", "key_string": "Key String", "minutes": "Minutes", "month": "Month", "mxsecurity_confirm_desc": "Connecting to MXsecurity will synchronize the device's local time to MXsecurity's NTP server. If you change the time, the device will be disconnected from MXsecurity. Do you want to continue?", "ntp": "NTP", "ntp_auth_tab_title": "NTP Authentication", "offset": "Offset", "page_title": "System Time", "second_time_server": "Time Server 2", "sntp": "SNTP", "time_zone": "Time Zone", "week": "Week", "clock_source_fallback_mode": "Clock Source Fallback Mode", "clock_source_fallback_mode_desc": "If the NTP/SNTP clock source becomes unavailable, the system will automatically switch the time source to the local. Please note that if the local time is incorrect, it may cause abnormal behavior."}, "trust_access": {"accept_all_from_lan": "Accept All LAN Port Connections", "enable_accessible": "Trusted IP List (Disabling this will allow all IP connections)", "page_title": "Trusted Access", "size_limitation": "This device allows maximum of 10 trusted access rules.", "trust_access_confrim_dialog_desc": "All access will be blocked except the local serial console. Are you sure you want to apply?", "trust_access_confrim-dialog_title": "Warning: Blocking All Non-Serial Access", "trust_access_create_dialog_title": "Create Index {{portIndex}}", "trust_access_setting_dialog_title": "Edit Index {{portIndex}}"}, "trusted_ca_cert": {"page_title": "Trusted CA Certificate", "select_ca_certificate": "Select CA Certificate", "subject": "Subject", "title_create_ca_cer": "Generate CA Certificate", "title_delete_dialog_desc": "Are you sure you want to delete the selected ca certificate?", "title_delete_dialog_title": "Delete CA Certificate"}, "turbo_chain": {"bridge_member_hint": "If a port is configured as a bridge interface member, it cannot be used for L2 redundancy features: Turbo Ring, Turbo Chain, or RSTP", "chain_information": "Chain Information", "chain_role": "Chain Role", "head": "Head", "head_port": "Head Port", "head_port_status": "Head Port Status", "initiated": "Initiated", "member": "Member", "member_number_port_status": "Member {{ number }} Port Status", "member_port_number": "Member Port {{ portIndex }}", "member_port_status": "Member Port Status", "page_title": "Turbo Chain", "status": "Status", "tail": "Tail", "tail_port": "Tail Port", "tail_port_status": "Tail Port Status"}, "turbo_ring_v2": {"backup_path": "Backup Path", "backup_port": "Backup Port", "break": "Break", "bridge_member_hint": "If a port is configured as a bridge interface member, it cannot be used for L2 redundancy features: Turbo Ring, Turbo Chain, or RSTP", "coupling_mode": "Coupling Mode", "coupling_mode_backup": "Coupling Backup Path", "coupling_mode_primary": "Coupling Primary Path", "coupling_port": "Coupling Port", "dual_homing": "Dual Homing", "healthy": "Healthy", "master_id": "Master ID", "page_title": "Turbo Ring V2", "primary_path": "Primary Path", "primary_port": "Primary Port", "ring": "Ring", "ring_coupling_setting": "Ring Coupling Settings", "ring_coupling_status": "Ring Coupling Status", "ring_id": "Ring ID", "ring_port": "Ring Port", "ring_setting": "Ring Settings", "ring_setting_dialog_title": "{{ portIndex }} Settings", "ring_status": "Ring Status"}, "user_interface": {"enable_http": "HTTP", "enable_https": "HTTPS", "enable_moxa_service": "Moxa Service", "enable_ping": "Ping Response (WAN)", "enable_snmp": "SNMP", "enable_telnet": "Telnet", "http_port": "TCP Port (HTTP)", "https_port": "TCP Port (HTTPS)", "max_session_http": "Maximum Number of Login Sessions for HTTP+HTTPS", "max_session_terminal": "Maximum Number of Login Sessions for Telnet+SSH", "moxa_tcp_port": "TCP Port for Moxa Service (Encrypted)", "moxa_udp_port": "UDP Port for Moxa Service (Encrypted)", "page_title": "User Interface", "ping_response": "Ping Response", "snmp_port": "SNMP - UDP Port", "ssh_port": "TCP Port (SSH)", "telnet_comfirm_desc": "Are you sure tou want to enable non-secure inetrface Telnet?", "telnet_comfirm_title": "Enable Telnet Interface", "telnet_port": "TCP Port (Telnet)"}, "utilization": {"cpu_historical_record": "CPU Usage History", "cpu_utilization": "CPU Usage", "free": "Free", "last_update_time": "Last update time", "mem_historical_record": "Memory Usage History", "mem_utilization": "Memory Usage", "page_title": "Utilization", "power_historical_record": "Power Usage History", "power_utilization": "Power Consumption", "used": "Used"}, "vlan": {"access": "Access", "access_port": "Access Port", "all_member_vlan": "All Member VIDs", "delete_entry_confirm_desc": "Are you sure you want to delete the selected entry?", "delete_vlan_confirm_desc": "Are you sure you want to delete the selected VLAN?", "egress_port": "Egress Port", "egress_tagged_table_title": "VLAN Membership Table", "forbidden_port": "Forbidden Port", "forbidden_vlan": "Forbidden VLAN", "global": "Global", "gvrp": "GVRP", "hybrid": "Hybrid", "hybrid_port": "Hybrid Port", "management_port": "Management Port", "management_port_quick_setting": "Management Port Quick Settings", "management_vlan": "Management VLAN", "management_vlan_port_setting_hint": "Select the port that is connected to your computer and make sure all settings are configured correctly to avoid being disconnected from the device.", "member_port": "Member Port", "mgmt_setting_disabled_access_mode": "If port is access mode, it can not change to this VLAN.", "mgmt_setting_disabled_egress": "This port is member port.", "mgmt_setting_disabled_forbidden": "This port is forbidden port.", "mgmt_setting_disabled_pvid": "PVID is binding to this VLAN, so it can not be deleted.", "page_title": "VLAN", "port_bridge_hint": "This port is currently assigned as the Bridge mode.", "port_ids_hint": "This port is currently set to IDS mode.", "port_mode_table_title": "VLAN Switchport Mode Table", "port_setting_disabled_forbidden": "This port is forbidden port for this VLAN.", "port_setting_disabled_pvid_forbidden": "This PVID can not bind to this VLAN because this port is the forbidden port.", "port_setting_disabled_pvid_member": "This PVID can not bind to this VLAN because this port is not the member.", "port_setting_disabled_tagged": "This VLAN is tagged VLAN.", "port_setting_disabled_untagged": "This VLAN is untagged VLAN.", "port_setting_error_pvid_forbidden": "Forbidden port can not apply", "port_setting_error_pvid_member": "Not Tagged or Untagged VLAN", "pvid": "PVID", "quick_vlan_setting_for_selected_port": "Quick VLAN settings for selected port", "size_limitation": "This device only allows {{ size }} VLANs.", "switchport_table": "Switchport Table", "tagged_port": "Tagged Port", "tagged_vlan": "Tagged VLAN", "trunk": "Trunk", "trunk_port": "Trunk Port", "untagged_port": "Untagged Port", "untagged_vlan": "Untagged VLAN", "vid": "VID", "vid_exist_warning": "VLAN already exist", "vlan": "VLAN", "vlan_create_hint": "You can create multiple VLANs at once by entering single VLAN IDs or a range of IDs. For example, 2, 4-8, 10-13", "vlan_max_hint": "Max {{ max }} VLANs", "vlan_max_warning": "Max {{ max }} VLANs per once", "vlan_range_error": "VLAN range must be {{ min }} - {{ max }}.", "vlan_setting_info_title": "How to Setup", "vlan_used_hint": "This VLAN is currently assigned as the PVID. To delete this VLAN, change the PVID to a different VLAN."}, "vrrp": {"accept": "Accept", "accept_mode": "Accept Mode", "adv": "Advertisement Interval", "adv_abbr": "Adv Int (s)", "adv_v3_abbr": "Adv Int (ms)", "adv_v3_error_10": "Advertisement Interval must be divisible by 10.", "delete_confirm_msg": "Are you sure you want to delete the selected VRRP entry?", "delete_nat_msg": "NAT Rules with selected VRRP ID will also be removed.", "di_status": "DI Status", "disable_wan_warning": "WAN Redundancy is enabled. VRRP and WAN Redundancy cannot be enabled at the same time.", "duplicate_with_interface_ip": "The Target IP should exclude IPs associated with existing network interfaces.", "failure_cnt": "Failure Count", "interval": "Interval", "link_status": "Link Status", "master_addr": "Master Address", "monitored_port": "Monitored Port", "no_event": "No Event", "obj_ping_tracking": "Object Ping Tracking", "page_title": "VRRP", "preempt_delay": "<PERSON><PERSON><PERSON>", "preemption": "Preemption", "priority": "Priority", "priority_abbr": "Priority", "priority_off": "Off - VRRP Priority", "priority_on": "On - VRRP Priority", "success_cnt": "Success Count", "target_ip": "Target IP", "target_ip_hint": "Leave empty or set to 0.0.0.0 to disable", "title_add_vrrp": "Create Virtual Router", "title_delete_vrrp": "Delete Virtual Router", "title_edit_vrrp": "Edit Virtual Router", "track_ifs": "Native Interface Tracking", "tracking_interface": "Tracking Interface", "tracking_ping": "Tracking Ping", "v2": "Version 2", "v3": "Version 3", "vip": "Virtual IP", "vip_abbr": "VIP", "vip_dup_msg": "Virtual IP not available (already in use)", "vrid": "Virtual Router ID\n", "vrid_abbr": "VRID", "vrid_dup_msg": "Virtual Router ID already exists on the this interface", "vrrp_setting_subtitle": "VRRP Interface Setting", "vrrp_size_limitation": "The maximum number of VRRP entries is {{ size }}.", "vrrp_tracking_subtitle": "VRRP Tracking"}, "wan_redundancy": {"apply_dmz_warning": "For the WAN2 interface, only one of WAN Redundancy and DMZ functionality can be enabled. Please confirm if DMZ mode has been activated.", "cellular": "Cellular", "disable_vrrp_warning": "VRRP is enabled. WAN Redundancy and VRRP cannnot be enabled at the same time.", "disable_warning_for_cellular_desc": "WAN Redundancy has been disabled. The device will now use cellular as the default route interface for WAN connection.", "disable_warning_for_cellular_title": "Use Cellular as Default Route", "edit_interface_dialog_title": "Edit {{ interface }} Interface Settings", "eth_wan": "Ethernet WAN", "eth_wan_1": "Ethernet WAN 1", "eth_wan_2": "Ethernet WAN 2", "eth_wan_3": "Ethernet WAN 3", "eth_wan_4": "Ethernet WAN 4", "eth_wan_number": "Ethernet WAN {{ number }}", "failback": "<PERSON><PERSON><PERSON>", "failback_hint": "The system will always switch back to the higher priority interface when it is available.", "failover": "Failover", "failover_hint": "The system will maintain the current interface, even if the higher priority interface recovers.", "load_balancing": "<PERSON><PERSON>", "page_title": "WAN Redundancy", "ping_cellular_warning": "Cellular ping checks may incur additional data costs.", "ping_check": "Ping Check", "ping_failure_retry_times": "Ping Failure Retry Attemps", "ping_interval": "<PERSON>", "ping_success_retry_times": "Ping Success Retry Attempts", "ping_timeout": "Ping Timeout", "wan_backup_priority": "WAN Backup Priority", "wan_redundancy_mode": "WAN Redundancy Mode", "wan_switch_mode": "WAN Switching Mode", "wan_switch_mode_hint": "If enabled, the system will always switch back to the higher priority interface when it is available.", "wi_fi": "Wi-Fi"}, "wizard": {"access_mode_pvid": "Access Mode PVID", "complete": "Setup Complete!", "complete_info": "Make sure to check the configuration of the device is correct after applying these settings.", "max_port": "The maximum number of ports is {{ maxPort }}.", "nat_settings": "NAT Settings", "network_configuration": "Network Configuration", "page_title": "Setup Wizard", "port": "Port", "port_1_configuration": "Port 1 (Internal Port)", "port_2_configuration": "Port 2 (External Port)", "setup_info": "This Setup Wizard will help you set up your device in just a few steps.", "step": "Step"}}, "general": {"button": {"add": "ADD", "apply": "APPLY", "back": "BACK", "backup": "BACK UP", "browse": "BROWSE", "cancel": "CANCEL", "change": "CHANGE", "change_password": "CHANGE PASSWORD", "change_pin": "CHANGE PIN", "clear": "CLEAR", "close": "CLOSE", "collapse": "COLLAPSE", "collaspe": "COLLASPE", "confirm": "CONFIRM", "copy": "COPY", "create": "CREATE", "delete": "DELETE", "disable": "DISABLE", "download": "DOWNLOAD", "edit": "EDIT", "enable": "ENABLE", "expand": "EXPAND", "export": "EXPORT", "generate": "GENERATE", "import": "IMPORT", "install": "INSTALL", "locate": "LOCATE", "login": "LOG IN", "logout": "LOG OUT", "navigate": "NAVIGATE", "next": "NEXT", "ok": "OK", "ping": "PING", "re_auth": "RE-AUTH", "redirect": "REDIRECT", "refresh": "REFRESH", "regenerate": "REGENERATE", "reset": "RESET", "reset_key": "RESET KEY", "restart": "RESTART", "restore": "RESTORE", "select": "Select", "send": "SEND", "send_test_email": "SEND TEST EMAIL", "start_learning": "START LEARNING", "stop_learning": "STOP LEARNING", "sync_from_browser": "SYNC FROM BROWSER", "uninstall": "UNINSTALL", "upgrade": "UPGRADE"}, "common_abbrev": {"aes": "AES", "aes128_sha": "AES128-SHA", "aes156_sha": "AES256-SHA", "ascii": "ASCII", "auth_type_asc_two": "ASCII", "chap": "CHAP", "cts": "CTS", "dcd": "DCD", "des": "DES", "des_cbc_md5": "DES-CBC-MD5", "des_cbc_sha": "DES-CBC-SHA", "des_cbc3_md5": "DES-CBC-MD5", "des_cbc3_sha": "DES-CBC3-SHA", "dhe_rsa_aes128_sha": "DHE-RSA-AES128-SHA", "dhe_rsa_aes256_sha": "DHE-RSA-AES256-SHA", "dsr": "DSR", "dtr": "DTR", "edh_rsa_des_cbc_sha": "EDH-RSA-DES-CBC-SHA", "edh_rsa_des_cbc3_sha": "EDH-RSA-DES-CBC3-SHA", "hex": "HEX", "md5": "MD5", "nmea": "NMEA", "pap": "PAP", "rc4_md5": "RC4-MD5", "rc4_sha": "RC4-SHA", "rts": "RTS", "scp": "SCP", "sftp": "SFTP", "sha": "SHA", "sha256": "SHA-256", "sha512": "SHA-512", "tftp": "TFTP"}, "common_account": {"account": "Account", "authentication_password": "Authentication password", "authentication_type": "Authentication Type", "authority": "Authority", "confirm_password": "Confirm Password", "delete_account_desc": "Are you sure you want to delete the selected account?", "delete_account_title": "Delete Account", "email": "Email", "password": "Password", "pwd_mask": "********", "username": "Username", "welcome": "Welcome"}, "common_category": {"adp": "ADP", "device_lockdown": "Device Lockdown", "dos": "DoS Policy", "dpi_policy": "Protocol Filter Policy", "ips": "IPS", "malformed": "Malformed Packets", "ob_l2_filter": "Layer 2 Policy", "ob_l3_policy": "Layer 3-7 Policy", "session_control": "Session Control", "system": "System", "trust_access": "Trusted Access", "vpn": "VPN"}, "common_port": {"all_port": "All Ports", "also_apply_port": "Copy Configurations to Ports", "also_apply_port_hint": "Copy the configurations to the ports you select from the drop-down box.", "destination_port": "Destination Port", "member_port": "Member Port", "port": "Port", "port_name": "Port {{ portName }}", "port_settings": "Port Settings", "port_state": "Port State", "port_status": "Port Status", "tcp_port": "TCP Port", "udp_port": "UDP Port"}, "common_route": {"active_wan": "Active WAN", "application_protocol": "Application Protocol", "default_route": "Default Route", "destination": "Destination Address", "dst_ip": "Destination IP", "dst_ip_end": "Destination IP Range End", "dst_ip_start": "Destination IP Range Start", "dst_mac": "Destination MAC", "dst_port": "Destination Port", "dst_port_end": "Destination Port Range End", "dst_port_start": "Destination Port Range Start", "ether_type": "EtherType", "icmp_code": "ICMP Code", "icmp_type": "ICMP Type", "inbound_interface": "Inbound Interface", "incoming_interface": "Incoming Interface", "indu_app_service": "Industrial Application Service", "ip_protocol": "IP Protocol", "ips_category": "IPS Category", "ips_severity": "IPS Severity", "metric": "Metric", "network_service": "Network Service", "nexthop": "Next Hop", "outbound_interface": "Outbound Interface", "outgoing_interface": "Outgoing Interface", "policy_id": "Policy ID", "policy_name": "Policy Name", "src_ip": "Source IP", "src_ip_end": "Source IP: End", "src_ip_start": "Source IP: Start", "src_mac": "Source MAC", "src_port": "Source Port", "src_port_end": "Source Port: End", "src_port_start": "Source Port: Start", "sub_category": "Subcategory", "tcp_flags": "TCP Flags", "wan": "<PERSON>"}, "common_severity": {"alert": "<PERSON><PERSON>", "critical": "Critical", "debug": "Debug", "emergency": "Emergency", "error": "Error", "info": "Informational", "notice": "Notice", "severity": "Severity", "warning": "Warning"}, "common_version": {"v1": "V1", "v2": "V2"}, "common": {"action": "Action", "active": "Active", "alias": "<PERSON><PERSON>", "all": "All", "allow": "Allow", "any": "Any", "auth_type": "Auth Type", "authentication": "Authentication", "auto": "Auto", "backup": "Backup", "bit": "Bit", "blocking": "Blocking", "connected": "Connected", "connecting": "Connecting", "deny": "<PERSON><PERSON>", "description": "Description", "disable": "Disabled", "disconnect": "Disconnected", "display": "Display", "enable": "Enabled", "end": "End", "end_date": "End Date", "end_time": "End Time", "even": "Even", "event": "Event", "file_name": "File Name", "forwarding": "Forwarding", "general": "General", "high": "High", "host_ip_address": "Host IP Address", "host_name": "Host Name", "id": "ID", "index": "Index", "interface": "Interface", "ip_address": "IP Address", "ip_domain": "IP Address/Domain Name", "led": "LED", "link_down": "Link down", "listening": "Listening", "local": "Local", "local_storage": "Local Storage", "log": "Log", "log_destination": "Log Destination", "low": "Low", "mac_address": "MAC Address", "management": "Management", "manual": "Manual", "master": "Master", "method": "Method", "mode": "Mode", "name": "Name", "netmask": "Netmask", "no": "No", "none": "None", "odd": "Odd", "offline": "Offline", "online": "Online", "packets": "Packets", "passive": "Passive", "priority": "Priority", "protocol": "Protocol", "remote": "Remote", "restore": "Rest<PERSON>", "retry": "Retry", "select_file": "Select File", "server_ip_address": "Server IP Address", "setting": "Settings", "share_key": "Share Key", "share_key_hint": "After leaving this page or refreshing, the Share Key will automatically be cleared to enhance security.", "snmp_trap_server": "Trap", "source": "Source", "start": "Start", "start_date": "Start Date", "start_time": "Start Time", "state": "State", "status": "Status", "step": "Step {{ step }}", "subnet_mask": "Subnet Mask", "syslog_server": "Syslog", "tcp": "TCP", "time": "Time", "timeout": "Timeout", "trap_server": "Trap", "try_clear_event_log": "please try clearing event log.", "type": "Type", "udp": "UDP", "unexpected_error": "An error has occurred", "unknown": "Unknown", "usb": "USB", "value": "Value", "version": "Version", "vlan_id": "VLAN ID", "vlan_vid": "VLAN {{ vid }}", "yes": "Yes"}, "date": {"1st_week": "1st", "2nd_week": "2nd", "3rd_week": "3rd", "4th_week": "4th", "april": "April", "august": "August", "daily": "Daily", "december": "December", "february": "February", "friday": "<PERSON><PERSON>", "hourly": "Hourly", "january": "January", "july": "July", "june": "June", "last_week": "Last", "march": "March", "may": "May", "monday": "Mon", "monthly": "Monthly", "monthly_day": "Day(s) of the Month", "november": "November", "october": "October", "saturday": "Sat", "september": "September", "sunday": "Sun", "thursday": "<PERSON>hu", "tuesday": "<PERSON><PERSON>", "wednesday": "Wed", "weekly": "Weekly", "weekly_day": "Day(s) of the Week"}, "dialog": {"add_static_multicast_entry": "Add Static Multicast Entry", "add_static_unicast_entry": "Add Static Unicast Entry", "delete_entry_confirm_desc": "Are you sure you want to delete selected entry?", "edit_static_multicast_entry": "Edit Static Multicast Entry", "edit_static_unicast_entry": "Edit Static Unicast Entry", "title_add_stream_adapter_entry": "Add Stream Adapter Entry", "title_clear_statistics_counter": "Clear Counter", "title_comparison": "Data Comparison", "title_create_entry_msg": "Create Entry", "title_create_host_table_msg": "Create Host Settings", "title_create_la_msg": "Create Link Aggregation", "title_create_lan_interface": "Create LAN Interface Entry", "title_create_rule_msg": "Create Rule", "title_create_secondary_ip": "Create Secondary IP Entry", "title_create_v3_account_table_msg": "Create SNMP Account <PERSON><PERSON>s", "title_create_v3_trap_account_table_msg": "Create SNMP Trap Account <PERSON><PERSON>s", "title_create_vlan_msg": "Create VLAN", "title_delete_entry_msg": "Delete Entry", "title_delete_gre_interface": "Delete GRE Interface Entry", "title_delete_la_msg": "Delete Link Aggregation", "title_delete_lan_interface": "Delete LAN Interface Entry", "title_delete_loopback_interface": "Delete Loopback Interface Entry", "title_delete_secondary_ip": "Delete Secondary IP Entry", "title_delete_vlan_msg": "Delete VLAN", "title_edit_cos_msg": "Edit CoS {{cosIndex}} Settings", "title_edit_dscp_msg": "Edit DSCP {{dscpIndex}} Settings", "title_edit_entry_empty_msg": "Edit Entry Settings", "title_edit_entry_msg": "Edit Entry {{entryIndex}} Settings", "title_edit_event_notification": "Edit Event Notification", "title_edit_host_table_msg": "Edit Host Settings", "title_edit_la_pre_msg": "Edit Port Channel {{channelIndex}} Settings", "title_edit_lan_interface": "Edit LAN Interface Entry", "title_edit_management_vlan_msg": "Edit Management VLAN", "title_edit_mirror_msg": "Edit Session {{sessionIndex}} Settings", "title_edit_mtu": "Edit MTU Entry", "title_edit_port_msg": "Edit Port {{portIndex}} Settings", "title_edit_rule_msg": "Edit Rule", "title_edit_secondary_ip": "Edit Secondary IP Entry", "title_edit_statistics_setting": "Display Settings", "title_edit_stream_adapter_entry": "Edit Stream Adapter Entry", "title_edit_v3_account_table_msg": "Edit SNMP {{authority}} Account Settings", "title_edit_v3_trap_account_table_msg": "Edit SNMP Trap Account <PERSON><PERSON>s", "title_edit_vlan_msg": "Edit VLAN {{vlanIndex}} Settings", "title_export_ssl_certificate": "Export SSL Certificate Request", "title_reset_statistics_graph": "Reset Statistics Graph", "title_select_file": "Select File", "title_system_message": "System Message", "warning": "Warning"}, "led": {"bp_amber_on": "LAN Bypass enabled", "bp_off": "LAN Bypass disabled", "cell_gnss_green_on": "GNSS located successfully", "cell_gnss_off": "The GNSS function is disabled", "cell_gnss_red_on": "Less than four satellites have been located", "cell_rat_5g_amber_on": "LTE/UMTS/HSPA/GSM/GPRS/EDGE is connected", "cell_rat_5g_green_on": "5G NR is connected", "cell_rat_5g_off": "No cellular service", "cell_rat_amber_on": "UMTS/HSPA/GSM/GPRS/EDGE connected", "cell_rat_green_on": "4G LTE connected", "cell_rat_off": "No cellular service", "cell_signal_amber_on": "Fair cellular signal", "cell_signal_green_on": "Good cellular signal", "cell_signal_off": "No cellular signal", "cell_signal_red_on": "Poor cellular signal", "cell_sim1_green_on": "A SIM card is inserted in SIM1 and is working normally", "cell_sim1_off": "No SIM card is inserted in SIM1", "cell_sim1_red_on": "A SIM card is inserted in SIM1 but is not working properly", "cell_sim2_green_on": "A SIM card is inserted in SIM2 and is working normally", "cell_sim2_off": "No SIM card is inserted in SIM2", "cell_sim2_red_on": "A SIM card is inserted in SIM2 but is not working properly", "ct_green_blink_on": "The secure router that enables Turbo Chain and Turbo Chain is broken.", "ct_green_on": "The secure router enables a coupling function to form a back-up path or the switch is the tail of Turbo Chain.", "ct_off": "When the secure router disables the coupling or tail role of Turbo Chain", "fault_green_on": "Device operation has issue, check event log to have further information.", "fault_off": "Device is operating normally.", "fault_red_blink0": {"5Hz_on": "Factory Default, no Configuration Change"}, "fault_red_blink1Hz_on": "Factory Default, no Configuration Change", "fault_red_blink2": {"5Hz_on": "Configuration Importing and Saving"}, "fault_red_blink5Hz_on": "Configuration Importing and Saving", "fault_red_on": "System Fault", "ha_green_on": "Firewall HA enabled", "ha_off": "Firewall HA disabled", "ha_red_on": "Firewall HA enabled", "learning_amber_blink1Hz_on": "The device lockdown learning is in progress", "learning_off": "Learning finished", "lockdown_green_on": "The device lockdown whitelist is enable", "lockdown_off": "The device lockdown whitelist is disable", "mgmt_ct_broken_hint": "Turbo Chain is not healthy.", "mgmt_ct_off_hint": "The device has disabled coupling to form a back-up path and is not the tail of Turbo Chain.", "mgmt_ct_ready_hint": "Switch enables coupling to form a back-up path or is the tail of Turbo Chain.", "mgmt_fault_error_hint": "System initialization failure or EEPROM information error.", "mgmt_fault_well_hint": "System operating well.", "mgmt_mh_broken_hint": "Turbo Ring/Turbo Chain is not healthy.", "mgmt_mh_off_hint": "The device is not Master/Head/Root of Turbo Ring/Turbo Chain.", "mgmt_mh_ready_hint": "Switch is Master/Head/Root of Turbo Ring/Turbo Chain.", "mgmt_state_fail_hint": "System failed during power-on self-test.", "mgmt_state_off_hint": "System is off.", "mgmt_state_ready_hint": "System is ready.", "mgmt_state_self_test_hint": "System is under power-on self-test or performing a reset to factory default settings.", "mgmt_sync_converged_hint": "The PTP function has successfully converged.", "mgmt_sync_enabled_hint": "The PTP function is enabled.", "mgmt_sync_syncing_hint": "The device has received sync packets.", "mh_green_blink_on": "The secure router is the Ring Master/Head of Turbo Ring/Turbo Chain and Turbo Ring/Turbo Chain is broken.", "mh_green_on": "The secure router is set as the Master of the Turbo Ring, or as the Head of the Turbo Chain.", "mh_off": "When the secure router is not the Master/Head of this Turbo Ring/Turbo Chain", "module_eps_off_hint": "The external power is not ready to supply power to the PoE port or no power.", "module_eps_ready_hint": "The external power is ready to supply power to the PoE port.", "module_ms_out_of_service_hint": "The module is out of service.", "module_ms_ready_hint": "Normal", "module_ms_reserved_hint": "Abnormal: Module initial fail", "module_ms_self_test_hint": "The module is performing power-on self-test.", "module_poe_8023_hint": "The port is connected to IEEE 802.3at PD.", "module_poe_8023_off_hint": "The power is not being supplied or the port is not connected to an IEEE 802.3at PD.", "module_poe_8023af_hint": "The port is connected to an IEEE 802.3af/at PD.", "module_poe_8023af_low_hint": "The PoE power has been shut off because the power budget is too low.", "module_poe_8023af_off_hint": "The power is not being supplied or the port is not connected to an IEEE 802.3af PD.", "module_poe_pd_circuir_hint": "The port is detecting over current or a short circuit on the PD.", "module_poe_pd_fail_hint": "Power is not being supplied to a Powered Device (PD).", "module_poe_pd_normal_hint": "Power is being supplied to a Powered Device (PD).", "module_port_100_hint": "The port is active and data is being transmitted at 10/100Mbps.", "module_port_100_tx_hint": "The port data is being transmitted at 10/100Mbps.", "module_port_1000_hint": "The port is active and data is being transmitted at 1,000 Mbps.", "module_port_1000_tx_hint": "The port data is being transmitted at 1,000 Mbps.", "module_port_down_hint": "The port is inactive or link down.", "module_port_hint": "The port is active and links on {{ operSpeed }} Mbps", "pwr1_amber_on": "Power is being supplied to the main module's power input PWR1", "pwr1_off": "Power is not being supplied to the main module's power input PWR1", "pwr2_amber_on": "Power is being supplied to the main module's power input PWR2", "pwr2_off": "Power is not being supplied to the main module's power input PWR2", "serial_green_on": "Data is being transmitted", "serial_off": "No data is begin transmitted", "state_green_blink_on": "After pressing reset button for 5 seconds and system is ready to do factory reset", "state_green_on": "The system passed the self-diagnosis test on boot-up and is ready to run", "state_off": "The system is off.", "state_red_on": "The system has initially failed in boot-up process", "status_unknown_hint": "Status Unknown.", "usb_green_blink_on": "USB data is being transmitted", "usb_green_on": "USB drive successfully connected", "usb_off": "USB drive disconnected", "usb_red_on": "USB dongle malfunction", "vpn_amber_on": "Only parts of the VPN tunnels are working normally", "vpn_green_on": "All VPN tunnels are working normally", "vpn_off": "No active VPN connections", "vrrp_green_on": "When the secure router is Master of VRRP or HA", "vrrp_off": "When the secure router is not Master of VRRP or HA", "wan_dmz_amber_on": "The WAN2/DMZ port is set to the WAN function", "wan_dmz_green_on": "The WAN2/DMZ port is set to the DMZ function", "wan_dmz_off": "The WAN2/DMZ port is disabled"}, "menu_tree": {"account_management": "Account Management", "application": "Industrial Application", "certificate_management": "Certificate Management", "device_security": "Device Security", "diagnostics": "Diagnostics", "event_log_and_notification": "Event Logs and Notifications", "firewall": "Firewall", "iec61375": "IEC 61375", "jump_page_placeholder": "Search for a function", "l2_redundancy": "Layer 2 Redundancy", "l2_switching": "Layer 2 Switching", "l3_redundancy": "Layer 3 Redundancy", "management_interface": "Management Interface", "multicast": "Multicast", "multicast_route": "Multicast Route", "network_configuration": "Network Configuration", "network_security": "Network Security", "network_service": "Network Service", "network_status": "Network Status", "port": "Ports", "redundancy": "Redundancy", "routing": "Routing", "security": "Security", "system": "System", "system_management": "System Management", "system_status": "System Status", "time": "Time", "tools": "Tools", "unicast_route": "Unicast Route", "vpn": "VPN"}, "speed": {"100m": "100M", "10g": "10G", "10m": "10M", "1g": "1G", "2500m": "2500M", "40g": "40G", "56g": "56G", "full": "Full", "half": "Half"}, "table_function": {"export_csv": "Export CSV", "export_json": "Export JSON", "export_pdf": "Export PDF", "filter_desc": "Search", "limit_count": "<PERSON>.", "row_count": "Total", "selected_count": "Selected"}, "tooltip": {"add": "Add", "auto_refresh_disabled": "Auto Refresh: Disabled", "auto_refresh_enabled": "Auto Refresh: Enabled", "clear_logs": "Clear Logs", "close": "Close", "delete": "Delete", "duplicate_redundant_port": "Cannot assign ports with prexisting connections", "edit": "Edit", "export": "Export", "interface_disabled": "The Interface is disabled", "management_port_not_delete": "Management port cannot be deleted", "more": "More", "re_auth": "Re-<PERSON><PERSON>", "refresh": "Refresh", "reorder": "Reorder", "reorder_finish": "Finish Reordering", "reorder_priority": "Reorder Priorities", "rule_expired": "Rule Expired", "set_event_notifications": "Set Event Notifications"}, "top_nav": {"change_language": {"language": "Language", "title": "Change Language"}, "default_setting": {"confirmation_msg": "Are you sure you want to save running configuration as a custom default?", "confirmation_title": "Custom Default", "current_cfg_name": "Current configuration name: {{name}}", "current_cfg_name_tooltip": "Configuration name can be modified from \"Config Backup and Restore\"", "reset_confirmation_msg": "Are you sure you want to reset the system configuration to custom defaults?", "save_cfg_name_tooltip": "Configuration name in custom default saved in non-volatile memory", "saved_cfg_name": "Saved configuration name: {{name}}", "title": "Save Custom Default"}, "factory_default": {"confirmation_msg": "Are you sure you want to reset the system configurations to factory default?", "confirmation_title": "Factory Defaults", "have_cert_mgmt_msg": "Custom Default will be cleared after executing factory default.", "keep_cert_mgmt_msg": "Keep certificate database and configuration.", "setting_title": "Reset to <PERSON><PERSON><PERSON>s", "title": "Reset to Defaults"}, "logout": {"confirmation_msg": "Are you sure you want to log out?", "title": "Log Out"}, "restart_machine": {"confirmation_msg": "Are you sure you want to restart the device?", "confirmation_title": "Restart the device", "title": "Reboot"}, "user_profile": {"greeting": "Hi"}}, "unit": {"byte": "bytes", "connection": "connection", "day": "day", "days": "days", "db": "dB", "dbm": "dBm", "fps": "fps", "hour": "hour", "kbyte": "KBytes", "m": "M", "mb": "MB", "mbps": "Mbps", "millisec": "millisec.", "min": "min.", "ms": "ms", "ns": "ns", "percent": "%", "pkts": "pkt/s", "sec": "sec.", "times": "times", "watt": "<PERSON>", "watts": "<PERSON>", "x": "x"}}, "login": {"change_password": "Change Password", "default_password_bypass_desc": "Whether to bypass the modification of the default password?", "default_password_bypass_title": "Default Password Bypass", "factory_default_note": "Please note that you need to use the default network settings to re-establish a web-browser connection with your device.", "firmware_upgrade_note": "Please log in to the device again.", "Login_fail_records": "The latest login failure record(s).", "login_success_records_datetime": "Your last successful login was", "modify_password_notification": "Please change the default username and password in order to enhance security.", "package_fail_install": "Failed to install MXsecurity agent package", "package_success_install": "Successfully installed MXSecurity agent package", "password": "Password", "password_expire_notification": "Your password has expired. Please change your password.", "redirected_message": "Logout Successful", "username": "Username"}, "package": {"dlm": {"page_title": "DLM"}, "firewallAdvancedProtection": {"page_title": "Advanced Protection"}, "janusAdp": {"page_title": "ADP"}, "janusAssetRecognition": {"page_title": "Asset Recognition"}, "janusConfiguration": {"page_title": "Configuration"}, "janusDashboard": {"page_title": "Dashboard"}, "janusDp": {"page_title": "Domain Protection"}, "janusIps": {"page_title": "IPS"}, "janusPolicySetting": {"page_title": "Protocol Filter Policy"}, "networkSecurityManager": {"page_title": "MXsecurity"}}, "page_not_found": {"back_link": "Back to Index Page", "desc": "The requested URL was not found on this server.", "title": "Page Not Found :("}, "request_handler": {"action_exporting": "Exporting...", "action_importing": "Importing...", "action_loading": "Loading...", "action_ping": "Ping...", "action_regenerating": "Regenerating...", "action_saving": "Saving...", "action_upgrading": "Upgrading...", "action_upload": "Uploading..."}, "response_handler": {"backup_success": "Backup Successfully", "clear_success": "Clear Successfully.", "export_file_success": "Exported File Successfully", "export_ssl_cert": "Exported SSL Certificate Successfully", "import_cert_success": "Imported Certificate Successfully", "import_config": "Imported Configuration Successfully", "import_ssl_cert": "Imported SSL Certificate Successfully", "locator": "Trigger Device Locator Successfully", "re_auth_port_success": "Re-Authentication Port Successfully.", "res_complete_refresh": "Complete Refresh.", "res_copy": "<PERSON>pied", "res_cos_success": "CoS Settings Successfully Updated.", "res_dscp_success": "DSCP Settings Successfully Updated.", "res_entry_create_success": "Entry Successfully Created", "res_entry_delete_success": "Entry Successfully Deleted.", "res_entry_update_success": "Entry Successfully Updated.", "res_event_notification_success": "Updated Event Notification Successfully", "res_factory_default_success": "Factory Default Successfully.", "res_global_delete": "Successfully Deleted.", "res_global_install": "Successfully Installed.", "res_global_send": "Successfully Send.", "res_global_success": "Successfully Updated.", "res_global_uninstall": "Successfully Uninstalled.", "res_global_upgrade": "Successfully Upgraded.", "res_import_config_success": "Exported Config File Successfully. Device will restart immediately", "res_ping_success": "Ping Finished.", "res_port_success": "Port Settings Successfully Updated.", "res_regen_ssh_success": "SSH Key Successfully Regenerated.", "res_regen_ssl_success": "SSL Certificate Successfully Regenerated.", "res_save_to_startup_success": "Saved Running Configuration to Startup Configuration Successfully", "res_server_error": "Server Connection Error", "res_upgrading_firmware_success": "Upgraded Firmware Successfully. Device will restart immediately", "res_v3_account_update_success": "Authentication Account Successfully Updated.", "res_v3_host_update_success": "Updated SNMP v3 Host Successfully"}, "validators": {"at_least_one_recipient": "At least one recipient email address is required", "conflict_network_out_of_band_management": "{{ ip }} ip address is in the subnet of interface Out of Band Management", "conflict_network_segment_bridge": "{{ ip }} ip address is in the subnet of Bridge interface {{ name }}", "conflict_network_segment_gre": "IP address conflict detected. {{ ip }} overlaps GRE interface {{ name }}. Change the IP or change the interface, and then try again.", "conflict_network_segment_lan": "{{ ip }} ip address is in the subnet of LAN interface {{ name }}", "conflict_network_segment_virtual_interface": "{{ ip }} ip address is in the subnet of Virtual interface {{ name }}", "conflict_network_segment_wan": "{{ ip }} ip address is in the subnet of interface WAN", "conflict_network_segment_zone_bridge": "{{ ip }} ip address is in the subnet of Zone-based Bridge interface {{ name }}", "conflict_secondary_ip": "{{ ip }} ip address is in the subnet of interface Secondary IP", "duplicate_area_id": "Duplicate Area ID", "duplicate_certificate_name": "A certificate with the same name already exists. Please use a different name for the new certificate.", "duplicate_date": "Duplicated Date", "duplicate_interface": "Duplicated Interface", "duplicate_ip": "Duplicate IP", "duplicate_ip_and_mac_address": "This IP address and MAC address combination already exists.", "duplicate_ip_range": "Duplicate IP range", "duplicate_key_id": "Key ID cannot be duplicate", "duplicate_mac_address": "Duplicated MAC address", "duplicate_name": "Duplicated Name", "duplicate_netmask": "Duplicate Netmask", "duplicate_ospf_aggregation_rule": "Duplicate OSPF Aggregation rule", "duplicate_ospf_interface": "Duplicated OSPF Interface", "duplicate_password": "Your new password can not be the same as your original password. Please enter a new password.", "duplicate_pvid": "PVID cannot be duplicate", "duplicate_ring_coupling_ports": "Duplicate ring coupling ports", "duplicate_ring_ports": "Duplicate ring ports", "duplicate_rule": "The rule already exists.", "duplicate_static_route_rule": "Duplicate Static Route rule", "duplicate_subnet": "Duplicate subnets", "duplicate_udp_port": "Duplicate UDP port", "duplicate_vlan_id": "VLAN ID cannot be duplicate", "invalid": "Invalid", "invalid_area_id": "Invalid Area ID", "invalid_both_ring_port": "The port cannot belong to both rings or dual homing.", "invalid_char_and_not_dot_not_dash_in_begin_end": "Only a-z, A-Z, 0-9 are allowed, and the first and last characters cannot be . or -", "invalid_deadint_less": "Less than hello interval", "invalid_deadint_multiple": "Must be a multiple of hello interval", "invalid_email": "In<PERSON>id Email Address", "invalid_end_date": "End date should be later than or equal to start date", "invalid_end_time": "End time should be later than start time", "invalid_even_string": "String length must be even", "invalid_file_ext": "File extension must be {{ fileExtension }}", "invalid_format": "Not in correct format, must be a-z, A-Z, 0-9 or . - _ @ ! # $ % ^ & * ( ) /", "invalid_format_allow_comma": "Not in correct format, must be a-z, A-Z, 0-9 or . , - _ @ ! # $ % ^ & * ( ) /", "invalid_format_allow_space": "Only a maximum of {{count}} spaces allowed in the string", "invalid_format_first_not_number": "Not in correct format, must be a-z, A-Z, 0-9 or . - _ @ ! # $ % ^ & * ( ) / And the first word of name can't be number.", "invalid_format_no_more_hint": "Invalid Format", "invalid_format_not_space": "Not in correct format, must be a-z, A-Z, 0-9 or . - _ @ ! # $ % ^ & * ( ) / > <", "invalid_format_system_symbol": "Not in correct format, must be a-z, A-Z, 0-9 or . - _ @ ! # $ % ^ & * ( )", "invalid_format_system_symbol_pppoe": "Not in correct format, must be a-z, A-Z, 0-9 or . - _ @ ! # $ % ^ & * ( ) / \\", "invalid_format_virginia_guideline": "Not in correct format, must be a-z, A-Z, 0-9 or _ @ ! # $ % ^ & * ( ) . - + = { } [ ] | : ; , ~", "invalid_format_virginia_guideline_with_angle": "Not in correct format, must be a-z, A-Z, 0-9 or _ @ ! # $ % ^ & * ( ) . - + = { } [ ] | : ; , ~ > <", "invalid_grater_than": "{{ largeItem }} needs to be greater than {{ smallItem }}", "invalid_hex": "Invalid hex number", "invalid_integer": "Invalid integer", "invalid_ip_address": "Invalid IP Address", "invalid_ip_address_domain_name": "Invalid IP Address/ Domain Name", "invalid_ip_protocol": "Invalid IP Protocol", "invalid_ip_range_same_segment": "IP range should be within the same segment.", "invalid_mac_address": "Invalid MAC address", "invalid_mac_address_all_zero": "MAC address 00:00:00:00:00:00 is reserved", "invalid_max_length": "The maximum number of characters is {{max}}.", "invalid_max_udp_port": "The maximum number of UDP ports is {{max}}.", "invalid_max_vlan_size": "Due to VLAN size, only {{ max }} ports can be selected.", "invalid_multicast_address": "Invalid Multicast Address", "invalid_multicast_mac_address": "Invalid Multicast MAC Address", "invalid_netmask": "Invalid <PERSON>", "invalid_overide_rule_schedule": "The rule schedule overrides the other rule schedule", "invalid_ping_check": "Remote host IP address should be configured on the enabled interface first", "invalid_port_range": "Invalid port range", "invalid_positive_integer": "Invalid positive integer", "invalid_range": "Range is {{ rangeBegin }} to {{ rangeEnd }}", "invalid_regex_level_1": "Only allow a-z, A-Z, 0-9 or . - _ .", "invalid_regex_reserved_char_not_space": "Only allow a-z, A-Z, 0-9.", "invalid_rule_end_time": "The end time must be 15 minutes later than the start time.", "invalid_rule_start_time": "The rule schedule must be 15 minutes later than current time", "invalid_single_range": "Range is {{ singleNum }} or {{ rangeBegin }} to {{ rangeEnd }}", "invalid_udp_port": "Invalid UDP port", "ip_twin_over_limit": "Dedicated outgoing interface limit reached", "nat_deny_single_range_error": "Single to Range(Subnet) is forbidden.", "nat_l4_port_error": "L4 Port can be configured when TCP or UDP Protocol is configured.", "nat_one_range_one_range_error": "For One Range to One Range, width of Range should be equal on Original and Translated.", "nat_one_trans_error": "At least one Translated IP / Port is required.", "nat_ori_src_mac_error": "Original Source MAC can't be configured when Range or Subnet Original Source IP is configured.", "nat_range_range_error": "One / Multiple Range(Subnet) to One / Multiple Range(Subnet) is forbidden. (except for equal width One Range to One Range)", "nat_trans_sri_port_error": "Translated Source Port is forbidden when Dynamic Translated Source IP is configured.", "nat_trans_transiface_error": "Outgoing Interface can't be configured when translated destination IP or Port is configured.", "not_same_pvid_same_ip": "Different PVID must be used on the different IP address.", "only_lan": "Only LAN interface can be selected", "range_allow_comma": "{{ rangeBegin }} - {{ rangeEnd }}, allow comma(,)", "require_hexadecimal": "Hex digit", "require_min_length": "At least {{number}} characters", "require_range_between": "{{ rangeBegin }} - {{ rangeEnd }}", "require_range_single_between": "{{ singleNum }}, {{ rangeBegin }} - {{ rangeEnd }}", "required": "Required", "same_pvid_same_ip": "Same PVID must be used on the same IP address.", "wan_only_one": "WAN interface can only be selected once"}}