import { AfterViewInit, ChangeDetectionStrategy, Component, ViewChild } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { SelectionModel } from '@angular/cdk/collections';
import { MatDialog } from '@angular/material/dialog';
import { MatLegacyPaginator as MatPaginator } from '@angular/material/legacy-paginator';
import { MatLegacyTableDataSource as MatTableDataSource } from '@angular/material/legacy-table';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatSort } from '@angular/material/sort';

import { GlobalEvent, GlobalEventType } from '@mx-ros-web/models/global.event';
import { GenerateDocService } from '@mx-ros-web/mx-service/generate-doc.service';
import { TranslateService } from '@ngx-translate/core';
import { forEach } from 'lodash-es';
import { Observable, forkJoin } from 'rxjs';

import * as environment from '@mx-ros-web/environments/environment';
import { AppState } from '@mx-ros-web/shared/Service/app.service';
import { AuthService } from '@mx-ros-web/shared/Service/auth.service';
import { ErrorService } from '@mx-ros-web/shared/Service/error.service';
import { FetchService } from '@mx-ros-web/shared/Service/fetch.service';
import { UtilsService } from '@mx-ros-web/shared/Service/utils.service';
import { mediumDialogConfig, smallDialogConfig } from '@mx-ros-web/shared/dialog-config.service';

import { CategoryLevel, IpProtocolStr, SeverityLevel } from './eventlog.def';
import { FlushLogConfirmationDialogComponent } from './flush-log-confirmation-dialog/flush-log-confirmation-dialog';
import { ThresholdSettingDialogComponent } from './threshold-setting-dialog/threshold-setting-dialog';

@Component({
  templateUrl: './eventlog.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EventlogComponent implements AfterViewInit {
  noPermission = this.auth.authNodes.eventLog;
  ipSecNoPermission = this.auth.serviceDefine.SRV_IPSEC;
  registeredAction = [];
  registeredActionDisplay = [];
  registeredActionList = [];
  tabIndex = 0;
  readonly SeverityLevelSel = [
    {
      value: SeverityLevel.EMERGENCY,
      text: this.translate.instant('general.common_severity.emergency'),
    },
    {
      value: SeverityLevel.ALERT,
      text: this.translate.instant('general.common_severity.alert'),
    },
    {
      value: SeverityLevel.CRITICAL,
      text: this.translate.instant('general.common_severity.critical'),
    },
    {
      value: SeverityLevel.ERROR,
      text: this.translate.instant('general.common_severity.error'),
    },
    {
      value: SeverityLevel.WARNING,
      text: this.translate.instant('general.common_severity.warning'),
    },
    {
      value: SeverityLevel.NOTICE,
      text: this.translate.instant('general.common_severity.notice'),
    },
    {
      value: SeverityLevel.INFO,
      text: this.translate.instant('general.common_severity.info'),
    },
    {
      value: SeverityLevel.DEBUG,
      text: this.translate.instant('general.common_severity.debug'),
    },
  ];

  readonly CategorySel = [
    {
      value: CategoryLevel.SYSTEM,
      text: this.translate.instant('general.common_category.system'),
      firewall: false,
      file: 'system',
      permission: true,
    },
    {
      value: CategoryLevel.VPN,
      text: this.translate.instant('general.common_category.vpn'),
      firewall: false,
      file: 'vpn',
      permission: this.auth.serviceDefine.SRV_IPSEC,
    },
    {
      value: CategoryLevel.TRUST_ACCESS,
      text: this.translate.instant('general.common_category.trust_access'),
      firewall: true,
      file: 'trusted-access',
      permission: this.auth.serviceDefine.SRV_ACCESS,
    },
    {
      value: CategoryLevel.MALFORMED,
      text: this.translate.instant('general.common_category.malformed'),
      firewall: true,
      file: 'malformed-packets',
      permission: this.auth.serviceDefine.SRV_IPT_FILTER_GLOBAL,
    },
    {
      value: CategoryLevel.DOS,
      text: this.translate.instant('general.common_category.dos'),
      firewall: true,
      file: 'dos-policy',
      permission: this.auth.serviceDefine.SRV_IPT_DOS,
    },
    {
      value: CategoryLevel.DEV_LOCKDOWN,
      text: this.translate.instant('general.common_category.device_lockdown'),
      firewall: true,
      file: 'device-lockdown',
      permission: this.auth.serviceDefine.SRV_DEVICE_LOCKDOWN,
    },
    {
      value: CategoryLevel.OB_L3_POLICY,
      text: this.translate.instant('general.common_category.ob_l3_policy'),
      firewall: true,
      file: 'layer-3-7-policy',
      permission: this.auth.serviceDefine.SRV_L3L7_POLICY,
    },
    {
      value: CategoryLevel.DPI_POLICY,
      text: this.translate.instant('general.common_category.dpi_policy'),
      firewall: true,
      file: 'protocol-filter-policy',
      permission: this.auth.serviceDefine.SRV_PKG,
    },
    {
      value: CategoryLevel.ADP,
      text: this.translate.instant('general.common_category.adp'),
      firewall: true,
      file: 'adp',
      permission: this.auth.serviceDefine.SRV_PKG,
    },
    {
      value: CategoryLevel.IPS,
      text: this.translate.instant('general.common_category.ips'),
      firewall: true,
      file: 'ips',
      permission: this.auth.serviceDefine.SRV_PKG,
    },
    {
      value: CategoryLevel.SESSION_CONTROL,
      text: this.translate.instant('general.common_category.session_control'),
      firewall: true,
      file: 'session-control',
      permission: this.auth.serviceDefine.SRV_SESSION_CTRL,
    },
    {
      value: CategoryLevel.OB_L2_FILTER,
      text: this.translate.instant('general.common_category.ob_l2_filter'),
      firewall: true,
      file: 'layer2-filter',
      permission: this.auth.serviceDefine.SRV_L2_FILTER,
    },
    {
      value: CategoryLevel.OB_L3_FILTER,
      text: this.translate.instant('features.firewall_layer3_policy.page_title'),
      firewall: true,
      file: 'layer3-policy',
      permission: this.auth.serviceDefine.SRV_IPT_FILTER,
    },
    {
      value: CategoryLevel.PING_RESPONSE,
      text: this.translate.instant('features.eventlog.ping_response'),
      firewall: true,
      file: 'ping-response',
      permission: this.auth.serviceDefine.SRV_PING_RES,
    },
  ];
  selectedCategory = this.translate.instant('general.common_category.ob_l3_policy');
  selectedCategoryValue = this.auth.serviceDefine.SRV_L3L7_POLICY
    ? CategoryLevel.OB_L3_POLICY
    : CategoryLevel.DEV_LOCKDOWN;

  get CategoryLevel(): typeof CategoryLevel {
    return CategoryLevel;
  }

  @ViewChild('eventLogTableSort') eventLogTableSort: MatSort;
  @ViewChild('eventLogTablePaginator') eventLogTablePaginator: MatPaginator;
  eventLogTableDisplayedColumns: string[] = ['index', 'timestamp', 'severity', 'additional-message', 'dummy'];
  eventLogTableDataSource: MatTableDataSource<any> = new MatTableDataSource();
  eventLogTableSelection = new SelectionModel<any>(true, []);
  eventLogTableData = [];
  eventLogTableRows = [];
  eventLogTableDataLength;
  eventLogTablePanelPageSize = 20;
  eventLogTableMaxSize = 1000;
  eventLogSelect = new UntypedFormControl();

  @ViewChild('firewallLogTableSort') firewallLogTableSort: MatSort;
  @ViewChild('firewallLogTablePaginator') firewallLogTablePaginator: MatPaginator;
  firewallLogTableDisplayedColumns: string[] = [
    'index',
    'timestamp',
    'severity',
    'policy-id',
    'policy-name',
    'ether-type',
    'ip-protocol',
    'incoming-interface',
    'src-mac',
    'src-ip',
    'src-port',
    'outgoing-interface',
    'dst-ip',
    'dst-port',
    'tcp-flags',
    'icmp-type',
    'icmp-code',
    'action',
    'dummy',
  ];
  firewallLogTableDataSource: MatTableDataSource<any> = new MatTableDataSource();
  firewallLogTableSelection = new SelectionModel<any>(true, []);
  firewallLogTableData = [];
  firewallLogTableRows = [];
  firewallLogTableDataLength;
  firewallLogTablePanelPageSize = 20;
  firewallLogTableMaxSize = 1000;
  firewallLogSelect = new UntypedFormControl();

  @ViewChild('vpnLogTableSort') vpnLogTableSort: MatSort;
  @ViewChild('vpnLogTablePaginator') vpnLogTablePaginator: MatPaginator;
  vpnLogTableDisplayedColumns: string[] = ['index', 'timestamp', 'severity', 'additional-message', 'dummy'];
  vpnLogTableDataSource: MatTableDataSource<any> = new MatTableDataSource();
  vpnLogTableSelection = new SelectionModel<any>(true, []);
  vpnLogTableData = [];
  vpnLogTableRows = [];
  vpnLogTableDataLength;
  vpnLogTablePanelPageSize = 20;
  vpnLogTableMaxSize = 1000;

  @ViewChild('thresholdTableSort') thresholdTableSort: MatSort;
  @ViewChild('thresholdTablePaginator') thresholdTablePaginator: MatPaginator;
  thresholdTableDisplayedColumns: string[] = [
    'edit',
    'enable',
    'event-name',
    'warning-threshold',
    'oversize-action',
    'registered-action',
    'dummy',
  ];
  thresholdTableDataSource: MatTableDataSource<any> = new MatTableDataSource();
  thresholdTableSelection = new SelectionModel<any>(true, []);
  thresholdTableData = [];
  thresholdTableRows = [];
  thresholdTableDataLength;
  thresholdTablePanelPageSize = 20;
  thresholdTableMaxSize = 255;

  @ViewChild('assetRecognitionTableSort') set assetRecognitionTableSort(data: MatSort) {
    this.assetRecognitionTableDataSource.sort = data;
    this.assetRecognitionTableDataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'assetIpAddress':
          return item.assetIpAddress;
        case 'assetMacAddress':
          return item.assetMacAddress;
        default:
          return item[property];
      }
    };
  }
  @ViewChild('assetRecognitionTablePaginator') set assetRecognitionTablePaginator(data: MatPaginator) {
    this.assetRecognitionTableDataSource.paginator = data;
  }
  assetRecognitionTableDisplayedColumns: string[] = [
    'index',
    'timestamp',
    'severity',
    'asset-ip-address',
    'asset-mac-address',
    'additional-message',
    'dummy',
  ];
  assetRecognitionTableDataSource: MatTableDataSource<any> = new MatTableDataSource();
  assetRecognitionTableSelection = new SelectionModel<any>(true, []);
  assetRecognitionTableData = [];
  assetRecognitionTableRows = [];
  assetRecognitionTableDataLength;
  assetRecognitionTablePanelPageSize = 20;
  assetRecognitionTableMaxSize = 1000;
  assetRecognitionPermission = false;

  autoRestoreForm = new UntypedFormGroup({
    autoLoadEventLog: new UntypedFormControl(false, Validators.required),
  });
  abc02Enable = this.auth.serviceDefine.SRV_ABC02;

  // button tooltip text
  systemText: string;
  vpnText: string;
  firewallText: string;
  assetRecognitionText: string;

  private uriRequestData;

  constructor(
    private auth: AuthService,
    private appState: AppState,
    private http: HttpClient,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private errorService: ErrorService,
    private utils: UtilsService,
    private fetchService: FetchService,
    private translate: TranslateService,
    private docService: GenerateDocService
  ) {}

  ngAfterViewInit(): void {
    this.refreshTabContent().subscribe({
      complete: () => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
      },
      error: error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      },
    });
    this.setupTableSetting();
  }

  refreshTabContent(): Observable<any> {
    return new Observable(observer => {
      this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
      this.thresholdTableData = [];
      this.eventLogTableData = [];
      this.firewallLogTableData = [];
      this.vpnLogTableData = [];

      const request = {
        eventLogMgmt: this.http.get(`${environment.uriRequestURL}/setting/data/SRV_EVENTLOG_MGMT`),
        abc02: this.http.get(`${environment.uriRequestURL}/setting/data/SRV_ABC02`),
        getDpiCap: this.http.get(`${environment.uriRequestURL}/pkg_web/getDpiCap`),
      };

      if (!this.auth.serviceDefine.SRV_ABC02) {
        delete request.abc02;
      }

      forkJoin(request).subscribe(
        (data: any) => {
          this.uriRequestData = {
            eventLogMgmt: data.eventLogMgmt,
            abc02: data?.abc02,
            getDpiCap: data.getDpiCap,
          };

          if (data.getDpiCap?.dpiCap?.includes('DP') && !this.CategorySel.some(cat => cat.value === CategoryLevel.DP)) {
            this.CategorySel.push({
              value: CategoryLevel.DP,
              text: this.translate.instant('features.eventlog.dp'),
              firewall: true,
              file: 'domain-protection',
              permission: true
            });
          }

          data.eventLogMgmt.SRV_EVENTLOG_MGMT_Table.forEach((entry, index) => {
            if (index < CategoryLevel.MAX) {
              this.getRegisteredAction(data.eventLogMgmt.SRV_EVENTLOG_MGMT_Table[index]);

              const categoryName = this.utils.GetSelName(index, this.CategorySel);
              // 判斷是否顯示
              const category = this.CategorySel.find(cat => cat.text === categoryName);
              if (category?.permission) {
                const isEnabled = entry.capacity_threshold !== 0;
                const isWarningThreshold = entry.capacity_threshold === 0;
                const isOverwrite = entry.oversize_action === '0';
                this.thresholdTableData.push({
                  index: index,
                  enableDisplay: isEnabled ? 'general.common.enable' : 'general.common.disable',
                  categoryName: categoryName,
                  warningThreshold: isWarningThreshold ? this.utils.emptyDash : `${entry.capacity_threshold}%`,
                  oversizeAction: isOverwrite
                    ? 'features.eventlog.oversize_action_overwrite'
                    : 'features.eventlog.oversize_action_stop',
                  registeredAction: this.registeredAction,
                  registeredActionDisplay: this.registeredActionDisplay,
                  registeredActionList: this.registeredActionList,
                });
              }
            }
          });

          this.getLogTableContent(CategoryLevel.SYSTEM, 'eventLog');
          this.checkEventLog();
          this.checkFirewallLog();
          this.getLogTableContent(CategoryLevel.VPN, 'vpnLog');
          this.getLogTableContent(CategoryLevel.OB_AR, 'arLog');

          if (!this.abc02Enable) {
            this.autoRestoreForm.get('autoLoadEventLog').disable();
          }
          this.autoRestoreForm.patchValue({
            autoLoadEventLog: data?.abc02?.auto_log === '1',
          });

          this.assetRecognitionPermission = data.getDpiCap?.dpiCap?.includes('AR');
          this.systemText = this.translate.instant('general.common_category.system');
          this.vpnText = this.translate.instant('general.common_category.vpn');
          this.firewallText = this.CategorySel.find(cat => cat.value === this.selectedCategoryValue).text;
          this.assetRecognitionText = this.translate.instant('features.eventlog.asset-recognition');

          observer.complete();
        },
        error => {
          observer.error(error);
        }
      );
    });
  }

  moxaLogFileName(showCategory: number): string {
    let categoryFile: string;
    forEach(this.CategorySel, category => {
      if (category.value === showCategory) {
        categoryFile = category.file;
      }
    });

    return categoryFile;
  }

  refresh(): void {
    this.refreshTabContent().subscribe({
      complete: () => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
      },
      error: error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      },
    });
    this.setupTableSetting();
  }

  flushLogEntry(category: CategoryLevel): void {
    let categoryName: string;
    switch (category) {
      case CategoryLevel.SYSTEM:
        categoryName = this.systemText;
        break;
      case CategoryLevel.VPN:
        categoryName = this.vpnText;
        break;
      case CategoryLevel.MOXA_LOG_CLEAR_ALL:
        categoryName = this.translate.instant('general.common.all');
        break;
      case CategoryLevel.OB_AR:
        categoryName = this.assetRecognitionText;
        break;
      case this.selectedCategoryValue:
        categoryName = this.firewallText;
        break;
    }

    smallDialogConfig.data = {
      category: category,
      categoryName: categoryName,
    };
    const dialogRef = this.dialog.open(FlushLogConfirmationDialogComponent, smallDialogConfig);

    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        this.refresh();
      }
    });
  }

  selectCategory(category: number): void {
    this.selectedCategory = this.utils.GetSelName(category, this.CategorySel);
    this.selectedCategoryValue = category;
    this.getLogTableContent(this.selectedCategoryValue, 'firewallLog');
    this.firewallText = this.CategorySel.find(cat => cat.value === this.selectedCategoryValue).text;
  }

  getLogTableContent(category: number, tableData: string): void {
    const logTableData = [];
    let logTableDisplayedColumns = [];
    forkJoin({
      eventLogEntry: this.http.get(`${environment.uriRequestURL}/status/eventLogEntry?show_category=${category}`),
    }).subscribe((data: any) => {
      switch (category) {
        case CategoryLevel.SYSTEM:
        case CategoryLevel.VPN:
          logTableDisplayedColumns = ['index', 'timestamp', 'severity', 'additional-message', 'dummy'];
          data.eventLogEntry.forEach((entry, index) => {
            logTableData.push({
              index: parseInt(index, 10) + 1,
              timestamp: `${entry.DATE} ${entry.TIME}`,
              severity: this.utils.GetSelName(entry.SEVERITY, this.SeverityLevelSel),
              additionalMessage: entry.LOGSTR,
            });
          });
          break;
        case CategoryLevel.TRUST_ACCESS:
          logTableDisplayedColumns = [
            'index',
            'timestamp',
            'severity',
            'ether-type',
            'ip-protocol',
            'incoming-interface',
            'src-mac',
            'src-ip',
            'src-port',
            'outgoing-interface',
            'dst-ip',
            'dst-port',
            'tcp-flags',
            'icmp-type',
            'icmp-code',
            'action',
            'additional-message',
            'dummy',
          ];
          data.eventLogEntry.forEach((entry, index) => {
            logTableData.push({
              index: parseInt(index, 10) + 1,
              timestamp: `${entry.DATE} ${entry.TIME}`,
              severity: this.utils.GetSelName(entry.SEVERITY, this.SeverityLevelSel),
              etherType: entry.ETHERTYPE,
              ipProtocol: entry.IPPROTO,
              incomingInterface: entry.INIF !== '' ? entry.INIF : this.utils.emptyDash,
              srcMac: entry.SRCMAC !== '' ? entry.SRCMAC : this.utils.emptyDash,
              srcIp: entry.SRCIP,
              srcPort: this.getL4PortDisplay(entry.SRCPORT, entry.IPPROTO),
              outgoingInterface: entry.OUTIF !== '' ? entry.OUTIF : this.utils.emptyDash,
              dstIp: entry.DSTIP,
              dstPort: this.getL4PortDisplay(entry.DSTPORT, entry.IPPROTO),
              tcpFlags: this.getTcpFlagDisplay(entry.TCPFLAG, entry.IPPROTO),
              icmpType: this.getIcmpDisplay(entry.ICMPTYPE, entry.IPPROTO),
              icmpCode: this.getIcmpDisplay(entry.ICMPCODE, entry.IPPROTO),
              action: entry.ACTION,
              additionalMessage: entry.ADDMSG,
            });
          });
          break;
        // eslint-disable-next-line no-duplicate-case
        case CategoryLevel.TRUST_ACCESS:
        case CategoryLevel.MALFORMED:
        case CategoryLevel.DEV_LOCKDOWN:
        case CategoryLevel.OB_L3_FILTER:
        case CategoryLevel.PING_RESPONSE:
          logTableDisplayedColumns = [
            'index',
            'timestamp',
            'severity',
            'ether-type',
            'ip-protocol',
            'incoming-interface',
            'src-mac',
            'src-ip',
            'src-port',
            'outgoing-interface',
            'dst-ip',
            'dst-port',
            'tcp-flags',
            'icmp-type',
            'icmp-code',
            'action',
            'additional-message',
            'dummy',
          ];
          data.eventLogEntry.forEach((entry, index) => {
            logTableData.push({
              index: parseInt(index, 10) + 1,
              timestamp: `${entry.DATE} ${entry.TIME}`,
              severity: this.utils.GetSelName(entry.SEVERITY, this.SeverityLevelSel),
              etherType: entry.ETHERTYPE,
              ipProtocol: entry.IPPROTO,
              incomingInterface: entry.INIF !== '' ? entry.INIF : this.utils.emptyDash,
              srcMac: entry.SRCMAC !== '' ? entry.SRCMAC : this.utils.emptyDash,
              srcIp: entry.SRCIP,
              srcPort: this.getL4PortDisplay(entry.SRCPORT, entry.IPPROTO),
              outgoingInterface: entry.OUTIF !== '' ? entry.OUTIF : this.utils.emptyDash,
              dstIp: entry.DSTIP,
              dstPort: this.getL4PortDisplay(entry.DSTPORT, entry.IPPROTO),
              tcpFlags: this.getTcpFlagDisplay(entry.TCPFLAG, entry.IPPROTO),
              icmpType: this.getIcmpDisplay(entry.ICMPTYPE, entry.IPPROTO),
              icmpCode: this.getIcmpDisplay(entry.ICMPCODE, entry.IPPROTO),
              action: entry.ACTION,
              additionalMessage: entry.ADDMSG,
            });
          });
          break;
        case CategoryLevel.DOS:
          logTableDisplayedColumns = [
            'index',
            'timestamp',
            'severity',
            'ether-type',
            'sub-category',
            'ip-protocol',
            'incoming-interface',
            'src-mac',
            'src-ip',
            'src-port',
            'outgoing-interface',
            'dst-ip',
            'dst-port',
            'tcp-flags',
            'icmp-type',
            'icmp-code',
            'action',
            'additional-message',
            'dummy',
          ];
          data.eventLogEntry.forEach((entry, index) => {
            logTableData.push({
              index: parseInt(index, 10) + 1,
              timestamp: `${entry.DATE} ${entry.TIME}`,
              severity: this.utils.GetSelName(entry.SEVERITY, this.SeverityLevelSel),
              etherType: entry.ETHERTYPE,
              subCategory: entry.SUBCATE,
              ipProtocol: entry.IPPROTO,
              incomingInterface: entry.INIF !== '' ? entry.INIF : this.utils.emptyDash,
              srcMac: entry.SRCMAC !== '' ? entry.SRCMAC : this.utils.emptyDash,
              srcIp: entry.SRCIP,
              srcPort: this.getL4PortDisplay(entry.SRCPORT, entry.IPPROTO),
              outgoingInterface: entry.OUTIF !== '' ? entry.OUTIF : this.utils.emptyDash,
              dstIp: entry.DSTIP,
              dstPort: this.getL4PortDisplay(entry.DSTPORT, entry.IPPROTO),
              tcpFlags: this.getTcpFlagDisplay(entry.TCPFLAG, entry.IPPROTO),
              icmpType: this.getIcmpDisplay(entry.ICMPTYPE, entry.IPPROTO),
              icmpCode: this.getIcmpDisplay(entry.ICMPCODE, entry.IPPROTO),
              action: entry.ACTION,
              additionalMessage: entry.ADDMSG,
            });
          });
          break;
        case CategoryLevel.OB_L3_POLICY:
          logTableDisplayedColumns = [
            'index',
            'timestamp',
            'severity',
            'policy-id',
            'policy-name',
            'ether-type',
            'ip-protocol',
            'incoming-interface',
            'src-mac',
            'src-ip',
            'src-port',
            'outgoing-interface',
            'dst-ip',
            'dst-port',
            'tcp-flags',
            'icmp-type',
            'icmp-code',
            'action',
            'dummy',
          ];
          data.eventLogEntry.forEach((entry, index) => {
            logTableData.push({
              index: parseInt(index, 10) + 1,
              timestamp: `${entry.DATE} ${entry.TIME}`,
              severity: this.utils.GetSelName(entry.SEVERITY, this.SeverityLevelSel),
              policyId: entry.POLICYID_STR || entry.POLICYID,
              policyName: entry.POLICYNAME,
              etherType: entry.ETHERTYPE,
              ipProtocol: entry.IPPROTO,
              incomingInterface: entry.INIF !== '' ? entry.INIF : this.utils.emptyDash,
              srcMac: entry.SRCMAC !== '' ? entry.SRCMAC : this.utils.emptyDash,
              srcIp: entry.SRCIP,
              srcPort: this.getL4PortDisplay(entry.SRCPORT, entry.IPPROTO),
              outgoingInterface: entry.OUTIF !== '' ? entry.OUTIF : this.utils.emptyDash,
              dstIp: entry.DSTIP,
              dstPort: this.getL4PortDisplay(entry.DSTPORT, entry.IPPROTO),
              tcpFlags: this.getTcpFlagDisplay(entry.TCPFLAG, entry.IPPROTO),
              icmpType: this.getIcmpDisplay(entry.ICMPTYPE, entry.IPPROTO),
              icmpCode: this.getIcmpDisplay(entry.ICMPCODE, entry.IPPROTO),
              action: entry.ACTION,
            });
          });
          break;
        case CategoryLevel.DPI_POLICY:
          logTableDisplayedColumns = [
            'index',
            'timestamp',
            'severity',
            'application-protocol',
            'policy-id',
            'policy-name',
            'ether-type',
            'ip-protocol',
            'incoming-interface',
            'src-ip',
            'src-port',
            'outgoing-interface',
            'dst-ip',
            'dst-port',
            'action',
            'dummy',
          ];
          data.eventLogEntry.forEach((entry, index) => {
            logTableData.push({
              index: parseInt(index, 10) + 1,
              timestamp: `${entry.DATE} ${entry.TIME}`,
              severity: this.utils.GetSelName(entry.SEVERITY, this.SeverityLevelSel),
              applicationProtocol: entry.APPPROTO,
              policyId: entry.POLICYID_STR || entry.POLICYID,
              policyName: entry.POLICYNAME,
              etherType: entry.ETHERTYPE,
              ipProtocol: entry.IPPROTO,
              incomingInterface: entry.INIF !== '' ? entry.INIF : this.utils.emptyDash,
              srcMac: entry.SRCMAC !== '' ? entry.SRCMAC : this.utils.emptyDash,
              srcIp: entry.SRCIP,
              srcPort: this.getL4PortDisplay(entry.SRCPORT, entry.IPPROTO),
              outgoingInterface: entry.OUTIF !== '' ? entry.OUTIF : this.utils.emptyDash,
              dstIp: entry.DSTIP,
              dstPort: this.getL4PortDisplay(entry.DSTPORT, entry.IPPROTO),
              tcpFlags: this.getTcpFlagDisplay(entry.TCPFLAG, entry.IPPROTO),
              icmpType: this.getIcmpDisplay(entry.ICMPTYPE, entry.IPPROTO),
              icmpCode: this.getIcmpDisplay(entry.ICMPCODE, entry.IPPROTO),
              action: entry.ACTION,
            });
          });
          break;
        case CategoryLevel.ADP:
          logTableDisplayedColumns = [
            'index',
            'timestamp',
            'application-protocol',
            'policy-id',
            'policy-name',
            'ether-type',
            'ip-protocol',
            'incoming-interface',
            'src-ip',
            'src-port',
            'outgoing-interface',
            'dst-ip',
            'dst-port',
            'action',
            'dummy',
          ];
          data.eventLogEntry.forEach((entry, index) => {
            logTableData.push({
              index: parseInt(index, 10) + 1,
              timestamp: `${entry.DATE} ${entry.TIME}`,
              applicationProtocol: entry.APPPROTO,
              policyId: entry.POLICYID_STR || entry.POLICYID,
              policyName: entry.POLICYNAME,
              etherType: entry.ETHERTYPE,
              ipProtocol: entry.IPPROTO,
              incomingInterface: entry.INIF !== '' ? entry.INIF : this.utils.emptyDash,
              srcMac: entry.SRCMAC !== '' ? entry.SRCMAC : this.utils.emptyDash,
              srcIp: entry.SRCIP,
              srcPort: this.getL4PortDisplay(entry.SRCPORT, entry.IPPROTO),
              outgoingInterface: entry.OUTIF !== '' ? entry.OUTIF : this.utils.emptyDash,
              dstIp: entry.DSTIP,
              dstPort: this.getL4PortDisplay(entry.DSTPORT, entry.IPPROTO),
              tcpFlags: this.getTcpFlagDisplay(entry.TCPFLAG, entry.IPPROTO),
              icmpType: this.getIcmpDisplay(entry.ICMPTYPE, entry.IPPROTO),
              icmpCode: this.getIcmpDisplay(entry.ICMPCODE, entry.IPPROTO),
              action: entry.ACTION,
            });
          });
          break;
        case CategoryLevel.IPS:
          logTableDisplayedColumns = [
            'index',
            'timestamp',
            'ips-severity',
            'ips-category',
            'policy-id',
            'policy-name',
            'ether-type',
            'ip-protocol',
            'incoming-interface',
            'src-mac',
            'src-ip',
            'src-port',
            'outgoing-interface',
            'dst-ip',
            'dst-port',
            'tcp-flags',
            'action',
            'dummy',
          ];
          data.eventLogEntry.forEach((entry, index) => {
            logTableData.push({
              index: parseInt(index, 10) + 1,
              timestamp: `${entry.DATE} ${entry.TIME}`,
              ipsSeverity: entry.IPSSEVERITY,
              ipsCategory: entry.IPSCATE,
              policyId: entry.POLICYID_STR || entry.POLICYID,
              policyName: entry.POLICYNAME,
              etherType: entry.ETHERTYPE,
              ipProtocol: entry.IPPROTO,
              incomingInterface: entry.INIF !== '' ? entry.INIF : this.utils.emptyDash,
              srcMac: entry.SRCMAC !== '' ? entry.SRCMAC : this.utils.emptyDash,
              srcIp: entry.SRCIP,
              srcPort: this.getL4PortDisplay(entry.SRCPORT, entry.IPPROTO),
              outgoingInterface: entry.OUTIF !== '' ? entry.OUTIF : this.utils.emptyDash,
              dstIp: entry.DSTIP,
              dstPort: this.getL4PortDisplay(entry.DSTPORT, entry.IPPROTO),
              tcpFlags: this.getTcpFlagDisplay(entry.TCPFLAG, entry.IPPROTO),
              icmpType: this.getIcmpDisplay(entry.ICMPTYPE, entry.IPPROTO),
              icmpCode: this.getIcmpDisplay(entry.ICMPCODE, entry.IPPROTO),
              action: entry.ACTION,
            });
          });
          break;
        case CategoryLevel.SESSION_CONTROL:
          logTableDisplayedColumns = [
            'index',
            'timestamp',
            'severity',
            'policy-id',
            'policy-name',
            'ether-type',
            'ip-protocol',
            'incoming-interface',
            'src-mac',
            'src-ip',
            'src-port',
            'outgoing-interface',
            'dst-ip',
            'dst-port',
            'tcp-flags',
            'icmp-type',
            'icmp-code',
            'action',
            'dummy',
          ];
          data.eventLogEntry.forEach((entry, index) => {
            logTableData.push({
              index: parseInt(index, 10) + 1,
              timestamp: `${entry.DATE} ${entry.TIME}`,
              severity: this.utils.GetSelName(entry.SEVERITY, this.SeverityLevelSel),
              policyId: entry.POLICYID_STR || entry.POLICYID,
              policyName: entry.POLICYNAME,
              etherType: entry.ETHERTYPE,
              ipProtocol: entry.IPPROTO,
              incomingInterface: entry.INIF !== '' ? entry.INIF : this.utils.emptyDash,
              srcMac: entry.SRCMAC !== '' ? entry.SRCMAC : this.utils.emptyDash,
              srcIp: entry.SRCIP,
              srcPort: this.getL4PortDisplay(entry.SRCPORT, entry.IPPROTO),
              outgoingInterface: entry.OUTIF !== '' ? entry.OUTIF : this.utils.emptyDash,
              dstIp: entry.DSTIP,
              dstPort: this.getL4PortDisplay(entry.DSTPORT, entry.IPPROTO),
              tcpFlags: this.getTcpFlagDisplay(entry.TCPFLAG, entry.IPPROTO),
              icmpType: this.getIcmpDisplay(entry.ICMPTYPE, entry.IPPROTO),
              icmpCode: this.getIcmpDisplay(entry.ICMPCODE, entry.IPPROTO),
              action: entry.ACTION,
            });
          });
          break;
        case CategoryLevel.OB_L2_FILTER:
          logTableDisplayedColumns = [
            'index',
            'timestamp',
            'severity',
            'ether-type',
            'src-mac',
            'dst-mac',
            'action',
            'dummy',
          ];
          data.eventLogEntry.forEach((entry, index) => {
            logTableData.push({
              index: parseInt(index, 10) + 1,
              timestamp: `${entry.DATE} ${entry.TIME}`,
              severity: this.utils.GetSelName(entry.SEVERITY, this.SeverityLevelSel),
              etherType: entry.ETHERTYPE,
              srcMac: entry.SRCMAC !== '' ? entry.SRCMAC : this.utils.emptyDash,
              dstMac: entry.DSTMAC !== '' ? entry.DSTMAC : this.utils.emptyDash,
              action: entry.ACTION,
            });
          });
          break;
        case CategoryLevel.OB_AR:
          logTableDisplayedColumns = [
            'index',
            'timestamp',
            'severity',
            'asset-ip-address',
            'asset-mac-address',
            'additional-message',
          ];
          data.eventLogEntry.forEach((entry, index) => {
            logTableData.push({
              index: parseInt(index, 10) + 1,
              timestamp: `${entry.DATE} ${entry.TIME}`,
              severity: this.utils.GetSelName(entry.SEVERITY, this.SeverityLevelSel),
              assetIpAddress: entry.IP,
              assetMacAddress: entry.MAC,
              additionalMessage: entry.CURRSTATUS
                ? this.translate.instant('features.eventlog.asset_recognition_online')
                : this.translate.instant('features.eventlog.asset_recognition_offline'),
            });
          });
          break;
        case CategoryLevel.DP:
          logTableDisplayedColumns = [
            'index',
            'timestamp',
            'severity',
            'policy-id',
            'domain',
            'src-ip',
            'src-port',
            'dst-ip',
            'dst-port',
            'protocol',
            'action',
          ];
          data.eventLogEntry.forEach((entry, index) => {
            logTableData.push({
              index: parseInt(index, 10) + 1,
              timestamp: `${entry.DATE} ${entry.TIME}`,
              severity: this.utils.GetSelName(entry.SEVERITY, this.SeverityLevelSel),
              policyId: entry.POLICYID_STR || entry.POLICYID,
              domain: entry.DOMAINNAME,
              srcIp: entry.SRCIP,
              srcPort: this.getL4PortDisplay(entry.SRCPORT, entry.IPPROTO),
              dstIp: entry.DSTIP,
              dstPort: this.getL4PortDisplay(entry.DSTPORT, entry.IPPROTO),
              protocol: entry.APPPROTO,
              action: entry.ACTION,
            });
          });
          break;
      }

      switch (tableData) {
        case 'eventLog':
          this.eventLogTableDisplayedColumns = logTableDisplayedColumns;
          this.eventLogTableData = logTableData;
          break;
        case 'firewallLog':
          this.firewallLogTableDisplayedColumns = logTableDisplayedColumns;
          this.firewallLogTableData = logTableData;
          break;
        case 'vpnLog':
          this.vpnLogTableDisplayedColumns = logTableDisplayedColumns;
          this.vpnLogTableData = logTableData;
          break;
        case 'arLog':
          this.assetRecognitionTableDisplayedColumns = logTableDisplayedColumns;
          this.assetRecognitionTableData = logTableData;
          break;
      }

      this.updateTableData();
    });
  }

  updateTableData(): void {
    this.eventLogTableRows = this.eventLogTableData.slice(0, this.eventLogTablePanelPageSize);
    this.eventLogTableDataSource.data = this.eventLogTableData;
    this.eventLogTableSelection.clear();

    this.firewallLogTableRows = this.firewallLogTableData.slice(0, this.firewallLogTablePanelPageSize);
    this.firewallLogTableDataSource.data = this.firewallLogTableData;
    this.firewallLogTableSelection.clear();

    this.vpnLogTableRows = this.vpnLogTableData.slice(0, this.vpnLogTablePanelPageSize);
    this.vpnLogTableDataSource.data = this.vpnLogTableData;
    this.vpnLogTableSelection.clear();

    this.thresholdTableRows = this.thresholdTableData.slice(0, this.thresholdTablePanelPageSize);
    this.thresholdTableDataSource.data = this.thresholdTableData;
    this.thresholdTableSelection.clear();

    this.assetRecognitionTableRows = this.assetRecognitionTableData.slice(0, this.assetRecognitionTablePanelPageSize);
    this.assetRecognitionTableDataSource.data = this.assetRecognitionTableData;
    this.assetRecognitionTableSelection.clear();
  }

  updateTableFilter(filterValue: string, dataSource: MatTableDataSource<any>): void {
    dataSource.filter = filterValue?.trim().toLowerCase();
  }

  setupTableSetting(): void {
    this.eventLogTableDataSource.paginator = this.eventLogTablePaginator;
    this.eventLogTableDataSource.sort = this.eventLogTableSort;
    this.eventLogTableDataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'ips-severity':
          return item.ipsSeverity;
        case 'sub-category':
          return item.subCategory;
        case 'application-protocol':
          return item.applicationProtocol;
        case 'ips-category':
          return item.ipsCategory;
        case 'policy-id':
          return item.policyId;
        case 'policy-name':
          return item.policyName;
        case 'ether-type':
          return item.etherType;
        case 'ip-protocol':
          return item.ipProtocol;
        case 'incoming-interface':
          return item.incomingInterface;
        case 'src-mac':
          return item.srcMac;
        case 'src-ip':
          return item.srcIp;
        case 'src-port':
          return item.srcPort;
        case 'outgoing-interface':
          return item.outgoingInterface;
        case 'dst-ip':
          return item.dstIp;
        case 'dst-port':
          return item.dstPort;
        case 'tcp-flags':
          return item.tcpFlags;
        case 'icmp-type':
          return item.icmpType;
        case 'icmp-code':
          return item.icmpCode;
        case 'additional-message':
          return item.additionalMessage;

        default:
          return item[property];
      }
    };
    this.eventLogTableDataSource.filterPredicate = (data: any, filter: string): boolean => {
      return (
        String(data.index).toLowerCase().indexOf(filter) !== -1 ||
        String(data.timestamp).toLowerCase().indexOf(filter) !== -1 ||
        String(data.severity).toLowerCase().indexOf(filter) !== -1 ||
        String(data.ipsSeverity).toLowerCase().indexOf(filter) !== -1 ||
        String(data.subCategory).toLowerCase().indexOf(filter) !== -1 ||
        String(data.applicationProtocol).toLowerCase().indexOf(filter) !== -1 ||
        String(data.ipsCategory).toLowerCase().indexOf(filter) !== -1 ||
        String(data.policyId).toLowerCase().indexOf(filter) !== -1 ||
        String(data.policyName).toLowerCase().indexOf(filter) !== -1 ||
        String(data.ipProtocol).toLowerCase().indexOf(filter) !== -1 ||
        String(data.incomingInterface).toLowerCase().indexOf(filter) !== -1 ||
        String(data.srcMac).toLowerCase().indexOf(filter) !== -1 ||
        String(data.srcIp).toLowerCase().indexOf(filter) !== -1 ||
        String(data.srcPort).toLowerCase().indexOf(filter) !== -1 ||
        String(data.outgoingInterface).toLowerCase().indexOf(filter) !== -1 ||
        String(data.dstIp).toLowerCase().indexOf(filter) !== -1 ||
        String(data.dstPort).toLowerCase().indexOf(filter) !== -1 ||
        String(data.tcpFlags).toLowerCase().indexOf(filter) !== -1 ||
        String(data.icmpType).toLowerCase().indexOf(filter) !== -1 ||
        String(data.icmpCode).toLowerCase().indexOf(filter) !== -1 ||
        String(data.action).toLowerCase().indexOf(filter) !== -1 ||
        String(data.additionalMessage).toLowerCase().indexOf(filter) !== -1 ||
        String(data.domain).toLowerCase().indexOf(filter) !== -1 ||
        String(data.protocol).toLowerCase().indexOf(filter) !== -1
      );
    };

    this.firewallLogTableDataSource.paginator = this.firewallLogTablePaginator;
    this.firewallLogTableDataSource.sort = this.firewallLogTableSort;
    this.firewallLogTableDataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'ips-severity':
          return item.ipsSeverity;
        case 'sub-category':
          return item.subCategory;
        case 'application-protocol':
          return item.applicationProtocol;
        case 'ips-category':
          return item.ipsCategory;
        case 'policy-id':
          return item.policyId;
        case 'policy-name':
          return item.policyName;
        case 'ether-type':
          return item.etherType;
        case 'ip-protocol':
          return item.ipProtocol;
        case 'incoming-interface':
          return item.incomingInterface;
        case 'src-mac':
          return item.srcMac;
        case 'dst-mac':
          return item.dstMac;
        case 'src-ip':
          return item.srcIp;
        case 'src-port':
          return item.srcPort;
        case 'outgoing-interface':
          return item.outgoingInterface;
        case 'dst-ip':
          return item.dstIp;
        case 'dst-port':
          return item.dstPort;
        case 'tcp-flags':
          return item.tcpFlags;
        case 'icmp-type':
          return item.icmpType;
        case 'icmp-code':
          return item.icmpCode;
        case 'additional-message':
          return item.additionalMessage;
        default:
          return item[property];
      }
    };
    this.firewallLogTableDataSource.filterPredicate = (data: any, filter: string): boolean => {
      return (
        String(data.index).toLowerCase().indexOf(filter) !== -1 ||
        String(data.timestamp).toLowerCase().indexOf(filter) !== -1 ||
        String(data.severity).toLowerCase().indexOf(filter) !== -1 ||
        String(data.ipsSeverity).toLowerCase().indexOf(filter) !== -1 ||
        String(data.subCategory).toLowerCase().indexOf(filter) !== -1 ||
        String(data.applicationProtocol).toLowerCase().indexOf(filter) !== -1 ||
        String(data.ipsCategory).toLowerCase().indexOf(filter) !== -1 ||
        String(data.policyId).toLowerCase().indexOf(filter) !== -1 ||
        String(data.policyName).toLowerCase().indexOf(filter) !== -1 ||
        String(data.ipProtocol).toLowerCase().indexOf(filter) !== -1 ||
        String(data.incomingInterface).toLowerCase().indexOf(filter) !== -1 ||
        String(data.srcMac).toLowerCase().indexOf(filter) !== -1 ||
        String(data.srcIp).toLowerCase().indexOf(filter) !== -1 ||
        String(data.srcPort).toLowerCase().indexOf(filter) !== -1 ||
        String(data.outgoingInterface).toLowerCase().indexOf(filter) !== -1 ||
        String(data.dstIp).toLowerCase().indexOf(filter) !== -1 ||
        String(data.dstPort).toLowerCase().indexOf(filter) !== -1 ||
        String(data.tcpFlags).toLowerCase().indexOf(filter) !== -1 ||
        String(data.icmpType).toLowerCase().indexOf(filter) !== -1 ||
        String(data.icmpCode).toLowerCase().indexOf(filter) !== -1 ||
        String(data.action).toLowerCase().indexOf(filter) !== -1 ||
        String(data.additionalMessage).toLowerCase().indexOf(filter) !== -1
      );
    };

    this.vpnLogTableDataSource.paginator = this.vpnLogTablePaginator;
    this.vpnLogTableDataSource.sort = this.vpnLogTableSort;
    this.vpnLogTableDataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'ips-severity':
          return item.ipsSeverity;
        case 'sub-category':
          return item.subCategory;
        case 'application-protocol':
          return item.applicationProtocol;
        case 'ips-category':
          return item.ipsCategory;
        case 'policy-id':
          return item.policyId;
        case 'policy-name':
          return item.policyName;
        case 'ether-type':
          return item.etherType;
        case 'ip-protocol':
          return item.ipProtocol;
        case 'incoming-interface':
          return item.incomingInterface;
        case 'src-mac':
          return item.srcMac;
        case 'src-ip':
          return item.srcIp;
        case 'src-port':
          return item.srcPort;
        case 'outgoing-interface':
          return item.outgoingInterface;
        case 'dst-ip':
          return item.dstIp;
        case 'dst-port':
          return item.dstPort;
        case 'tcp-flags':
          return item.tcpFlags;
        case 'icmp-type':
          return item.icmpType;
        case 'icmp-code':
          return item.icmpCode;
        case 'additional-message':
          return item.additionalMessage;
        default:
          return item[property];
      }
    };
    this.vpnLogTableDataSource.filterPredicate = (data: any, filter: string): boolean => {
      return (
        String(data.index).toLowerCase().indexOf(filter) !== -1 ||
        String(data.timestamp).toLowerCase().indexOf(filter) !== -1 ||
        String(data.severity).toLowerCase().indexOf(filter) !== -1 ||
        String(data.ipsSeverity).toLowerCase().indexOf(filter) !== -1 ||
        String(data.subCategory).toLowerCase().indexOf(filter) !== -1 ||
        String(data.applicationProtocol).toLowerCase().indexOf(filter) !== -1 ||
        String(data.ipsCategory).toLowerCase().indexOf(filter) !== -1 ||
        String(data.policyId).toLowerCase().indexOf(filter) !== -1 ||
        String(data.policyName).toLowerCase().indexOf(filter) !== -1 ||
        String(data.ipProtocol).toLowerCase().indexOf(filter) !== -1 ||
        String(data.incomingInterface).toLowerCase().indexOf(filter) !== -1 ||
        String(data.srcMac).toLowerCase().indexOf(filter) !== -1 ||
        String(data.srcIp).toLowerCase().indexOf(filter) !== -1 ||
        String(data.srcPort).toLowerCase().indexOf(filter) !== -1 ||
        String(data.outgoingInterface).toLowerCase().indexOf(filter) !== -1 ||
        String(data.dstIp).toLowerCase().indexOf(filter) !== -1 ||
        String(data.dstPort).toLowerCase().indexOf(filter) !== -1 ||
        String(data.tcpFlags).toLowerCase().indexOf(filter) !== -1 ||
        String(data.icmpType).toLowerCase().indexOf(filter) !== -1 ||
        String(data.icmpCode).toLowerCase().indexOf(filter) !== -1 ||
        String(data.action).toLowerCase().indexOf(filter) !== -1 ||
        String(data.additionalMessage).toLowerCase().indexOf(filter) !== -1
      );
    };

    this.thresholdTableDataSource.paginator = this.thresholdTablePaginator;
    this.thresholdTableDataSource.sort = this.thresholdTableSort;
    this.thresholdTableDataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'enable':
          return item.enableDisplay;
        case 'event-name':
          return item.categoryName;
        case 'warning-threshold':
          return item.warningThreshold;
        case 'oversize-action':
          return item.oversizeAction;
        case 'registered-action':
          return item.registeredActionDisplay;
        default:
          return item[property];
      }
    };
    this.thresholdTableDataSource.filterPredicate = (data: any, filter: string): boolean => {
      return (
        String(data.index).toLowerCase().indexOf(filter) !== -1 ||
        String(data.enableDisplay).toLowerCase().indexOf(filter) !== -1 ||
        String(data.categoryName).toLowerCase().indexOf(filter) !== -1 ||
        String(data.warningThreshold).toLowerCase().indexOf(filter) !== -1 ||
        String(data.oversizeAction).toLowerCase().indexOf(filter) !== -1 ||
        String(data.registeredActionDisplay).toLowerCase().indexOf(filter) !== -1
      );
    };
  }

  trackByFn(index, item): void {
    return item.key;
  }

  onEventLogPageChange(event): void {
    this.eventLogTableRows = this.eventLogTableData.slice(
      this.eventLogTablePanelPageSize * event.pageIndex,
      this.eventLogTablePanelPageSize * event.pageIndex + this.eventLogTablePanelPageSize
    );
  }

  onFirewallLogPageChange(event): void {
    this.firewallLogTableRows = this.firewallLogTableData.slice(
      this.firewallLogTablePanelPageSize * event.pageIndex,
      this.firewallLogTablePanelPageSize * event.pageIndex + this.firewallLogTablePanelPageSize
    );
  }

  onVpnLogPageChange(event): void {
    this.vpnLogTableRows = this.vpnLogTableData.slice(
      this.vpnLogTablePanelPageSize * event.pageIndex,
      this.vpnLogTablePanelPageSize * event.pageIndex + this.vpnLogTablePanelPageSize
    );
  }

  onThresholdPageChange(event): void {
    this.thresholdTableRows = this.thresholdTableData.slice(
      this.thresholdTablePanelPageSize * event.pageIndex,
      this.thresholdTablePanelPageSize * event.pageIndex + this.thresholdTablePanelPageSize
    );
  }

  getL4PortDisplay(l4Port: number, ipProtocolStr: string): string {
    if (IpProtocolStr.TCP !== ipProtocolStr && IpProtocolStr.UDP !== ipProtocolStr) {
      return this.utils.emptyDash;
    } else {
      return String(l4Port);
    }
  }

  getIcmpDisplay(icmp: number, ipProtocolStr: string): string {
    if (IpProtocolStr.ICMP !== ipProtocolStr) {
      return this.utils.emptyDash;
    } else {
      return String(icmp);
    }
  }

  getTcpFlagDisplay(tcpFlag: string, ipProtocolStr: string): string {
    if (IpProtocolStr.TCP !== ipProtocolStr) {
      return this.utils.emptyDash;
    } else {
      return String(tcpFlag);
    }
  }

  getRegisteredAction(registeredAction): void {
    this.registeredAction = [];
    this.registeredActionDisplay = [];
    this.registeredActionList = [];
    if (registeredAction.snmp_checkbox === '1') {
      this.registeredAction.push('snmp_checkbox');
      this.registeredActionDisplay.push(this.translate.instant('general.common.snmp_trap_server'));
      this.registeredActionList.push({ value: '1', text: 'snmp_checkbox' });
    } else {
      this.registeredActionList.push({ value: '0', text: 'snmp_checkbox' });
    }

    if (registeredAction.email_checkbox === '1') {
      this.registeredAction.push('email_checkbox');
      this.registeredActionDisplay.push(this.translate.instant('general.common_account.email'));
      this.registeredActionList.push({ value: '1', text: 'email_checkbox' });
    } else {
      this.registeredActionList.push({ value: '0', text: 'email_checkbox' });
    }
  }

  editThresholdData(rowData): void {
    mediumDialogConfig.data = {
      row: rowData,
      eventLogMgmt: this.uriRequestData.eventLogMgmt,
    };
    const dialogRef = this.dialog.open(ThresholdSettingDialogComponent, mediumDialogConfig);

    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        this.refreshTabContent().subscribe({
          complete: () => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          },
          error: error => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            this.errorService.handleError(error);
          },
        });
      }
    });
  }

  updateAutoRestore(): void {
    if (this.autoRestoreForm.invalid) {
      this.autoRestoreForm.markAllAsTouched();
      return;
    }
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    this.uriRequestData.abc02.auto_log = this.autoRestoreForm.get('autoLoadEventLog').value ? '1' : '0';
    this.snackBar.open(this.translate.instant('request_handler.action_saving'));
    this.http.post(`${environment.uriRequestURL}/setting/data/?SRV=SRV_ABC02`, this.uriRequestData.abc02).subscribe(
      () => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.snackBar.open(this.translate.instant('response_handler.res_global_success'), '', {
          duration: 3000,
        });
      },
      error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      }
    );
  }

  checkEventLog(): void {
    if (sessionStorage.getItem('eventLogSeverity')) {
      const severity = sessionStorage.getItem('eventLogSeverity');
      sessionStorage.removeItem('eventLogSeverity');
      this.eventLogSelect.setValue(severity);
      this.updateTableFilter(severity, this.eventLogTableDataSource);
    }
  }

  generateDoc(showCategory: CategoryLevel, docType: 'json' | 'csv' | 'pdf'): void {
    if (docType === 'json') {
      const options = this.utils.createDownloadOptions();
      this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
      this.snackBar.open(this.translate.instant('request_handler.action_saving'));
      const postData = {
        show_category: showCategory === CategoryLevel.OB_L3_POLICY ? this.selectedCategoryValue : showCategory,
      };
      this.http.post(`${environment.uriRequestURL}/auth/net_MakeMoxaLogFile`, postData, options).subscribe(
        (response: any) => {
          this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          const blob = new Blob([response.body], { type: 'text/html' });
          if ((window.navigator as any).msSaveOrOpenBlob) {
            // IE11
            (window.navigator as any).msSaveOrOpenBlob(blob, `/event_log/${this.moxaLogFileName(showCategory)}`);
          } else {
            this.fetchService
              .getFile(`${environment.apiHost}/event_log/${this.moxaLogFileName(showCategory)}?${new Date().getTime()}`)
              .then(blob => {
                const objectURL = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.download = `${this.moxaLogFileName(showCategory)}.json`;
                link.href = objectURL;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
              });
          }
          this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          this.snackBar.open(this.translate.instant('response_handler.export_file_success'), '', {
            duration: 3000,
          });
        },
        error => {
          this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          this.errorService.handleError(error);
        }
      );
    } else {
      //# region PDF and CSV 參數設定
      const categoryConfig = {
        [CategoryLevel.SYSTEM]: {
          tableTitle: {
            index: this.translate.instant('general.common.index'),
            timestamp: this.translate.instant('features.eventlog.timestamp'),
            severity: this.translate.instant('general.common_severity.severity'),
            additionalMessage: this.translate.instant('features.eventlog.additional_message'),
          },
          tableContent: this.eventLogTableDataSource.data,
          pdfPageGroup: [['index', 'timestamp', 'severity', 'additionalMessage']],
        },
        [CategoryLevel.VPN]: {
          tableTitle: {
            index: this.translate.instant('general.common.index'),
            timestamp: this.translate.instant('features.eventlog.timestamp'),
            severity: this.translate.instant('general.common_severity.severity'),
            additionalMessage: this.translate.instant('features.eventlog.additional_message'),
          },
          tableContent: this.vpnLogTableDataSource.data,
          pdfPageGroup: [['index', 'timestamp', 'severity', 'additionalMessage']],
        },
        [CategoryLevel.TRUST_ACCESS]: {
          tableTitle: {
            index: this.translate.instant('general.common.index'),
            timestamp: this.translate.instant('features.eventlog.timestamp'),
            severity: this.translate.instant('general.common_severity.severity'),
            etherType: this.translate.instant('general.common_route.ether_type'),
            ipProtocol: this.translate.instant('general.common_route.ip_protocol'),
            incomingInterface: this.translate.instant('general.common_route.incoming_interface'),
            srcMac: this.translate.instant('general.common_route.src_mac'),
            srcIp: this.translate.instant('general.common_route.src_ip'),
            srcPort: this.translate.instant('general.common_route.src_port'),
            outgoingInterface: this.translate.instant('general.common_route.outgoing_interface'),
            dstIp: this.translate.instant('general.common_route.dst_ip'),
            dstPort: this.translate.instant('general.common_route.dst_port'),
            tcpFlags: this.translate.instant('general.common_route.tcp_flags'),
            icmpType: this.translate.instant('general.common_route.icmp_type'),
            icmpCode: this.translate.instant('general.common_route.icmp_code'),
            action: this.translate.instant('general.common.action'),
            additionalMessage: this.translate.instant('features.eventlog.additional_message'),
          },
          tableContent: this.firewallLogTableDataSource.data,
          pdfPageGroup: [
            [
              'index',
              'timestamp',
              'severity',
              'etherType',
              'ipProtocol',
              'incomingInterface',
              'srcMac',
              'srcIp',
              'srcPort',
              'outgoingInterface',
              'dstIp',
              'dstPort',
              'tcpFlags',
              'icmpType',
              'icmpCode',
              'action',
              'additionalMessage',
            ],
          ],
        },
        [CategoryLevel.PING_RESPONSE]: {
          tableTitle: {
            index: this.translate.instant('general.common.index'),
            timestamp: this.translate.instant('features.eventlog.timestamp'),
            severity: this.translate.instant('general.common_severity.severity'),
            etherType: this.translate.instant('general.common_route.ether_type'),
            ipProtocol: this.translate.instant('general.common_route.ip_protocol'),
            incomingInterface: this.translate.instant('general.common_route.incoming_interface'),
            srcMac: this.translate.instant('general.common_route.src_mac'),
            srcIp: this.translate.instant('general.common_route.src_ip'),
            srcPort: this.translate.instant('general.common_route.src_port'),
            outgoingInterface: this.translate.instant('general.common_route.outgoing_interface'),
            dstIp: this.translate.instant('general.common_route.dst_ip'),
            dstPort: this.translate.instant('general.common_route.dst_port'),
            tcpFlags: this.translate.instant('general.common_route.tcp_flags'),
            icmpType: this.translate.instant('general.common_route.icmp_type'),
            icmpCode: this.translate.instant('general.common_route.icmp_code'),
            action: this.translate.instant('general.common.action'),
            additionalMessage: this.translate.instant('features.eventlog.additional_message'),
          },
          tableContent: this.firewallLogTableDataSource.data,
          pdfPageGroup: [
            [
              'index',
              'timestamp',
              'severity',
              'etherType',
              'ipProtocol',
              'incomingInterface',
              'srcMac',
              'srcIp',
              'srcPort',
              'outgoingInterface',
              'dstIp',
              'dstPort',
              'tcpFlags',
              'icmpType',
              'icmpCode',
              'action',
              'additionalMessage',
            ],
          ],
        },
        [CategoryLevel.MALFORMED]: {
          tableTitle: {
            index: this.translate.instant('general.common.index'),
            timestamp: this.translate.instant('features.eventlog.timestamp'),
            severity: this.translate.instant('general.common_severity.severity'),
            etherType: this.translate.instant('general.common_route.ether_type'),
            ipProtocol: this.translate.instant('general.common_route.ip_protocol'),
            incomingInterface: this.translate.instant('general.common_route.incoming_interface'),
            srcMac: this.translate.instant('general.common_route.src_mac'),
            srcIp: this.translate.instant('general.common_route.src_ip'),
            srcPort: this.translate.instant('general.common_route.src_port'),
            outgoingInterface: this.translate.instant('general.common_route.outgoing_interface'),
            dstIp: this.translate.instant('general.common_route.dst_ip'),
            dstPort: this.translate.instant('general.common_route.dst_port'),
            tcpFlags: this.translate.instant('general.common_route.tcp_flags'),
            icmpType: this.translate.instant('general.common_route.icmp_type'),
            icmpCode: this.translate.instant('general.common_route.icmp_code'),
            action: this.translate.instant('general.common.action'),
            additionalMessage: this.translate.instant('features.eventlog.additional_message'),
          },
          tableContent: this.firewallLogTableDataSource.data,
          pdfPageGroup: [
            [
              'index',
              'timestamp',
              'severity',
              'etherType',
              'ipProtocol',
              'incomingInterface',
              'srcMac',
              'srcIp',
              'srcPort',
              'outgoingInterface',
              'dstIp',
              'dstPort',
              'tcpFlags',
              'icmpType',
              'icmpCode',
              'action',
              'additionalMessage',
            ],
          ],
        },
        [CategoryLevel.DOS]: {
          tableTitle: {
            index: this.translate.instant('general.common.index'),
            timestamp: this.translate.instant('features.eventlog.timestamp'),
            severity: this.translate.instant('general.common_severity.severity'),
            etherType: this.translate.instant('general.common_route.ether_type'),
            subCategory: this.translate.instant('general.common_route.sub_category'),
            ipProtocol: this.translate.instant('general.common_route.ip_protocol'),
            incomingInterface: this.translate.instant('general.common_route.incoming_interface'),
            srcMac: this.translate.instant('general.common_route.src_mac'),
            srcIp: this.translate.instant('general.common_route.src_ip'),
            srcPort: this.translate.instant('general.common_route.src_port'),
            outgoingInterface: this.translate.instant('general.common_route.outgoing_interface'),
            dstIp: this.translate.instant('general.common_route.dst_ip'),
            dstPort: this.translate.instant('general.common_route.dst_port'),
            tcpFlags: this.translate.instant('general.common_route.tcp_flags'),
            icmpType: this.translate.instant('general.common_route.icmp_type'),
            icmpCode: this.translate.instant('general.common_route.icmp_code'),
            action: this.translate.instant('general.common.action'),
            additionalMessage: this.translate.instant('features.eventlog.additional_message'),
          },
          tableContent: this.firewallLogTableDataSource.data,
          pdfPageGroup: [
            [
              'index',
              'timestamp',
              'severity',
              'etherType',
              'subCategory',
              'ipProtocol',
              'incomingInterface',
              'srcMac',
              'srcIp',
              'srcPort',
              'outgoingInterface',
              'dstIp',
              'dstPort',
              'tcpFlags',
              'icmpType',
              'icmpCode',
              'action',
              'additionalMessage',
            ],
          ],
        },
        [CategoryLevel.DEV_LOCKDOWN]: {
          tableTitle: {
            index: this.translate.instant('general.common.index'),
            timestamp: this.translate.instant('features.eventlog.timestamp'),
            severity: this.translate.instant('general.common_severity.severity'),
            etherType: this.translate.instant('general.common_route.ether_type'),
            ipProtocol: this.translate.instant('general.common_route.ip_protocol'),
            incomingInterface: this.translate.instant('general.common_route.incoming_interface'),
            srcMac: this.translate.instant('general.common_route.src_mac'),
            srcIp: this.translate.instant('general.common_route.src_ip'),
            srcPort: this.translate.instant('general.common_route.src_port'),
            outgoingInterface: this.translate.instant('general.common_route.outgoing_interface'),
            dstIp: this.translate.instant('general.common_route.dst_ip'),
            dstPort: this.translate.instant('general.common_route.dst_port'),
            tcpFlags: this.translate.instant('general.common_route.tcp_flags'),
            icmpType: this.translate.instant('general.common_route.icmp_type'),
            icmpCode: this.translate.instant('general.common_route.icmp_code'),
            action: this.translate.instant('general.common.action'),
            additionalMessage: this.translate.instant('features.eventlog.additional_message'),
          },
          tableContent: this.firewallLogTableDataSource.data,
          pdfPageGroup: [
            [
              'index',
              'timestamp',
              'severity',
              'etherType',
              'ipProtocol',
              'incomingInterface',
              'srcMac',
              'srcIp',
              'srcPort',
              'outgoingInterface',
              'dstIp',
              'dstPort',
              'tcpFlags',
              'icmpType',
              'icmpCode',
              'action',
              'additionalMessage',
            ],
          ],
        },
        [CategoryLevel.OB_L3_POLICY]: {
          tableTitle: {
            index: this.translate.instant('general.common.index'),
            timestamp: this.translate.instant('features.eventlog.timestamp'),
            severity: this.translate.instant('general.common_severity.severity'),
            policyId: this.translate.instant('general.common_route.policy_id'),
            policyName: this.translate.instant('general.common_route.policy_name'),
            etherType: this.translate.instant('general.common_route.ether_type'),
            ipProtocol: this.translate.instant('general.common_route.ip_protocol'),
            incomingInterface: this.translate.instant('general.common_route.incoming_interface'),
            srcMac: this.translate.instant('general.common_route.src_mac'),
            srcIp: this.translate.instant('general.common_route.src_ip'),
            srcPort: this.translate.instant('general.common_route.src_port'),
            outgoingInterface: this.translate.instant('general.common_route.outgoing_interface'),
            dstIp: this.translate.instant('general.common_route.dst_ip'),
            dstPort: this.translate.instant('general.common_route.dst_port'),
            tcpFlags: this.translate.instant('general.common_route.tcp_flags'),
            icmpType: this.translate.instant('general.common_route.icmp_type'),
            icmpCode: this.translate.instant('general.common_route.icmp_code'),
            action: this.translate.instant('general.common.action'),
          },
          tableContent: this.firewallLogTableDataSource.data,
          pdfPageGroup: [
            [
              'index',
              'timestamp',
              'severity',
              'policyId',
              'policyName',
              'etherType',
              'ipProtocol',
              'incomingInterface',
              'srcMac',
              'srcIp',
              'srcPort',
              'outgoingInterface',
              'dstIp',
              'dstPort',
              'tcpFlags',
              'icmpType',
              'icmpCode',
              'action',
            ],
          ],
        },
        [CategoryLevel.DPI_POLICY]: {
          tableTitle: {
            index: this.translate.instant('general.common.index'),
            timestamp: this.translate.instant('features.eventlog.timestamp'),
            severity: this.translate.instant('general.common_severity.severity'),
            applicationProtocol: this.translate.instant('general.common_route.application_protocol'),
            policyId: this.translate.instant('general.common_route.policy_id'),
            policyName: this.translate.instant('general.common_route.policy_name'),
            etherType: this.translate.instant('general.common_route.ether_type'),
            ipProtocol: this.translate.instant('general.common_route.ip_protocol'),
            incomingInterface: this.translate.instant('general.common_route.incoming_interface'),
            srcIp: this.translate.instant('general.common_route.src_ip'),
            srcPort: this.translate.instant('general.common_route.src_port'),
            outgoingInterface: this.translate.instant('general.common_route.outgoing_interface'),
            dstIp: this.translate.instant('general.common_route.dst_ip'),
            dstPort: this.translate.instant('general.common_route.dst_port'),
            action: this.translate.instant('general.common.action'),
          },
          tableContent: this.firewallLogTableDataSource.data,
          pdfPageGroup: [
            [
              'index',
              'timestamp',
              'severity',
              'applicationProtocol',
              'policyId',
              'policyName',
              'etherType',
              'ipProtocol',
              'incomingInterface',
              'srcIp',
              'srcPort',
              'outgoingInterface',
              'dstIp',
              'dstPort',
              'action',
            ],
          ],
        },
        [CategoryLevel.DP]: {
          tableTitle: {
            index: this.translate.instant('general.common.index'),
            timestamp: this.translate.instant('features.eventlog.timestamp'),
            severity: this.translate.instant('general.common_severity.severity'),
            applicationProtocol: this.translate.instant('general.common_route.application_protocol'),
            policyId: this.translate.instant('general.common_route.policy_id'),
            policyName: this.translate.instant('general.common_route.policy_name'),
            etherType: this.translate.instant('general.common_route.ether_type'),
            ipProtocol: this.translate.instant('general.common_route.ip_protocol'),
            incomingInterface: this.translate.instant('general.common_route.incoming_interface'),
            srcIp: this.translate.instant('general.common_route.src_ip'),
            srcPort: this.translate.instant('general.common_route.src_port'),
            outgoingInterface: this.translate.instant('general.common_route.outgoing_interface'),
            dstIp: this.translate.instant('general.common_route.dst_ip'),
            dstPort: this.translate.instant('general.common_route.dst_port'),
            action: this.translate.instant('general.common.action'),
          },
          tableContent: this.firewallLogTableDataSource.data,
          pdfPageGroup: [
            [
              'index',
              'timestamp',
              'severity',
              'applicationProtocol',
              'policyId',
              'policyName',
              'etherType',
              'ipProtocol',
              'incomingInterface',
              'srcIp',
              'srcPort',
              'outgoingInterface',
              'dstIp',
              'dstPort',
              'action',
            ],
          ],
        },
        [CategoryLevel.ADP]: {
          tableTitle: {
            index: this.translate.instant('general.common.index'),
            timestamp: this.translate.instant('features.eventlog.timestamp'),
            applicationProtocol: this.translate.instant('general.common_route.application_protocol'),
            policyId: this.translate.instant('general.common_route.policy_id'),
            policyName: this.translate.instant('general.common_route.policy_name'),
            etherType: this.translate.instant('general.common_route.ether_type'),
            ipProtocol: this.translate.instant('general.common_route.ip_protocol'),
            incomingInterface: this.translate.instant('general.common_route.incoming_interface'),
            srcIp: this.translate.instant('general.common_route.src_ip'),
            srcPort: this.translate.instant('general.common_route.src_port'),
            outgoingInterface: this.translate.instant('general.common_route.outgoing_interface'),
            dstIp: this.translate.instant('general.common_route.dst_ip'),
            dstPort: this.translate.instant('general.common_route.dst_port'),
            action: this.translate.instant('general.common.action'),
          },
          tableContent: this.firewallLogTableDataSource.data,
          pdfPageGroup: [
            [
              'index',
              'timestamp',
              'severity',
              'applicationProtocol',
              'policyId',
              'policyName',
              'etherType',
              'ipProtocol',
              'incomingInterface',
              'srcIp',
              'srcPort',
              'outgoingInterface',
              'dstIp',
              'dstPort',
              'action',
            ],
          ],
        },
        [CategoryLevel.IPS]: {
          tableTitle: {
            index: this.translate.instant('general.common.index'),
            timestamp: this.translate.instant('features.eventlog.timestamp'),
            ipsSeverity: this.translate.instant('general.common_severity.severity'),
            ipsCategory: this.translate.instant('general.common_route.ips_category'),
            policyId: this.translate.instant('general.common_route.policy_id'),
            policyName: this.translate.instant('general.common_route.policy_name'),
            etherType: this.translate.instant('general.common_route.ether_type'),
            ipProtocol: this.translate.instant('general.common_route.ip_protocol'),
            incomingInterface: this.translate.instant('general.common_route.incoming_interface'),
            srcMac: this.translate.instant('general.common_route.src_mac'),
            srcIp: this.translate.instant('general.common_route.src_ip'),
            srcPort: this.translate.instant('general.common_route.src_port'),
            outgoingInterface: this.translate.instant('general.common_route.outgoing_interface'),
            dstIp: this.translate.instant('general.common_route.dst_ip'),
            dstPort: this.translate.instant('general.common_route.dst_port'),
            tcpFlags: this.translate.instant('general.common_route.tcp_flags'),
            action: this.translate.instant('general.common.action'),
          },
          tableContent: this.firewallLogTableDataSource.data,
          pdfPageGroup: [
            [
              'index',
              'timestamp',
              'ipsSeverity',
              'ipsCategory',
              'policyId',
              'policyName',
              'etherType',
              'ipProtocol',
              'incomingInterface',
              'srcMac',
              'srcIp',
              'srcPort',
              'outgoingInterface',
              'dstIp',
              'dstPort',
              'tcpFlags',
              'action',
            ],
          ],
        },
        [CategoryLevel.SESSION_CONTROL]: {
          tableTitle: {
            index: this.translate.instant('general.common.index'),
            timestamp: this.translate.instant('features.eventlog.timestamp'),
            severity: this.translate.instant('general.common_severity.severity'),
            policyId: this.translate.instant('general.common_route.policy_id'),
            policyName: this.translate.instant('general.common_route.policy_name'),
            etherType: this.translate.instant('general.common_route.ether_type'),
            ipProtocol: this.translate.instant('general.common_route.ip_protocol'),
            incomingInterface: this.translate.instant('general.common_route.incoming_interface'),
            srcMac: this.translate.instant('general.common_route.src_mac'),
            srcIp: this.translate.instant('general.common_route.src_ip'),
            srcPort: this.translate.instant('general.common_route.src_port'),
            outgoingInterface: this.translate.instant('general.common_route.outgoing_interface'),
            dstIp: this.translate.instant('general.common_route.dst_ip'),
            dstPort: this.translate.instant('general.common_route.dst_port'),
            tcpFlags: this.translate.instant('general.common_route.tcp_flags'),
            icmpType: this.translate.instant('general.common_route.icmp_type'),
            icmpCode: this.translate.instant('general.common_route.icmp_code'),
            action: this.translate.instant('general.common.action'),
          },
          tableContent: this.firewallLogTableDataSource.data,
          pdfPageGroup: [
            [
              'index',
              'timestamp',
              'severity',
              'policyId',
              'policyName',
              'etherType',
              'ipProtocol',
              'incomingInterface',
              'srcMac',
              'srcIp',
              'srcPort',
              'outgoingInterface',
              'dstIp',
              'dstPort',
              'tcpFlags',
              'icmpType',
              'icmpCode',
              'action',
            ],
          ],
        },
        [CategoryLevel.OB_L2_FILTER]: {
          tableTitle: {
            index: this.translate.instant('general.common.index'),
            timestamp: this.translate.instant('features.eventlog.timestamp'),
            severity: this.translate.instant('general.common_severity.severity'),
            etherType: this.translate.instant('general.common_route.ether_type'),
            srcMac: this.translate.instant('general.common_route.src_mac'),
            dstMac: this.translate.instant('general.common_route.dst_mac'),
            action: this.translate.instant('general.common.action'),
          },
          tableContent: this.firewallLogTableDataSource.data,
          pdfPageGroup: [['index', 'timestamp', 'severity', 'etherType', 'srcMac', 'dstMac', 'action']],
        },
        [CategoryLevel.OB_AR]: {
          tableTitle: {
            index: this.translate.instant('general.common.index'),
            timestamp: this.translate.instant('features.eventlog.timestamp'),
            severity: this.translate.instant('general.common_severity.severity'),
            assetIpAddress: this.translate.instant('features.eventlog.asset_ip_address'),
            assetMacAddress: this.translate.instant('features.eventlog.asset_mac_address'),
            additionalMessage: this.translate.instant('features.eventlog.additional_message'),
          },
          tableContent: this.assetRecognitionTableDataSource.data,
          pdfPageGroup: [['index', 'timestamp', 'severity', 'assetIpAddress', 'assetMacAddress', 'additionalMessage']],
        },
      };
      // #endregion

      this.docService.generateDoc(docType, this.utils.getFilename('Event-Log-Table'), categoryConfig[showCategory]);
    }
  }

  checkFirewallLog(): void {
    const janusLogCategory = sessionStorage.getItem('janus_log_category');
    const janusLogSeverity = sessionStorage.getItem('janus_log_severity');
    if (janusLogCategory || janusLogSeverity) {
      this.tabIndex = 1;
      if (janusLogCategory !== undefined) {
        this.selectCategory(Number(janusLogCategory));
        sessionStorage.removeItem('janus_log_category');
      }

      if (janusLogSeverity !== undefined) {
        this.firewallLogSelect.setValue(janusLogSeverity);
        this.updateTableFilter(janusLogSeverity, this.firewallLogTableDataSource);
      }
      sessionStorage.removeItem('janus_log_severity');
    } else {
      this.selectCategory(this.selectedCategoryValue);
    }
  }
}
