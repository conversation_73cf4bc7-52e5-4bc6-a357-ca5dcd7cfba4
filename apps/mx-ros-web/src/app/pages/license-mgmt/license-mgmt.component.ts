import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ViewChild } from '@angular/core';
import { DatePipe } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { MatDialog } from '@angular/material/dialog';
import { MatLegacyPaginator as MatPaginator } from '@angular/material/legacy-paginator';
import { MatLegacyTableDataSource as MatTableDataSource } from '@angular/material/legacy-table';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatSort } from '@angular/material/sort';

import { GlobalEvent, GlobalEventType } from '@mx-ros-web/models/global.event';
import { TranslateService } from '@ngx-translate/core';
import { forEach } from 'lodash-es';
import { Observable, forkJoin } from 'rxjs';

import * as environment from '@mx-ros-web/environments/environment';
import { AppState } from '@mx-ros-web/shared/Service/app.service';
import { ErrorService } from '@mx-ros-web/shared/Service/error.service';
import { UtilsService } from '@mx-ros-web/shared/Service/utils.service';
import { largeDialogConfig } from '@mx-ros-web/shared/dialog-config.service';

import { AddLicenseDialogComponent } from './add-license/add-license-dialog';
import { LicenseStatus, LicenseType, Toggle } from './license-mgmt.def';

type OverviewDataType = {
  name: string;
  type: string;
  validDuration: string;
  startTime: string;
  endTime: string;
  status: LicenseStatus | string;
};

@Component({
  templateUrl: './license-mgmt.component.html',
  styleUrls: ['./license-mgmt.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LicenseMgmtComponent implements AfterViewInit {
  readonly LicenseStatus = LicenseStatus;

  @ViewChild('historyTableRowsSort') historyTableRowsSort: MatSort;
  @ViewChild('historyTablePaginator') historyTablePaginator: MatPaginator;
  historyTableDisplayedColumns: string[] = [
    'update-time',
    'activation-code',
    'category',
    'type',
    'license-duration',
    'dummy'
  ];
  historyTableDataSource: MatTableDataSource<any> = new MatTableDataSource();

  overviewData: {
    IPS: OverviewDataType;
  } = { IPS: null };

  private readonly emptyOv = {
    name: this.utils.emptyDash,
    type: this.utils.emptyDash,
    validDuration: this.utils.emptyDash,
    startTime: this.utils.emptyDash,
    endTime: this.utils.emptyDash,
    status: this.utils.emptyDash,
    dailyConsumption: this.utils.emptyDash,
    remainingPoints: this.utils.emptyDash
  };

  constructor(
    private appState: AppState,
    private datePipe: DatePipe,
    private http: HttpClient,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private errorService: ErrorService,
    private utils: UtilsService,
    private translate: TranslateService,
    private cdr: ChangeDetectorRef
  ) {
    forEach(this.overviewData, (_, type) => {
      this.overviewData[type] = this.emptyOv;
    });
  }

  ngAfterViewInit(): void {
    this.setupTableSetting();
    this.refreshTableData().subscribe({
      complete: () => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
      },
      error: error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      }
    });
  }

  refreshTableData(): Observable<any> {
    return new Observable(observer => {
      forkJoin({
        licenseRecord: this.http.get(`${environment.uriRequestURL}/status/licenseRecord`),
        licenseOverview: this.http.get(`${environment.uriRequestURL}/status/licenseOverview`)
      }).subscribe(
        data => {
          const historyTableData = [];
          forEach(data.licenseRecord, (licenseList, type) => {
            // overview
            const overviewData = data.licenseOverview[type];
            if (overviewData && Object.keys(overviewData).length !== 0) {
              this.overviewData[type] = {
                name: overviewData.CATEGORY,
                validDuration: overviewData.VALIDDUR,
                remainingPoints: overviewData.REMAININGPOINTS,
                dailyConsumption: overviewData.DAILYCONSUMPTION,
                status: this.getLicenseStatus(+overviewData.STATUS),
                startTime: this.datePipe.transform(new Date(+overviewData.SDATE * 1000), this.utils.dateTimeFormat),
                endTime: this.datePipe.transform(new Date(+overviewData.EDATE * 1000), this.utils.dateTimeFormat),
                type: this.getLicenseType(+overviewData.LICTYPE)
              };
            } else {
              this.overviewData[type] = this.emptyOv;
            }

            // history
            forEach(licenseList, (row: any) => {
              historyTableData.push({
                updateTime: row.UPDATEDATE,
                actCode: +row.TOGGLE === Toggle.ENABLED ? row.ACT_CODE : row.NAME_ACT_CODE,
                duration: row.LICDUR,
                type: this.getLicenseType(+row.LICTYPE),
                category: row.CATEGORY,
                totalPoints: row?.TOTALPOINTS
              });
            });
          });

          this.historyTableDataSource.data = historyTableData;
          this.cdr.markForCheck();
          observer.complete();
        },
        error => {
          observer.error(error);
        }
      );
    });
  }

  setupTableSetting(): void {
    // settings for License History Table
    this.historyTableDataSource.paginator = this.historyTablePaginator;
    this.historyTableDataSource.sort = this.historyTableRowsSort;
    this.historyTableDataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'update-time':
          return item.updateTime;
        case 'activation-code':
          return item.actCode;
        case 'license-duration':
          return item.totalPoints;
        default:
          return item[property];
      }
    };
    this.historyTableDataSource.filterPredicate = (data: any, filter: string): boolean => {
      return (
        String(data.updateTime).toLowerCase().indexOf(filter) !== -1 ||
        String(data.actCode).toLowerCase().indexOf(filter) !== -1 ||
        String(data.duration).toLowerCase().indexOf(filter) !== -1 ||
        String(data.type).toLowerCase().indexOf(filter) !== -1 ||
        String(data.category).toLowerCase().indexOf(filter) !== -1 ||
        String(data.totalPoints).toLowerCase().indexOf(filter) !== -1
      );
    };
  }

  updateTableFilter(filterValue: string): void {
    this.historyTableDataSource.filter = filterValue.trim().toLowerCase();
  }

  addLicense(): void {
    const dialogRef = this.dialog.open(AddLicenseDialogComponent, largeDialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        this.refreshTableData().subscribe({
          complete: () => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          },
          error: error => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            this.errorService.handleError(error);
          }
        });
      }
    });
  }

  refresh(): void {
    this.refreshTableData().subscribe({
      complete: () => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.snackBar.open(this.translate.instant('response_handler.res_complete_refresh'), '', {
          duration: 3000
        });
      },
      error: error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      }
    });
  }

  private getLicenseType(type: LicenseType): string {
    switch (type) {
      case LicenseType.STANDARD:
        return this.translate.instant('features.license_management.standard');
      case LicenseType.RENEW:
        return this.translate.instant('features.license_management.renew');
      case LicenseType.CV:
        return this.translate.instant('features.license_management.cv');
      case LicenseType.TRIAL:
        return this.translate.instant('features.license_management.trial');
      default:
        return this.utils.emptyDash;
    }
  }

  private getLicenseStatus(status: LicenseStatus): string {
    switch (status) {
      case LicenseStatus.DEPLETED:
        return this.translate.instant('features.license_management.depleted');
      case LicenseStatus.VALID:
        return this.translate.instant('features.license_management.valid');
    }
  }
}
