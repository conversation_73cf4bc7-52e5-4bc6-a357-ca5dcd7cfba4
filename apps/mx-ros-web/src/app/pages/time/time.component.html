<div class="app-content-container">
  <h2 class="page-title">{{ 'features.time.page_title' | translate }}</h2>
  <div class="page-function">
    <mat-tab-group>
      <!-- System Time tab -->
      <mat-tab label="{{ 'general.common.time' | translate }}">
        <mat-card>
          <div class="card-button-top-right">
            <button mat-icon-button (click)="refresh()">
              <mat-icon matTooltip="{{ 'general.tooltip.refresh' | translate }}">refresh</mat-icon>
            </button>
          </div>
          <form [formGroup]="timeForm" (ngSubmit)="updateTimeSetting()">
            <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
              <mat-form-field class="form-field-lg">
                <input
                  id="input-current-time"
                  matInput
                  placeholder="{{ 'features.time.current_time' | translate }}"
                  formControlName="currentTime"
                />
              </mat-form-field>
            </div>
            <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
              <mat-form-field>
                <mat-select
                  id="select-clock-source"
                  placeholder="{{ 'features.time.clock_source' | translate }}"
                  formControlName="clockSource"
                >
                  <mat-option id="option-clock-source-local" [value]="ClockSourceType.LOCAL">
                    {{ 'general.common.local' | translate }}
                  </mat-option>
                  <mat-option
                    id="option-clock-source-sntp"
                    [value]="ClockSourceType.SNTP"
                    [disabled]="sntpOptionDisable"
                    matTooltip="{{ 'features.time.disable_sntp' | translate }}"
                    [matTooltipDisabled]="!sntpOptionDisable"
                    [matTooltipPosition]="'right'"
                  >
                    {{ 'features.time.sntp' | translate }}
                  </mat-option>
                  <mat-option id="option-clock-source-ntp" [value]="ClockSourceType.NTP">
                    {{ 'features.time.ntp' | translate }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
              <ng-container *ngIf="timeForm.value.clockSource !== ClockSourceType.LOCAL">
                <mat-form-field>
                  <mat-select
                    placeholder="{{ 'features.time.clock_source_fallback_mode' | translate }}"
                    formControlName="fallback"
                  >
                    <mat-option *ngFor="let item of enableOption" [value]="item.value">
                      {{ item.text | translate }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
                <mat-icon
                  class="form-help-tip"
                  fxHide.xs="true"
                  [matTooltip]="'features.time.clock_source_fallback_mode_desc' | translate"
                  matTooltipPosition="right"
                >
                  info</mat-icon
                >
              </ng-container>
            </div>
            <!-- for local clock source -->
            <ng-container *ngIf="timeForm.value.clockSource === ClockSourceType.LOCAL">
              <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
                <mat-form-field class="form-field-sm">
                  <input
                    id="input-date"
                    matInput
                    [matDatepicker]="Date"
                    placeholder="{{ 'features.time.date' | translate }}"
                    formControlName="date"
                    required
                  />
                  <mat-datepicker-toggle matSuffix [for]="Date"></mat-datepicker-toggle>
                  <mat-datepicker #Date></mat-datepicker>
                </mat-form-field>
              </div>
              <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
                <mat-form-field>
                  <input
                    id="input-time"
                    matInput
                    type="time"
                    placeholder="{{ 'general.common.time' | translate }}"
                    formControlName="time"
                  />
                </mat-form-field>
              </div>
            </ng-container>
            <!-- for NTP as clock source-->
            <ng-container *ngIf="timeForm.value.clockSource === ClockSourceType.NTP">
              <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
                <mat-form-field>
                  <input
                    id="input-first-time-server"
                    matInput
                    type="text"
                    maxlength="39"
                    placeholder="{{ 'features.time.first_time_server' | translate }}"
                    formControlName="firstTimeServer"
                    #firstTimeServer
                  />
                  <mat-hint align="end">{{ firstTimeServer.value.length }} / 39</mat-hint>
                </mat-form-field>
                <mat-form-field>
                  <mat-select
                    id="select-first-ntp-time-server-key-string"
                    placeholder="{{ 'features.time.authentication' | translate }}"
                    formControlName="firstNTPTimeServerKeyString"
                  >
                    <mat-option id="option-first-ntp-time-server-key-string-disable" [value]="false">
                      {{ 'general.common.disable' | translate }}
                    </mat-option>
                    <mat-option
                      id="option-first-ntp-time-server-key-string-{{ key.keyId }}"
                      [value]="key.keyId"
                      *ngFor="let key of ntpAuthKeyTableData"
                    >
                      {{ key.keyId }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
              <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
                <mat-form-field>
                  <input
                    id="input-second-time-server"
                    matInput
                    type="text"
                    maxlength="39"
                    placeholder="{{ 'features.time.second_time_server' | translate }}"
                    formControlName="secondTimeServer"
                    #secondTimeServer
                  />
                  <mat-hint align="end">{{ secondTimeServer.value.length }} / 39</mat-hint>
                </mat-form-field>
                <mat-form-field>
                  <mat-select
                    id="select-secord-ntp-time-server-key-string"
                    placeholder="{{ 'features.time.authentication' | translate }}"
                    formControlName="secondNTPTimeServerKeyString"
                  >
                    <mat-option id="option-secord-ntp-time-server-key-string-disable" [value]="false">
                      {{ 'general.common.disable' | translate }}
                    </mat-option>
                    <mat-option
                      id="option-secord-ntp-time-server-key-string-{{ key.keyId }}"
                      [value]="key.keyId"
                      *ngFor="let key of ntpAuthKeyTableData"
                    >
                      {{ key.keyId }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </ng-container>
            <!-- for SNTP as clock source-->
            <ng-container *ngIf="timeForm.value.clockSource === ClockSourceType.SNTP">
              <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
                <mat-form-field>
                  <input
                    id="input-first-time-server"
                    matInput
                    type="text"
                    maxlength="39"
                    placeholder="{{ 'features.time.first_time_server' | translate }}"
                    formControlName="firstTimeServer"
                    #firstTimeServer
                  />
                  <mat-hint align="end">{{ firstTimeServer.value.length }} / 39</mat-hint>
                </mat-form-field>
              </div>
              <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
                <mat-form-field>
                  <input
                    id="input-second-time-server"
                    matInput
                    type="text"
                    maxlength="39"
                    placeholder="{{ 'features.time.second_time_server' | translate }}"
                    formControlName="secondTimeServer"
                    #secondTimeServer
                  />
                  <mat-hint align="end">{{ secondTimeServer.value.length }} / 39</mat-hint>
                </mat-form-field>
              </div>
            </ng-container>
            <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
              <button id="button-apply" mat-raised-button color="primary" type="submit" [disabled]="noPermission">
                {{ 'general.button.apply' | translate }}
              </button>
              <button
                id="button-sync-from-browser"
                mat-raised-button
                color="accent"
                [ngClass]="{ 'not-editable': noPermission }"
                type="button"
                (click)="syncBrowserTime()"
                *ngIf="timeForm.value.clockSource === ClockSourceType.LOCAL"
                [disabled]="noPermission"
              >
                {{ 'general.button.sync_from_browser' | translate }}
              </button>
            </div>
          </form>
        </mat-card>
      </mat-tab>
      <mat-tab label="{{ 'features.time.time_zone' | translate }}">
        <mat-card>
          <form [formGroup]="timeZoneForm" (ngSubmit)="updateTimeZoneSetting()">
            <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
              <mat-form-field class="form-field-lg">
                <mat-select
                  id="select-time-zone"
                  placeholder="{{ 'features.time.time_zone' | translate }}"
                  formControlName="timeZone"
                >
                  <mat-option
                    id="option-time-zone-{{ timeZone }}"
                    [value]="timeZone.id"
                    *ngFor="let timeZone of utcSets"
                  >
                    ({{ timeZone.value }}){{ timeZone.city }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div class="form-group-title-level-1">{{ 'features.time.daylight_saving' | translate }}</div>
            <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
              <mat-form-field>
                <mat-select
                  id="select-daylight-saving"
                  placeholder="{{ 'features.time.daylight_saving_status' | translate }}"
                  formControlName="daylightSaving"
                  (selectionChange)="onDstSelectChange($event.value)"
                >
                  <mat-option id="option-daylight-saving-enable" [value]="true">
                    {{ 'general.common.enable' | translate }}
                  </mat-option>
                  <mat-option id="option-daylight-saving-disable" [value]="false">
                    {{ 'general.common.disable' | translate }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div *ngIf="timeZoneForm.get('daylightSaving').value" formGroupName="daylightSavingSettings">
              <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
                <mat-form-field>
                  <mat-select
                    id="select-time-zone"
                    placeholder="{{ 'features.time.offset' | translate }}"
                    formControlName="offset"
                  >
                    <mat-option
                      id="option-time-zone-{{ offset.text }}"
                      [value]="offset.value"
                      *ngFor="let offset of offsetHrSel"
                    >
                      {{ offset.text }}
                    </mat-option>
                  </mat-select>
                  <mat-hint align="end">{{ 'general.unit.hour' | translate }}</mat-hint>
                </mat-form-field>
              </div>
              <div class="form-group-title-level-2">{{ 'general.common.start' | translate }}</div>
              <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
                <mat-form-field>
                  <mat-select
                    id="select-time-zone"
                    placeholder="{{ 'features.time.month' | translate }}"
                    formControlName="startMonth"
                  >
                    <mat-option
                      id="option-time-zone-{{ startMonth.text }}"
                      [value]="startMonth.value"
                      *ngFor="let startMonth of monthSel"
                    >
                      {{ startMonth.text }}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="timeZoneForm.get('daylightSavingSettings.startMonth').errors?.required">
                    {{ 'validators.required' | translate }}</mat-error
                  >
                </mat-form-field>
                <mat-form-field class="form-field-xs">
                  <mat-select
                    id="select-time-zone"
                    placeholder="{{ 'features.time.week' | translate }}"
                    formControlName="startWeek"
                  >
                    <mat-option
                      id="option-time-zone-{{ startWeek.text }}"
                      [value]="startWeek.value"
                      *ngFor="let startWeek of weekSel"
                    >
                      {{ startWeek.text }}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="timeZoneForm.get('daylightSavingSettings.startWeek').errors?.required">
                    {{ 'validators.required' | translate }}</mat-error
                  >
                </mat-form-field>
                <mat-form-field class="form-field-xs">
                  <mat-select
                    id="select-time-zone"
                    placeholder="{{ 'features.time.day' | translate }}"
                    formControlName="startDay"
                  >
                    <mat-option
                      id="option-time-zone-{{ startDay.text }}"
                      [value]="startDay.value"
                      *ngFor="let startDay of daySel"
                    >
                      {{ startDay.text }}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="timeZoneForm.get('daylightSavingSettings.startDay').errors?.required">
                    {{ 'validators.required' | translate }}</mat-error
                  >
                </mat-form-field>
                <mat-form-field class="form-field-xs">
                  <mat-select
                    id="select-time-zone"
                    placeholder="{{ 'features.time.hour' | translate }}"
                    formControlName="startHour"
                  >
                    <mat-option id="option-time-zone-{{ hour }}" [value]="hour" *ngFor="let hour of hourSel">
                      {{ pad(hour) }}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="timeZoneForm.get('daylightSavingSettings.startHour').errors?.required">
                    {{ 'validators.required' | translate }}</mat-error
                  >
                </mat-form-field>
                <mat-form-field class="form-field-sm">
                  <mat-select
                    id="select-time-zone"
                    placeholder="{{ 'features.time.minutes' | translate }}"
                    formControlName="startMinutes"
                  >
                    <mat-option id="option-time-zone-{{ min }}" [value]="min" *ngFor="let min of minutesSel">
                      {{ pad(min) }}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="timeZoneForm.get('daylightSavingSettings.startMinutes').errors?.required">
                    {{ 'validators.required' | translate }}</mat-error
                  >
                </mat-form-field>
              </div>
              <div class="form-group-title-level-2">{{ 'general.common.end' | translate }}</div>
              <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
                <mat-form-field>
                  <mat-select
                    id="select-time-zone"
                    placeholder="{{ 'features.time.month' | translate }}"
                    formControlName="endMonth"
                  >
                    <mat-option
                      id="option-time-zone-{{ endMonth.text }}"
                      [value]="endMonth.value"
                      *ngFor="let endMonth of monthSel"
                    >
                      {{ endMonth.text }}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="timeZoneForm.get('daylightSavingSettings.endMonth').errors?.required">
                    {{ 'validators.required' | translate }}</mat-error
                  >
                </mat-form-field>
                <mat-form-field class="form-field-xs">
                  <mat-select
                    id="select-time-zone"
                    placeholder="{{ 'features.time.week' | translate }}"
                    formControlName="endWeek"
                  >
                    <mat-option
                      id="option-time-zone-{{ endWeek.text }}"
                      [value]="endWeek.value"
                      *ngFor="let endWeek of weekSel"
                    >
                      {{ endWeek.text }}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="timeZoneForm.get('daylightSavingSettings.endWeek').errors?.required">
                    {{ 'validators.required' | translate }}
                  </mat-error>
                </mat-form-field>
                <mat-form-field class="form-field-xs">
                  <mat-select
                    id="select-time-zone"
                    placeholder="{{ 'features.time.day' | translate }}"
                    formControlName="endDay"
                  >
                    <mat-option
                      id="option-time-zone-{{ endDay.text }}"
                      [value]="endDay.value"
                      *ngFor="let endDay of daySel"
                    >
                      {{ endDay.text }}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="timeZoneForm.get('daylightSavingSettings.endDay').errors?.required">
                    {{ 'validators.required' | translate }}
                  </mat-error>
                </mat-form-field>
                <mat-form-field class="form-field-xs">
                  <mat-select
                    id="select-time-zone"
                    placeholder="{{ 'features.time.hour' | translate }}"
                    formControlName="endHour"
                  >
                    <mat-option id="option-time-zone-{{ hour }}" [value]="hour" *ngFor="let hour of hourSel">
                      {{ pad(hour) }}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="timeZoneForm.get('daylightSavingSettings.endHour').errors?.required">
                    {{ 'validators.required' | translate }}
                  </mat-error>
                </mat-form-field>
                <mat-form-field class="form-field-sm">
                  <mat-select
                    id="select-time-zone"
                    placeholder="{{ 'features.time.minutes' | translate }}"
                    formControlName="endMinutes"
                  >
                    <mat-option id="option-time-zone-{{ min }}" [value]="min" *ngFor="let min of minutesSel">
                      {{ pad(min) }}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="timeZoneForm.get('daylightSavingSettings.endMinutes').errors?.required">
                    {{ 'validators.required' | translate }}</mat-error
                  >
                </mat-form-field>
              </div>
            </div>
            <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
              <button
                id="button-apply"
                mat-raised-button
                color="primary"
                type="submit"
                [ngClass]="{ 'not-editable': noPermission }"
                [disabled]="noPermission"
              >
                {{ 'general.button.apply' | translate }}
              </button>
            </div>
          </form>
        </mat-card>
      </mat-tab>
      <!-- NTP authentication keys -->
      <mat-tab label="{{ 'features.time.ntp_auth_tab_title' | translate }}">
        <mat-card>
          <div fxLayout="row" fxLayoutAlign="space-between center">
            <!-- add/delete button -->
            <div class="table-icon-tool-bar-div" fxFlex="70" fxFlex.lt-lg="50">
              <span
                *ngIf="ntpAuthKeySelection.selected.length === 0"
                matTooltip="{{ 'features.time.key_size_limitation' | translate : { size: 20 } }}"
                [matTooltipDisabled]="ntpAuthKeyTableDataLength < 20"
              >
                <button
                  id="button-add"
                  mat-icon-button
                  matTooltip="{{ 'general.tooltip.add' | translate }}"
                  [matTooltipDisabled]="ntpAuthKeyTableDataLength >= 20"
                  (click)="createNtpAuthKey()"
                  [disabled]="ntpAuthKeyTableDataLength >= 20 || noPermission"
                  [ngClass]="{ 'not-editable': ntpAuthKeyTableDataLength >= 20 || noPermission }"
                >
                  <mat-icon>add_box</mat-icon>
                </button>
              </span>
              <button
                id="button-delete"
                *ngIf="ntpAuthKeySelection.selected.length >= 1"
                mat-icon-button
                matTooltip="{{ 'general.tooltip.delete' | translate }}"
                (click)="deleteNtpAuthKey()"
                [ngClass]="{ 'not-editable': noPermission }"
                [disabled]="noPermission"
              >
                <mat-icon>delete</mat-icon>
              </button>
            </div>
            <!-- Search bar -->
            <div class="filter-input-block" fxFlex="30" fxFlex.lt-lg="50">
              <mat-form-field class="form-field-search">
                <mat-icon class="icon-search" matPrefix>search</mat-icon>
                <input
                  matInput
                  placeholder="{{ 'general.table_function.filter_desc' | translate }}"
                  (keyup)="updateNtpAuthKeyFilter($event.target.value)"
                />
              </mat-form-field>
            </div>
          </div>
          <!-- Per key config table -->
          <div id="dataContent">
            <div fxShow.xs="true" fxHide="true">
              <!-- It's RWD to fulfill different web size. -->
              <mat-expansion-panel
                class="expansion-panel-wrapper"
                *ngFor="let row of ntpAuthKeyTableRows; trackBy: trackByFn"
              >
                <mat-expansion-panel-header>
                  <mat-panel-title>{{ 'features.time.key_id' | translate }}: {{ row.keyId }}</mat-panel-title>
                </mat-expansion-panel-header>
                <p>{{ 'features.time.key_id' | translate }}: {{ row.keyId }}</p>
                <p>{{ 'general.common.type' | translate }}: {{ row.keyType }}</p>
                <p>{{ 'features.time.key_string' | translate }}: {{ row.keyString }}</p>
                <!-- edit/delete buttons for RWD -->
                <mat-action-row>
                  <button
                    color="primary"
                    mat-raised-button
                    (click)="editNtpAuthKey(row)"
                    [ngClass]="{ 'not-editable': noPermission }"
                    [disabled]="noPermission"
                  >
                    {{ 'general.button.edit' | translate }}
                  </button>
                  <button
                    color="warn"
                    mat-raised-button
                    (click)="deleteNtpAuthKey(row)"
                    [ngClass]="{ 'not-editable': noPermission }"
                    [disabled]="noPermission"
                  >
                    {{ 'general.button.delete' | translate }}
                  </button>
                </mat-action-row>
              </mat-expansion-panel>
              <mat-paginator
                [length]="ntpAuthKeyTableDataLength"
                [pageSize]="ntpAuthKeyTablePanelPageSize"
                [hidePageSize]="true"
                (page)="onNtpAuthKeyTablePageChange($event)"
              >
              </mat-paginator>
            </div>
            <div fxShow="true" fxHide.xs="true">
              <div class="app-table-wrapper">
                <!-- Per column setting of data content table -->
                <table mat-table #ntpAuthKeyTableSort="matSort" matSort [dataSource]="ntpAuthKeyTableDataSource">
                  <!-- Select column -->
                  <ng-container matColumnDef="select">
                    <th mat-header-cell *matHeaderCellDef>
                      <mat-checkbox
                        color="primary"
                        (change)="$event ? ntpAuthKeyMasterToggle() : null"
                        [checked]="ntpAuthKeySelection.hasValue() && isAllNtpAuthKeySelected()"
                        [indeterminate]="ntpAuthKeySelection.hasValue() && !isAllNtpAuthKeySelected()"
                        [disabled]="ntpAuthKeyTableDataLength === 0"
                      >
                      </mat-checkbox>
                    </th>
                    <td mat-cell *matCellDef="let row">
                      <mat-checkbox
                        id="checkbox-ntp-auth-key-{{ row.key }}"
                        color="primary"
                        (click)="$event.stopPropagation()"
                        (change)="$event ? ntpAuthKeySelection.toggle(row) : null"
                        [checked]="ntpAuthKeySelection.isSelected(row)"
                      >
                      </mat-checkbox>
                    </td>
                  </ng-container>
                  <!-- Edit column -->
                  <ng-container matColumnDef="edit">
                    <th mat-header-cell *matHeaderCellDef></th>
                    <td mat-cell *matCellDef="let row">
                      <mat-icon
                        class="table-icon-action"
                        *ngIf="!noPermission"
                        matTooltip="{{ 'general.tooltip.edit' | translate }}"
                        attr.id="button-edit-ntp-auth-key-{{ row.key }}"
                        (click)="editNtpAuthKey(row)"
                        >edit
                      </mat-icon>
                      <mat-icon class="table-icon-action table-not-editable" *ngIf="noPermission">edit</mat-icon>
                    </td>
                  </ng-container>
                  <!-- key-id column -->
                  <ng-container matColumnDef="key-id">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'features.time.key_id' | translate }}</th>
                    <td mat-cell *matCellDef="let row">{{ row.keyId }}</td>
                  </ng-container>
                  <!-- key-type column -->
                  <ng-container matColumnDef="key-type">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'general.common.type' | translate }}</th>
                    <td mat-cell *matCellDef="let row">{{ row.keyType }}</td>
                  </ng-container>
                  <!-- rule start date column -->
                  <ng-container matColumnDef="key-string">
                    <th mat-header-cell *matHeaderCellDef>{{ 'features.time.key_string' | translate }}</th>
                    <td mat-cell *matCellDef="let row">{{ row.keyString }}</td>
                  </ng-container>
                  <!-- dummy column -->
                  <ng-container matColumnDef="dummy">
                    <th mat-header-cell *matHeaderCellDef></th>
                    <td mat-cell *matCellDef="let row"></td>
                  </ng-container>
                  <tr mat-header-row *matHeaderRowDef="ntpAuthKeyTableDisplayedColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: ntpAuthKeyTableDisplayedColumns"></tr>
                </table>
              </div>
              <mat-paginator
                #ntpAuthKeyTablePaginator
                [ngClass]="{ disableChangePage: ntpAuthKeyTableDataLength <= 50 }"
                [hidePageSize]="true"
                [showFirstLastButtons]="false"
              ></mat-paginator>
              <div class="table-max-count">{{ 'general.table_function.limit_count' | translate }} 20</div>
            </div>
          </div>
        </mat-card>
      </mat-tab>
    </mat-tab-group>
  </div>
</div>
