/* eslint-disable no-case-declarations */

import { AfterViewInit, ChangeDetectionStrategy, Component, ViewChild } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { FormGroup, UntypedFormControl, Validators } from '@angular/forms';
import { SelectionModel } from '@angular/cdk/collections';
import { MatDialog } from '@angular/material/dialog';
import { MatLegacyPaginator as MatPaginator } from '@angular/material/legacy-paginator';
import { MatLegacyTableDataSource as MatTableDataSource } from '@angular/material/legacy-table';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatSort } from '@angular/material/sort';

import { GlobalEvent, GlobalEventType } from '@mx-ros-web/models/global.event';
import { TranslateService } from '@ngx-translate/core';
import { cloneDeep, findIndex, forEach, orderBy } from 'lodash-es';
import * as moment from 'moment';
import { Observable, forkJoin, of } from 'rxjs';
import { filter } from 'rxjs/operators';

import * as environment from '@mx-ros-web/environments/environment';
import { ConfirmDialogComponent } from '@mx-ros-web/app/shared/Component/confirm-dialog/confirm-dialog.component';
import { OptionInterface } from '@mx-ros-web/app/shared/def/share-model.def';
import { SRV_DST } from '@mx-ros-web/app/shared/def/srv-dst.def';
import { SRV_NTP_AUTH_KEYS } from '@mx-ros-web/app/shared/def/srv-ntp-auth-keys.def';
import { SRV_NTP } from '@mx-ros-web/app/shared/def/srv-ntp.def';
import { SRV_TIMESERVER } from '@mx-ros-web/app/shared/def/srv-timeset.def';
import { AppState } from '@mx-ros-web/shared/Service/app.service';
import { AuthService } from '@mx-ros-web/shared/Service/auth.service';
import { ErrorService } from '@mx-ros-web/shared/Service/error.service';
import { UtilsService } from '@mx-ros-web/shared/Service/utils.service';
import { mediumDialogConfig, smallDialogConfig } from '@mx-ros-web/shared/dialog-config.service';

import { AuthKeyDeleteDialogComponent } from './auth-key-delete-dialog/auth-key-delete-dialog';
import { AuthKeySettingDialogComponent } from './auth-key-setting-dialog/auth-key-setting-dialog';
import { ClockSourceType, NTPKeyType } from './time.def';

type OptionType = { value: number; text: string };
type UtcType = { id: number; value: string; city: string };
@Component({
  templateUrl: './time.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TimeComponent implements AfterViewInit {
  readonly ClockSourceType = ClockSourceType;

  noPermission = this.auth.authNodes.systemTime;

  // selection option
  utcSets: UtcType[] = [];
  offsetHrSel: OptionType[] = [];
  monthSel: OptionType[] = [];
  weekSel: OptionType[] = [];
  daySel: OptionType[] = [];
  hourSel = [...Array(24).keys()].map(x => x++);
  minutesSel = [...Array(60).keys()].map(x => x++);

  timeForm = new FormGroup({
    currentTime: new UntypedFormControl({ value: '', disabled: true }),
    clockSource: new UntypedFormControl(ClockSourceType.LOCAL),
    fallback: new UntypedFormControl(0),
    date: new UntypedFormControl('', Validators.required),
    time: new UntypedFormControl(''),
    firstTimeServer: new UntypedFormControl('', [Validators.maxLength(39)]),
    firstNTPTimeServerKeyString: new UntypedFormControl(''),
    secondTimeServer: new UntypedFormControl('', [Validators.maxLength(39)]),
    secondNTPTimeServerKeyString: new UntypedFormControl('')
  });

  timeZoneForm = new FormGroup({
    timeZone: new UntypedFormControl('', [Validators.required]),
    daylightSaving: new UntypedFormControl('', [Validators.required]),
    daylightSavingSettings: new FormGroup({
      startMonth: new UntypedFormControl(null, Validators.required),
      startWeek: new UntypedFormControl(null, Validators.required),
      startDay: new UntypedFormControl(null, Validators.required),
      startHour: new UntypedFormControl(null, Validators.required),
      startMinutes: new UntypedFormControl(null, Validators.required),
      endMonth: new UntypedFormControl(null, Validators.required),
      endWeek: new UntypedFormControl(null, Validators.required),
      endDay: new UntypedFormControl(null, Validators.required),
      endHour: new UntypedFormControl(null, Validators.required),
      endMinutes: new UntypedFormControl(null, Validators.required),
      offset: new UntypedFormControl(null, Validators.required)
    })
  });

  private uriRequestData;

  @ViewChild('ntpAuthKeyTableSort') ntpAuthKeyTableSort: MatSort;
  @ViewChild('ntpAuthKeyTablePaginator') ntpAuthKeyTablePaginator: MatPaginator;
  ntpAuthKeyTableDisplayedColumns: string[] = ['select', 'edit', 'key-id', 'key-type', 'key-string', 'dummy'];
  ntpAuthKeyTableDataSource: MatTableDataSource<any> = new MatTableDataSource();
  ntpAuthKeySelection = new SelectionModel<any>(true, []);
  ntpAuthKeyTableData = [];
  ntpAuthKeyTableRows = [];
  ntpAuthKeyTableDataLength;
  ntpAuthKeyTablePanelPageSize = 20;

  sntpOptionDisable = false;

  enableOption: OptionInterface<number>[] = [
    { value: 0, text: 'general.common.disable' },
    { value: 1, text: 'general.common.enable' }
  ];

  constructor(
    private appState: AppState,
    private auth: AuthService,
    private dialog: MatDialog,
    private http: HttpClient,
    private snackBar: MatSnackBar,
    private errorService: ErrorService,
    private utils: UtilsService,
    private translate: TranslateService
  ) {
    this.offsetHrSel = this.generateOffsetHrSel();
    this.utcSets = this.getUtcSets();
    this.monthSel = this.getMonthSel();
    this.weekSel = this.getWeekSel();
    this.daySel = this.getDaySel();
  }

  ngAfterViewInit(): void {
    this.setupTableSetting();
    this.refreshTabContent().subscribe({
      complete: () => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
      },
      error: error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      }
    });
  }

  refreshTabContent(): Observable<any> {
    return new Observable(observer => {
      this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
      // clear table in case that re-query happens
      this.ntpAuthKeyTableData = [];
      forkJoin({
        time: this.http.get<SRV_TIMESERVER>(environment.uriRequestURL + '/setting/data/SRV_TIMESET'),
        ntp: this.http.get<SRV_NTP>(environment.uriRequestURL + '/setting/data/SRV_NTP'),
        dst: this.http.get<SRV_DST>(environment.uriRequestURL + '/setting/data/SRV_DST'),
        ntp_auth_key: this.http.get<SRV_NTP_AUTH_KEYS>(environment.uriRequestURL + '/setting/data/SRV_NTP_AUTH_KEYS')
      }).subscribe(
        data => {
          this.uriRequestData = {
            time: data.time,
            ntp: data.ntp,
            dst: data.dst,
            ntpAuthKeyTable: data.ntp_auth_key.SRV_NTP_AUTH_KEYS_Table
          };
          const year = data.time.year + 1900;
          const month = data.time.mon + 1;
          const timeZoneIdx = findIndex(this.utcSets, utc => data.ntp.timezone === utc.id);
          const currentTimeData =
            moment(
              month +
                '/' +
                data.time.day +
                '/' +
                year +
                ' ' +
                data.time.hour +
                ':' +
                data.time.min +
                ':' +
                data.time.sec,
              'M/D/YYYY H:m:s'
            ).format('YYYY-MM-DD HH:mm:ss') +
            ' ' +
            this.utcSets[timeZoneIdx].value;
          const timeData = moment(data.time.hour + ':' + data.time.min, 'H:m').format('HH:mm');

          this.timeForm.patchValue({
            currentTime: currentTimeData,
            clockSource: data.ntp.userenable,
            fallback: data.ntp.fallback ?? 0,
            date: new Date(month + '/' + data.time.day + '/' + year),
            time: timeData,
            firstTimeServer: data.ntp.timeserver1,
            firstNTPTimeServerKeyString: data.ntp.client1_auth_on ? data.ntp.client1_keyID : false,
            secondTimeServer: data.ntp.timeserver2,
            secondNTPTimeServerKeyString: data.ntp.client2_auth_on ? data.ntp.client2_keyID : false
          });
          this.sntpOptionDisable = data.ntp.enable === '1';
          this.timeZoneForm.patchValue({
            timeZone: data.ntp.timezone,
            daylightSaving: data.dst.smon !== 0,
            daylightSavingSettings: {
              startMonth: data.dst.smon === 0 ? null : data.dst.smon,
              startWeek: data.dst.sweek === 0 ? null : data.dst.sweek,
              startDay: data.dst.sday === 0 ? null : data.dst.sday,
              // below two timeZoneForm fields (startHour, startMinutes) are 0-based value, while dst fields are 1-based value.
              startHour: data.dst.shr ? data.dst.shr - 1 : null,
              startMinutes: data.dst.smin ? data.dst.smin - 1 : null,
              endMonth: data.dst.emon === 0 ? null : data.dst.emon,
              endWeek: data.dst.eweek === 0 ? null : data.dst.eweek,
              endDay: data.dst.eday === 0 ? null : data.dst.eday,
              // below two timeZoneForm fields (endHour, endMinutes) are 0-based value, while dst fields are 1-based value.
              endHour: data.dst.ehr ? data.dst.ehr - 1 : null,
              endMinutes: data.dst.emin ? data.dst.emin - 1 : null,
              offset: data.dst.omin
            }
          });
          this.onDstSelectChange(this.timeZoneForm.get('daylightSaving').value);

          // NTP auth table
          forEach(data.ntp_auth_key.SRV_NTP_AUTH_KEYS_Table, (elem, index) => {
            if (elem.valid === 0) {
              return true;
            }
            this.ntpAuthKeyTableData.push({
              key: index,
              keyId: elem.keyID,
              index: index + 1,
              keyType: this.getAuthKeyTypeName(elem.keyType),
              keyTypeRaw: elem.keyType,
              keyString: this.translate.instant('general.common_account.pwd_mask')
            });
          });
          this.updateTableData();

          observer.complete();
        },
        error => {
          this.errorService.handleError(error);
          observer.error(error);
        }
      );
    });
  }

  updateTimeSetting(): void {
    if (this.timeForm.invalid) {
      this.timeForm.markAllAsTouched();
      return;
    }
    let request, patchData;
    smallDialogConfig.data = {
      title: this.translate.instant('general.dialog.warning'),
      desc: this.translate.instant('features.time.mxsecurity_confirm_desc')
    };

    // 沒有 PKG 權限的話，不需要再跳出確認視窗
    const dialogRef = this.auth.serviceDefine.SRV_PKG
      ? this.dialog.open(ConfirmDialogComponent, smallDialogConfig).afterClosed()
      : of({ confirm: true });

    dialogRef.pipe(filter(result => result?.confirm)).subscribe(() => {
      const patchNtpData = {
        userenable: this.timeForm.get('clockSource').value,
        fallback: this.timeForm.controls.fallback.value,
        // raw data
        timezone: this.uriRequestData.ntp.timezone,
        timeserver1: this.uriRequestData.ntp.timeserver1,
        client1_auth_on: this.uriRequestData.ntp.client1_auth_on,
        client1_keyID: this.uriRequestData.ntp.client1_keyID,
        timeserver2: this.uriRequestData.ntp.timeserver2,
        client2_auth_on: this.uriRequestData.ntp.client2_auth_on,
        client2_keyID: this.uriRequestData.ntp.client2_keyID,
        enable: this.uriRequestData.ntp.enable,
        server_auth_on: this.uriRequestData.ntp.server_auth_on
      };

      switch (this.timeForm.get('clockSource').value) {
        case ClockSourceType.LOCAL:
          const currentDateTime = new Date(this.timeForm.value.date);
          const currentYear = currentDateTime.getFullYear() - 1900;
          const currentMonth = currentDateTime.getMonth();
          const currentDate = currentDateTime.getDate();
          const currentHour = parseInt(this.timeForm.value.time.split(':')[0], 10);
          const currentMinute = parseInt(this.timeForm.value.time.split(':')[1], 10);
          const patchTimeData = {
            year: currentYear,
            mon: currentMonth,
            day: currentDate,
            hour: currentHour,
            min: currentMinute,
            sec: 0
          };
          request = `${environment.uriRequestURL}/setting/data/?SRV=SRV_NTP&SRV0=SRV_TIMESET`;
          patchData = Object.assign({}, patchNtpData, patchTimeData);
          break;
        case ClockSourceType.NTP:
        case ClockSourceType.SNTP:
          let firstNTPTimeServerEnable = false;
          let firstNTPTimeServerkey = 0;
          let secondNTPTimeServerEnable = false;
          let secondNTPTimeServerkey = 0;
          if (this.timeForm.get('firstNTPTimeServerKeyString').value > 0) {
            firstNTPTimeServerEnable = true;
            firstNTPTimeServerkey = this.timeForm.get('firstNTPTimeServerKeyString').value;
          }
          if (this.timeForm.get('secondNTPTimeServerKeyString').value > 0) {
            secondNTPTimeServerEnable = true;
            secondNTPTimeServerkey = this.timeForm.get('secondNTPTimeServerKeyString').value;
          }
          patchNtpData.client1_auth_on = firstNTPTimeServerEnable ? 1 : 0;
          patchNtpData.client2_auth_on = secondNTPTimeServerEnable ? 1 : 0;
          patchNtpData.client1_keyID = firstNTPTimeServerkey;
          patchNtpData.client2_keyID = secondNTPTimeServerkey;
          patchNtpData.timeserver1 = this.timeForm.get('firstTimeServer').value;
          patchNtpData.timeserver2 = this.timeForm.get('secondTimeServer').value;
          request = `${environment.uriRequestURL}/setting/data/?SRV=SRV_NTP`;
          patchData = patchNtpData;
          break;
      }
      this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
      this.snackBar.open(this.translate.instant('request_handler.action_saving'));
      this.http.post(request, patchData).subscribe(
        () => {
          this.snackBar.open(this.translate.instant('response_handler.res_global_success'), '', {
            duration: 3000
          });
          this.refreshTabContent().subscribe({
            complete: () => {
              this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            },
            error: error => {
              this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
              this.errorService.handleError(error);
            }
          });
        },
        error => {
          this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          this.errorService.handleError(error);
        }
      );
    });
  }

  syncBrowserTime(): void {
    const now = new Date();
    this.timeForm.patchValue({
      date: now,
      time: this.utils.getDateTimeString(now, { onlyTime: true }).substring(0, 5)
    });
  }

  refresh(): void {
    this.refreshTabContent().subscribe({
      complete: () => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.snackBar.open(this.translate.instant('response_handler.res_complete_refresh'), '', {
          duration: 3000
        });
      },
      error: error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      }
    });
  }

  updateTimeZoneSetting(): void {
    if (this.timeZoneForm.invalid) {
      this.timeZoneForm.markAllAsTouched();
      return;
    }
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    this.snackBar.open(this.translate.instant('request_handler.action_saving'));
    const postNTPData = {
      timezone: this.timeZoneForm.value.timeZone,
      // raw data
      userenable: this.uriRequestData.ntp.userenable,
      timeserver1: this.uriRequestData.ntp.timeserver1,
      client1_auth_on: this.uriRequestData.ntp.client1_auth_on,
      client1_keyID: this.uriRequestData.ntp.client1_keyID,
      timeserver2: this.uriRequestData.ntp.timeserver2,
      client2_auth_on: this.uriRequestData.ntp.client2_auth_on,
      client2_keyID: this.uriRequestData.ntp.client2_keyID,
      enable: this.uriRequestData.ntp.enable,
      server_auth_on: this.uriRequestData.ntp.server_auth_on
    };
    let postDSTData = {};
    if (this.timeZoneForm.value.daylightSaving) {
      postDSTData = {
        smon: this.timeZoneForm.get('daylightSavingSettings.startMonth').value,
        sweek: this.timeZoneForm.get('daylightSavingSettings.startWeek').value,
        sday: this.timeZoneForm.get('daylightSavingSettings.startDay').value,
        // below two DST fields (shr, smin) are 1-based value, 0 for disabled.
        shr: this.timeZoneForm.get('daylightSavingSettings.startHour').value + 1,
        smin: this.timeZoneForm.get('daylightSavingSettings.startMinutes').value + 1,
        emon: this.timeZoneForm.get('daylightSavingSettings.endMonth').value,
        eweek: this.timeZoneForm.get('daylightSavingSettings.endWeek').value,
        eday: this.timeZoneForm.get('daylightSavingSettings.endDay').value,
        // below two DST fields (ehr, emin) are 1-based value, 0 for disabled.
        ehr: this.timeZoneForm.get('daylightSavingSettings.endHour').value + 1,
        emin: this.timeZoneForm.get('daylightSavingSettings.endMinutes').value + 1,
        omin: this.timeZoneForm.get('daylightSavingSettings.offset').value
      };
    } else {
      postDSTData = {
        smon: 0,
        sweek: 0,
        sday: 0,
        shr: 0,
        smin: 0,
        emon: 0,
        eweek: 0,
        eday: 0,
        ehr: 0,
        emin: 0,
        omin: 0
      };
    }

    const postData = Object.assign({}, postNTPData, postDSTData);
    forkJoin([
      this.http.post(environment.uriRequestURL + '/setting/data/?SRV=SRV_NTP&SRV0=SRV_DST', postData)
    ]).subscribe(
      () => {
        this.refresh();
        this.snackBar.open(this.translate.instant('response_handler.res_global_success'), '', {
          duration: 3000
        });
      },
      error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      }
    );
  }

  onDstSelectChange(dstEnable: boolean): void {
    if (dstEnable) {
      this.timeZoneForm.get('daylightSavingSettings').enable();
    } else {
      this.timeZoneForm.get('daylightSavingSettings').disable();
    }
  }

  pad(num: number): string {
    if (num < 10) {
      return '0' + num;
    }
    return String(num);
  }

  private generateOffsetHrSel(): OptionType[] {
    const indexArray = [...Array(25).keys()];
    const offsetHrSel: OptionType[] = [];
    forEach(indexArray, index => {
      offsetHrSel.push({
        value: index * 30,
        text: String(index * 0.5)
      });
    });
    return offsetHrSel;
  }

  private getMonthSel(): OptionType[] {
    return [
      { value: 1, text: this.translate.instant('general.date.january') },
      { value: 2, text: this.translate.instant('general.date.february') },
      { value: 3, text: this.translate.instant('general.date.march') },
      { value: 4, text: this.translate.instant('general.date.april') },
      { value: 5, text: this.translate.instant('general.date.may') },
      { value: 6, text: this.translate.instant('general.date.june') },
      { value: 7, text: this.translate.instant('general.date.july') },
      { value: 8, text: this.translate.instant('general.date.august') },
      { value: 9, text: this.translate.instant('general.date.september') },
      { value: 10, text: this.translate.instant('general.date.october') },
      { value: 11, text: this.translate.instant('general.date.november') },
      { value: 12, text: this.translate.instant('general.date.december') }
    ];
  }
  private getWeekSel(): OptionType[] {
    return [
      { value: 1, text: this.translate.instant('general.date.1st_week') },
      { value: 2, text: this.translate.instant('general.date.2nd_week') },
      { value: 3, text: this.translate.instant('general.date.3rd_week') },
      { value: 4, text: this.translate.instant('general.date.4th_week') },
      { value: 6, text: this.translate.instant('general.date.last_week') }
    ];
  }
  private getDaySel(): OptionType[] {
    return [
      { value: 1, text: this.translate.instant('general.date.sunday') },
      { value: 2, text: this.translate.instant('general.date.monday') },
      { value: 3, text: this.translate.instant('general.date.tuesday') },
      { value: 4, text: this.translate.instant('general.date.wednesday') },
      { value: 5, text: this.translate.instant('general.date.thursday') },
      { value: 6, text: this.translate.instant('general.date.friday') },
      { value: 7, text: this.translate.instant('general.date.saturday') }
    ];
  }

  private getUtcSets(): UtcType[] {
    return [
      { id: 0, value: 'UTC-12:00', city: this.translate.instant('features.time.city.eniwetok') },
      {
        id: 1,
        value: 'UTC-11:00',
        city: this.translate.instant('features.time.city.midway_island')
      },
      { id: 2, value: 'UTC-10:00', city: this.translate.instant('features.time.city.hawaii') },
      { id: 3, value: 'UTC-09:00', city: this.translate.instant('features.time.city.alaska') },
      {
        id: 4,
        value: 'UTC-08:00',
        city: this.translate.instant('features.time.city.pacific_time')
      },
      { id: 5, value: 'UTC-07:00', city: this.translate.instant('features.time.city.arizona') },
      {
        id: 6,
        value: 'UTC-07:00',
        city: this.translate.instant('features.time.city.mountain_time')
      },
      {
        id: 7,
        value: 'UTC-06:00',
        city: this.translate.instant('features.time.city.central_time')
      },
      { id: 8, value: 'UTC-06:00', city: this.translate.instant('features.time.city.mexico_city') },
      {
        id: 9,
        value: 'UTC-06:00',
        city: this.translate.instant('features.time.city.saskatchewan')
      },
      { id: 10, value: 'UTC-05:00', city: this.translate.instant('features.time.city.bogota') },
      {
        id: 11,
        value: 'UTC-05:00',
        city: this.translate.instant('features.time.city.eastern_time')
      },
      { id: 12, value: 'UTC-05:00', city: this.translate.instant('features.time.city.indiana') },
      {
        id: 13,
        value: 'UTC-04:00',
        city: this.translate.instant('features.time.city.atlantic_time')
      },
      { id: 14, value: 'UTC-04:00', city: this.translate.instant('features.time.city.caracas') },
      { id: 15, value: 'UTC-04:00', city: this.translate.instant('features.time.city.santiago') },
      {
        id: 16,
        value: 'UTC-03:30',
        city: this.translate.instant('features.time.city.newfoundland')
      },
      { id: 17, value: 'UTC-03:00', city: this.translate.instant('features.time.city.brasilia') },
      {
        id: 18,
        value: 'UTC-03:00',
        city: this.translate.instant('features.time.city.buenos_aires')
      },
      {
        id: 19,
        value: 'UTC-02:00',
        city: this.translate.instant('features.time.city.mid_atlantic')
      },
      { id: 20, value: 'UTC-01:00', city: this.translate.instant('features.time.city.azores') },
      { id: 21, value: 'UTC+00:00', city: this.translate.instant('features.time.city.casablanca') },
      { id: 22, value: 'UTC+00:00', city: this.translate.instant('features.time.city.greenwich') },
      { id: 23, value: 'UTC+01:00', city: this.translate.instant('features.time.city.amsterdam') },
      { id: 24, value: 'UTC+01:00', city: this.translate.instant('features.time.city.belgrade') },
      { id: 25, value: 'UTC+01:00', city: this.translate.instant('features.time.city.brussels') },
      { id: 26, value: 'UTC+01:00', city: this.translate.instant('features.time.city.sarajevo') },
      { id: 27, value: 'UTC+02:00', city: this.translate.instant('features.time.city.athens') },
      { id: 28, value: 'UTC+02:00', city: this.translate.instant('features.time.city.bucharest') },
      { id: 29, value: 'UTC+02:00', city: this.translate.instant('features.time.city.cairo') },
      { id: 30, value: 'UTC+02:00', city: this.translate.instant('features.time.city.harare') },
      { id: 31, value: 'UTC+02:00', city: this.translate.instant('features.time.city.helsinki') },
      { id: 32, value: 'UTC+02:00', city: this.translate.instant('features.time.city.jerusalem') },
      { id: 33, value: 'UTC+03:00', city: this.translate.instant('features.time.city.baghdad') },
      { id: 34, value: 'UTC+03:00', city: this.translate.instant('features.time.city.moscow') },
      { id: 35, value: 'UTC+03:00', city: this.translate.instant('features.time.city.nairobi') },
      { id: 36, value: 'UTC+03:30', city: this.translate.instant('features.time.city.tehran') },
      { id: 37, value: 'UTC+04:00', city: this.translate.instant('features.time.city.abu_dhabi') },
      { id: 38, value: 'UTC+04:00', city: this.translate.instant('features.time.city.baku') },
      { id: 39, value: 'UTC+04:30', city: this.translate.instant('features.time.city.kabul') },
      {
        id: 40,
        value: 'UTC+05:00',
        city: this.translate.instant('features.time.city.ekaterinburg')
      },
      { id: 41, value: 'UTC+05:00', city: this.translate.instant('features.time.city.islamabad') },
      { id: 42, value: 'UTC+05:30', city: this.translate.instant('features.time.city.bombay') },
      { id: 43, value: 'UTC+06:00', city: this.translate.instant('features.time.city.astana') },
      { id: 44, value: 'UTC+06:00', city: this.translate.instant('features.time.city.colombo') },
      { id: 45, value: 'UTC+07:00', city: this.translate.instant('features.time.city.bangkok') },
      { id: 46, value: 'UTC+08:00', city: this.translate.instant('features.time.city.beijing') },
      { id: 47, value: 'UTC+08:00', city: this.translate.instant('features.time.city.perth') },
      { id: 48, value: 'UTC+08:00', city: this.translate.instant('features.time.city.singapore') },
      { id: 49, value: 'UTC+08:00', city: this.translate.instant('features.time.city.taipei') },
      { id: 50, value: 'UTC+09:00', city: this.translate.instant('features.time.city.osaka') },
      { id: 51, value: 'UTC+09:00', city: this.translate.instant('features.time.city.seoul') },
      { id: 52, value: 'UTC+09:00', city: this.translate.instant('features.time.city.yakutsk') },
      { id: 53, value: 'UTC+09:30', city: this.translate.instant('features.time.city.adelaide') },
      { id: 54, value: 'UTC+09:30', city: this.translate.instant('features.time.city.darwin') },
      { id: 55, value: 'UTC+10:00', city: this.translate.instant('features.time.city.brisbane') },
      { id: 56, value: 'UTC+10:00', city: this.translate.instant('features.time.city.canberra') },
      { id: 57, value: 'UTC+10:00', city: this.translate.instant('features.time.city.guam') },
      { id: 58, value: 'UTC+10:00', city: this.translate.instant('features.time.city.hobart') },
      {
        id: 59,
        value: 'UTC+10:00',
        city: this.translate.instant('features.time.city.vladivostok')
      },
      { id: 60, value: 'UTC+11:00', city: this.translate.instant('features.time.city.magadan') },
      { id: 61, value: 'UTC+12:00', city: this.translate.instant('features.time.city.auckland') },
      { id: 62, value: 'UTC+12:00', city: this.translate.instant('features.time.city.fiji') }
    ];
  }

  updateNtpAuthKeyFilter(filterValue: string): void {
    this.ntpAuthKeyTableDataSource.filter = filterValue.trim().toLowerCase();
  }

  isAllNtpAuthKeySelected(): boolean {
    const numSelected = this.ntpAuthKeySelection.selected.length;
    const numRows = this.ntpAuthKeyTableDataSource.data.length;
    return numSelected === numRows;
  }

  ntpAuthKeyMasterToggle(): void {
    this.isAllNtpAuthKeySelected()
      ? this.ntpAuthKeySelection.clear()
      : this.ntpAuthKeyTableDataSource.data.forEach(row => this.ntpAuthKeySelection.select(row));
  }

  createNtpAuthKey(): void {
    mediumDialogConfig.data = {
      data: null,
      ntpAuthKeyTable: cloneDeep(this.uriRequestData.ntpAuthKeyTable)
    };
    const dialogRef = this.dialog.open(AuthKeySettingDialogComponent, mediumDialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        this.refreshTabContent().subscribe({
          complete: () => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          },
          error: error => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            this.errorService.handleError(error);
          }
        });
      }
    });
  }

  editNtpAuthKey(row): void {
    mediumDialogConfig.data = {
      data: row,
      ntpAuthKeyTable: cloneDeep(this.uriRequestData.ntpAuthKeyTable)
    };
    const dialogRef = this.dialog.open(AuthKeySettingDialogComponent, mediumDialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        this.refreshTabContent().subscribe({
          complete: () => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          },
          error: error => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            this.errorService.handleError(error);
          }
        });
      }
    });
  }

  deleteNtpAuthKey(row?: any): void {
    if (row) {
      smallDialogConfig.data = {
        data: row,
        ntpAuthKeyTable: cloneDeep(this.uriRequestData.ntpAuthKeyTable)
      };
    } else {
      let selectedData = this.ntpAuthKeySelection.selected;
      selectedData = orderBy(selectedData, ['key'], ['desc']);
      smallDialogConfig.data = {
        data: selectedData,
        ntpAuthKeyTable: cloneDeep(this.uriRequestData.ntpAuthKeyTable)
      };
    }
    const dialogRef = this.dialog.open(AuthKeyDeleteDialogComponent, smallDialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        this.refreshTabContent().subscribe({
          complete: () => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          },
          error: error => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            this.errorService.handleError(error);
          }
        });
      }
    });
  }

  updateTableData(): void {
    this.ntpAuthKeyTableRows = this.ntpAuthKeyTableData.slice(0, this.ntpAuthKeyTablePanelPageSize);
    this.ntpAuthKeyTableDataLength = this.ntpAuthKeyTableData.length;
    this.ntpAuthKeyTableDataSource.data = this.ntpAuthKeyTableData;
    this.ntpAuthKeySelection.clear();
  }

  setupTableSetting(): void {
    this.ntpAuthKeyTableDataSource.paginator = this.ntpAuthKeyTablePaginator;
    this.ntpAuthKeyTableDataSource.sort = this.ntpAuthKeyTableSort;
    this.ntpAuthKeyTableDataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'key-id':
          return item.keyId;
        case 'key-type':
          return item.keyType;
        case 'key-string':
          return item.keyString;
        default:
          return item[property];
      }
    };
    this.ntpAuthKeyTableDataSource.filterPredicate = (data: any, filter: string): boolean => {
      return (
        String(data.keyId).toLowerCase().indexOf(filter) !== -1 ||
        String(data.keyType).toLowerCase().indexOf(filter) !== -1 ||
        String(data.keyString).toLowerCase().indexOf(filter) !== -1
      );
    };
  }

  trackByFn(index, item) {
    return item.key;
  }

  onNtpAuthKeyTablePageChange(event): void {
    this.ntpAuthKeyTableRows = this.ntpAuthKeyTableData.slice(
      this.ntpAuthKeyTablePanelPageSize * event.pageIndex,
      this.ntpAuthKeyTablePanelPageSize * event.pageIndex + this.ntpAuthKeyTablePanelPageSize
    );
  }

  getAuthKeyTypeName(auth_key: NTPKeyType): string {
    switch (auth_key) {
      case NTPKeyType.NTP_AUTH_KEY_TYPE_MD5:
        return this.translate.instant('general.common_abbrev.md5');
      case NTPKeyType.NTP_AUTH_KEY_TYPE_SHA512:
        return `${this.translate.instant('general.common_abbrev.sha')}-512`;
    }
  }
}
