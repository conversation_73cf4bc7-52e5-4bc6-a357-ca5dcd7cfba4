import { ChangeDetectionStrategy, Component, Inject, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { AbstractControl, UntypedFormControl, UntypedFormGroup, ValidatorFn, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';

import { GlobalEvent, GlobalEventType } from '@mx-ros-web/models/global.event';
import { TranslateService } from '@ngx-translate/core';
import { cloneDeep, findIndex } from 'lodash-es';

import * as environment from '@mx-ros-web/environments/environment';
import { AppState } from '@mx-ros-web/shared/Service/app.service';
import { ErrorService } from '@mx-ros-web/shared/Service/error.service';
import { ValidatorPattern } from '@mx-ros-web/shared/validator/validators';

import { NTPKeyType } from '../time.def';

@Component({
  templateUrl: 'auth-key-setting-dialog.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AuthKeySettingDialogComponent implements OnInit {
  readonly NTPKeyTypeSel = [
    {
      value: NTPKeyType.NTP_AUTH_KEY_TYPE_MD5,
      text: this.translate.instant('general.common_abbrev.md5')
    },
    {
      value: NTPKeyType.NTP_AUTH_KEY_TYPE_SHA512,
      text: `${this.translate.instant('general.common_abbrev.sha')}-512`
    }
  ];

  hideKey = true;
  private ntpAuthKeyTable = [];
  private keyIndex;
  authKeySettingForm: UntypedFormGroup;

  constructor(
    public dialogRef: MatDialogRef<AuthKeySettingDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public dialogData: any,
    private appState: AppState,
    private http: HttpClient,
    private snackBar: MatSnackBar,
    private errorService: ErrorService,
    private translate: TranslateService
  ) {
    this.ntpAuthKeyTable = cloneDeep(this.dialogData.ntpAuthKeyTable);
    if (this.dialogData.data) {
      // edit, find the desired one.
      this.keyIndex = this.dialogData.data.key;
    } else {
      // create, find empty
      this.keyIndex = findIndex(this.dialogData.ntpAuthKeyTable, (elem: any) => elem.valid === 0);
    }
    this.authKeySettingForm = new UntypedFormGroup({
      keyId: new UntypedFormControl('', [
        Validators.required,
        Validators.min(1),
        Validators.max(65535),
        Validators.pattern(ValidatorPattern.VAILD_NUMBER),
        this.isDuplicated(this.ntpAuthKeyTable)
      ]),
      type: new UntypedFormControl('', Validators.required),
      keyString: new UntypedFormControl('', [
        Validators.required,
        Validators.maxLength(32),
        Validators.pattern(ValidatorPattern.VAILD_REGEX_NOT_SPACE)
      ])
    });
  }

  ngOnInit(): void {
    if (this.dialogData.data) {
      this.authKeySettingForm.patchValue({
        keyId: this.dialogData.data.keyId,
        type: this.dialogData.data.keyTypeRaw
      });
      this.authKeySettingForm.controls['keyId'].disable();
    }
  }

  onGeneralDialogSubmit() {
    if (this.authKeySettingForm.invalid) {
      this.authKeySettingForm.markAllAsTouched();
      return;
    }
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    const patchData = {
      valid: 1,
      keyID: this.authKeySettingForm.get('keyId').value,
      keyType: this.authKeySettingForm.get('type').value,
      keyStr: this.authKeySettingForm.get('keyString').value
    };
    this.dialogData.ntpAuthKeyTable[this.keyIndex] = patchData;

    this.snackBar.open(this.translate.instant('request_handler.action_saving'));

    this.http
      .post(`${environment.uriRequestURL}/setting/data/?SRV=SRV_NTP_AUTH_KEYS`, this.dialogData.ntpAuthKeyTable)
      .subscribe(
        () => {
          this.dialogRef.close('submit');
          this.snackBar.open(this.translate.instant('response_handler.res_entry_create_success'), '', {
            duration: 3000
          });
        },
        error => {
          this.dialogRef.close('submit');
          this.errorService.handleError(error);
        }
      );
  }

  private isDuplicated(ntpAuthKeyTable: Array<any>): ValidatorFn {
    // skip itself for key ID comparison
    if (this.dialogData.data) {
      ntpAuthKeyTable.splice(this.keyIndex, 1);
    }
    return (nameControl: AbstractControl): { [key: string]: any } | null => {
      // find duplicate ID return {isDuplicate: true}
      // otherwise return null
      const rt = ntpAuthKeyTable.some(item => item.keyID === nameControl.value);
      return rt ? { isDuplicate: true } : null;
    };
  }
}
