import { ChangeDetectionStrategy, Component, Inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import {
  AbstractControl,
  FormControl,
  FormGroup,
  UntypedFormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators
} from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';

import { GlobalEvent, GlobalEventType } from '@mx-ros-web/models/global.event';
import { TranslateService } from '@ngx-translate/core';
import { forEach } from 'lodash-es';

import * as environment from '@mx-ros-web/environments/environment';
import { AppState } from '@mx-ros-web/shared/Service/app.service';
import { ErrorService } from '@mx-ros-web/shared/Service/error.service';
import { UtilsService } from '@mx-ros-web/shared/Service/utils.service';
import { ValidatorPattern } from '@mx-ros-web/shared/validator/validators';

@Component({
  templateUrl: 'static-route-setting-dialog.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class StaticRouteSettingDialogComponent {
  staticRoute = [];
  readonly editIndex: number = 0;
  readonly netmaskOption = this.utils.getSubMaskOption({ rangeStart: 0, rangeEnd: 32 });

  staticRouteSettingForm = new FormGroup(
    {
      enable: new FormControl(null, [Validators.required]),
      name: new FormControl('', [
        Validators.required,
        Validators.pattern(ValidatorPattern.VAILD_REGEX_NOT_SPACE),
        Validators.maxLength(10),
        this.isDuplicateName()
      ]),
      dest: new FormControl('', [Validators.required, Validators.pattern(ValidatorPattern.IPADDR_REGEX)]),
      mask: new FormControl('', [Validators.required]),
      nexthop: new FormControl('', [Validators.required, Validators.pattern(ValidatorPattern.IPADDR_REGEX)]),
      metric: new FormControl('', [Validators.required, Validators.min(1), Validators.max(254)])
    },
    [this.checkStaticRoute()]
  );

  constructor(
    public dialogRef: MatDialogRef<StaticRouteSettingDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public dialogData: any,
    private appState: AppState,
    private http: HttpClient,
    private snackBar: MatSnackBar,
    private errorService: ErrorService,
    private translate: TranslateService,
    private utils: UtilsService
  ) {
    this.staticRoute = this.dialogData.staticRoute;
    if (this.dialogData.row) {
      this.editIndex = this.dialogData.row.key;
      this.staticRouteSettingForm.patchValue({
        enable: this.dialogData.row.enableRaw === 1,
        name: this.dialogData.row.name,
        dest: this.dialogData.row.dest,
        mask: this.dialogData.row.mask,
        nexthop: this.dialogData.row.nexthop,
        metric: this.dialogData.row.metric
      });
    } else {
      this.editIndex = this.dialogData.staticRoute.length;
    }
  }

  private checkStaticRoute(): ValidatorFn {
    return (form: UntypedFormGroup): ValidationErrors | null => {
      let duplicate = false;
      forEach(this.staticRoute, (entry, entryIndex: number) => {
        if (entryIndex === this.editIndex) {
          return true;
        }
        if (
          entry.dest === form.get('dest').value &&
          entry.mask === form.get('mask').value &&
          (entry.nexthop === form.get('nexthop').value || entry.metric === form.get('metric').value)
        ) {
          duplicate = true;
        }
        // break forEach
        if (duplicate) {
          return false;
        }
      });
      return duplicate ? { duplicate: true } : null;
    };
  }

  private isDuplicateName(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      for (const entry of this.staticRoute) {
        if (this.dialogData.row && entry.name === this.dialogData.row.name) {
          // Skip if name keeps the same when editing.
          continue;
        }
        if (entry.name === control.value) {
          return { isDuplicate: { value: control.value } };
        }
      }
      return null;
    };
  }

  onDialogSubmit() {
    if (this.staticRouteSettingForm.invalid) {
      this.staticRouteSettingForm.markAllAsTouched();
      return;
    }
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    (this.staticRoute[this.editIndex].enable = this.staticRouteSettingForm.get('enable').value ? 1 : 0),
      (this.staticRoute[this.editIndex].name = this.staticRouteSettingForm.get('name').value),
      (this.staticRoute[this.editIndex].dest = this.staticRouteSettingForm.get('dest').value),
      (this.staticRoute[this.editIndex].mask = this.staticRouteSettingForm.get('mask').value),
      (this.staticRoute[this.editIndex].nexthop = this.staticRouteSettingForm.get('nexthop').value),
      (this.staticRoute[this.editIndex].metric = this.staticRouteSettingForm.get('metric').value),
      this.http.post(`${environment.uriRequestURL}/setting/data/?SRV=SRV_SROUTE`, this.staticRoute).subscribe(
        () => {
          this.dialogRef.close('submit');
          this.snackBar.open(this.translate.instant('response_handler.res_global_success'), '', {
            duration: 3000
          });
        },
        error => {
          this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          this.errorService.handleError(error);
        }
      );
  }

  onCreateDialogSubmit() {
    if (this.staticRouteSettingForm.invalid) {
      this.staticRouteSettingForm.markAllAsTouched();
      return;
    }
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    const patchData = {
      enable: this.staticRouteSettingForm.get('enable').value ? 1 : 0,
      name: this.staticRouteSettingForm.get('name').value,
      dest: this.staticRouteSettingForm.get('dest').value,
      mask: this.staticRouteSettingForm.get('mask').value,
      nexthop: this.staticRouteSettingForm.get('nexthop').value,
      metric: this.staticRouteSettingForm.get('metric').value
    };
    this.staticRoute.push(patchData);

    this.snackBar.open(this.translate.instant('request_handler.action_saving'));
    this.http.post(`${environment.uriRequestURL}/setting/data/?SRV=SRV_SROUTE`, this.staticRoute).subscribe(
      () => {
        this.dialogRef.close('submit');
        this.snackBar.open(this.translate.instant('response_handler.res_global_success'), '', {
          duration: 3000
        });
      },
      error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      }
    );
  }
}
