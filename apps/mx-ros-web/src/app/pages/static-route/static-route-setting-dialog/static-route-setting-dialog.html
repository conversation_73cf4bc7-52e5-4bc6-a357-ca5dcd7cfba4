<h1 mat-dialog-title *ngIf="!dialogData.row">
  {{ 'features.static_route.title_add_static_route_dialog_title' | translate }}
</h1>
<h1 mat-dialog-title *ngIf="dialogData.row">
  {{ 'features.static_route.title_edit_static_route_pre_msg' | translate }}
</h1>
<div mat-dialog-content>
  <form [formGroup]="staticRouteSettingForm">
    <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
      <mat-form-field>
        <mat-select
          id="select-static-route"
          placeholder="{{ 'general.common.status' | translate }}"
          formControlName="enable"
          required
        >
          <mat-option id="option-static-route-enable" [value]="true">
            {{ 'general.common.enable' | translate }}
          </mat-option>
          <mat-option id="option-static-route-disable" [value]="false">
            {{ 'general.common.disable' | translate }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
      <mat-form-field>
        <input
          id="input-static-route-name"
          matInput
          maxlength="10"
          #name
          placeholder="{{ 'general.common.name' | translate }}"
          formControlName="name"
          required
        />
        <mat-hint align="end">{{name.value.length}} / 10</mat-hint>
        <mat-error *ngIf="staticRouteSettingForm.get('name').errors?.pattern"
          >{{ 'validators.invalid_format_not_space' | translate }}
        </mat-error>
        <mat-error *ngIf="staticRouteSettingForm.get('name').errors?.isDuplicate">
          {{ 'validators.duplicate_name' | translate }}
        </mat-error>
      </mat-form-field>
    </div>
    <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
      <mat-form-field>
        <input
          id="input-static-route-dest"
          matInput
          placeholder="{{ 'general.common_route.destination' | translate }}"
          formControlName="dest"
          required
        />
        <mat-error *ngIf="staticRouteSettingForm.get('dest').errors?.pattern">
          {{ 'validators.invalid_ip_address' | translate }}
        </mat-error>
      </mat-form-field>
      <mat-form-field>
        <mat-select
          id="select-subnet-mask"
          placeholder="{{ 'general.common.subnet_mask' | translate }}"
          formControlName="mask"
          required
        >
          <mat-option id="option-subnet-mask-{{ mask.id }}" *ngFor="let mask of netmaskOption" [value]="mask.value">
            {{ mask.name }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
      <mat-form-field>
        <input
          id="input-static-route-nexthop"
          matInput
          placeholder="{{ 'general.common_route.nexthop' | translate }}"
          formControlName="nexthop"
          required
        />
        <mat-error *ngIf="staticRouteSettingForm.get('nexthop').errors?.pattern">
          {{ 'validators.invalid_ip_address' | translate }}
        </mat-error>
      </mat-form-field>
      <mat-form-field>
        <input
          id="input-static-route-metric"
          matInput
          appNumberOnly
          min="1"
          max="254"
          placeholder="{{ 'general.common_route.metric' | translate }}"
          formControlName="metric"
          required
        />
        <mat-hint align="begin">
          {{ 'validators.require_range_between' | translate:{ rangeBegin: 1, rangeEnd: 254 } }}</mat-hint
        >
        <mat-error
          *ngIf="staticRouteSettingForm.get('metric').errors?.min || staticRouteSettingForm.get('metric').errors?.max"
        >
          {{ 'validators.invalid_range' | translate:{ rangeBegin: 1, rangeEnd: 254 } }}
        </mat-error>
      </mat-form-field>
    </div>
    <div class="caption-font error-caption" *ngIf="staticRouteSettingForm.errors?.duplicate" fxLayout.xs="column">
      {{ 'validators.duplicate_static_route_rule' | translate }}
    </div>
  </form>
</div>
<div mat-dialog-actions align="end">
  <button id="dialog-button-cancel" mat-button mat-dialog-close color="primary">
    {{ 'general.button.cancel' | translate }}
  </button>
  <button id="dialog-button-apply" mat-raised-button color="primary" (click)="onDialogSubmit()" *ngIf="dialogData.row">
    {{ 'general.button.apply' | translate }}
  </button>
  <button
    id="dialog-button-create"
    mat-raised-button
    color="primary"
    (click)="onCreateDialogSubmit()"
    *ngIf="!dialogData.row"
  >
    {{ 'general.button.create' | translate }}
  </button>
</div>
