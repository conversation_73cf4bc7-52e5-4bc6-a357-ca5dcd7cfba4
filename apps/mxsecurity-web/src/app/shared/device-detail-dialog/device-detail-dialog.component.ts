import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';

import { TranslateService } from '@ngx-translate/core';
import { DateTime, Interval } from 'luxon';
import { Subject, takeUntil } from 'rxjs';

import { PostService } from '@mxsecurity/shared/http/post.service';
import { SnackBarService } from '@mxsecurity/shared/service/snack-bar.service';
import { SharedService } from '@mxsecurity/shared/shared.service';
import { SimpleDialogService } from '@mxsecurity/shared/simple-dialog/simple-dialog.service';
import { SocketResponseType } from '@mxsecurity/shared/socket/socket.model';
import { SocketService } from '@mxsecurity/shared/socket/socket.service';
import { Pattern, PatternLength } from '@mxsecurity/shared/validator/pattern.model';

import { AuthService } from '../auth/auth.service';
import { RoleId } from '../auth/user.model';
import { checkSpaceCount } from '../validator/max-space.validator';
import { DeviceDetailInfo, DeviceProducts, DeviceStatus } from './device-detail-dialog.model';

@Component({
  templateUrl: './device-detail-dialog.component.html',
  styleUrls: ['./device-detail-dialog.component.scss']
})
export class DeviceDetailDialogComponent implements OnInit, OnDestroy {
  patternLength = PatternLength;
  deviceStatus = DeviceStatus;

  devDetailData: DeviceDetailInfo;
  isCellular: boolean;
  isLocationEditMode = false;
  locationLoading = false;
  locationCountDown: any;
  noData = this._sharedService.noData;

  locationForm = new FormGroup({
    location: new FormControl('', [Validators.required, Validators.pattern(Pattern.Location), checkSpaceCount(4)])
  });

  private _locationCountDownSeconds = 30;
  private _deviceId = this._simpleDialogService.getReceivedData();
  private _notifyDestroy = new Subject<void>();
  constructor(
    private _simpleDialogService: SimpleDialogService,
    private _postService: PostService,
    private _sharedService: SharedService,
    private _socketService: SocketService,
    private _translate: TranslateService,
    private _snackBar: SnackBarService,
    private _authService: AuthService
  ) {}

  ngOnInit(): void {
    setTimeout(() => {
      this.getDetailData();
      this.listenGroupSocket();
    }, 0);
  }

  getDetailData(): void {
    this._postService.sendGet('devices/' + this._deviceId).subscribe(data => {
      this.isCellular = data.product === DeviceProducts.OnCell;
      const isCellularModuleEnabled = data.profile.cellular?.module === 1;
      const cellularModuleStatus = isCellularModuleEnabled
        ? this._translate.instant('general.common.enabled')
        : this._translate.instant('general.common.disabled');

      /** Device is cellular product and module is enabled */
      const isCellularEnabled = this.isCellular && isCellularModuleEnabled;
      const isSignalExist = data.profile.cellular?.quality !== this.noData;

      this.devDetailData = {
        /* Basic information */
        status: data.status,
        panelPath: 'assets/img/panel-img/' + this._getPanelImgFile(data.modelName),
        mac: data.mac,
        deviceName: data.hostname,
        productModel: data.modelName,
        location: data.location,
        serialNumber: data.serialNumber,
        firmware: data.firmwareVersion,
        lanIp: data.profile.general.lan,
        wanIp: data.ip ? data.ip : this.noData,
        oobIp: data.profile.general.oob,
        systemUptime: this._getSystemUptime(data.profile.general.uptime),
        /* Cellular information (Follow OnCell page) */
        cellularModule: this.isCellular ? cellularModuleStatus : this.noData,
        simCard: isCellularEnabled ? data.profile.cellular.profile[0].name : this.noData,
        cellularCarrier: isCellularEnabled ? data.profile.cellular.cellularCarrier.toUpperCase() : this.noData,
        signal:
          isCellularEnabled && isSignalExist
            ? this._translate.instant('pages.mapView.' + data.profile.cellular.quality)
            : this.noData,
        cellularMode: isCellularEnabled ? data.profile.cellular.cellularMode.toUpperCase() : this.noData,
        cellularBand: isCellularEnabled ? data.profile.cellular.band.toUpperCase() : this.noData,
        phoneNumber: isCellularEnabled ? data.profile.cellular.profile[0].phoneNumber : this.noData,
        imsi: isCellularEnabled ? data.profile.cellular.profile[0].imsi : this.noData,
        imei: isCellularEnabled ? data.profile.cellular.imei : this.noData
      };
      this.locationForm.get('location').setValue(data.location);
    });
  }

  onEditLocationClick(): void {
    this.isLocationEditMode = true;
  }

  applyLocation(): void {
    this.isLocationEditMode = false;
    this.locationLoading = true;

    const locateData = {
      location: this.locationForm.get('location').value
    };
    this._postService.sendPatch(`devices/${this._deviceId}/location`, locateData, true).subscribe(() => {
      this._locationTimer(this._locationCountDownSeconds);
    });
  }

  cancelLocation(): void {
    this.locationForm.get('location').setValue(this.devDetailData.location);
    this.isLocationEditMode = false;
  }

  listenGroupSocket(): void {
    this._socketService
      .onMessage('group')
      .pipe(takeUntil(this._notifyDestroy))
      .subscribe(data => {
        if (!this.devDetailData || this._deviceId !== data.deviceId) {
          return;
        }
        switch (data.type) {
          case SocketResponseType.Online:
          case SocketResponseType.Offline:
            this.devDetailData.status = data.type;
            break;
          case SocketResponseType.Location:
            this.devDetailData.location = data.payload.location;
            this.locationLoading = false;
            clearInterval(this.locationCountDown);
            this._snackBar.openSuccessSnackBar('pages.mapView.locationSuccess');
            break;
        }
      });
  }

  getTooltipContent(): string {
    return this.isDeviceOffline() ? 'pages.mapView.deviceOfflineLocation' : 'pages.mapView.editLocation';
  }

  isDeviceOffline(): boolean {
    return this.devDetailData?.status === DeviceStatus.Offline;
  }

  showEditLocation(): boolean {
    return RoleId[this._authService.user.getValue().role.name] !== RoleId.Viewer;
  }

  ngOnDestroy(): void {
    this._notifyDestroy.next();
    this._notifyDestroy.complete();
    this._socketService.removeListener('group');

    this.locationLoading = false;
    clearInterval(this.locationCountDown);
  }

  private _getPanelImgFile(modelName: string): string {
    let model = modelName.toLowerCase().split('-').slice(0, 2);
    if (this.isCellular) {
      model = model[0].split(' ');
    }
    return model.join('_') + '_panel.webp'; // ex: edr_g9010_panel.png
  }

  private _getSystemUptime(start: string): string {
    const startTime = DateTime.fromISO(start);
    if (!startTime.isValid) {
      return start;
    }

    const endTime = DateTime.now();
    const duration = Interval.fromDateTimes(startTime, endTime).toDuration(['days', 'hours', 'minutes', 'seconds']);

    return `${duration.days}d${duration.hours}h${duration.minutes}m${Math.round(duration.seconds)}s`;
  }

  /** count down 30 sec. to wait web socket */
  private _locationTimer(countDownTime: number): void {
    clearInterval(this.locationCountDown);
    let locationTimer = countDownTime;

    this.locationCountDown = setInterval(() => {
      locationTimer--;
      if (locationTimer === 0) {
        this.locationLoading = false;
        clearInterval(this.locationCountDown);
        this._snackBar.openErrorSnackBar('pages.mapView.locationFail');
      }
    }, 1000);
  }
}
