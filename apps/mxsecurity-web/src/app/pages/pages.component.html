<mat-toolbar id="header-toolbar" color="primary">
  <button (click)="openMenu()" mat-icon-button>
    <mat-icon>menu</mat-icon>
  </button>
  <div [routerLink]="['/pages/dashboard']">
    <img alt="MXsecurity Logo" src="assets/img/logo_mxsecurity_w.webp" height="30" />
  </div>
  <span style="flex: 1 1 auto"></span>
  <div id="header-role">{{ usernameAndRole }}</div>
  <div id="header-border"></div>
  <button [matMenuTriggerFor]="profileMenu" mat-icon-button>
    <mat-icon>more_vert</mat-icon>
  </button>
</mat-toolbar>

<mat-sidenav-container>
  <mat-sidenav class="app-sidenav" #sidenav [opened]="isNavOpened" mode="side">
    <div class="jump-block" fxLayout="row" fxLayoutAlign="center">
      <mat-form-field class="form-field-search search">
        <mat-icon class="icon-search" matPrefix>search</mat-icon>
        <input #search (keyup)="filterTree($event)" matInput placeholder="{{ 'general.table.search' | translate }}" />
      </mat-form-field>
      <mat-icon *ngIf="!isNavOpened" (click)="focusSearch()" fxFlexAlign="center">search</mat-icon>
    </div>
    <div class="menu-block">
      <mat-tree class="menu-tree" [dataSource]="treeDataSource" [treeControl]="treeControl">
        <mat-tree-node
          class="menu-node"
          #nodeRouterLink="routerLinkActive"
          *matTreeNodeDef="let node"
          [matTreeNodePaddingIndent]="20"
          [ngClass]="getNodeLevel(node)"
          (click)="treeControl.collapseAll()"
          matTreeNodePadding
          routerLink="{{ node.href }}"
          routerLinkActive="node-active"
        >
          <div fxLayoutAlign="start center">
            <div class="menu-line"></div>
            <mat-icon
              class="node-icon"
              [ngClass]="{ 'icon-color-white': !nodeRouterLink.isActive, 'icon-focused': nodeRouterLink.isActive }"
              [color]="nodeRouterLink.isActive ? 'primary' : ''"
              [matTooltip]="(node.matIcon && ('menu.' + node.key | translate)) || ''"
              (click)="clickIcon()"
            >
              {{ node.matIcon }}
            </mat-icon>
            <ng-container *ngIf="isNavOpened"> {{ 'menu.' + node.key | translate }} </ng-container>
          </div>
        </mat-tree-node>

        <mat-tree-node
          class="menu-node"
          *matTreeNodeDef="let node; when: hasChild"
          [matTreeNodePaddingIndent]="20"
          [ngClass]="getNodeLevel(node)"
          [ngClass]="addParentActiveClass(node)"
          (click)="toggleMenu(node)"
          matTreeNodePadding
        >
          <div fxLayoutAlign="start center">
            <div class="menu-line"></div>
            <mat-icon
              class="node-icon icon-color-white"
              (click)="clickIcon()"
              matTooltip="{{ 'menu.' + node.key | translate }}"
            >
              {{ node.matIcon }}
            </mat-icon>
            <ng-container *ngIf="isNavOpened"> {{ 'menu.' + node.key | translate }} </ng-container>
          </div>
          <mat-icon class="mat-icon-rtl-mirror menu-icon">
            {{ treeControl.isExpanded(node) ? 'expand_less' : 'expand_more' }}
          </mat-icon>
        </mat-tree-node>
      </mat-tree>
    </div>
  </mat-sidenav>
  <mat-sidenav-content id="app-sidenav-content">
    <div
      [ngClass]="{ 'menu-activate': isNavOpened === true, 'app-content-margin': isNavOpened !== true }"
      fxLayout="row"
    >
      <div class="app-content-container">
        <div fxLayout="row" fxLayoutAlign="space-between none">
          <div class="page-title">{{ pageTitle }}</div>
        </div>
        <div id="page-content">
          <router-outlet></router-outlet>
        </div>
      </div>
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>

<mat-menu #profileMenu="matMenu" [overlapTrigger]="false">
  <div>
    <button *ngIf="isMainLicenseValid" (click)="changePassword()" mat-menu-item>
      <mat-icon svgIcon="changePwd"></mat-icon>
      <span>{{ 'general.topNav.changePwd' | translate }}</span>
    </button>
    <button (click)="openTroubleshooting()" mat-menu-item>
      <mat-icon class="material-icons">troubleshoot</mat-icon>
      <span>{{ 'general.topNav.troubleshoot' | translate }}</span>
    </button>
    <button (click)="logout()" mat-menu-item>
      <mat-icon>logout</mat-icon>
      <span>{{ 'general.topNav.logout' | translate }}</span>
    </button>
  </div>
</mat-menu>
