// Variables
$color-blue: #29b6f6;
$color-green: #66bb6a;
$color-text: #888;
$color-highlight: #00897b;
$spacing-base: 0.5rem;
$card-padding: $spacing-base * 2;
$border-width: 10px;
$shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

.dashboard-cards {
  display: flex;
  justify-content: space-between;
  gap: 2rem;
  height: 80%;
}

.card {
  display: flex;
  flex: 1;
  padding: $card-padding;
  box-shadow: $shadow;
  cursor: pointer;
  border-bottom-width: $border-width;
  border-bottom-style: solid;
  overflow: auto;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  &--blue {
    border-bottom-color: $color-blue;
  }

  &--green {
    border-bottom-color: $color-green;
  }

  &__content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    justify-content: space-between;
  }

  &__main {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-grow: 2;
  }

  &__count {
    font-size: 2rem;
    font-weight: 700;
    color: $color-highlight;
    margin-right: $spacing-base;
  }
  &__label {
    font-size: 1rem;
    color: $color-text;
    margin-top: 0.5rem;
  }

  &__footer {
    font-size: 0.9rem;
    color: $color-text;
    text-align: center;
    flex-grow: 1;
    display: flex;
    align-items: end;
    justify-content: center;
  }
}
