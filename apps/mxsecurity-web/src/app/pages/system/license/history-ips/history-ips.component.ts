import { Component, computed, effect, EventEmitter, inject, Input, Output, signal, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatLegacyPaginator as MatPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';

import { filter } from 'rxjs';

import { SharedModule } from '@mxsecurity/app/shared/shared.module';
import { SimpleDialogService } from '@mxsecurity/app/shared/simple-dialog/simple-dialog.service';

import { DeactivateLicenseDialogComponent } from '../deactivate-license-dialog/deactivate-license-dialog.component';
import { disableDeactivateButtonStatus } from '../license.helper';
import { LicenseList } from '../license.model';
import { ReactivateIpsLicensesDialogComponent } from '../reactivate-ips-licenses-dialog/reactivate-ips-licenses-dialog.component';

@Component({
  selector: 'app-history-ips',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './history-ips.component.html',
  styleUrls: ['./history-ips.component.scss']
})
export class HistoryIpsComponent {
  @Input() serverId: string;
  @Input() isMainLicenseValid: boolean;
  @Input() set overViewData(value: LicenseList) {
    this.overViewDataSignal.set(value);
  }
  readonly overViewDataSignal = signal(null);
  @Input() set historyData(value: LicenseList[]) {
    this.dataSource.data = value || [];
  }

  @Output() refreshLicense = new EventEmitter();

  @ViewChild(MatSort) set matSort(sort: MatSort) {
    if (sort) {
      this.dataSource.sort = sort;
    }
  }
  @ViewChild(MatPaginator) set matPaginator(paginator: MatPaginator) {
    if (paginator) {
      this.dataSource.paginator = paginator;
    }
  }

  readonly #simpleDialogService = inject(SimpleDialogService);

  readonly isCustomizedLicense = computed(() => {
    const overView = this.overViewDataSignal();
    return overView?.isCustomizedLicense;
  });

  readonly disableDeactivateButtonStatus = computed(() => {
    const overView = this.overViewDataSignal();
    return disableDeactivateButtonStatus(overView);
  });

  readonly disableDeactivateButton = computed(() => {
    const status = this.disableDeactivateButtonStatus();
    return status?.depleted || status?.deactivated || status?.noLicense;
  });

  readonly deactivateButtonTooltip = computed(() => {
    const status = this.disableDeactivateButtonStatus();

    if (status?.noLicense) {
      return 'pages.system.license.noIpsLicense';
    }
    if (status?.depleted) {
      return 'pages.system.license.noIpsLicensePoints';
    }
    if (status?.deactivated) {
      return 'pages.system.license.ipsLicenseHaveBeenDeactivated';
    }
    return 'pages.system.license.deactivateLicense';
  });

  displayedColumns = computed(() => {
    const isCustomizedLicense = this.isCustomizedLicense();
    const columns = isCustomizedLicense
      ? ['createTime', 'activationCode', 'type', 'duration', 'licenseNode']
      : ['createTime', 'activationCode', 'type', 'licenseNode'];

    return columns;
  });
  dataSource = new MatTableDataSource<LicenseList>();

  search = new FormControl('');
  searchValue = toSignal(this.search.valueChanges, {
    initialValue: ''
  });

  constructor() {
    effect(() => {
      const searchValue = this.searchValue();
      this.dataSource.filter = searchValue.trim().toLowerCase();
    });
  }

  refresh(): void {
    this.refreshLicense.emit('refresh');
  }

  deactivate(): void {
    this.#simpleDialogService.openMDialog({
      component: DeactivateLicenseDialogComponent
    });

    this.#simpleDialogService
      .rightBtnAction()
      .pipe(filter(data => !!data))
      .subscribe(element => {
        this.#simpleDialogService.openLDialog({
          component: ReactivateIpsLicensesDialogComponent,
          stepper: true,
          data: { serverId: this.serverId, element }
        });
      });
  }
}
