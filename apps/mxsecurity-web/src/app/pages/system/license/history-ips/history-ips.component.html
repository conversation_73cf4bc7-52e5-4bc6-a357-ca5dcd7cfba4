<mat-card>
  <mat-card-header>
    <mat-card-title>{{ 'pages.system.license.history' | translate }}</mat-card-title>
  </mat-card-header>
  <mat-card-content>
    <div class="table-tool-bar" fxLayout="row" fxLayoutAlign="space-between center">
      <div *ngIf="isMainLicenseValid; else emptyDiv" fxLayout="row" fxLayoutAlign="start center">
        <span matTooltip="{{ deactivateButtonTooltip() | translate }}">
          <button
            *ngIf="!isCustomizedLicense()"
            [disabled]="disableDeactivateButton()"
            [style.opacity]="disableDeactivateButton() ? 0.5 : 1"
            (click)="deactivate()"
            mat-icon-button
          >
            <mat-icon svgIcon="deactivate"></mat-icon>
          </button>
        </span>
        <button (click)="refresh()" mat-icon-button matTooltip="{{ 'general.table.refresh' | translate }}">
          <mat-icon>refresh</mat-icon>
        </button>
      </div>
      <mat-form-field fxFlex="25">
        <mat-icon matPrefix>search</mat-icon>
        <input [formControl]="search" matInput placeholder="{{ 'general.table.search' | translate }}" />
      </mat-form-field>
    </div>
    <table [dataSource]="dataSource" mat-table matSort matSortActive="createTime" matSortDirection="desc">
      <ng-container matColumnDef="createTime">
        <th *matHeaderCellDef mat-header-cell mat-sort-header>{{ 'pages.system.license.createTime' | translate }}</th>
        <td *matCellDef="let element" mat-cell>{{ element.createTime }}</td>
      </ng-container>

      <ng-container matColumnDef="activationCode">
        <th *matHeaderCellDef mat-header-cell mat-sort-header>
          {{ 'pages.system.license.activationCode' | translate }}
        </th>
        <td *matCellDef="let element" mat-cell>{{ element.activationCode }}</td>
      </ng-container>

      <ng-container matColumnDef="duration">
        <th *matHeaderCellDef mat-header-cell mat-sort-header>
          {{ 'pages.system.license.licenseDuration' | translate }} ({{ 'pages.system.license.days' | translate }})
        </th>
        <td *matCellDef="let element" mat-cell>
          {{ element.duration }}
        </td>
      </ng-container>

      <ng-container matColumnDef="type">
        <th *matHeaderCellDef mat-header-cell mat-sort-header>{{ 'pages.system.license.licenseType' | translate }}</th>
        <td *matCellDef="let element" mat-cell>{{ element.type }}</td>
      </ng-container>

      <ng-container matColumnDef="licenseNode">
        <th *matHeaderCellDef mat-header-cell mat-sort-header>
          {{ 'pages.system.license.licensePoints' | translate }}
        </th>
        <td *matCellDef="let element" mat-cell>
          {{ isCustomizedLicense() ? ('pages.system.license.unlimited' | translate) : element.licenseNode }}
        </td>
      </ng-container>

      <tr *matHeaderRowDef="displayedColumns()" mat-header-row></tr>
      <tr *matRowDef="let row; columns: displayedColumns()" mat-row></tr>
    </table>
    <mat-paginator [pageSizeOptions]="[10, 20, 50]" showFirstLastButtons></mat-paginator>
  </mat-card-content>
</mat-card>

<ng-template #emptyDiv>
  <div></div>
</ng-template>
