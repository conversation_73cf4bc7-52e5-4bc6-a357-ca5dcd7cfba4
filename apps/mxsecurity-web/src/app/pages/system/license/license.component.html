<mat-tab-group [selectedIndex]="selectedTab">
  <mat-tab label="{{ 'pages.system.license.nsm' | translate }}">
    <app-overview
      [overViewData]="overviewData.get(licenseCategory.NSM)"
      [serverId]="serverId"
      (reloadLicense)="refreshLicense($event)"
    ></app-overview>
    <app-history
      [historyData]="historyData.get(licenseCategory.NSM)"
      [isMainLicenseValid]="isMainLicenseValid"
      (refreshLicense)="refreshLicense($event)"
    ></app-history>
  </mat-tab>

  <mat-tab label="{{ 'pages.system.license.ips' | translate }}">
    <app-overview-ips
      [overViewData]="overviewData.get(licenseCategory.IPS)"
      [serverId]="serverId"
      [notification]="notification"
      (reloadLicense)="refreshLicense($event)"
    ></app-overview-ips>
    <app-history-ips
      [serverId]="serverId"
      [overViewData]="overviewData.get(licenseCategory.IPS)"
      [historyData]="historyData.get(licenseCategory.IPS)"
      [isMainLicenseValid]="isMainLicenseValid"
      (refreshLicense)="refreshLicense($event)"
    ></app-history-ips>
    <app-deactivated-licenses [deactivation]="deactivation" [serverId]="serverId" (refresh)="refreshLicense($event)">
    </app-deactivated-licenses>
    <app-device-license
      *ngIf="!isNsmRebind"
      [isMainLicenseValidInput]="isMainLicenseValid"
      [overViewData]="overviewData.get(licenseCategory.IPS)"
      (refreshLicense)="refreshLicense($event)"
    ></app-device-license>
  </mat-tab>

  <mat-tab label="{{ 'pages.system.license.licenseMigrate' | translate }}" *ngIf="showLicenseMigrate()">
    <app-license-transfer [serverId]="serverId" (refreshLicense)="refreshLicense($event)"> </app-license-transfer>
  </mat-tab>
</mat-tab-group>
