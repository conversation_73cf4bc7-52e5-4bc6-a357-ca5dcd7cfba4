import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  effect,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  signal,
  ViewChild
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { SelectionModel } from '@angular/cdk/collections';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatLegacyPaginator as MatPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';

import { TranslateService } from '@ngx-translate/core';
import { Subject } from 'rxjs';
import { filter, switchMap, takeUntil } from 'rxjs/operators';

import { AuthService } from '@mxsecurity/app/shared/auth/auth.service';
import { DeviceStatus } from '@mxsecurity/pages/device-deployment/device-deployment.model';
import { PostService } from '@mxsecurity/shared/http/post.service';
import { SnackBarService } from '@mxsecurity/shared/service/snack-bar.service';
import { SharedService } from '@mxsecurity/shared/shared.service';
import { SimpleDialogService } from '@mxsecurity/shared/simple-dialog/simple-dialog.service';
import { SocketResponseType } from '@mxsecurity/shared/socket/socket.model';
import { SocketService } from '@mxsecurity/shared/socket/socket.service';

import { DeviceLicenseRegisterDialogComponent } from '../device-license-register-dialog/device-license-register-dialog.component';
import { validIpsDevice } from '../license.helper';
import {
  DeviceLicenseUnit,
  LicenseAction,
  LicenseCategory,
  LicenseDevices,
  LicenseList,
  LicenseStatus
} from '../license.model';
import { LicenseService } from '../license.service';

@Component({
  selector: 'app-device-license',
  templateUrl: './device-license.component.html',
  styleUrls: ['./device-license.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DeviceLicenseComponent implements OnInit, OnDestroy {
  @Input() set isMainLicenseValidInput(nsmValue: boolean) {
    this.displayedColumns = nsmValue ? this._oriDisplayedColumns : this._oriDisplayedColumns.slice(1);
    this.isMainLicenseValid.set(nsmValue);
  }
  readonly isMainLicenseValid = signal(false);

  @Input() set overViewData(value: LicenseList) {
    this.overViewDataSignal.set(value);
  }
  readonly overViewDataSignal = signal(null);

  @Output() refreshLicense = new EventEmitter();

  @ViewChild(MatSort) set sort(sort: MatSort) {
    this.dataSource.sort = sort;
  }
  @ViewChild(MatPaginator) set paginator(paginator: MatPaginator) {
    this.dataSource.paginator = paginator;
  }

  displayedColumns: string[] = [
    'select',
    'deviceName',
    'status',
    'location',
    'productModel',
    'serialNumber',
    'mac',
    'firmwareVersion',
    'group',
    'licenseStatus',
    'licenseStatusInfo'
  ];
  dataSource = new MatTableDataSource<LicenseDevices>();
  selection = new SelectionModel<LicenseDevices>(true, []);
  showAllRows = true;

  private _allDataSource: LicenseDevices[];
  private _notifyDestroy = new Subject<void>();
  private _oriDisplayedColumns = this.displayedColumns;

  firmwareVersion = this._auth.SystemInfo?.firmwareVersion ?? '';
  serverVersion = this._auth.SystemInfo?.serverVersion.split('-')[0] ?? '';

  search = new FormControl('');
  searchValue = toSignal(this.search.valueChanges, {
    initialValue: ''
  });

  readonly #v2Version = '3.13.0';

  constructor(
    private _cdr: ChangeDetectorRef,
    private _postService: PostService,
    private _sharedService: SharedService,
    private _simpleDialogService: SimpleDialogService,
    private _socketService: SocketService,
    private _snackBar: SnackBarService,
    private _translateService: TranslateService,
    private _auth: AuthService,
    private _licenseService: LicenseService
  ) {
    effect(() => {
      const searchValue = this.searchValue();
      this.dataSource.filter = searchValue.trim().toLowerCase();
    });
  }

  ngOnInit(): void {
    this.getAllDevices();
    this.listenSocket();
    this.dataSource.filterPredicate = (data, filter: string): boolean =>
      this._sharedService.setFilterPredicate(this._oriDisplayedColumns.slice(1), data, filter);
  }

  getAllDevices(): void {
    this._postService.sendGet('devices').subscribe(result => {
      this.selection.clear();
      this.dataSource.data = result.map(data => {
        return {
          canApply: data.canApply,
          firewallCap: data.firewallCap,
          firewallStatus: data.firewallStatus,
          deviceId: data.deviceId,
          deviceName: data.hostname,
          status: data.status,
          location: data.location,
          productModel: data.modelName,
          serialNumber: data.serialNumber,
          mac: data.mac,
          firmwareVersion: data.firmwareVersion,
          group: data.groupName,
          isIPS_DEVICE: !!data.profile?.license?.find(item => item.category === 'IPS-DEVICE'),
          licenseStatus: this.#parseLicenseStatus(data.profile?.license),
          isValidIPSDevice: this.#parseLicenseStatus(data.profile?.license) === validIpsDevice
        };
      });
      this._allDataSource = this.dataSource.data;
    });
  }

  listenSocket(): void {
    this._socketService
      .getMessage('group')
      .pipe(takeUntil(this._notifyDestroy))
      .subscribe(data => {
        const index = this.dataSource.data.findIndex(item => item.deviceId === data.deviceId);
        if (index === -1) {
          return;
        }
        switch (data.type) {
          case SocketResponseType.Online:
            this.dataSource.data[index].status = DeviceStatus.Online;
            break;
          case SocketResponseType.Offline:
            this.dataSource.data[index].status = DeviceStatus.Offline;
            break;
        }
        this._cdr.markForCheck();
      });
  }

  setDeviceLicense(): void {
    this._simpleDialogService.openMDialog({
      component: DeviceLicenseRegisterDialogComponent,
      data: {
        selection: this.selection.selected
      }
    });

    this._simpleDialogService
      .rightBtnAction()
      .pipe(
        filter(data => data),
        switchMap(() => {
          return this._postService.sendPatch('licenses/bind', {
            deviceIdList: this.selection.selected.map(item => item.deviceId),
            category: LicenseCategory.IPS,
            action: LicenseAction.Activate
          });
        }),
        takeUntil(this._notifyDestroy)
      )
      .subscribe(result => {
        this.reloadLicense(result, 'setLicenseSuccess');
      });
  }

  removeDeviceLicense(): void {
    this._simpleDialogService.openSDialog({
      title: 'pages.system.license.removeDeviceLicense',
      content: 'pages.system.license.removeLicenseMsg',
      selectedNum: this.selection.selected.length,
      rightBtnText: 'general.button.unregister'
    });

    this._simpleDialogService
      .rightBtnAction()
      .pipe(
        filter(data => data),
        switchMap(() => {
          return this._postService.sendPatch('licenses/bind', {
            deviceIdList: this.selection.selected.map(data => data.deviceId),
            category: LicenseCategory.IPS,
            action: LicenseAction.Deactivate
          });
        }),
        takeUntil(this._notifyDestroy)
      )
      .subscribe(result => {
        this.reloadLicense(result, 'removeLicenseSuccess');
      });
  }

  reloadLicense(result, msg: string): void {
    this.selection.clear();
    this.refreshLicense.emit('refresh');
    this._snackBar.openSuccessSnackBar(`pages.system.license.${msg}`);
    result.forEach(res => {
      const index = this.dataSource.data.findIndex(item => item.deviceId === res.deviceId);
      if (index === -1) {
        return;
      }
      this.dataSource.data[index].licenseStatus = this.#parseLicenseStatus(res.payload.license);
    });
    this._cdr.markForCheck();
  }

  isSetDeviceLicenseDisabled(): boolean {
    const overViewStatus = this.overViewDataSignal()?.status;
    if (overViewStatus !== LicenseStatus.Valid) {
      return true;
    }

    return this.selection.selected.some(data => data.licenseStatus === LicenseStatus.Valid);
  }

  isRemoveDeviceLicenseDisabled(): boolean {
    return this.selection.selected.some(data => data.licenseStatus !== LicenseStatus.Valid);
  }

  isAllOnPageSelected(): boolean {
    let numSelected = 0;
    this.dataSource.connect().value.forEach(data => {
      if (this.selection.isSelected(data)) {
        numSelected += 1;
      }
    });

    const numRows = this.#filterDisableData().length;
    return numSelected === numRows && numRows > 0;
  }

  selectOnPage(): void {
    const filteredData = this.#filterDisableData();
    if (this.isAllOnPageSelected()) {
      this.selection.deselect(...filteredData);
    } else {
      this.selection.select(...filteredData);
    }
  }

  isSelectOnPageDisabled(): boolean {
    return this.#filterDisableData().length === 0;
  }

  isSelectOnPageIndeterminate(): boolean {
    return this.dataSource.connect().value.some(data => {
      return this.selection.isSelected(data) && !this.isAllOnPageSelected();
    });
  }

  masterToggle(data: boolean): void {
    if (!data) {
      this.selection.clear();
    } else {
      this.selection.select(
        ...this.dataSource.data.filter(
          data => data.status === DeviceStatus.Online && data.canApply && !this.licenseCheckboxDisable(data)
        )
      );
    }
  }

  getAllNum(): number {
    return this.dataSource.data.filter(
      data => data.status === DeviceStatus.Online && data.canApply && !this.licenseCheckboxDisable(data)
    ).length;
  }

  toggleShowAllRows(): void {
    this.showAllRows = !this.showAllRows;
    if (this.showAllRows) {
      this.dataSource.data = this._allDataSource;
    } else {
      this.dataSource.data = this.selection.selected;
    }
  }

  getSetLicenseTooltip(): string {
    if (this.overViewDataSignal()?.status !== LicenseStatus.Valid) {
      return this._translateService.instant('pages.system.license.noIpsLicense');
    }

    if (this.selection.selected.some(data => data.licenseStatus === LicenseStatus.Valid)) {
      return this._translateService.instant('pages.system.license.setLicenseDisabled', {
        category: LicenseCategory.IPS
      });
    } else {
      return this._translateService.instant('pages.system.license.setDeviceLicense');
    }
  }

  getRemoveLicenseTooltip(): string {
    if (this.selection.selected.some(data => data.licenseStatus !== LicenseStatus.Valid)) {
      return this._translateService.instant('pages.system.license.removeLicenseDisabled', {
        category: LicenseCategory.IPS
      });
    } else {
      return this._translateService.instant('pages.system.license.removeDeviceLicense');
    }
  }

  //# region License Tooltip
  licenseCheckboxDisable(row): boolean {
    const { canApply, firewallStatus, firewallCap, firmwareVersion, isIPS_DEVICE, status } = row;

    if (isIPS_DEVICE && status === DeviceStatus.Offline) {
      return true;
    }

    if (!canApply && !firewallStatus && !firewallCap) {
      return true;
    }

    if (!canApply && !firewallStatus && firewallCap) {
      return true;
    }

    if (!canApply && firewallStatus && firewallCap) {
      return true;
    }

    const ipsVersion = this._licenseService.getIpsVersion();
    if (ipsVersion === 1) {
      return true;
    }
    if (ipsVersion === 2) {
      const isVersionBelowV2 = this.checkVersionIsLessThanEqual('3.13.0', firmwareVersion);
      const isNewLicenseValid = this.checkVersionIsLessThan(firmwareVersion, this.firmwareVersion);
      if (!(isVersionBelowV2 && isNewLicenseValid)) {
        return true;
      }
    }
    if (ipsVersion === 3) {
      const newLicenseCompare = this.checkVersionIsLessThan(firmwareVersion, this.firmwareVersion);
      if (newLicenseCompare) {
        return true;
      }
    }

    return false;
  }

  licenseTooltipDisable(row): boolean {
    const { canApply, firewallStatus, firewallCap, firmwareVersion, isIPS_DEVICE, status } = row;

    if (isIPS_DEVICE && status === DeviceStatus.Offline) {
      return false;
    }

    if (!canApply && !firewallStatus && !firewallCap) {
      return false;
    }

    if (!canApply && !firewallStatus && firewallCap) {
      return false;
    }

    if (!canApply && firewallStatus && firewallCap) {
      return false;
    }

    const ipsVersion = this._licenseService.getIpsVersion();

    if (ipsVersion === 1) {
      return false;
    }

    if (ipsVersion === 2) {
      const isVersionBelowV2 = this.checkVersionIsLessThanEqual(this.#v2Version, firmwareVersion);
      const isNewLicenseValid = this.checkVersionIsLessThan(firmwareVersion, this.firmwareVersion);
      if (!(isVersionBelowV2 && isNewLicenseValid)) {
        return false;
      }
    }

    if (ipsVersion === 3) {
      const newLicenseCompare = this.checkVersionIsLessThan(firmwareVersion, this.firmwareVersion);
      if (newLicenseCompare) {
        return false;
      }
    }

    return true;
  }

  licenseTooltip(row): string {
    const { canApply, firewallStatus, firewallCap, firmwareVersion, isIPS_DEVICE, status } = row;

    if (isIPS_DEVICE && status === DeviceStatus.Offline) {
      return this._translateService.instant('pages.system.license.deviceIsOffline');
    }

    if (!canApply && !firewallStatus && !firewallCap) {
      return this._translateService.instant('pages.system.license.notSupportModel');
    }

    if (!canApply && !firewallStatus && firewallCap) {
      return this._translateService.instant('pages.system.license.enableNetworkSecurity');
    }

    if (!canApply && firewallStatus && firewallCap) {
      return this._translateService.instant('pages.system.license.notSupportIPS');
    }
    const ipsVersion = this._licenseService.getIpsVersion();

    if (ipsVersion === 1) {
      return this._translateService.instant('pages.system.license.oldVersionNotSupport');
    }

    if (ipsVersion === 2) {
      const isVersionBelowV2 = this.checkVersionIsLessThanEqual('3.13.0', firmwareVersion);
      const isNewLicenseValid = this.checkVersionIsLessThan(firmwareVersion, this.firmwareVersion);
      if (!(isVersionBelowV2 && isNewLicenseValid)) {
        return this._translateService.instant('pages.system.license.oldVersionNotSupport');
      }
    }

    if (ipsVersion === 3) {
      const newLicenseCompare = this.checkVersionIsLessThan(firmwareVersion, this.firmwareVersion);
      if (newLicenseCompare) {
        return this._translateService.instant('pages.system.license.newVersionNotSupport', {
          version: this.firmwareVersion
        });
      }
    }
  }
  //# endregion

  /**
   * 比較兩個版本
   * @param version1 版本1
   * @param version2 版本2
   * @returns -1 表示 version1 小於 version2, 0 表示相等, 1 表示 version1 大於 version2
   */
  compareVersions(version1: string, version2: string): number {
    const cleanVersion = (version: string) => (version.startsWith('V') ? version.slice(1) : version);

    const version1Parts = cleanVersion(version1).split('.').map(Number);
    const version2Parts = cleanVersion(version2).split('.').map(Number);

    const maxLength = Math.max(version1Parts.length, version2Parts.length);

    for (let i = 0; i < maxLength; i++) {
      const part1 = version1Parts[i] || 0;
      const part2 = version2Parts[i] || 0;

      if (part1 < part2) return -1;
      if (part1 > part2) return 1;
    }

    return 0;
  }

  /**
   * 檢查版本是否小於等於
   * @param version1 版本1
   * @param version2 版本2
   * @returns 是否小於等於
   */
  checkVersionIsLessThanEqual(version1: string, version2: string): boolean {
    return this.compareVersions(version1, version2) <= 0;
  }

  /**
   * 檢查版本是否小於
   * @param version1 版本1
   * @param version2 版本2
   * @returns 是否小於
   */
  checkVersionIsLessThan(version1: string, version2: string): boolean {
    return this.compareVersions(version1, version2) < 0;
  }

  #filterDisableData() {
    const filterCanApply = this.dataSource.connect().value.filter(data => data.canApply);

    const filterFirmwareVersion = filterCanApply.filter(data => {
      const ipsVersion = this._licenseService.getIpsVersion();
      if (ipsVersion === 1) {
        return false;
      }

      if (ipsVersion === 2) {
        const isVersionBelowV2 = this.checkVersionIsLessThanEqual(this.#v2Version, data.firmwareVersion);
        const isNewLicenseValid = this.checkVersionIsLessThan(data.firmwareVersion, this.firmwareVersion);
        if (!(isVersionBelowV2 && isNewLicenseValid)) {
          return false;
        }
      }

      if (ipsVersion === 3) {
        const newLicenseCompare = this.checkVersionIsLessThan(data.firmwareVersion, this.firmwareVersion);
        if (newLicenseCompare) {
          return false;
        }
      }

      return true;
    });

    return filterFirmwareVersion;
  }

  #parseLicenseStatus(licenses: DeviceLicenseUnit[]): string {
    if (!licenses) {
      return LicenseStatus.NoLicense;
    }
    const license = licenses.find(item => item.category.startsWith(LicenseCategory.IPS));
    if (!license) {
      return LicenseStatus.NoLicense;
    }
    if (license.category === LicenseCategory.IPS_MGMT && license.endTime === 0) {
      return LicenseStatus.NoLicense;
    }
    return license.category === LicenseCategory.IPS_MGMT ? license.status : `${license.status} (${license.category})`;
  }

  ngOnDestroy(): void {
    this._notifyDestroy.next();
    this._notifyDestroy.complete();
    this._socketService.removeListener('group');
  }
}
