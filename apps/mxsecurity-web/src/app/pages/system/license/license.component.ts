import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  <PERSON><PERSON><PERSON>roy,
  OnInit,
  signal,
  WritableSignal
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { TranslateService } from '@ngx-translate/core';
import { forkJoin } from 'rxjs';

import { SystemInfo } from '@mxsecurity/app/shared/shared.model';
import { SocketService } from '@mxsecurity/app/shared/socket/socket.service';
import { AuthService } from '@mxsecurity/shared/auth/auth.service';
import { PostService } from '@mxsecurity/shared/http/post.service';
import { SharedService } from '@mxsecurity/shared/shared.service';
import { SimpleDialogService } from '@mxsecurity/shared/simple-dialog/simple-dialog.service';

import { SystemSettingsTime } from '../settings/time/time.model';
import { TransferDeactivation } from './deactivated-licenses/deactivated-licenses.model';
import { LicenseNotification } from './license-notification-dialog/license-notification.model';
import { LicenseResponse, Overview } from './license-response.model';
import { getIPSStatus, getLicenseType } from './license.helper';
import { License, LicenseAction, LicenseCategory, LicenseList, LicenseStatus, LicenseType } from './license.model';
import { LicenseService } from './license.service';

@Component({
  selector: 'app-license',
  templateUrl: './license.component.html',
  styleUrls: ['./license.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LicenseComponent implements OnInit, OnDestroy {
  licenseCategory = LicenseCategory;
  licenseStatus = LicenseStatus;
  tabs = [LicenseCategory.NSM, LicenseCategory.IPS];
  selectedTab: number | null;
  historyData = new Map<string, LicenseList[]>();
  overviewData = new Map<string, LicenseList>();
  isMainLicenseValid: boolean;
  isNsmRebind: boolean;
  serverId: string;
  showLicenseMigrate: WritableSignal<boolean> = signal(false);
  notification: LicenseNotification;
  deactivation: TransferDeactivation[] = [];

  constructor(
    private _activatedRoute: ActivatedRoute,
    private _authService: AuthService,
    private _cdr: ChangeDetectorRef,
    private _postService: PostService,
    private _sharedService: SharedService,
    private _simpleDialogService: SimpleDialogService,
    private _translate: TranslateService,
    private _licenseService: LicenseService,
    private _socketService: SocketService
  ) {}

  ngOnInit(): void {
    if (this._activatedRoute.snapshot.queryParamMap.get('tab')) {
      this.selectedTab = this.tabs.findIndex(
        data => data === this._activatedRoute.snapshot.queryParamMap.get('tab').toUpperCase()
      );
    }
    if (this._activatedRoute.snapshot.queryParamMap.get('tabIndex')) {
      this.selectedTab = Number(this._activatedRoute.snapshot.queryParamMap.get('tabIndex'));
    }

    this.getAllLicense();
  }

  refreshLicense(data: string | any): void {
    if (data === 'refresh') {
      this.getAllLicense();
    } else {
      this.setLicenseData(data);
    }
  }

  private getAllLicense(): void {
    forkJoin({
      license: this._postService.sendGetHaveInterface<LicenseResponse>('licenses'),
      systemInfo: this._postService.sendGetHaveInterface<SystemSettingsTime>('system/time'),
      notification: this._postService.sendGetHaveInterface<LicenseNotification>('licenses/notification'),
      deactivation: this._postService.sendGetHaveInterface<TransferDeactivation[]>('licenses/transfer/deactivation')
    }).subscribe(({ license, systemInfo, notification, deactivation }) => {
      this.serverId = license.serverId;
      this._licenseService.setSystemInfo(systemInfo);
      this._licenseService.setLicenseData(license);
      this.notification = notification;
      this.deactivation = deactivation;
      this.setLicenseData(license);

      this.showLicenseMigrate.set(
        license.data
          .map(item => item.list)
          .reduce((acc, val) => acc.concat(val), [])
          .filter(item => item.version === 1 || item.version === 2)
          .filter(item => {
            const diffUTC = item.endTime - systemInfo.time;
            const diffDays = Math.floor(diffUTC / (60 * 60 * 24));
            return diffDays > 0;
          }).length > 0
      );

      this.updateSystemInfo();
    });
  }

  private setLicenseData(result: LicenseResponse): void {
    this.historyData.clear();
    this.overviewData.clear();

    const nsmItem = result.data.find(item => item.category === LicenseCategory.NSM);
    if (nsmItem) {
      const overview = nsmItem.overview;
      this._authService.nsmLicense = new License(LicenseCategory.NSM, overview.status, overview.action);
      this.isNsmRebind = this._authService.nsmLicense.action === LicenseAction.Rebind;
      this.isMainLicenseValid = overview.status === LicenseStatus.Valid;

      if (overview.action === LicenseAction.Rebind) {
        this._simpleDialogService.openSDialog({
          title: 'general.dialog.warning',
          content: this._translate.instant('pages.system.license.nsmNodeErrorMsg', {
            nodes: overview.node,
            devices: overview.usedNode
          }),
          showLeftBtn: false
        });
      }

      this.overviewData.set(nsmItem.category, this.getListData(overview));
      this.historyData.set(
        nsmItem.category,
        nsmItem.list.map(data => this.getListData(data))
      );
    }

    const ipsItem = result.data.find(item => item.category === LicenseCategory.IPS);
    if (ipsItem) {
      const overview = ipsItem.overview;
      this._authService.ipsLicense = new License(LicenseCategory.IPS, overview.status, overview.action);
      this._licenseService.setIpsVersion(overview.version);
      this.overviewData.set(ipsItem.category, this.getIpsListData(overview));
      this.historyData.set(
        ipsItem.category,
        ipsItem.list.map(data => this.getIpsListData(data))
      );
    }

    this._cdr.markForCheck();
  }

  private updateSystemInfo(): void {
    this._postService.sendGetHaveInterface<SystemInfo>('system/info').subscribe(data => {
      this._authService.updateSystemInfo(data);
    });
  }

  private setLicenseType(type: LicenseType, status: LicenseStatus, startAt: string): LicenseType | string {
    if (status === LicenseStatus.Upcoming) {
      return type + ' (Effective from ' + this._sharedService.transDate(startAt) + ')';
    } else {
      return type;
    }
  }

  private getIpsListData(data) {
    return {
      name: this._translate.instant(`pages.system.license.ipsMgmt`),
      duration: this.getDuration(data),
      elapsed: data.elapsed,
      updatedTime: this._sharedService.transDateTime(data.updatedAt),
      createTime: this._sharedService.transDateTime(data.createdAt),
      startTime: this._sharedService.transDateTime(data.startAt),
      endTime: this._sharedService.transDateTime(data.endAt),
      node: data.node,
      licenseNode: data.licenseNode,
      usedNode: data.usedNode,
      status: getIPSStatus(data, this._translate),
      statusRow: data.status,
      type: getLicenseType(data, this._translate),
      typeRow: data.type,
      activationCode: data.activationCode,
      isOverview: data.isOverview,
      isCustomizedLicense: data?.type === LicenseType.Cv,
      version: data.version
    };
  }

  private getListData(data) {
    return {
      name: this._translate.instant(`pages.system.license.${data.category.toLowerCase()}`),
      duration: data.duration,
      elapsed: data.elapsed,
      updatedTime: this._sharedService.transDateTime(data.updatedAt),
      createTime: this._sharedService.transDateTime(data.createdAt),
      startTime: this._sharedService.transDateTime(data.startAt),
      endTime: this._sharedService.transDateTime(data.endAt),
      node: data.node,
      licenseNode: data.licenseNode,
      usedNode: data.usedNode,
      status: data.status,
      statusRow: data.status,
      type: this.setLicenseType(data.type, data.status, data.startAt),
      typeRow: data.type,
      activationCode: data.activationCode,
      isOverview: data.isOverview,
      version: data.version
    };
  }

  getDuration(data: Overview) {
    if (data.isOverview) {
      return data.duration;
    }

    if (this.overviewData.get(LicenseCategory.IPS).typeRow !== LicenseType.Cv) {
      return data.duration;
    }

    if (data.type !== LicenseType.Cv) {
      return this._sharedService.noData;
    }

    return data.duration;
  }

  ngOnDestroy(): void {
    this._socketService.removeListener('group');
  }
}
