import { TranslateService } from '@ngx-translate/core';

import { Overview } from './license-response.model';
import { DisableDeactivateButton, LicenseList, LicenseStatus, LicenseType } from './license.model';

export const validIpsDevice = 'Valid (IPS-DEVICE)';

export const getIPSStatus = (license: Overview, translate: TranslateService) => {
  if (license.status === LicenseStatus.Valid) {
    return translate.instant('pages.system.license.valid');
  }

  if (license.status === LicenseStatus.Deactivated) {
    return translate.instant('pages.system.license.deactivated');
  }

  return translate.instant('pages.system.license.depleted');
};

export const disableDeactivateButtonStatus = (license: LicenseList): DisableDeactivateButton => {
  const { typeRow, statusRow } = license || {};

  if (!typeRow || !statusRow) {
    return { noLicense: true, depleted: false, deactivated: false };
  }

  const isTrialOrNew = typeRow === LicenseType.Trial || typeRow === LicenseType.New;
  const isTrialNewOrCv = isTrialOrNew || typeRow === LicenseType.Cv;

  if (isTrialOrNew && statusRow === LicenseStatus.Depleted) {
    return { depleted: true, deactivated: false, noLicense: false };
  }

  if (isTrialNewOrCv && statusRow === LicenseStatus.Deactivated) {
    return { depleted: false, deactivated: true, noLicense: false };
  }

  return { depleted: false, deactivated: false, noLicense: false };
};

export const getLicenseType = (license: Overview, translate: TranslateService) => {
  if (license.type === LicenseType.New && license.status === LicenseStatus.Valid) {
    return translate.instant('pages.system.license.standard');
  }

  if (license.type === LicenseType.Cv && license.status === LicenseStatus.Valid) {
    return translate.instant('pages.system.license.customized');
  }

  if (license.type === LicenseType.Trial && license.status === LicenseStatus.Valid) {
    return translate.instant('pages.system.license.trial');
  }

  if (license.type === LicenseType.Trial && license.status === LicenseStatus.Depleted) {
    return translate.instant('pages.system.license.trialDepleted');
  }

  if (license.type === LicenseType.New && license.status === LicenseStatus.Reclaimed) {
    return translate.instant('pages.system.license.reclaimed');
  }

  if (license.type === LicenseType.New && license.status === LicenseStatus.Depleted) {
    return translate.instant('pages.system.license.standardRunOut');
  }

  if (license.type === LicenseType.Cv && license.status === LicenseStatus.Expired) {
    return translate.instant('pages.system.license.customizedExpired');
  }

  if (license.type === LicenseType.Trial && license.status === LicenseStatus.Deactivated) {
    return translate.instant('pages.system.license.trialDeactivated');
  }

  if (license.type === LicenseType.New && license.status === LicenseStatus.Deactivated) {
    return translate.instant('pages.system.license.standardDeactivated');
  }

  if (license.type === LicenseType.Cv && license.status === LicenseStatus.Deactivated) {
    return translate.instant('pages.system.license.customizedDeactivated');
  }
  if (license.endTime === 0) {
    return translate.instant('pages.system.license.noLicense');
  }

  return '---';
};
