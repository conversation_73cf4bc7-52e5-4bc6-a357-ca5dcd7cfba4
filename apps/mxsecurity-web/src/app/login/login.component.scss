@import 'color.scss';
@import 'font-style.scss';
@import 'shared.scss';

.login-background {
  background-image: url('../../assets/img/bg_login.webp');
}

:host ::ng-deep {
  .login-outer-frame {
    background-color: #0e0f23;
    background-position: top center;
    background-repeat: no-repeat;
    height: 100vh;
    overflow: auto;

    .inner-frame {
      width: 400px;

      .logo-frame {
        height: 200px;

        img {
          width: 320px;
        }
      }

      .input-block {
        width: 100%;

        .mat-input-element {
          color: #fff !important;
        }

        .mat-form-field {
          color: #fff !important;
        }

        .mat-form-field-label {
          color: #fff !important;
        }

        .mat-form-field-underline {
          background-color: #fff !important;
        }

        .username {
          width: 80%;
          left: 40px;
        }

        .password {
          width: 80%;
          left: 40px;
        }
      }

      .login-error-msg {
        width: 80%;
        margin-left: 40px;
        margin-bottom: 30px;
        color: red;
        font-size: 13px;
        @include text-wrap;
      }

      .bt-login {
        @extend .btn-font;
        width: 80%;
        height: 46px;
        margin-left: 40px;
        color: $text-white;
      }

      .login-msg {
        width: 80%;
        margin-left: 40px;
        margin-bottom: 10px;
        color: #9b9b9b;
        font-size: 13px;
        @include text-wrap;
      }

      .copyright {
        width: 80%;
        margin-left: 40px;
        color: #fff;
        text-align: center;
      }
    }
  }
}
