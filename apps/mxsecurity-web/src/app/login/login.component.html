<div class="login-outer-frame" fxLayout="row" fxLayoutAlign="center center" ngClass="login-background">
  <div class="inner-frame" fxFlexAlign="center center" fxFlexLayout="row">
    <div class="logo-frame" fxLayout="row" fxLayoutAlign="center center">
      <img alt="logo" fxFlexLayout="column" src="assets/img/logo_w.png" />
    </div>
    <div class="input-block">
      <form [formGroup]="loginForm" (ngSubmit)="onLogin()">
        <mat-form-field class="username">
          <input formControlName="username" matInput placeholder="{{ 'general.commonAccount.username' | translate }}" />
          <mat-error *ngIf="loginForm.get('username').hasError('required')">
            {{ 'validators.required' | translate }}
          </mat-error>
          <mat-error *ngIf="loginForm.get('username').hasError('pattern')">
            {{ 'validators.invalid' | translate }}
          </mat-error>
        </mat-form-field>
        <mat-form-field class="password suffix-form-field">
          <input
            [type]="hidePassword ? 'password' : 'text'"
            formControlName="password"
            matInput
            maxlength="63"
            placeholder="{{ 'general.commonAccount.password' | translate }}"
            type="password"
            autocomplete="off"
          />
          <button (click)="hidePassword = !hidePassword" mat-icon-button type="button">
            <mat-icon> {{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
          </button>
          <mat-error *ngIf="loginForm.get('password').hasError('required')">
            {{ 'validators.required' | translate }}
          </mat-error>
        </mat-form-field>
        <div class="login-error-msg" *ngIf="loginError">{{ loginFailureMessage }}</div>
        <button class="bt-login" color="primary" mat-raised-button>{{ 'general.button.login' | translate }}</button>
      </form>
    </div>
    <div fxLayout="row" fxLayoutAlign="center center">
      <img alt="MXsecurity Logo" src="assets/img/logo_mxsecurity_w.webp" height="50" />
    </div>
    <div class="login-msg">{{ loginMessage }}</div>
    <div class="copyright mat-body-1">{{ 'general.copyright' | translate }}</div>
  </div>
</div>
