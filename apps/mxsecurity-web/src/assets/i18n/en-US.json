{"errors": {"A001": "Missing required field", "A002": "Missing query string", "A003": "Incorrect format", "D001": "Maximum number of licenses reached", "D002": "The device cannot be found", "D003": "The device must be online", "D004": "The device was deleted", "F001": "The firmware cannot be found", "F002": "This firmware already exists", "F003": "Maximum number of firmware files reached", "G001": "This group already exists", "G002": "The group cannot be found", "G003": "The default group cannot be modified", "G004": "Admin users cannot be assigned to groups", "I001": "This interface already exists", "I002": "The interface cannot be found", "I003": "The default group cannot be modified", "I004": "This interface is referenced by a security profile", "L001": "Invalid activation code", "L002": "The license has expired", "L003": "Duplicate activation code", "L004": "Maximum number of nodes reached", "L005": "The device cannot be activated or deactivated", "L006": "Invalid start time", "L007": "Invalid start time for the New type license", "O001": "This object already exists", "O002": "The object cannot be found", "O003": "This object is referenced by a security profile", "P001": "The package cannot be found", "P002": "This package already exists", "P003": "Maximum number of packages reached", "P004": "Unsupported version", "S001": "Update db error", "SP001": "This profile already exists", "SP002": "The profile cannot be found", "T001": "Unauthorized token", "T002": "Token expired", "T003": "Invalid token", "U001": "Permission denied", "U002": "This username already exists", "U003": "The user cannot be found", "U004": "The role cannot be found", "U005": "Invalid username or password", "U006": "The password does not meet the minimum length", "U007": "The password exceeds the maximum length", "U008": "The password cannot be the same as the username", "U009": "Must include at least one uppercase character", "U010": "Must include at least one lowercase character", "U011": "Must include at least one digit", "U012": "Must include at least one non-alphanumeric character", "U013": "The password cannot be the same as the previous password", "U014": "invalid username", "Unknown": "Unknown error"}, "general": {"bulletin": {"diskOver90": "Disk capacity has reached 90%. No additional event logs will be recorded. Please expand the storage capacity for the MXsecurity instance to continue recording logs."}, "button": {"abort": "ABORT", "apply": "APPLY", "back": "BACK", "backup": "BACK UP", "cancel": "CANCEL", "change": "CHANGE", "checkNow": "CHECK NOW", "clear": "CLEAR", "close": "CLOSE", "collapse": "COLLAPSE", "confirm": "CONFIRM", "connected": "Connected", "copy": "COPY", "create": "CREATE", "deactivate": "DEACTIVATE", "delete": "DELETE", "disable": "DISABLE", "disconnected": "Disconnected", "download": "DOWNLOAD", "download_all_logs": "Download All Logs", "download_filter_logs": "Download Filter Logs", "downloadAllLogs": "Download All Logs", "downloadFilterLogs": "Download Filter Logs", "edit": "EDIT", "enable": "ENABLE", "expand": "EXPAND", "export": "EXPORT", "finish": "FINISH", "import": "IMPORT", "locate": "LOCATE", "login": "LOG IN", "logout": "LOG OUT", "next": "NEXT", "ping": "PING", "reboot": "REBOOT", "redirect": "REDIRECT", "register": "REGISTER", "remove": "REMOVE", "reset": "RESET", "restore": "RESTORE", "send": "SEND", "unregister": "DEREGISTER", "upgrade": "UPGRADE", "upload": "UPLOAD"}, "common": {"action": "Action", "allow": "Allow", "any": "Any", "deny": "<PERSON><PERSON>", "description": "Description", "deviceInUse": "Device In Use", "deviceName": "Device Name", "disabled": "Disabled", "enabled": "Enabled", "endDate": "End Date", "firmwareVersion": "Firmware Version", "group": "Group", "index": "Index", "ipAddress": "IP Address", "location": "Location", "mac": "MAC Address", "name": "Name", "options": "Options", "productModel": "Product Model", "profileInUse": "Profile In Use", "refCount": "References", "serialNumber": "Serial Number", "startDate": "Start Date", "status": "Status"}, "commonAccount": {"account": "Account", "confirmPassword": "Confirm Password", "deleteAccountDesc": "Are you sure you want to delete the selected account?", "email": "Email", "password": "Password", "pwd_mask": "********", "username": "Username"}, "copyright": "Copyright © 2024 Moxa, Inc. All Rights Reserved.", "dialog": {"deleteMsg": "Are you sure you want to delete the selected {{ item }}?", "deleteTitle": "Delete {{ item }}", "isSelected": "{{ number }} item(s) selected", "warning": "Warning"}, "errorPage": {"authority": "Authority", "authorityDesc": "This is your first time logging in. For safety reasons, please update the default account and password.", "backLink": "Back to Dashboard Page", "pageNotFound": "Page Not Found :(", "pageNotFoundDesc": "The requested URL was not found on this server.", "permissionDenied": "Permission Denied :(", "permissionDeniedDesc": "You do not have permission to view this page."}, "fileDrop": {"browse": "browse", "dropText": "Drag and drop a file here, or"}, "log": {"localStorage": "Local Storage", "logDestination": "Log Destination", "snmpTrapServer": "SNMP Trap Server", "syslogServer": "Syslog Server", "title": "Log"}, "severity": {"alert": "<PERSON><PERSON>", "critical": "Critical", "debug": "Debug", "emergency": "Emergency", "error": "Error", "high": "High", "information": "Information", "informational": "Informational", "low": "Low", "medium": "Medium", "notice": "Notice", "title": "Severity", "warning": "Warning"}, "shortWeekday": {"fri": "Fri.", "mon": "Mon.", "sat": "Sat.", "sun": "Sun.", "thu": "<PERSON><PERSON>.", "tue": "<PERSON><PERSON>.", "wed": "Wed."}, "table": {"add": "Add", "delete": "Delete", "download": "Download", "downloadAllLogs": "Download All Logs", "edit": "Edit", "filter": "Filter", "info": "Info", "more": "More", "permissionDenied": "Permission Denied", "reboot": "Reboot", "refresh": "Refresh", "reorderFinish": "Finish Reordering", "reorderPriority": "Reorder Priorities", "search": "Search", "transfer": "Transfer", "upgrade": "Upgrade"}, "topNav": {"caseInsensitive": "Case-insensitive", "changePwd": "Change Password", "changeSuccess": "Your password was updated successfully. Please log in again.", "confirmNewPwd": "Confirm New Password", "currentPwd": "Current Password", "invalidKey": "The following name(s) are reserved: admin, operator, viewer, root, administrator, auditor", "logout": "Log Out", "logoutMsg": "Are you sure you want to log out?", "newPwd": "New Password", "subject": "Subject", "troubleshoot": "Troubleshooting", "troubleshootMsg": "You can export the debug logs to the local host for troubleshooting.", "updateAuthority": "Update Authority", "updateSuccess": "Your account authority has been changed. Please log in again.", "username": "Username"}, "unit": {"days": "day(s)", "entries": "entries", "minute": "minute", "minutes": "minute(s)", "months": "month(s)", "percent": "%", "pkts": "pkt/s", "sec": "sec.", "seconds": "second(s)", "thousand": "thousand"}, "weekday": {"friday": "Friday", "monday": "Monday", "saturday": "Saturday", "sunday": "Sunday", "thursday": "Thursday", "tuesday": "Tuesday", "wednesday": "Wednesday"}}, "login": {"forceLogout": "Your account authority has been changed. Please contact the administrator if you have any questions.", "loginSuccess": "Login successful", "sessionTimeout": "Session timed out"}, "menu": {"about": "About", "alertSetting": "<PERSON><PERSON>", "authorityError": "Authority", "dashboard": "Dashboard", "deviceConfiguration": "Device Configuration", "deviceDeployment": "Device Deployment", "deviceGroup": "Device Groups", "eventLog": "Event Log", "firmware": "Firmwares", "interface": "Device Interface", "license": "Licenses", "logging": "Logging", "management": "Management", "mapView": "Map View", "notification": "Notification", "object": "Objects", "pageNotFoundError": "Page Not Found", "permissionError": "Permission Denied", "policyProfile": "Policy Profiles", "report": "Report", "settings": "Settings", "softwarePackage": "Software Packages", "system": "System", "userAccount": "User Accounts", "vpn": "VPN"}, "pages": {"about": {"cardTitle": "MXsecurity", "serverId": "ID", "serverVersion": "Version"}, "dashboard": {"addWidget": "Add Widget", "adp": "ADP", "adpDestIp": {"linkButton": "Show Event Log", "name": "Top 5 ADP Policy Events by Destination IP"}, "adpSeverity": {"linkButton": "Show Event Log", "name": "Top 5 ADP Policy Events by Severities"}, "adpSrcIp": {"linkButton": "Show Event Log", "name": "Top 5 ADP Policy Events by Source IP"}, "auditLogByTimeline": {"linkButton": "Show Audit Log", "name": "Audit Log by Timeline"}, "cellularConnection": {"fair": "Fair", "good": "Good", "linkButton": "Show Map View", "name": "Signal Quality (Cellular Router)", "noSignal": "No Signal", "poor": "Poor"}, "connectionType": {"2g": "2G", "3g": "3G", "4g": "4G", "5g": "5G", "ethernet": "Ethernet", "linkButton": "Show Map View", "name": "Connection Interface (Cellular Router)", "wifi": "WiFi"}, "deviceLogByTimeline": {"linkButton": "Show Device Log", "name": "Device Log by Timeline"}, "Devices": "<PERSON>ce(s)", "dos": "DoS Policy", "dpi": "Protocol Filter Policy", "dpiDestIp": {"linkButton": "Show Event Log", "name": "Top 5 Protocol Filter Policy Events by Destination IP"}, "dpiSeverity": {"linkButton": "Show Event Log", "name": "Top 5 Protocol Filter Policy Events by Severities"}, "dpiSrcIp": {"linkButton": "Show Event Log", "name": "Top 5 Protocol Filter Policy Events by Source IP"}, "estimatedRunOutDate": "Estimated Depletion Date", "firewallLogByTimeline": {"linkButton": "Show Firewall Log", "name": "Firewall Log by Timeline"}, "groupStatus": {"label": "Total Number of Devices", "linkButton": "Show Device Groups", "name": "Group Status", "onlineDevices": "Online Device(s)"}, "ips": "IPS", "ipsAvailableDays": "Remaining IPS Validity", "ipsAvailablePoints": "IPS Point Balance", "ipsCategory": {"linkButton": "Show Event Log", "name": "Top 5 IPS Policy Events by Category"}, "ipsDestIp": {"linkButton": "Show Event Log", "name": "Top 5 IPS Policy Events by Destination IP"}, "ipsSeverity": {"linkButton": "Show Event Log", "name": "Top 5 IPS Policy Events by Severities"}, "ipsSrcIp": {"linkButton": "Show Event Log", "name": "Top 5 IPS Policy Events by Source IP"}, "l2Policy": "Layer 2 Policy", "l3DestIp": {"linkButton": "Show Event Log", "name": "Top 5 Layer 3-7 Policy Events by Destination IP"}, "l3Policy": "Layer 3-7 Policy", "l3Severity": {"linkButton": "Show Event Log", "name": "Top 5 Layer 3-7 Policy Events by Severities"}, "l3SrcIp": {"linkButton": "Show Event Log", "name": "Top 5 Layer 3-7 Policy Events by Source IP"}, "licenseUsage": {"linkButton": "Show Licenses", "name": "Licenses"}, "malformed": "Malformed Packets", "nodes": "Nodes", "noWidgetMsg": "Maximum number of widgets reached.", "nsmUsedTotal": "MXsecurity Used / Total", "points": "Points", "sc": "Session Control", "systemStatus": {"cpu": "CPU", "disk": "Disk", "memory": "Memory", "name": "System Status"}, "totalDevices": "Total Number of Device(s)", "trustAccess": "Trusted Access", "vpnLogByTimeline": {"linkButton": "Show VPN Log", "name": "VPN Log by Timeline"}}, "deviceDeployment": {"alreadySentSms": "Already sent {{ smsNumber }}/{{ max }} SMS in this month", "applied": "Applied", "atLeastSelectOne": "Select at least one SMS control command", "cellularModuleDisable": "The Cellular Module is disabled, sending SMS command is not permitted.", "cellularStartConnecting": "Cellular Start Connecting", "cellularStopConnecting": "Cellular Stop Connecting", "configSync": "The configuration of the selected device(s) will be synced.", "daily": "Daily", "date": "Date", "deleteMsg": "Are you sure you want to delete the selected device(s)?", "deleteSchedule": "Delete Schedule", "deleteScheduleSuccess": "Device(s) schedule deleted successfully", "deleteSuccess": "Device(s) deleted successfully", "deleteTitle": "Delete Device(s)", "deviceConfiguration": "Device Configuration", "deviceDetail": "<PERSON><PERSON>", "deviceDisableHint": "Function is disabled from the device side", "deviceSelected": "device(s) selected", "endTime": "End Date", "firmware": "Firmware", "firmwareUpgrade": "The firmware of the selected device(s) will be upgraded", "firmwareVersion": "Firmware Version", "general": "General", "groupName": "Group Name", "groupSelected": "group(s) selected", "invalidDate": "Invalid Date", "invalidPeriod": "Invalid Period", "lastRebootTime": "Last Reboot Time", "lastUpdate": "Last Update", "lastUpdateTime": "Last Update Time", "location": "Location", "mac": "MAC", "manually": "Manually", "maxSms": "You have reached the monthly SMS limit (MAX. {{ max }})", "noConfigAvailable": "No configurations available", "noConfigMsg": "Check the configurations on the Management/Device Configuration page.", "noFirmwareMsg": "Check the firmware files on the Management/Firmware page.", "noPackageMsg": "Check the software packages on the Management/Software Packages page.", "noProfileAvailable": "No profiles available", "noProfileMsg": "Check the profiles on the Management/Policy Profiles page.", "notSupportModel": "Not support model", "notSync": "Not Synced", "noVersionAvailable": "No version available", "oneTime": "One Time", "outOfSync": "Out of Sync", "package": "Package", "packageUpgrade": "The software package of the selected device(s) will be upgraded.", "packageVersion": "Package Version", "period": "Period", "policyProfile": "Policy Profiles", "processing": "Processing", "profileName": "Profile Name", "profileSync": "The profile of the selected device(s) will be synced.", "reboot": "The device(s) will be rebooted.", "rebootDisabled": "Only online device(s) can be rebooted.", "rebootMsg": "Are you sure you want to reboot the selected device(s)?", "rebootTitle": "Reboot Device(s)", "remoteSmsControl": "Remote SMS Control", "restoreConfigDisabled": "Only online devices of the same model type can be synced.", "sameVersionWarning": "One or more of the selected devices already have version {{ version }} applied.", "schedule": "Schedule", "scheduleDisabled": "Only devices of the same model type can be scheduled.", "scheduleOverlapMsg": "Can not select the time slot which already assigns the Reboot or Firmware upgrade.", "scheduleSettings": "Schedule Settings", "scheduling": "Scheduling", "schedulingMode": "Scheduling Mode", "schedulingPeriod": "Scheduling Period", "schedulingReboot": "Scheduling Reboot", "selectConfigFile": "Select Configuration File", "selectFile": "Select File", "sendSms": "Send SMS", "sendSmsControl": "Send SMS Control", "sendSmsOnCell": "Select one OnCell device to send SMS Control", "sendSmsSuccess": "Send SMS successfully", "serialNumber": "Serial Number", "setDoOff": "Set DO Off", "setDoOn": "Set DO On", "shouldBeSameVersion": "The package version of the selected devices should be the same.", "shouldHaveJanus": "One or more of the selected device(s) do not have a software package installed.", "shouldSyncOnline": "Only online device(s) can be synced.", "showAll": "Show All Groups and Devices", "showSelected": "Show Selected Groups and Devices", "smsCountDownHint": "Send next SMS after 60 seconds", "softwarePackage": "Software Packages", "startIpsecTunnel": "Start IPSec Tunnel", "startTime": "Start Date", "status": "Status", "statusProfileName": "Status / Profile Name", "stopIpsecTunnel": "Stop IPSec Tunnel", "switchSim": "Switch SIM", "sync": "Synced", "syncConfig": "Sync Configuration", "syncConfigTitle": "Sync Configuration To Device(s)", "syncModified": "Synced (Modified)", "syncProfile": "Sync Profile", "syncProfileTitle": "Sync Profile To Device(s)", "systemRestart": "System Restart", "time": "Time", "updateScheduleSuccess": "Device schedule updated successfully", "upgradeDisabled": "Only online devices  can be upgraded.", "upgradePackageError": "Firmware versions above 2.5.0 and below 2.4.x cannot coexist.", "upgradePackageNotSameDisabled": "Only the devices of same model type can be selected", "upToDate": "Up-to-date", "version": "Version", "weekly": "Weekly", "weeklyDay": "Weekly Day"}, "eula": {"agree_notice": "I accept the End-User License Agreement", "agreeNotice": "I accept the End-User License Agreement", "title": "End-User License Agreement"}, "logging": {"eventLog": {"adp": "ADP", "audit": "Audit", "device": "<PERSON><PERSON>", "dos": "DoS Policy", "dpi": "Protocol Filter Policy", "endDate": "End Date", "endTime": "End Time", "event": "Event", "firewall": "Firewall", "ips": "IPS", "l2Policy": "Layer 2 Policy", "l3Policy": "Layer 3-7 Policy", "malformed": "Malformed Packets", "sc": "Session Control", "setting": "Setting", "severity": "Severity", "startDate": "Start Date", "startTime": "Start Time", "tab": {"audit": {"deviceName": "Device Name", "event": "Event", "groupName": "Group Name", "message": "Message", "severity": "Severity", "time": "Time", "username": "Username"}, "device": {"deviceName": "Device Name", "event": "Event", "groupName": "Group Name", "mac": "MAC Address", "message": "Message", "severity": "Severity", "time": "Time", "username": "Username"}, "firewall": {"action": "Action", "adp": "ADP", "all": "All", "appProtocol": "Application Protocol", "category": "Category", "deviceName": "Device Name", "dos": "DoS Policy", "dpi": "Protocol Filter Policy", "dstIp": "Destination IP", "dstMac": "Destination MAC", "dstPort": "Destination Port", "etherType": "EtherType", "event": "Event", "fromInterface": "Incoming Interface", "groupName": "Group Name", "icmpCode": "ICMP Code", "icmpType": "ICMP Type", "id": "Index", "ips": "IPS", "ipsCategory": "IPS Category", "ipsSeverity": "IPS Severity", "l3Policy": "Layer 3-7 Policy", "malformed": "Malformed Packets", "message": "Additional Message", "policyId": "Policy ID", "policyName": "Policy Name", "protocol": "IP Protocol", "security": "Security", "sessionControl": "Session Control", "severity": "Severity", "srcIp": "Source IP", "srcMac": "Source MAC", "srcPort": "Source Port", "subCategory": "Subcategory", "tcpFlag": "TCP Flags", "time": "Time", "toInterface": "Outgoing Interface", "trustAccess": "Trusted Access", "username": "Username", "vlanId": "VLAN ID"}, "vpn": {"deviceName": "Device Name", "event": "Event", "groupName": "Group Name", "message": "Additional Message", "severity": "Severity", "time": "Time", "username": "Username"}}, "trustAccess": "Trusted Access", "vpn": "VPN"}, "notification": {"advancedSettingMsg": "Once the maximum number of notifications has been reach in period of time, no more notifications are sent until next period.", "advancedSettings": "Advanced Settings", "appProtocol": "Application Protocol", "arpFlood": "ARP-Flood", "atLeastOneReceiver": "At least one Receiver", "bufferOverflow": "Buffer Overflow", "chooseDevices": "Choose Devices", "createdBy": "Created By", "createNotification": "Create Notification", "createSuccess": "Notification created successfully", "deleteKey": "Notification(s)", "deleteNotification": "Delete Notification", "deleteSuccess": "Notification(s) deleted successfully", "deviceCount": "<PERSON><PERSON>", "deviceName": "Device Name", "DNP3": "DNP3", "dosAttacks": "DoS attacks", "dstIp": "Destination IP", "dstMac": "Destination MAC", "editNotification": "Edit Notification", "EIP": "EIP", "email": "Email", "emailContent": "Email Content", "emailContentDefault": "The event ${event} triggered at device ${productModel}, ${deviceName}, happened at ${eventTime}.", "emailHeader": "[MXsecurity] Notification ${notificationName}\ngenerated from ${deviceName}", "emailMsgAutoSentFrom": "This notification was automatically sent \nfrom MXsecurity.", "emailMsgCheck": "Please check the detailed information \non MXsecurity.", "emailMsgGreeting": "Dear Sir/ <PERSON>am,", "emailMsgSignOff": "Best regards,\nMXsecurity", "eq": "Equal to", "event": "Event", "eventFilter": "Choose Event and Filter", "eventFilterRule": "Event Filter Rule", "eventTime": "Event Time", "exploits": "Exploits", "fileVulnerabilities": "File vulnerabilities", "filterRule": "Filter Rule", "filterRuleDetail": "Filter Rule(s) Detail", "finScan": "FIN Scan", "floodingScan": "Flooding & Scan", "GOOSE": "GOOSE", "gt": "Lower than", "gte": "Lower than or Equal to", "icmpDeath": "ICMP-Flood", "IEC-104": "IEC-104", "ipAddress": "IP Address", "ipRangeHint": "You could use * to represent the result of the /8/16/24 subnet mask, ex. 192.168.*.* \nCould not be used at the beginning of an IP address or alone in the middle *", "ipsCate": "IPS Category", "ipSpoofing": "IP Spoofing", "ipsSeverity": "IPS Severity", "location": "Location", "lt": "Higher than", "lte": "Higher than or Equal to", "macAddress": "<PERSON>dress", "macRangeHint": "You could use *to represent a range of MAC address, ex. 00:90:E8:*:*:* \nCould not be used at the beginning of a MAC address or alone in the middle.", "malwareTraffic": "Malware traffic", "maxEnableSize": "The MAX. enabled notifications is {{num}}.", "maxNotification": "MAX. Notification", "maxPerUserSize": "The MAX. notifications per user is {{num}}.", "MMS": "MMS", "Modbus/TCP": "Modbus/TCP", "newTcpWithoutSynScan": "TCP Sessions Without SYN", "nmapIdScan": "NMAP-ID Scan", "nmapXmasScan": "NMAP-<PERSON><PERSON>", "notificationActions": "Notification Actions", "notificationEvent": "Notification Event", "notificationInfo": "Notification Information", "notificationLimit": "Notification Limit", "notificationName": "Notification Name", "nullScan": "<PERSON><PERSON>", "OmronFINS": "Omron FINS", "periodTime": "Period of Time", "policyName": "Policy Name", "productModel": "Product Model", "protocolAttackProtection": "Protocol Attack Protection", "receiverEmailAddress": "Receiver <PERSON><PERSON>dress", "receiverSetting": "Receiver Settings", "reconnaissance": "Reconnaissance", "resetToDefault": "Reset to default", "serialNumber": "Serial Number", "severity": "Severity", "severityMode": "Severity Mode", "severityRule": "Severity Rule", "showAllDevices": "Show All Devices", "showSelectedDevices": "Show Selected Devices", "srcIp": "Source IP", "srcMac": "Source MAC", "Step7Comm": "Step7Comm", "subCate": "Sub Category", "synFinScan": "SYN/FIN Scan", "synFlood": "SYN-Flood", "synRstScan": "SYN/RST Scan", "syslog": "Syslog", "syslogContent": "Syslog Content", "syslogContentDefault": "Notification ${notificationName} been triggered at device. ${productModel}, ${deviceName}, happened at ${eventTime}. Please check detail information at MXsecurity.", "udpFlood": "UDP-Flood", "updateSuccess": "Notification updated successfully", "webThreats": "Web threats", "xmasScan": "<PERSON><PERSON>"}}, "management": {"deviceConfiguration": {"configModel": "Configuration Model", "configName": "Configure Name", "createSuccess": "Device configuration created successfully", "deleteKey": "Device Configuration(s)", "deleteSuccess": "Device configuration(s) deleted successfully", "editConfig": "Edit Configuration", "enterConfigInfo": "Enter Configuration File Information", "firmwareVersion": "Firmware Version", "group": "Group", "isReferenced": "One or more of the selected configuration(s) are referenced.", "lastModifiedTime": "Last Modified Time", "location": "Location", "mac": "MAC Address", "maxTableSize": "The maximum configurations is {{num}}.", "noModelMsg": "There is no model for configuration", "offlineWarning": "Device offline", "onlyAcceptIni": "Only Configuration files in '.ini' format are accepted.", "onlyOneFilePerTime": "Only one file can be uploaded at a time.", "selectConfigFile": "Select Configuration File", "selectWarning": "Only allows one device for configuration backup", "serialNumber": "Serial Number", "updateSuccess": "Device configuration updated successfully", "uploadConfigFile": "Upload Configuration File (.ini)", "uploadConfigMethod": "Upload Configuration Method", "uploadConfigTitle": "Upload Device Configuration File", "uploadDeviceConfig": "Upload Configuration from Device", "uploadLocalConfig": "Upload Configuration from Local"}, "deviceGroup": {"accessPermission": "Access Permission", "addDevices": "Add Devices", "adminPermission": "Admin users have permission to all groups ", "createGroup": "Create Group", "createSuccess": "Device group created successfully", "deleteKey": "Delete Device Group(s)", "deleteSuccess": "Device group(s) deleted success", "description": "Description", "deviceCount": "<PERSON><PERSON>", "editGroup": "Edit Device Group", "enterGroupInfo": "Enter Group Information", "firmwareVersion": "Firmware Version", "grantAccessPermission": "Grant Access Permission", "group": "Group", "groupName": "Group Name", "location": "Location", "mac": "MAC", "role": "Role", "serialNumber": "Serial Number", "showAllDevices": "Show All Devices", "showSelectedDevices": "Show Selected Devices", "status": "Status", "updateSuccess": "Device group updated successfully", "username": "Username"}, "firmware": {"buildTime": "Build Time", "deleteKey": "Firmware(s)", "deleteSuccess": "Firmware deleted successfully", "description": "Description", "dropZoneTitle": "Upload a firmware file (.rom)", "isReferenced": "One or more of the selected firmware(s) are referenced.", "maxRowMsg": "The maximum number of firmware files is {{ max }}.", "maxSize": "The maximum allowed file size is 1 GB.", "modelSeries": "Model Series", "onlyAcceptRom": "Only firmware files in '.rom' format are accepted.", "onlyOneFilePerTime": "Only one file can be uploaded at a time.", "uploadFirmware": "Upload Firmware", "uploadSuccess": "Firmware uploaded successfully", "version": "Version"}, "inUse": "Yes", "object": {"filter": {"address": "IP Address and Subnet", "code": "Code", "createObject": "Create Object", "createSuccess": "Object created successfully", "customIpProtocol": "Custom IP Protocol", "decimal": "(Decimal)", "deleteKey": "Object(s)", "deleteSuccess": "Object(s) deleted successfully", "detail": "Details", "editObject": "Edit Object", "endPort": "Port: End", "icmp": "ICMP", "icmpCode": "ICMP Code", "icmpType": "ICMP Type", "industrialAppService": "Industrial Application Service", "ipAddress": "IP Address", "ipEnd": "IP Address: End", "ipProtocol": "IP Protocol", "ipRange": "IP Range", "ipStart": "IP Address: Start", "ipType": "IP Type", "isReferenced": "One or more of the selected objects are referenced", "leaveAsAny": "Leave blank to represent Any", "maxRowMsg": "The maximum number of objects is {{ max }}.", "name": "Filter", "needSelectedMsg": "Select at least one item", "networkName": "Network Name", "networkService": "Network Service", "objectName": "Name", "objectReference": "Object References", "objectReferenceMsg": "This object is referenced by a policy index in the following profile(s):", "objectType": "Object Type", "port": "Port", "portRange": "TCP and UDP Port Range", "selectIndustrialAppService": "Select Industrial Application Service(s) *", "selectNetworkService": "Select Network Service(s) *", "servicePortType": "Service Port Type", "singleIp": "Single IP", "singlePort": "Single TCP and UDP Port", "startPort": "Port: Start", "subnet": "Subnet", "subnetMask": "Subnet Mask", "tcp": "TCP", "tcpUdp": "TCP and UDP", "type": "Type", "udp": "UDP", "updateSuccess": "Object updated successfully", "userDefinedService": "User-defined Service"}, "interface": {"bridge": "Bridge", "createInterface": "Create Interface", "createSuccess": "Interface created successfully", "deleteKey": "Interface(s)", "deleteSuccess": "Interface(s) deleted successfully", "editInterface": "Edit Interface", "interfaceName": "Interface Name", "interfaceReference": "Interface References", "interfaceReferenceMsg": "This interface is referenced by a policy index in the following profile(s):", "invalidKey": "The following name(s) are reserved: Any", "isReferenced": "One or more of the selected interfaces are referenced.", "maxRowMsg": "The maximum number of interfaces is {{ max }}.", "mode": "Mode", "name": "Interface", "port": "Port-based", "updateSuccess": "Interface updated successfully", "vlan": "VLAN", "vlanIdBridgeType": "VLAN ID / Bridge Type", "zone": "Zone-based"}}, "policyProfile": {"createProfile": "Create Policy Profile", "createSuccess": "Profile created successfully", "deleteKey": "Profile(s)", "deleteSuccess": "Profile(s) deleted successfully", "dos": {"dosLogSetting": "DoS Log Settings", "dosSetting": "DoS Settings", "floodProtection": "Flood Protection", "limit": "Limit", "portScanProtection": "Port-Scan Protection", "sessionSYNProtection": "Session SYN Protection", "stat1": "<PERSON><PERSON>", "stat10": "SYN-Flood", "stat11": "ARP-Flood", "stat12": "UDP-Flood", "stat2": "<PERSON><PERSON>", "stat3": "NMAP-<PERSON><PERSON>", "stat4": "SYN/FIN Scan", "stat5": "FIN Scan", "stat6": "NMAP-ID Scan", "stat7": "SYN/RST Scan", "stat8": "TCP Sessions Without SYN", "stat8Tooltip": "Limitation: For asymmetric network architectures and when NAT is enabled, it is strongly advised not to enable \"TCP Sessions Without SYN\" disabled to avoid unexpected disconnections.", "stat9": "ICMP-Flood", "title": "DoS"}, "editProfile": "Edit Policy Profile", "ips": {"accept": "Accept", "category": "Category", "custom": "(custom)", "id": "ID", "impact": "Impact", "monitor": "Monitor", "noPackageMsg": "Check the software packages on the Management/Software Packages page.", "noVersionAvailable": "No version available", "packageVersion": "Package Version", "reference": "Reference", "reset": "Reset", "ruleSetting": "Rule Settings", "title": "IPS", "updateSuccess": "Rule(s) updated successfully", "warningMsg": "Before configuring any policies, please make sure the Intrusion Prevention System (IPS) function is enabled on the Firewall > Advanced Protection > Configuration screen in the device's web interface."}, "isReferenced": "One or more of the selected profile(s) are referenced.", "layer3to7": {"allowAll": "Allow All", "createPolicy": "Create Layer 3-7 Policy", "createSuccess": "Layer 3-7 policy created successfully", "default_action_log": "Default Action Log", "default_action_log_destination": "Default Action Log Destination", "default_action_severity": "Default Action Severity", "defaultAction": "Default Action", "defaultActionLog": "Default Action Log", "defaultActionLogDestination": "Default Action Log Destination", "defaultActionSeverity": "Default Action Severity", "deleteKey": "Layer 3-7 Policy(s)", "deleteSuccess": "Layer 3-7 policy(s) deleted successfully", "deleteTitle": "Delete Layer 3-7 Policy", "denyAll": "<PERSON><PERSON>", "destinationAddress": "Destination Address", "destinationPort": "Destination Port or Protocol", "destIpAddress": "Destination IP Address", "destService": "Destination Service", "editPolicy": "<PERSON> Layer 3-7 <PERSON>", "enforce": "Status", "enforcement": "Status", "event": "Event", "eventSetting": "Global Policy Event Settings", "filterMode": "Filter Mode", "globalSetting": "Global Policy Settings", "incomingInterface": "Incoming Interface", "ipAndPortFiltering": "IP and Port Filtering", "ipAndSourceMacBinding": "IP and Source MAC Binding", "ipTypeError": "Source Port IP Protocol ({{ source }}) is different from Destination Port IP Protocol ({{ dest }})", "maxRowMsg": "The maximum number of policy rules for this device is {{ max }}.", "outgoingInterface": "Outgoing Interface", "policyName": "Name", "protocolService": "Protocol and Service", "sourceAddress": "Source Address", "sourceIpAddress": "Source IP Address", "sourceMacAddress": "Source MAC Address", "sourceMacFiltering": "Source MAC Filtering", "sourcePort": "Source Port", "title": "Layer 3-7", "updateSuccess": "Layer 3-7 policy updated successfully"}, "maxRowMsg": "The maximum number of profiles is {{ max }}.", "profileName": "Profile Name", "profileReference": "Profile References", "profileReferenceMsg": "This profile is referenced by the following device(s):", "sessionControl": {"concurrentTcp": "Concurrent TCP Requests", "connectionsRequestUnit": "connections/s", "connectionsUnit": "connections", "createPolicy": "Create Session Control Policy", "createSuccess": "Session control policy created successfully", "deleteKey": "Session Control Policy(s)", "deleteSuccess": "Session control policy(s) deleted successfully", "destinationIp": "Destination IP", "destinationPort": "Destination Port", "destIpAddress": "IP Address", "destPort": "Port", "drop": "Drop", "editPolicy": "Edit Session Control Policy", "enforcement": "Status", "maxRowMsg": "The maximum number of policy for this device is {{ max }}.", "monitor": "Monitor", "sub_title": "Network Host and Service Resource Protector", "subTitle": "Network Host and Service Resource Protector", "tcp_limit_error": "You must configure at least one limitation", "tcpConnectionLimit": "TCP Connection Limitation", "tcpDestError": "IP Address and Port cannot both be Any at the same time", "tcpDestination": "TCP Destination", "tcpLimitError": "You must configure at least one limitation", "tcpLimitMsg": "At least one limitation is required", "title": "Session Control", "totalTcp": "Total TCP Connections", "updateSuccess": "Session control policy updated successfully"}, "updateSuccess": "Profile updated successfully"}, "scheduleInUse": "Schedule In Use", "scheduling": "Scheduling", "softwarePackage": {"applicationProducts": "APPLICATION PRODUCTS", "auto-download": "Auto-download", "bugsFixed": "BUGS FIXED", "buildTime": "Build Time", "changes": "CHANGES", "checkConnection": "Check connection status with MOXA server.", "checkNewPackage": "Checks for new package versions on MOXA server.", "checkSoftwarePackage": "CHECK SOFTWARE PACKAGE", "daily": "Daily", "deleteKey": "Software Package(s)", "deleteSuccess": "Software package(s) deleted successfully", "description": "Description", "detailInfo": "Detailed Info", "dropZoneTitle": "Upload a package file (.pkg)", "endDate": "End Date", "endTime": "End Time", "enhancements": "ENHANCEMENTS", "event": "Event", "isReferenced": "One or more of the selected package(s) are referenced.", "janus": "Network Security Packages", "lastConnectionCheck": "Last Connection Check", "lastSoftwarePackageUpdateResult": "Last Software Package Update Result", "licenseActivationReminder": "License Activation Reminder", "licenseActivationReminderContent": "To ensure enhanced security mechanisms, please activate the license to enable this feature.", "licenseTransferReminder": "License Transfer Reminder", "licenseTransferReminderContent": "To ensure enhanced security mechanisms, please transfer your MXsecurity license before uploading the network security packages.", "local": "Local", "log": "Log", "maxPackageMsg": "Maximum simultaneous downloads: {{ max }} files.", "maxRowMsg": "The maximum number of software packages is {{ max }}.", "maxSize": "The maximum allowed file size is 1 GB.", "message": "Message", "newFeatures": "NEW FEATURES", "notes": "NOTES", "onlyAcceptPkg": "Only files in '.pkg' format are accepted.", "onlyOneFilePerTime": "Only one file can be uploaded at a time.", "packageDownloading": "Package downloader is working for others", "packageReference": "Package References", "packageReferenceMsg": "This package is referenced by the following profile(s):", "period": "Period", "productModel": "Product Model", "releaseDate": "RELEASE DATE", "releaseNote": "Release Note", "scheduling": "Scheduling", "schedulingMode": "Scheduling Mode", "server": "Server", "serverDisconnected": "The software package can only be checked when the server is connected.", "severity": "Severity", "softwarePackageAlreadyLatest": "Software Package already up-to-date.", "softwarePackageCheck": "Software Package Check", "softwarePackagesFile": "Software Packages File", "softwarePackagesUpdateCheck": "Software Packages Update Check", "startDate": "Start Date", "startTime": "Start Time", "supportedFunctions": "Supported Functions", "supportedOperatingSystems": "SUPPORTED OPERATING SYSTEMS", "supportModel": "Supported Models", "supportSeries": "Supported Series", "syncSettingNotSet": "Complete the Sync Settings to check the software package.", "syncSettings": "Sync Settings", "syncSettingUpdateSuccess": "Sync Settings updated successfully", "syncSoftwarePackageBySchedule": "Sync Software Package by Schedule", "syncSoftwarePackageByScheduleTooltip": "Set the frequency for scheduled checks with the MOXA server.", "time": "Time", "updateCheckTooltip": "Check for updated security package versions on the MOXA server.", "uploadBy": "Upload By", "uploadSoftwarePackage": "Upload Package", "uploadSuccess": "Software package uploaded successfully", "username": "Username", "version": "Version", "weekday": "Weekday", "weekly": "Weekly", "zeus": "MXsecurity Agent Packages"}}, "mapView": {"2g": "2G", "3g": "3G", "4g": "4G", "5g": "5G", "basicInfo": "Basic Information", "cancel": "Cancel", "cellularBand": "Cellular Band", "cellularCarrier": "Cellular Carrier", "cellularInfo": "Cellular Information", "cellularMode": "Cellular Mode", "cellularModule": "Cellular Module", "cellularRouter": "Cellular Router", "cellularSignal": "Cellular Signal", "cellularSim": "Cellular SIM", "confirm": "Confirm", "connectionType": "Connection Type", "deviceDetail": "<PERSON><PERSON>", "deviceDetailTitle": "Device {{ devName }} Information", "deviceNotFound": "<PERSON><PERSON> not Found", "deviceOfflineLocation": "<PERSON><PERSON> is offline, cannot edit location", "deviceType": "Device Type", "editLocation": "Edit Location", "ethernet": "Ethernet", "fair": "Fair", "firmwareVersion": "Firmware Version", "good": "Good", "imei": "IMEI", "imsi": "IMSI", "lanIpAddress": "LAN IP Address", "lastUpdate": "Last Update", "location": "Location", "locationFail": "Location change fail", "locationHint1": "The user can manually edit and display the device location on the map view. ", "locationHint2": "\n  Please enter the location with \"Latitude, Longitude\" format, ex. 31.0, 122.51.", "locationSuccess": "Location change successfully", "mac": "MAC Address", "noLocationDevices": "No Location Devices", "noSignal": "No Signal", "offline": "Offline", "online": "Online", "onlineStatus": "Online Status", "oobIpAddress": "OOB IP Address", "phoneNumber": "Phone Number", "poor": "Poor", "remoteSmsCtrl": "Remote SMS Control", "searchPlaceholder": "Search by Device Name, Serial Number, and MAC Address", "secureRouter": "Secure Router", "serialNumber": "Serial Number", "signal": "Signal", "signalLevel": "Signal Level", "simCard": "SIM Card", "simCardNumber": "SIM Card Number", "status": "Status", "systemUptime": "System Uptime", "wanIpAddress": "WAN IP Address", "wifi": "WiFi"}, "report": {"cellularSignal": "Cellular Signal", "cellularSignalMessage": "The report will be sent at 0:30 on the selected date and will include the <strong>data</strong> for the <strong>previous date.</strong>", "chooseReportType": "Choose Report Type", "createScheduleTitle": "Schedule an Email to Send {{ reportName }} Report", "createSuccess": "Report schedule created successfully", "creator": "Creator", "customize": "Customize", "daily": "Daily", "dailyMessage": "Report will sent at 0:30 at the specific date, which contains the data from the previous day.", "dataUsage": "Data Usage", "dataUsageMessage": "The report will be sent at 0:30 on the selected date and will include the <strong>data</strong> for the <strong>previous month.</strong>", "date": "Date", "deleteSuccess": "Report schedule(s) deleted successfully", "deviceCount": "<PERSON><PERSON>", "deviceSelected": "device(s) selected", "downloadSuccess": "Download Report successfully", "downloadTitle": "Download {{ reportName }} Report", "editScheduleTitle": "Edit {{ reportName }} Schedule Report", "generateReportDate": "Generate Report Date", "groupName": "Group Name", "groupSelected": "group(s) selected", "invalidDate": "Invalid Date", "invalidPeriod": "Invalid Period", "inventory": "Inventory", "inventoryMessage": "The report will be sent at 0:30 on the selected date and will include the <strong>data</strong> for the <strong>selected date.</strong>", "lastUpdated": "Last Updated", "maxPerUserSize": "The MAX. schedule reports per user is {{ maxPerUser }}. \n Or the MAX. schedule reports is {{ maxReport }}", "monthly": "Monthly", "monthlyMessage": "Report will sent at 0:30 at the specific date, which contains the data from the previous month.", "oneTime": "One Time", "oneTimeMessage": "Report will sent at 0:30 at the specific date, which contains the data from the previous day.", "period": "Period", "receiverEmail": "Receiver <PERSON><PERSON>", "receiversHint": "You can typing multiple email address, place a comma to separate them, the limit of receivers is 5.", "reportSendingDate": "Report sending date", "reportSetting": "Report Setting", "reportType": "Report Type", "schedule": "Schedule", "scheduleReport": "Schedule Report", "scheduleSend": "Schedule Send", "scheduling": "Scheduling", "schedulingMode": "Scheduling Mode", "selectTargetDevices": "Select Target Devices", "setUpScheduleEmail": "Set Up Schedule Email", "showAll": "Show All Groups and Devices", "showAllDevices": "Show All Devices", "showSelected": "Show Selected Groups and Devices", "showSelectedDevices": "Show Selected Devices", "subject": "Subject", "subjectHint": "This field will be used as mail subject of schedule report.", "tileUrlErrorMessage": "Please check the user's browser can connect to the internet to access the map data", "timeZone": "Time Zone", "timeZoneHint": "Current time zone at {{ timeZone }}, user can modify in the report setting.", "timeZoneSetting": "Time Zone Setting", "timezoneSuccess": "Timezone apply successfully", "trail": "Trail", "trailMapErrorMessage": "There is no trail data on {{date}}", "trailMapMessage": "Trail (GPS location) is recorded every minute daily. Data from the past three months can be accessed for review.", "trailMessage": "The report will be sent at 0:30 on the selected date and will include the <strong>data</strong> for the <strong>previous date.</strong>", "updatedTime": "Update Date", "updateSuccess": "Report schedule updated successfully"}, "system": {"license": {"activate": "Activate", "activatedCode": "Activation code", "activatedTime": "Activate Time", "activateIntroPre": "Download the license from the ", "activateIntroSuf": ", and paste the Activation Code here.", "activationCode": "Activation Code", "addLicenseStep1": "Log in to the ", "addLicenseStep2": "Choose \"Activate a Product License\" and select \"{{ licenseTypeName }}\" as the product type.", "addLicenseStep3": "Enter the Registration Code and UUID on the Moxa License Site. The UUID can be obtained in the next step.", "addLicenseSuccess": "License added successfully", "addNewLicense": "Add New License", "availablePoints": "IPS Point Balance", "below": "Less than", "condition": "<PERSON><PERSON>", "copyDeactivateCode": "Copy Deactivation Code", "copyId": "Copy UUID", "copyIntro": "Copy and paste the UUID into the ", "createTime": "Create Date", "customized": "Custom", "customizedDeactivated": "Custom (Deactivated)", "customizedExpired": "Custom (Expired)", "daily": "Daily", "dailyConsumePoints": "Daily Point Usage", "days": "days", "daysRemaining": "days remaining", "deactivateCode": "Deactivation Code:", "deactivated": "Deactivated", "deactivateIPSLicenses": "Deactivate IPS Licenses", "deactivateIpsLicenses": "IPS License(s) deactivated successfully", "deactivateIPSLicensesContent1": "This will deactivate all current licenses and disable IPS pattern update functionality for affected devices. The deactivated licenses can be reactivated on a new server instance.", "deactivateIPSLicensesContent2": "Are you sure you want to continue?", "deactivateLicense": "Deactivate Licenses", "deactivationCode": "Deactivation Code", "depleted": "Depleted", "deviceIsOffline": "The device is offline.", "duration": "Valid for", "emailRecipient": "Email <PERSON>cipient", "enableNetworkSecurity": "Please enable the network security package on device", "endTime": "End Date", "estimatedPointsRunOutDate": "Estimated Point Depletion Date", "expiryDate": "Expiry Date", "firmwareVersion": "Firmware Version", "frequency": "Notification Frequency", "getNewLicense": "Request New License", "group": "Group", "history": "License History", "insufficientMsg": "The {{ category }} license has less valid nodes than the MXsecurity license. Please contact the administrator to confirm the license assignment.", "ips": "IPS", "ipsLicensedDevicesManagement": "IPS License Management", "ipsLicenseHaveBeenDeactivated": "No active license(s) to deactivate.", "ipsLicenseStatus": "IPS License Status", "ipsMgmt": "IPS-MGMT", "ipsNodeErrorMsg": "The number of bound devices ({{ devices }}) exceeds the number of nodes ({{ nodes }}) on the IPS license. Please unbind any unnecessary devices.", "license_awaiting_conversion": "Licenses Awaiting Conversion", "license_transfer": "License Transfer", "licenseAwaitingConversion": "Licenses Awaiting Conversion", "licenseBinding": "Device License Binding", "licenseDuration": "License Duration", "licenseExpiryNotification": "License Expiry Notifications\n", "licenseMigrate": "License Migration", "licenseNodes": "License Nodes", "licensePoints": "License Points", "licenseRunOutNotification": "License Depletion Notifications", "licenseSite": "Moxa License Site", "licenseStatus": "License Status", "licenseType": "License Type", "location": "Location", "loginLicenseSite": "Go to the Moxa License Site", "mac": "MAC", "monthly": "Monthly", "moxaLicenseSite": "Moxa License Site", "name": "Name", "navigatorSMTP1": "Email settings are not configured. Go to", "navigatorSMTP2": "System > Settings > Email", "navigatorSMTP3": "to configure the required settings.", "newUUID": "New UUID (from new server instance)", "newVersionNotSupport": "Please upgrade the firmware higher than {{version}}", "node": "Total Nodes", "nodeErrorMsg": "Insufficient valid nodes on the {{ category }} license. Please contact the administrator to confirm the license assignment.", "noIpsLicense": "No IPS license.", "noIpsLicensePoints": "All licenses are already depleted.", "noLicense": "No License", "not_transfer_license": "No license to transfer", "notSupportIPS": "This product does not support IPS", "notSupportModel": "Not support model", "notTransferLicense": "No license to transfer", "nsm": "MXsecurity", "nsmNodeErrorMsg": "The number of connected devices ({{ devices }}) exceeds the number of nodes ({{ nodes }}) on the MXsecurity license. Please disconnect any unnecessary devices.", "offlineDevice": "Offline device", "oldUUID": "Old UUID (from drop-down):", "oldVersionNotSupport": "Please transfer your license", "overview": "Overview", "reActivate": "Reactivate", "reactivateIPSLicensesTitle": "Reactivate IPS Licenses on New Instance", "reactivateLicenseStep2": "Go to \"Transfer a Product License\" and select \"IPS\" as the product type.", "reactivateLicenseStep3": "Provide the following information on the Moxa License Site:", "reactivateLicenseStep4": "and paste the Activation Code into the new server instance.", "reclaimed": "Reclaimed", "registerValidIPSLicenseInfo": "One or more of the selected devices have a device-based IPS license (IPS-DEVICE) assigned to it.\nClicking REGISTER will reclaim those local IPS licenses as centralized point-based licenses that are managed through the management software.", "remarkCvIpsExpiredMsg": "IPS license expired.", "remarkCvIpsExpiringMsg": "The IPS license will expire in {{ days }} days.", "remarkExpiredMsg": "The {{ category }} license has expired. To continue using all features, enter a valid license code.", "remarkExpiringMsg": "The {{ category }} license expires in {{ days }} days. To continue using all features, enter a new license code.", "remarkIpsExpiredMsg": "IPS license points depleted.", "remarkIpsExpiringMsg": "The total points of all IPS licenses will deplete in {{ days }} days.\n", "remarkUpdateFw": "Please update product firmware to {{version}} or higher for enhanced security and license management.", "removeDeviceLicense": "Deregister as IPS-licensed Device(s)", "removeLicenseDisabled": "One or more of the selected device(s) do not have a {{ category }} license applied to it", "removeLicenseMsg": "Are you sure you want to deregister the selected device(s) as IPS-licensed devices? This will reclaim the used IPS license points and disable IPS pattern update functionality on the device(s).", "removeLicenseSuccess": "License removed successfully", "securityGroup": "Group", "serialNumber": "Serial Number", "serverId": "UUID", "setDeviceLicense": "Register as IPS-licensed Device(s)", "setLicenseDisabled": "One or more of the selected device(s) already have a {{ category }} license applied to it", "setLicenseMsg": "Are you sure you want to register the selected device(s) as IPS-licensed devices? Each device will use 1 IPS license point per day.", "setLicenseSuccess": "License applied successfully", "shouldRemoveOnline": "Licenses can only be removed from online devices", "shouldSetOnline": "Licenses can only be applied to online devices", "showAllDevices": "Show All Devices", "showSelectedDevices": "Show Selected Devices", "standard": "Standard", "standardDeactivated": "Standard (Deactivated)", "standardRunOut": "Standard (Depleted)", "startTime": "Start Date", "status": "Status", "transferLicenseStep2": "Choose \"Migrate a Product License\" and select \"MXsecurity\" as the product type.", "transferLicenseStep3": "Enter the \"UUID\" on the Moxa License Site. The UUID can be obtained in the next step.", "transferLicenseSuccess": "License transferred successfully", "transferStatus": "Transfer  Status", "trial": "Trial", "trialDeactivated": "Trial (Deactivated)", "trialDepleted": "Trial (Depleted)", "type": "Type", "unlimited": "Unlimited", "update_fw": "Please update to FW {{version}} or higher", "updateDate": "Update Date", "updatedTime": "Update Date", "updateFw": "Please update to FW {{version}} or higher", "updateNotification": "Notification updated successfully", "uploadConfigFile": "Upload a license file (.csv)", "usedNode": "Used Nodes", "valid": "<PERSON><PERSON>", "validIpsDeviceInfo": "This device uses a device-based IPS license, which you can check by going to the device's web page. To reclaim, convert, and centrally manage the license, click the Register as IPS-licensed Device(s) icon.", "weekly": "Weekly"}, "settings": {"audit": "<PERSON>t Log", "auditLog": "<PERSON>t Log", "autoLogoutTime": "User Auto Logout After", "autoRefreshTime": "Dashboard Auto Refresh Interval", "availableLogs": "Available Logs", "availableLogsError": "You must select at least one log", "date": "Date", "device": "<PERSON><PERSON>", "deviceLog": "<PERSON><PERSON>", "diskUsage": "Disk Usage", "email": "Email", "emailAuthentication": "Email <PERSON>", "emailAuthWarningMsg": "Suggest enabling and setting up Email authentication when using SMTP service with Port 587.", "eventLogAutoRefresh": "Event Log Auto Refresh", "eventLogAutoRefreshTime": "Event Log Auto Refresh Interval", "firewall": "Firewall Log", "firewall_tooltip": "Entries counted by each firewall subcategory", "firewallLog": "Firewall Log", "firewallTooltip": "The log entry limit applies to each firewall subcategory separately.", "forceLogout": "The system time was changed by an administrator, please log in again.", "format": "Format", "free": "Free", "logPurge": "Log Purge", "mailServer": "SMTP Domain Name or IP address", "maxRetentionEntries": "Max Retention Entries", "maxRetentionTime": "Max Retention Time", "noLimit": "No limit", "otherFiles": "Others", "port": "Port", "preferences": "Preferences", "protocol": "Protocol", "purgeSetting": "Purge Settings", "receiverEmailAddress": "Receiver <PERSON><PERSON>dress", "secureConnection": "Secure Connection", "secureConnectWarningMsg": "Suggest enabling secure connection when using SMTP service with Port 587.", "senderEmailAddress": "Sender Email Address", "sendLogsToAddress": "Send logs to Syslog Server", "sendTestEmailSuccess": "Successfully send Em<PERSON>", "sendTestSyslog": "SEND TEST SYSLOG", "sendTestSyslogSuccess": "Successfully send syslog", "serverAddress": "Server Address/ Domain Name", "smtpServerPort": "SMTP Server Port", "syslog": "Syslog", "tcp": "TCP", "testEmail": "Test Email", "TestEmailBtn": "TEST EMAIL", "time": "Time", "udp": "UDP", "updateEmailSuccess": "Email updated successfully", "updateLogAutoRefreshSuccess": "Event Log Auto Refresh updated successfully", "updateLogPurgeSuccess": "Log purge settings updated successfully", "updatePreferenceSuccess": "Preference updated successfully", "updateSyslogSuccess": "Syslog updated successfully", "updateTimeMsg": "Changing the time will automatically log you out.", "updateTimeSuccess": "System time updated successfully", "usageSummery": "{{ free }} MB free of {{ total }} MB", "vpn": "VPN Log", "vpnLog": "VPN Log"}, "userAccount": {"accountList": "Account List", "confirmPassword": "Confirm Password", "createSuccess": "New user created successfully", "createUser": "Create User", "deleteMsg": "Are you sure you want to delete the selected user(s)?", "deleteSuccess": "User(s) deleted successfully", "deleteTitle": "Delete User(s)", "description": "Description", "editUser": "Edit User", "lastLogin": "Last Login", "minLength": "Minimum Length", "password": "Password", "passwordPolicy": "Password Policy", "passwordRange": "8 to 32 characters", "roleName": "Role", "updatePolicySuccess": "Password policy updated successfully", "updateSuccess": "User updated successfully", "username": "Username"}}}, "validators": {"duplicateEmail": "There has duplicate emails", "excludeLastPassword": "The new password cannot be the same as the last password", "excludeUserName": "Cannot include the username", "invalid": "Invalid character(s)", "invalid_date": "Invalid Date", "invalid_format_allow_space": "Only a maximum of {{count}} spaces allowed in the string", "invalidDate": "Invalid Date", "invalidEmail": "Invalid email", "invalidFormatAllowSpace": "Only a maximum of {{count}} spaces allowed in the string", "invalidIpAddress": "Invalid IP address", "invalidIpAddressOrDomainName": "Invalid IP address or Domain Name", "invalidLocation": "Special allow (-_@!#$%^&*().,/)", "invalidMacAddress": "Invalid MAC address", "invalidMacAddressAllZero": "MAC address 00:00:00:00:00:00 is reserved", "invalidSeverity": "Invalid Severity", "ipRangeError": "IP Address: End needs to be greater than IP Address: Start", "isExist": "already exists", "isExistOrUsedByOtherUser": "already exists or used by other user", "maxReceiverSize": "The MAX. receivers is {{num}}.", "needDigit": "Must include at least one digit (0 - 9)", "needGreaterThan": "{{ largeItem }} needs to be greater than {{ smallItem }}", "needLowerCase": "Must include at least one lowercase character (a - z)", "needSpecialCharacter": "Must include at least one special character (~!@#$%^&*_-+=`|\\(){}[]:;”’<>,.?/)", "needUpperCase": "Must include at least one uppercase character (A - Z)", "notMeetPolicy": "Does not meet the password policy requirements", "portRangeError": "Port: End needs to be greater than Port: Start", "pwdNotMatch": "Password does not match", "range": "Invalid range ({{ min }} ~ {{ max }})", "required": "Required", "requireMaxLength": "Must be no more than {{ number }} characters long", "requireMinLength": "Must be at least {{ number }} characters long"}}