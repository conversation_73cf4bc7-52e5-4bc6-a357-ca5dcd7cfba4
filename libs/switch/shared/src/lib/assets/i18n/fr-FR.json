{"__readme__": {"__1__": "All key naming is snake_case, in addition to using uri key, e.g.'ospfRRDStatic'", "__2__": "In 'common', words are used repeatedly. It can be reclassified and named 'common_xxx', e.g.'common_port', 'common_account'...", "__3__": "In 'dialog', words are used repeatedly in the dialog.", "__4__": "In 'button', put all button words in and must be capitalized"}, "login": {"title_login_records": "Historique de connexion", "redirected_message": "Déconnexion réussie", "login_success_records_greeting": "Bienvenue {{ name }}", "login_success_records_datetime": "La dernière connexion réussie a eu lieu à {{ time }}.", "login_fail_records": "Les derniers enregistrements d'échecs de connexion.", "modify_password_notification": "Veuillez changer le nom d'utilisateur et le mot de passe par défaut pour renforcer la sécurité.", "password_expire_notification": "Votre mot de passe a expiré. Veuillez changer votre mot de passe.", "daylight_saving_upgrade": "La fonctionnalité de l'heure d'été a été mise à jour.", "daylight_saving_notification": "Veuillez mettre à jour vos configurations.", "factory_default_note": "Veuillez noter que vous devez utiliser les paramètres réseau par défaut pour rétablir une connexion avec votre commutateur via le navigateur web.", "firmware_upgrade_note": "Veuillez noter que vous devez rétablir une connexion avec votre commutateur via le navigateur web."}, "general": {"top_nav": {"config_change": {"start_up_save_tip": "Les configurations n'ont pas été enregistrées dans les configurations de démarrage.", "confirmation_message": "Êtes-vous sûr de vouloir appliquer les configurations en cours aux configurations de démarrage?", "title_apply_to_start_config": "Appliquer aux configurations de démarrage"}, "user_profile": {"greeting": "<PERSON><PERSON><PERSON>, {{ username }}", "enable_auto_save": "Activer l'enregistrement automatique", "disable_auto_save": "Désactiver l'enregistrement automatique", "disable_auto_save_hint": "Une fois appliquées, les configurations seront enregistrées dans les configurations en cours d'exécution au lieu des configurations de démarrage.", "change_language": "Changer de langue", "change_mode": "Changer de mode", "locator": "Localisateur", "reset_factory_default": "Réinitialiser aux paramètres par défaut", "save_custom_default": "Enregistrer les paramètres par défaut personnalisés", "standard_mode": "Standard", "advanced_mode": "<PERSON><PERSON><PERSON>", "standard_mode_tooltip": "Mode standard: Certaines fonctionnalités/paramètres seront masqués pour simplifier les choses.", "advanced_mode_tooltip": "Mode avancé: Les fonctionnalités/paramètres avancés seront disponibles pour les utilisateurs qui souhaitent ajuster ces paramètres."}, "auto_save_mode": {"enable_auto_save_title": "Activer le mode d'enregistrement automatique", "enable_auto_save_msg": "Êtes-vous sûr de vouloir activer le mode d'enregistrement automatique?", "disable_auto_save_title": "Désactiver le mode d'enregistrement automatique", "disable_auto_save_msg": "Êtes-vous sûr de vouloir désactiver le mode d'enregistrement automatique?"}, "advanced_browse_mode": {"advanced_notification_title": "Passer en mode avancé", "advanced_notification_msg": "Êtes-vous sûr de vouloir passer du mode Standard au mode Avancé?", "basic_notification_title": "Passer en mode standard", "basic_notification_msg": "Êtes-vous sûr de vouloir passer du mode avancé au mode standard?"}, "locator": {"title_switch_locator": "Localisateur de commutateur", "duration": "<PERSON><PERSON><PERSON>", "hint": "Déclenchez les LEDs pour qu'ils commencent à clignoter sur l'appareil afin de faciliter sa localisation."}, "restart_machine": {"confirmation_title": "<PERSON><PERSON><PERSON><PERSON>", "confirmation_msg": "Êtes-vous sûr de vouloir redémarrer l'appareil?"}, "factory_default": {"confirmation_title": "Paramètres par défaut d'usine", "confirmation_msg": "Êtes-vous sûr de vouloir réinitialiser la configuration du système aux paramètres d'usine?", "factory_default_hint": "La réinitialisation aux paramètres d’usine par défaut effacera les configurations par défaut personnalisées.", "custom_default": "Personnalisé par défaut", "confirmation_msg_custom_default": "Voulez-vous vraiment réinitialiser les configurations système à la valeur par défaut personnalisée?", "custom_default_not_exist": "La valeur par défaut personnalisée ne peut PAS être exécutée lorsque son état est \"Aucune configuration trouvée !\".", "saved_config_name": "Nom de la configuration enregistrée", "config_name_hint": "Le nom de la configuration par défaut personnalisée est enregistré dans la mémoire non volatile.", "clear_all_config": "Supprimer les configurations, les fichiers journaux et les clés d'authentification"}, "save_custom_default": {"confirmation_msg": "Voulez-vous vraiment enregistrer la configuration de démarrage en tant que configuration personnalisée par défaut?", "current_config_name": "Nom de la configuration actuelle", "config_name_hint": "Le nom de la configuration peut être modifié sur la page \"Config Backup and Restore\"."}, "logout": {"confirmation_title": "Se déconnecter", "confirmation_msg": "Êtes-vous sûr de vouloir vous déconnecter?"}}, "page_state": {"page_not_found": "Page non trouvée", "page_not_found_desc": "L'URL demandée est introuvable sur ce serveur.", "back_link": "Retour à la page d'accueil"}, "menu_tree": {"jump_page_placeholder": "Rechercher une fonction", "system": "Système", "system_management": "La gestion du système", "account_management": "Gestion de compte", "provisioning": "Approvisionnement", "port_interface": "Interface de port", "l2_switching": "Commutation de couche 2", "unicast_route": "Route de monodiffusion", "multicast_route": "Route multidiffusion", "mac": "MAC", "qos": "QoS", "redundancy": "Redondance", "l2_redundancy": "Redondance de couche 2", "l3_redundancy": "Redondance de couche 3", "network_service": "Service réseau", "routing": "Routage", "network_management": "La gestion du réseau", "device_security": "Sécurité de l'appareil", "network_security": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "diagnostics": "Diagnostique", "network_status": "L'état du réseau", "tools": "Outils", "log_and_event_notification": "Journaux d'événements et notifications", "application": "Application industrielle", "iec61850": "IEC 61850", "iec62439_3": "IEC 62439-3"}, "dialog": {"title_refresh_browser": "Actualiser le navigateur", "title_change_default_password": "Modifier le mot de passe par défaut", "title_change_password": "Changer le mot de passe", "title_session_expired": "La session a expiré", "title_notification": "Notification", "title_edit_interface": "Modifier l'interface {{ interfaceName }}", "edit_port_msg": "Modifier le port {{ portIndex }} Paramètres", "edit_vlan_msg": "Modifier le VLAN {{ vlanIndex }} Paramètres", "create_entry_msg": "<PERSON><PERSON><PERSON> une entrée", "edit_entry_msg": "Modifier les paramètres d'entrée", "delete_entry_msg": "Supprimer l'entrée", "title_delete_key": "Supprimer le certificat et la clé", "title_delete_account": "Supprimer le compte", "delete_entry_confirm_desc": "Êtes-vous sûr de vouloir supprimer l'entrée sélectionnée?", "title_select_file": "Sélectionner un fichier", "title_device_unplugged": "{{ device }} est débranché", "desc_device_unplugged": "Veuillez vérifier si le {{ device }} est branché.", "redirect_to_ip": "Rediriger vers {{ ipAddress }}", "page_redirect_content": "La page sera redirigée après 5 secondes.", "redirect_failed_hint": "Si la redirection échoue, veuillez vérifier vos paramètres réseau.", "after_seconds": "Après {{ second }} secondes", "redirecting": "Redirection...", "title_choose_tracking_id": "Choisissez un identifiant de suivi"}, "common": {"network": "<PERSON><PERSON><PERSON>", "enable_header": "Activer", "enabled": "Enabled", "disable_header": "Désactiver", "disabled": "Disabled", "none": "None", "authentication": "Authentification", "active": "Active", "inactive": "Inactif", "passive": "Passive", "ip": "IP", "ip_address": "Adresse IP", "mac": "MAC", "mac_address": "<PERSON><PERSON><PERSON>", "server_address": "Adresse IP du serveur", "subnet_mask": "Masque de sous-rés<PERSON>", "domain_name": "Nom de domaine", "ip_or_domain": "Adresse IP/Nom de domaine", "general": "Général", "normal": "Normal", "type": "Type", "mode": "Mode", "yes": "O<PERSON>", "no": "Non", "auto": "Auto", "user_defined": "User Defined", "valid": "Valide", "invalid": "Invalide", "required": "Requis", "version": "Version", "unknown": "Inconnu", "read_only": "Read Only", "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reset": "Réinitialiser", "add": "Ajouter", "delete": "<PERSON><PERSON><PERSON><PERSON>", "export": "Exporter", "up": "En haut", "down": "Bas", "index": "Index", "name": "Nom", "method": "Méthode", "file_name": "Nom de fi<PERSON>er", "file_server": "<PERSON><PERSON><PERSON> <PERSON>", "select_file": "Sélectionner un fichier", "action": "Action", "authority": "Autorité", "any": "Any", "all": "Tous", "unselect": "Unselect", "settings": "Paramètres", "status": "Statut", "local": "Local", "usb": "USB", "usb_hint": "Le port USB est compatible avec le configurateur de sauvegarde automatique ABC-02.", "usb_hw_disabled_hint": "La clé USB n'est pas valide car la fonction de stockage externe est désactivée.", "micro_sd": "microSD", "micro_sd_hw_disabled_hint": "La microSD n'est pas valide car la fonction de stockage externe est désactivée.", "location": "Emplacement", "time": "Temps", "start_date": "Date de début", "start_time": "<PERSON><PERSON> d<PERSON>", "end_time": "Heure de fin", "timeout": "Temps libre", "interface": "Interface", "threshold": "<PERSON><PERSON>", "broadcast": "Diffuser", "multicast": "Multidiffusion", "algorithm": "Algorithme", "manual": "Manual", "master": "<PERSON><PERSON><PERSON>", "priority": "Priorité", "permanent": "Permanent", "queue": "File d'attente", "netmask": "Ma<PERSON> de <PERSON>", "backup": "<PERSON><PERSON><PERSON><PERSON>", "backup_en": "Backup", "restroe": "<PERSON><PERSON><PERSON>", "broken": "Endommagé", "learning": "Apprentissage", "listening": "Écoute", "discarding": "<PERSON><PERSON>", "forwarding": "Expéditeur", "blocking": "Blocage", "packets": "Paquets", "notice": "Notice", "warning": "Warning", "critical": "Critical", "error": "Error", "security": "Sécurité", "slave": "Esclave", "slot": "Fen<PERSON>", "simple": "Simple", "state": "État", "subtype": "Sous-type", "protocol": "Protocole", "init": "Initié", "retry": "Recommencez", "severity": "Gravité", "destination": "Destination", "description": "Description", "distance": "Distance", "expired_date": "Date d'expiration", "delay_time": "Temporisation", "serial_number": "Numéro de série", "product_revision": "Révision du produit", "quick_setting": "Réglages rapides", "admin_status": "Statut administrateur", "link_status": "État du lien", "system_status": "État du système", "link_up": "Re<PERSON>", "link_down": "Lien vers le bas", "point_to_point": "Point-to-point", "ethertype": "EtherType", "auth_type": "Type d'authentification", "authentication_type": "Type d'identification", "default_gateway": "Passerelle par défaut", "encryprion_key": "Clé de cryptage", "encryption_method": "<PERSON><PERSON><PERSON><PERSON> de chiffrement", "authentication_password": "Mot de passe d'authentification", "server_ip_address": "Adresse IP du serveur", "key": "Clé", "key_id": "ID de clé", "vlan_id": "ID VLAN", "vlan_vid": "VLAN {{ vid }}", "vlan_list_info": "Plusieurs VLAN peuvent être définis et doivent être saisis sous la forme d'un nombre unique ou d'une plage, par exemple 2, 4-8, 10-13.", "share_key": "Partager la clé", "share_key_hint": "Après avoir quitté cette page ou actualisé, la clé de partage sera automatiquement effacée pour améliorer la sécurité.", "auto_backup": "Sauvegarder automatiquement", "auto_backup_hint": "Sauvegarder sur un stockage externe lorsque les configurations changent.", "dynamic": "Dynamique", "static": "Statique", "ref_manual_hint": "Veuillez vous référer au manuel de l'utilisateur pour plus d'informations sur ce paramètre.", "default": "<PERSON><PERSON><PERSON>", "other": "Other", "trusted": "Trusted", "untrusted": "Untrusted", "infinite": "Infini", "source": "Source", "low": "Faible", "high": "<PERSON><PERSON>", "connected": "Connecté", "disconnected": "Débranché", "incorrect_connected": "Connexion incorrecte", "set_event_notifications": "Définir les notifications d'événements", "include": "Inclure", "exclude": "Exclure", "immediate": "Immediate", "never": "Never", "interface_name": "Nom de l'interface", "interface_alias": "Alias ​​d'interface", "hello_interval": "Bonjour intervalle", "neighbor": "Voisin", "current": "Actuel", "ping": "<PERSON>", "logical": "Logique", "interval": "Intervalle", "route": "Itinéraire", "open": "Open", "short": "Short", "role": "Role", "speed": "Vitesse", "failed": "Échec", "successful": "<PERSON><PERSON><PERSON><PERSON>", "idle": "Inactif", "protection": "Protection", "pending": "En attente", "supported": "<PERSON><PERSON><PERSON>", "not_supported": "Non pris en charge", "import": "Importer", "propagate": "Propager", "ascii": "ASCII", "hex": "HEX", "zone": "Zone"}, "common_account": {"account": "<PERSON><PERSON><PERSON>", "username": "Nom d'utilisateur", "pwd_mask": "********", "password": "Mot de passe", "confirm_password": "Confirmez passe", "email": "Email", "delete_account_desc": "Êtes-vous sûr de vouloir supprimer le compte sélectionné?"}, "common_abbrev": {"md5": "MD5", "sha": "SHA", "sha-224": "SHA-224", "sha-256": "SHA-256", "sha-384": "SHA-384", "sha-512": "SHA-512", "sftp": "SFTP", "tftp": "TFTP", "pap": "BOUILLIE", "chap": "TYPE", "cos": "CoS", "dscp": "DSCP", "cir": "CRI", "cir_full": "Taux d'Information Engagé", "cbs": "CBS", "cbs_full": "Taille de rafale engagée"}, "common_port": {"port": "Port", "ports": "Ports", "port_name": "Port {{ portName }}", "tcp_port": "Port TCP", "udp_port": "Port UDP", "port_state": "État du port", "port_status": "État des ports", "port_settings": "Paramètres des ports", "port_shutdown": "Port Shutdown", "destination_port": "Le port de destination", "reflect_port": "Port de réflexion", "all_port": "All Ports", "member_port": "Port membre", "also_apply_port": "Copier les configurations sur les ports", "also_apply_port_hint": "<PERSON><PERSON>z les configurations sur les ports que vous sélectionnez dans la liste déroulante.", "ingress_port": "Ingress Port"}, "button": {"cancel": "ANNULER", "apply": "APPLIQUER", "create": "CRÉER", "edit": "MODIFIER", "delete": "SUPPRIMER", "sync_from_browser": "SYNC DEPUIS LE NAVIGATEUR", "regenerate": "RÉGÉNÉRER", "import": "IMPORTER", "export": "EXPORTER", "expand": "DÉVELOPPER", "collapse": "EFFONDREMENT", "copy": "COPIER", "close": "FERMER", "download": "TÉLÉCHARGER", "ping": "PING", "clear": "EFFACER", "backup": "SAUVEGARDE", "restore": "RESTAURER", "upgrade": "AMÉLIORER", "reset": "RÉINITIALISER", "locate": "LOCALISER", "disable": "DÉSACTIVER", "enable": "ACTIVER", "change": "CHANGEMENT", "logout": "SE DÉCONNECTER", "reboot": "REDÉMARRER", "confirm": "CONFIRMER", "remove": "RETIRER", "back": "DOS", "log_in": "CONNEXION", "re_auth": "RE-AUTH", "recovery": "RÉCUPÉRATION", "export_cid_file": "EXPORTER FICHIER CID", "export_server_ca": "EXPORTATION SERVEUR CA", "change_password": "CHANGER LE MOT DE PASSE", "export_server_cert": "CERTIFICAT DE SERVEUR D'EXPORTATION", "view_all_event_logs": "AFFICHER TOUS LES JOURNAUX D'ÉVÉNEMENTS", "update_daylight_saving": "MISE À JOUR HEURE D'ÉTÉ", "select": "SÉLECTIONNER", "retry": "RECOMMENCEZ", "encrypt": "CRYPTER", "change_cak": "CHANGER CKN ET CAK", "save": "SAUVEGARDER", "find_rp": "TROUVER RP", "cut_off": "COUPER", "go_to": "ALLER À", "got_it": "COMPRIS"}, "tooltip": {"edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON>", "copy_config": "<PERSON><PERSON><PERSON> les <PERSON>", "close": "<PERSON><PERSON><PERSON>", "clear": "<PERSON>", "clear_graph": "Effacer le graphique", "remove": "<PERSON><PERSON><PERSON>", "reorder": "Réorganiser", "recovery": "Récupération", "select_tracking": "Sélectionner l'ID de suivi", "remove_tracking": "Supprimer l'ID de suivi", "vlan_size_limitation": "Cet appareil permet uniquement {{ size }} VLAN.", "size_limitation": "Le nombre maximum de comptes d'utilisateurs pour cet appareil est {{ size }}.", "reorder_priority": "Réorganiser la priorité", "reorder_finish": "Réorganisation terminée", "export_pdf_all_acl": "Exporter tous les ACL en PDF", "export_pdf_only_this_port": "Exporter uniquement les informations de ce port au format PDF", "export_pdf_only_this_vlan": "Exporter uniquement les informations de ce VLAN au format PDF", "clear_counter": "<PERSON><PERSON><PERSON><PERSON> le compteur", "unicast_mac_address_hint": "Autoriser uniquement l'adresse MAC de monodiffusion.", "disable_la_member_hint": "Il s'agit d'un port membre d'un groupe d'agrégation de liens.", "duplicate_redundant_port": "Impossible de dupliquer les ports redondants", "auto_refresh_enabled": "Actualisation automatique: Activé", "auto_refresh_disabled": "Actualisation automatique: Désactivé", "manually_refersh_disabled": "Impossible d'actualiser manuellement si 'Actualisation automatique' est activé", "integration_3A_3B_hint": "Ce port est une intégration de 3/A et 3/B.", "set_to_static": "Définir en statique"}, "unit": {"fps": "fps", "min": "min.", "sec": "seconde.", "ms": "ms", "byte": "octet", "m": "M", "kbyte": "Ko", "mbps": "Mbps", "w": "W", "watt": "<PERSON>", "watts": "watts", "percent": "%", "day": "jour", "times": "fois", "mb": "Mo", "ns": "ns", "microsecond": "µs", "ppm": "PPM", "packets": "paquets", "v": "V", "dbm": "dBm", "c": "°C", "f": "°F", "nm": "nm"}, "speed": {"10m": "10M", "100m": "100M", "1g": "1G", "10g": "10G", "40g": "40G", "56g": "56G", "2500m": "2500M", "full": "Full", "half": "Half"}, "table_function": {"filter_desc": "Recherche", "export_pdf": "Exporter PDF", "export_csv": "Exporter CSV", "selected_count": "<PERSON><PERSON>", "row_count": "Total", "limit_count": "<PERSON>."}, "led": {"status_unknown_hint": "Statut inconnu.", "state_green_on": "Fonctionnement normal.", "state_green_blink4hz": "Le système démarre.", "state_red_on": "Le système n'a pas réussi à s'initialiser.", "state_off": "Le système est éteint.", "fault_red_on": "Le système a rencontré une panne, veuillez vérifier le journal système pour plus de détails.", "fault_red_blink4Hz": "Le commutateur a démarré et le micrologiciel a été chargé dans la mémoire.", "fault_off": "Fonctionnement normal.", "mh_turbo_chain_head_green_on": "Le commutateur est la tête de la Turbo Chain.", "mh_turbo_ring_green_on": "Le commutateur est le maître de Turbo  Anneau 1 ou de Turbo Ring 2.", "mh_mrp_green_on": "Le commutateur est le gestionnaire de l'anneau MRP.", "mh_turbo_ring_green_blink2hz_mds": "Le commutateur est le maître du Turbo Ring 1 ou du Turbo Ring 2, et au moins un des anneaux est défectueux.", "mh_turbo_chain_head_green_blink2hz_mds": "Le commutateur est à la tête de la  Turbo Chain et la chaîne est défectueuse.", "mh_mrp_green_blink2hz_mds": "Le commutateur est le gestionnaire de l'anneau MRP et l'anneau est ouvert.", "mh_turbo_chain_member_green_blink2hz_mds": "Le commutateur est membre de la Turbo Chain, et le port membre 1 correspondant est non-opérationnel.", "mh_turbo_chain_tail_green_blink2hz_mds": "Le commutateur est à la queue de la Turbo Chain, et le port membre correspondant est non opérationnel.", "mh_turbo_ring_green_blink4hz": "Le commutateur est le maître du Turbo Ring 1 ou du Turbo Ring 2, et au moins un des anneaux est défectueux.", "mh_turbo_chain_head_green_blink4hz": "Le commutateur est à la tête de la Turbo Chain, et la chaîne est cassée.", "mh_mrp_green_blink4hz": "Le commutateur est le gestionnaire de l'anneau MRP et l'anneau est ouvert.", "mh_turbo_chain_member_green_blink4hz": "Le commutateur est membre de la Turbo Chain, et le port membre 1 correspondant est non-opérationnel.", "mh_turbo_chain_tail_green_blink4hz": "Le commutateur est à la queue de la Turbo Chain, et le port membre 1 correspondant est non opérationnel.", "mh_off": "Le commutateur n'est ni le maître du Turbo Ring 1 ou du Turbo Ring 2, ni à la tête de la Turbo Chain, ni le gestionnaire de l'anneau MRP.", "ct_turbo_ring_green_on": "La fonction de couplage du commutateur est activée pour établir un chemin de secours.", "ct_mrp_green_on": "Le commutateur active une fonction de couplage pour créer un chemin de secours.", "ct_turbo_chain_tail_green_on": "Le commutateur est la queue de Turbo Chain.", "ct_dual_homing_green_on": "La fonction de dual homing du commutateur est activée.", "ct_turbo_ring_dual_homing_green_on": "Les fonctions de coupling et de dual homing d'origine du commutateur sont activées.", "ct_turbo_chain_dual_homing_green_on": "La fonction de  dual homing du commutateur est activée et le commutateur est la queue de la chaîne Turbo.", "ct_mrp_dual_homing_green_on": "Les fonctions MRP connection et dual homing du commutateur sont activées.", "ct_turbo_chain_head_green_blink2hz_mds": "Le commutateur est à la tête de la Turbo Chain, et le port membre correspondant est non opérationnel.", "ct_turbo_chain_member_green_blink2hz_mds": "Le commutateur est membre de la Turbo Chain, et le port membre 2 correspondant est non opérationnel.", "ct_turbo_chain_tail_green_blink2hz_mds": "Le commutateur est à la queue de la Turbo Chain, et la chaîne est défectueuse.", "ct_turbo_chain_head_green_blink4hz": "Le commutateur est à la tête de la Turbo Chain, et le port membre correspondant est non opérationnel.", "ct_turbo_chain_member_green_blink4hz": "Le commutateur est membre de la Turbo Chain, et le port membre 2 correspondant est non opérationnel.", "ct_turbo_chain_tail_green_blink4hz": "Le commutateur est à la queue de la Turbo Chain, et la chaîne est cassée.", "ct_off": "La fonction de couplage ou de double homing du commutateur est désactivée, ou le commutateur n’est pas à la queue de la Turbo Chain.", "sync_amber_on": "La fonction PTP est activée.", "sync_amber_blink4hz": "Le commutateur a reçu des paquets de synchronisation.", "sync_green_on": "La fonction PTP a convergé avec succès.", "sync_off": "La fonction PTP est désactivée.", "ms_green_on": "Fonctionnement normal.", "ms_green_blink2hz": "Ce module démarre.", "ms_off": "Le module est hors service.", "ms_red_on": "Le module n'a pas réussi à s'initialiser ou l'utilisateur a inséré le mauvais module.", "eps_amber_on": "L'alimentation externe est prête à alimenter le port PoE.", "eps_off": "Il n'y a pas d'alimentation externe pour le périphérique PoE.", "pwr_eps_amber_on": "L'alimentation externe est fournie à l'entrée EPS du module.", "pwr_eps_off": "Il n'y a pas d'alimentation externe pour le périphérique PoE.", "pwr_amber_on": "L'alimentation est fournie à l'entrée d'alimentation du module.", "pwr_off": "L'alimentation n'est pas fournie à l'entrée d'alimentation du module.", "port_poe_green_on": "Le port est connecté à un périphérique alimenté IEEE 802.3at (PD).", "port_poe_green_on_poebt": "Le port est connecté à un périphérique alimenté par IEEE 802.3bt (PD).", "port_poe_amber_on": "Le port est connecté à un périphérique alimenté par IEEE 802.3af (PD).", "port_poe_amber_on_poebt": "Le port est connecté à un périphérique alimenté IEEE 802.3af/at (PD).", "port_poe_amber_blink4hz": "L'alimentation PoE a été coupée car le budget d'alimentation n'est pas suffisant.", "port_poe_red_on": "Échec de détection d'appareil alimenté (PD).", "port_poe_red_blink4hz": "Une surintensité ou un court-circuit a été détecté sur l'appareil alimenté (PD).", "port_poe_off": "L'alimentation n'est pas fournie à l'appareil alimenté (PD).", "port_link_up_hint": "Le port est actif et se connecte sur {{ operSpeed }}bps.", "port_link_down_hint": "Le port est inactif ou la liaison est interrompue.", "prp_green_on": "La fonction PRP est activée.", "prp_off": "La fonction PRP est désactivée.", "hsr_green_on": "La fonction HSR est activée.", "hsr_off": "La fonction HSR est désactivée.", "coup_green_on": "La fonction Coupling est activée.", "coup_off": "La fonction Coupling est désactivée."}}, "features": {"storm_control": {"page_title": "Contrôle des tempêtes de trafic", "dlf": "DLF"}, "la": {"page_title": "Agrégation de liens", "port_channel": "Port Channel (Trunk)", "wait_time": "Temps d'attente", "configure_member": "Configurer le membre", "active_member": "Membre actif", "la_group_status": "Statut du groupe LA", "lacp": "LACP", "smac": "SMAC", "dmac": "DMAC", "smac_dmac": "SMAC + DMAC", "config_member_port": "config le port membre", "config_member_port_hint": "Conservez au moins un port qui ne peut pas être ajouté à un Port Channel.", "delete_port_channel_confirm_desc_1": "Avertissement: ", "delete_port_channel_confirm_desc_2": "Certaines fonctionnalités (telles que RSTP et VLAN) liées à l'agrégation de liens sélectionnée seront définies sur les valeurs par défaut.", "delete_port_channel_confirm_desc_3": "Êtes-vous sûr de vouloir supprimer l'agrégation de liens sélectionnée?", "la_size_limitation": "Cet appareil permet uniquement {{ size }} canaux de port.", "create_la_msg": "Créer une agrégation de liens", "edit_la_pre_msg": "Modifier le canal de port {{ portChannel }} Paramètres", "delete_la_msg": "Supprimer l'agrégation de liens", "only_select_eight": "Un maximum de huit peut être sélectionné."}, "scheduler": {"page_title": "Planificateur", "strict_priority": "Strict Priority", "weight_round_robin": "Weighted Round Robin", "sp": "SP", "wrr": "WRR", "wfq": "WFQ"}, "egress_shaper": {"page_title": "Egress <PERSON>", "egress_rate": "Egress Rate (CIR)"}, "rate_limit": {"page_title": "<PERSON>ite de débit d'entrée", "ingress_rate": "Ingress Rate (CIR)", "ebs": "EBS", "ebs_full": "Excess <PERSON><PERSON><PERSON>", "conform_action": "Conform Action", "exceed_action": "Exceed Action", "violate_action": "Violate Action", "blind": "Color Blind", "aware": "Color Aware", "do_nothing": "Do Nothing", "drop": "Drop", "remark_cos": "Remark CoS", "remark_dscp": "Remark DSCP", "simple_token_bucket": "Simple Token Bucket", "sr_tcm": "SrTCM", "remark_value": "Remark Value", "release_interval": "Release Interval", "rate_limit_port_shutdown": "Rate Limit Port Shutdown"}, "classification": {"page_title": "Classification", "cos_priority": "CoS Priority", "preference_type": "Trust Type", "dhcp_mapping": "DSCP Mapping", "cos_mapping": "CoS Mapping", "untag_default_priority": "Untag Default Priority", "edit_dscp_msg": "Edit DSCP {{ dscpIndex }} Settings", "edit_cos_msg": "Edit CoS {{ cosIndex }} Settings"}, "linkup_delay": {"page_title": "<PERSON><PERSON><PERSON>", "remaining_time": "Temps restant"}, "port_mirror": {"page_title": "Port Mirroring", "span": "SPAN", "rspan": "RSPAN", "session_id": "Session ID", "reflect_port_mode": "Reflect Port Mode", "rspan_type": "RSPAN Type", "rspan_vid": "RSPAN VLAN ID", "rspan_settings": "RSPAN Intermediate Settings", "rspan_setting_hint": "Assurez-vous que tous les ports utilisés pour la communication RSPAN sont ajoutés au VLAN RSPAN approprié", "rspan_vlan_setting_hint": "Pour éviter que les trames balisées ne soient abandonnées, ne sélectionnez comme port Reflect qu'un port VLAN en mode Trunk ou Hybrid.", "duplicate_intermediate_vlan": "Cet ID de VLAN intermédiaire est déjà utilisé.", "rspan_role": "RSPAN Intermediate Role", "rspan_intermediate_vid1": "RSPAN Intermediate 1st VLAN ID", "rspan_intermediate_vid2": "RSPAN Intermediate 2nd VLAN ID", "enable_rspan_title": "Activer le rôle intermédiaire RSPAN", "enable_rspan_warning": "Si ce paramètre est appliqué, toutes les sessions RSPAN existantes seront supprimées", "rx_source_port": "Rx Source Port(s)", "tx_source_port": "Tx Source Port(s)", "designated_port": "Port désigné", "destination_ports": "Port(s) de destination", "destination_port_info": "Pour les ports d'accès, le PVID du port sera défini sur l'ID du VLAN RSPAN. \nPour les ports hybrides ou trunk, le port deviendra membre du VLAN RSPAN", "destination_port_hint": "Les ports de destination seront ajoutés au VLAN RSPAN.", "destination_ports_or_designated_port": "Port(s) de destination ou port désigné", "source_port_two_field_invalid": "<PERSON><PERSON> devez sélectionner soit le port source Tx, soit le port source Rx.", "create_mirror_msg": "<PERSON><PERSON><PERSON> une session", "edit_mirror_msg": "Modifier les paramètres de la session {{ sessionIndex }}", "select_tx_or_rx_hint": "Soit les ports sources TX, soit les ports sources RX doivent être sélectionnés.", "is_not_access_port": "Ce port n'est pas un port d'accès.", "is_not_trunk_port": "Ce port n'est pas un port de trunk.", "source_port_must_be_access_port": "Le port source doit être un port d’accès lorsque le mode Port Reflect est activé.", "reflect_port_must_be_access_port": "Le port Reflect doit être un port d’accès lorsque le mode Port Reflect est activé.", "reflect_port_must_be_trunk_hybrid_port": "Le port Reflect doit être un port trunk/hybride lorsque le mode Port Reflect est activé.", "pvid_is_not_rspan_vid": "Le PVID de ce port n'est pas le VLAN RSPAN.", "rspan_source_session_exist": "Une session source RSPAN existe déjà.", "rspan_destination_session_exist": "Une session de destination RSPAN existe déjà.", "rspan_cannot_create": "Une session RSPAN ne peut pas être créée lorsque le rôle intermédiaire RSPAN est activé.", "session_span_size_limitation": "Le nombre maximum d'entrées SPAN est {{ size }}.", "session_rspan_size_limitation": "Le nombre maximum d'entrées RSPAN est {{ size }}.", "delete_session_title": "Supprimer la session", "delete_session_content": "Êtes-vous sûr de vouloir supprimer la session sélectionnée?", "rspan_vid_hint_l2": "L’utilisation du VLAN de gestion ou des VLAN configurés pour l’attribution de VLAN pour RSPAN n’est pas recommandée.", "rspan_vid_hint_l3": "L’utilisation d’interfaces VLAN ou de VLAN configurés pour l’attribution de VLAN pour RSPAN n’est pas recommandée. "}, "vlan": {"page_title": "VLAN", "vlan": "VLAN", "global": "Mondial", "management_vlan": "VLAN de gestion", "management_port": "Port de gestion", "mgmt_vlan_settings": "Paramètres du VLAN de gestion", "management_vlan_port_setting_hint": "Veuillez sélectionner le port auquel votre ordinateur est connecté et assurez-vous que les paramètres sont corrects pour éviter d'être déconnecté du commutateur", "port_mode_table_title": "Tableau du mode switchport VLAN", "egress_tagged_table_title": "Tableau d'appartenance VLAN", "gvrp": "GVRP", "vlan_unaware": "VLAN Unaware", "vlan_unaware_gvrp_error": "GVRP cannot be enabled while VLAN Unaware is active.", "vlan_unaware_active_disable_hint": "VLAN cannot be modified while VLAN Unaware is active.", "all_member_vlan": "Tous les ID de VLAN membres", "dynamic_gvrp": "GVRP dynamique", "egress_port": "Egress Port", "tagged_port": "Tagged Port", "untagged_port": "Untagged Port", "forbidden_port": "Port interdit", "vid_exist_warning": "VLAN existe déjà", "vlan_max_warning": "Max. 10 VLAN par fois", "vlan_max_hint": "Max. 10 VLAN", "pvid": "PVID", "tagged_vlan": "Tagged VLAN", "untagged_vlan": "Untagged VLAN", "access": "Accès", "access_port": "Port d'accès", "trunk": "Trunk", "trunk_port": "Trunk Port", "hybrid": "Hybrid", "hybrid_port": "Port Hybride", "vlan_assignment": "Attribution de VLAN", "delete_vlan_confirm_desc": "Êtes-vous sûr de vouloir supprimer le VLAN sélectionné?", "mgmt_setting_disabled_pvid": "PVID est lié à ce VLAN, il ne peut donc pas être supprimé.", "mgmt_setting_disabled_access_mode": "Si le port utilise le mode d'accès, il ne peut pas passer à ce VLAN.", "mgmt_setting_disabled_forbidden": "Ce port est un port interdit.", "mgmt_setting_disabled_egress": "Ce port est un port membre.", "port_setting_disabled_tagged": "Ce VLAN est un VLAN tagged.", "port_setting_disabled_untagged": "Ce VLAN est un VLAN untagged", "port_setting_disabled_forbidden": "Ce port est un port interdit pour ce VLAN.", "port_setting_disabled_pvid_member": "Ce PVID ne peut pas se lier à ce VLAN car ce port n'est pas un port membre.", "port_setting_disabled_pvid_forbidden": "Ce PVID ne peut pas se lier à ce VLAN car ce port est un port interdit.", "port_setting_error_pvid_member": "VLAN non taggé ou untagged", "port_setting_error_pvid_forbidden": "Ceci est un port interdit et les paramètres ne peuvent pas être appliqués", "vlan_setting_vid_info": "Plusieurs VLAN peuvent être créés et doivent être saisis sous la forme d'un seul chiffre ou d'une série de chiffres, par exemple 2, 4-8, 10-13.", "te_mstid": "TE-MSTID", "temstid_member": "Membre TE-MSTID", "temstid_info": "Pour le membre TE-MSTID de jonction VLAN, le flux sera transféré par une règle de transfert statique au lieu d'un mécanisme d'apprentissage/de transfert MAC.", "create_vlan_msg": "Créer un VLAN", "delete_vlan_msg": "Supprimer VLAN", "vlan_setting_info_title": "Comment configurer", "example_scenario": "Exemple de scénario", "example_scenario_info_1": "Port 1: Mode hybride, PVID 1, TAG VLAN 3-5 et UNTAG VLAN 1", "example_scenario_info_2": "Port 2: Mode Trunk, PVID 2 et TAG VLAN 2-5", "example_scenario_info_3": "Port 3: Mode d'accès, PVID 1 et UNTAG VLAN 1", "example_scenario_info_4": "Paramètres du commutateur A: VLAN de gestion 1", "setup_flow": "Configurer le flux", "vlan_port_mode_setup": "Configuration du mode de port VLAN", "port_number_setting": "Port {{ portNumber }} Paramètres", "setup_list_hybrid": "- Mode sélectionner 'Hybrid<PERSON>'", "setup_list_apply": "- Appliquer", "setup_list_trunk": "- Mode sélectionner 'Trunk'", "setup_list_access": "- Le mode par défaut est 'Accès' et il n'est pas nécessaire de changer quoi que ce soit.", "setup_list_pvid2": "- PVID 2", "vlan_create_member_setup": "Création de VLAN/configuration de membre de VLAN", "vlan_create_member_setup_info_part_1": "<PERSON><PERSON><PERSON>", "vlan_create_member_setup_info_part_2": ", ajouter l'ID VLAN {{ vlanIndex }} , ajouter un port membre {{ portIndex }}", "vlan_port_pvid_setup": "Configuration PVID du port VLAN", "vlan_port_pvid_setup_info": "<PERSON><PERSON><PERSON><PERSON> et pas besoin de changer."}, "l3_interface": {"page_title": "<PERSON>face <PERSON>", "loopback_size_limitation": "Cet appareil permet uniquement {{ size }} boucle local.", "operStatus": "Statut de l'opération", "loopback_id": "ID de la boucle local", "vlan_interface": "Interface VLAN", "loopback_interface": "interface boucle local", "alias": "<PERSON><PERSON>", "mtu": "MTU", "proxy_arp": "Proxy ARP", "vlan_id_hint": "L'ID de VLAN de l'interface doit être égal au VLAN de couche 2 pour que le routage de couche 3 fonctionne.", "delete_ip_interface_desc": "Êtes-vous sûr de vouloir supprimer cette entrée?", "vlan_card_title": "Interface VLAN", "loopback_card_title": "interface boucle local", "delete_ip_interface": "Supprimer l'interface", "add_l3_vlan_ip_interface": "Créer des paramètres d'interface VLAN", "edit_l3_vlan_ip_interface": "Modifier les paramètres d'interface VLAN", "add_l3_loopback_ip_interface": "<PERSON><PERSON>er les paramètres de l'interface de boucle local", "edit_l3_loopback_ip_interface": "Modifier les paramètres de l'interface boucle local", "from_dcp": "(Depuis PROFINET DCP)"}, "stp": {"page_title": "Spanning Tree", "stp_mode": "Mode STP", "compatibility": "Compatibilité", "stp": "STP", "rstp": "RSTP", "mstp": "MSTP", "stp_rstp": "STP/RSTP", "bridge_priority": "Bridge Priority", "error_recovery_time": "Error Recovery Time", "forward_delay_time": "Forward Delay Time", "hello_time": "Hello Time", "max_age": "Max. Age", "edge": "Edge", "guard": "Guard", "path_cost": "Path Cost", "multiples_of_number": "Multiples of {{ number }}", "path_cost_help_info": "La valeur du coût du chemin sera automatiquement attribuée en fonction des différentes vitesses de port si la valeur est définie sur zéro.", "bpdu_guard": "BPDU Guard", "root_guard": "Root Guard", "loop_guard": "Loop Guard", "bpdu_filter": "BPDU Filter", "root_information": "Root Information", "bridge_id": "Bridge ID", "root_path_cost": "Root Path Cost", "bridge_information": "Bridge Information", "running_protocol": "Running Protocol", "port_role": "Port Role", "link_type": "Link Type", "shared": "Shared", "bpdu_inconsistency": "BPDU Inconsistency", "root_inconsistency": "Root Inconsistency", "loop_inconsistency": "Loop Inconsistency", "link_type_shared_lan": "Shared LAN", "alternate": "Alternate", "root": "Root", "designated": "Designated", "instance": "Instance", "instance_index": "Instance {{ instId }}", "all_instances": "All Instances", "instance_list": "Instance List", "instance_id": "Instance ID", "mstp_size_limitation": "Le nombre maximum d'instances pour cet appareil est {{ size }} sauf pour le CIST.", "vlan_list": "Liste VLAN", "port_table_of_cist": "Table des Ports du CIST", "port_table_of_instance": "Table des ports d'instance {{ instId }}", "information_of_cist": "Informations du CIST", "information_of_instance": "Informations d'instance {{ instId }}", "region_name": "Nom de la région", "region_revision": "Révision Régionale", "max_hops": "<PERSON><PERSON>", "instance_id_duplicate": "L'ID d'instance a été créé.", "except_for_cist": "Sauf pour le CIST", "copy_port_config": "Copier les configurations de ports​", "select_from_port_of_inst": "Depuis les ports de {{ instName }}", "select_to_inst": "Aux instances", "general_information": "Informations générales", "regional_root_id": "Identifiant racine régional", "cist_root_id": "Identifiant racine CIST", "cist_path_cost": "Coût du chemin CIST", "designated_root_id": "Identifiant racine d<PERSON>", "other_vlans": "Autres VLAN", "create_instance": "<PERSON><PERSON><PERSON> une instance", "edit_instance": "Modifier l'instance {{ instId }} Paramètres", "delete_instance": "Supprimer l'instance", "edit_cist": "Modifier les paramètres CIST", "edit_instance_port": "Modifier l'instance {{ instId }} Port {{ portIndex }} Paramètres", "edit_cist_port": "Modifier le port CIST {{ portIndex }} Paramètres"}, "port_security": {"page_title": "Sécurité des ports", "port_security_mode": "Mode de sécurité des ports", "port_security_mode_help_info": "La modification du mode de sécurité du port réinitialisera tous les paramètres.", "mac_sticky": "MAC Sticky", "static_port_lock": "Static Port Lock", "address_limit": "Address Limit", "secure_action": "Secure Action", "current_address": "<PERSON><PERSON><PERSON> actuelle", "configured_address": "<PERSON>resse configurée man<PERSON>", "violation": "Violation", "effective": "Efficace", "secure_pack_drop": "Packet Drop", "total_entry": "Hôtes de confiance totale", "max_address": "Le nombre max. d'adresses dans le système", "sticky_configured": "Sticky Configured", "lock_configured": "Lock Configured", "sticky_dynamic": "Sticky Dynamic", "address_limit_hint": "Si la valeur limite d'adresse a changé, toutes les adresses MAC du port seront supprimées."}, "garp": {"page_title": "GARP", "join_time": "Join Time", "leave_time": "Leave Time", "leave_all_time": "Leave All Time", "required_join_time_multiple": "Le temps de jointure doit être un multiple de {{ num }}", "required_leave_time_multiple": "Le temps de congé doit être un multiple de {{ num }}", "required_leave_all_time_multiple": "Le congé de tous les temps doit être un multiple de {{ num }}"}, "lldp": {"page_title": "LLDP", "neighbor_status": "Neighbor Status", "sidenav_header": "Detailed Information", "port_local_intf_status": "Port Local Interface", "port_id_subtype": "Port ID SubType", "port_id": "Port ID", "port_desc": "Port Description", "dot1_tlv_info": "Extended 802.1 TLV", "dot3_tlv_info": "Extended 802.3 TLV", "port_vlan_id": "Port VLAN ID", "vlan_name": "VLAN Name", "vlan_tx_status": "VLAN ID / Name", "aggregated_and_status": "Link Aggregation Status", "aggregated_port_id": "Aggregated Port ID", "max_frame_size": "Maximum Frame <PERSON>", "port_traffic_statistics": "Port Traffic Statistics", "total_frame_out": "Total Frames Out", "total_entries_aged": "Total Entries Aged", "total_frame_in": "Total Frames In", "total_frame_receviced_in_error": "Total Frames Received In Error", "total_frame_discarded": "Total Frames Discarded", "total_tlvs_unrecognized": "Total TLVS Unrecognized", "total_tlv_discarded": "Total TLVs Discarded", "management_address_table": "Management Address Table", "management_address": "Management Address", "extended_eip_tlv": "Extended Ethernet/IP TLV", "vendor_id": "Vendor ID", "device_type": "Device Type", "product_code": "Product Code", "major_revision": "Major Revision", "minor_revision": "Minor Revision", "interface_id": "Interface ID", "lldp_version": "LLDP Version", "transmit_interval": "Transmit Interval", "notification_interval": "Notification Interval", "reinit_delay": "Reinitialization Delay", "holdtime_multiplier": "Holdtime Multiplier", "chass_id_subtype": "Chassis ID Subtype", "tx_delay": "Tx Delay", "subtype_chassis_component": "<PERSON><PERSON>s-Component", "subtype_if_alias": "<PERSON>-<PERSON><PERSON>", "subtype_port_component": "Port-Component", "subtype_mac_addr": "MAC-Address", "subtype_network_address": "Network-Address", "subtype_if_name": "If-Name", "subtype_unknown": "Unknown Subtype", "chassis_id": "<PERSON><PERSON>s <PERSON>", "tlv": "TLV", "local_info": "Local Information", "local_timer": "Local Timer", "remote_table_statistics": "Remote Table Statistics", "statistics_last_change_time": "Last Change Time (ms)", "statistics_insert": "Inserts", "statistics_drops": "Drops", "statistics_ageout": "Ageouts", "tx_status": "Tx Status", "rx_status": "Rx Status", "nbr_port_id": "Neighbor Port ID", "nbr_chassis_id": "Neighbor Chassis ID", "tx_only": "Tx Only", "rx_only": "Rx Only", "tx_and_rx": "Tx and Rx", "basic": "Basic", "basic_transmit_tlvs": "Basic Transmit TLVs", "8021_transmit_tlvs": "802.1 Transmit TLVs", "8023_transmit_tlvs": "802.3 Transmit TLVs", "port_component_description": "Port Component Description.", "system_name": "System Name", "system_desc": "System Description", "system_capability": "System Capability", "la_statistics": "Link Aggregation Statistics", "lldp_update_success": "Successfully Updated LLDP Global Settings.", "local_port": "Local Port", "sys_capability": "System Capability", "hold_time": "Hold Time", "repeater": "<PERSON><PERSON><PERSON>", "bridge": "Bridge", "vlan_access_point": "Vlan Access Point", "telephone": "Telephone", "docsis_cable_device": "Docsis Cable Device", "station_only": "Station Only"}, "mac_address_table": {"page_title": "Tableau d'adresses MAC", "independent_vlan_learning": "Apprentissage VLAN indépendant", "mac_learning_mode": "Mode d'apprentissage MAC", "mac_learning_mode_help_info": "Notez que le mode de changement réinitialisera le module L2 associé.", "aging_time": "Aging Time", "learnt_unicast": "Learnt Unicast", "learnt_multicast": "Learnt Multicast"}, "port_setting": {"page_title": "Paramètres des ports", "admin": "Admin", "media_type": "Media Type", "10m_half": "10M Half", "10m_full": "10M Full", "100m_half": "100M Half", "100m_full": "100M Full", "speed_duplex": "Speed/Duplex", "flow_control": "Flow Control", "flow_control_hint1": "Le contrôle de flux peut être activé/désactivé, mais il n'est efficace qu'en duplex intégral.", "flow_control_hint2": "La contre-pression peut être activée/désactivée, mais elle n’est efficace qu’en semi-duplex.", "mdi_mdix": "MDI/MDIX", "mdi": "MDI", "mdix": "MDIX", "enabled_xmit": "Enabled Transmit", "enabled_rcv": "Enabled Receive", "fiber_speed_disable": "Le port fibre ne peut pas définir la vitesse/duplex.", "fiber_mdi_disable": "Le port fibre ne peut pas définir MDI/MDIX.", "fiber_copy_to_other_port_disable": "Le port fibre ne peut pas copier les configurations vers d'autres ports.", "port_copy_to_fiber_port_disable": "Impossible de copier les configurations sur les ports fibre."}, "dashboard": {"page_title": "Résumé de l'appareil", "system_info": "Informations système", "panel_status": "État du panneau", "panel_view": "Vue du panneau", "link_up_port": "Link Up Ports", "link_down_port": "Link Down Ports", "module": "Module {{ index }} - {{ name }}", "product_model": "Mod<PERSON><PERSON> du produit", "firmware_version": "Version du firmware", "system_uptime": "Disponibilité du système", "ip_address_v4": "Adresse IPv4", "ip_address_v6": "Adresse IPv6", "l3_ip_address_list": "Liste d'adresses IP de l'interface", "redundant_protocol": "Protocol de redondance", "power_model": "<PERSON><PERSON><PERSON><PERSON> de puissance", "external_storage": "Stockage externe", "iec62439_3_protocol": "Protocole IEC 62439-3", "event_summary": "Résumé des événements", "event_summary_hint": "(3 derniers jours)", "top_5_interface_error_packet": "Top 5 Interface Error Packets", "top_5_interface_utilization": "Top 5 des utilisations d'interfaces", "critical_hint": "Une anomalie s'est produite et le système risque de fonctionner anormalement à l'avenir", "error_hint": "Une anomalie s'est produite, mais le fonctionnement du système n'a pas été affecté", "warning_hint": "L'information contient un avertissement/rappel mais cela n'affecte pas les fonctions ou les opérations du système", "notice_hint": "L'information indique que la fonction fonctionne correctement et que l'appareil fonctionne normalement", "tx_error": "Tx Error", "rx_error": "<PERSON><PERSON>", "unsupported_module_warning": "Attention : module non pris en charge détecté. Retirez le module non pris en charge pour maintenir un fonctionnement normal."}, "igmp_snooping": {"page_title": "IGMP Snooping", "vlan_setting": "VLAN Settings", "group_table": "Group Table", "forwarding_table": "Forwarding Table", "query_interval": "Query Interval", "static_router_port": "Static Router Port", "dynamic_router_port": "Dynamic Router Port", "config_role": "Config Role", "active_role": "Active Role", "startup_query_interval": "Startup Query Interval", "startup_query_count": "Startup Query Count", "other_quer_present_interval": "Other Query Present Interval", "group_address": "Group Address", "filter_mode": "Filter Mode", "source_address": "Source Address", "querier": "<PERSON><PERSON>", "non_querier": "Non-Querier"}, "turbo_ring_v2": {"page_title": "Turbo Ring V2", "ring_coupling_mode": "Mode de couplage en anneau", "static_ring_coupling": "accouplement à anneau statique", "dynamic_ring_coupling": "Couplage annulaire dynamique", "ring_id": "Ring ID", "master_id": "Master ID", "ring_port": "Ring Port", "coupling_mode": "Coupling Mode", "coupling_port": "Coupling Port", "primary_path": "Primary Path", "backup_path": "Backup Path", "ring_setting": "Ring Settings", "ring_coupling_setting": "Ring Coupling Settings", "coupling_setting": "Groupe de couplage {{ id }} Paramètres", "static_ring_coupling_setting": "Paramètres de couplage annulaire statique", "dynamic_ring_coupling_setting": "Paramètres de couplage annulaire dynamique", "coupling_group_id": "ID du groupe de couplage", "coupling_group_status": "Statut du groupe de couplage", "group_id": "Groupe {{ id }}", "ring_status": "Ring Status", "ring_index": "Indice de l’anneau", "total_ring_number": "Nombre total de sonneries", "healthy": "Healthy", "break": "Break", "ring_coupling_status": "Ring Coupling Status", "static_ring_coupling_status": "État de l’accouplement annulaire statique", "dynamic_ring_coupling_status": "État du couplage annulaire dynamique", "coupling_mode_primary": "Coupling Primary Path", "coupling_mode_backup": "Coupling Backup Path", "coupling_port_status": "État du port de couplage", "primary_mac": "MAC primaire", "primary_port": "Port principal", "primary_port_status": "État du port principal", "backup_mac": "Sauvegarde MAC", "backup_port": "Port de sauvegarde", "backup_port_status": "État du port de sauvegarde", "ring_setting_dialog_title": "{{ portIndex }} Settings", "dip_lock_hint": "Turbo Ring V2 est verrouillé en raison des configurations DIP."}, "8021x": {"page_title": "IEEE 802.1X", "auth_mode": "Authentication Mode", "local_database": "Local Database", "re_auth": "Reauthenticate", "port_control": "Port Control", "auth_session_type": "Type de session d’authentification", "max_request": "Max. Request", "quiet_period": "Quiet Period", "reauthentication": "Reauthentication", "reauth_period": "Reauthentication Period", "server_timeout": "Server Timeout", "supp_timeout": "Supp Timeout", "tx_period": "Tx Period", "auth_port": "Auth Port", "retransmit": "Retransmit", "force_unauthorized": "Force Unauthorized", "force_Authorized": "Force Authorized", "reauth_port_confirm": "Êtes-vous sûr de vouloir réauthentifier le port {{ port }}?", "authorized": "Autorisé", "unauthorized": "Non autorisé", "title_reauth_port": "Réauthentifier le port", "port_setting": "Port {{ portIndex }} Paramètres", "account_setting": "Compte {{ userName }} Paramètres", "timeout_retransmit_hint": "Tous les temps de tentative ne peuvent pas dépasser la valeur du délai d'attente du serveur Dot1x. Note: Toutes les tentatives = Timeout * (Retransmit + 1). Valeur recommandée: {{ number }}", "session_status": "État de la session", "auth_table_of_port_based": "Table d’authentification basée sur le port", "auth_table_of_mac_based": "Table d’authentification basée sur MAC"}, "dual_homing": {"page_title": "Dual Homing", "multi_dual_homing": "Multiple Dual Homing", "dual_homing_table_settings": "Paramètres tableau du Dual Homing", "primary_port": "Primary Port", "primary_link_status": "Primary Link Status", "primary_port_status": "Primary Port Status", "secondary_port": "Secondary Port", "secondary_link_status": "Secondary Link Status", "secondary_port_status": "Secondary Port Status", "secondary_port_hint": "The secondary port cannot be the same as the primary port.", "path_switching_mode": "Path Switching Mode", "primary_path_always_first": "Primary path always first", "maintain_current_path": "Maintain current path", "maintain_current_path_hint": "Maintain current path until it is disconnected", "primary_path_sensing_recovery": "Primary path sensing recovery", "path_switching_mode_hint": "It is recommended to enable the Linkup Delay function of ", "path_switching_mode_hint_2": " port.", "path": "Path", "linkup_delay_warning_title": "Linkup Delay is Disabled"}, "poe": {"page_title": "PoE", "power_output": "Power Output", "poe_supported": "PoE Supported", "scheduling": "Scheduling", "pd_failure_check": "PD Failure Check", "auto_power_cutting": "Auto Power Cutting", "auto_power_cutting_hint": "La coupure automatique de l'alimentation supprime la sortie d'alimentation du port d'index de priorité la plus basse et la plus petite si la consommation d'énergie dépasse le budget d'alimentation du système.", "system_power_budget": "Budget de puissance du système", "system_power_budget_hint": "Le budget d'alimentation du système dépend de la capacité de la source de l'alimentation externe (EPS).", "actual_power_budget": "Budget de puissance réel", "actual_power_budget_hint": "La valeur inférieure entre \"Budget de puissance réel\" et \"Budget de puissance système\" deviendra la \"Limite de budget de puissance\".", "output_mode": "Output Mode", "high_power": "High Power", "force": "Force", "power_allocation": "Power Allocation", "legacy_pd_detection": "Legacy PD Detection", "critical": "Critique", "low": "Low", "high": "High", "rule": "Rule", "also_apply_port": "Apply the rule to the port", "device_ip": "Device IP", "check_frequency": "Check Frequency", "no_response_times": "No Response Times", "no_action": "No Action", "restart_pd": "Restart PD", "shutdown_pd": "Shut down PD", "system_time_status": "System Time Status", "system_time": "System Time", "local_timeZone": "Local TimeZone", "daylight_saving_time": "Daylight Saving Time", "off": "Off", "on": "On", "rule_name": "Rule Name", "schedule_time": "Schedule Time", "repeat_execution": "Repeat Execution", "daily": "Daily", "weekly": "Weekly", "weekdays": "Weekdays", "weekend": "Weekend", "sun": "<PERSON><PERSON>", "mon": "<PERSON>n", "tue": "Mar", "wed": "<PERSON><PERSON>", "thu": "<PERSON><PERSON>", "fri": "Ven", "sat": "Sam", "sunday": "<PERSON><PERSON><PERSON>", "monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "<PERSON><PERSON><PERSON><PERSON>", "saturday": "<PERSON><PERSON>", "maximum_input_power": "Maximum Input Power", "power_budget_limit": "Power Budget Limit", "power_management_mode": "Power Management Mode", "allocated_power": "Allocated Power", "consumed_power": "Consumed Power", "remaining_available_power": "Remaining Power Available", "classification": "Classification", "over_current": "Over current", "current_ma": "Current (mA)", "voltage_v": "Voltage (V)", "consumption_w": "Consumption (W)", "device_type": "Device Type", "not_present": "Not present", "legacy": "Legacy PD", "dot3af": "802.3 af", "dot3at": "802.3 at", "dot3bt": "802.3 bf", "dot3btss": "802.3 bt SS", "dot3btds": "802.3 bt DS", "na": "N/A", "configuration_suggestion": "Suggestion de configuration", "no_suggestion": "Aucune proposition", "enable_poe": "Enable PoE power output", "disable_poe": "Disable PoE power output", "select_auto": "Select output mode \"Auto\"", "select_high_power": "Select output mode \"High power\"", "select_force": "Select output mode \"Force\"", "enable_legacy": "Enable legacy PD detection", "raise_eps_voltage": "Augmenter la tension d'alimentation externe pour qu'elle soit supérieure à 46 VDC", "pd_failure_check_status": "PD Failure Check Status", "alive": "Alive", "not_alive": "Not alive", "schedule_size_limitation": "This device only allows {{ size }} schedules.", "title_create_rule": "<PERSON><PERSON><PERSON> une règle", "title_edit_rule": "Modifier la règle", "allocated_power_hint": "Calculez le budget de puissance de tous les ports et assurez-vous que la puissance totale allouée est inférieure à la limite de budget de puissance.", "consumed_power_hint": "Calculez la consommation d'énergie en temps réel de tous les ports.", "change_power_mode_dialog_title": "Définir le mode de gestion de l'alimentation", "select_allocated_power_mode": "Êtes-vous sûr de vouloir sélectionner le mode \"Puissance allouée\"? Si tel est le cas, la \"coupure automatique de l'alimentation\" sera désactivée.", "select_consumed_power_mode": "Êtes-vous sûr de vouloir sélectionner le mode \"Puissance consommée\"? Si tel est le cas, la \"coupure automatique de l'alimentation\" sera activée.", "change_power_cutting_dialog_title": "Définir la coupure automatique de l'alimentation", "disable_auto_power_cutting": "Êtes-vous sûr de vouloir désactiver la \"coupure automatique de l'alimentation\"? Si c'est le cas, le \"Mode de gestion de l'alimentation\" deviendra le mode \"Puissance allouée\".", "enable_auto_power_cutting": "Êtes-vous sûr de vouloir activer la \"coupure automatique de l'alimentation\"? Si c'est le cas, le \"Mode de gestion de l'alimentation\" deviendra le mode \"Puissance consommée\".", "avaliable_power_hint": "La \"puissance disponible restante\" correspond à la \"puissance d'entrée maximale\"moins \"{{ power }}\"."}, "turbo_chain": {"page_title": "Turbo Chain", "chain_role": "Chain Role", "head": "Head", "member": "Member", "tail": "Tail", "head_port": "Head Port", "tail_port": "Tail Port", "member_port_number": "Member Port {{ portIndex }}", "chain_information": "Chain Information", "head_port_status": "Head Port Status", "member_port_status": "Member Port Status", "tail_port_status": "Tail Port Status", "member_number_port_status": "Member {{ number }} Port Status", "initiated": "Initiated"}, "mrp": {"menu_title": "MRP", "page_title": "Media Redundancy Protocol", "mrp_role": "Role", "ring_manager": "Ring Manager", "ring_client": "Ring Client", "domain_uuid": "Domain UUID", "domain_id": "Domain ID", "react_on_link_change": "React on Link Change", "ring_port": "Ring Port {{ portIndex }}", "ring_status": "Ring Status", "mrp_ring": "MRP Ring", "ring_state": "Ring State", "vid_hint": "L'ID VLAN doit correspondre aux paramètres du port redondant.", "react_on_link_change_hint": "Cette fonction est uniquement disponible sur MRP Ring Manager Switch. Une fois activé, le Ring Manager réagira immédiatement au changement de lien et la topologie MRP convergera plus rapidement.", "initiation": "Initiation", "awaiting_connection": "En attente de connexion", "primary_ring_port_link_up": "Primary Ring Port Link Up", "ring_open": "Ring Open", "ring_closed": "Ring Closed", "data_exchange_idle": "Data Exchange Idle", "pass_through": "Pass Through", "data_exchange": "Data Exchange", "pass_through_idle": "Pass Through Idle", "port_pvid_warning": "L'ID VLAN doit correspondre aux paramètres du port redondant", "port_mode_warning": "Les ports MRP Ring ne fonctionneront pas à moins d’être réglés sur le mode VLAN Trunk ou le mode VLAN hybride.", "interconnection_port_mode_warning": "Les ports d’interconnexion MRP ne fonctionneront pas à moins d’être réglés sur le mode VLAN Trunk ou le mode VLAN hybride.", "interconnection": "Interconnexion", "interconnection_role": "Rôle d'interconnexion", "interconnection_manager": "Interconnection Manager", "interconnection_client": "Interconnection Client", "interconnection_mode": "Mode d'interconnexion", "lc_mode": "LC-Mode", "rc_mode": "RC-Mode", "interconnection_id": "Interconnection ID", "interconnection_port": "Interconnection Port", "interconnection_status": "Interconnection Status", "interconnection_state": "Interconnection State", "interconnection_open": "Interconnection Open", "interconnection_closed": "Interconnection Closed", "interconnection_port_idle": "Interconnection Port Idle"}, "unicast_table": {"page_title": "Tableau monodiffusion", "static_unicast": "Monodiffusion statique", "edit_static_unicast_entry": "Modifier cette entrée de monodiffusion statique", "add_static_unicast_entry": "Ajouter une entrée monodiffusion statique", "size_limitation": "Le nombre maximum d'entrées monodiffusion statiques pour cet appareil est {{ size }}."}, "static_forwarding_table": {"page_title": "Static Forwarding Table", "menu_title": "Static Forwarding", "size_limitation": "The maximum number of static unicast entries for this device is {{ size }}.", "title_add_static_forwarding": "Create a Static Forwarding Entry", "title_edit_static_forwarding": "Edit This Static Forwarding Entry", "title_delete_static_forwarding": "Delete This Static Forwarding Entry"}, "multicast_table": {"page_title": "Table de multidiffusion statique", "static_multicast": "Multidiffusion statique", "delete_on_reset": "Supprimer lors de la réinitialisation", "delete_on_timeout": "Supprimer après expiration", "add_static_multicast_entry": "Ajouter une entrée de multidiffusion statique", "edit_static_multicast_entry": "Modifier cette entrée de multidiffusion statique", "size_limitation": "Le nombre maximum d'entrées de multidiffusion statique pour cet appareil est {{ size }}."}, "gmrp": {"page_title": "GMRP", "group_restrict": "Restriction de groupe"}, "time_sync": {"page_title": "Time Synchronization", "page_title_abbr": "Time Sync.", "mds_m2_insert_warning": "Pour utiliser la synchronisation horaire, un module compatible doit être inséré dans l’emplacement M2.", "profile": "Profile", "8021as": "IEEE 802.1AS-2011 Profile", "8021as_abbr": "IEEE 802.1AS-2011", "1588default": "IEEE 1588 Default-2008 Profile", "1588default_abbr": "IEEE 1588 Default-2008", "iec61850": "IEC 61850-9-3-2016 Profile", "iec61850_abbr": "IEC 61850-9-3-2016", "c37238": "IEEE C37.238-2017 Profile", "c37238_abbr": "IEEE C37.238-2017", "priority_number": "Priority {{ number }}", "clock_type": "Clock Type", "clock_type_bc": "Boundary Clock", "clock_type_tc": "Transparent Clock", "delay_mechanism": "Delay Mechanism", "e2e": "End-to-End", "p2p": "Peer-to-Peer", "transport_mode": "Transport Mode", "8023ehternet": "IEEE 802.3 Ethernet", "udp_ipv4": "UDP IPv4", "udp_ipv6": "UDP IPv6", "domain_number": "Domain Number", "clock_mode": "Clock Mode", "two_step": "Two-step", "one_step": "One-step", "accuracy_alert": "Accuracy <PERSON>", "bmca": "BMCA", "bmca_hint": "Le meilleur algorithme d'horloge maître (BMCA) empêche la boucle de chemin cyclique lors de l'utilisation de Transparent Clock. Nous vous recommandons d'activer cette fonction.", "max_steps_removed": "Étapes maximales supprimées", "grandmaster_id": "Grandmaster ID", "announce_interval": "Announce Interval", "announce_receipt_timeout": "Announce Receipt Timeout", "sync_interval": "Sync Interval", "sync_receipt_timeout": "Sync Receipt Timeout", "delay_req_interval": "Delay-Request Interval", "pdelay_req_interval": "Pdelay-Request Interval", "neighbor_rate_ratio": "Neighbor Rate Ratio", "neighbor_prop_delay": "Neighbor Propagation Delay", "path_delay": "Path Delay", "neighbor_prop_delay_thresh": "Neighbor Propagation Delay Threshold", "synchronization_status": "Synchronization Status", "transport_type": "Transport Type", "current_data_set": "Current Data Set", "parent_data_set": "Parent Data Set", "locked": "Locked", "unlocked": "Unlocked", "freerun": "<PERSON><PERSON>", "syncing": "Syncing", "browser_time": "Browser Time", "ptp_clock_time": "PTP Clock Time (TAI)", "ptp_slave_port": "PTP Slave Port", "offset_from_master": "Offset From Master", "mean_path_delay": "Mean Path Delay", "steps_removed": "Steps Removed", "parent_identity": "Parent Identity", "grandmaster_identity": "Grandmaster Identity", "cumulative_rate_ratio": "Cumulative Rate Ratio", "grandmaster_priority_number": "Grandmaster Priority {{ number }}", "grandmaster_clock_class": "Grandmaster Clock Class", "grandmaster_clock_accuracy": "Grandmaster Clock Accuracy", "8021as_capable": "802.1AS-capable", "initializing": "Initializing ...", "res_ptp_initializing_done": "The PTP service is ready.", "faulty": "<PERSON><PERSON><PERSON>", "pre_master": "PreMaster", "uncalibrated": "Uncalibrated", "sync_transmit": "Transmitting Synchronized", "sync_receive": "Receiving Synchronized", "edit_port_profile_setting": "Edit the Port {{ portIndex }} IEEE 1588v2 Default Profile Settings"}, "stream_adapter": {"page_title": "Priority Management", "pcp": "Priority Code Point (PCP)", "ingress_table_limit_hint": "Chaque port prend en charge un maximum de 10 entrées.", "port_size_limitation": "Le nombre maximum d'entrées (10) pour ce port a été atteint.", "hexadecimal": "Hex digit", "egress_untag": "Egress Untag", "ingress": "Ingress", "egress": "Egress", "per_stream_priority_title": "Per-stream Priority", "port_default_priority_title": "Port Default Priority", "per_stream_priority_hint": "Les flux non marqués seront traités en fonction de la priorité par flux s'ils correspondent à des règles définies par l'utilisateur. Si les flux ne correspondent à aucune règle, ils seront traités en fonction de la priorité de port par défaut.", "add_stream_adapter_entry": "Ajouter une entrée prioritaire par flux", "edit_stream_adapter_entry": "Modifier cette entrée prioritaire par flux", "edit_port_def_priority": "Modifier la priorité par défaut du port {{ portIndex }}"}, "static_route": {"page_title": "Routage statique", "size_limitation": "Le nombre maximum de routes statiques pour cet appareil est {{ size }}.", "next_hop_IP": "Next Hop IP", "next_hop_interface": "Next Hop Interface", "delete_static_route_desc": "Êtes-vous sûr de vouloir supprimer la route sélectionnée?", "next_hop_type": "Next Hop Type *", "next_hop_type_hint": "Vous pouvez sélectionner une interface VLAN créée précédemment ou laisser ce champ vide.", "next_hop_two_field_invalid": "<PERSON><PERSON> <PERSON><PERSON> spécifier soit l'IP, soit l'interface du prochain saut", "add_static_route": "Créer une route statique", "edit_static_route": "Modifier cette route statique", "delete_static_route": "Supprimer cette route statique"}, "routing_table": {"page_title": "Table de routage", "static": "Statique", "next_hop": "Prochain saut", "ad_metric": "AD/Metric"}, "online_accounts": {"page_title": "Comptes en ligne", "idle_time": "Temps d'inactivité", "remove_account_dialog_title": "Supprimer ce compte en ligne", "remove_account_dialog_desc": "Êtes-vous sûr de vouloir supprimer ce compte en ligne?"}, "8021qbv": {"page_title": "Time-aware <PERSON><PERSON><PERSON>", "cycle_time": "Temps d'un cycle", "start_time_hint": "Le Time-aware S<PERSON>per est basé sur l'heure PTP actuelle. V<PERSON> pouvez déterminer l'heure de début ou définir l'heure maintenant, ce qui déterminera le moment où la fonction a démarré.", "config_change_time": "Temps de changement de configuration", "default_setting": "Paramètres par défaut", "gate_control_list": "Gate Control List", "totla_slot": "Total Slots", "interval": "Intervalle", "selected_queue_summary": "Résumé de la file d'attente sélectionnée", "interval_hint": "L'intervalle de porte indique quand la porte s'ouvrira et combien de temps elle restera ouverte. L'intervalle minimum pour les paquets Ethernet est indiqué ci-dessous", "interval_hint_1G": "1G ports: 1µs,", "interval_hint_100M": "100M ports: 10µs,", "interval_hint_10M": "10M ports: 100µs", "port_status": "Port {{ portIndex }} Status", "select_port": "Sélectionner le port"}, "ospf": {"page_title": "OSPF", "ospf_settings": "Paramètres OSPF", "ospf_status": "Statut OSPF", "area": "Area", "neighbor": "Neighbor", "aggregation": "Aggregation", "virtual_link": "Virtual Link", "router_id": "Router ID", "current_router_id": "Router ID actuel", "current_router_id_hint": "Si l'ID du routeur est défini sur 0.0.0.0, l'adresse IP d'interface la plus basse sera automatiquement attribuée en tant qu'ID du routeur.", "compatible_rfc_1583": "Compatibilité RFC 1583", "spf_hold_time": "SPF Hold Time", "redistribute": "Redistribute", "metric": "Metric", "ospfRRDStatic": "Static", "ospfRRDConnected": "Connected", "ospfRRDRip": "RIP", "area_size_limitation": "The maximum number of areas for this device is {{ size }}.", "area_id": "Area ID", "area_type": "Area Type", "normal": "Normal", "stub": "<PERSON><PERSON>", "nssa": "NSSA", "summary": "Summary", "no_summary": "No Summary", "delete_area_desc": "Êtes-vous sûr de vouloir supprimer la zone sélectionnée?", "dead_interval": "Intervalle mort", "cost": "Coût", "network_type": "Type de réseau", "passive_interface": "Interface passive", "neighbor_ip_address": "Adresse IP du voisin", "summary_hint": "Le résumé n'est pas disponible si le type de zone est défini sur Normal.", "broadcast": "Broadcast", "non_broadcast": "Non-broadcast", "point_to_point": "Point-to-point", "point_to_multipoint": "Point-to-multipoint", "nbr_ip_address": "Adresse IP du voisin", "nbr_size_limitation": "Le nombre maximum de voisins pour cet appareil est {{ size }}.", "delete_nbr_desc": "Êtes-vous sûr de vouloir supprimer le voisin sélectionné?", "lsa_type": "Type LSA", "type_7": "Type 7", "aggregation_size_limitation": "Le nombre maximal d'agrégations pour cet appareil est {{ size }}.", "delete_aggregation_desc": "Êtes-vous sûr de vouloir supprimer l'agrégation sélectionnée?", "vLink_size_limitation": "Le nombre maximum de liens virtuels pour cet appareil est {{ size }}.", "delete_vLink_desc": "Êtes-vous sûr de vouloir supprimer le lien virtuel sélectionné?", "loopback": "boucle local", "waiting": "En attendant", "dr": "DR", "bdr": "BDR", "dr_other": "DR Other", "dr_router_id": "DR Router ID", "bdr_router_id": "BDR Router ID", "neighbor_id": "Neighbor ID", "neighbor_state": "Neighbor State", "dead_time": "Dead Time", "accempt": "Attempt", "two_way": "2-Way", "exstart": "Exstart", "exange": "Exchange", "loading": "Loading", "full": "Full", "database": "Database", "link_id": "Link ID", "adv_router": "ADV Router", "age": "Age", "events": "Events", "ls_retrans_queue_len": "LSA Retransmission Queue Length", "hello_suppressed": "Hello Suppressed", "router": "Router", "asbr_summary": "ASBR Summary", "as_external": "AS External", "group_member": "Group Member", "nssa_external": "NSSA External", "title_edit_ospf_redistribute": "Edit Redistribute {{ protocol }}", "title_create_ospf_area": "Create an Area", "title_edit_ospf_area": "Edit This Area", "title_delete_ospf_area": "Delete This Area", "title_create_ospf_nbr": "Create a Neighbor", "title_edit_ospf_nbr": "Edit This Neighbor", "title_delete_ospf_nbr": "Delete This Neighbor", "title_create_ospf_aggregation": "Créer une agrégation", "title_edit_ospf_aggregation": "Modifier cette agrégation", "title_delete_ospf_aggregation": "Supprimer cette agrégation", "title_create_ospf_vlink": "<PERSON><PERSON><PERSON> un lien virtuel", "title_edit_ospf_vlink": "Modifier ce lien virtuel", "title_delete_ospf_vlink": "Supprimer ce lien virtuel"}, "vrrp": {"page_title": "VRRP", "v2": "V2", "v3": "V3", "virtual_router_enable": "Virtual Router", "vrid": "VRID", "decrement": "Decrement", "primary_ip": "Virtual Router IP Address", "adv_int": "Advertisement Interval", "preempt_mode": "Preempt Mode", "preempt_delay": "<PERSON><PERSON><PERSON>", "accept_mode": "Accept Mode", "auth_key": "Authentication Key", "size_limitation": "Le nombre maximum d'entrées VRRP pour cet appareil est {{ size }}.", "delete_vrrp_desc": "Êtes-vous sûr de vouloir supprimer le routeur virtuel sélectionné?", "master_address": "Master Address", "master_adv_int": "Master Advertisement Interval (ms)", "master_down_int": "Master Down Interval (ms)", "title_add_vrrp": "Create a Virtual Router", "title_edit_vrrp": "Edit This Virtual Router", "title_delete_vrrp": "Delete This Virtual Router", "require_decrement_less_than_priority": "La valeur doit être inférieure à la valeur Priorité"}, "dns": {"page_title": "Paramètres DNS", "primary_dns_server": "Serveur DNS primaire", "secondary_dns_server": "Serveur DNS secondaire", "dns_server_number": "Adresse IP du serveur DNS{{ number }}", "dns_server": "Serveur dns", "dns_reverse_lookup": "DNS Reverse Lookup", "zone_table": "Zone Table", "dns_table_for_name": "Table DNS pour {{ zoneName }}", "dns_server_summary": "Résumé du serveur DNS", "fqdn": "FQDN", "fqdn_hint": "FQDN (nom de domaine complet) est \"Nom d'hôte\".\"Nom de domaine\"", "dns_forwarding": "Redirection DNS", "dns_forwarding_hint": "Un serveur DNS actif est nécessaire pour activer la redirection DNS.", "default_forwarder_ip": "Adresse IP par défaut du redirecteur", "default_forwarder_ip_hint": "Si le redirecteur par défaut est spécifié, les requêtes DNS pour les zones non répertoriées dans la table des redirecteurs seront transmises au redirecteur par défaut.", "forwarder_ip": "Adresse IP du redirecteur", "forwarders_table": "Table des Transitaires", "zone_hint": "'.' peut être utilisé pour rediriger n’importe quelle Zone.", "zone_size_limitation": "Le nombre maximum d'entrées de zone est {{ size }}.", "dns_size_limitation": "Le nombre maximum d'entrées DNS est {{ size }}.", "title_create_zone": "Créer une Zone", "title_edit_zone": "Modifier {{ zoneName }} Paramètres", "title_delete_zone": "Supprimer zone(s)", "delete_zone_desc": "Êtes-vous sûr de vouloir supprimer la ou les zones sélectionnées?", "title_create_dns": "<PERSON><PERSON>er un enregistrement de ressource pour {{ zoneName }}", "title_edit_dns": "Modifier l'enregistrement de ressource pour {{ zoneName }}", "title_delete_dns": "Supprimer le(s) enregistrement(s) de ressource", "delete_dns_desc": "Êtes-vous sûr de vouloir supprimer le(s) enregistrement(s) de ressource sélectionné(s)?", "title_create_forwarding": "C<PERSON>er une entrée de redirection DNS", "title_edit_forwarding": "Modifier l’entrée de redirection DNS", "title_delete_forwarding": "Supprimer l’entrée de redirection DNS", "delete_forwarding_desc": "Êtes-vous sûr de vouloir supprimer l’entrée de redirection DNS sélectionnée?", "duplicate_hostname": "Le même nom d’hôte existe déjà", "duplicate_domain_name": "Le même nom de domaine existe déjà", "duplicate_zone": "Cette zone existe déjà "}, "acl": {"page_title": "Liste de contrôle d'accès", "access_list_type": "Type de liste d'accès", "access_list_type_hint": "Pour un même index, l'adresse MAC a une priorité plus élevée que l'adresse IP.", "access_list_index_hint": "Un indice inférieur représente une priorité supérieure.", "ip_based": "IP-based", "mac_based": "MAC-based", "acl_size_limitation": "Le nombre maximum d'entrées ACL est {{ size }}.", "acl_ip_based_size_limitation": "Le nombre maximum de règles ACL basées sur IP est {{ size }}.", "acl_mac_based_size_limitation": "Le nombre maximum de règles ACL basées sur MAC est {{ size }}.", "acl_rule_size_limitation": "Le nombre maximum de règles ACL est {{ size }}.", "active_interface_type": "Type d'interface active", "vlan_based": "Basé sur VLAN", "port_based": "Basé sur le port", "active_ingress_vlan": "Active Ingress VLAN", "active_egress_vlan": "Active Egress VLAN", "active_ingress_port": "Active Ingress Ports", "active_egress_port": "Active Egress Ports", "ingress_setting_hint": "Le VLAN actif et le VLAN de règle doivent être identiques.", "egress_setting_hint": "Les règles avec une action de redirection ne peuvent pas être appliquées aux interfaces de sortie.", "ingress_setting_vlan_hint": "Le VLAN actif et le VLAN de règle doivent être identiques.\nLes règles avec une action de remarque ne peuvent pas être appliquées aux interfaces d'entrée.", "egress_setting_vlan_hint": "Le VLAN actif et le VLAN de règle doivent être identiques.\nLes règles avec une action de redirection ne peuvent pas être appliquées aux interfaces de sortie.", "rule_type": "Rule Type", "index_priority_hint": "Rules with a lower index have a higher priority.", "acl_rule": "ACL Rule", "rule": "Rule", "rule_index": "Rule Index {{ruleIndex}}", "permit": "Permit", "deny": "<PERSON><PERSON>", "ethertype_value": "EtherType Value", "goose": "GOOSE", "smv": "SMV", "protocol_number": "Protocol Number", "user_defined": "User-defined", "source": "Source", "source_port": "Source Port", "source_ip_addr": "Source IP Address", "source_ip_mask": "Source IP Mask", "source_mac_addr": "Source MAC Address", "source_mac_mask": "Source MAC Mask", "destination_port": "Destination Port", "destination_ip_addr": "Destination IP Address", "destination_ip_mask": "Destination IP Mask", "destination_mac_addr": "Destination MAC Address", "destination_mac_mask": "Destination MAC Mask", "cos_remark": "CoS Remark", "dscp_remark": "DSCP Remark", "optional_parameter": "Optional Parameter", "log": "Log", "logging": "Logging", "logging_enable": "Logging Enable", "src_port": "Src. Port", "dst_port": "Dst. Port", "icmp_type": "Type ICMP", "icmp_code": "Code ICMP", "igmp_type": "Type IGMP", "redirect": "Réorienter", "redirect_mirror": "Redirection/Miroir", "redirect_enable": "Réorienter", "redirect_port": "Port de redirection", "redirect_port_name": "Redirection vers le port {{ portName }}", "mirror": "Miroir", "session_id": "Session {{id}}", "mirror_disable_hint": "L'action Miroir n'est pas disponible si la fonction de mise en miroir est désactivée sur le port.", "session_disable_hint": "L'action 'Miroir' ne peut pas prendre effet sur une session de mise en miroir de port désactivée.", "mirror_sesstion": "Miroir à session {{ sesstionId }}", "remark_cos": "Remark CoS to {{ cos }}", "remark_dscp": "Remark DSCP to {{ dscp }}", "acl_table_of_name": "ACL Table of {{ aclName }}", "delete_acl_list_desc": "Êtes-vous sûr de vouloir supprimer la ou les listes de contrôle d'accès sélectionnées?", "delete_acl_rule_desc": "Êtes-vous sûr de vouloir supprimer la ou les règles sélectionnées?", "any_hint": "Si aucune valeur n'est saisie, elle sera considérée comme définie sur Tout.", "log_interval": "Intervalle d'enregistrement", "log_threshold": "Seuil de journalisation", "acl_summary": "Résumé ACL", "number_of_activate_acl": "Nombre d'ACL activées (Max. 16)", "activate_direct": "Direction", "ingress": "Entrée", "egress": "<PERSON><PERSON><PERSON>", "both": "Les deux", "activated": "Activé", "inactivated": "Désactivé", "hit_count": "Compte de coups", "counter": "Comptoir", "view_list": "Aff<PERSON>r la liste", "view_by_acl": "Afficher par ACL", "view_by_port": "Vue par port", "view_by_vlan": "Vue par VLAN", "acl_table_of_type": "Tableau ACL de {{typeIndex}}", "no_activated_acl_port": "Aucune ACL n'est activée sur ce port.", "no_activated_acl_vlan": "Aucune ACL n'est activée sur ce VLAN.", "status_hint": "Les règles avec un indice inférieur ont une priorité plus élevée. L'appareil commencera à faire correspondre les paquets à toutes les règles dans l'ordre numérique, en commençant par l'index le plus bas. Si le paquet correspond à une règle, la règle correspondante sera appliquée.", "title_create_access_list": "<PERSON><PERSON><PERSON> une liste d'accès", "title_edit_access_list": "Modifier {{ typeIndex }} Paramètres de la liste d'accès", "title_delete_acl_list": "Supprimer la ou les listes d'accès", "title_create_acl_rule": "<PERSON><PERSON>er un index de règles {{ ruleIndex }} pour {{ typeIndex }}", "title_edit_acl_rule": "Modifier l'index des règles {{ ruleIndex }} de {{ typeIndex }}", "title_delete_acl_rule": "<PERSON><PERSON><PERSON><PERSON> règle(s)", "title_clear_acl_counter": "Effacer les compteurs", "desc_clear_all_acl_counter_desc": "Êtes-vous sûr de vouloir réinitialiser tous les compteurs?", "desc_clear_single_acl_counter_desc": "Êtes-vous sûr de vouloir réinitialiser le compteur du {{ typeIndex }} LCA?", "blacklist_udp_port_dhcp_server": "Le serveur DHCP n'est pas autorisé", "blacklist_udp_port_dhcp_client": "Le client DHCP n'est pas autorisé", "blacklist_udp_port_moxa_command": "Le service Moxa n'est pas autorisé", "blacklist_ether_type_eth_confg_test_protocol": "Le protocole de test de configuration Ethernet n'est pas autorisé", "blacklist_ether_type_lldp": "LLDP n'est pas autorisé", "blacklist_ether_type_eapol": "EAPOL n'est pas autorisé", "blacklist_ether_type_lacp": "Le LACP n'est pas autorisé", "blacklist_ether_type_llc_jumbo_frame": "La trame LLC Jumbo n'est pas autorisée", "blacklist_ether_type_arp": "ARP n'est pas autorisé", "blacklist_ether_type_mrp": "Le MRP n'est pas autorisé", "blacklist_ether_type_profinet": "PROFINET n'est pas autorisé", "blacklist_ether_type_ptp": "PTP n'est pas autorisé", "blacklist_ether_type_goose": "GOOSE n'est pas autorisé", "blacklist_ether_type_smv": "Le SMV n'est pas autorisé", "blacklist_mac_ieee_reserved_multicast": "L'adresse MAC de multidiffusion réservée IEEE n'est pas autorisée", "blacklist_mac_ip_multicast": "L'adresse MAC de multidiffusion IP n'est pas autorisée", "blacklist_mac_broadcast": "L'adresse MAC de diffusion n'est pas autorisée", "blacklist_mac_l2_multicast": "L'adresse MAC de multidiffusion L2 n'est pas autorisée", "blacklist_mac_device": "L'adresse MAC de l'appareil n'est pas autorisée", "blacklist_dest_ip_multicast": "L'adresse IP multidiffusion n'est pas autorisée", "overwrite_vlan_dialog_title": "Remplacer la règle VLAN par un VLAN actif", "overwrite_vlan_dialog_content": "Le VLAN actif et le VLAN de règle doivent être identiques. Êtes-vous sûr de vouloir que le VLAN actif remplace le VLAN de règle ?"}, "stream_id": {"page_title": "Identification de flux", "title_create_stream_id": "<PERSON><PERSON><PERSON> un flux", "title_edit_stream_id": "Modifier ce flux", "title_delete_stream_id": "Supprimer le(s) flux", "delete_stream_id_desc": "Êtes-vous sûr de vouloir supprimer le ou les flux sélectionnés?"}, "8021cb": {"page_title": "Frame Replication and Elimination for Reliability (FRER)", "frer": "FRER", "split": "Split", "forward": "Forward", "merge": "<PERSON><PERSON>", "stream_vid_mac": "Stream (VLAN / MAC Address)", "input_port": "Input Port", "input_ports": "Input Ports", "output_port_index": "Output Port {{ portIndex }}", "output_port": "Output Port", "output_ports": "Output Ports", "ingress_stream": "Ingress Stream", "egress_stream": "Egress Stream", "vlan_overwrite": "Écrasement VLAN", "mac_address_overwrite": "Écraser l'adresse MAC", "priority_overwrite": "Ecrasement prioritaire", "overwrite": "<PERSON><PERSON><PERSON><PERSON>", "disable_port_input_hint": "Ceci est un port d'entrée sélectionné.", "disable_port_vlan_hint": "Ce port n'est pas membre du VLAN correspondant.", "disable_vid_overwrite_hint": "Le port de sortie n'est pas membre du VLAN correspondant.", "disable_exist_stream_hint": "Il existe déjà une entrée FRER pour ce flux.", "disable_select_stream_hint": "Le flux a déjà été sélectionné.", "to_end_device": "Pour terminer l'appareil", "ingress_size_limitation": "Le flux d'entrée maximal est {{ size }}.", "title_create_frer_entry": "C<PERSON>er une entrée FRER", "title_edit_frer_entry": "Modifier cette entrée FRER", "title_delete_frer_entry": "Supprimer cette entrée FRER", "delete_frer_entry_desc": "Êtes-vous sûr de vouloir supprimer les entrées FRER sélectionnées ?"}, "loop_protection": {"page_title": "<PERSON> de boucle réseau", "detect_interval": "Intervalle de détection", "loop_status": "Loop Status", "peer_port": "Peer Port", "looping": "Looping"}, "binding_database": {"page_title": "Binding Database", "binding_settings": "Binding Settings", "binding_status": "Binding Status", "binding_status_hint": "La liaison dynamique apprend de la surveillance DHCP.", "binding_status_hint_2": "L'état de la liaison ne sera pas mis à jour si la combinaison d'ID de VLAN et d'adresse MAC de l'entrée statique existe déjà.", "title_create_entry": "C<PERSON>er une entrée statique de base de données de liaison", "title_edit_entry": "Modifier cette entrée statique de base de données de liaison", "duplicate_of_dynamic_entry": "La combinaison ID VLAN et adresse MAC existe déjà. Cette nouvelle entrée écrasera l'entrée dynamique initiale.", "size_limitation": "Le nombre maximum d'entrées d'état de liaison est {{ size }}.", "binding_table_max": "Max. {{ size }} of Binding Status table", "dai": "DAI", "ipsg": "IPSG", "ipsg_dai": "IPSG, DAI"}, "dhcp_snooping": {"page_title": "Surveillance DHCP", "port_is_ip_sg_enable": "Ce port est activé pour IP Source Guard. IP Source Guard ne peut être activé que sur un port non approuvé.", "port_is_dai_enable": "Ce port est activé pour l'inspection ARP dynamique. L'inspection ARP dynamique ne peut être activée que sur un port non approuvé.", "port_is_ip_sg_and_dai_enable": "Ce port est activé pour l'inspection ARP dynamique et la protection de la source IP. L'inspection ARP dynamique et la protection de la source IP ne peuvent être activées que sur un port non approuvé."}, "ip_source_guard": {"page_title": "Protecteur de source IP", "port_is_trusted": "Ce port est un port de confiance pour DHCP Snooping. Seuls les ports non approuvés peuvent être activés pour IP Source Guard.", "port_is_la_member": "Ce port est membre de Port Channel. IP Source Guard ne peut pas être activé sur le port membre.", "binding_status_single_empty": "Le statut de liaison du port {{ port }} est vide.", "binding_status_multiple_empty": "Le statut de liaison du port {{ port }} sont vides.", "binding_setting_hint": "<PERSON><PERSON> de<PERSON> activer la surveillance DHCP pour obtenir la liaison dynamique ou configurer les données dans la base de données de liaison -> Paramètres de liaison."}, "mms": {"page_title": "MMS", "ied_name": "Nom de l'IED", "cid_file_settings": "Paramètres du fichier CID", "report_control_block": "Bloc de contrôle de rapport", "data_change": "Modification des données", "data_update": "Mise à jour des données", "quality_change": "Changement de qualité", "integrity": "Intégrité", "buffer_time": "Temps tampon", "integrity_period": "Période d'intégrité", "t_profile_cert_info": "Informations sur le certificat de profil en T", "a_profile_cert_info": "Informations sur le certificat de profil A", "ca_name": "Nom de l'autorité de certification", "t_profile_security": "Sécurité du profil en T", "a_profile_security": "Sécurité du profil A", "import_client_ca": "Importation de l'autorité de certification cliente", "import_client_cert": "Importer un certificat client", "title_edit_name": "Modifier {{ name }}", "title_mms_enable_warning": "Activer le protocole MMS", "mms_enable_warning_desc": "Êtes-vous sûr de vouloir activer le protocole non sécurisé (MMS)?"}, "password_policy": {"page_title": "Politique de mot de passe", "minimum_length": "Longueur minimale du mot de passe", "policy_numbers": "<PERSON><PERSON> contenir au moins un chiffre (0-9)", "policy_uppercase": "Doit contenir au moins une lettre majuscule (AZ)", "policy_lowercase": "Doit contenir au moins une lettre minuscule (az)", "policy_symbols": "Doit contenir au moins un caractère spécial ({}[]()|:;~!@#%^*-_+=,.)", "max_life_time": "Durée de validité maxi.", "password_complexity_strengh_check": "Vérification de la complexité du mot de passe"}, "system_info": {"page_title": "Paramètres d'informations", "system_name": "Nom de l'appareil", "contact_information": "Coordonnées", "sync_to_chassis_id_hint": "Lorsque le sous-type d'ID de châssis LLDP est défini sur « local », la modification du nom du périphérique entraînera la modification simultanée de l'ID de châssis LLDP."}, "login_authentication": {"page_title": "Authentification de connexion", "authentication_protocol": "Protocole d'authentification", "radius_local": "RADIUS, Local", "tacacs_local": "TACACS+, Local"}, "login_policy": {"page_title": "Politique de connexion", "login_message": "Message de connexion", "auth_fail_message": "Message d'échec d'authentification de connexion", "failure_lockout": "Échec de connexion verrouillé", "retry_failure_threshold": "Seuil de défaillance", "lockouttime": "<PERSON><PERSON><PERSON>rrouillage", "auto_logout_setting": "Déconnexion automatique après", "auto_logout_warring_title": "Désactiver la déconnexion automatique", "auto_logout_setting_alert": "Si la valeur de déconnexion automatique est définie sur 0, la session n'expirera jamais. Veuillez vous déconnecter avant de fermer votre navigateur."}, "ip_settings": {"page_title": "Configuration IP", "ip_settings": "Paramètres IP", "ip_status": "Statut IP", "get_ip_from": "Obtenir l'IP de", "dns_server": "Adresse IP du serveur DNS", "ipv6": "IPv6", "ipv6_global_unicast_address_prefix": "Préfixe d'adresse de monodiffusion globale IPv6", "ipv6_dns_server_number": "Serveur DNS IPv6 {{ number }}", "ipv6_dns_server": "Serveur DNS IPv6", "ipv6_global_unicast_address": "Adresse de monodiffusion globale IPv6", "ipv6_link_local_address": "Adresse lien-local IPv6", "profinet_dcp": "PROFINET DCP", "dhcp_bootfile": "Fichier de démarrage DHCP", "dhcp_bootfile_hint": "Si cette option est activée, le système téléchargera et restaurera automatiquement les paramètres de configuration du fichier de démarrage décrit dans l’Option 67 à partir du serveur de fichiers décrit dans l’Option 66.", "dhcp_client": "Identifiant client DHCP", "dhcp_client_hint": "Si cette option est activée, le système envoie des messages client DHCP avec une balise Option 61 incluant un ID client. Le serveur DHCP attribue l'adresse IP associée à la valeur d'ID client, si disponible.", "dhcp_client_type": "Type d'identifiant client DHCP", "dhcp_client_value": "Valeur d'identifiant client DHCP"}, "management_interface": {"page_title": "Interface de gestion", "user_interface": "Interface utilisateur", "interface": "interface", "enable_http": "HTTP", "http_port": "HTTP - Port TCP", "enable_https": "HTTPS", "https_port": "HTTPS - Port TCP", "enable_telnet": "Telnet", "telnet_port": "Telnet - Port TCP", "ssh_port": "SSH-Port TCP", "enable_snmp_V1V2c": "Version SNMP V1, V2c", "snmp_protocol": "SNMP - Protocole de la couche transport", "udp": "UDP", "tcp": "TCP", "snmp_udp_port": "SNMP - Port UDP", "snmp_tcp_port": "SNMP - Port TCP", "enable_moxa_service": "Service Moxa", "moxa_tcp_port": "Service Moxa (crypté) - Port TCP", "moxa_udp_port": "Service Moxa (crypté) - Port UDP", "max_session_http": "Nombre maximum de sessions de connexion pour HTTP+HTTPS", "max_session_terminal": "Nombre maximum de sessions de connexion pour Telnet+SSH", "enable_nonsecure_interface_warning_title": "Activer le {{ interfaceType }} Interface", "enable_nonsecure_interface_warning": "Êtes-vous sûr de vouloir activer l'interface non sécurisée ({{ interfaceType }}) ?"}, "hareward_interface": {"page_title": "Hardware Interfaces", "dip_switch": "DIP Switch", "usb_function": "USB Interface", "micro_sd_function": "MicroSD Interface"}, "account_management": {"page_title": "Comptes utilisateur", "admin": "Admin", "supervisor": "Supervisor", "user": "User", "new_password": "Nouveau mot de passe", "title_edit_account": "Modifier ce compte", "title_add_account": "<PERSON><PERSON>er un nouveau compte", "title_edit_account_password": "Modifier le mot de passe du compte", "new_pwd_not_match": "Le mot de passe ne correspond pas.", "tech_account_add_error": "Le compte \"moxasupport\" ne peut pas être créé car il est réservé au support technique Moxa.", "tech_account_remove_error": "Le compte \"moxasupport\" ne peut pas être modifié ou supprimé car il est réservé au support technique Moxa.", "account_name_taken": "Ce nom d'utilisateur de compte est déjà pris", "size_limitation": "Le nombre maximum de comptes d'utilisateurs pour cet appareil est {{ size }}."}, "time": {"page_title": "Le temps du système", "sntp": "SNTP", "ntp": "NTP", "time_zone": "Time Zone", "current_time": "Current Time", "daylight_saving": "Daylight Saving", "end_date": "End Date", "offset": "Offset", "ntp_authentication": "Authentification NTP", "query_interval": "Intervalle de requête", "ptp": "PTP", "start": "Start", "end": "End", "date": "Date", "month": "<PERSON><PERSON>", "week": "<PERSON><PERSON><PERSON>", "day": "Jour", "hour": "<PERSON><PERSON>", "minute": "Minute", "jan": "Jan", "feb": "Fév", "mar": "Mar", "apr": "Avr", "may": "<PERSON>", "jun": "Juin", "jul": "<PERSON><PERSON>", "aug": "Août", "sep": "Sep", "oct": "Oct", "nov": "Nov", "dec": "Déc", "1st": "1er", "2nd": "2ème", "3rd": "3ème", "4th": "4ème", "last": "<PERSON><PERSON>", "sun": "<PERSON><PERSON><PERSON>", "mon": "<PERSON><PERSON>", "tue": "<PERSON><PERSON>", "wed": "<PERSON><PERSON><PERSON><PERSON>", "thu": "<PERSON><PERSON>", "fri": "<PERSON><PERSON><PERSON><PERSON>", "sat": "<PERSON><PERSON>", "time_server_number": "Time Server {{ number }}", "time_server_1": "1st Time Server: IP Address/Domain Name", "time_server_2": "2nd Time Server: IP Address/Domain Name", "clock_source": "Clock Source", "key_string": "Key String", "delete_entry_confirm_desc": "Êtes-vous sûr de vouloir supprimer la ou les chaînes de clé sélectionnées?", "size_limitation": "Le nombre maximum de clés d'authentification NTP pour cet appareil est {{ size }}."}, "ntp_server": {"page_title": "Serveur NTP"}, "ssh_ssl": {"page_title": "SSH & SSL", "ssh": "SSH", "ssl": "SSL", "regen_ssh_key": "Régénérer la clé SSH", "regen_ssl_cert": "Régénérer le certificat SSL", "export_ssl_cert": "Exporter le certificat SSL", "import_ssl_cert": "Certificat d'importation", "ssl_info": "Informations sur le certificat", "ca_name": "Nom de l'autorité de certification", "title_export_ssl_certificate": "Exporter le certificat SSL"}, "dhcp": {"page_title": "Serveur DHCP", "dhcp": "DHCP", "ntp_server": "Adresse IP du serveur NTP", "dhcp_pool_settings": "Paramètres pool de serveurs DHCP", "static_ip_assignment_table": "Table d’attribution d’adresses IP statiques", "start_ip": "Adresse IP de départ", "end_ip": "Adresse IP de fin", "lease_time": "<PERSON><PERSON><PERSON> du <PERSON>", "hostname": "Nom d'hôte", "log_server": "Adresse IP du serveur de journalisation", "gateway": "<PERSON><PERSON><PERSON>", "matching_rule": "Règle d’appariement", "client_id_type": "type d’identificateur client", "client_id_value": "valeur d’identificateur client", "circuit_id_type": "Option 82 Type d’identification de circuit", "circuit_id_value": "Option 82 Valeur d’identification du circuit", "remote_id_type": "Option 82 Type d’identification à distance", "remote_id_value": "Option 82 Valeur d’identification à distance", "hostname_hint": "Le nom d'hôte représente le nom du client DHCP et sera encodé dans la balise Option 12 du paquet d'offre DHCP.", "time_left": "Temps restant", "dhcp_ip_mac": "Affectation IP basée sur DHCP/MAC", "dhcp_static_ip": "Attribution DHCP/IP statique", "lease_table": "Tableau des baux", "ip_mac_binding": "Affectation IP basée sur MAC", "ip_port_binding": "Affectation IP basée sur le port", "classless_static_route_table": "Table de routage statique sans classe", "delete_dhcp_entry_confirm_desc": "Êtes-vous sûr de vouloir supprimer ce pool de serveurs DHCP?", "delete_static_ip_entry_confirm_desc": "Êtes-vous sûr de vouloir supprimer la ou les entrées IP statiques sélectionnées?", "delete_ip_port_entry_confirm_desc": "Êtes-vous sûr de vouloir supprimer la ou les attributions d'IP basées sur les ports sélectionnées?", "delete_ip_mac_entry_confirm_desc": "Êtes-vous sûr de vouloir supprimer la ou les attributions d'IP basées sur MAC sélectionnées?", "dhcp_size_limitation": "Le nombre maximum de pools de serveurs DHCP pour cet appareil est {{ size }}.", "invalid_dhcp_pool_range": "Invalide:  L'adresse IP de gestion du commutateur doit se trouver dans la plage du sous-réseau IP.", "delete_static_route_entry_confirm_desc": "Êtes-vous sûr de vouloir supprimer l'itinéraire sélectionné?", "default_gateway_setting_hint": "La passerelle par défaut pour les routes statiques sans classe utilise l'adresse de passerelle par défaut configurée dans la section de configuration Attribution IP basée sur le port."}, "dhcp_relay": {"page_title": "DHCP Relay Agent", "option82": "Option 82", "server1": "1ère adresse IP du serveur", "server2": "Adresse IP du 2e serveur", "server3": "Adresse IP du 3e serveur", "server4": "Adresse IP du 4ème serveur", "remote_id_type": "Type d'identification à distance", "remote_id_value": "Valeur d'identification à distance", "remote_id_display": "Affichage de l'ID à distance", "client_id": "Client ID", "relay": "<PERSON><PERSON><PERSON>"}, "ping": {"page_title": "ping", "ping_result": "Pinger {{ targetHost }} résultat"}, "email_settings": {"page_title": "Paramètres de messagerie", "tls_enable": "TLS", "sender_address": "<PERSON><PERSON><PERSON> de l'expéditeur", "recipient_1": "Adresse e-mail du 1er destinataire", "recipient_2": "Adresse e-mail du 2e destinataire", "recipient_3": "Adresse e-mail du 3ème destinataire", "recipient_4": "Adresse e-mail du 4e destinataire", "recipient_5": "Adresse e-mail du 5ème destinataire"}, "snmp": {"page_title": "SNMP", "v1": "V1, V2c, V3", "v2c": "V1, V2c", "v3": "V3 Only", "snmp_version": "Version SNMP", "snmp_account": "Compte SNMP", "read_community": "Communa<PERSON><PERSON>", "read_write_community": "Communauté de lec-/écriture", "read_write": "Read/Write", "des": "DES", "aes": "AES", "snmp_account_size_limitation": "Le nombre maximum de comptes SNMP pour cet appareil est {{ size }}.", "snmp_warning_dialog_V1V2c_title": "Définir la version SNMP", "snmp_warning_dialog_V1V2c_desc": "Etes-vous sûr de vouloir activer l'interface non sécurisée (SNMP version V1, V2c)?", "snmp_warning_dialog_authMD5_title": "Définissez le type d'authentification MD5", "snmp_warning_dialog_authMD5_desc": "L'authentification MD5 n'offre qu'une sécurité limitée. Êtes-vous sur de vouloir continuer?", "change_password_title": "Modifier le mot de passe d'authentification", "change_key_title": "Modifier la clé de chiffrement", "change_key_button": "CHANGER LA CLE DE CRYPTAGE", "create_v3_account": "C<PERSON>er un compte SNMP", "edit_v3_account": "Modifier ce compte SNMP"}, "snmp_trap": {"page_title": "Interruption/Informer SNMP", "snmp_trap_inform_recipient": "Interruption SNMP/Informer le destinataire", "snmp_inform_settings": "Paramètres d'information SNMP", "inform_retry": "Informer les tentatives", "inform_timeout": "Informer le d<PERSON><PERSON>", "recipient_name": "Adresse IP du destinataire/Nom de domaine", "trap_community": "Communauté des pièges", "snmp_trap_inform_account": "Comptes SNMP Trap/Inform", "trap_v1": "Trap V1", "trap_v2c": "Trap V2c", "inform_v2c": "Inform V2c", "trap_v3": "Trap V3", "inform_v3": "Inform V3", "title_delete_host_dialog_title": "Supp<PERSON>er cet hôte", "title_delete_host_dialog_desc": "Êtes-vous sûr de vouloir supprimer cet hôte?", "snmp_trap_account_size_limitation": "Le nombre maximum de comptes d'interruption/d'information SNMP pour ce périphérique est {{ size }}.", "snmp_host_size_limitation": "Le nombre maximum d'hôtes d'interruption SNMP pour ce périphérique est {{ size }}.", "create_v3_trap_account": "<PERSON><PERSON>er un compte d'interruption SNMP", "edit_v3_trap_account": "Modifier ce compte d'interruption SNMP", "create_host_table": "<PERSON><PERSON><PERSON> un hôte", "edit_host_table": "Modifier cet hôte"}, "arp": {"page_title": "Tableau ARP", "title_clear_message": "Effacer toutes les entrées ARP", "clear_confirmation_message": "Êtes-vous sûr de vouloir effacer toutes les entrées ARP?"}, "event_log": {"page_title": "Journaux d'événements", "boot": "Numéro de démarrage", "progname": "Nom du programme", "timestamp": "Horodatage", "uptime": "Temps de disponibilité", "message": "Message", "severity_emerg": "Emergency", "severity_alert": "<PERSON><PERSON>", "severity_info": "Info", "severity_debug": "Debug", "flush_log_entry_confirmation_message": "Êtes-vous sûr de vouloir effacer toutes les entrées du journal?", "total_entries": "Entrées totales: ", "clear_all_logs": "Effacer tous les journaux", "capacity_warning": "Avertissement de capacité", "capacity_warning_hint": "L'action enregistrée peut être configurée pour des événements individuels sur la page Notifications d'événements.", "warning_threshold": "Seuil d'avertissement", "oversize_action": "Action surdimensionnée", "oversize_action_overwrite": "Overwrite the oldest event log", "oversize_action_stop": "Stop recording event logs", "title_clear_all_logs": "Effacer tous les journaux", "debug_hint": "{{ number }} les journaux sont à usage interne.", "hash_value": "<PERSON><PERSON> de ha<PERSON>ge", "auto_backup_of_event_log": "Sauvegarde automatique du journal des événements"}, "trust_access": {"page_title": "Accès de confiance", "size_limitation": "Le nombre maximum d'entrées d'accès de confiance pour ce périphérique est {{ size }}.", "delete_all_warning_title": "Impossible de supprimer toutes les entrées d'accès de confiance", "delete_all_warning_1": "Si activé, Trusted Access nécessite au moins une entrée active. Il est fortement recommandé de conserver", "delete_all_warning_2": "votre adresse IP actuelle", "delete_all_warning_3": "comme une entrée active."}, "utilization": {"page_title": "Utilisation des ressources", "cpu_utilization": "L'utilisation du processeur", "cpu_historical_record": "Historique d'utilisation du processeur", "mem_utilization": "Utilisation de la mémoire", "mem_historical_record": "Historique d'utilisation de la mémoire", "power_utilization": "Consommation d'énergie", "power_historical_record": "Historique de la consommation d'énergie", "last_update_time": "Dernière mise à jour", "used": "<PERSON><PERSON><PERSON><PERSON>", "free": "<PERSON><PERSON><PERSON>", "past_10_second": "Dernières 10 secondes", "past_30_second": "Dernières 30 secondes", "past_300_second": "Dernières 300 secondes", "selecting_visible_polyline": "Modifier les polylignes visibles", "polyline_display_hint": "Cliquez sur l'icône dans le coin supérieur droit du widget pour sélectionner les données à afficher."}, "tacacs_server": {"page_title": "Serveur TACACS+", "tacacs": "TACACS+", "auth_type_asc_two": "ASCII"}, "syslog_server": {"page_title": "Syslog", "syslog_server": "Serveur Syslog", "auth_disable_hint": "Le certificat et la clé ne peuvent pas être activés car ils n'existent pas.", "tls": "TLS", "common_name": "Common Name", "expireTime": "Date d'expiration", "key_limitation": "Le nombre maximum de certifications et de jeux de clés pour cet appareil est {{ size }}.", "title_add_key": "Ajouter un certificat et un jeu de clés", "title_edit_key": "Modifier ce certificat et cette clé", "delete_key_desc": "Êtes-vous sûr de vouloir supprimer le certificat et la clé?", "client_certificate": "Client Certificate", "client_key": "Client Key", "ca_key": "CA Key"}, "radius": {"page_title": "Serveur RADIUS", "radius": "RAYON", "server_address_number": "Adresse IP du serveur {{ number }}", "mschap": "MS-CHAPv1", "mschap_v2": "MS-CHAPv2"}, "config_bk_res": {"page_title": "Sauvegarde et restauration de la configuration", "menu_title": "Sauvegarde et restauration de la configuration", "file_encryption": "File Encryption", "file_signature": "File Signature", "config_name": "Nom de configuration", "config_file_encryption": "Configuration File Encryption", "configuration_selection": "Select Configuration", "running_configuration": "Running Configuration", "startup_configuration": "Startup Configuration", "default_configuration": "Default Configuration", "not_included": "Not Included", "included": "Included", "signed_config": "Signed Configuration", "sign_hint": "Lorsqu'elle est activée, une signature numérique est ajoutée lorsqu'un administrateur sauvegarde ou restaure la configuration.", "sign_disable_hint": "Cette fonction ne peut pas être activée car les clés privée et publique sont vides.", "private": "Priv<PERSON>", "certificate": "Certificat", "label": "<PERSON><PERSON><PERSON><PERSON>", "length": "<PERSON><PERSON><PERSON>", "key_limitation": "Le nombre maximum de paires de clés pour cet appareil est {{ size }}.", "title_add_key": "Ajouter une clé personnalisée", "title_edit_key": "Modifier cette clé personnalisée", "delete_key_desc": "Êtes-vous sûr de vouloir supprimer cette paire de clés?", "auto_bk_of_config": "Sauvegarde automatique de la configuration", "auto_load_of_config": "Restauration automatique de la configuration", "auto_restore": "Restauration automatique de la configuration", "auto_restore_hint": "Restaurer automatiquement la configuration à partir d'un périphérique de stockage externe lors du démarrage.", "encrypt_whole_file": "Chiffrer l’intégralité du fichier", "encrypt_sensitive_information_only": "<PERSON><PERSON><PERSON> uniquement les informations sensibles", "encrypt_hint": "Si l’option \"Chiffrer uniquement les informations sensibles\" est sélectionnée et que le champ Clé de chiffrement est laissé vide, la clé de chiffrement Moxa sera utilisée à la place."}, "firmware_upgrade": {"page_title": "Mise à jour du firmware"}, "module_information": {"page_title": "Informations sur les modules", "module_name": "Nom du module", "no_module_msg": "Aucun module installé"}, "event_notification": {"page_title": "Notifications d'événements", "group": "Groupe", "event_name": "Event Name", "system_and_function": "System and Functions", "registered_event": "Registered Event", "registered_action": "Registered Action", "registered_port": "Registered Port", "group_general": "Général", "group_switching": "Commutation", "group_poe": "PoE", "group_routing": "Routage", "group_tracking": "Tracking", "notification_loginSuccess": "Login success", "notification_loginFail": "<PERSON><PERSON> failed", "notification_loginLockout": "Login lockout", "notification_accountChanged": "Account settings changed", "notification_certificationChanged": "SSL certification changed", "notification_passwordChanged": "Password changed", "notification_coldStart": "Cold start", "notification_warmStart": "Warm start", "notification_configurationChanged": "Configuration changed", "notification_configurationImported": "Configuration imported", "notification_logCapacityThreshold": "Log capacity threshold", "notification_powerOff": "Power On->Off", "notification_powerOn": "Power Off->On", "notification_diOn": "DI on", "notification_diOff": "DI off", "notification_topologyChanged": "Topology changed", "notification_couplingChanged": "Coupling changed", "notification_masterChanged": "Master changed", "notification_masterMismatch": "Master mismatch", "notification_rstpTopologyChanged": "RSTP topology changed", "notification_rstpRootChanged": "RSTP root changed", "notification_rstpMigration": "RSTP migration", "notification_rstpInvalidBpdu": "RSTP invalid BPDU", "notification_rstpNewPortRole": "RSTP new port role", "notification_mstpTopologyChanged": "MSTP topology changed", "notification_mstpRootChanged": "MSTP root changed", "notification_mstpNewPortRole": "MSTP new port role", "notification_linkHealthyCheckFail": "Redundant port health check failed", "notification_dualHomingPathSwitched": "Dual homing path changed", "notification_dot1xAuthFail": "802.1X auth failed", "notification_lldpTableChanged": "LLDP table changed", "notification_rmonRaisingAlarm": "RMON raising alarm", "notification_rmonFallingAlarm": "RMON failing alarm", "notification_macsecInterfaceMKAFail": "MACsec MKA failed", "notification_dhcpsnpDynamicEntrySetFailed": "Binding Status dynamic entry failed", "notification_dhcpsnpUntrustMacDiscard": "DHCP client ingress discards packets due to the DHCP Snooping rule", "notification_dhcpsnpUntrustServerDiscard": "DHCP server discards packets due to the DHCP Snooping rule", "notification_multipleCouplingPathChanged": "Multiple coupling path changed", "notification_dhcpBootfileFail": "DHCP Bootfile failed", "notification_trackingStatusChanged": "Tracking Status Changed", "notification_trackingReactionPort": "Tracking Action Triggered on Port", "notification_trackingReactionStaticRoute": "Tracking Action Triggered on Static Route", "notification_trackingReactionVrrp": "Tracking Action Triggered on VRRP", "notification_pdPowerOn": "PD power on", "notification_pdPowerOff": "PD power off", "notification_lowInputVoltage": "Low input voltage", "notification_pdOverCurrent": "PD over-current", "notification_pdNoResponse": "PD no response", "notification_overPowerBudgetLimit": "Over power budget limit", "notification_powerDetectionFailure": "Power detection failure", "notification_nonPdOrPdShort": "Non-PD or PD short circuit", "notification_portOn": "Port On", "notification_portOff": "Port Off", "notification_rateLimitedOn": "Port shut down by Rate Limit", "notification_rateLimitedOff": "Port recovered by Rate Limit", "notification_psecViolationPortDown": "Port shut down by Port Security", "notification_fiberWarning": "Fiber Check warning", "notification_linkUp": "Interface up", "notification_linkDown": "Interface down", "notification_adjacencyChanged": "OSPF adjacency changed", "notification_drChanged": "OSPF DR changed", "notification_becomeDR": "OSPF become DR", "notification_vrrpMasterChanged": "VRRP virtual router master changed", "notification_pimSmDrChanged": "PIM-SM DR changed", "notification_pimSmRpAdded": "PIM-SM RP added by BSM", "notification_pimSmRpDeleted": "PIM-SM RP deleted by BSM", "notification_supABportTimediff": "A PHR Supervision frame time difference event occurred on ports A, B", "notification_gcTimeout": "GOOSE Check entry counter timeout", "notification_gcTimeoutClear": "GOOSE Check entry counter timeout clear", "notification_gcPortTampered": "GOOSE Check tampered ingress port", "notification_gcAddrTampered": "GOOSE Check tampered source MAC address", "notification_gcLockViolation": "GOOSE Check Lock violation", "notification_gcEntryReset": "GOOSE Check entry reset", "notification_gcEntryDelete": "GOOSE Check entry delete", "action_trap": "Trap", "information": "Information", "title_edit_event_notification": "Modifier cette notification d'événement"}, "relay_output": {"page_title": "<PERSON><PERSON><PERSON>", "relay_alarm_cut_off": "Coupure d'alarme de relais", "relay_alarm_settings": "Paramètres d'alarme de relais", "fault_led_display": "Affichage LED de défaut", "cut_off": "Cut-off"}, "statistics": {"page_title": "Statistiques du réseau", "bandwidth_utilization": "Utilisation de la bande passante", "packet_counter": "Compteur <PERSON>", "rxTotalOctets": "Nombre total d'octets reçus", "collisionPackets": "Paquets de collision", "dropPackets": "<PERSON><PERSON><PERSON> abandon<PERSON>", "dropPacketsHint": "Le temps de mise à jour par défaut du compteur de paquets abandonnés est d'environ 5 secondes et augmente en fonction du nombre de ports sur l'appareil.", "rxPausePackets": "<PERSON><PERSON>", "txTotalOctets": "Octets totaux de transmission", "txUnicastPackets": "Paquets Tx Unicast", "crcAlignErrorPackets": "Paquets d'erreur d'alignement CRC", "txMulticastPackets": "Paquets Tx Multicast", "rxBroadcastPackets": "Paquets de diffusion Rx", "rxUnicastPackets": "Rx Unicast Packets", "jabberPackets": "Paquets Jabber", "excessiveCollisionPackets": "Paquets de collision excessifs", "txTotalPackets": "Tx paquets totaux", "fragmentPackets": "Paquets fragmentés", "rxTotalPackets": "Nombre total de paquets Rx", "lateCollisionPackets": "Paquets de collision tardive", "oversizePackets": "Paquets surdimensionnés", "rxMulticastPackets": "Rx paquets multidiffusion", "txBroadcastPackets": "Paquets de diffusion Tx", "undersizePackets": "Paquets sous-dimensionnés", "txptpPackets": "Tx paquets PTP", "rxptpPackets": "Rx paquets PTP", "displayMode": "Mode d'affichage", "packetCounter": "Packet Counter", "bandwidthUtilization": "Bandwidth Usage", "line_num_target_port": "Doubler {{ number }} Port de surveillance", "line_num_target_sniffer": "Doubler {{ number }} <PERSON><PERSON><PERSON><PERSON>", "txandrx": "Tx/Rx", "txonly": "Tx", "rxonly": "Rx", "all_port": "Tous les ports", "all_ge_port": "Tous les ports GE", "all_fe_port": "Tous les ports FE", "line_num": "Doubler{{ number }}", "clear_graph_desc": "Êtes-vous sûr de vouloir effacer toutes les données du graphique?", "clear_table_desc": "Êtes-vous sûr de vouloir effacer toutes les données de la table?", "benchmark_line": "Référence", "benchmark_line_time": "Ligne de référence - Temps", "comparison_line": "Comparaison", "comparison_line_time": "Ligne de comparaison - Temps", "selecting_visible_columns": "Modifier les colonnes visibles", "title_comparison": "Comparer les données", "title_reset_statistics_graph": "Réinitialiser le graphique des statistiques", "title_edit_statistics_setting": "Paramètres d'affichage", "title_clear_statistics_counter": "Effacer les compteurs"}, "mab": {"page_title": "Contournement d'authen. MAC", "page_title_abbr": "MAB", "auth_mode": "Mode d'authentification", "local_database": "Local Database", "quiet_period": "<PERSON><PERSON><PERSON><PERSON> calme", "reauthentication": "Réauthentification", "reauth_period": "<PERSON>auth Period", "size_limitation": "Le nombre maximum d'entrées d'adresse MAC pour cet appareil est {{ size }}.", "retransmit": "Retransmettre", "force_unauthorized": "Force Unauthorized", "force_Authorized": "Force Authorized", "reauth_port_confirm": "Êtes-vous sûr de vouloir réauthentifier le port?", "authorized": "Autorisé", "unauthorized": "Non autorisé", "title_reauth_port": "Reauth Port", "port_setting": "Port {{ portIndex }} Paramètres", "account_setting": "Compte {{ userName }} Paramètres", "timeout_retransmit_hint": "Tous les temps de tentative ne peuvent pas dépasser la valeur du délai d'attente du serveur Dot1x. Note: Toutes les tentatives = Timeout * (Retransmit + 1). Valeur recommandée: {{ number }}", "clear_button_disabled_hint": "Effacez toutes les entrées d'adresse MAC collectées via le contournement d'authentification MAC lorsque le nombre d'entrées atteint la capacité maximale.", "clear_warning_hint": "Le nombre d'entrées d'adresses MAC collectées via le contournement d'authentification MAC est à sa capacité maximale.", "title_clear_mac_address": "Effacer les adresses MAC", "clear_mac_address_message": "Êtes-vous sûr de vouloir effacer toutes les adresses MAC collectées via le contournement de l'authentification MAC?", "radius_hint": "802.1X et MAC Authentication Bypass partagent le même serveur RADIUS."}, "modbus_tcp": {"page_title": "Modbus TCP", "enable_modbus_tcpo_title": "Activer Modbus TCP", "enable_modbus_tcp_warning": "Êtes-vous sûr de vouloir activer le protocole non sécurisé (Modbus TCP)?"}, "fiber_check": {"page_title": "Vérification de la fibre", "threshold_settings": "Paramètres de seuil", "model_name": "Nom du modèle", "serial_number": "Numéro de série", "wavelength": "<PERSON><PERSON><PERSON> d'onde", "voltage": "Tension", "temperature": "Température", "tx_power": "Puissance Tx", "rx_power": "Puissance Rx", "temperature_limit": "Seuil de température", "tx_power_threshold_low": "Seuil de puissance Tx bas", "tx_power_threshold_high": "Seuil de puissance Tx élevé", "rx_power_threshold_low": "Seuil de puissance Rx bas", "rx_power_threshold_high": "Seuil de puissance Rx élevé", "temp_over_spec": "La température dépasse le seuil.", "tx_power_over_spec": "La puissance de transmission est supérieure au seuil.", "tx_power_under_spec": "La puissance de transmission est inférieure au seuil.", "rx_power_over_spec": "La puissance Rx est supérieure au seuil.", "rx_power_under_spec": "La puissance Rx est inférieure au seuil.", "port_copy_to_copper_port_disable": "Impossible de copier les configurations sur le port cuivre.", "warning": "Avertissement", "reset_title": "Réinitialiser le port {{ portIndex }} Paramètres de seuil", "reset_desc": "Êtes-vous sûr de vouloir réinitialiser ce port en mode automatique (par défaut) et effacer tous les paramètres de seuil?", "reset_all_ports": "Réinitialiser tous les ports", "reset_all_ports_title": "Réinitialiser tous les paramètres de seuil de ports", "reset_all_ports_desc": "Êtes-vous sûr de réinitialiser tous les ports en mode automatique (par défaut) et d'effacer tous les paramètres de seuil?"}, "dai": {"page_title": "Inspection ARP dynamique", "port_is_la_member": "Ce port est membre de Port Channel. L'inspection ARP dynamique ne peut pas être activée sur le port membre.", "port_is_trusted": "Ce port est un port de confiance pour DHCP Snooping. Seuls les ports non approuvés peuvent être activés pour l'inspection ARP dynamique."}, "mac_sec": {"page_title": "Sécurité du contrôle d'accès aux médias", "mka_status": "Statut MKA", "participant_ckn": "Participant CKN", "participant_cak": "Participant CAK", "key_server": "<PERSON><PERSON><PERSON> de c<PERSON>", "peer_list_mi": "Identifiant de membre de liste de pairs (MI)", "peer_list_mn": "Numéro de message de liste de pairs (MN)", "sci": "Identifiant de canal sécurisé (SCI)", "peer_list_type": "Type de liste de pairs", "live_peer_list": "Liste de pairs en direct", "potential_peer_list": "Liste des pairs potentiels", "peer_list_mi_hint": "MI: Les messages MKA incluent le propre ID de membre de l'expéditeur et les ID d'autres pairs potentiels dont il a reçu des messages.", "not_support_10G_port": "MACsec n'est pas pris en charge sur les ports 10G.", "member_port_disable": "Ce port est membre d'un port canalisé. La sécurité du contrôle d'accès aux médias ne peut pas être modifiée sur un port membre."}, "profinet": {"page_title": "PROFINET", "protocol_information": "Informations sur le protocole", "enable_profinet_title": "Activer PROFINET", "enable_profinet_warning": "Êtes-vous sûr de vouloir activer un protocole non sécurisé (PROFINET)?", "vlan_not_exist": "Le VLAN n'existe pas.", "label_format_hint": "C'est le format de l'étiquette. Chaque étiquette doit être séparée par un \".\". Le nombre maximum de caractères pour une étiquette est de 63."}, "ethernet_ip": {"page_title": "EtherNet/IP", "enable_eip_title": "Activer EtherNet/IP", "enable_eip_warning": "Êtes-vous sûr de vouloir activer un protocole non sécurisé (EtherNet/IP)?"}, "multi_coupling": {"page_title": "Couplage r<PERSON> multiple", "main_ring_protocol": "Protocole de l'anneau principal", "switch_role": "<PERSON><PERSON><PERSON>-interrupteur", "group_id": "Coupling-GroupID", "polling_interval": "Intervalle de Coupling-Sondage", "polling_interval_hint": "Pour l'anneau principal, le nombre maximum d'ID de chemin est de 16 lors de l'utilisation d'un intervalle d'interrogation de 80 ms. Lorsque vous utilisez un intervalle d'interrogation de 40 ms, nous vous suggérons d'utiliser 8 ID de chemin.", "table_settings": "Paramètres du tableau de Coupling", "table_status": "État de la table de Coupling", "path_id": "Path ID", "coupling_port": "Coupling Port", "coupling_port_state": "Coupling port state", "partner_mac": "Partner MAC", "connect_status": "Connection Status", "error_status": "Error Status", "multiple_active": "Multiple active switches", "multiple_backup": "Multiple backup switches", "role_information": "Informations sur le rôle", "edit_path_title": "Modifier l'identifiant du chemin {{ pathId }} Paramètres", "multi_coupling_enable_hint": "Le protocole d'anneau principal Turbo Ring V2 ou MRP doit être activé.", "is_coupling_port": "Ceci a été sélectionné comme port de couplage.", "is_selected_path": "Cet ID de chemin a déjà été sélectionné.", "error_status_message": "Plusieurs commutateurs actifs/de secours ont été détectés. Veuillez vérifier cet appareil et l'appareil partenaire pour vous assurer qu'il n'y a pas de doublons."}, "pim_dm": {"page_title": "PIM-DM", "state_refresh": "Actualisation de l'état", "state_refresh_interval": "Intervalle de rafraîchissement de l'état", "pim_dm_hint": "Lorsque PIM-DM est activé, s'il y a une demande d'IGMP, il peut être utilisé avec la surveillance IGMP.", "state_refresh_hint": "Tous les routeurs du réseau en mode PIM-DM doivent activer l'actualisation de l'état et configurer le même intervalle."}, "pim_sm": {"page_title": "PIM-SM", "pim_sm_settings": "Paramètres PIM-SM", "pim_sm_hint": "Lorsque PIM-SM est activé, assurez-vous d'activer le IGMP snooping et les configurations associées en fonction de vos besoins.", "spt_method": "Méthode de permutation de l'arborescence du chemin le plus court", "join_prune_interval": "Join/<PERSON><PERSON>e <PERSON>", "dr_priority": "DR Priority", "bsr": "BSR", "bsr_candidate": "BSR Candidate", "bsr_address": "BSR Address", "bsr_priority": "BSR Priority", "bsr_hash_mask_length": "BSR Hash Mask Length", "dr_priority_hint": "The DR with the larger priority value is preferred in DR election.", "bsr_priority_hint": "The BSR with the larger priority value is preferred in BSR election.", "rp": "RP", "static_rp": "Static RP", "candidate_rp": "Candidate RP", "group_address": "Group Address", "group_mask": "Group Mask", "rp_address": "RP Address", "override": "Override", "interface_not_created": "The PIM-SM interface has not been created yet.", "rp_if_name": "RP Interface Name", "rp_priority": "RP Priority", "static_rp_size_limitation": "Le nombre maximum de RP statiques pour cet appareil est de {{ size }}.", "candidate_rp_size_limitation": "Le nombre maximum de RP candidats pour cet appareil est de {{ size }}.", "ssm": "SSM", "pim_ssm": "PIM-SSM", "pim_ssm_size_limitation": "Le nombre maximum de PIM-SSM pour cet appareil est de {{ size }}.", "title_create_static_rp": "Créer un RP statique", "title_edit_static_rp": "Modifier le RP statique", "title_delete_static_rp": "Supprimer le RP statique", "delete_static_rp_desc": "Êtes-vous sûr de vouloir supprimer le RP statique sélectionné?", "override_hint": "Le remplacement signifie que ce RP statique sera utilisé avant l'apprentissage dynamique (BSR) en cas de conflit.", "title_create_candidate_rp": "C<PERSON>er un candidat RP", "title_edit_candidate_rp": "Modifier le RP du candidat", "title_delete_candidate_rp": "Supprimer le RP candidat", "delete_candidate_rp_desc": "Etes-vous sûr de vouloir supprimer le RP candidat sélectionné?", "rp_priority_hint": "Le RP avec la valeur de priorité la plus petite est préféré lors de l'élection du RP.", "rp_if_name_address": "Nom de l'interface RP (adresse RP)", "title_add_ssm_range": "Ajouter une plage SSM", "title_edit_ssm_range": "Modifier la plage SSM", "title_delete_ssm": "Supprimer SSM", "delete_ssm_desc": "Êtes-vous sûr de vouloir supprimer le SSM sélectionné?", "static_ssm_entry_hint": "Cette plage est réservée au SSM dans la RFC 7761.", "pim_sm_status": "PIM-SM Status", "dr_address": "DR Address", "bsr_rp": "BSR / RP", "elected_bsr": "Elected BSR", "rp_mapping": "RP Mapping", "rp_mapping_result": "RP Mapping Result"}, "multicast_routing_table": {"page_title": "Multicast Routing Table", "multicast_group": "Multicast Group", "upstream_neighbor": "Upstream Neighbor", "incoming_interface": "Incoming Interface", "outgoing_interface": "Outgoing Interface", "prune": "<PERSON><PERSON><PERSON>", "assert": "Assert"}, "prp_hsr": {"page_title": "PRP/HSR", "prp_hsr_protocol": "Protocole PRP/HSR", "entry_forget_time": "<PERSON><PERSON><PERSON> le temps", "net_id": "Net ID", "lan_id": "LAN ID", "prp": "PRP", "hsr": "HSR", "coupling": "Coupling", "enable_prp_hsr_title": "Activer PRP/HSR", "enable_prp_hsr_warning": "L'activation de PRP/HSR réinitialisera la configuration de tous les ports du module PRP/HSR à leurs valeurs par défaut. Es-tu sûr?", "no_phr_module_warning": "Aucun module PHR valide détecté. Vérifiez le module PHR."}, "tracking": {"page_title": "Tracking", "tracking_list_of_interface": "Tracking List of Interface", "tracking_list_of_ping": "Tracking List of Ping", "tracking_list_of_logical": "Tracking List of Logical", "tracking_list_of_all": "Tracking List of All", "tid": "Tracking ID", "down_to_up": "Down to Up", "up_delay": "Up Delay", "up_to_down": "Up to Down", "down_delay": "Down Delay", "received": "Received", "lost": "Lost", "and": "AND", "or": "OR", "nand": "NAND", "nor": "NOR", "entry_state": "État", "interface_tracking": "Suivi des interfaces", "ping_tracking": "Suivi des pings", "logical_tracking": "Logical Tracking", "create_interface_tracking_entry_title": "<PERSON><PERSON><PERSON> une entrée de suivi d'interface", "edit_interface_tracking_entry_title": "Modifier cette entrée de suivi d'interface", "create_ping_tracking_entry_title": "<PERSON><PERSON><PERSON> une entrée de suivi Ping", "edit_ping_tracking_entry_title": "Modifier cette entrée de suivi Ping", "create_logical_tracking_entry_title": "<PERSON><PERSON>er une entrée de suivi logique", "edit_logical_tracking_entry_title": "Modifier cette entrée de suivi logique", "interface_type": "Type d'interface", "port": "Port", "ping": "<PERSON>", "logical": "Logical", "interface": "Interface", "network_interface": "Network Interface", "status_change_from_down_to_up": "Changement d'état (de bas en haut)", "status_change_from_up_to_down": "Changement d'état (de haut en bas)", "logical_list": "Liste logique", "logical_oper": "Opérateur logique", "interfaec_ip_logic": "Interface/Adresse IP/Liste logique", "time_since_last_change": "Temps depuis la dernière modification", "no_of_change": "Nombre de changements", "only_select_four": "Un maximum de quatre peuvent être sélectionnés", "require_tid_larger": "L'ID de suivi doit être supérieur au TID de la liste logique.", "require_at_least_two_tid": "Plus de deux TID sont requis", "duplicated_tid": "ID de suivi en double", "tracking_size_limitation": "Le nombre maximum d'entrées de suivi pour cet appareil est de {{ size }}.", "sync_to_latest_status": "Synchronisation. au dernier état"}, "auto_config": {"page_title": "Configuration automatique", "cdu_port": "Control Unit Port", "auto_config_info": "Port de l’unité de contrôle", "import_mode_hint": "Informations de configuration automatique", "propagate_mode_hint": "Il nécessite l’activation du client DHCP et de LLDP, ainsi que la préconfiguration du fichier de démarrage et de l’ID du client du client DHCP. Dans ce mode, la configuration automatique envoie uniquement les paquets de l’option 61 sur le port de l’unité de contrôle."}, "multi_local_route": {"page_title": "Multicast Local Route", "routes": "Routes", "macl": "MACL", "vrrp_master_only": "VRRP Master Only", "multi_local_route_hint": "Si la route locale de multidiffusion est activée, la surveillance IGMP doit également être activée.", "vrrp_master_only_hint": "S'il est activé, le commutateur ne peut acheminer les flux de multidiffusion que lorsqu'il agit en tant que maître VRRP.", "source_vlan": "Source VLAN", "downstream_vlan": "Downstream VLAN", "multi_local_route_size_limitation": "Le nombre maximum d'entrées de route locale de multidiffusion est de {{ size }}.", "create_route_msg": "Créer une route locale de multidiffusion", "edit_route_msg": "Modifier le VLAN source {{ vid }}", "macl_id": "MACL ID", "title_create_macl_rule": "Créer une ACL de multidiffusion", "title_edit_macl_rule": "Modifier l'ID MACL {{ maclId }}", "only_select_sixteen": "Un maximum de seize peuvent être sélectionnés", "delete_session_title": "Supprimer la route locale de multidiffusion", "delete_session_content": "Êtes-vous sûr de vouloir supprimer la route locale de multidiffusion sélectionnée?", "source_vlan_cannot_set": "Le VLAN source ne peut pas être défini."}, "supervision_frame": {"page_title": "Supervision Frame", "supervision_frame_enable_hint": "Le protocole PRP/HSR doit être activé avant que la trame de supervision ne soit activée.", "life_check_interval": "Life Check Interval", "destination_address": "Adresse de <PERSON>", "forward_to_interlink": "Supervision Forward To Interlink", "nodes_table": "Nodes Table", "forget_time": "Node Forget Time", "node_type": "Node Type", "time_last_seen_a": "Time Last Seen A", "time_last_seen_b": "Time Last Seen B"}, "goose_check": {"page_title": "Vérification GOOSE", "goose_lock": "Verrouillage GOOSE", "goose_lock_hint": "Si le verrouillage GOOSE est activé, les paquets GOOSE qui ne sont pas affichés dans le tableau de surveillance seront supprimés.", "tamper_response": "Réponse aux altérations", "tamper_response_hint": "Le choix de « supprimer » supprimera tous les paquets GOOSE altérés. Le choix de « désactiver le port » désactivera le port d'entrée des paquets GOOSE altérés.", "port_disable": "Port Disable", "app_id": "APP ID", "goose_address": "GOOSE Address (DA)", "monitoring_table_status": "Monitoring Table Status", "goose_lock_status": "GOOSE Lock Status", "lock_violation_status": "Lcok Violation Status", "goose_name": "GoCB Name", "rx_counter": "Rx Counter", "create_goose": "Create Static GOOSE Entry", "port_tampered": "Tampered Port", "sa_tampered": "Tampered SA", "duplicate_goose": "Une entrée GOOSE statique identique existe déjà", "exist_dynamic_entry": "Une entrée GOOSE apprise dynamiquement identique existe déjà. Cliquer sur Appliquer changera cette entrée en statique.", "lock_violation_normal_hint": "Tous les paquets GOOSE détectés sont affichés dans la table de surveillance.", "lock_violation_warning_hint": "Des paquets GOOSE inattendus ont été détectés qui ne sont pas affichés dans la table de surveillance.", "goose_table_max": "Max. {{ size }} of Monitoring table", "size_limitation": "Le nombre maximal d'entrées de contrôle GOOSE pour ce périphérique est de {{ size }}."}}, "request_handler": {"action_saving": "enregistrement en cours", "action_loading": "Chargement ...", "action_upgrading": "Mise à niveau en cours ...", "action_ping": "Pinging..."}, "response_handler": {"res_server_error": "Erreur de connexion au serveur.", "res_global_success": "Mise à jour réussie.", "res_complete_refresh": "Actualisation terminée.", "res_complete_encrypt_data": "Cryptage des données terminé.", "res_port_success": "Les paramètres du port ont été mis à jour avec succès.", "res_entry_create_success": "L'entrée a été créée avec succès.", "res_entry_update_success": "L'entrée a été mise à jour avec succès.", "res_entry_delete_success": "L'entrée a été supprimée avec succès.", "res_port_enable_success": "Port {{ portIndex }} a été activé avec succès.", "res_port_disable_success": "Port {{ portIndex }} a été désactivé avec succès.", "res_dscp_success": "Les paramètres DSCP ont été mis à jour avec succès.", "res_cos_success": "Les paramètres CoS ont été mis à jour avec succès.", "res_regen_ssh_success": "La clé SSH a été régénérée avec succès.", "res_regen_ssl_success": "Le certificat SSL a été régénéré avec succès.", "export_ssl_cert": "Le certificat SSL a été exporté avec succès.", "import_ssl_cert": "Le certificat SSL a été importé avec succès.", "import_config": "Configuration importée avec succès.", "res_copy": "<PERSON><PERSON><PERSON>.", "res_ping_success": "<PERSON> terminé.", "res_auto_save_mode_success": "Mode d'enregistrement automatique mis à jour avec succès.", "res_switch_browse_mode_success": "Modes changés avec succès.", "res_v3_account_update_success": "Le compte d'authentification a été mis à jour avec succès.", "res_host_update_success": "L'hôte SNMP a été mis à jour avec succès.", "res_upgrading_firmware_success": "Mise à jour du firmware réussie. L'appareil va maintenant redémarrer.", "res_event_notification_success": "Notifications d'événements mises à jour avec succès.", "res_save_to_startup_success": "La configuration en cours a été enregistrée avec succès dans la configuration de démarrage.", "clear_success": "<PERSON>ff<PERSON><PERSON> avec succès.", "res_factory_default_success": "Réinitialisation réussie aux paramètres d'usine par défaut.", "backup_success": "Sauvegarde ré<PERSON>.", "res_custom_default_success": "Réinitialisation réussie des paramètres par défaut personnalisés.", "download_success": "Téléchargement réussi.", "locator": "Le localisateur de périphérique a été déclenché avec succès.", "re_auth_port_success": "Le port a été réauthentifié avec succès.", "res_recovery_port_success": "Le port a été récupéré avec succès."}, "error_handler": {"error_session_expired_dialog": "Cette session est expirée. Le système reviendra à la page de connexion."}, "validators": {"required": "Requis", "require_min_length": "Min. {{ number }} caractères", "require_range_between": "{{ rangeBegin }} - {{ rangeEnd }}", "require_range_single_between": "{{ singleNum }}, {{ rangeBegin }} - {{ rangeEnd }}", "require_hexadecimal": "<PERSON><PERSON><PERSON> hexa<PERSON> uniquement", "require_unicast": "IP de monodiffusion uniquement", "required_at_least_two": "Au moins 2 ports de sortie", "required_at_least_one_overwrite": "Au moins un élément de remplacement", "required_priority_multiple": "La priorité doit être un multiple de {{ num }}", "required_label_max_length": "Le nombre maximum de caractères pour une étiquette est {{ number }}", "required_interval_multiple": "L'intervalle de délai d'attente doit être un multiple de {{ num }}", "required_timeout_multiple": "Cette combinaison d'adresse de groupe et de masque de réseau de groupe existe déjà", "invalid": "Invalide", "invalid_format": "Format invalide", "invalid_positive_integer": "<PERSON>tier positif invalide", "invalid_hex": "Numéro hexadécimal invalide", "invalid_range": "La plage valide est de {{ rangeBegin }} pour {{ rangeEnd }}", "invalid_single_range": "La portée est {{ singleNum }} ou {{ rangeBegin }} pour {{ rangeEnd }}", "invalid_mac_address": "Adresse MAC invalide", "invalid_ip_address": "Adresse IP invalide", "invalid_area_id": "Identifiant de zone invalide", "invalid_router_id": "ID de routeur invalide", "invalid_vlan_port": "Port de membre VLAN invalide", "invalid_vlan_output_port": "VLAN invalide pour le port de sortie", "invalid_netmask": "Masque de rés<PERSON> invalide", "invalid_char": "Se<PERSON>s a-z, A-<PERSON>, 0-9 sont autorisés", "invalid_email": "<PERSON><PERSON> invalide", "invalid_regex_level_1": "Seulement a-z, A-Z, 0-9 ou . - _ sont autorisés", "invalid_regex_level_2": "Seulement a-z, A-Z, 0-9 ou . , - _ + = | : ; @ ! ~ # % ^ * ( ) [ ] { } sont autorisés", "invalid_sys_desc": "Uniquement a-z, A-Z, 0-9 ou ~ ! @ # $ % ^ & * ( ) { } [ ] < > _ + - = \\ : ; , . / sont autorisé", "invalid_login_failure_message": "Se<PERSON>s les caractères a-z, A-Z, 0-9 ou ! # $ % & ' ( ) * + , \\ - . / : ; < = > @ [ ] ^ _ ` { | } ~ sont autorisés", "invalid_regex_macsec_cak_and_ckn": "Seulement a-z, A-Z, 0-9 ou @ % $ ^ * ' ` ( ) _ + = { } : . , ~ [ ] - sont autorisés.", "invalid_char_and_dash": "<PERSON><PERSON><PERSON> a-z, <PERSON>-<PERSON>, 0-9 ou - sont autorisés", "invalid_char_and_dot_dash": "Seulement a-z, A-<PERSON>, 0-9 ou . - sont autorisés", "invalid_lowercase_and_dash_dot": "<PERSON><PERSON><PERSON> az, 0-9 ou . - sont autorisés", "invalid_file_name": "Seulement a-z, A-Z, 0-9 ou / ( ) . - _ sont autorisés", "duplicate_ip": "IP en double", "duplicate_ip_range": "Plage IP en double", "duplicate_vrid": "VRID en double", "duplicate_stream": "Ce flux existe déjà", "duplicate_id": "Identifiant en double", "duplicate_input_ports": "Ports d'entrée en double", "duplicate_loopback_id": "Cet ID de bouclage est déjà utilisé", "duplicate_vlan_id": "Cet ID de VLAN est déjà utilisé.", "duplicate_vlan_and_mac": "Cette combinaison d'ID de VLAN et d'adresse MAC existe déjà", "duplicate_group_address_and_netmask": "Cette combinaison d'adresse de groupe et de masque de réseau de groupe existe déjà", "two_deciaml_palce": "Maximum 2 décimales", "duplicate_ip_and_netmask": "Cette combinaison d'adresse IP et de masque de sous-réseau existe déjà", "three_deciaml_palce": "Maximum 3 décimales", "same_as_ingress_stream": "Same as the ingress stream"}}