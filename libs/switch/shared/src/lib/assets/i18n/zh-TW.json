{"__readme__": {"__1__": "All key naming is snake_case, in addition to using uri key, e.g.'ospfRRDStatic'", "__2__": "In 'common', words are used repeatedly. It can be reclassified and named 'common_xxx', e.g.'common_port', 'common_account'...", "__3__": "In 'dialog', words are used repeatedly in the dialog.", "__4__": "In 'button', put all button words in and must be capitalized"}, "login": {"title_login_records": "登入記錄", "redirected_message": "登出成功", "login_success_records_greeting": "歡迎 {{ name }}", "login_success_records_datetime": "上次成功登入時間是 {{ time }}.", "login_fail_records": "最後一次登入失敗記錄", "modify_password_notification": "請更改預設用戶名和密碼以增強安全性。", "password_expire_notification": "您的密碼已過期。請更改您的密碼。", "daylight_saving_upgrade": "夏令時間功能已更新。", "daylight_saving_notification": "請更新您的設定。", "factory_default_note": "請注意，您需要使用預設網絡設置來重新建立與交換機的網頁連線。", "firmware_upgrade_note": "請注意，您需要重新建立與交換機的網頁連接。"}, "general": {"top_nav": {"config_change": {"start_up_save_tip": "設定尚未保存到啟動設定。", "confirmation_message": "您確定要將運行設定應用到啟動設定嗎？", "title_apply_to_start_config": "應用於啟動設定"}, "user_profile": {"greeting": "你好，{{ username }}", "enable_auto_save": "啟用自動保存", "disable_auto_save": "關閉自動保存", "disable_auto_save_hint": "應用後，設定將保存到運行設定而不是啟動設定。", "change_language": "變更語言", "change_mode": "改變模式", "locator": "定位器", "reset_factory_default": "重置為預設設置", "save_custom_default": "保存自定義預設值", "standard_mode": "標準", "advanced_mode": "進階的", "standard_mode_tooltip": "標準模式：一些功能/參數將被隱藏以使其更簡單。", "advanced_mode_tooltip": "進階模式：想要調整這些設置的用戶將可以使用進階功能/參數。"}, "auto_save_mode": {"enable_auto_save_title": "啟用自動保存模式", "enable_auto_save_msg": "您確定要啟用自動保存模式嗎？", "disable_auto_save_title": "關閉自動保存模式", "disable_auto_save_msg": "您確定要關閉自動保存模式嗎？"}, "advanced_browse_mode": {"advanced_notification_title": "更改為進階模式", "advanced_notification_msg": "您確定要從標準模式更改為進階模式嗎？", "basic_notification_title": "更改為標準模式", "basic_notification_msg": "您確定要從進階模式更改為標準模式嗎？"}, "locator": {"title_switch_locator": "交換機定位器", "duration": "期間", "hint": "觸發設備上的 LED 開始閃爍，以便更容易定位。"}, "restart_machine": {"confirmation_title": "重啟", "confirmation_msg": "您確定要重新啟動設備嗎？"}, "factory_default": {"confirmation_title": "原廠預設值", "confirmation_msg": "您確定要將系統設定重置為原廠預設設置嗎？", "factory_default_hint": "重置為出廠默認設置將清除自定義默認設定。", "custom_default": "自定義預設值", "confirmation_msg_custom_default": "您確定要將系統設定重置為自定義預設值嗎？", "custom_default_not_exist": "當自定義預設值的狀態為 \"未找到設定！\"時,無法執行自定義預設值。", "saved_config_name": "保存的設定名稱", "config_name_hint": "自定義默認設定名稱保存在非易失性記憶體中。", "clear_all_config": "Delete configurations, log files, and credential keys"}, "save_custom_default": {"confirmation_msg": "您確定要將啟動設定保存為自定義預設值嗎？", "current_config_name": "當前設定名稱", "config_name_hint": "可以在\"Config Backup and Restore\"頁面上修改設定名稱。"}, "logout": {"confirmation_title": "登出", "confirmation_msg": "您確定要退出嗎？"}}, "page_state": {"page_not_found": "找不到網頁", "page_not_found_desc": "在此伺服器上找不到所請求的 URL。", "back_link": "返回索引頁"}, "menu_tree": {"jump_page_placeholder": "搜索功能", "system": "系統", "system_management": "系統管理", "account_management": "帳戶管理", "provisioning": "正在設定", "port_interface": "連接埠介面", "l2_switching": "二層交換", "unicast_route": "單播路由", "multicast_route": "組播路由", "mac": "MAC", "qos": "QoS", "redundancy": "備援", "l2_redundancy": "二層備援", "l3_redundancy": "三層備援", "network_service": "網路服務", "routing": "路由", "network_management": "網路管理", "device_security": "設備安全", "network_security": "網路安全", "diagnostics": "診斷", "network_status": "網路狀態", "tools": "工具", "log_and_event_notification": "事件日誌和通知", "application": "工業應用", "iec61850": "IEC 61850", "iec62439_3": "IEC 62439-3"}, "dialog": {"title_refresh_browser": "刷新瀏覽器", "title_change_default_password": "更改預設密碼", "title_change_password": "更改密碼", "title_session_expired": "連線已過期", "title_notification": "通知", "title_edit_interface": "Edit Interface {{ interfaceName }}", "edit_port_msg": "編輯連接埠 {{ portIndex }} 設置", "edit_vlan_msg": "編輯VLAN {{ vlanIndex }} 設置", "create_entry_msg": "創建項目", "edit_entry_msg": "編輯項目設置", "delete_entry_msg": "刪除項目", "title_delete_key": "刪除證書和密鑰", "title_delete_account": "刪除帳戶", "delete_entry_confirm_desc": "您確定要刪除所選項目嗎？", "title_select_file": "選擇文件", "title_device_unplugged": "{{ device }} 已拔掉插頭", "desc_device_unplugged": "請檢查 {{ device }} 是否已插入。", "redirect_to_ip": "重定向至 {{ ipAddress }}", "page_redirect_content": "頁面將在5秒後重定向。", "redirect_failed_hint": "如果重定向失敗，請檢查您的網路設置。", "after_seconds": "{{ second }} 秒後", "redirecting": "正在重定向……", "title_choose_tracking_id": "選擇Tracking ID"}, "common": {"network": "網路", "enable_header": "開啟", "enabled": "Enabled", "disable_header": "關閉", "disabled": "Disabled", "none": "None", "authentication": "驗證", "active": "Active", "inactive": "非活動", "passive": "Passive", "ip": "IP", "ip_address": "IP位址", "mac": "MAC", "mac_address": "MAC位址", "server_address": "伺服器的IP位址", "subnet_mask": "子網路遮罩", "domain_name": "域名", "ip_or_domain": "IP位址/域名", "general": "基本", "normal": "正常", "type": "類型", "mode": "模式", "yes": "是", "no": "否", "auto": "Auto", "user_defined": "User Defined", "valid": "有效的", "invalid": "無效的", "required": "必需的", "version": "版本", "unknown": "未知", "read_only": "Read Only", "refresh": "刷新", "reset": "重置", "add": "添加", "delete": "刪除", "export": "匯出", "up": "向上", "down": "向下", "index": "編號", "name": "名稱", "method": "方法", "file_name": "文件名", "file_server": "文件伺服器", "select_file": "選擇文件", "action": "行動", "authority": "權限", "any": "Any", "all": "全部", "unselect": "Unselect", "settings": "設置", "status": "狀態", "local": "Local", "usb": "USB", "usb_hint": "USB連接埠與ABC-02自動備份設定器兼容。", "usb_hw_disabled_hint": "由於外部存儲功能被關閉，USB無效。", "micro_sd": "microSD", "micro_sd_hw_disabled_hint": "由於外部存儲功能被關閉，microSD 無效。", "location": "地點", "time": "時間", "start_date": "開始日期", "start_time": "開始時間", "end_time": "時間結束", "timeout": "逾時", "interface": "介面", "threshold": "臨界點", "broadcast": "廣播", "multicast": "組播", "algorithm": "演算法", "manual": "Manual", "master": "Master", "priority": "優先級", "permanent": "永久的", "queue": "隊列", "netmask": "遮罩", "backup": "備份", "backup_en": "Backup", "restroe": "還原", "broken": "斷線", "learning": "學習", "listening": "傾聽", "discarding": "丟棄", "forwarding": "傳送", "blocking": "封鎖", "packets": "封包", "notice": "Notice", "warning": "Warning", "critical": "Critical", "error": "Error", "security": "資訊安全", "slave": "從", "slot": "槽", "simple": "Simple", "state": "狀態", "subtype": "子類型", "protocol": "協議", "init": "發起", "retry": "重試", "severity": "嚴重性", "destination": "目標", "description": "描述", "distance": "距離", "expired_date": "截止日期", "delay_time": "延遲時間", "serial_number": "序列號碼", "product_revision": "硬體版本", "quick_setting": "快速設置", "admin_status": "管理員狀態", "link_status": "連線狀態", "system_status": "系統狀況", "link_up": "連線", "link_down": "連線中斷", "point_to_point": "Point-to-point", "ethertype": "以太類型", "auth_type": "驗證類型", "authentication_type": "身份驗證類型", "default_gateway": "預設閘道", "encryprion_key": "加密密鑰", "encryption_method": "加密方法", "authentication_password": "驗證密碼", "server_ip_address": "伺服器的IP位址", "key": "密鑰", "key_id": "密鑰ID", "vlan_id": "VLAN ID", "vlan_vid": "VLAN {{ vid }}", "vlan_list_info": "可以設置多個 VLAN，並且應輸入單個數字或範圍，例如 2、4-8、10-13。", "share_key": "共享密鑰", "share_key_hint": "離開本頁面或刷新後，共享密鑰將自動清除，以增強安全性。", "auto_backup": "自動備份", "auto_backup_hint": "設定更改時備份到外部儲存裝置。", "dynamic": "動態", "static": "靜態", "ref_manual_hint": "有關此設置的資訊，請參閱使用者手冊。", "default": "<PERSON><PERSON><PERSON>", "other": "Other", "trusted": "Trusted", "untrusted": "Untrusted", "infinite": "無窮", "source": "來源", "low": "低的", "high": "高的", "connected": "連接的", "disconnected": "斷開連接", "incorrect_connected": "連接不正確", "set_event_notifications": "設置事件通知", "include": "包括", "exclude": "排除", "immediate": "Immediate", "never": "Never", "interface_name": "介面名稱", "interface_alias": "介面別名", "hello_interval": "問候週期", "neighbor": "鄰居", "current": "當前", "ping": "<PERSON>", "logical": "邏輯", "interval": "週期", "route": "路由", "open": "Open", "short": "Short", "role": "角色", "speed": "速度", "failed": "失敗", "successful": "成功", "idle": "閑置", "protection": "保護", "pending": "待處理", "supported": "支援", "not_supported": "不支援", "import": "導入", "propagate": "傳播", "ascii": "ASCII", "hex": "HEX", "zone": "區域"}, "common_account": {"account": "帳戶", "username": "用戶名", "pwd_mask": "********", "password": "密碼", "confirm_password": "確認密碼", "email": "Email", "delete_account_desc": "您確定要刪除所選帳戶嗎？"}, "common_abbrev": {"md5": "MD5", "sha": "SHA", "sha-224": "SHA-224", "sha-256": "SHA-256", "sha-384": "SHA-384", "sha-512": "SHA-512", "sftp": "SFTP", "tftp": "TFTP", "pap": "PAP", "chap": "CHAP", "cos": "CoS", "dscp": "DSCP", "cir": "CIR", "cir_full": "承諾訊息速率", "cbs": "CBS", "cbs_full": "承諾突發大小"}, "common_port": {"port": "連接埠", "ports": "連接埠", "port_name": "連接埠 {{ portName }}", "tcp_port": "TCP 連接埠", "udp_port": "UDP連接埠", "port_state": "連接埠狀態", "port_status": "連接埠狀態", "port_settings": "連接埠設置", "port_shutdown": "Port Shutdown", "destination_port": "目標連接埠", "reflect_port": "反映連接埠", "all_port": "All Ports", "member_port": "會員連接埠", "also_apply_port": "將此設定複製到其他連接埠", "also_apply_port_hint": "將設定複製到您從下拉框中選擇的連接埠。", "ingress_port": "入流連接埠"}, "button": {"cancel": "取消", "apply": "送出", "create": "建立", "edit": "編輯", "delete": "刪除", "sync_from_browser": "從瀏覽器同步", "regenerate": "重建", "import": "匯入", "export": "匯出", "expand": "展開", "collapse": "收合", "copy": "複製", "close": "關閉", "download": "下載", "ping": "PING", "clear": "清除", "backup": "備份", "restore": "恢復", "upgrade": "升級", "reset": "重置", "locate": "定位", "disable": "關閉", "enable": "開啟", "change": "改變", "logout": "登出", "reboot": "重啟", "confirm": "確認", "remove": "消除", "back": "返回", "log_in": "登入", "re_auth": "重新授權", "recovery": "恢復", "export_cid_file": "匯出CID文件", "export_server_ca": "匯出伺服器 CA", "change_password": "更改密碼", "export_server_cert": "匯出伺服器證書", "view_all_event_logs": "查看所有事件日誌", "update_daylight_saving": "更新夏令時間", "select": "選擇", "retry": "重試", "encrypt": "加密", "change_cak": "更改 CKN 和 CAK", "save": "保存", "find_rp": "查找 RP", "cut_off": "切斷", "go_to": "轉到", "got_it": "了解"}, "tooltip": {"edit": "編輯", "delete": "刪除", "copy": "複製", "copy_config": "複製設定", "close": "關閉", "clear": "清除", "clear_graph": "清晰的圖表", "remove": "消除", "reorder": "重新排序", "recovery": "恢復", "select_tracking": "選擇 Tracking ID", "remove_tracking": "刪除 Tracking ID", "vlan_size_limitation": "該設備只允許 {{ size }} VLAN。", "size_limitation": "該設備的最大用戶帳戶數為 {{ size }}.", "reorder_priority": "重新排序優先級", "reorder_finish": "重新排序完成", "export_pdf_all_acl": "將所有 ACL 匯出為 PDF", "export_pdf_only_this_port": "僅匯出該連接埠的資訊為PDF", "export_pdf_only_this_vlan": "僅匯出該VLAN的資訊為PDF", "clear_counter": "清除計數器", "unicast_mac_address_hint": "僅允許單播 MAC 位址。", "disable_la_member_hint": "這是鏈路聚合連接埠通道的成員連接埠。", "duplicate_redundant_port": "無法複製冗餘連接埠", "auto_refresh_enabled": "自動刷新：  已啟用", "auto_refresh_disabled": "自動刷新：  已關閉", "manually_refersh_disabled": "如果啟用‘自動刷新’，則無法手動刷新", "integration_3A_3B_hint": "此埠是 3/A 和 3/B 的集成。", "set_to_static": "Set to static"}, "unit": {"fps": "fps", "min": "min.", "sec": "sec.", "ms": "ms", "byte": "byte", "m": "M", "kbyte": "Kbyte", "mbps": "Mbps", "w": "W", "watt": "瓦", "watts": "瓦特", "percent": "%", "day": "天", "times": "次", "mb": "MB", "ns": "ns", "microsecond": "μs", "ppm": "PPM", "packets": "封包", "v": "V", "dbm": "dBm", "c": "°C", "f": "°F", "nm": "nm"}, "speed": {"10m": "10M", "100m": "100M", "1g": "1G", "10g": "10G", "40g": "40G", "56g": "56G", "2500m": "2500M", "full": "全", "half": "半"}, "table_function": {"filter_desc": "搜索", "export_pdf": "匯出 PDF", "export_csv": "匯出 CSV", "selected_count": "選定", "row_count": "全部的", "limit_count": "最大"}, "led": {"status_unknown_hint": "狀態未知。", "state_green_on": "正常運作。", "state_green_blink4hz": "系統正在啟動。", "state_red_on": "系統初始化失敗。", "state_off": "系統已關閉。", "fault_red_on": "系統出現故障，請檢查系統日誌以了解詳細訊息。", "fault_red_blink4Hz": "交換機已啟動，韌體已加載到記憶體中。", "fault_off": "正常運作。", "mh_turbo_chain_head_green_on": "此交換機是Turbo Chain的Head。", "mh_turbo_ring_green_on": "此交換機是Turbo Ring 1或Turbo Ring 2的Master。", "mh_mrp_green_on": "此交換機是MRP冗餘管理者。", "mh_turbo_ring_green_blink2hz_mds": "此交換機是Turbo Ring 1或Turbo Ring 2的Master並且至少有一個環網損壞。", "mh_turbo_chain_head_green_blink2hz_mds": "此交換機是Turbo Chain的Head，且這條鏈路斷了。", "mh_mrp_green_blink2hz_mds": "此交換機是MRP冗餘管理者並且環是開放的。", "mh_turbo_chain_member_green_blink2hz_mds": "此交換機是Turbo Chain成員，對應的成員連接埠1鏈路斷開。", "mh_turbo_chain_tail_green_blink2hz_mds": "這台交換機是Turbo Chain的Tail且對應成員連接埠鏈路斷開。", "mh_turbo_ring_green_blink4hz": "此交換機是Turbo Ring 1或Turbo Ring 2的Master並且至少有一個環網損壞。", "mh_turbo_chain_head_green_blink4hz": "此交換機是Turbo Chain的Head，且這條鏈路斷了。", "mh_mrp_green_blink4hz": "此交換機是MRP冗餘管理者並且環是開放的。", "mh_turbo_chain_member_green_blink4hz": "此交換機是Turbo Chain的成員，對應的成員連接埠1鏈路斷開。", "mh_turbo_chain_tail_green_blink4hz": "此交換機是Turbo Chai的Tail，且對應成員連接埠鏈路斷開。", "mh_off": "此交換機不是 Turbo Ring 1 或 Turbo Ring 2 的 Master、Turbo Chain 的 Head 或 MRP冗餘管理者。", "ct_turbo_ring_green_on": "這台交換機開啟coupling功能，形成備用路徑。", "ct_mrp_green_on": "這台交換機開啟coupling功能，形成備用路徑。", "ct_turbo_chain_tail_green_on": "此交換機是Turbo Chain的Tail。", "ct_dual_homing_green_on": "交換機的 dual homing 功能已啟用。", "ct_turbo_ring_dual_homing_green_on": "交換機的 coupling 和 dual homing 功能已啟用。", "ct_turbo_chain_dual_homing_green_on": "交換機的 dual homing 功能已啟用，交換機是 Turbo Chain 的尾部。", "ct_mrp_dual_homing_green_on": "交換機的 MRP connection 和 dual homing 功能已啟用。", "ct_turbo_chain_head_green_blink2hz_mds": "此交換機是Turbo Chain的Head，對應的Member連接埠鏈路斷開。", "ct_turbo_chain_member_green_blink2hz_mds": "此交換機是Turbo Chain的成員，對應的成員連接埠2鏈路斷開。", "ct_turbo_chain_tail_green_blink2hz_mds": "此交換機是Turbo Chain的Tail，且這條鏈路斷了。", "ct_turbo_chain_head_green_blink4hz": "此交換機是Turbo Chain的Head，且對應的Member連接埠鏈路斷開。", "ct_turbo_chain_member_green_blink4hz": "此交換機是Turbo Chain的成員，對應的成員連接埠2鏈路斷開。", "ct_turbo_chain_tail_green_blink4hz": "這台交換機是Turbo Chain的Tail，且鏈路断了。", "ct_off": "此交換機已關閉 Turbo Chain 的Coupling 或者是Turbo Chain 的Tail。", "sync_amber_on": "PTP功能已啟用。", "sync_amber_blink4hz": "此交換機已收到同步封包。", "sync_green_on": "PTP功能已成功收斂。", "sync_off": "PTP功能已關閉。", "ms_green_on": "正常運作", "ms_green_blink2hz": "該模組正在啟動。", "ms_off": "模組已停止服務。", "ms_red_on": "模組初始化失敗，或者用戶插入了錯誤的模組。", "eps_amber_on": "外部電源已準備好向PoE連接埠供電。", "eps_off": "PoE設備沒有外部電源。", "pwr_eps_amber_on": "外部電源正在向模組的 EPS 輸入供電。", "pwr_eps_off": "PoE設備沒有外部電源。", "pwr_amber_on": "正在向模組的電源輸入供電。", "pwr_off": "模組的電源輸入未供電。", "port_poe_green_on": "該連接埠已連接到 IEEE 802.3at 受電設備 (PD)。", "port_poe_green_on_poebt": "該連接埠已連接到 IEEE 802.3bt 受電設備 (PD)。", "port_poe_amber_on": "該連接埠已連接到 IEEE 802.3af 受電設備 (PD)。", "port_poe_amber_on_poebt": "該連接埠已連接到 IEEE 802.3af/at 受電設備 (PD)。", "port_poe_amber_blink4hz": "由於功率預算不足，PoE 電源已被關閉。", "port_poe_red_on": "用電設備 (PD) 檢測失敗。", "port_poe_red_blink4hz": "在受電設備 (PD) 上檢測到過電流或短路。", "port_poe_off": "未向受電設備 (PD) 供電。", "port_link_up_hint": "該連接埠處於活動狀態並且正在連線 {{ operSpeed }}基點。", "port_link_down_hint": "連接埠處於非活動狀態或鏈路已關閉。", "prp_green_on": "PRP 功能已啟用。", "prp_off": "PRP 功能已禁用。", "hsr_green_on": "HSR 功能已啟用。", "hsr_off": "HSR 功能已啟用。", "coup_green_on": "Coupling 功能已啟用。", "coup_off": "Coupling 功能已禁用。"}}, "features": {"storm_control": {"page_title": "網路風暴控制", "dlf": "DLF"}, "la": {"page_title": "鏈路聚合", "port_channel": "連接埠通道（Trunk）", "wait_time": "等待時間", "configure_member": "設定成員", "active_member": "運作中成員", "la_group_status": "鏈路聚合組狀態", "lacp": "LACP", "smac": "SMAC", "dmac": "DMAC", "smac_dmac": "SMAC+DMAC", "config_member_port": "設定成員連接埠", "config_member_port_hint": "至少保留一個不能添加到連接埠通道的連接埠。", "delete_port_channel_confirm_desc_1": "警告：", "delete_port_channel_confirm_desc_2": "與所選鏈路聚合相關的某些功能（例如 RSTP 和 VLAN）將設置為預設值。", "delete_port_channel_confirm_desc_3": "您確定要刪除所選的鏈路聚合嗎？", "la_size_limitation": "該設備只允許 {{ size }} 組連接埠通道。", "create_la_msg": "創建鏈路聚合", "edit_la_pre_msg": "編輯連接埠通道 {{ portChannel }} 設置", "delete_la_msg": "刪除鏈路聚合", "only_select_eight": "最多可以選擇八個。"}, "scheduler": {"page_title": "調度員", "strict_priority": "Strict Priority", "weight_round_robin": "Weighted Round Robin", "sp": "SP", "wrr": "WRR", "wfq": "WFQ"}, "egress_shaper": {"page_title": "出流整形器", "egress_rate": "出口率 (CIR)"}, "rate_limit": {"page_title": "入流速率限制", "ingress_rate": "進入率（CIR）", "ebs": "EBS", "ebs_full": "超額突發大小", "conform_action": "符合行動", "exceed_action": "超越行動", "violate_action": "違規行為", "blind": "色盲", "aware": "色彩感知", "do_nothing": "Do Nothing", "drop": "Drop", "remark_cos": "Remark CoS", "remark_dscp": "Remark DSCP", "simple_token_bucket": "Simple Token Bucket", "sr_tcm": "SrTCM", "remark_value": "備註值", "release_interval": "釋放週期", "rate_limit_port_shutdown": "連接埠關閉速率限制"}, "classification": {"page_title": "分類", "cos_priority": "CoS 優先級", "preference_type": "信任類型", "dhcp_mapping": "DSCP 映射", "cos_mapping": "CoS 映射", "untag_default_priority": "取消標記預設優先級", "edit_dscp_msg": "編輯 DSCP {{ dscpIndex }} 設置", "edit_cos_msg": "編輯 CoS {{ cosIndex }} 設置"}, "linkup_delay": {"page_title": "連線延遲", "remaining_time": "剩餘時間"}, "port_mirror": {"page_title": "連接埠鏡像", "span": "SPAN", "rspan": "RSPAN", "session_id": "會話ID", "reflect_port_mode": "反映連接埠模式", "rspan_type": "RSPAN 類型", "rspan_vid": "RSPAN VLAN ID", "rspan_settings": "RSPAN 中間設置", "rspan_setting_hint": "所有 VLAN Trunk連接埠都將添加到 RSPAN VLAN 中。", "rspan_vlan_setting_hint": "為防止標記的封包被丟棄，請僅選擇設定為 Trunk 或 Hybrid 模式的 VLAN 埠作為反映埠。", "duplicate_intermediate_vlan": "這個Intermediate VLAN ID 已被使用。", "rspan_role": "RSPAN 中間角色", "rspan_intermediate_vid1": "第一個 RSPAN Intermediate VLAN ID", "rspan_intermediate_vid2": "第二個 RSPAN Intermediate VLAN ID", "enable_rspan_title": "啟用 RSPAN 中間角色", "enable_rspan_warning": "此設置將刪除所有現有的 RSPAN 會話。您確定要繼續嗎？", "rx_source_port": "接收源連接埠", "tx_source_port": "發送源連接埠", "designated_port": "指定連接埠", "destination_ports": "目標連接埠", "destination_port_info": "Access port的PVID將會被改為RSPAN VLAN ID。 \nHybrid 或Trunk ports則會被加入RSPAN VLAN的成員。", "destination_port_hint": "目標連接埠將被加入 RSPAN VLAN。", "destination_ports_or_designated_port": "目標連接埠或指定連接埠", "source_port_two_field_invalid": "需要選擇 Tx 或 Rx 源連接埠", "create_mirror_msg": "創建會話", "edit_mirror_msg": "編輯會話 {{ sessionIndex }} 設置", "select_tx_or_rx_hint": "需要選擇 TX 或 RX 源連接埠。", "is_not_access_port": "此連接埠不是Access連接埠。", "is_not_trunk_port": "此連接埠不是Trunk連接埠。", "source_port_must_be_access_port": "啟用反射埠模式時,源埠必須是訪問埠。", "reflect_port_must_be_access_port": "啟用反射埠模式時,反射埠必須是訪問埠。", "reflect_port_must_be_trunk_hybrid_port": "啟用反射埠模式時,反射埠必須是中繼/混合埠。", "pvid_is_not_rspan_vid": "該連接埠的PVID不是RSPAN VLAN。", "rspan_source_session_exist": "RSPAN 來源會話已存在。", "rspan_destination_session_exist": "RSPAN 目標會話已存在。", "rspan_cannot_create": "啟用 RSPAN 中間角色後，無法創建 RSPAN 會話。", "session_span_size_limitation": "SPAN 項目的最大數量為 {{ size }}.", "session_rspan_size_limitation": "RSPAN 項目的最大數量為 {{ size }}.", "delete_session_title": "刪除會話", "delete_session_content": "您確定要刪除所選會話嗎？", "rspan_vid_hint_l2": "不建議對 RSPAN 使用管理 VLAN 或 VLAN 分配設定的 VLAN。", "rspan_vid_hint_l3": "不建議將 VLAN 介面或 VLAN 分配設定的 VLAN 用於 RSPAN。"}, "vlan": {"page_title": "VLAN", "vlan": "VLAN", "global": "全域", "management_vlan": "管理VLAN", "management_port": "管理連接埠", "mgmt_vlan_settings": "管理連接埠快速設置", "management_vlan_port_setting_hint": "請選擇您的計算機連接的連接埠並確保設置正確，以免與交換機斷開連接。", "port_mode_table_title": "VLAN 交換機連接埠模式表", "egress_tagged_table_title": "VLAN成員表", "gvrp": "GVRP", "vlan_unaware": "VLAN Unaware", "vlan_unaware_gvrp_error": "GVRP cannot be enabled while VLAN Unaware is active.", "vlan_unaware_active_disable_hint": "VLAN cannot be modified while VLAN Unaware is active.", "all_member_vlan": "所有成員 VLAN ID", "dynamic_gvrp": "動態 GVRP", "egress_port": "出口連接埠", "tagged_port": "標記連接埠", "untagged_port": "未標記的連接埠", "forbidden_port": "禁用連接埠", "vid_exist_warning": "VLAN已存在", "vlan_max_warning": "每次最多 10 個 VLAN", "vlan_max_hint": "最多 10 個 VLAN", "pvid": "PVID", "tagged_vlan": "標記的 VLAN", "untagged_vlan": "未標記的 VLAN", "access": "Access", "access_port": "Access連接埠", "trunk": "Trunk", "trunk_port": "Trunk連接埠", "hybrid": "Hybrid", "hybrid_port": "Hybrid連接埠", "vlan_assignment": "VLAN Assignment", "delete_vlan_confirm_desc": "您確定要刪除所選的 VLAN 嗎？", "mgmt_setting_disabled_pvid": "PVID已綁定到該VLAN，因此無法刪除。", "mgmt_setting_disabled_access_mode": "如果連接埠使用的是Access模式，則不能切換到該VLAN。", "mgmt_setting_disabled_forbidden": "此連接埠為禁止連接埠。", "mgmt_setting_disabled_egress": "該連接埠為成員連接埠。", "port_setting_disabled_tagged": "此 VLAN 是標記 VLAN。", "port_setting_disabled_untagged": "此 VLAN 是未標記的 VLAN。", "port_setting_disabled_forbidden": "此連接埠是該 VLAN 的禁止連接埠。", "port_setting_disabled_pvid_member": "該PVID無法綁定到該VLAN，因為該連接埠不是成員連接埠。", "port_setting_disabled_pvid_forbidden": "該PVID無法綁定到該VLAN，因為該連接埠是禁止連接埠。", "port_setting_error_pvid_member": "未標記或未標記 VLAN", "port_setting_error_pvid_forbidden": "這是禁止連接埠，無法應用設置", "vlan_setting_vid_info": "可以創建多個 VLAN，並且應輸入單個數字或範圍，例如 2、4-8、10-13。", "te_mstid": "TE-MSTID", "temstid_member": "TE-MSTID 會員", "temstid_info": "對於VLAN加入TE-MSTID成員，流將通過靜態傳送規則轉發，而不是MAC學習/傳送機制。", "create_vlan_msg": "創建VLAN", "delete_vlan_msg": "刪除VLAN", "vlan_setting_info_title": "如何設置", "example_scenario": "示例場景", "example_scenario_info_1": "連接埠1： Hybrid模式、PVID 1、TAG VLAN 3-5 和 UNTAG VLAN 1", "example_scenario_info_2": "連接埠2： Trunk模式、PVID 2 和 TAG VLAN 2-5", "example_scenario_info_3": "連接埠3： Access模式、PVID 1 和 UNTAG VLAN 1", "example_scenario_info_4": "交換機-A 設置：  管理 VLAN 1", "setup_flow": "設置流程", "vlan_port_mode_setup": "VLAN 連接埠模式設置", "port_number_setting": "連接埠 {{ portNumber }} 設置", "setup_list_hybrid": "- 模式選擇‘Hybrid’", "setup_list_apply": "- 申請", "setup_list_trunk": "- 模式選擇‘Trunk’", "setup_list_access": "- 預設模式是'Access'，無需更改任何內容。", "setup_list_pvid2": "-PVID 2", "vlan_create_member_setup": "VLAN創建/VLAN成員設置", "vlan_create_member_setup_info_part_1": "點擊", "vlan_create_member_setup_info_part_2": ",添加VLAN ID {{ vlanIndex }} ,添加成員連接埠 {{ portIndex }}", "vlan_port_pvid_setup": "VLAN連接埠PVID設置", "vlan_port_pvid_setup_info": "準備好了，不需要改變。"}, "l3_interface": {"page_title": "網路介面", "loopback_size_limitation": "該設備只允許 {{ size }} 環回。", "operStatus": "運行狀態", "loopback_id": "環回ID", "vlan_interface": "VLAN 介面", "loopback_interface": "環回介面", "alias": "<PERSON><PERSON>", "mtu": "MTU", "proxy_arp": "代理ARP", "vlan_id_hint": "介面 VLAN ID 應與二層 VLAN 相同，三層路由才能工作。", "delete_ip_interface_desc": "您確定要刪除此項目嗎？", "vlan_card_title": "VLAN 介面", "loopback_card_title": "環回介面", "delete_ip_interface": "刪除介面", "add_l3_vlan_ip_interface": "創建 VLAN 介面設置", "edit_l3_vlan_ip_interface": "編輯 VLAN 介面設置", "add_l3_loopback_ip_interface": "創建環回介面設置", "edit_l3_loopback_ip_interface": "編輯環回介面設置", "from_dcp": "（來自 PROFINET DCP）"}, "stp": {"page_title": "Spanning Tree", "stp_mode": "STP模式", "compatibility": "兼容性", "stp": "STP", "rstp": "RSTP", "mstp": "MSTP", "stp_rstp": "STP/RSTP", "bridge_priority": "橋樑優先", "error_recovery_time": "錯誤恢復時間", "forward_delay_time": "傳送延遲時間", "hello_time": "Hello時間", "max_age": "最大年齡", "edge": "邊緣", "guard": "防護", "path_cost": "路徑成本", "multiples_of_number": " {{ number }}的倍數", "path_cost_help_info": "如果該值設置為零，則將根據不同的連接埠速度自動分配路徑成本值。", "bpdu_guard": "BPDU 防護", "root_guard": "根防護", "loop_guard": "循環防護", "bpdu_filter": "BPDU 過濾器", "root_information": "根資訊", "bridge_id": "橋ID", "root_path_cost": "根路徑成本", "bridge_information": "橋資訊", "running_protocol": "運行協議", "port_role": "連接埠角色", "link_type": "連線類型", "shared": "Shared", "bpdu_inconsistency": "BPDU 不一致", "root_inconsistency": "根不一致", "loop_inconsistency": "循環不一致", "link_type_shared_lan": "Shared LAN", "alternate": "備用", "root": "根", "designated": "指定的", "instance": "實例", "instance_index": "實例 {{ instId }}", "all_instances": "所有實例", "instance_list": "實例列表", "instance_id": "實例ID", "mstp_size_limitation": "該設備的最大實例數是 {{ size }} 除了 CIST。", "vlan_list": "VLAN 列表", "port_table_of_cist": "CIST連接埠表", "port_table_of_instance": "實例連接埠表 {{ instId }}", "information_of_cist": "CIST資訊", "information_of_instance": "實例資訊 {{ instId }}", "region_name": "地區名稱", "region_revision": "區域修訂", "max_hops": "最大跳躍數", "instance_id_duplicate": "實例ID已創建。", "except_for_cist": "CIST 除外。", "copy_port_config": "複製連接埠設定​", "select_from_port_of_inst": "從連接埠 {{ instName }}", "select_to_inst": "到實例", "general_information": "一般資訊", "regional_root_id": "區域根ID", "cist_root_id": "CIST根ID", "cist_path_cost": "CIST路徑成本", "designated_root_id": "指定根ID", "other_vlans": "其他 VLAN", "create_instance": "創建實例", "edit_instance": "編輯實例 {{ instId }} 設置", "delete_instance": "刪除實例", "edit_cist": "編輯 CIST 設置", "edit_instance_port": "編輯實例 {{ instId }} 連接埠 {{ portIndex }} 設置", "edit_cist_port": "編輯CIST連接埠 {{ portIndex }} 設置"}, "port_security": {"page_title": "連接埠安全", "port_security_mode": "連接埠安全模式", "port_security_mode_help_info": "更改連接埠安全模式將重置所有設置。", "mac_sticky": "MAC Sticky", "static_port_lock": "Static Port Lock", "address_limit": "位址限制", "secure_action": "安全行動", "current_address": "目前的位址", "configured_address": "手動設定的位址", "violation": "違反", "effective": "有效的", "secure_pack_drop": "Packet Drop", "total_entry": "完全信任主機", "max_address": "系統中的最大位址數", "sticky_configured": "粘性設定", "lock_configured": "鎖定已設定", "sticky_dynamic": "粘性動態", "address_limit_hint": "如果位址限制值發生更改，則該連接埠上的所有 MAC 位址都將被刪除。"}, "garp": {"page_title": "GARP", "join_time": "加入時間", "leave_time": "離開時間", "leave_all_time": "離開時間", "required_join_time_multiple": "加入時間必須是 {{ num }}", "required_leave_time_multiple": "離開時間必須是 {{ num }}", "required_leave_all_time_multiple": "離開時間必須是 {{ num }}"}, "lldp": {"page_title": "LLDP", "neighbor_status": "鄰居狀態", "sidenav_header": "詳細資訊", "port_local_intf_status": "連接埠本地介面", "port_id_subtype": "連接埠 ID 子類型", "port_id": "連接埠號", "port_desc": "連接埠描述", "dot1_tlv_info": "擴展 802.1 TLV", "dot3_tlv_info": "擴展 802.3 TLV", "port_vlan_id": "連接埠VLAN ID", "vlan_name": "VLAN 名稱", "vlan_tx_status": "VLAN ID/名稱", "aggregated_and_status": "鏈路聚合狀態", "aggregated_port_id": "聚合連接埠 ID", "max_frame_size": "最大幀尺寸", "port_traffic_statistics": "連接埠交通統計", "total_frame_out": "總幀數", "total_entries_aged": "超時項目總數/超时条目总数", "total_frame_in": "總幀數", "total_frame_receviced_in_error": "錯誤接收的總幀數", "total_frame_discarded": "丟棄的總幀數", "total_tlvs_unrecognized": "無法識別的TLVS總數", "total_tlv_discarded": "丟棄的TLV總數", "management_address_table": "管理位址表", "management_address": "管理位址", "extended_eip_tlv": "擴展Ethernet/IP TLV", "vendor_id": "供應商ID", "device_type": "設備類型", "product_code": "產品代碼", "major_revision": "主要修訂", "minor_revision": "小修改", "interface_id": "介面ID", "lldp_version": "LLDP版本", "transmit_interval": "傳輸週期", "notification_interval": "通知週期", "reinit_delay": "重新初始化延遲", "holdtime_multiplier": "保持時間乘數", "chass_id_subtype": "<PERSON><PERSON><PERSON> ID 子類型", "tx_delay": "發送延遲", "subtype_chassis_component": "<PERSON><PERSON>s-Component", "subtype_if_alias": "<PERSON>-<PERSON><PERSON>", "subtype_port_component": "Port-Component", "subtype_mac_addr": "MAC-Address", "subtype_network_address": "Network-Address", "subtype_if_name": "If-Name", "subtype_unknown": "未知亞型", "chassis_id": "<PERSON><PERSON>s <PERSON>", "tlv": "TLV", "local_info": "本地資訊", "local_timer": "本地計時器", "remote_table_statistics": "遠端表格統計", "statistics_last_change_time": "最後更改時間（毫秒）", "statistics_insert": "插入", "statistics_drops": "丟棄", "statistics_ageout": "老化", "tx_status": "發送狀態", "rx_status": "接收狀態", "nbr_port_id": "鄰居連接埠ID", "nbr_chassis_id": "鄰居Chessis ID", "tx_only": "Tx Only", "rx_only": "Rx Only", "tx_and_rx": "Tx and Rx", "basic": "基本的", "basic_transmit_tlvs": "基本傳輸 TLV", "8021_transmit_tlvs": "802.1 傳輸 TLV", "8023_transmit_tlvs": "802.3 傳輸 TLV", "port_component_description": "連接埠組件描述。", "system_name": "系統名稱", "system_desc": "系統描述", "system_capability": "系統能力", "la_statistics": "鏈路聚合統計", "lldp_update_success": "成功更新 LLDP 全局設置。", "local_port": "本地連接埠", "sys_capability": "系統能力", "hold_time": "保持時間", "repeater": "中繼器", "bridge": "橋", "vlan_access_point": "VLAN Access點", "telephone": "電話", "docsis_cable_device": "Docsis 電纜設備", "station_only": "僅限設備"}, "mac_address_table": {"page_title": "MAC位址表", "independent_vlan_learning": "獨立VLAN學習", "mac_learning_mode": "MAC學習模式", "mac_learning_mode_help_info": "請注意，更改模式將重置相關的L2模組。", "aging_time": "老化時間", "learnt_unicast": "學習單播", "learnt_multicast": "學習組播"}, "port_setting": {"page_title": "連接埠設置", "admin": "管理員", "media_type": "媒體類型", "10m_half": "10M Half", "10m_full": "10M Full", "100m_half": "100M Half", "100m_full": "100M Full", "speed_duplex": "速度/雙工", "flow_control": "流量控制", "flow_control_hint1": "流量控制可以啟用/關閉，但僅在全雙工時有效。", "flow_control_hint2": "可以啟用/禁用背壓,但僅在半雙工時有效。", "mdi_mdix": "MDI/MDIX", "mdi": "MDI", "mdix": "MDIX", "enabled_xmit": "啟用傳輸", "enabled_rcv": "啟用接收", "fiber_speed_disable": "光纖連接埠無法設置速度/雙工。", "fiber_mdi_disable": "光纖連接埠無法設置MDI/MDIX。", "fiber_copy_to_other_port_disable": "光纖連接埠無法將設定複製到其他連接埠。", "port_copy_to_fiber_port_disable": "無法將設定複製到光纖連接埠。"}, "dashboard": {"page_title": "設備摘要", "system_info": "系統資訊", "panel_status": "面板狀態", "panel_view": "面板視圖", "link_up_port": "連線連接埠", "link_down_port": "未連線連接埠", "module": "模組 {{ index }} - {{ name }}", "product_model": "產品型號", "firmware_version": "韌體版本", "system_uptime": "系統正常運行時間", "ip_address_v4": "IPv4 位址", "ip_address_v6": "IPv6 位址", "l3_ip_address_list": "介面IP位址列表", "redundant_protocol": "冗餘協議", "power_model": "電力模型", "external_storage": "外置儲存", "iec62439_3_protocol": "IEC 62439-3 協議", "event_summary": "活動概要", "event_summary_hint": "（最近 3 天）", "top_5_interface_error_packet": "前 5 個介面錯誤封包", "top_5_interface_utilization": "使用率最高的 5 個介面", "critical_hint": "發生異常，系統未來存在運行異常的風險", "error_hint": "發生異常，但系統運行未受影響", "warning_hint": "該資訊包含警告/提醒，但不影響功能或系統操作", "notice_hint": "該資訊表明該功能工作正常，設備運行正常", "tx_error": "發送錯誤", "rx_error": "接收錯誤", "unsupported_module_warning": "注意：檢測到不支援的模組。卸下不支援的模組以保持正常運作。"}, "igmp_snooping": {"page_title": "IGMP Snooping", "vlan_setting": "VLAN設置", "group_table": "組表", "forwarding_table": "傳送表", "query_interval": "查詢週期", "static_router_port": "靜態路由器連接埠", "dynamic_router_port": "動態路由器連接埠", "config_role": "設定角色", "active_role": "現行角色", "startup_query_interval": "啟動查詢週期", "startup_query_count": "啟動查詢計數", "other_quer_present_interval": "其他查詢存在週期", "group_address": "群組位址", "filter_mode": "過濾模式", "source_address": "來源位址", "querier": "<PERSON><PERSON>", "non_querier": "Non-Querier"}, "turbo_ring_v2": {"page_title": "Turbo Ring V2", "ring_coupling_mode": "Ring Coupling模式", "static_ring_coupling": "靜態Ring Coupling", "dynamic_ring_coupling": "動態Ring Coupling", "ring_id": "環網ID", "master_id": "主環網ID", "ring_port": "環網連接埠", "coupling_mode": "Ring Coupling模式", "coupling_port": "Coupling連接埠", "primary_path": "主要路徑", "backup_path": "備份路徑", "ring_setting": "環網設置", "ring_coupling_setting": "Ring Coupling設置", "coupling_setting": "Coupling組 {{ id }} 設置", "static_ring_coupling_setting": "靜態Ring Coupling設置", "dynamic_ring_coupling_setting": "動態Ring Coupling設置", "coupling_group_id": "Coupling組 ID", "coupling_group_status": "Coupling組狀態", "group_id": "組 {{ id }}", "ring_status": "環網狀態", "ring_index": "Ring Index", "total_ring_number": "總環數", "healthy": "健康", "break": "斷線", "ring_coupling_status": "Ring Coupling模式狀態", "static_ring_coupling_status": "靜態Ring Coupling狀態", "dynamic_ring_coupling_status": "動態Ring Coupling狀態", "coupling_mode_primary": "Coupling Primary Path", "coupling_mode_backup": "Coupling Backup Path", "coupling_port_status": "Coupling埠狀態", "primary_mac": "Primary MAC", "primary_port": "主埠", "primary_port_status": "主埠狀態", "backup_mac": "備份 MAC", "backup_port": "備份埠", "backup_port_status": "備份埠狀態", "ring_setting_dialog_title": "{{ portIndex }} 設置", "dip_lock_hint": "Turbo Ring V2 由於 DIP 設定而被鎖定。"}, "8021x": {"page_title": "IEEE 802.1X", "auth_mode": "身份驗證模式", "local_database": "Local Database", "re_auth": "重新驗證", "port_control": "連接埠控制", "auth_session_type": "身份驗證會話類型", "max_request": "最大請求", "quiet_period": "安靜時期", "reauthentication": "重新驗證", "reauth_period": "重新驗證期", "server_timeout": "伺服器逾時時間", "supp_timeout": "支援逾時時間", "tx_period": "發送周期", "auth_port": "驗證連接埠", "retransmit": "重傳", "force_unauthorized": "Force Unauthorized", "force_Authorized": "Force Authorized", "reauth_port_confirm": "您確定要重新驗證連接埠{{ port }}嗎？", "authorized": "授權", "unauthorized": "未經授權", "title_reauth_port": "重新驗證連接埠", "port_setting": "連接埠 {{ portIndex }} 設置", "account_setting": "帳戶 {{ userName }} 設置", "timeout_retransmit_hint": "所有重試次數不能超過 Dot1x 伺服器逾時值。注意：  所有重試次數=超時*（重傳+1）。建議值：  {{ number }}", "session_status": "會話狀態", "auth_table_of_port_based": "基於埠的身份驗證表", "auth_table_of_mac_based": "基於 MAC 的身份驗證表"}, "dual_homing": {"page_title": "Dual Homing", "multi_dual_homing": "Multiple Dual Homing", "dual_homing_table_settings": "Dual Homing 項目設定", "primary_port": "主要連接埠", "primary_link_status": "主鏈路狀態", "primary_port_status": "主埠狀態", "secondary_port": "備援連接埠", "secondary_link_status": "次鏈路狀態", "secondary_port_status": "次埠狀態", "secondary_port_hint": "備援連接埠不能與主連接埠相同。", "path_switching_mode": "路徑切換模式", "primary_path_always_first": "Primary path always first", "maintain_current_path": "Maintain current path", "maintain_current_path_hint": "保持當前路徑直到斷開", "primary_path_sensing_recovery": "Primary path sensing recovery", "path_switching_mode_hint": "建議啟用", "path_switching_mode_hint_2": "埠的 Linkup Delay 功能", "path": "路徑", "linkup_delay_warning_title": "Linkup Delay 已禁用"}, "poe": {"page_title": "PoE", "power_output": "功率輸出", "poe_supported": "支援 PoE", "scheduling": "安排", "pd_failure_check": "PD故障檢查", "auto_power_cutting": "自動斷電", "auto_power_cutting_hint": "如果功耗超出系統的功率預算，自動斷電功能將刪除最低優先級和最小索引連接埠功率輸出。", "system_power_budget": "系統功率預算", "system_power_budget_hint": "系統功率預算取決於外部電源 (EPS) 的電源能力。", "actual_power_budget": "實際功率預算", "actual_power_budget_hint": "\"實際功率預算\"和\"系統功率預算\"之間的較低值將成為\"功率預算限制\"。", "output_mode": "輸出模式", "high_power": "大功率", "force": "強制", "power_allocation": "電量分配", "legacy_pd_detection": "傳統 PD 檢測", "critical": "重要", "low": "低的", "high": "高的", "rule": "規則", "also_apply_port": "將規則應用於連接埠", "device_ip": "設備IP", "check_frequency": "檢查頻率", "no_response_times": "無回應時間", "no_action": "沒有行動", "restart_pd": "重新啟動PD", "shutdown_pd": "關閉PD", "system_time_status": "系統時間狀態", "system_time": "系統時間", "local_timeZone": "本地時區", "daylight_saving_time": "夏令時間", "off": "關閉", "on": "打開", "rule_name": "規則名稱", "schedule_time": "安排時間", "repeat_execution": "重複執行", "daily": "日常的", "weekly": "每週", "weekdays": "工作日", "weekend": "週末", "sun": "星期日", "mon": "星期一", "tue": "星期二", "wed": "星期三", "thu": "星期四", "fri": "星期五", "sat": "星期六", "sunday": "星期日", "monday": "星期一", "tuesday": "星期二", "wednesday": "星期三", "thursday": "星期四", "friday": "星期五", "saturday": "星期六", "maximum_input_power": "最大輸入功率", "power_budget_limit": "功率預算限制", "power_management_mode": "電源管理模式", "allocated_power": "Allocated Power", "consumed_power": "Consumed Power", "remaining_available_power": "剩餘可用電量", "classification": "分類", "over_current": "過流", "current_ma": "電流（毫安）", "voltage_v": "電壓（V）", "consumption_w": "消耗（W）", "device_type": "設備類型", "not_present": "不存在", "legacy": "舊版PD", "dot3af": "802.3 af", "dot3at": "802.3 at", "dot3bt": "802.3 bf", "dot3btss": "802.3 bt SS", "dot3btds": "802.3 bt DS", "na": "不適用", "configuration_suggestion": "設定建議", "no_suggestion": "沒有建議", "enable_poe": "啟用 PoE 電源輸出", "disable_poe": "禁用 PoE 電源輸出", "select_auto": "選擇輸出模式\"自動\"", "select_high_power": "選擇輸出模式\"高功率\"", "select_force": "選擇輸出模式\"強制\"", "enable_legacy": "啟用舊版 PD 檢測", "raise_eps_voltage": "提高外部電源電壓，使其大於 46 VDC", "pd_failure_check_status": "PD故障檢查狀態", "alive": "運作中", "not_alive": "未運作", "schedule_size_limitation": "該設備只允許 {{ size }} 時間表。", "title_create_rule": "創建規則", "title_edit_rule": "編輯規則", "allocated_power_hint": "計算所有連接埠的功率預算，並確保分配的總功率低於功率預算限制。", "consumed_power_hint": "計算所有連接埠的實時功耗。", "change_power_mode_dialog_title": "設置電源管理模式", "select_allocated_power_mode": "您確定要選擇\"分配功率\"模式嗎？如果是這樣，\"自動斷電\"將被關閉。", "select_consumed_power_mode": "您確定要選擇\"消耗電量\"模式嗎？如果是這樣，\"自動斷電\"將啟用。", "change_power_cutting_dialog_title": "設置自動斷電", "disable_auto_power_cutting": "您確定要禁用\"自動斷電\"嗎？如果是這樣，\"電源管理模式\"將變為\"分配電源\"模式。", "enable_auto_power_cutting": "您確定要啟用\"自動斷電\"功能嗎？如果是這樣，\"電源管理模式\"將變成\"消耗電源\"模式。", "avaliable_power_hint": "\"剩餘可用功率\"是\"最大輸入功率\"減去\"{{ power }}\"。"}, "turbo_chain": {"page_title": "Turbo Chain", "chain_role": "Chain角色", "head": "Head", "member": "Member", "tail": "Tail", "head_port": "Head 連接埠", "tail_port": "Tail 連接埠", "member_port_number": "Member 連接埠 {{ portIndex }}", "chain_information": "Chain 資訊", "head_port_status": "Head 連接埠狀態", "member_port_status": "Member 連接埠狀態", "tail_port_status": "Tail 連接埠狀態", "member_number_port_status": "Member {{ number }} 連接埠狀態", "initiated": "發起"}, "mrp": {"menu_title": "MRP", "page_title": "介質冗餘協議(MRP)", "mrp_role": "角色", "ring_manager": "Ring Manager", "ring_client": "Ring Client", "domain_uuid": "域UUID", "domain_id": "域名ID", "react_on_link_change": "對連線更改做出反應", "ring_port": "環網連接埠 {{ portIndex }}", "ring_status": "環網狀態", "mrp_ring": "MRP環網", "ring_state": "環網狀態", "vid_hint": "VLAN ID 必須與冗餘連接埠的設置一致。", "react_on_link_change_hint": "此功能僅在 MRP Ring Manager 交換機上可用。一旦啟用，環管理器將立即對鏈路變化做出反應，並且MRP拓撲將更快地收斂。", "initiation": "引發", "awaiting_connection": "等待連接", "primary_ring_port_link_up": "主環連接埠連線", "ring_open": "環開", "ring_closed": "環閉合", "data_exchange_idle": "數據交換空閒", "pass_through": "通過", "data_exchange": "數據交換", "pass_through_idle": "通過空閒", "port_pvid_warning": "VLAN ID 必須與冗餘連接埠的設置一致", "port_mode_warning": "除非設置為 VLAN 中繼模式或 VLAN 混合模式,否則 MRP 環埠將不起作用。", "interconnection_port_mode_warning": "除非設置為 VLAN 中繼模式或 VLAN 混合模式,否則 MRP 互連埠將不起作用。", "interconnection": "互連", "interconnection_role": "互連角色", "interconnection_manager": "Interconnection Manager", "interconnection_client": "Interconnection Client", "interconnection_mode": "互聯模式", "lc_mode": "LC-Mode", "rc_mode": "RC-Mode", "interconnection_id": "互連ID", "interconnection_port": "互連連接埠", "interconnection_status": "互連狀態", "interconnection_state": "互連狀態", "interconnection_open": "互聯開放", "interconnection_closed": "互連已關閉", "interconnection_port_idle": "互連連接埠空閒"}, "unicast_table": {"page_title": "單播表", "static_unicast": "靜態單播", "edit_static_unicast_entry": "編輯此靜態單播項目", "add_static_unicast_entry": "添加靜態單播項目", "size_limitation": "該設備的靜態單播項目的最大數量為 {{ size }}。"}, "static_forwarding_table": {"page_title": "靜態傳送表", "menu_title": "靜態傳送", "size_limitation": "該設備的靜態單播項目的最大數量為 {{ size }}.", "title_add_static_forwarding": "創建靜態傳送項目", "title_edit_static_forwarding": "編輯此靜態傳送項目", "title_delete_static_forwarding": "刪除該靜態傳送項目"}, "multicast_table": {"page_title": "靜態組播表", "static_multicast": "靜態組播", "delete_on_reset": "重置時刪除", "delete_on_timeout": "逾時刪除", "add_static_multicast_entry": "添加靜態組播項目", "edit_static_multicast_entry": "編輯此靜態組播項目", "size_limitation": "該設備的靜態組播項目的最大數量為 {{ size }}"}, "gmrp": {"page_title": "GMRP", "group_restrict": "群組限制"}, "time_sync": {"page_title": "時間同步", "page_title_abbr": "時間同步。", "mds_m2_insert_warning": "要使用時間同步,必須在 M2 插槽中插入相容的模組。", "profile": "規範", "8021as": "IEEE 802.1AS-2011 規範", "8021as_abbr": "IEEE 802.1AS-2011", "1588default": "IEEE 1588 Default-2008 規範", "1588default_abbr": "IEEE 1588 Default-2008", "iec61850": "IEC 61850-9-3-2016 規範", "iec61850_abbr": "IEC 61850-9-3-2016", "c37238": "IEEE C37.238-2017 規範", "c37238_abbr": "IEEE C37.238-2017", "priority_number": "優先級 {{ number }}", "clock_type": "時鐘類型", "clock_type_bc": "Boundary Clock", "clock_type_tc": "Transparent Clock", "delay_mechanism": "延遲機制", "e2e": "End-to-End", "p2p": "Peer-to-Peer", "transport_mode": "傳輸模式", "8023ehternet": "IEEE 802.3 Ethernet", "udp_ipv4": "UDP IPv4", "udp_ipv6": "UDP IPv6", "domain_number": "域名號碼", "clock_mode": "時鐘模式", "two_step": "Two-step", "one_step": "One-step", "accuracy_alert": "準確性警報", "bmca": "BMCA", "bmca_hint": "建議開啟最佳主時鐘演算法(BMCA)，以防止使用Transparent Clock時出現網路迴圈的狀況。", "max_steps_removed": "刪除的最大步數", "grandmaster_id": "主時鐘ID", "announce_interval": "宣布週期", "announce_receipt_timeout": "通知接收逾時", "sync_interval": "同步週期", "sync_receipt_timeout": "同步接收逾時", "delay_req_interval": "延遲請求週期", "pdelay_req_interval": "Pdelay-請求週期", "neighbor_rate_ratio": "鄰區比率", "neighbor_prop_delay": "鄰居傳播延遲", "path_delay": "路徑延遲", "neighbor_prop_delay_thresh": "鄰居傳播延遲閾值", "synchronization_status": "同步狀態", "transport_type": "運輸類型", "current_data_set": "當前數據集", "parent_data_set": "父數據集", "locked": "鎖定", "unlocked": "解鎖", "freerun": "自由運行模式", "syncing": "同步", "browser_time": "瀏覽器時間", "ptp_clock_time": "PTP 時鐘時間（TAI）", "ptp_slave_port": "PTP從連接埠", "offset_from_master": "與主時鐘的偏移", "mean_path_delay": "平均路徑延遲", "steps_removed": "步驟已刪除", "parent_identity": "父時鐘辨識", "grandmaster_identity": "主時鐘辨識", "cumulative_rate_ratio": "累計比率", "grandmaster_priority_number": "主時鐘優先級 {{ number }}", "grandmaster_clock_class": "主時鐘等級", "grandmaster_clock_accuracy": "主時鐘精準度", "8021as_capable": "支持 802.1AS", "initializing": "正在初始化...", "res_ptp_initializing_done": "PTP服務已準備就緒。", "faulty": "有故障", "pre_master": "預備主時鐘", "uncalibrated": "未校準", "sync_transmit": "同步傳輸", "sync_receive": "同步接收", "edit_port_profile_setting": "編輯連接埠 {{ portIndex }} IEEE 1588v2 預設規範設定"}, "stream_adapter": {"page_title": "優先管理", "pcp": "優先級代碼點（PCP）", "ingress_table_limit_hint": "每個連接埠最多支援10個項目。", "port_size_limitation": "已達到此連接埠的最大項目數 (10)。", "hexadecimal": "十六進制數字", "egress_untag": "出口未標記", "ingress": "入口", "egress": "出口", "per_stream_priority_title": "每個流優先級", "port_default_priority_title": "連接埠預設優先級", "per_stream_priority_hint": "如果未標記的流符合任何用戶定義的規則，則將根據每個流的優先級進行處理。如果流不匹配任何規則，將根據預設連接埠優先級進行處理。", "add_stream_adapter_entry": "添加每個流優先級項目", "edit_stream_adapter_entry": "編輯此每流優先級項目", "edit_port_def_priority": "編輯連接埠預設優先級 {{ portIndex }}"}, "static_route": {"page_title": "靜態路由", "size_limitation": "該設備的最大靜態路由數為 {{ size }}.", "next_hop_IP": "下一跳IP", "next_hop_interface": "下一跳介面", "delete_static_route_desc": "您確定要刪除所選路線嗎？", "next_hop_type": "下一跳類型*", "next_hop_type_hint": "您可以選擇之前創建的 VLAN 介面或將此字段留空。", "next_hop_two_field_invalid": "您必須指定下一跳的 IP 或介面", "add_static_route": "創建靜態路由", "edit_static_route": "編輯此靜態路由", "delete_static_route": "刪除該靜態路由"}, "routing_table": {"page_title": "路由表", "static": "靜態", "next_hop": "下一跳", "ad_metric": "AD/公制"}, "online_accounts": {"page_title": "線上帳戶", "idle_time": "閒置時間", "remove_account_dialog_title": "刪除此線上帳戶", "remove_account_dialog_desc": "您確定要刪除此線上帳戶嗎？"}, "8021qbv": {"page_title": "時間感知塑造者", "cycle_time": "週期", "start_time_hint": "時間感知整形器基於當前 PTP 時間。您可以確定開始時間，也可以立即設置時間，這將確定該功能何時開始。", "config_change_time": "設定更改時間", "default_setting": "預設設定", "gate_control_list": "閘門控制清單", "totla_slot": "總槽位", "interval": "週期", "selected_queue_summary": "選定的隊列摘要", "interval_hint": "閘門週期表示閘門何時打開以及將保持打開狀態多長時間。以太網封包的最小週期如下：", "interval_hint_1G": "1G連接埠：  1μs，", "interval_hint_100M": "100M連接埠：  10μs，", "interval_hint_10M": "10M連接埠：  100μs", "port_status": "連接埠 {{ portIndex }} 地位", "select_port": "選擇連接埠"}, "ospf": {"page_title": "OSPF", "ospf_settings": "OSPF 設置", "ospf_status": "OSPF 狀態", "area": "區域", "neighbor": "鄰居", "aggregation": "聚合", "virtual_link": "虛擬連線", "router_id": "路由器ID", "current_router_id": "當前路由器ID", "current_router_id_hint": "如果Router ID設置為0.0.0.0，則最低的介面IP位址將自動分配為Router ID。", "compatible_rfc_1583": "RFC 1583 兼容性", "spf_hold_time": "SPF 保持時間", "redistribute": "重新分配", "metric": "度量值", "ospfRRDStatic": "靜態", "ospfRRDConnected": "連接的", "ospfRRDRip": "RIP", "area_size_limitation": "該設備的最大區域數是 {{ size }}.", "area_id": "區域ID", "area_type": "區域類型", "normal": "Normal", "stub": "<PERSON><PERSON>", "nssa": "NSSA", "summary": "Summary", "no_summary": "No Summary", "delete_area_desc": "您確定要刪除所選區域嗎？", "dead_interval": "Dead週期", "cost": "成本", "network_type": "網路類型", "passive_interface": "被動介面", "neighbor_ip_address": "鄰居IP位址", "summary_hint": "如果區域類型設置為普通，則摘要不可用。", "broadcast": "Broadcast", "non_broadcast": "Non-broadcast", "point_to_point": "Point-to-point", "point_to_multipoint": "Point-to-multipoint", "nbr_ip_address": "鄰居IP位址", "nbr_size_limitation": "該設備的最大鄰居數是 {{ size }}.", "delete_nbr_desc": "您確定要刪除選定的鄰居嗎？", "lsa_type": "LSA 類型", "type_7": "Type 7", "aggregation_size_limitation": "該設備的最大聚合數量為 {{ size }}.", "delete_aggregation_desc": "您確定要刪除選定的聚合嗎？", "vLink_size_limitation": "該設備的最大虛擬連線數為 {{ size }}.", "delete_vLink_desc": "您確定要刪除所選的虛擬連線嗎？", "loopback": "環回", "waiting": "等待", "dr": "DR", "bdr": "BDR", "dr_other": "其他DR", "dr_router_id": "DR路由器ID", "bdr_router_id": "BDR 路由器 ID", "neighbor_id": "鄰域 ID", "neighbor_state": "鄰域狀態", "dead_time": "不運作時間", "accempt": "試圖", "two_way": "2種方法", "exstart": "開始", "exange": "交換", "loading": "正在加載", "full": "滿的", "database": "資料庫", "link_id": "連線ID", "adv_router": "ADV路由器", "age": "年齡", "events": "活動", "ls_retrans_queue_len": "LSA重傳隊列長度", "hello_suppressed": "Hello封包抑制", "router": "路由器", "asbr_summary": "ASBR摘要", "as_external": "作為外部", "group_member": "團體成員", "nssa_external": "NSSA 外部", "title_edit_ospf_redistribute": "編輯重新分發 {{ protocol }}", "title_create_ospf_area": "創建一個區域", "title_edit_ospf_area": "編輯該區域", "title_delete_ospf_area": "刪除該區域", "title_create_ospf_nbr": "創建一個鄰居", "title_edit_ospf_nbr": "編輯這個鄰居", "title_delete_ospf_nbr": "刪除這個鄰居", "title_create_ospf_aggregation": "創建一個聚合", "title_edit_ospf_aggregation": "編輯此聚合", "title_delete_ospf_aggregation": "刪除此聚合", "title_create_ospf_vlink": "創建虛擬連線", "title_edit_ospf_vlink": "編輯此虛擬連線", "title_delete_ospf_vlink": "刪除該虛擬連線"}, "vrrp": {"page_title": "VRRP", "v2": "V2", "v3": "V3", "virtual_router_enable": "虛擬路由器", "vrid": "虛擬ID", "decrement": "減少", "primary_ip": "虛擬路由器IP位址", "adv_int": "廣告週期", "preempt_mode": "搶占模式", "preempt_delay": "搶占延遲", "accept_mode": "接受模式", "auth_key": "驗證密鑰", "size_limitation": "該設備的 VRRP 項目最大數量為 {{ size }}.", "delete_vrrp_desc": "您確定要刪除選定的虛擬路由器嗎？", "master_address": "主位址", "master_adv_int": "主廣告週期（毫秒）", "master_down_int": "主控停機週期（毫秒）", "title_add_vrrp": "創建虛擬路由器", "title_edit_vrrp": "編輯此虛擬路由器", "title_delete_vrrp": "刪除該虛擬路由器", "require_decrement_less_than_priority": "該值必須小於優先順序值"}, "dns": {"page_title": "DNS 設置", "primary_dns_server": "主 DNS 伺服器", "secondary_dns_server": "輔助 DNS 伺服器", "dns_server_number": "DNS伺服器IP位址{{ number }}", "dns_server": "DNS伺服器", "dns_reverse_lookup": "DNS反向查找", "zone_table": "區域表", "dns_table_for_name": "DNS 表 {{ zoneName }}", "dns_server_summary": "DNS伺服器摘要", "fqdn": "完全限定域名", "fqdn_hint": "FQDN（完全限定域名）是 \"主機名\".\"域名\"", "dns_forwarding": "DNS 轉發", "dns_forwarding_hint": "啟用 DNS 轉發需要活動的 DNS 伺服器。", "default_forwarder_ip": "預設轉發器 IP 位址", "default_forwarder_ip_hint": "如果指定了默認轉發器,則轉發器表中未列出的區域的 DNS 查詢將轉發到預設轉發器。", "forwarder_ip": "轉發器 IP 位址", "forwarders_table": "貨運代理表", "zone_hint": "'.' 可用於轉發任何區域。", "zone_size_limitation": "區域項目的最大數量是 {{ size }}.", "dns_size_limitation": "DNS項目的最大數量是 {{ size }}.", "title_create_zone": "創建一個區域", "title_edit_zone": "編輯 {{ zoneName }} 設置", "title_delete_zone": "刪除區域", "delete_zone_desc": "您確定要刪除所選區域嗎？", "title_create_dns": "創建{{ zoneName }}資源記錄 ", "title_edit_dns": "編輯{{ zoneName }}資源記錄 ", "title_delete_dns": "刪除資源記錄", "delete_dns_desc": "您確定要刪除選定的資源記錄嗎？", "title_create_forwarding": "創建 DNS 轉發條目", "title_edit_forwarding": "編輯 DNS 轉發條目", "title_delete_forwarding": "刪除 DNS 轉發條目", "delete_forwarding_desc": "您確定要刪除選定的 DNS 轉發條目嗎？", "duplicate_hostname": "相同的主機名已存在", "duplicate_domain_name": "相同的功能變數名稱已經存在", "duplicate_zone": "此區域已存在"}, "acl": {"page_title": "Access控制列表", "access_list_type": "Access列表類型", "access_list_type_hint": "對於相同的索引，MAC位址的優先級高於IP位址。", "access_list_index_hint": "較低的索引代表較高的優先級。", "ip_based": "IP-based", "mac_based": "MAC-based", "acl_size_limitation": "ACL項目的最大數量是 {{ size }}.", "acl_ip_based_size_limitation": "基於 IP 的 ACL 規則的最大數量為 {{ size }}.", "acl_mac_based_size_limitation": "基於 MAC 的 ACL 規則的最大數量為 {{ size }}.", "acl_rule_size_limitation": "ACL規則的最大數量是 {{ size }}.", "active_interface_type": "啟用介面類型", "vlan_based": "VLAN-based", "port_based": "Port-based", "active_ingress_vlan": "啟用入流VLAN", "active_egress_vlan": "啟用出流VLAN", "active_ingress_port": "啟用入流連接埠", "active_egress_port": "啟用出流連接埠", "ingress_setting_hint": "啟用VLAN和規則VLAN必須相同。", "egress_setting_hint": "帶有重定向操作的規則不能應用於出流介面。", "ingress_setting_vlan_hint": "啟用VLAN和規則VLAN 必須相同。\n帶有備註操作的規則不能應用於入流介面。", "egress_setting_vlan_hint": "啟用VLAN和規則VLAN 必須相同。\n具有重定向操作的規則不能應用出流介面。", "rule_type": "規則類型", "index_priority_hint": "索引較低的規則具有較高的優先級。", "acl_rule": "ACL 規則", "rule": "規則", "rule_index": "規則索引 {{ruleIndex}}", "permit": "Permit", "deny": "<PERSON><PERSON>", "ethertype_value": "以太類型值", "goose": "GOOSE", "smv": "SMV", "protocol_number": "協議號碼", "user_defined": "User-defined", "source": "來源", "source_port": "來源連接埠", "source_ip_addr": "來源IP位址", "source_ip_mask": "來源IP遮罩", "source_mac_addr": "來源MAC位址", "source_mac_mask": "來源 MAC 遮罩", "destination_port": "目標連接埠", "destination_ip_addr": "目標IP位址", "destination_ip_mask": "目標IP遮罩", "destination_mac_addr": "目標MAC位址", "destination_mac_mask": "目標 MAC 遮罩", "cos_remark": "Cos標記", "dscp_remark": "DSCP標記", "optional_parameter": "可選參數", "log": "日誌", "logging": "記錄", "logging_enable": "啟用日誌記錄", "src_port": "來源連接埠", "dst_port": "目標連接埠", "icmp_type": "ICMP 類型", "icmp_code": "ICMP 代碼", "igmp_type": "IGMP 類型", "redirect": "重定向", "redirect_mirror": "重定向/鏡像", "redirect_enable": "重定向", "redirect_port": "重定向連接埠", "redirect_port_name": "重定向到連接埠 {{ portName }}", "mirror": "鏡子", "session_id": "會話 {{id}}", "mirror_disable_hint": "如果連接埠關閉鏡像功能，則無法進行鏡像操作。", "session_disable_hint": "'鏡像'操作無法對禁用的連接埠鏡像會話生效。", "mirror_sesstion": "鏡像到會話 {{ sesstionId }}", "remark_cos": "將 CoS 備註為 {{ cos }}", "remark_dscp": "將 DSCP 備註為 {{ dscp }}", "acl_table_of_name": "ACL表 {{ aclName }}", "delete_acl_list_desc": "您確定要刪除選定的Access控制列表嗎？", "delete_acl_rule_desc": "您確定要刪除所選規則嗎？", "any_hint": "如果沒有輸入任何值，將被視為設置為任意。", "log_interval": "記錄週期", "log_threshold": "記錄閾值", "acl_summary": "ACL摘要", "number_of_activate_acl": "啟用的 ACL 數量（最大 16）", "activate_direct": "方向", "ingress": "入流", "egress": "出流", "both": "兩個都", "activated": "已啟用", "inactivated": "已停用", "hit_count": "點擊數", "counter": "計數器", "view_list": "查看列表", "view_by_acl": "通過ACL查看", "view_by_port": "依照連接埠查看", "view_by_vlan": "依照VLAN查看", "acl_table_of_type": "ACL表 {{typeIndex}}", "no_activated_acl_port": "此連接埠上沒有啟用ACL。", "no_activated_acl_vlan": "此 VLAN 上沒有啟用ACL。", "status_hint": "索引較低的規則具有較高的優先級。設備將從最低索引開始依照數字順序將封包與所有規則匹配。如果封包匹配某個規則，則將應用相應的規則。", "title_create_access_list": "創建Access列表", "title_edit_access_list": "編輯 {{ typeIndex }} Access列表設置", "title_delete_acl_list": "刪除Access列表", "title_create_acl_rule": "創建規則索引 {{ ruleIndex }} 為了 {{ typeIndex }}", "title_edit_acl_rule": "編輯規則索引 {{ ruleIndex }} 的 {{ typeIndex }}", "title_delete_acl_rule": "刪除規則", "title_clear_acl_counter": "清空計數器", "desc_clear_all_acl_counter_desc": "您確定要重置所有計數器嗎？", "desc_clear_single_acl_counter_desc": "您確定要重置計數器嗎？ {{ typeIndex }} ACL？", "blacklist_udp_port_dhcp_server": "不允許 DHCP 伺服器", "blacklist_udp_port_dhcp_client": "不允許 DHCP 用戶端", "blacklist_udp_port_moxa_command": "不允許 Moxa 服務", "blacklist_ether_type_eth_confg_test_protocol": "不允許以太網設定測試協議", "blacklist_ether_type_lldp": "不允許 LLDP", "blacklist_ether_type_eapol": "不允許 EAPOL", "blacklist_ether_type_lacp": "不允許 LACP", "blacklist_ether_type_llc_jumbo_frame": "不允許使用 LLC 巨型幀", "blacklist_ether_type_arp": "不允許 ARP", "blacklist_ether_type_mrp": "不允許 MRP", "blacklist_ether_type_profinet": "不允許使用 PROFINET", "blacklist_ether_type_ptp": "不允許 PTP", "blacklist_ether_type_goose": "不允許GOOSE", "blacklist_ether_type_smv": "不允許使用 SMV", "blacklist_mac_ieee_reserved_multicast": "不允許 IEEE 保留組播 MAC 位址", "blacklist_mac_ip_multicast": "不允許 IP 組播 MAC 位址", "blacklist_mac_broadcast": "不允許廣播 MAC 位址", "blacklist_mac_l2_multicast": "不允許 L2 組播 MAC 位址", "blacklist_mac_device": "不允許設備 MAC 位址", "blacklist_dest_ip_multicast": "不允許組播 IP 位址", "overwrite_vlan_dialog_title": "用啟用 VLAN 覆蓋規則 VLAN", "overwrite_vlan_dialog_content": "啟用的 VLAN 和規則 VLAN 必須相同。您確定要讓啟用VLAN 覆蓋規則 VLAN 嗎？"}, "stream_id": {"page_title": "流識別", "title_create_stream_id": "創建流", "title_edit_stream_id": "編輯此流", "title_delete_stream_id": "刪除流", "delete_stream_id_desc": "您確定要刪除選定的流嗎？"}, "8021cb": {"page_title": "幀複製和可靠性消除（FRER）", "frer": "FRER", "split": "分裂", "forward": "傳送", "merge": "合併", "stream_vid_mac": "流（VLAN/MAC 位址）", "input_port": "輸入連接埠", "input_ports": "輸入連接埠", "output_port_index": "輸出連接埠 {{ portIndex }}", "output_port": "輸出連接埠", "output_ports": "輸出連接埠", "ingress_stream": "入口流", "egress_stream": "出口流", "vlan_overwrite": "VLAN 覆蓋", "mac_address_overwrite": "MAC位址覆蓋", "priority_overwrite": "優先覆蓋", "overwrite": "覆蓋", "disable_port_input_hint": "這是一個選定的輸入連接埠。", "disable_port_vlan_hint": "該連接埠不是相應VLAN的成員。", "disable_vid_overwrite_hint": "輸出連接埠不是對應VLAN的成員。", "disable_exist_stream_hint": "此流已存在 FRER 項目。", "disable_select_stream_hint": "該流已被選擇。", "to_end_device": "至終端設備", "ingress_size_limitation": "最大入口流是 {{ size }}.", "title_create_frer_entry": "創建 FRER 項目", "title_edit_frer_entry": "編輯此 FRER 項目", "title_delete_frer_entry": "刪除此 FRER 項目", "delete_frer_entry_desc": "您確定要刪除選定的 FRER 項目嗎？"}, "loop_protection": {"page_title": "網路迴圈保護", "detect_interval": "檢測週期", "loop_status": "迴圈狀態", "peer_port": "對等連接埠", "looping": "Looping"}, "binding_database": {"page_title": "綁定資料庫", "binding_settings": "綁定設置", "binding_status": "綁定狀態", "binding_status_hint": "動態綁定是向 DHCP Snooping學習的。", "binding_status_hint_2": "如果靜態表項目的VLAN ID和MAC位址組合已經存在，則綁定狀態不會更新。", "title_create_entry": "創建綁定資料庫靜態項目", "title_edit_entry": "編輯此綁定資料庫靜態項目", "duplicate_of_dynamic_entry": "VLAN ID 和 MAC 位址組合已存在。這個新項目將覆蓋初始動態項目。", "size_limitation": "綁定狀態項目的最大數量為 {{ size }}.", "binding_table_max": "最大 {{ size }} 綁定狀態表", "dai": "DAI", "ipsg": "IPSG", "ipsg_dai": "IPSG、DAI"}, "dhcp_snooping": {"page_title": "DHCP Snooping", "port_is_ip_sg_enable": "此連接埠已為IP來源保護啟用。IP來源保護只能在不受信任的連接埠上啟用。", "port_is_dai_enable": "此連接埠已啟用動態 ARP 檢查。動態 ARP 檢查只能在不受信任的連接埠上啟用。", "port_is_ip_sg_and_dai_enable": "此連接埠已啟用動態 ARP 檢查和IP來源保護。動態 ARP 檢查和IP來源保護只能在不受信任的連接埠上啟用。"}, "ip_source_guard": {"page_title": "IP來源保護", "port_is_trusted": "此連接埠是 DHCP Snooping 的可信連接埠。只能為IP來源保護啟用不受信任的連接埠。", "port_is_la_member": "該連接埠是連接埠通道的成員。成員連接埠上無法啟用IP來源保護。", "binding_status_single_empty": "連接埠的綁定狀態 {{ port }} 是空的。", "binding_status_multiple_empty": "連接埠的綁定狀態 {{ port }} 都是空的。", "binding_setting_hint": "您應該啟用 DHCP Snooping 以獲取動態綁定或在綁定資料庫 -> 綁定設置中設定數據。"}, "mms": {"page_title": "MMS", "ied_name": "IED名稱", "cid_file_settings": "CID文件設置", "report_control_block": "報告控制區塊", "data_change": "數據變更", "data_update": "數據更新", "quality_change": "質變", "integrity": "數據完整性", "buffer_time": "緩衝時間", "integrity_period": "數據完整性檢查週期時間", "t_profile_cert_info": "T-Profile證書資訊", "a_profile_cert_info": "A-Profile證書資訊", "ca_name": "CA名稱", "t_profile_security": "T-Profile安全加密", "a_profile_security": "A-Profile安全加密", "import_client_ca": "匯入用戶端CA", "import_client_cert": "匯入用戶端證書", "title_edit_name": "編輯 {{ name }}", "title_mms_enable_warning": "啟用MMS協議", "mms_enable_warning_desc": "您確定要啟用非安全協議 (MMS) 嗎？"}, "password_policy": {"page_title": "密碼制定原則", "minimum_length": "最小密碼長度", "policy_numbers": "必須包含至少一位數字（0-9）", "policy_uppercase": "必須包含至少一個大寫字母（AZ）", "policy_lowercase": "必須包含至少一個小寫字母 (az)", "policy_symbols": "必須至少包含一個特殊字符({}[]()|:;~!@#%^*-_+=,.)", "max_life_time": "密碼最長有效期限", "password_complexity_strengh_check": "密碼複雜度及強度檢查"}, "system_info": {"page_title": "設備資訊", "system_name": "設備名稱", "contact_information": "聯繫資訊", "sync_to_chassis_id_hint": "當 LLDP chassis ID 子類型為 \"local\"時，修改設備名稱將同步修改 LLDP chassis ID。"}, "login_authentication": {"page_title": "登入驗證", "authentication_protocol": "身份驗證協議", "radius_local": "RADIUS, Local", "tacacs_local": "TACACS+, Local"}, "login_policy": {"page_title": "登入規則", "login_message": "登入訊息", "auth_fail_message": "登入驗證失敗訊息", "failure_lockout": "帳戶登入失敗鎖定", "retry_failure_threshold": "重試失敗閾值", "lockouttime": "鎖定持續時間", "auto_logout_setting": "自動登出時間", "auto_logout_warring_title": "關閉自動登出", "auto_logout_setting_alert": "如果自動登出值設置為 0，會話將永遠不會超時。請在關閉瀏覽器之前先退出。"}, "ip_settings": {"page_title": "IP設定", "ip_settings": "IP設置", "ip_status": "IP狀態", "get_ip_from": "獲取IP來自", "dns_server": "DNS伺服器IP位址", "ipv6": "IPv6", "ipv6_global_unicast_address_prefix": "IPv6 全球單播地址前綴", "ipv6_dns_server_number": "IPv6 DNS 伺服器 {{ number }}", "ipv6_dns_server": "IPv6 DNS 伺服器", "ipv6_global_unicast_address": "IPv6全球單播位址", "ipv6_link_local_address": "IPv6 鏈路本地位址", "profinet_dcp": "PROFINET DCP", "dhcp_bootfile": "DHCP 啟動文件", "dhcp_bootfile_hint": "如果啟用，系統將自動從選項 66 中描述的檔案伺服器下載並恢復選項 67 中描述的引導檔的設定設置。", "dhcp_client": "DHCP 客端標示符", "dhcp_client_hint": "開啟後，系統將會傳送包含Option 61 標籤的DHCP客端訊息。DHCP伺服器將會盡可能分配與客端ID值關聯的IP位址。", "dhcp_client_type": "DHCP 客端標示符類型", "dhcp_client_value": "DHCP 客端標示符值"}, "management_interface": {"page_title": "管理介面", "user_interface": "用戶介面", "interface": "介面", "enable_http": "HTTP", "http_port": "HTTP - TCP 連接埠", "enable_https": "HTTPS", "https_port": "HTTPS - TCP 連接埠", "enable_telnet": "遠程登入", "telnet_port": "Telnet - TCP 連接埠", "ssh_port": "SSH - TCP 連接埠", "enable_snmp_V1V2c": "SNMP版本V1、V2c", "snmp_protocol": "SNMP - 傳輸層協議", "udp": "UDP", "tcp": "TCP", "snmp_udp_port": "SNMP - UDP 連接埠", "snmp_tcp_port": "SNMP - TCP 連接埠", "enable_moxa_service": "Moxa服務", "moxa_tcp_port": "Moxa 服務（加密）- TCP 連接埠", "moxa_udp_port": "Moxa 服務（加密）- UDP 連接埠", "max_session_http": "HTTP+HTTPS 的最大登入會話數", "max_session_terminal": "Telnet+SSH 的最大登入會話數", "enable_nonsecure_interface_warning_title": "啟用 {{ interfaceType }} 介面", "enable_nonsecure_interface_warning": "您確定要啟用非安全介面嗎（{{ interfaceType }}）？"}, "hareward_interface": {"page_title": "硬體介面", "dip_switch": "指撥開關", "usb_function": "USB介面", "micro_sd_function": "MicroSD 介面"}, "account_management": {"page_title": "用戶帳戶", "admin": "Admin", "supervisor": "Supervisor", "user": "User", "new_password": "新密碼", "title_edit_account": "編輯此帳戶", "title_add_account": "創建一個新帳戶", "title_edit_account_password": "編輯帳戶密碼", "new_pwd_not_match": "密碼不匹配。", "tech_account_add_error": "無法創建帳戶 \"moxasupport\",因為它是為 Moxa 技術支援保留的。", "tech_account_remove_error": "無法編輯或刪除帳戶 \"moxasupport\",因為它是為 Moxa 技術支援保留的。", "account_name_taken": "該帳戶用戶名已被佔用", "size_limitation": "該設備的最大用戶帳戶數為 {{ size }}"}, "time": {"page_title": "系統時間", "sntp": "SNTP", "ntp": "NTP", "time_zone": "時區", "current_time": "當前時間", "daylight_saving": "夏令時間", "end_date": "結束日期", "offset": "抵消", "ntp_authentication": "NTP驗證", "query_interval": "查詢間隔", "ptp": "PTP", "start": "開始", "end": "結尾", "date": "日期", "month": "月", "week": "星期", "day": "天", "hour": "小時", "minute": "分鐘", "jan": "一月", "feb": "二月", "mar": "三月", "apr": "四月", "may": "五月", "jun": "六月", "jul": "七月", "aug": "八月", "sep": "九月", "oct": "十月", "nov": "十一月", "dec": "十二月", "1st": "第一", "2nd": "第二", "3rd": "第三", "4th": "第四", "last": "最後的", "sun": "星期日", "mon": "星期一", "tue": "星期二", "wed": "星期三", "thu": "星期四", "fri": "星期五", "sat": "星期六", "time_server_number": "時間伺服器 {{ number }}", "time_server_1": "第一時間伺服器：IP 位址/域名", "time_server_2": "第二個時間伺服器：IP 位址/域名", "clock_source": "時鐘來源", "key_string": "密鑰字符串", "delete_entry_confirm_desc": "您確定要刪除選定的密鑰字符串嗎？", "size_limitation": "該設備的 NTP 身份驗證密鑰的最大數量為 {{ size }}"}, "ntp_server": {"page_title": "NTP伺服器"}, "ssh_ssl": {"page_title": "SSH 和 SSL", "ssh": "SSH", "ssl": "SSL", "regen_ssh_key": "重新生成 SSH 密鑰", "regen_ssl_cert": "重新生成 SSL 證書", "export_ssl_cert": "匯出 SSL 證書", "import_ssl_cert": "匯入證明書", "ssl_info": "證書資訊", "ca_name": "CA名稱", "title_export_ssl_certificate": "匯出 SSL 證書"}, "dhcp": {"page_title": "DHCP 伺服器", "dhcp": "DHCP", "ntp_server": "NTP伺服器IP位址", "dhcp_pool_settings": "DHCP 伺服器池設置", "static_ip_assignment_table": "靜態IP分配表", "start_ip": "起始IP位址", "end_ip": "結束IP位址", "lease_time": "租期", "hostname": "主機名", "log_server": "日誌伺服器IP位址", "gateway": "閘道", "matching_rule": "匹配規則", "client_id_type": "客戶端識別碼類型", "client_id_value": "客戶端識別碼值", "circuit_id_type": "選項 82 電路 ID 類型", "circuit_id_value": "選項 82 電路 ID 值", "remote_id_type": "選項 82 遠端 ID 類型", "remote_id_value": "選項 82 遠端 ID 值", "hostname_hint": "主機名代表 DHCP 用戶端的名稱，並將被編碼到 DHCP Offer 封包的選項 12 標記中。", "time_left": "剩餘時間", "dhcp_ip_mac": "基於 DHCP/MAC 的 IP 分配", "dhcp_static_ip": "DHCP/靜態IP分配", "lease_table": "租賃表", "ip_mac_binding": "基於MAC的IP分配", "ip_port_binding": "基於連接埠的IP分配", "classless_static_route_table": "無類別靜態路由表", "delete_dhcp_entry_confirm_desc": "您確定要刪除此 DHCP 伺服器池嗎？", "delete_static_ip_entry_confirm_desc": "您確定要刪除選定的靜態 IP 條目嗎？", "delete_ip_port_entry_confirm_desc": "您確定要刪除選定的基於連接埠的 IP 分配嗎？", "delete_ip_mac_entry_confirm_desc": "您確定要刪除選定的基於 MAC 的 IP 分配嗎？", "dhcp_size_limitation": "該設備的 DHCP 伺服器池的最大數量為 {{ size }}.", "invalid_dhcp_pool_range": "無效的：  交換機的管理IP位址應在IP子網範圍內。", "delete_static_route_entry_confirm_desc": "您確定要刪除所選路線嗎？", "default_gateway_setting_hint": "無類靜態路由的預設閘道使用在基於連接埠的 IP 分配設定部分中設定的預設閘道位址。"}, "dhcp_relay": {"page_title": "DHCP中繼代理", "option82": "Option 82", "server1": "第一個伺服器IP位址", "server2": "第二個伺服器IP位址", "server3": "第三個伺服器IP位址", "server4": "第四個伺服器IP位址", "remote_id_type": "遠程ID類型", "remote_id_value": "遠程ID值", "remote_id_display": "遠程ID顯示", "client_id": "Client ID", "relay": "中繼"}, "ping": {"page_title": "<PERSON>", "ping_result": "Ping {{ targetHost }} 結果"}, "email_settings": {"page_title": "電子郵件設置", "tls_enable": "TLS", "sender_address": "發件人位址", "recipient_1": "第一收件人電子郵件位址", "recipient_2": "第二收件人電子郵件位址", "recipient_3": "第三收件人電子郵件位址", "recipient_4": "第四收件人電子郵件位址", "recipient_5": "第五收件人電子郵件位址"}, "snmp": {"page_title": "SNMP", "v1": "V1, V2c, V3", "v2c": "V1, V2c", "v3": "V3 Only", "snmp_version": "SNMP版本", "snmp_account": "SNMP 帳戶", "read_community": "閱讀社群", "read_write_community": "讀/寫社群", "read_write": "Read/Write", "des": "DES", "aes": "AES", "snmp_account_size_limitation": "該設備的最大 SNMP 帳戶數為 {{ size }}.", "snmp_warning_dialog_V1V2c_title": "設置 SNMP 版本", "snmp_warning_dialog_V1V2c_desc": "您確定要啟用非安全介面（SNMP 版本 V1、V2c）嗎？", "snmp_warning_dialog_authMD5_title": "設置身份驗證類型 MD5", "snmp_warning_dialog_authMD5_desc": "MD5 身份驗證僅提供有限的安全性。您確定要繼續嗎？", "change_password_title": "更改驗證密碼", "change_key_title": "更改加密密鑰", "change_key_button": "更改加密密鑰", "create_v3_account": "創建 SNMP 帳戶", "edit_v3_account": "編輯此 SNMP 帳戶"}, "snmp_trap": {"page_title": "SNMP陷阱/通知", "snmp_trap_inform_recipient": "SNMP陷阱/通知收件人", "snmp_inform_settings": "SNMP通知設置", "inform_retry": "通知重試", "inform_timeout": "通知逾時", "recipient_name": "收件人IP位址/域名", "trap_community": "陷阱社群", "snmp_trap_inform_account": "SNMP 陷阱/通知帳戶", "trap_v1": "Trap V1", "trap_v2c": "Trap V2c", "inform_v2c": "Inform V2c", "trap_v3": "Trap V3", "inform_v3": "Inform V3", "title_delete_host_dialog_title": "刪除該主機", "title_delete_host_dialog_desc": "您確定要刪除該主機嗎？", "snmp_trap_account_size_limitation": "該設備的 SNMP 陷阱/通知帳戶的最大數量為 {{ size }}.", "snmp_host_size_limitation": "該設備的 SNMP 陷阱主機的最大數量為 {{ size }}.", "create_v3_trap_account": "創建 SNMP 陷阱帳戶", "edit_v3_trap_account": "編輯此 SNMP 陷阱帳戶", "create_host_table": "創建主機", "edit_host_table": "編輯該主機"}, "arp": {"page_title": "ARP表", "title_clear_message": "清除所有 ARP 項目", "clear_confirmation_message": "您確定要清除所有 ARP 項目嗎？"}, "event_log": {"page_title": "事件日誌", "boot": "啟動號碼", "progname": "程序名稱", "timestamp": "時間戳", "uptime": "正常運行時間", "message": "訊息", "severity_emerg": "Emergency", "severity_alert": "<PERSON><PERSON>", "severity_info": "Info", "severity_debug": "Debug", "flush_log_entry_confirmation_message": "您確定要清除所有日誌項目嗎？", "total_entries": "總項目：", "clear_all_logs": "清除所有日誌", "capacity_warning": "容量警告", "capacity_warning_hint": "可以在事件通知頁面上為各個事件設定註冊操作。", "warning_threshold": "警告閾值", "oversize_action": "事件超量設定", "oversize_action_overwrite": "Overwrite the oldest event log", "oversize_action_stop": "Stop recording event logs", "title_clear_all_logs": "清除所有日誌", "debug_hint": "{{ number }} 日誌供內部使用。", "hash_value": "哈希值", "auto_backup_of_event_log": "自動備份事件日誌"}, "trust_access": {"page_title": "可信Access", "size_limitation": "該設備的最大可信Access項目數為 {{ size }}.", "delete_all_warning_title": "無法刪除所有可信Access項目", "delete_all_warning_1": "如果啟用，可信Access需要至少一個活動項目。強烈建議保留", "delete_all_warning_2": "您當前的IP位址", "delete_all_warning_3": "作為活動項目。"}, "utilization": {"page_title": "資源利用率", "cpu_utilization": "CPU使用率", "cpu_historical_record": "CPU 使用歷史記錄", "mem_utilization": "記憶體使用情況", "mem_historical_record": "記憶體使用歷史記錄", "power_utilization": "能量消耗", "power_historical_record": "電力消耗歷史", "last_update_time": "最近更新時間 ", "used": "用過的", "free": "可用的", "past_10_second": "過去 10 秒", "past_30_second": "過去 30 秒", "past_300_second": "過去 300 秒", "selecting_visible_polyline": "選取顯示資料", "polyline_display_hint": "按一下右上角的圖示以選擇要顯示的資料。"}, "tacacs_server": {"page_title": "TACACS+伺服器", "tacacs": "TACACS+", "auth_type_asc_two": "ASCII"}, "syslog_server": {"page_title": "系統日誌", "syslog_server": "系統日誌伺服器", "auth_disable_hint": "無法啟用證書和密鑰，因為它們不存在。", "tls": "TLS", "common_name": "通用名稱", "expireTime": "到期時間", "key_limitation": "該設備的認證和密鑰集的最大數量是 {{ size }}.", "title_add_key": "添加證書和密鑰集", "title_edit_key": "編輯此證書和密鑰", "delete_key_desc": "您確定要刪除證書和密鑰嗎？", "client_certificate": "用戶端證書", "client_key": "用戶端密鑰", "ca_key": "CA 密鑰"}, "radius": {"page_title": "RADIUS 伺服器", "radius": "RADIUS", "server_address_number": "伺服器的IP位址 {{ number }}", "mschap": "MS-CHAPv1", "mschap_v2": "MS-CHAPv2"}, "config_bk_res": {"page_title": "設定備份和還原", "menu_title": "設定備份和還原", "file_encryption": "文件加密", "file_signature": "文件簽名", "config_name": "設定名稱", "config_file_encryption": "設定文件加密", "configuration_selection": "選擇設定類型", "running_configuration": "Running Configuration", "startup_configuration": "Startup Configuration", "default_configuration": "是否包含預設設定", "not_included": "Not Included", "included": "Included", "signed_config": "簽名設定", "sign_hint": "啟用後，當管理員備份或還原設定時會添加數字簽名。", "sign_disable_hint": "由於私鑰和公鑰為空，無法啟用此功能。", "private": "私人的", "certificate": "證書", "label": "標籤", "length": "長度", "key_limitation": "該設備的密鑰對的最大數量是 {{ size }}.", "title_add_key": "添加自定義密鑰", "title_edit_key": "編輯此自定義密鑰", "delete_key_desc": "您確定要刪除該密鑰對嗎？", "auto_bk_of_config": "自動備份設定", "auto_load_of_config": "自動還原設定", "auto_restore": "自動還原設定", "auto_restore_hint": "啟動期間自動從外部存儲設備還原設定。", "encrypt_whole_file": "加密整個檔", "encrypt_sensitive_information_only": "僅加密敏感資訊", "encrypt_hint": "如果選擇了'僅加密敏感資訊\"並且加密密鑰欄位留空,則將使用Moxa 加密密鑰。"}, "firmware_upgrade": {"page_title": "韌體升級"}, "module_information": {"page_title": "模組資訊", "module_name": "模組名稱", "no_module_msg": "未安裝模組"}, "event_notification": {"page_title": "事件通知", "group": "團體", "event_name": "活動名稱", "system_and_function": "系統和功能", "registered_event": "註冊活動", "registered_action": "註冊行動", "registered_port": "註冊連接埠", "group_general": "一般的", "group_switching": "交換", "group_poe": "供電", "group_routing": "路由", "group_tracking": "Tracking", "notification_loginSuccess": "Login success", "notification_loginFail": "<PERSON><PERSON> failed", "notification_loginLockout": "Login lockout", "notification_accountChanged": "Account settings changed", "notification_certificationChanged": "SSL certification changed", "notification_passwordChanged": "Password changed", "notification_coldStart": "Cold start", "notification_warmStart": "Warm start", "notification_configurationChanged": "Configuration changed", "notification_configurationImported": "Configuration imported", "notification_logCapacityThreshold": "Log capacity threshold", "notification_powerOff": "Power On->Off", "notification_powerOn": "Power Off->On", "notification_diOn": "DI on", "notification_diOff": "DI off", "notification_topologyChanged": "Topology changed", "notification_couplingChanged": "Coupling changed", "notification_masterChanged": "Master changed", "notification_masterMismatch": "Master mismatch", "notification_rstpTopologyChanged": "RSTP topology changed", "notification_rstpRootChanged": "RSTP root changed", "notification_rstpMigration": "RSTP migration", "notification_rstpInvalidBpdu": "RSTP invalid BPDU", "notification_rstpNewPortRole": "RSTP new port role", "notification_mstpTopologyChanged": "MSTP topology changed", "notification_mstpRootChanged": "MSTP root changed", "notification_mstpNewPortRole": "MSTP new port role", "notification_linkHealthyCheckFail": "Redundant port health check failed", "notification_dualHomingPathSwitched": "Dual homing path changed", "notification_dot1xAuthFail": "802.1X auth failed", "notification_lldpTableChanged": "LLDP table changed", "notification_rmonRaisingAlarm": "RMON raising alarm", "notification_rmonFallingAlarm": "RMON failing alarm", "notification_macsecInterfaceMKAFail": "MACsec MKA failed", "notification_dhcpsnpDynamicEntrySetFailed": "Binding Status dynamic entry failed", "notification_dhcpsnpUntrustMacDiscard": "DHCP client ingress discards packets due to the DHCP Snooping rule", "notification_dhcpsnpUntrustServerDiscard": "DHCP server discards packets due to the DHCP Snooping rule", "notification_multipleCouplingPathChanged": "Multiple coupling path changed", "notification_dhcpBootfileFail": "DHCP Bootfile failed", "notification_trackingStatusChanged": "Tracking Status Changed", "notification_trackingReactionPort": "Tracking Action Triggered on Port", "notification_trackingReactionStaticRoute": "Tracking Action Triggered on Static Route", "notification_trackingReactionVrrp": "Tracking Action Triggered on VRRP", "notification_pdPowerOn": "PD power on", "notification_pdPowerOff": "PD power off", "notification_lowInputVoltage": "Low input voltage", "notification_pdOverCurrent": "PD over-current", "notification_pdNoResponse": "PD no response", "notification_overPowerBudgetLimit": "Over power budget limit", "notification_powerDetectionFailure": "Power detection failure", "notification_nonPdOrPdShort": "Non-PD or PD short circuit", "notification_portOn": "Port On", "notification_portOff": "Port Off", "notification_rateLimitedOn": "Port shut down by Rate Limit", "notification_rateLimitedOff": "Port recovered by Rate Limit", "notification_psecViolationPortDown": "Port shut down by Port Security", "notification_fiberWarning": "Fiber Check warning", "notification_linkUp": "Interface up", "notification_linkDown": "Interface down", "notification_adjacencyChanged": "OSPF adjacency changed", "notification_drChanged": "OSPF DR changed", "notification_becomeDR": "OSPF become DR", "notification_vrrpMasterChanged": "VRRP virtual router master changed", "notification_pimSmDrChanged": "PIM-SM DR changed", "notification_pimSmRpAdded": "PIM-SM RP added by BSM", "notification_pimSmRpDeleted": "PIM-SM RP deleted by BSM", "notification_supABportTimediff": "A PHR Supervision frame time difference event occurred on ports A, B", "notification_gcTimeout": "GOOSE Check entry counter timeout", "notification_gcTimeoutClear": "GOOSE Check entry counter timeout clear", "notification_gcPortTampered": "GOOSE Check tampered ingress port", "notification_gcAddrTampered": "GOOSE Check tampered source MAC address", "notification_gcLockViolation": "GOOSE Check Lock violation", "notification_gcEntryReset": "GOOSE Check entry reset", "notification_gcEntryDelete": "GOOSE Check entry delete", "action_trap": "Trap", "information": "Information", "title_edit_event_notification": "編輯此事件通知"}, "relay_output": {"page_title": "繼電器警報", "relay_alarm_cut_off": "關閉繼電器警報", "relay_alarm_settings": "繼電器警報設置", "fault_led_display": "故障 LED 顯示", "cut_off": "截止"}, "statistics": {"page_title": "網路統計", "bandwidth_utilization": "頻寬使用情況", "packet_counter": "封包計數器", "rxTotalOctets": "Rx總八位字節", "collisionPackets": "衝突封包", "dropPackets": "丟棄的封包", "dropPacketsHint": "丟棄封包計數器的預設更新時間約為 5 秒，並根據設備上的連接埠數量增加。", "rxPausePackets": "接收暫停封包", "txTotalOctets": "Tx總八位字節", "txUnicastPackets": "發送單播封包", "crcAlignErrorPackets": "CRC 對齊錯誤封包", "txMulticastPackets": "發送組播封包", "rxBroadcastPackets": "接收廣播封包", "rxUnicastPackets": "接收單播封包", "jabberPackets": "Jabber 封包", "excessiveCollisionPackets": "衝突封包過多", "txTotalPackets": "發送總封包", "fragmentPackets": "分段封包", "rxTotalPackets": "接收總封包", "lateCollisionPackets": "延遲衝突封包", "oversizePackets": "過大的封包", "rxMulticastPackets": "接收組播封包", "txBroadcastPackets": "Tx 廣播封包", "undersizePackets": "封包過小", "txptpPackets": "發送 PTP 封包", "rxptpPackets": "接收 PTP 封包", "displayMode": "顯示模式", "packetCounter": "Packet Counter", "bandwidthUtilization": "Bandwidth Usage", "line_num_target_port": "線 {{ number }} 監控連接埠", "line_num_target_sniffer": "線 {{ number }} 嗅探器", "txandrx": "Tx/Rx", "txonly": "Tx", "rxonly": "Rx", "all_port": "所有連接埠", "all_ge_port": "所有 GE 連接埠", "all_fe_port": "所有 FE 連接埠", "line_num": "線{{ number }}", "clear_graph_desc": "您確定要清除所有圖表數據嗎？", "clear_table_desc": "您確定要清除所有表數據嗎？", "benchmark_line": "基準", "benchmark_line_time": "基準線-時間", "comparison_line": "比較", "comparison_line_time": "比較線-時間", "selecting_visible_columns": "編輯列表", "title_comparison": "比較數據", "title_reset_statistics_graph": "重置統計圖表", "title_edit_statistics_setting": "顯示設置", "title_clear_statistics_counter": "清空計數器"}, "mab": {"page_title": "MAC旁路認證", "page_title_abbr": "MAB", "auth_mode": "身份驗證模式", "local_database": "Local Database", "quiet_period": "安靜時期", "reauthentication": "重新驗證", "reauth_period": "<PERSON><PERSON><PERSON>時期", "size_limitation": "該設備的MAC位址項目的最大數量是 {{ size }}.", "retransmit": "重傳", "force_unauthorized": "Force Unauthorized", "force_Authorized": "Force Authorized", "reauth_port_confirm": "您確定要重新驗證連接埠嗎？", "authorized": "授權", "unauthorized": "未經授權", "title_reauth_port": "Re<PERSON><PERSON>連接埠", "port_setting": "連接埠 {{ portIndex }} 設置", "account_setting": "帳戶 {{ userName }} 設置", "timeout_retransmit_hint": "所有重試次數不能超過 Dot1x 伺服器逾時值。注意：  所有重試次數=超時*（重傳+1）。建議值：  {{ number }}", "clear_button_disabled_hint": "當項目數量達到最大容量時，清除通過 MAC 認證旁路收集的所有 MAC 位址項目。", "clear_warning_hint": "通過MAC認證繞過收集的MAC位址項目數量已達到最大容量。", "title_clear_mac_address": "清除MAC位址", "clear_mac_address_message": "您確定要清除通過 MAC 身份驗證繞過收集的所有 MAC 位址嗎？", "radius_hint": "802.1X和MAC旁路認證繞過共享同一個RADIUS伺服器。"}, "modbus_tcp": {"page_title": "Modbus TCP", "enable_modbus_tcpo_title": "啟用 Modbus TCP", "enable_modbus_tcp_warning": "您確定要啟用非安全協議 (Modbus TCP) 嗎？"}, "fiber_check": {"page_title": "Fiber Check", "threshold_settings": "閾值設置", "model_name": "型號名稱", "serial_number": "序列號碼", "wavelength": "波長", "voltage": "電壓", "temperature": "溫度", "tx_power": "發射功率", "rx_power": "接收功率", "temperature_limit": "溫度閾值", "tx_power_threshold_low": "發射功率閾值低", "tx_power_threshold_high": "發射功率閾值高", "rx_power_threshold_low": "接收功率閾值低", "rx_power_threshold_high": "接收功率閾值高", "temp_over_spec": "溫度超過閾值。", "tx_power_over_spec": "Tx 功率超過閾值。", "tx_power_under_spec": "Tx 功率低於閾值。", "rx_power_over_spec": "接收功率超過閾值。", "rx_power_under_spec": "接收功率低於閾值。", "port_copy_to_copper_port_disable": " 無法將設定複製到銅連接埠。", "warning": "警告", "reset_title": "重置連接埠 {{ portIndex }} 閾值設置", "reset_desc": "您確定要將此連接埠重置為自動模式（預設）並清除所有閾值設置嗎？", "reset_all_ports": "重置所有連接埠", "reset_all_ports_title": "重置所有連接埠閾值設置", "reset_all_ports_desc": "您確定將所有連接埠重置為自動模式（預設）並清除所有閾值設置嗎？"}, "dai": {"page_title": "動態ARP檢查", "port_is_la_member": "該連接埠是連接埠通道的成員。成員連接埠無法啟用動態ARP檢測。", "port_is_trusted": "此連接埠是 DHCP Snooping 的可信連接埠。只有不受信任的連接埠才能啟用動態 ARP 檢查。"}, "mac_sec": {"page_title": "媒體Access控制安全", "mka_status": "MKA 狀態", "participant_ckn": "參與者CKN", "participant_cak": "參與者CAK", "key_server": "密鑰伺服器", "peer_list_mi": "對等列表成員標識符（MI）", "peer_list_mn": "對等列表消息號碼（MN）", "sci": "安全通道 ID (SCI)", "peer_list_type": "對等列表類型", "live_peer_list": "實時同行列表", "potential_peer_list": "潛在同行名單", "peer_list_mi_hint": "MI：  MKA 消息包括發送者自己的成員 ID 以及已從其接收消息的其他潛在對等點的 ID。", "not_support_10G_port": "10G 連接埠不支援 MACsec。", "member_port_disable": "此埠是鏈路聚合的成員埠，因此MACsec無法在此埠上進行編輯。"}, "profinet": {"page_title": "PROFINET", "protocol_information": "協議資訊", "enable_profinet_title": "啟用 PROFINET", "enable_profinet_warning": "您確定要啟用非安全協議 (PROFINET) 嗎？", "vlan_not_exist": "VLAN不存在。", "label_format_hint": "這是標籤的格式。每個標籤應該用\".\"分隔。標籤的最大字符數為 63。"}, "ethernet_ip": {"page_title": "EtherNet/IP", "enable_eip_title": "啟用EtherNet/IP", "enable_eip_warning": "您確定要啟用非安全協議 (EtherNet/IP) 嗎？"}, "multi_coupling": {"page_title": "Multiple Network Coupling", "main_ring_protocol": "主環協議", "switch_role": "Coupling 交換機作用", "group_id": "Coupling 組ID", "polling_interval": "Coupling 輪詢週期", "polling_interval_hint": "對於主環，使用 80ms 輪詢週期時，路徑 ID 的最大數量為 16。當使用 40ms 輪詢週期時，我們建議使用 8 個路徑 ID。", "table_settings": "Coupling 項目設定", "table_status": "Coupling 項目狀態", "path_id": "路徑ID", "coupling_port": "Coupling 連接埠", "coupling_port_state": "Coupling 連接埠狀態", "partner_mac": "夥伴MAC", "connect_status": "連接狀態", "error_status": "錯誤狀態", "multiple_active": "多個運行中交換機", "multiple_backup": "多個備份交換機", "role_information": "角色資訊", "edit_path_title": "編輯路徑ID {{ pathId }} 設置", "multi_coupling_enable_hint": "必須啟用主環協議 Turbo Ring V2 或 MRP。", "is_coupling_port": "這已被選為Coupling連接埠。", "is_selected_path": "該路徑 ID 已被選擇。", "error_status_message": "已檢測到多個主動/備用交換機。請檢查此設備和合作夥伴設備，以確保沒有重複。"}, "pim_dm": {"page_title": "PIM-DM", "state_refresh": "狀態刷新", "state_refresh_interval": "狀態刷新週期", "pim_dm_hint": "當PIM-DM啟用時，如果有IGMP請求，可以與IGMP Snooping一起使用。", "state_refresh_hint": "PIM-DM 模式網路中的所有路由器必須啟用狀態刷新並設定相同的時間週期。"}, "pim_sm": {"page_title": "PIM-SM", "pim_sm_settings": "PIM-SM 設置", "pim_sm_hint": "啟用 PIM-SM 時，請確保已經根據您的需求啟用 IGMP 偵聽和相關設定。", "spt_method": "最短路徑樹切換方法", "join_prune_interval": "加入/修剪週期", "dr_priority": "DR 優先順序", "bsr": "BSR", "bsr_candidate": "BSR 候選", "bsr_address": "BSR 地址", "bsr_priority": "BSR 優先順序", "bsr_hash_mask_length": "BSR 雜湊遮罩長度", "dr_priority_hint": "在 DR 選舉中，將優先選擇順序值較大的 DR。", "bsr_priority_hint": "在 BSR 選舉中，將優先選擇優先順序值較大的 BSR。", "rp": "RP", "static_rp": "靜態 RP", "candidate_rp": "候選 RP", "group_address": "組地址", "group_mask": "組遮罩", "rp_address": "RP 地址", "override": "覆蓋", "interface_not_created": "尚未創建 PIM-SM 介面。", "rp_if_name": "RP 介面名稱", "rp_priority": "RP 優先順序", "static_rp_size_limitation": "此設備的最大靜態 RP 數量為 {{ size }}。", "candidate_rp_size_limitation": "此設備的最大候選 RP 數量為 {{ size }}。", "ssm": "SSM", "pim_ssm": "PIM-SSM", "pim_ssm_size_limitation": "此設備的最大 PIM-SSM 數量為 {{ size }}。", "title_create_static_rp": "創建靜態 RP", "title_edit_static_rp": "編輯靜態 RP", "title_delete_static_rp": "刪除靜態 RP", "delete_static_rp_desc": "您確定要刪除所選的靜態 RP 嗎？", "override_hint": "覆蓋代表當衝突發生時，在動態學習的BSR和靜態 RP中將優先選擇靜態 RP。", "title_create_candidate_rp": "創建候選 RP", "title_edit_candidate_rp": "編輯候選 RP", "title_delete_candidate_rp": "刪除候選 RP", "delete_candidate_rp_desc": "您確定要刪除選定的候選 RP 嗎？", "rp_priority_hint": "在 RP 選舉中，優先值較小的 RP 優先。", "rp_if_name_address": "RP 介面名稱 (RP 位址)", "title_add_ssm_range": "添加 SSM 範圍", "title_edit_ssm_range": "編輯 SSM 範圍", "title_delete_ssm": "刪除 SSM", "delete_ssm_desc": "您確定要刪除選定的 SSM 嗎？", "static_ssm_entry_hint": "根據RFC 7761，此範圍保留給SSM使用。", "pim_sm_status": "PIM-SM 狀態", "dr_address": "DR 地址", "bsr_rp": "BSR / RP", "elected_bsr": "選舉的 BSR", "rp_mapping": "RP 映射", "rp_mapping_result": "RP 映射結果"}, "multicast_routing_table": {"page_title": "組播路由表", "multicast_group": "組播組", "upstream_neighbor": "上游鄰居", "incoming_interface": "傳入介面", "outgoing_interface": "傳出介面", "prune": "修剪", "assert": "主張"}, "prp_hsr": {"page_title": "PRP/HSR", "prp_hsr_protocol": "PRP/HSR 協議", "entry_forget_time": "項目忘記時間", "net_id": "Net ID", "lan_id": "LAN ID", "prp": "PRP", "hsr": "HSR", "coupling": "Coupling", "enable_prp_hsr_title": "啟用 PRP/HSR", "enable_prp_hsr_warning": "您確定要啟用 PRP/HSR 嗎? 此舉會將PRP/HSR模組上所有埠的設定重置為預設值。", "no_phr_module_warning": "未偵測到有效的 PHR 模組。請檢查 PHR 模組。"}, "tracking": {"page_title": "Tracking", "tracking_list_of_interface": "Tracking List of Interface", "tracking_list_of_ping": "Tracking List of Ping", "tracking_list_of_logical": "Tracking List of Logical", "tracking_list_of_all": "Tracking List of All", "tid": "Tracking ID", "down_to_up": "狀態恢復變更", "up_delay": "延遲恢復", "up_to_down": "狀態中斷變更", "down_delay": "中斷延遲", "received": "已接收", "lost": "丟失", "and": "AND", "or": "OR", "nand": "NAND", "nor": "NOR", "entry_state": "狀態", "interface_tracking": "Interface Tracking", "ping_tracking": "Ping Tracking", "logical_tracking": "Logical Tracking", "create_interface_tracking_entry_title": "創建 Interface Tracking 項目", "edit_interface_tracking_entry_title": "編輯此 Interface Tracking 項目", "create_ping_tracking_entry_title": "創建 Ping Tracking 項目", "edit_ping_tracking_entry_title": "編輯此 Ping Tracking 項目", "create_logical_tracking_entry_title": "創建 Logical Tracking 項目", "edit_logical_tracking_entry_title": "編輯此 Logical Tracking 項目", "interface_type": "介面類別", "port": "Port", "ping": "<PERSON>", "logical": "Logical", "interface": "Interface", "network_interface": "Network Interface", "status_change_from_down_to_up": "狀態更改（狀態恢復）", "status_change_from_up_to_down": "狀態更改（狀態中斷)", "logical_list": "邏輯清單", "logical_oper": "邏輯運算子", "interfaec_ip_logic": "介面/IP 位址/邏輯清單", "time_since_last_change": "自上次更改以來的時間", "no_of_change": "更改次數", "only_select_four": "最多可選擇四個", "require_tid_larger": "Tracking ID 必須大於邏輯清單的 TID。", "require_at_least_two_tid": "需要兩個以上的 TID", "duplicated_tid": "重複的Tracking ID", "tracking_size_limitation": "此設備的最大Tracking項目數為 {{ size }}。", "sync_to_latest_status": "同步到最新狀態"}, "auto_config": {"page_title": "自動設定", "cdu_port": "控制單元埠", "auto_config_info": "自動設定資訊", "import_mode_hint": "它要求啟用 DHCP 用戶端和 LLDP,並預設定 DHCP 用戶端啟動檔和用戶端 ID。在此模式下,自動設定僅通過控制單元埠發送選項 61 數據包。", "propagate_mode_hint": "它要求將IP設定設置為手動並啟用LLDP。在此模式下,DHCP 伺服器根據 LLDP 資訊分配 IP 位址。"}, "multi_local_route": {"page_title": "組播本地路由", "routes": "路由", "macl": "MACL", "vrrp_master_only": "僅在VRRP Master作用", "multi_local_route_hint": "如果啟用了組播本地路由，則還必須啟用 IGMP 偵聽。", "vrrp_master_only_hint": "如果開啟，交換機只能作為VRRP Master時路由組播流。", "source_vlan": "來源 VLAN", "downstream_vlan": "下游 VLAN", "multi_local_route_size_limitation": "組播本地路由項目的最大數量為 {{ size }}。", "create_route_msg": "創建組播本地路由", "edit_route_msg": "編輯來源 VLAN {{ vid }}", "macl_id": "MACL ID", "title_create_macl_rule": "創建組播 ACL", "title_edit_macl_rule": "編輯 MACL ID {{ maclId }}", "only_select_sixteen": "最多可選擇十六個", "delete_session_title": "刪除組播本地路由", "delete_session_content": "您確定要刪除所選的組播本地路由嗎？", "source_vlan_cannot_set": "無法設置來源 VLAN。"}, "supervision_frame": {"page_title": "監控幀", "supervision_frame_enable_hint": "必須先啟用 PRP/HSR 協議，然後才能啟用監控幀。", "life_check_interval": "生命檢查週期", "destination_address": "目標位址", "forward_to_interlink": "監管轉發到互連", "nodes_table": "節點表", "forget_time": "節點遺忘時間", "node_type": "節點類型", "time_last_seen_a": "上次看到的時間 A", "time_last_seen_b": "上次看到的時間 B"}, "goose_check": {"page_title": "GOOSE Check", "goose_lock": "GOOSE Lock", "goose_lock_hint": "開啟GOOSE Lock後，不在監控表中的GOOSE封包將會被丟棄。", "tamper_response": "竄改回應", "tamper_response_hint": "選擇「丟棄」時，將會丟棄任何被竄改的封包。選擇「關閉此埠」則會將收到竄改GOOSE封包的埠關閉。", "port_disable": "Port Disable", "app_id": "APP ID", "goose_address": "GOOSE 地址 (DA)", "monitoring_table_status": "監控表狀態", "goose_lock_status": "GOOSE Lock狀態", "lock_violation_status": "Lock Violation狀態", "goose_name": "GoCB名稱", "rx_counter": "Rx計數器", "create_goose": "創建靜態GOOSE項目", "port_tampered": "竄改埠", "sa_tampered": "竄改SA", "duplicate_goose": "已經存在相同的靜態GOOSE項目", "exist_dynamic_entry": "已經存在相同的動態學習GOOSE項目。點擊「應用」將此項目轉為靜態項目。", "lock_violation_normal_hint": "所有偵測到的GOOSE封包都已經顯示在監控表中。", "lock_violation_warning_hint": "非預期且不在監控表中的GOOSE封包被偵測到。", "goose_table_max": "監控表的最大 {{ size }}", "size_limitation": "此設備最大的GOOSE Check項目數量為{ size }}。"}}, "request_handler": {"action_saving": "保存……", "action_loading": "正在加載...", "action_upgrading": "正在升級……", "action_ping": "Pinging ..."}, "response_handler": {"res_server_error": "伺服器連接錯誤。", "res_global_success": "成功更新。", "res_complete_refresh": "刷新完成。", "res_complete_encrypt_data": "加密數據完成。", "res_port_success": "已成功更新連接埠設置。", "res_entry_create_success": "成功創建項目。", "res_entry_update_success": "已成功更新項目。", "res_entry_delete_success": "刪除項目成功。", "res_port_enable_success": "連接埠 {{ portIndex }} 已成功啟用。", "res_port_disable_success": "連接埠 {{ portIndex }} 已成功禁用。", "res_dscp_success": "成功更新 DSCP 設置。", "res_cos_success": "已成功更新 CoS 設置。", "res_regen_ssh_success": "成功重新生成 SSH 密鑰。", "res_regen_ssl_success": "成功重新生成 SSL 證書。", "export_ssl_cert": "成功匯出SSL證書。", "import_ssl_cert": "SSL證書匯入成功。", "import_config": "設定匯入成功。", "res_copy": "已復制。", "res_ping_success": "<PERSON>結束了。", "res_auto_save_mode_success": "成功更新自動保存模式。", "res_switch_browse_mode_success": "模式切換成功。", "res_v3_account_update_success": "認證帳號更新成功。", "res_host_update_success": "已成功更新 SNMP 主機。", "res_upgrading_firmware_success": "韌體升級成功。設備現在將重新啟動。", "res_event_notification_success": "已成功更新事件通知。", "res_save_to_startup_success": "成功將運行設定保存到啟動設定中。", "clear_success": "清除成功。", "res_factory_default_success": "成功重置為出廠預設設置。", "backup_success": "備份成功。", "res_custom_default_success": "已成功重置為自定義預設值。", "download_success": "下載成功。", "locator": "成功觸發設備定位器。", "re_auth_port_success": "已成功重新驗證連接埠。", "res_recovery_port_success": "連接埠恢復成功。"}, "error_handler": {"error_session_expired_dialog": "本次連線階段已經結束。系統將返回登入頁面。"}, "validators": {"required": "必需", "require_min_length": "最低限度 {{ number }} 字元", "require_range_between": "{{ rangeBegin }} - {{ rangeEnd }}", "require_range_single_between": "{{ singleNum }}, {{ rangeBegin }} - {{ rangeEnd }}", "require_hexadecimal": "僅限十六進制數字", "require_unicast": "僅單播IP", "required_at_least_two": "至少2個輸出連接埠", "required_at_least_one_overwrite": "至少一項覆蓋項目", "required_priority_multiple": "優先級必須是 {{ num }}", "required_label_max_length": "標籤的最大字符數為 {{ number }}", "required_interval_multiple": "週期必須是 {{ num }} 的倍數", "required_timeout_multiple": "逾時週期必須是 {{ num }} 的倍數", "invalid": "無效的", "invalid_format": "無效的格式", "invalid_positive_integer": "無效的正整數", "invalid_hex": "無效的十六進制數", "invalid_range": "有效範圍是從 {{ rangeBegin }} 到 {{ rangeEnd }}", "invalid_single_range": "範圍是 {{ singleNum }} 或者 {{ rangeBegin }} 到 {{ rangeEnd }}", "invalid_mac_address": "無效的 MAC 位址", "invalid_ip_address": "IP 位址無效", "invalid_area_id": "無效的區域 ID", "invalid_router_id": "無效的路由器 ID", "invalid_vlan_port": "無效的VLAN成員連接埠", "invalid_vlan_output_port": "輸出連接埠的 VLAN 無效", "invalid_netmask": "無效的遮罩", "invalid_char": "僅允許 a-z、A-Z、0-9", "invalid_email": "不合規電郵", "invalid_regex_level_1": "僅 a-z、A-Z、0-9 或 . - _ 被允許", "invalid_regex_level_2": "僅 a-z、A-Z、0-9 或 . , - _ + = | : ; @ ! ~ # % ^ * ( ) [ ] { } 是允許的", "invalid_sys_desc": "只允許輸入 a-z, A-Z, 0-9 or ~ ! @ # $ % ^ & * ( ) { } [ ] < > _ + - = \\ : ; , . /", "invalid_login_failure_message": "只允許輸入 a-z, A-Z, 0-9 or ! # $ % & ' ( ) * + , \\ - . / : ; < = > @ [ ] ^ _ ` { | } ~ ", "invalid_regex_macsec_cak_and_ckn": "僅限 a-z、A-Z、0-9 或 @ % $ ^ * ' ` ( ) _ + = { } : . , ~ [ ] - 允許。", "invalid_char_and_dash": "僅允許使用 a-z、A-Z、0-9 或 -", "invalid_char_and_dot_dash": "僅允許使用 a-z、A-Z、0-9 或 . -", "invalid_lowercase_and_dash_dot": "僅允許使用 az、0-9 或 . -", "invalid_file_name": "僅允許使用 a-z、A-Z、0-9 或 / ( ) . - _", "duplicate_ip": "重複IP", "duplicate_ip_range": "IP範圍重複", "duplicate_vrid": "重複的 VRID", "duplicate_stream": "該流已經存在", "duplicate_id": "ID重複", "duplicate_input_ports": "重複的輸入連接埠", "duplicate_loopback_id": "此環回 ID 已被使用", "duplicate_vlan_id": "此 VLAN ID 已被使用。", "duplicate_vlan_and_mac": "此 VLAN ID 和 MAC 位址組合已存在", "duplicate_group_address_and_netmask": "此組位址和組網路遮罩組合已存在", "two_deciaml_palce": "最多 2 位小數", "duplicate_ip_and_netmask": "此 IP 位址和子網路遮罩組合已存在", "three_deciaml_palce": "最多 3 位小數", "same_as_ingress_stream": "與入口流相同"}}