{"__readme__": {"__1__": "All key naming is snake_case, in addition to using uri key, e.g.'ospfRRDStatic'", "__2__": "In 'common', words are used repeatedly. It can be reclassified and named 'common_xxx', e.g.'common_port', 'common_account'...", "__3__": "In 'dialog', words are used repeatedly in the dialog.", "__4__": "In 'button', put all button words in and must be capitalized"}, "login": {"title_login_records": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "redirected_message": "<PERSON><PERSON><PERSON><PERSON><PERSON> erfo<PERSON>g<PERSON>ich", "login_success_records_greeting": "Willkommen {{ name }}", "login_success_records_datetime": "Der letzte erfolgreiche Login-Zeitpunkt war {{ time }}.", "login_fail_records": "Die letzte(n) Anmeldefehler Aufzeichnung(en).", "modify_password_notification": "Bitte ändern Sie den Standard-Benutzernamen und das Passwort, um die Sicherheit zu erhöhen", "password_expire_notification": "Ihr Passwort ist abgelaufen. Bitte ändern Sie Ihr Passwort..", "daylight_saving_upgrade": "Die Sommerzeitfunktion wurde aktualisiert.", "daylight_saving_notification": "Bitte aktualisieren Sie Ihre Konfigurationen.", "factory_default_note": "<PERSON><PERSON>, dass Sie die Standard-Netzwerkeinstellungen verwenden müssen, um eine Webbrowser-Verbindung mit Ihrem <PERSON> wiederherzustellen.", "firmware_upgrade_note": "<PERSON><PERSON>, dass Si<PERSON> eine Webbrowser-Verbindung mit <PERSON><PERSON>em S<PERSON> wiederherstellen müssen."}, "general": {"top_nav": {"config_change": {"start_up_save_tip": "Die Konfigurationen wurden nicht in den Startkonfigurationen gespeichert.", "confirmation_message": "<PERSON>d <PERSON><PERSON> sic<PERSON>, dass Sie laufende Konfigurationen auf Startkonfigurationen anwenden möchten?", "title_apply_to_start_config": "<PERSON>f Startkonfigu<PERSON>en anwenden"}, "user_profile": {"greeting": "<PERSON><PERSON> {{ username }}", "enable_auto_save": "Automatisches Speichern aktivieren", "disable_auto_save": "Automatisches Speichern deaktivieren", "disable_auto_save_hint": "<PERSON><PERSON> anwenden werden die Konfigurationen als laufende Konfiguration und nicht als Startkonfiguration gespeichert.", "change_language": "Sprache ändern", "change_mode": "<PERSON><PERSON>", "locator": "Lokalisieren", "reset_factory_default": "Auf Standardeinstellungen zurücksetzen", "save_custom_default": "Benutzerdefinierte Standardeinstellung speichern", "standard_mode": "Standard", "advanced_mode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "standard_mode_tooltip": "Standardmodus: Zur Vereinfachung werden einige der Funktionen/Parameter ausgeblendet.", "advanced_mode_tooltip": "Erweiterter Modus: Die erweiterten Funktionen/Parameter stehen Benutzern zur Verfügung, die diese Einstellungen anpassen möchten."}, "auto_save_mode": {"enable_auto_save_title": "Automatischen Speichermodus aktivieren", "enable_auto_save_msg": "Sind <PERSON> sic<PERSON>, dass Sie den automatischen Speichermodus aktivieren möchten?", "disable_auto_save_title": "Automatischen Speichermodus deaktivieren", "disable_auto_save_msg": "Sind <PERSON> sic<PERSON>, dass Sie den automatischen Speichermodus deaktivieren möchten?"}, "advanced_browse_mode": {"advanced_notification_title": "In den erweiterten Modus wechseln", "advanced_notification_msg": "Sind <PERSON> sic<PERSON>, dass Sie vom Standardmodus in den erweiterten Modus wechseln möchten?", "basic_notification_title": "In den Standardmodus wechseln", "basic_notification_msg": "Sind <PERSON> sicher, dass Si<PERSON> vom erweiterten Modus in den Standardmodus wechseln möchten?"}, "locator": {"title_switch_locator": "Switch lokalisieren", "duration": "<PERSON><PERSON>", "hint": "Lassen Sie die LEDs am Gerät blinken, damit es leichter zu finden ist."}, "restart_machine": {"confirmation_title": "Neustart", "confirmation_msg": "Sind <PERSON> sic<PERSON>, dass Sie das Gerät neu starten wollen?"}, "factory_default": {"confirmation_title": "Werkseinstellung", "confirmation_msg": "Sind <PERSON> sic<PERSON>, dass Sie die Systemkonfigurationen auf die Werkseinstellungen zurücksetzen möchten?", "factory_default_hint": "Durch das Zurücksetzen auf die Werkseinstellungen werden die benutzerdefinierten Standardkonfigurationen gelöscht.", "custom_default": "Benutzerdefinierte Standardeinstellung", "confirmation_msg_custom_default": "Sind <PERSON> sicher, dass Sie die Systemkonfigurationen auf die benutzerdefinierte Standardeinstellung zurücksetzen möchten?", "custom_default_not_exist": "Die benutzerdefinierte Standardeinstellung kann NICHT ausgeführt werden, wenn ihr Status \"Keine Konfiguration gefunden!\" ist.", "saved_config_name": "Name der gespeicherten Konfiguration", "config_name_hint": "Der benutzerdefinierte Standardkonfigurationsname wird im nichtflüchtigen Speicher gespeichert.", "clear_all_config": "Konfigurationen, Protokolldateien und Anmeldeschlüssel löschen"}, "save_custom_default": {"confirmation_msg": "Sind <PERSON> sic<PERSON>, dass Sie die Startkonfiguration als benutzerdefinierte Standardeinstellung speichern möchten?", "current_config_name": "Name der aktuellen Konfiguration", "config_name_hint": "Der Konfigurationsname kann auf der Seite \"Config <PERSON> and <PERSON><PERSON>\" geändert werden."}, "logout": {"confirmation_title": "Abmelden", "confirmation_msg": "Sind Sie sicher, dass Sie sich abmelden wollen?"}}, "page_state": {"page_not_found": "Seite nicht gefunden", "page_not_found_desc": "Die angeforderte URL wurde auf diesem Server nicht gefunden.", "back_link": "Zurück zur Startseite"}, "menu_tree": {"jump_page_placeholder": "<PERSON><PERSON> suchen", "system": "System", "system_management": "Systemverwaltung", "account_management": "Kontoverwaltung", "provisioning": "Provisionierung", "port_interface": "Port-Schnittstelle", "l2_switching": "Layer-2-Switching", "unicast_route": "Unicast-Route", "multicast_route": "Multicast-Route", "mac": "MAC", "qos": "QoS", "redundancy": "Redundanz", "l2_redundancy": "Layer-2-<PERSON><PERSON><PERSON>", "l3_redundancy": "Layer-3-<PERSON><PERSON><PERSON>", "network_service": "Netzwerkdienst", "routing": "Routing", "network_management": "Netzwerkverwaltung", "device_security": "Gerätesicherheit", "network_security": "Netzwerksicherheit", "diagnostics": "Diagnose", "network_status": "Netzwerkstatus", "tools": "Werkzeuge", "log_and_event_notification": "Ereignisprotokolle und Benachrichtigungen", "application": "Indust<PERSON><PERSON>", "iec61850": "IEC 61850", "iec62439_3": "IEC 62439-3"}, "dialog": {"title_refresh_browser": "Browser aktualisieren", "title_change_default_password": "Standard-Passwort ändern", "title_change_password": "Passwort ändern", "title_session_expired": "Sitzung abgelaufen", "title_notification": "Benachrichtigung", "title_edit_interface": "Schnittstelle {{ interfaceName }} bearbeiten", "edit_port_msg": "Port {{ portIndex }} Einstellungen bearbeiten ", "edit_vlan_msg": "VLAN {{ vlanIndex }} Einstellungen bearbeiten ", "create_entry_msg": "Eintrag erstellen", "edit_entry_msg": "Eintragseinstellungen bearbeiten", "delete_entry_msg": "Eintrag löschen", "title_delete_key": "Zertifikat und Schlüssel löschen", "title_delete_account": "Konto löschen", "delete_entry_confirm_desc": "Sind <PERSON> sicher, dass Si<PERSON> den ausgewählten Eintrag löschen möchten?", "title_select_file": "<PERSON>i ausw<PERSON>hlen", "title_device_unplugged": "{{ device }} ist nicht angeschlossen", "desc_device_unplugged": "<PERSON>te prüfen Si<PERSON>, ob das {{ device }} angeschlossen ist.", "redirect_to_ip": "<PERSON><PERSON><PERSON><PERSON><PERSON> zu {{ ipAddress }}", "page_redirect_content": " Die Seite wird in 5 Sekunden weitergeleitet.", "redirect_failed_hint": "Wenn die Weiterleitung fehlschlägt, überprüfen Sie bitte Ihre Netzwerkeinstellungen.", "after_seconds": "Nach {{ second }} Se<PERSON><PERSON>", "redirecting": "Weiterleitung...", "title_choose_tracking_id": "Wählen Sie eine Tracking-IDn"}, "common": {"network": "Netzwerk", "enable_header": "Enable", "enabled": "Aktiviert", "disable_header": "Disable", "disabled": "Deaktiviert", "none": "None", "authentication": "Authentifizierung", "active": "Active", "inactive": "Inaktiv", "passive": "Passive", "ip": "IP", "ip_address": "IP-Adresse", "mac": "MAC", "mac_address": "MAC-Adresse", "server_address": "Server IP Adresse", "subnet_mask": "Subnetzmaske", "domain_name": "Domänenname", "ip_or_domain": "IP-Adresse/Domänenname", "general": "Allgemein", "normal": "Normal", "type": "<PERSON><PERSON>", "mode": "Modus", "yes": "<PERSON>a", "no": "<PERSON><PERSON>", "auto": "Auto", "user_defined": "User Defined", "valid": "<PERSON><PERSON><PERSON><PERSON>", "invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "Version", "unknown": "Unbekannt", "read_only": "Read Only", "refresh": "Aktualisieren", "reset": "Z<PERSON>ücksetzen", "add": "Hinzufügen", "delete": "Löschen", "export": "Exportieren", "up": "Hoch", "down": "<PERSON>ter", "index": "Index", "name": "Name", "method": "<PERSON>e", "file_name": "Dateiname", "file_server": "Dateiserver", "select_file": "<PERSON>i ausw<PERSON>hlen", "action": "Aktion", "authority": "Beh<PERSON><PERSON>", "any": "Any", "all": "Alle", "unselect": "Unselect", "settings": "Einstellungen", "status": "Status", "local": "Local", "usb": "USB", "usb_hint": "Der USB-Anschluss ist mit dem automatischen Backup-Konfigurator ABC-02 kompatibel.", "usb_hw_disabled_hint": "Der USB-Speicher ist ungültig, da die externe Speicherfunktion deaktiviert ist", "micro_sd": "microSD", "micro_sd_hw_disabled_hint": "Die microSD-Karte ist ungültig, da die externe Speicherfunktion deaktiviert ist", "location": "<PERSON><PERSON>", "time": "Zeit", "start_date": "Startdatum", "start_time": "Startzeit", "end_time": "Endzeit", "timeout": "Zeitüberschreitung", "interface": "Schnittstelle", "threshold": "Schwellenwert", "broadcast": "Broadcast", "multicast": "Multicast", "algorithm": "<PERSON><PERSON><PERSON><PERSON>", "manual": "Manual", "master": "Master", "priority": "Priorität", "permanent": "<PERSON><PERSON><PERSON>", "queue": "Warteschlange", "netmask": "Netzmaske", "backup": "Sicherung", "backup_en": "Backup", "restroe": "Wiederherstellen", "broken": "Fehlerhaft", "learning": "Learning", "listening": "Listening", "discarding": "Discarding", "forwarding": "Forwarding", "blocking": "Blocking", "packets": "<PERSON><PERSON>", "notice": "Notice", "warning": "Warning", "critical": "Critical", "error": "Error", "security": "Sicherheit", "slave": "Sklave", "slot": "Steckplatz", "simple": "Simple", "state": "Zustand", "subtype": "Untertyp", "protocol": "Protokoll", "init": "Init", "retry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "severity": "Schweregrad", "destination": "Zielort", "description": "Beschreibung", "distance": "Distanz", "expired_date": "Verfallsdatum", "delay_time": "Verzögerungszeit", "serial_number": "Seriennummer", "product_revision": "Produktrevision", "quick_setting": "Schnelleinstellungen", "admin_status": "Admin-Status", "link_status": "Link-Status", "system_status": "Systemstatus", "link_up": "Link Up", "link_down": "Link Down", "point_to_point": "Point-to-point", "ethertype": "EtherType", "auth_type": "Authentifizierungstyp", "authentication_type": "Authentifizierungsart", "default_gateway": "Standard-Gateway", "encryprion_key": "Verschlüsselungsschlüssel", "encryption_method": "Verschlüsselungsmethode", "authentication_password": "Authentifizierungs-Passwort", "server_ip_address": "Server IP Adresse", "key": "Schlüssel", "key_id": "Schlüssel-ID", "vlan_id": "VLAN-ID", "vlan_vid": "VLAN {{ vid }}", "vlan_list_info": "<PERSON><PERSON> können mehrere VLANs eingestellt werden und sollten als einzelne Zahl oder als Bereich eingegeben werden, z. B. 2, 4-8, 10-13.", "share_key": "Freigabeschlüssel", "share_key_hint": "Nach dem Verlassen dieser Seite oder dem Aktualisieren wird der Freigabeschlüssel automatisch gelöscht, um die Sicherheit zu erhöhen.", "auto_backup": "Automatisch sichern", "auto_backup_hint": "Auf externen Speicher sichern, wenn sich Konfigurationen ändern.", "dynamic": "Dynamisch", "static": "Statisch", "ref_manual_hint": "<PERSON><PERSON> zu dieser Einstellung finden Sie im Benutzerhandbuch.", "default": "<PERSON><PERSON><PERSON>", "other": "Other", "trusted": "Trusted", "untrusted": "Untrusted", "infinite": "<PERSON><PERSON><PERSON>", "source": "Source", "low": "<PERSON><PERSON><PERSON>", "high": "Hoch", "connected": "Verbunden", "disconnected": "Getrennt", "incorrect_connected": "Falsch angeschlossen", "set_event_notifications": "Ereignisbenachrichtigungen festlegen", "include": "Einschließen", "exclude": "Ausschließen", "immediate": "Immediate", "never": "Never", "interface_name": "Schnittstellenname", "interface_alias": "Schnittstellenalias", "hello_interval": "Hello Interval", "neighbor": "<PERSON><PERSON><PERSON>", "current": "Aktuell", "ping": "<PERSON>", "logical": "<PERSON><PERSON><PERSON>", "interval": "Intervall", "route": "Route", "open": "Open", "short": "Short", "role": "Role", "speed": "Geschwindigkeit", "failed": "<PERSON>cht bestanden", "successful": "Erfolgreich", "idle": "<PERSON><PERSON><PERSON><PERSON>", "protection": "<PERSON><PERSON><PERSON>", "pending": "<PERSON><PERSON><PERSON><PERSON>", "supported": "Unterstützt", "not_supported": "<PERSON>cht unterstützt", "import": "Importieren", "propagate": "Vermehren", "ascii": "ASCII", "hex": "HEX", "zone": "Zone"}, "common_account": {"account": "Ko<PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON>", "pwd_mask": "********", "password": "Passwort", "confirm_password": "Passwort bestätigen", "email": "Email", "delete_account_desc": "Sind <PERSON> sic<PERSON>, dass Sie das ausgewählte Konto löschen möchten?"}, "common_abbrev": {"md5": "MD5", "sha": "SHA", "sha-224": "SHA-224", "sha-256": "SHA-256", "sha-384": "SHA-384", "sha-512": "SHA-512", "sftp": "SFTP", "tftp": "TFTP", "pap": "BREI", "chap": "KERL", "cos": "CoS", "dscp": "DSCP", "cir": "CIR", "cir_full": "Committed Information Rate", "cbs": "CBS", "cbs_full": "Committed <PERSON><PERSON><PERSON> Size"}, "common_port": {"port": "Port", "ports": "Ports", "port_name": "Port {{ portName }}", "tcp_port": "TCP-Port", "udp_port": "UDP Port", "port_state": "Port Zustand", "port_status": "Port Status", "port_settings": "Port-Einstellungen", "port_shutdown": "Port Shutdown", "destination_port": "Ziel-Port", "reflect_port": "Reflect Port", "all_port": "All Ports", "member_port": "Member Port", "also_apply_port": "Konfigurationen auf Ports kopieren", "also_apply_port_hint": "Kopieren Sie die Konfigurationen auf die Ports, die Sie im Dropdown-Feld auswählen.", "ingress_port": "Ingress Port"}, "button": {"cancel": "ABBRECHEN", "apply": "ANWENDEN", "create": "ERSTELLEN", "edit": "BEARBEITEN", "delete": "LÖSCHEN", "sync_from_browser": "VOM BROWSER SYNCHRONISIEREN", "regenerate": "REGENERIEREN", "import": "IMPORTIEREN", "export": "EXPORTIEREN", "expand": "EXPAND", "collapse": "COLLAPSE", "copy": "KOPIEREN", "close": "SCHLIESSEN", "download": "HERUNTERLADEN", "ping": "PING", "clear": "LOESCHEN", "backup": "SICHERN", "restore": "WIEDERHERSTELLEN", "upgrade": "AKTUALISIEREN", "reset": "ZURÜCKSETZEN", "locate": "LOKALISIEREN", "disable": "DEAKTIVIEREN", "enable": "AKTIVIERN", "change": "ÄNDERN", "logout": "AUSLOGGEN", "reboot": "Neustart", "confirm": "BESTÄTIGEN", "remove": "ENTFERNEN", "back": "ZURÜCK", "log_in": "ANMELDEN", "re_auth": "RE-AUTH", "recovery": "Wiederherstellung", "export_cid_file": "CID-DATEI EXPORTIEREN", "export_server_ca": "SERVER-CA EXPORTIEREN", "change_password": "PASSWORT ÄNDERN", "export_server_cert": "SERVERZERTIFIKAT EXPORTIEREN", "view_all_event_logs": "ALLE EREIGNISPROTOKOLLE ANZEIGEN", "update_daylight_saving": "SOMMERZEIT AKTUALISIEREN", "select": "WÄHLEN", "retry": "WIEDERHOLEN", "encrypt": "VERSCHLÜSSELN", "change_cak": "CKN UND CAK ÄNDERN", "save": "SPEICHERN", "find_rp": "RP FINDEN", "cut_off": "ABGESCHNITTEN", "go_to": "GEHE ZU", "got_it": "VERSTANDEN"}, "tooltip": {"edit": "<PERSON><PERSON><PERSON>", "delete": "Löschen", "copy": "<PERSON><PERSON><PERSON>", "copy_config": "Konfigurationen kopieren", "close": "Schließen", "clear": "Löschen", "clear_graph": "Grafik löschen", "remove": "Entfernen", "reorder": "<PERSON><PERSON> sortieren", "recovery": "Wiederherstellung", "select_tracking": "Tracking ID auswählen", "remove_tracking": "Tracking ID entfernen", "vlan_size_limitation": "Dieses Gerät erlaubt nur {{ size }} VLANs.", "size_limitation": "Die maximale Anzahl an Benutzerkonten für dieses Gerät beträgt {{ size }}.", "reorder_priority": "Priorität neu sortieren", "reorder_finish": "<PERSON><PERSON> sortieren beended", "export_pdf_all_acl": "Alle ACL als PDF exportieren", "export_pdf_only_this_port": "Nur die Informationen dieses Ports in PDF exportieren", "export_pdf_only_this_vlan": "Nur die Informationen dieses VLANs in PDF exportieren", "clear_counter": "Zähler löschen", "unicast_mac_address_hint": "Nur Unicast-MAC-Adresse zulassen.", "disable_la_member_hint": "Dies ist ein Mitglied eines Linkaggregations Port Kanals.", "duplicate_redundant_port": "Redundante Ports können nicht dupliziert werden", "auto_refresh_enabled": "Automatische Aktualisierung", "auto_refresh_disabled": "Automatische Aktualisierung", "manually_refersh_disabled": "Manuelle Aktualisierung ist nicht möglich, wenn ‚Automatische Aktualisierung‘ aktiviert ist", "integration_3A_3B_hint": "Dieser Port ist eine Integration von 3/A und 3/B.", "set_to_static": "Auf statisch stellen"}, "unit": {"fps": "fps", "min": "Mindest.", "sec": "Sek.", "ms": "MS", "byte": "Byte", "m": "M", "kbyte": "KByte", "mbps": "Mbps", "w": "W", "watt": "<PERSON>", "watts": "<PERSON>", "percent": "%", "day": "Tag", "times": "mal", "mb": "MB", "ns": "ns", "microsecond": "μs", "ppm": "PPM", "packets": "<PERSON><PERSON>", "v": "V", "dbm": "dBm", "c": "°C", "f": "°F", "nm": "nm"}, "speed": {"10m": "10M", "100m": "100M", "1g": "1G", "10g": "10G", "40g": "40G", "56g": "56G", "2500m": "2500M", "full": "Full", "half": "Half"}, "table_function": {"filter_desc": "<PERSON><PERSON>", "export_pdf": "PDF exportieren", "export_csv": "CSV exportieren", "selected_count": "Ausgewählt", "row_count": "Gesamt", "limit_count": "<PERSON>."}, "led": {"status_unknown_hint": "Status unbekannt.", "state_green_on": "<PERSON><PERSON>", "state_green_blink4hz": "Das System wird hochgefahren", "state_red_on": "Das System konnte nicht initialisiert werden.", "state_off": "Das System ist ausgeschaltet.", "fault_red_on": "Im System ist ein Fehler aufgetreten. Bitte überprüfen Sie das Systemprotokoll auf Einzelheiten.", "fault_red_blink4Hz": "Der Switch ist hochgefahren und die Firmware wurde in den Speicher geladen.", "fault_off": "<PERSON><PERSON>", "mh_turbo_chain_head_green_on": "Der Switch ist der Head of Turbo Chain", "mh_turbo_ring_green_on": "Der Switch ist der Master von Turbo Ring 1 oder Turbo Ring 2.", "mh_mrp_green_on": "Der Switch ist der Manager des MRP-Rings.", "mh_turbo_ring_green_blink2hz_mds": "Der Switch ist der Master von Turbo Ring 1 oder Turbo Ring 2 und mindestens ein Ring ist defekt.", "mh_turbo_chain_head_green_blink2hz_mds": "Der Switch ist der Head of Turbo Chain und die Turbo Chain ist defekt", "mh_mrp_green_blink2hz_mds": "Der Switch ist der Manager des MRP-Rings und der Ring ist offen.", "mh_turbo_chain_member_green_blink2hz_mds": "Der Switch ist Mitglied der Turbo Chain und der entsprechende Mitgliedsport 1 ist nicht angeschlossen", "mh_turbo_chain_tail_green_blink2hz_mds": "<PERSON> Switch, der das Ende der Turbo Chain bildet, und der entsprechende Member Port sind ausgefallen", "mh_turbo_ring_green_blink4hz": "Der Switch ist der Master von Turbo Ring 1 oder Turbo Ring 2 und mindestens ein Ring ist defekt.", "mh_turbo_chain_head_green_blink4hz": "Der Switch ist der Head of Turbo Chain und die Turbo Chain ist defekt", "mh_mrp_green_blink4hz": "Der Switch ist der Manager des MRP-Rings und der Ring ist offen.", "mh_turbo_chain_member_green_blink4hz": "Der Switch ist Mitglied der Turbo Chain und der entsprechende Mitgliedsport 1 ist nicht angeschlossen", "mh_turbo_chain_tail_green_blink4hz": "<PERSON> Switch, der das Ende der Turbo Chain bildet, und der entsprechende Member Port sind ausgefallen", "mh_off": "Der Switch ist nicht der Master von Turbo Ring 1 oder Turbo Ring 2, Head der Turbo Chain oder Manager des MRP-Rings", "ct_turbo_ring_green_on": "Die Kopplungsfunktion des Switches ist aktiviert, um einen Backup Pfad zu bilden", "ct_mrp_green_on": "Der Switch ermöglicht eine Kopplungsfunktion, um einen Backup Pfad zu bilden", "ct_turbo_chain_tail_green_on": "Der Switch ist das Ende der Turbo Chain.", "ct_dual_homing_green_on": "Die dual homing-Funktion des Switches ist aktiviert.", "ct_turbo_ring_dual_homing_green_on": "Die coupling- und dual homing-Funktionen des Switches sind aktiviert.", "ct_turbo_chain_dual_homing_green_on": "Die  dual homing-Funktion des Switches ist aktiviert und der Switch ist das Ende der Turbokette.", "ct_mrp_dual_homing_green_on": "Die MRP connection  und die dual homing -Funktion des Switches sind aktiviert.", "ct_turbo_chain_head_green_blink2hz_mds": "Der Switch ist der Head of Turbo Chain und der entsprechende Member Port ist Link Down", "ct_turbo_chain_member_green_blink2hz_mds": "Der Switch ist Mitglied der Turbo-Chain und der entsprechende Mitglieds-Port 2 ist Link Down", "ct_turbo_chain_tail_green_blink2hz_mds": "Der Switch ist das Ende der Turbo-Chain und die Kette ist unterbrochen.", "ct_turbo_chain_head_green_blink4hz": "Der Switch ist der Head der Turbo-Chain und der entsprechende Mitglieds-Port ist Link-Down.", "ct_turbo_chain_member_green_blink4hz": "Der Switch ist Mitglied der Turbo-Chain und der entsprechende Mitglieds-Port 2 ist Link Down.", "ct_turbo_chain_tail_green_blink4hz": "Der Switch ist das Ende der Turbo-Chain und die Kette ist unterbrochen.", "ct_off": "Die Coupling- oder dual-homing funktion des Switches ist deaktiviert oder der Switch ist nicht das Ende der Turbo-Chain", "sync_amber_on": "Die PTP-Funktion ist aktiviert.", "sync_amber_blink4hz": "Der Switch hat Synchronisierungspakete empfangen.", "sync_green_on": "Die PTP-Funktion wurde erfolgreich konvergiert.", "sync_off": "Die PTP-Funktion ist deaktiviert.", "ms_green_on": "<PERSON><PERSON>", "ms_green_blink2hz": "<PERSON><PERSON> wird hochgefahren.", "ms_off": "Das Modul ist außer Betrieb.", "ms_red_on": "Das Modul konnte nicht initialisiert werden, oder der Benutzer hat das falsche Modul eingefügt.", "eps_amber_on": "Das externe Netzteil ist bereit, den PoE-Port mit Strom zu versorgen.", "eps_off": "<PERSON>s gibt keine externe Stromversorgung für das PoE-Gerät.", "pwr_eps_amber_on": "Die externe Stromversorgung wird am EPS-Eingang des Moduls eingespeist.", "pwr_eps_off": "<PERSON>s gibt keine externe Stromversorgung für das PoE-Gerät.", "pwr_amber_on": "Der Stromeingang des Moduls wird mit Strom versorgt.", "pwr_off": "Der Stromeingang des Moduls wird nicht mit Strom versorgt.", "port_poe_green_on": "Der Port ist mit einem IEEE 802.3at-betriebenen Gerät (PD) verbunden.", "port_poe_green_on_poebt": "Der Port ist mit einem IEEE 802.3bt-betriebenen Gerät (PD) verbunden.", "port_poe_amber_on": "Der Port ist mit einem IEEE 802.3af-betriebenen Gerät (PD) verbunden.", "port_poe_amber_on_poebt": "Der Port ist mit einem IEEE 802.3af/at-betriebenen Gerät (PD) verbunden.", "port_poe_amber_blink4hz": "Der PoE-Strom wurde abgeschal<PERSON>t, weil das Strombudget nicht ausreicht.", "port_poe_red_on": "Fehler bei der Erkennung des angetriebenen Geräts (PD).", "port_poe_red_blink4hz": "Auf dem mit Strom versorgten Gerät (PD) wurde ein Überstrom oder ein Kurzschluss erkannt", "port_poe_off": "Das mit Strom versorgte Gerät (PD) wird nicht mit Strom versorgt.", "port_link_up_hint": "Der Port ist aktiv und verbindet sich {{ operSpeed }}bps.", "port_link_down_hint": "Der Port ist inaktiv oder die Verbindung ist unterbrochen.", "prp_green_on": "Die PRP-Funktion ist aktiviert.", "prp_off": "Die PRP-Funktion ist deaktiviert.", "hsr_green_on": "Die HSR-Funktion ist aktiviert.", "hsr_off": "Die HSR-Funktion ist deaktiviert.", "coup_green_on": "Die Coupling-Funktion ist aktiviert.", "coup_off": "Die Coupling-Funktion ist deaktiviert."}}, "features": {"storm_control": {"page_title": "Traffic Storm Control", "dlf": "DLF"}, "la": {"page_title": "Link-Aggregation", "port_channel": "Port-Kanal (Trunk)", "wait_time": "Wartezeit", "configure_member": "<PERSON><PERSON><PERSON><PERSON> konfigurieren", "active_member": "Aktives Mitglied", "la_group_status": "LA-Gruppenstatus", "lacp": "LACP", "smac": "SMAC", "dmac": "DMAC", "smac_dmac": "SMAC + DMAC", "config_member_port": "Config Member Port", "config_member_port_hint": "Behalten Sie mindestens einen Port bei, der nicht zu einem Portkanal hinzugefügt werden kann.", "delete_port_channel_confirm_desc_1": "Warnung: ", "delete_port_channel_confirm_desc_2": "Einige Funktionen (wie RSTP und VLAN), die sich auf die ausgewählte Link-Aggregation beziehen, werden auf Standardwerte gesetzt.", "delete_port_channel_confirm_desc_3": "Sind <PERSON> sic<PERSON>, dass Sie die ausgewählte Link-Aggregation löschen möchten?", "la_size_limitation": "Dieses Gerät erlaubt nur {{ size }} Portkanäle.", "create_la_msg": "Link-Aggregation erstellen", "edit_la_pre_msg": "Portkanal bearbeiten {{ portChannel }} Einstellungen", "delete_la_msg": "Link-Aggregation löschen", "only_select_eight": "<PERSON>s können maximal acht ausgewählt werden."}, "scheduler": {"page_title": "Planer", "strict_priority": "Strict Priority", "weight_round_robin": "Weighted Round Robin", "sp": "SP", "wrr": "WRR", "wfq": "WFQ"}, "egress_shaper": {"page_title": "Egress <PERSON>", "egress_rate": "Egress Rate (CIR)"}, "rate_limit": {"page_title": "Grenzwert für Ingress Rate", "ingress_rate": "Ingress-Rate (CIR)", "ebs": "EBS", "ebs_full": "Excess <PERSON><PERSON><PERSON>", "conform_action": "Aktion anpassen", "exceed_action": "Aktion überschreiten", "violate_action": "Aktion verletzen", "blind": "Farbenblind", "aware": "Farbbewusst", "do_nothing": "Do Nothing", "drop": "Drop", "remark_cos": "Remark CoS", "remark_dscp": "Remark DSCP", "simple_token_bucket": "Simple Token Bucket", "sr_tcm": "SrTCM", "remark_value": "Bemerkungswert", "release_interval": "Freigabe-Intervall", "rate_limit_port_shutdown": "Rate-Limit-Port-Abschaltung"}, "classification": {"page_title": "Klassifizierung", "cos_priority": "CoS-Priorität", "preference_type": "Vertrauensart", "dhcp_mapping": "DSCP-<PERSON><PERSON><PERSON><PERSON><PERSON>", "cos_mapping": "CoS-<PERSON><PERSON><PERSON><PERSON>ng", "untag_default_priority": "Standardpriorität aufheben", "edit_dscp_msg": "DSCP bearbeiten {{ dscpIndex }} Einstellungen", "edit_cos_msg": "CoS bearbeiten {{ cosIndex }} Einstellungen"}, "linkup_delay": {"page_title": "Verbindungsverzögerung", "remaining_time": "Verbleibende Zeit"}, "port_mirror": {"page_title": "Port-Spiegelung", "span": "SPAN", "rspan": "RSPAN", "session_id": "Session-ID", "reflect_port_mode": "Reflect-Port-Modus", "rspan_type": "RSPAN-Typ", "rspan_vid": "RSPAN VLAN-ID", "rspan_settings": "RSPAN-Zwischeneinstellungen", "rspan_setting_hint": "<PERSON><PERSON><PERSON>, dass alle Ports, die für die RSPAN-Kommunikation verwendet werden, dem entsprechenden RSPAN-VLAN hinzugefügt werden", "rspan_vlan_setting_hint": "Um zu verhindern, dass getaggte Frames verworfen werden, wählen Si<PERSON> nur einen VLAN-Port im Trunk- oder Hybrid-Modus als Reflect-Port.", "duplicate_intermediate_vlan": "<PERSON><PERSON>-VLAN-ID wird bereits verwendet.", "rspan_role": "RSPAN Intermediate Role", "rspan_intermediate_vid1": "RSPAN Intermediate 1. VLAN-ID", "rspan_intermediate_vid2": "RSPAN Intermediate 2. VLAN-ID", "enable_rspan_title": "RSPAN Intermediate Role aktivieren", "enable_rspan_warning": "<PERSON><PERSON> diese Einstellung angewandt wird, werden alle bestehenden RSPAN-Sitzungen gelöscht", "rx_source_port": "Rx-Quellport(s)", "tx_source_port": "Tx-Quellport(s)", "designated_port": "Designated Port", "destination_ports": "Destination Port(s)", "destination_port_info": "Bei Access-Ports wird die PVID des Ports auf die RSPAN-VLAN-ID gesetzt. Bei Hybrid- oder Trunk-Ports wird der Port zu einem Mitglied des RSPAN-VLANs gemacht", "destination_port_hint": "Zielports werden dem RSPAN-VLAN hinzugefügt.", "destination_ports_or_designated_port": "Destination Port(s) oder Designated Port", "source_port_two_field_invalid": "Erforderlich zur Auswahl des Tx- oder Rx-Quellports", "create_mirror_msg": "Sitzung erstellen", "edit_mirror_msg": "Sitzung {{ sessionIndex }} Einstellungen bearbeiten", "select_tx_or_rx_hint": "<PERSON>s muss entweder der TX- oder der RX-Quellanschluss ausgewählt werden.", "is_not_access_port": "Dieser Port ist kein Access-Port.", "is_not_trunk_port": "Dieser Port ist kein Trunk-Port.", "source_port_must_be_access_port": "Source port must be access port when Reflect Port Mode is enabled.", "reflect_port_must_be_access_port": "Reflect port must be access port when Reflect Port Mode is enabled.", "reflect_port_must_be_trunk_hybrid_port": "Reflect port must be trunk/hybrid port when Reflect Port Mode is enabled.", "pvid_is_not_rspan_vid": "Die PVID dieses Ports ist nicht das RSPAN-VLAN.", "rspan_source_session_exist": "Die RSPAN-Quellsitzung existiert bereits.", "rspan_destination_session_exist": "Die RSPAN-Zielsitzung existiert bereits.", "rspan_cannot_create": "RSPAN-Sitzung kann nicht erstellt werden, wenn die RSPAN-Zwischenrolle aktiviert ist.", "session_span_size_limitation": "Die maximale An<PERSON><PERSON> von SPAN-Einträgen beträgt {{ size }}.", "session_rspan_size_limitation": "Die maximale An<PERSON>hl von RSPAN-Einträgen beträgt {{ size }}.", "delete_session_title": "Sitzung löschen", "delete_session_content": "Sind <PERSON> sicher, dass Sie die ausgewählte Sitzung löschen möchten?", "rspan_vid_hint_l2": "Using the Management VLAN or VLAN Assignment-configured VLANs for RSPAN is not recommended.", "rspan_vid_hint_l3": "Using VLAN interfaces or VLAN Assignment-configured VLANs for RSPAN is not recommended."}, "vlan": {"page_title": "VLAN", "vlan": "VLAN", "global": "Global", "management_vlan": "Verwaltungs-VLAN", "management_port": "Verwaltungsport", "mgmt_vlan_settings": "Management-VLAN-Einstellungen", "management_vlan_port_setting_hint": "Bitte wählen Sie den Port aus, an den Ihr Computer angeschlossen ist, und stellen <PERSON> sicher, dass die Einstellungen korrekt sind, damit die Verbindung zum Switch nicht unterbrochen wird.", "port_mode_table_title": "VLAN Switchport Mode Table", "egress_tagged_table_title": "VLAN-Mitgliedschaftstabelle", "gvrp": "GVRP", "vlan_unaware": "VLAN Unaware", "vlan_unaware_gvrp_error": "GVRP cannot be enabled while VLAN Unaware is active.", "vlan_unaware_active_disable_hint": "VLAN cannot be modified while VLAN Unaware is active.", "all_member_vlan": "Alle Mitglieder-VLAN-IDs", "dynamic_gvrp": "Dynamisches GVRP", "egress_port": "Egress Port", "tagged_port": "Tagged Port", "untagged_port": "Untagged Port", "forbidden_port": "Forbidden Port", "vid_exist_warning": "VLAN existiert bereits", "vlan_max_warning": "Max. 10 VLANs auf einmal", "vlan_max_hint": "Max. 10 VLANs", "pvid": "PVID", "tagged_vlan": "Tagged VLAN", "untagged_vlan": "Untagged VLAN", "access": "Access", "access_port": "Access Port", "trunk": "Trunk", "trunk_port": "Trunk Port", "hybrid": "Hybrid", "hybrid_port": "Hybrid Port", "vlan_assignment": "VLAN-Zuweisung", "delete_vlan_confirm_desc": "Sind <PERSON> sic<PERSON>, dass Sie das ausgewählte VLAN löschen möchten?", "mgmt_setting_disabled_pvid": "PVID ist an dieses VLAN gebunden und kann daher nicht gelöscht werden.", "mgmt_setting_disabled_access_mode": "Wenn der Port den Zugriffsmodus verwendet, kann er nicht zu diesem VLAN wechseln.", "mgmt_setting_disabled_forbidden": "Dieser Port ist ein verbotener Forbidden-Port.", "mgmt_setting_disabled_egress": "Dieser Port ist ein Member-Port.", "port_setting_disabled_tagged": "Dieses VLAN ist ein getaggtes VLAN.", "port_setting_disabled_untagged": "Dieses VLAN ist ein nicht getaggtes VLAN.", "port_setting_disabled_forbidden": "Dieser Port ist ein Forbidden-Port für dieses VLAN.", "port_setting_disabled_pvid_member": "Diese PVID kann nicht an dieses VLAN gebunden werden, da dieser Port kein Member-Port ist.", "port_setting_disabled_pvid_forbidden": "Diese PVID kann nicht an dieses VLAN gebunden werden, da dieser Port ein Forbidden-Port ist.", "port_setting_error_pvid_member": "Nicht Tagged oder nicht Untagged VLAN", "port_setting_error_pvid_forbidden": "Dies ist ein verbotener Port und die Einstellungen können nicht angewendet werden", "vlan_setting_vid_info": "<PERSON><PERSON> können mehrere VLANs erstellt werden und sollten als einzelne Zahl oder als Bereich eingegeben werden, z. B. 2, 4-8, 10-13.", "te_mstid": "TE-MSTID", "temstid_member": "TE-MSTID-Mit<PERSON>ed", "temstid_info": "<PERSON><PERSON><PERSON> das VLAN-Join-TE-MSTID-Mit<PERSON>ed wird der Stream durch eine statische Weiterleitungsregel statt durch den MAC-Lern-/Weiterleitungsmechanismus weitergeleitet.", "create_vlan_msg": "VLAN erstellen", "delete_vlan_msg": "VLAN löschen", "vlan_setting_info_title": "Wie stellt man das ein", "example_scenario": "Beispielszenario", "example_scenario_info_1": "Port 1: Hybrid Mode, PVID 1, TAG VLAN 3-5, and UNTAG VLAN 1", "example_scenario_info_2": "Port 2: Trunk Mode, PVID 2, and TAG VLAN 2-5", "example_scenario_info_3": "Port 3: Access Mode, PVID 1, and UNTAG VLAN 1", "example_scenario_info_4": "Switch-A Settings: Management VLAN 1", "setup_flow": "<PERSON> e<PERSON><PERSON>ten", "vlan_port_mode_setup": "VLAN-Port-Modus-Setup", "port_number_setting": "Port {{ portNumber }} Einstellungen", "setup_list_hybrid": "Modus 'Hybrid' auswählen", "setup_list_apply": "- <PERSON><PERSON><PERSON>", "setup_list_trunk": "- <PERSON><PERSON> 'Trunk' auswählen", "setup_list_access": "Der Standardmodus ist 'Access' und es besteht keine Notwendigkeit, etwas zu ändern", "setup_list_pvid2": "- PVID 2", "vlan_create_member_setup": "VLAN erstellen/VLAN-Mitglieder einrichten", "vlan_create_member_setup_info_part_1": "<PERSON><PERSON>", "vlan_create_member_setup_info_part_2": ", VLAN-ID {{ vlanIndex }} hi<PERSON><PERSON><PERSON><PERSON> , Member Port {{ portIndex }} hinzuf<PERSON>gen", "vlan_port_pvid_setup": "VLAN-Port PVID einrichten", "vlan_port_pvid_setup_info": "<PERSON><PERSON><PERSON> und muss nicht geändert werden."}, "l3_interface": {"page_title": "Netzwerkschnittstelle", "loopback_size_limitation": "Dieses Gerät erlaubt nur {{ size }} Loopbacks.", "operStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loopback_id": "Loopback-ID", "vlan_interface": "VLAN-Schnittstelle", "loopback_interface": "Loopback-<PERSON><PERSON><PERSON>ste<PERSON>", "alias": "<PERSON><PERSON>", "mtu": "MTU", "proxy_arp": "Proxy-ARP", "vlan_id_hint": "<PERSON> Schnittstellen-VLAN-ID sollte mit dem Layer-2-VLAN übereinstimmen, damit Layer-3-<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>.", "delete_ip_interface_desc": "Sind <PERSON> sicher, dass Sie diesen Eintrag löschen möchten?", "vlan_card_title": "VLAN-Schnittstelle", "loopback_card_title": "Loopback-<PERSON><PERSON><PERSON>ste<PERSON>", "delete_ip_interface": "Schnittstelle löschen", "add_l3_vlan_ip_interface": "VLAN-Schnittstelleneinstellungen erstellen", "edit_l3_vlan_ip_interface": "VLAN-Schnittstelleneinstellungen bearbeiten", "add_l3_loopback_ip_interface": "Loopback-Schnittstelleneinstellungen erstellen", "edit_l3_loopback_ip_interface": "Loopback-Schnittstelleneinstellungen bearbeiten", "from_dcp": "(Von PROFINET DCP)"}, "stp": {"page_title": "Spanning Tree", "stp_mode": "STP-Modus", "compatibility": "Kompatibilität", "stp": "STP", "rstp": "RSTP", "mstp": "MSTP", "stp_rstp": "STP/RSTP", "bridge_priority": "Bridge Priority", "error_recovery_time": "Error Recovery Time", "forward_delay_time": "Forward Delay Time", "hello_time": "Hello Time", "max_age": "Max. Age", "edge": "Edge", "guard": "Guard", "path_cost": "Path Cost", "multiples_of_number": "Vielfache von {{ number }}", "path_cost_help_info": "Der Pfadkostenwert wird automatisch entsprechend der unterschiedlichen Portgeschwindigkeit zugewiesen, wenn der Wert auf Null gesetzt ist.", "bpdu_guard": "BPDU Guard", "root_guard": "Root Guard", "loop_guard": "Loop Guard", "bpdu_filter": "BPDU-Filter", "root_information": "Root-Informationen", "bridge_id": "Bridge-ID", "root_path_cost": "Root-Pfad-Kosten", "bridge_information": "Bridge Information", "running_protocol": "<PERSON><PERSON><PERSON>ll", "port_role": "Port Role", "link_type": "<PERSON><PERSON><PERSON><PERSON>", "shared": "Shared", "bpdu_inconsistency": "BPDU Inconsistency", "root_inconsistency": "Root Inconsistency", "loop_inconsistency": "Loop Inconsistency", "link_type_shared_lan": "Shared LAN", "alternate": "Alternate", "root": "Root", "designated": "Festgelegt", "instance": "Instanz", "instance_index": "Instanz {{ instId }}", "all_instances": "Alle Instanzen", "instance_list": "Instanzliste", "instance_id": "Instanz-ID", "mstp_size_limitation": "Die maximale Anzahl von Instanzen für dieses Gerät beträgt {{ size }}, außer für CIST.", "vlan_list": "VLAN-Liste", "port_table_of_cist": "Port<PERSON><PERSON><PERSON><PERSON> von CIST", "port_table_of_instance": "Port-Tabelle der Instanz {{ instId }}", "information_of_cist": "<PERSON><PERSON>", "information_of_instance": "Informationen zur Instanz {{ instId }}", "region_name": "Regionsname", "region_revision": "Regionsrevision", "max_hops": "<PERSON><PERSON>", "instance_id_duplicate": "Die Instanz-ID wurde erstellt.", "except_for_cist": "<PERSON><PERSON> <PERSON><PERSON><PERSON>.", "copy_port_config": "Kopieren Sie die Port-Konfigurationen", "select_from_port_of_inst": "<PERSON> von {{ instName }}", "select_to_inst": "<PERSON><PERSON>", "general_information": "Allgemeine Informationen", "regional_root_id": "Regionale Root-ID", "cist_root_id": "CIST-Root-ID", "cist_path_cost": "CIST-Pfadkosten", "designated_root_id": "Designierte Root-ID", "other_vlans": "Andere VLANs", "create_instance": "Instanz er<PERSON>llen", "edit_instance": "Instanz {{ instId }}, Einstellungen bearbeiten", "delete_instance": "Instanz löschen", "edit_cist": "CIST-Einstellungen bearbeiten", "edit_instance_port": "Instanz {{ instId }} Port {{ portIndex }} Einstellungen bearbeiten", "edit_cist_port": "CIST-Port {{ portIndex }} Einstellungen bearbeiten"}, "port_security": {"page_title": "Port-Security", "port_security_mode": "Port-Security Modus", "port_security_mode_help_info": "Das Ändern des Port-Security Modus setzt alle Einstellungen zurück.", "mac_sticky": "MAC Sticky", "static_port_lock": "Static Port Lock", "address_limit": "Adress<PERSON>it", "secure_action": "Sichere Aktion", "current_address": "Aktuelle Adresse", "configured_address": "<PERSON>l konfigurierte Adresse", "violation": "<PERSON><PERSON><PERSON><PERSON>", "effective": "Wirksam", "secure_pack_drop": "Packet Drop", "total_entry": "Total Trust Hosts", "max_address": "Die max. <PERSON><PERSON><PERSON> der Adressen im System", "sticky_configured": "<PERSON>y<PERSON>k<PERSON><PERSON><PERSON><PERSON><PERSON>", "lock_configured": "<PERSON><PERSON><PERSON> konfiguriert", "sticky_dynamic": "<PERSON><PERSON> Dynamik", "address_limit_hint": "<PERSON>n sich der Adressgrenzwert geändert hat, werden alle MAC-Adressen am Port gelöscht."}, "garp": {"page_title": "GARP", "join_time": "Join Time", "leave_time": "Leave Time", "leave_all_time": "Leave All Time", "required_join_time_multiple": "Die Join Time muss ein Vielfaches von {{ num }} sein", "required_leave_time_multiple": "Die Leave Time muss ein Vielfaches von {{ num }} sein ", "required_leave_all_time_multiple": "Die Leave All Time muss ein Vielfaches von {{ num }} sein"}, "lldp": {"page_title": "LLDP", "neighbor_status": "Neighbor Status", "sidenav_header": "Ausführliche Informationen", "port_local_intf_status": "Lokale Port-Schnittstelle", "port_id_subtype": "Port-ID-Subtyp", "port_id": "Port-ID", "port_desc": "Portbeschreibung", "dot1_tlv_info": "Erweiterter 802.1 TLV", "dot3_tlv_info": "Erweiterter 802.3 TLV", "port_vlan_id": "Port-VLAN-ID", "vlan_name": "VLAN-Name", "vlan_tx_status": "VLAN-ID/Name", "aggregated_and_status": "Link Aggregation Status", "aggregated_port_id": "Aggregierte Port-ID", "max_frame_size": "Maximale Frame Size", "port_traffic_statistics": "Port Traffic Statistics", "total_frame_out": "Gesamte Frames: Out", "total_entries_aged": "Gesamtzahl der abgelaufenen Einträge", "total_frame_in": "Gesamte Frames: In", "total_frame_receviced_in_error": "Gesamtzahl der fehlerhaft empfangenen Frames", "total_frame_discarded": "Gesamtzahl verworfener Frames", "total_tlvs_unrecognized": "Gesamte TLVS nicht erkannt", "total_tlv_discarded": "Verworfene TLVs insgesamt", "management_address_table": "Management Address Tabelle", "management_address": "Management Address", "extended_eip_tlv": "Erweiterte Ethernet/IP TLV", "vendor_id": "Hersteller-ID", "device_type": "Gerätetyp", "product_code": "Produkt-Code", "major_revision": "Major Revision", "minor_revision": "Minor Revision", "interface_id": "Schnittstellen-ID", "lldp_version": "LLDP-Version", "transmit_interval": "Sendeintervall", "notification_interval": "Benachrichtigungsintervall", "reinit_delay": "Neuinitialisierungsverzögerung", "holdtime_multiplier": "Haltezeit-Multiplikator", "chass_id_subtype": "Chassis-ID-Subtyp", "tx_delay": "Tx-Verzögerung", "subtype_chassis_component": "<PERSON><PERSON>s-Component", "subtype_if_alias": "<PERSON>-<PERSON><PERSON>", "subtype_port_component": "Port-Component", "subtype_mac_addr": "MAC-Address", "subtype_network_address": "Network-Address", "subtype_if_name": "If-Name", "subtype_unknown": "Unbekannter Subtyp", "chassis_id": "<PERSON><PERSON>s <PERSON>", "tlv": "TLV", "local_info": "Lokale Informationen", "local_timer": "Lokaler Timer", "remote_table_statistics": "Remote-Tabellenstatistik", "statistics_last_change_time": "Letzte Änderungszeit (ms)", "statistics_insert": "Einfügungen", "statistics_drops": "Drops", "statistics_ageout": "Abgelaufene", "tx_status": "<PERSON><PERSON><PERSON>", "rx_status": "Rx-Status", "nbr_port_id": "Neighbor Port ID", "nbr_chassis_id": "Neighbor Chassis ID", "tx_only": "Tx Only", "rx_only": "Rx Only", "tx_and_rx": "Tx and Rx", "basic": "Basic", "basic_transmit_tlvs": "Grundlegende Übertragungs-TLVs", "8021_transmit_tlvs": "802.1 Übertragungs-TLVs", "8023_transmit_tlvs": "802.3 Übertragungs-TLVs", "port_component_description": "Beschreibung der Portkomponente.", "system_name": "Systemname", "system_desc": "Systembeschreibung", "system_capability": "Systemfähigkeit", "la_statistics": "Link-Aggregations-Statistik", "lldp_update_success": "Globale LLDP-Einstellungen erfolgreich aktualisiert.", "local_port": "Lokaler Port", "sys_capability": "Systemfähigkeit", "hold_time": "Haltezeit", "repeater": "Verstärk<PERSON>", "bridge": "<PERSON><PERSON><PERSON><PERSON>", "vlan_access_point": "Vlan-Zugangspunkt", "telephone": "Telefon", "docsis_cable_device": "Docsis-Kabelgerät", "station_only": "Nur Station"}, "mac_address_table": {"page_title": "MAC-Adresstabelle", "independent_vlan_learning": "Unabhängiges VLAN-Lernen", "mac_learning_mode": "MAC-Lernmodus", "mac_learning_mode_help_info": "<PERSON><PERSON>, dass der Wechsel des Modus das zugehörige L2-<PERSON><PERSON><PERSON> zurücksetzt.", "aging_time": "Aging Time", "learnt_unicast": "Unicast gel<PERSON>t", "learnt_multicast": "Multicast gelernt"}, "port_setting": {"page_title": "Porteinstellungen", "admin": "Admin", "media_type": "Medientyp", "10m_half": "10M Half", "10m_full": "10M Full", "100m_half": "100M Half", "100m_full": "100M Full", "speed_duplex": "Speed/Duplex", "flow_control": "Flow Control", "flow_control_hint1": "Flow Control kann aktiviert/deaktiviert werden, ist aber nur bei Vollduplex wirksam", "flow_control_hint2": "Back Pressure can be enabled/disabled, but it is only effective at half duplex.", "mdi_mdix": "MDI/MDIX", "mdi": "MDI", "mdix": "MDIX", "enabled_xmit": "Senden aktiviert", "enabled_rcv": "Empfang aktiviert", "fiber_speed_disable": "Der Glasfaser-Port kann Speed/Duplex nicht einstellen.", "fiber_mdi_disable": "Der Glasfaser-Port kann MDI/MDIX nicht einstellen.", "fiber_copy_to_other_port_disable": "Der Glasfaser-Port kann keine Konfigurationen auf andere Ports kopieren.", "port_copy_to_fiber_port_disable": "Konfigurationen können nicht auf Glasfaser-Ports kopiert werden."}, "dashboard": {"page_title": "Geräteübersicht", "system_info": "Systeminformationen", "panel_status": "Panel-Status", "panel_view": "Panel-<PERSON><PERSON><PERSON>", "link_up_port": "Link Up Ports", "link_down_port": "Link Down Ports", "module": "Modul {{ index }} - {{ name }}", "product_model": "Produktmodell", "firmware_version": "Firmware-Version", "system_uptime": "System-Betriebszeit", "ip_address_v4": "IPv4-<PERSON><PERSON><PERSON>", "ip_address_v6": "IPv6-<PERSON><PERSON><PERSON>", "l3_ip_address_list": "Liste der Schnittstellen-IP-Adressen", "redundant_protocol": "Redundantes Protokoll", "power_model": "Power Model", "external_storage": "<PERSON><PERSON><PERSON> S<PERSON>icher", "iec62439_3_protocol": "IEC 62439-3-<PERSON><PERSON><PERSON>", "event_summary": "Ereigniszusammenfassung", "event_summary_hint": "(Letzte 3 Tage)", "top_5_interface_error_packet": "Top 5 Schnittstellenfehlerpakete", "top_5_interface_utilization": "Top 5 Schnittstellenauslastung", "critical_hint": "Es ist eine Anomalie aufgetreten und es besteht die Gefahr, dass das System in Zukunft abnormal funktioniert", "error_hint": "Es ist eine Anomalie aufgetreten, der Systembetrieb wurde jedoch nicht beeinträchtigt", "warning_hint": "Die Informationen enthalten eine Warnung/Erinnerung, haben jedoch keinen Einfluss auf Funktionen oder Systemvorgänge", "notice_hint": "Die Information besagt, dass die Funktion ordnungsgemäß funktioniert und das Gerät normal arbeitet", "tx_error": "Tx-<PERSON><PERSON>", "rx_error": "Rx<PERSON><PERSON><PERSON>", "unsupported_module_warning": "Vorsicht: Nicht unterstütztes Modul erkannt. Entfernen Sie das nicht unterstützte Modul, um die normale Funktion aufrechtzuerhalten."}, "igmp_snooping": {"page_title": "IGMP-Snooping", "vlan_setting": "VLAN-Einstellungen", "group_table": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forwarding_table": "Weiterleitungstabelle", "query_interval": "Abfrageintervall", "static_router_port": "Statischer Router-Port", "dynamic_router_port": "Dynamischer Router-Port", "config_role": "Rolle konfigurieren", "active_role": "Aktive Rolle", "startup_query_interval": "<PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> beim <PERSON>", "startup_query_count": "An<PERSON><PERSON> der Startabfragen", "other_quer_present_interval": "Anderes Abfrage-Präsenzintervall", "group_address": "Gruppenadresse", "filter_mode": "Filtermodus", "source_address": "Quelladresse", "querier": "<PERSON><PERSON>", "non_querier": "Non-Querier"}, "turbo_ring_v2": {"page_title": "Turbo Ring V2", "ring_coupling_mode": "Ring Coupling Mode", "static_ring_coupling": "Static Ring Coupling", "dynamic_ring_coupling": "Dynamic Ring Coupling", "ring_id": "Ring-ID", "master_id": "Master-ID", "ring_port": "Ring Port", "coupling_mode": "Ring Coupling Modus", "coupling_port": "Coupling Port", "primary_path": "Primärer Pfad", "backup_path": "Backup Path", "ring_setting": "Ring-Einstellungen", "ring_coupling_setting": "Ring Coupling Einstellungen", "coupling_setting": "Coupling Group {{ id }} Settings", "static_ring_coupling_setting": "Static Ring Coupling Settings", "dynamic_ring_coupling_setting": "Dynamic Ring Coupling Settings", "coupling_group_id": "Coupling Group ID", "coupling_group_status": "Coupling Group Status", "group_id": "Group {{ id }}", "ring_status": "Ring-Status", "ring_index": "Ring Index", "total_ring_number": "Total Ring Number", "healthy": "Healthy", "break": "Break", "ring_coupling_status": "Ring Coupling Status", "static_ring_coupling_status": "Static Ring Coupling Status", "dynamic_ring_coupling_status": "Dynamic Ring Coupling Status", "coupling_mode_primary": "Coupling Primary Path", "coupling_mode_backup": "Coupling Backup Path", "coupling_port_status": "Coupling Port Status", "primary_mac": "Primary MAC", "primary_port": "Primary Port", "primary_port_status": "Primary Port Status", "backup_mac": "Backup MAC", "backup_port": "Backup Port", "backup_port_status": "Backup Port Status", "ring_setting_dialog_title": "{{ portIndex }} Einstellungen", "dip_lock_hint": "Turbo Ring V2 ist aufgrund von DIP-Konfigurationen gesperrt."}, "8021x": {"page_title": "IEEE 802.1X", "auth_mode": "Authentifizierungsmodus", "local_database": "Local Database", "re_auth": "Erneut authentifizieren", "port_control": "Portkontrolle", "auth_session_type": "Authentication Session Type", "max_request": "Maximale Anfrage", "quiet_period": "Ruhezeit", "reauthentication": "Neuauthentifizierung", "reauth_period": "Reauthentifizierungszeitraum", "server_timeout": "Server-Timeout", "supp_timeout": "Supp Timeout", "tx_period": "Tx-Periode", "auth_port": "Auth-Port", "retransmit": "Erneut übertragen", "force_unauthorized": "Force Unauthorized", "force_Authorized": "Force Authorized", "reauth_port_confirm": "<PERSON>d <PERSON>, dass Si<PERSON> den Port {{ port }} neu authentifizieren wollen?", "authorized": "Autorisiert", "unauthorized": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title_reauth_port": "Den Port erneut authentifizieren", "port_setting": "Port {{ portIndex }} Einstellungen", "account_setting": "Konto {{ userName }} Einstellungen", "timeout_retransmit_hint": "Alle Wiederholungsversuche dürfen den Timeout-Wert des Dot1x-Servers nicht überschreiten. Notiz: Alle Wiederholungszeiten = Timeout * (Neuübertragung + 1). Empfohlener Wert: {{ number }}", "session_status": "Session Status", "auth_table_of_port_based": "Authentication Table of Port-Based", "auth_table_of_mac_based": "Authentication Table of MAC-Based"}, "dual_homing": {"page_title": "Dual Homing", "multi_dual_homing": "Multiple Dual Homing", "dual_homing_table_settings": "Dual Homing Tabelle Einstellungen", "primary_port": "Primärer Port", "primary_link_status": "Primärer Verbindungsstatus", "primary_port_status": "Primärer Portstatus", "secondary_port": "Sekundärer Hafen", "secondary_link_status": "Sekundärer Verbindungsstatus", "secondary_port_status": "Sekundärer Portstatus", "secondary_port_hint": "Der sekundäre Port darf nicht mit dem primären Port identisch sein.", "path_switching_mode": "Pfadumschaltmodus", "primary_path_always_first": "Primary path always first", "maintain_current_path": "Maintain current path", "maintain_current_path_hint": "Den aktuellen Pfad beibehalten, bis die Verbindung unterbrochen wird", "primary_path_sensing_recovery": "Primary path sensing recovery", "path_switching_mode_hint": "<PERSON><PERSON> wird empfohlen, die Funktion Linkup Delay von ", "path_switching_mode_hint_2": "-Ports zu aktivieren.", "path": "Weg", "linkup_delay_warning_title": "Linkup-Verzögerung ist deaktiviert."}, "poe": {"page_title": "PoE", "power_output": "Leistungsabgabe", "poe_supported": "PoE unterstützt", "scheduling": "Zeitplanungg", "pd_failure_check": "PD-Ausfallprüfung", "auto_power_cutting": "Auto Power Cutting", "auto_power_cutting_hint": "Auto Power Cutting entfernt die niedrigste Priorität und den kleinsten Index-Port-Stromausgang, wenn der Stromverbrauch das Strombudget des Systems übersteigt.", "system_power_budget": "Systemleistungsbudget", "system_power_budget_hint": "Das Leistungsbudget des Systems hängt von der Leistungsfähigkeit des externen Netzteils (EPS) ab", "actual_power_budget": "Tatsächliches Energiebudget", "actual_power_budget_hint": "Der niedrigere Wert zwischen dem \"Ist-Leistungsbudget\" und dem \"Systemleistungsbudget\" wird zur \"Leistungsbudgetgrenze\"", "output_mode": "Ausgabemodus", "high_power": "Hohe Energie", "force": "Erzwingen", "power_allocation": "Leistungszuweisung", "legacy_pd_detection": "Legacy PD Detection", "critical": "<PERSON><PERSON><PERSON>", "low": "<PERSON><PERSON><PERSON>", "high": "Hoch", "rule": "Regel", "also_apply_port": "Regel auf den Port anwenden", "device_ip": "Geräte-IP", "check_frequency": "Frequenz prüfen", "no_response_times": "<PERSON><PERSON>tionszeite<PERSON>", "no_action": "Keine Aktion", "restart_pd": "PD neu starten", "shutdown_pd": "<PERSON> abschalten", "system_time_status": "Systemzeitstatus", "system_time": "Systemzeit", "local_timeZone": "Lokale Zeitzone", "daylight_saving_time": "Sommerzeit", "off": "Aus", "on": "Ein", "rule_name": "Regelname", "schedule_time": "Zeit einplanen", "repeat_execution": "Ausführung wiederholen", "daily": "Tä<PERSON><PERSON>", "weekly": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "weekdays": "Wochentage", "weekend": "Wochenende", "sun": "Sonntag", "mon": "Montag", "tue": "Dienstag", "wed": "Mittwoch", "thu": "Don<PERSON><PERSON>", "fri": "Freitag", "sat": "Samstag", "sunday": "Sonntag", "monday": "Montag", "tuesday": "Dienstag", "wednesday": "Mittwoch", "thursday": "Don<PERSON><PERSON>", "friday": "Freitag", "saturday": "Samstag", "maximum_input_power": "Maximale Eingangsleistung", "power_budget_limit": "Energiebudgetgrenze", "power_management_mode": "Energieverwaltungsmodus", "allocated_power": "Allocated Power", "consumed_power": "Consumed Power", "remaining_available_power": "Verbleibende verfügbare Leistung", "classification": "Klassifizierung", "over_current": "<PERSON><PERSON><PERSON>", "current_ma": "Strom (mA)", "voltage_v": "Spannung (V)", "consumption_w": "<PERSON><PERSON><PERSON><PERSON> (W)", "device_type": "Gerätetyp", "not_present": "<PERSON>cht Vorhanden", "legacy": "Legacy-PD", "dot3af": "802.3 af", "dot3at": "802.3 at", "dot3bt": "802.3 bf", "dot3btss": "802.3 bt SS", "dot3btds": "802.3 bt DS", "na": "N / A", "configuration_suggestion": "Konfigurationsvorschlag", "no_suggestion": "<PERSON><PERSON>", "enable_poe": "PoE-Stromausgang aktivieren", "disable_poe": "PoE-Stromausgang deaktivieren", "select_auto": "Ausgabemodus \"Auto\" auswählen", "select_high_power": "Ausgabemodus \"Hohe Leistung\" auswählen", "select_force": "Ausgabemodus \"Erzwingen\" auswählen", "enable_legacy": "Legacy-PD-Erkennung aktivieren", "raise_eps_voltage": "<PERSON><PERSON><PERSON><PERSON>hen Sie die Spannung der externen Stromversorgung, sodass sie größer als 46 VDC ist", "pd_failure_check_status": "Status der PD-Fehlerprüfung", "alive": "Vorhand<PERSON>", "not_alive": "Nicht vorhanden", "schedule_size_limitation": "Dieses Gerät erlaubt nur {{ size }} Zeitpläne.", "title_create_rule": "Regel erstellen", "title_edit_rule": "<PERSON>el bear<PERSON>ten", "allocated_power_hint": "Berechnen Sie das Energiebudget für alle Ports und stellen Sie sicher, dass die zugewiesene Gesamtleistung unter dem Energiebudget liegt.", "consumed_power_hint": "Berechnen Sie den Stromverbrauch aller Ports in Echtzeit.", "change_power_mode_dialog_title": "<PERSON><PERSON>n Sie den Energieverwaltungsmodus ein", "select_allocated_power_mode": "<PERSON><PERSON> <PERSON><PERSON>, dass <PERSON><PERSON> den Modus \"Zugewiesene Leistung\" auswählen möchten? Wenn ja, wird die \"Automatische Stromabschaltung\" deaktiviert.", "select_consumed_power_mode": "<PERSON><PERSON> <PERSON>, dass Sie den Modus \"Verbrauchte Leistung\" wählen wollen? Wenn ja, wird die \"Automatische Stromabschaltung\" aktiviert.", "change_power_cutting_dialog_title": "<PERSON>ellen Sie die automatische Stromabschaltung ein", "disable_auto_power_cutting": "<PERSON>d <PERSON><PERSON>, dass Sie die \"Automatische Stromabschaltung\" deaktivieren wollen? Wenn ja, wird aus dem \"Energieverwaltungsmodus\" der Modus \"Zugewiesene Leistung\".", "enable_auto_power_cutting": "<PERSON>d <PERSON>, dass Si<PERSON> die \"Automatische Stromabschaltung\" aktivieren wollen? Wenn ja, wird aus dem \"Energieverwaltungsmodus\" der Modus \"Verbrauchte Energie\".", "avaliable_power_hint": "\"Verbleibende verfügbare Leistung\" ist \"Maximale Eingangsleistung\" minus \"{{ power }}\"."}, "turbo_chain": {"page_title": "Turbo Chain", "chain_role": "Chain Role", "head": "Head", "member": "Member", "tail": "Tail", "head_port": "Head Port", "tail_port": "Tail Port", "member_port_number": "Member Port {{ portIndex }}", "chain_information": "Chain Information", "head_port_status": "Head Port Status", "member_port_status": "Mitgliedshafen<PERSON><PERSON>", "tail_port_status": "Tail Port Status", "member_number_port_status": "Member {{ number }} Port Status", "initiated": "Initiier<PERSON>"}, "mrp": {"menu_title": "MRP", "page_title": "Media Redundancy Protocol", "mrp_role": "Role", "ring_manager": "Ring Manager", "ring_client": "Ring Client", "domain_uuid": "Domain UUID", "domain_id": "Domain-ID", "react_on_link_change": "Auf Linkänderung reagieren", "ring_port": "Ring Port {{ portIndex }}", "ring_status": "Ring Status", "mrp_ring": "MRP-Ring", "ring_state": "Ringzustand", "vid_hint": "Die VLAN-ID muss mit den Einstellungen des redundanten Ports übereinstimmen.", "react_on_link_change_hint": "Diese Funktion ist nur auf dem MRP Ring Manager Switch verfügbar. <PERSON><PERSON><PERSON> sie aktiviert ist, reagiert der Ring Manager sofort auf Linkwechsel und die MRP-Topologie konvergiert schneller.", "initiation": "Einleitung", "awaiting_connection": "<PERSON><PERSON> auf Verbindung", "primary_ring_port_link_up": "Primäre Ring-Port-Verbindung aufgebaut", "ring_open": "<PERSON> offen", "ring_closed": "Ring geschlossen", "data_exchange_idle": "Datenaustausch im Leerlauf", "pass_through": "Durchreichen", "data_exchange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pass_through_idle": "Durchreichen im Leerlauf", "port_pvid_warning": "Die VLAN-ID muss mit den Einstellungen des redundanten Ports übereinstimmen", "port_mode_warning": "MRP-Ring-Ports funktionieren nur, wenn sie auf den VLAN-Trunk-Modus oder den VLAN-Hybridmodus eingestellt sind.", "interconnection_port_mode_warning": "MRP-Interconnection-Ports funktionieren nur, wenn sie auf den VLAN-Trunk-Modus oder den VLAN-Hybridmodus eingestellt sind.", "interconnection": "Verbindung", "interconnection_role": "Verbindungs Rolle", "interconnection_manager": "Interconnection Manager", "interconnection_client": "Interconnection Client", "interconnection_mode": "Verbindungs <PERSON>", "lc_mode": "LC-Mode", "rc_mode": "RC-Mode", "interconnection_id": "Verbindungs-ID", "interconnection_port": "Verbindungs Port", "interconnection_status": "Verbindungs Status", "interconnection_state": "Verbindungs Zustand", "interconnection_open": "Verbindung offen", "interconnection_closed": "Verbindung geschlossen", "interconnection_port_idle": "Verbindungsport im Leerlauf"}, "unicast_table": {"page_title": "Unicast-Tabelle", "static_unicast": "Statischer Unicast", "edit_static_unicast_entry": "Diesen statischen Unicast-Eintrag bearbeiten", "add_static_unicast_entry": "Einen statischen Unicast-Eintrag hinzufügen", "size_limitation": "Die maximale Anzahl statischer Unicast-Einträge für dieses Gerät beträgt {{ size }}."}, "static_forwarding_table": {"page_title": "Statische Weiterleitungstabelle", "menu_title": "Statische Weiterleitung", "size_limitation": "Die maximale Anzahl statischer Unicast-Einträge für dieses Gerät beträgt {{ size }}.", "title_add_static_forwarding": "Einen statischen Weiterleitungseintrag erstellen", "title_edit_static_forwarding": "Diesen statischen Weiterleitungseintrag bearbeiten", "title_delete_static_forwarding": "Diesen statischen Weiterleitungseintrag löschen"}, "multicast_table": {"page_title": "Statische Multicast-Tabelle", "static_multicast": "Statischer Multicast", "delete_on_reset": "<PERSON><PERSON> löschen", "delete_on_timeout": "Bei Zeitüberschreitung löschen", "add_static_multicast_entry": "Einen statischen Multicast-Eintrag hinzufügen", "edit_static_multicast_entry": "Diesen statischen Multicast-Eintrag bearbeiten", "size_limitation": "Die maximale Anzahl statischer Multicast-Einträge für dieses Gerät beträgt {{ size }}."}, "gmrp": {"page_title": "GMRP", "group_restrict": "Gruppenbeschränkung"}, "time_sync": {"page_title": "Zeitsynchronisation", "page_title_abbr": "Zeitsynchronisierung", "mds_m2_insert_warning": "To use Time Synchronization, a compatible module must be inserted in the M2 slot.", "profile": "Profil", "8021as": "IEEE 802.1AS-2011-Profil", "8021as_abbr": "IEEE 802.1AS-2011", "1588default": "IEEE 1588 Default-2008-Profil", "1588default_abbr": "IEEE 1588 Default-2008", "iec61850": "IEC 61850-9-3-2016 Profil", "iec61850_abbr": "IEC 61850-9-3-2016", "c37238": "IEEE C37.238-2017-Profil", "c37238_abbr": "IEEE C37.238-2017", "priority_number": "Priorität {{ number }}", "clock_type": "Clock Type", "clock_type_bc": "Boundary Clock", "clock_type_tc": "Transparent Clock", "delay_mechanism": "Verzögerungsmechanismus", "e2e": "End-to-End", "p2p": "Peer-to-Peer", "transport_mode": "Transportmodus", "8023ehternet": "IEEE 802.3 Ethernet", "udp_ipv4": "UDP IPv4", "udp_ipv6": "UDP IPv6", "domain_number": "Domainnummer", "clock_mode": "Clock Mode", "two_step": "Two-step", "one_step": "One-step", "accuracy_alert": "Genauigkeitsalarm", "bmca": "BMCA", "bmca_hint": "Best Master Clock Algorithm (BMCA) verhindert zyklische Pfadschleifen bei Verwendung von Transparent Clock. Wir empfehlen, diese Funktion zu aktivieren.", "max_steps_removed": "Maximale Schritte entfernt", "grandmaster_id": "Grandmaster ID", "announce_interval": "Intervall ankündigen", "announce_receipt_timeout": "Zeitüberschrei. beim Empfang", "sync_interval": "Synchronisierungsintervall", "sync_receipt_timeout": "Zeitüberschreitung beim Synchronisierungsempfang", "delay_req_interval": "Intervall der Verzögerungsanforderung", "pdelay_req_interval": "Pdelay-Request-Intervall", "neighbor_rate_ratio": "Neighbor Rate Ratio", "neighbor_prop_delay": "Neighbor Propagation Delay", "path_delay": "Pfadverzögerung", "neighbor_prop_delay_thresh": " Neighbor Propagation Delay Threshold", "synchronization_status": "Synchronisierungsstatus", "transport_type": "Transportart", "current_data_set": "Aktueller Datensatz", "parent_data_set": "Übergeordneter Datensatz", "locked": "<PERSON><PERSON><PERSON><PERSON>", "unlocked": "Entsperrt", "freerun": "<PERSON><PERSON>", "syncing": "Synchroni<PERSON><PERSON>", "browser_time": "Browserzeit", "ptp_clock_time": "PTP-Uhrzeit (TAI)", "ptp_slave_port": "PTP-Slave-Port", "offset_from_master": "Offset vom Master", "mean_path_delay": "Mittlere Pfadverzögerung", "steps_removed": "<PERSON><PERSON><PERSON> entfernt", "parent_identity": "Übergeordnete Identity", "grandmaster_identity": "Grandmaster Identity", "cumulative_rate_ratio": "Kumulative Rate Ratio", "grandmaster_priority_number": "Grandmaster Priority {{ number }}", "grandmaster_clock_class": "Grandmaster Clock Class", "grandmaster_clock_accuracy": "Grandmaster Clock Accuracy", "8021as_capable": "802.1AS-fähig", "initializing": "Initialisierung ...", "res_ptp_initializing_done": "Der PTP-Dienst ist bereit.", "faulty": "Fehlerhaft", "pre_master": "PreMaster", "uncalibrated": "Unkalibriert", "sync_transmit": "Synchronisierte Übertragung", "sync_receive": "Synchronisierter Empfang", "edit_port_profile_setting": "Bearbeiten des Ports {{ portIndex }} IEEE 1588v2 Standard-Profileinstellungen"}, "stream_adapter": {"page_title": "Prioritätsmanagement", "pcp": "Priority Code Point (PCP)", "ingress_table_limit_hint": "Jeder Port unterstützt maximal 10 Einträge.", "port_size_limitation": "Die maximale Anzahl an Einträgen (10) für diesen Port wurde erreicht.", "hexadecimal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "egress_untag": "Egress Untag", "ingress": "Ingress", "egress": "Egress", "per_stream_priority_title": "Per-stream Priority", "port_default_priority_title": "Port-Standardpriorität", "per_stream_priority_hint": "Streams ohne Tags werden basierend auf der Priorität pro Stream verarbeitet, wenn sie benutzerdefinierten Regeln entsprechen. Wenn die Streams keiner Regel entsprechen, werden sie basierend auf der Standard-Portpriorität verarbeitet.", "add_stream_adapter_entry": "Einen Stream-Prioritätseintrag hinzufügen", "edit_stream_adapter_entry": "Bearbeiten Sie diesen Prioritätseintrag pro Stream", "edit_port_def_priority": "Bearbeiten Sie die Standardpriorität des Ports {{ portIndex }}"}, "static_route": {"page_title": "Statisches Routing", "size_limitation": "Die maximale Anzahl statischer Routen für dieses Gerät beträgt {{ size }}.", "next_hop_IP": "Nächste Hop-IP", "next_hop_interface": "Next Hop-Schnittstelle", "delete_static_route_desc": "Sind <PERSON> sicher, dass Sie die ausgewählte Route löschen möchten?", "next_hop_type": "Nächster Hop-Typ *", "next_hop_type_hint": "<PERSON>e können eine zuvor erstellte VLAN-Schnittstelle auswählen oder dieses Feld leer lassen.", "next_hop_two_field_invalid": "<PERSON><PERSON> müssen entweder die IP oder die Schnittstelle des nächsten Hops angeben", "add_static_route": "Eine statische Route erstellen", "edit_static_route": "Diese statische Route bearbeiten", "delete_static_route": "Diese statische Route löschen"}, "routing_table": {"page_title": "Routing-<PERSON><PERSON><PERSON>", "static": "Statisch", "next_hop": "Nächster Hop", "ad_metric": "AD/Metrik"}, "online_accounts": {"page_title": "Online-Konten", "idle_time": "Leerlaufzeit", "remove_account_dialog_title": "Dieses Online-Konto entfernen", "remove_account_dialog_desc": "Sind <PERSON> sic<PERSON>, dass Sie dieses Online-Konto entfernen möchten?"}, "8021qbv": {"page_title": "Time-aware <PERSON><PERSON><PERSON>", "cycle_time": "Zykluszeit", "start_time_hint": "Der zeitbewusste Shaper basiert auf der aktuellen PTP-Zeit. Sie können die Startzeit bestimmen oder jetzt die Zeit einstellen, die bestimmt, wann die Funktion gestartet wird.", "config_change_time": "Konfigurationsänderungszeit", "default_setting": "Standardeinstellungen", "gate_control_list": "Gate-Kontrollliste", "totla_slot": "Gesamtzahl der Slots", "interval": "Intervall", "selected_queue_summary": "Ausgewählte Warteschlangen-Zusammenfassung", "interval_hint": "Das Gate-Intervall gibt an, wann das Gate geöffnet wird und wie lange es", "interval_hint_1G": "1G-Ports: 1µs,", "interval_hint_100M": "100 Mio. Ports: 10µs,", "interval_hint_10M": "10 Mio. Ports: 100µs", "port_status": "Port {{ portIndex }} Status", "select_port": "Wählen Sie den Port aus"}, "ospf": {"page_title": "OSPF", "ospf_settings": "OSPF-Einstellungen", "ospf_status": "OSPF-Status", "area": "<PERSON><PERSON><PERSON>", "neighbor": "Neighbor", "aggregation": "Aggregation", "virtual_link": "Virtueller Link", "router_id": "Router-ID", "current_router_id": "Aktuelle Router-ID", "current_router_id_hint": "<PERSON>n die Router-ID auf 0.0.0.0 eingestellt ist, wird die niedrigste Schnittstellen-IP-Adresse automatisch als Router-ID zugewiesen.", "compatible_rfc_1583": "RFC 1583-Kompatibilität", "spf_hold_time": "SPF-Haltezeit", "redistribute": "Umverteilen", "metric": "<PERSON><PERSON><PERSON>", "ospfRRDStatic": "Statisch", "ospfRRDConnected": "Verbunden", "ospfRRDRip": "RIP", "area_size_limitation": "Die maximale Anzahl von Bereichen für dieses Gerät beträgt {{ size }}.", "area_id": "Bereichs-ID", "area_type": "Bereichstyp", "normal": "Normal", "stub": "<PERSON><PERSON>", "nssa": "NSSA", "summary": "Summary", "no_summary": "No Summary", "delete_area_desc": "Sind <PERSON> sic<PERSON>, dass Sie den ausgewählten Bereich löschen möchten?", "dead_interval": "Dead Interval", "cost": "<PERSON><PERSON>", "network_type": "Netzwerkart", "passive_interface": "Passive Schnittstelle", "neighbor_ip_address": "Neighbor IP Address", "summary_hint": "Zusammenfassung ist nicht verfügbar, wenn der Bereichstyp auf Normal eingestellt ist.", "broadcast": "Broadcast", "non_broadcast": "Non-broadcast", "point_to_point": "Point-to-point", "point_to_multipoint": "Point-to-multipoint", "nbr_ip_address": "Neighbor IP Address", "nbr_size_limitation": "Die maximale Anzahl an Nachbarn für dieses Gerät beträgt {{ size }}.", "delete_nbr_desc": "Sind <PERSON> sic<PERSON>, dass Sie den ausgewählten Nachbarn löschen möchten?", "lsa_type": "LSA-Typ", "type_7": "Type 7", "aggregation_size_limitation": "Die maximale Anzahl von Aggregationen für dieses Gerät beträgt {{ size }}.", "delete_aggregation_desc": "Sind <PERSON> sicher, dass Sie die ausgewählte Aggregation löschen möchten?", "vLink_size_limitation": "Die maximale Anzahl virtueller Links für dieses Gerät beträgt {{ size }}.", "delete_vLink_desc": "Sind <PERSON> sic<PERSON>, dass Sie den ausgewählten virtuellen Link löschen möchten?", "loopback": "Loopback", "waiting": "<PERSON><PERSON>", "dr": "DR", "bdr": "BDR", "dr_other": "DR Andere", "dr_router_id": "DR-Router-ID", "bdr_router_id": "BDR-Router-ID", "neighbor_id": "Neighbor ID", "neighbor_state": "Neighbor State", "dead_time": "Dead Time", "accempt": "Versuch", "two_way": "2-<PERSON><PERSON>", "exstart": "Exstart", "exange": "<PERSON><PERSON><PERSON><PERSON>", "loading": "Wird geladen", "full": "Voll", "database": "Datenbank", "link_id": "Link-ID", "adv_router": "ADV-Router", "age": "Alter", "events": "<PERSON><PERSON><PERSON><PERSON>", "ls_retrans_queue_len": "LSA Retransmission Queue Length", "hello_suppressed": "<PERSON><PERSON>", "router": "Router", "asbr_summary": "ASBR-Zusammenfassung", "as_external": "AS Extern", "group_member": "Gruppenmitglied", "nssa_external": "NSSA Extern", "title_edit_ospf_redistribute": "Bearbeiten Umverteilen {{ protocol }}", "title_create_ospf_area": "<PERSON><PERSON><PERSON>", "title_edit_ospf_area": "<PERSON>sen Bereich bearbeiten", "title_delete_ospf_area": "Diesen Bereich löschen", "title_create_ospf_nbr": "<PERSON><PERSON><PERSON> einen Nachbarn", "title_edit_ospf_nbr": "<PERSON><PERSON> Na<PERSON>barn bearbeiten", "title_delete_ospf_nbr": "<PERSON>sen Nachbarn löschen", "title_create_ospf_aggregation": "Eine Aggregation erstellen", "title_edit_ospf_aggregation": "Diese Aggregation bearbeiten", "title_delete_ospf_aggregation": "Diese Aggregation löschen", "title_create_ospf_vlink": "<PERSON><PERSON><PERSON><PERSON> einen virtuellen Link", "title_edit_ospf_vlink": "<PERSON><PERSON> bearbeiten", "title_delete_ospf_vlink": "<PERSON>sen virtuellen Link löschen"}, "vrrp": {"page_title": "VRRP", "v2": "V2", "v3": "V3", "virtual_router_enable": "Virtueller Router", "vrid": "VRID", "decrement": "Dekrementieren", "primary_ip": "Virtuelle Router-IP-Adresse", "adv_int": "Advertisement Interval", "preempt_mode": "Preempt-Modus", "preempt_delay": "<PERSON><PERSON><PERSON>", "accept_mode": "Accept Modus", "auth_key": "Authentifizierungsschlüssel", "size_limitation": "Die maximale Anzahl an VRRP-Einträgen für dieses Gerät beträgt {{ size }}.", "delete_vrrp_desc": "Sind <PERSON> sic<PERSON>, dass Sie den ausgewählten virtuellen Router löschen möchten?", "master_address": "Master-<PERSON><PERSON><PERSON>", "master_adv_int": "Master Advertisement Interval (ms)", "master_down_int": "Master Down Intervall (ms)", "title_add_vrrp": "<PERSON><PERSON><PERSON><PERSON> einen virtuellen Router", "title_edit_vrrp": "<PERSON><PERSON> virtuellen Router bearbeiten", "title_delete_vrrp": "<PERSON>sen virtuellen Router löschen", "require_decrement_less_than_priority": "Der Wert muss kleiner sein als der Prioritätswert."}, "dns": {"page_title": "DNS-Einstellungen", "primary_dns_server": "Primärer DNS Server", "secondary_dns_server": "Zweiter DNS Server", "dns_server_number": "IP-Adresse des DNS-Servers{{ number }}", "dns_server": "DNS Server", "dns_reverse_lookup": "DNS Reverse Lookup", "zone_table": "Zonentabelle", "dns_table_for_name": "DNS-<PERSON><PERSON><PERSON> für {{ zoneName }}", "dns_server_summary": "DNS-Server-Zusammenfassung", "fqdn": "FQDN", "fqdn_hint": "FQDN (vollständig qualifizierter Domänenname) ist \"Hostname\".\"Domänenname\"", "dns_forwarding": "DNS Forwarding", "dns_forwarding_hint": "An active DNS server is required to enable DNS Forwarding.", "default_forwarder_ip": "Default Forwarder IP Address", "default_forwarder_ip_hint": "If the default forwarder is specified, DNS queries for zones not listed in the Forwarders Table will be forwarded to the default forwarder.", "forwarder_ip": "Forwarder IP Address", "forwarders_table": "Forwarders Table", "zone_hint": "'.' can be used to forwarding any Zone.", "zone_size_limitation": "Die maximale Anzahl an Zoneneinträgen beträgt {{ size }}.", "dns_size_limitation": "Die maximale Anzahl an DNS-Einträgen beträgt {{ size }}.", "title_create_zone": "Eine Zone erstellen", "title_edit_zone": "Bearbeiten {{ zoneName }} Einstellungen", "title_delete_zone": "Zone(n) löschen", "delete_zone_desc": "<PERSON>d <PERSON> sic<PERSON>, dass Sie die ausgewählte(n) Zone(n) löschen möchten?", "title_create_dns": "Ressourcendatensatz erstellen für {{ zoneName }}", "title_edit_dns": "Ressourcendatensatz bearbeiten für {{ zoneName }}", "title_delete_dns": "Ressourceneintrag(e) löschen", "delete_dns_desc": "Sind <PERSON> sicher, dass Sie die ausgewählten Ressourceneinträge löschen möchten?", "title_create_forwarding": "Create a DNS Forwarding entry", "title_edit_forwarding": "Edit DNS Forwarding entry", "title_delete_forwarding": "Delete DNS Forwarding entry", "delete_forwarding_desc": "Are you sure you want to delete the selected DNS Forwarding entry?", "duplicate_hostname": "The same hostname has already exist", "duplicate_domain_name": "The same domain name has already exist", "duplicate_zone": "This zone already exists"}, "acl": {"page_title": "Zugriffskontrollliste", "access_list_type": "Zugriffslistentyp", "access_list_type_hint": "Für denselben Index hat die MAC-Adresse eine höhere Priorität als die IP-Adresse.", "access_list_index_hint": "Ein niedrigerer Index bedeutet eine höhere Priorität.", "ip_based": "IP-based", "mac_based": "MAC-based", "acl_size_limitation": "Die maximale An<PERSON>hl von ACL-Einträgen beträgt {{ size }}.", "acl_ip_based_size_limitation": "Die maximale Anzahl IP-basierter ACL-Regeln beträgt {{ size }}.", "acl_mac_based_size_limitation": "Die maximale Anzahl MAC-basierter ACL-Regeln beträgt {{ size }}.", "acl_rule_size_limitation": "Die maximale An<PERSON><PERSON> von ACL-Regeln beträgt {{ size }}.", "active_interface_type": "Aktiver Schnittstellentyp", "vlan_based": "VLAN-b<PERSON>ert", "port_based": "Port-basiert", "active_ingress_vlan": "Aktives Ingress-VLAN", "active_egress_vlan": "Aktives Egress-VLAN", "active_ingress_port": "Aktive Ingress Ports", "active_egress_port": "Aktive Egress-Ports", "ingress_setting_hint": "Aktives VLAN und Regel-VLAN müssen identisch sein.", "egress_setting_hint": "Regeln mit einer Redirect-Aktion können nicht auf Egress-Schnittstellen angewendet werden.", "ingress_setting_vlan_hint": "Aktives VLAN und Regel-VLAN müssen identisch sein. Regeln mit einer Remark-Aktion können nicht auf Ingress-Schnittstellen angewendet werden.", "egress_setting_vlan_hint": "Aktives VLAN und Regel-VLAN müssen identisch sein. Regeln mit einer Umleitungsaktion können nicht auf Egress-Schnittstellen angewendet werden.", "rule_type": "Regeltyp", "index_priority_hint": "Regeln mit einem niedrigeren Index haben eine höhere Priorität.", "acl_rule": "ACL-Regel", "rule": "Regel", "rule_index": "Regel-Index {{ruleIndex}}", "permit": "Permit", "deny": "<PERSON><PERSON>", "ethertype_value": "EtherType-Wert", "goose": "GANS", "smv": "SMV", "protocol_number": "Protokollnummer", "user_defined": "User-defined", "source": "<PERSON><PERSON>", "source_port": "Quell-Port", "source_ip_addr": "Quell-IP-Adresse", "source_ip_mask": "Quell-IP-Maske", "source_mac_addr": "Quell-MAC-Adresse", "source_mac_mask": "Quell-MAC-Maske", "destination_port": "Ziel-Port", "destination_ip_addr": "Ziel-IP-Adresse", "destination_ip_mask": "Ziel-IP-Maske", "destination_mac_addr": "Ziel-MAC-Adresse", "destination_mac_mask": "Ziel-MAC-Maske", "cos_remark": "CoS-Bemerkung", "dscp_remark": "DSCP-Bemerkung", "optional_parameter": "Optionaler Parameter", "log": "Protokoll", "logging": "Protokollierung", "logging_enable": "Protokollierung aktivieren", "src_port": "Quell-Port", "dst_port": "Ziel-Port", "icmp_type": "ICMP-Typ", "icmp_code": "ICMP-Code", "igmp_type": "IGMP-Typ", "redirect": "Umleiten", "redirect_mirror": "Umleiten/Spiegeln", "redirect_enable": "Umleiten", "redirect_port": "Port umleiten", "redirect_port_name": "Umleitung zu Port {{ portName }}", "mirror": "<PERSON><PERSON><PERSON>", "session_id": "Sitzung {{id}}", "mirror_disable_hint": "Die Spiegelungsaktion ist nicht verfügbar, wenn die Spiegelungsfunktion am Port deaktiviert ist.", "session_disable_hint": "Die ‚Spiegel‘-Aktion kann bei einer deaktivierten Port-Spiegelsitzung nicht wirksam werden.", "mirror_sesstion": "Spiegel zur Sitzung {{ sesstionId }}", "remark_cos": "Bemerkung CoS an {{ cos }}", "remark_dscp": "Bemerkung DSCP an {{ dscp }}", "acl_table_of_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{ aclName }}", "delete_acl_list_desc": "Sind <PERSON> sic<PERSON>, dass Sie die ausgewählte(n) Zugriffskontrollliste(n) löschen möchten?", "delete_acl_rule_desc": "Sind <PERSON> sic<PERSON>, dass Sie die ausgewählte(n) Regel(n) löschen möchten?", "any_hint": "<PERSON>n kein <PERSON>rt eingegeben wird, wird davon ausgegangen, dass er auf \"Beliebig\" gesetzt ist.", "log_interval": "Protokollierungsintervall", "log_threshold": "Protokollierungsschwelle", "acl_summary": "ACL-Zusammenfassung", "number_of_activate_acl": "<PERSON>zahl der aktivierten ACLs (Max. 16)", "activate_direct": "<PERSON><PERSON><PERSON>", "ingress": "Ingress", "egress": "Egress", "both": "<PERSON><PERSON>", "activated": "Aktiviert", "inactivated": "Deaktiviert", "hit_count": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "counter": "<PERSON><PERSON><PERSON>", "view_list": "Liste anzeigen", "view_by_acl": "Nach ACL anzeigen", "view_by_port": "Ansicht nach Port", "view_by_vlan": "Ansicht nach VLAN", "acl_table_of_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{typeIndex}}", "no_activated_acl_port": "Auf diesem Port ist keine ACL aktiviert.", "no_activated_acl_vlan": "In diesem VLAN ist keine ACL aktiviert.", "status_hint": "Regeln mit einem niedrigeren Index haben eine höhere Priorität. Das Gerät beginnt mit der Zuordnung der Pakete zu allen Regeln in numerischer Reihenfolge, beginnend mit dem niedrigsten Index. Wenn das Paket einer Regel entspricht, wird die entsprechende Regel angewendet.", "title_create_access_list": "Zugriffsliste erstellen", "title_edit_access_list": "Bearbeiten {{ typeIndex }} Zugriffslisteneinstellungen", "title_delete_acl_list": "Zugriffsliste(n) löschen", "title_create_acl_rule": "Regelindex {{ ruleIndex }} für {{ typeIndex }} erstellen", "title_edit_acl_rule": "Bearbeiten Sie den Regelindex {{ ruleIndex }} von {{ typeIndex }}", "title_delete_acl_rule": "Regel(n) löschen", "title_clear_acl_counter": "Zähler löschen", "desc_clear_all_acl_counter_desc": "Sind <PERSON> sicher, dass Sie alle Zähler zurücksetzen möchten?", "desc_clear_single_acl_counter_desc": "<PERSON>d <PERSON>, dass Sie den Zähler der {{ typeIndex }} ACL zurücksetzen wollen?", "blacklist_udp_port_dhcp_server": "DHCP-Server ist nicht erlaubt", "blacklist_udp_port_dhcp_client": "DHCP-Client ist nicht erlaubt", "blacklist_udp_port_moxa_command": "Moxa-Service ist nicht erlaubt", "blacklist_ether_type_eth_confg_test_protocol": "Ethernet Configuration Testing Protocol ist nicht zulässig", "blacklist_ether_type_lldp": "LLDP ist nicht erlaubt", "blacklist_ether_type_eapol": "EAPOL ist nicht erlaubt", "blacklist_ether_type_lacp": "LACP ist nicht erlaubt", "blacklist_ether_type_llc_jumbo_frame": "LLC-Jumbo-Frame ist nicht zulässig", "blacklist_ether_type_arp": "ARP ist nicht erlaubt", "blacklist_ether_type_mrp": "MRP ist nicht erlaubt", "blacklist_ether_type_profinet": "PROFINET ist nicht erlaubt", "blacklist_ether_type_ptp": "PTP ist nicht erlaubt", "blacklist_ether_type_goose": "GOOSE ist nicht erlaubt", "blacklist_ether_type_smv": "SMV ist nicht erlaubt", "blacklist_mac_ieee_reserved_multicast": "IEEE-reservierte Multicast-MAC-Adresse ist nicht zulässig", "blacklist_mac_ip_multicast": "IP-Multicast-MAC-Adresse ist nicht zulässig", "blacklist_mac_broadcast": "Broadcast-MAC-Ad<PERSON><PERSON> ist nicht zul<PERSON>ssig", "blacklist_mac_l2_multicast": "L2-Multicast-MAC-Adresse ist nicht zulässig", "blacklist_mac_device": "Geräte-MAC-Adresse ist nicht zulässig", "blacklist_dest_ip_multicast": "Multicast-IP-Adresse ist nicht zulässig", "overwrite_vlan_dialog_title": "Regel-VLAN mit aktivem VLAN überschreiben", "overwrite_vlan_dialog_content": "Aktives VLAN und Regel-VLAN müssen identisch sein. <PERSON><PERSON>, dass das aktive VLAN das Regel-VLAN überschreiben soll?"}, "stream_id": {"page_title": "Stream-Identifikation", "title_create_stream_id": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> einen Stream", "title_edit_stream_id": "Diesen Stream bearbeiten", "title_delete_stream_id": "Stream(s) löschen", "delete_stream_id_desc": "Sind <PERSON> sicher, dass Sie die ausgewählten Streams löschen möchten?"}, "8021cb": {"page_title": "Frame Replication and Elimination for Reliability (FRER)", "frer": "FRER", "split": "Teilen", "forward": "Vorwärts", "merge": "Zusammenführen", "stream_vid_mac": "Stream (VLAN/MAC-Adresse)", "input_port": "Eingangsport", "input_ports": "Eingangsports", "output_port_index": "Ausgangsport {{ portIndex }}", "output_port": "Ausgangsport", "output_ports": "Ausgangsports", "ingress_stream": "Ingress-Stream", "egress_stream": "Egress-Stream", "vlan_overwrite": "VLAN überschreiben", "mac_address_overwrite": "MAC-Adresse überschreiben", "priority_overwrite": "Priorität überschreiben", "overwrite": "Überschreiben", "disable_port_input_hint": "Dies ist ein ausgewählter Eingangsport.", "disable_port_vlan_hint": "Dieser Port ist kein Mitglied des entsprechenden VLAN.", "disable_vid_overwrite_hint": "Der Ausgangsport ist nicht Mitglied des entsprechenden VLANs", "disable_exist_stream_hint": "<PERSON><PERSON><PERSON> diesen Stream existiert bereits ein FRER-Eintrag.", "disable_select_stream_hint": "Der Stream wurde bereits ausgewählt.", "to_end_device": "Zum Endgerät", "ingress_size_limitation": "Der maximale Eingangsstrom beträgt {{ size }}.", "title_create_frer_entry": "FRER-Eintrag er<PERSON>llen", "title_edit_frer_entry": "Diesen FRER-Eintrag bearbeiten", "title_delete_frer_entry": "Diesen FRER-Eintrag löschen", "delete_frer_entry_desc": "Sind <PERSON> sicher, dass Sie die ausgewählten FRER-Einträge löschen möchten?"}, "loop_protection": {"page_title": "Network Loop Protection", "detect_interval": "Erkennungsintervall", "loop_status": "Loop-Status", "peer_port": "Peer-Port", "looping": "Looping"}, "binding_database": {"page_title": "Bindungsdatenbank", "binding_settings": "Bindungseinstellungen", "binding_status": "Bindungsstatus", "binding_status_hint": "Dynamische Bindung lernt vom DHCP-Snooping.", "binding_status_hint_2": "Der Bindungsstatus wird nicht aktualisiert, wenn die Kombination aus VLAN-ID und MAC-Adresse des statischen Eintrags bereits vorhanden ist.", "title_create_entry": "Erstellen Sie einen statischen Bindungsdatenbankeintrag", "title_edit_entry": "Bearbeiten Sie diesen statischen Bindungsdatenbankeintrag", "duplicate_of_dynamic_entry": "Die Kombination aus VLAN-ID und MAC-Adresse ist bereits vorhanden. Dieser neue Eintrag überschreibt den ursprünglichen dynamischen Eintrag.", "size_limitation": "Die maximale <PERSON><PERSON><PERSON> von Bindungsstatuseinträgen beträgt {{ size }}.", "binding_table_max": "<PERSON>. {{ size }} der Bindungsstatustabelle", "dai": "DAI", "ipsg": "IPSG", "ipsg_dai": "IPSG, DAI"}, "dhcp_snooping": {"page_title": "DHCP-Snooping", "port_is_ip_sg_enable": "Dieser Port ist für IP Source Guard aktiviert. IP Source Guard kann nur auf einem nicht vertrauenswürdigen Port aktiviert werden.", "port_is_dai_enable": "Dieser Port ist für die dynamische ARP-Inspektion aktiviert. Die dynamische ARP-Inspektion kann nur auf einem nicht vertrauenswürdigen Port aktiviert werden.", "port_is_ip_sg_and_dai_enable": "Dieser Port ist für Dynamic ARP Inspection und IP Source Guard aktiviert. Dynamische ARP-Inspektion und IP Source Guard können nur auf einem nicht vertrauenswürdigen Port aktiviert werden."}, "ip_source_guard": {"page_title": "IP Source Guard", "port_is_trusted": "Dieser Port ist ein vertrauenswürdiger Port für DHCP-Snooping. Für IP Source Guard können nur nicht vertrauenswürdige Ports aktiviert werden.", "port_is_la_member": "Dieser Port ist Mitglied des Port-Channels. IP Source Guard kann auf dem Mitglieds-Port nicht aktiviert werden.", "binding_status_single_empty": "Der Bindungsstatus des Ports {{ port }} ist leer.", "binding_status_multiple_empty": "Die Bindungsstatus des Ports {{ port }} sind leer.", "binding_setting_hint": "Sie sollten DHCP-Snooping aktivieren, um die dynamische Bindung zu erhalten, oder die Daten in der Bindungsdatenbank -> Bindungseinstellungen konfigurieren."}, "mms": {"page_title": "MMS", "ied_name": "IED-Name", "cid_file_settings": "CID-Dateieinstellungen", "report_control_block": "Ko<PERSON><PERSON>block melden", "data_change": "Datenänderung", "data_update": "Datenaktualisierung", "quality_change": "Qualität ändern", "integrity": "Integrität", "buffer_time": "Pufferzeit", "integrity_period": "Integritätszeitraum", "t_profile_cert_info": "Informationen zum T-Profil-Zertifikat", "a_profile_cert_info": "Informationen zum A-Profile-Zertifikat", "ca_name": "CA-Name", "t_profile_security": "T-Profil-Sicherheit", "a_profile_security": "A-Profil-Sicherheit", "import_client_ca": "Client-CA importieren", "import_client_cert": "Client-Zertifikat importieren", "title_edit_name": "{{ name }} bearbeiten", "title_mms_enable_warning": "Aktivieren Sie das MMS-Protokoll", "mms_enable_warning_desc": "<PERSON>d <PERSON> sic<PERSON>, dass Sie das nicht sichere Protokoll (MMS) aktivieren möchten?"}, "password_policy": {"page_title": "Kennwortrichtlinie", "minimum_length": "Mindestlänge des Passworts", "policy_numbers": "<PERSON>ss mindestens e<PERSON> (0-9) enthalten", "policy_uppercase": "Muss mindestens einen Großbuchstaben (AZ) enthalten", "policy_lowercase": "Muss mindestens einen Kleinbuchstaben (az) enthalten", "policy_symbols": "Muss mindestens ein Sonderzeichen enthalten ({}[]()|:;~!@#%^*-_+=,.)", "max_life_time": "Maximale Gültigkeitsdauer des Passworts", "password_complexity_strengh_check": "Überprüfung der Passwortkomplexität"}, "system_info": {"page_title": "Informationseinstellungen", "system_name": "G<PERSON><PERSON><PERSON><PERSON>", "contact_information": "Kontaktinformationen", "sync_to_chassis_id_hint": "Wenn der LLDP-Chassis-ID-Subtyp auf „local“ eingestellt ist, führt das Ändern des Gerätenamens zur gleichzeitigen Änderung der LLDP-Chassis-ID."}, "login_authentication": {"page_title": "Login-Authentifizierung", "authentication_protocol": "Authentifizierungsprotokoll", "radius_local": "RADIUS, Local", "tacacs_local": "TACACS+, Local"}, "login_policy": {"page_title": "Anmelderichtlinie", "login_message": "Anmeldenachricht", "auth_fail_message": "Meldung über fehlgeschlagene Anmeldung", "failure_lockout": "Sperre bei fehlgeschlagener Anmeldung", "retry_failure_threshold": "Schwellenwert für Wiederholungsversuche", "lockouttime": "<PERSON><PERSON><PERSON><PERSON>", "auto_logout_setting": "Automatische Abmeldung nach", "auto_logout_warring_title": "Automatische Abmeldung deaktivieren", "auto_logout_setting_alert": "Wenn der Wert für die automatische Abmeldung auf 0 gesetzt ist, kommt es nie zu einer Zeitüberschreitung der Sitzung. Bitte melden Si<PERSON> sich ab, bevor <PERSON> Ihren Browser schließen."}, "ip_settings": {"page_title": "IP-Konfiguration", "ip_settings": "IP-Einstellungen", "ip_status": "IP-Status", "get_ip_from": "IP abrufen von", "dns_server": "DNS-Server-IP-Adresse", "ipv6": "IPv6", "ipv6_global_unicast_address_prefix": "IPv6-Präfix für globale Unicast-Adressen", "ipv6_dns_server_number": "IPv6-DNS-Server {{ number }}", "ipv6_dns_server": "IPv6-DNS-Server", "ipv6_global_unicast_address": "IPv6 Globale Unicast-Adresse", "ipv6_link_local_address": "IPv6 Link-Local-Adresse", "profinet_dcp": "PROFINET DCP", "dhcp_bootfile": "DHCP-Bootdatei", "dhcp_bootfile_hint": "<PERSON>n diese Option aktiviert ist, lädt das System automatisch die Konfigurationseinstellungen der in Option 67 beschriebenen Bootdatei von dem in Option 66 beschriebenen Dateiserver herunter und stellt sie wieder her.", "dhcp_client": "DHCP-Client-<PERSON><PERSON><PERSON>", "dhcp_client_hint": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, sendet das System DHCP-Client-Nachrich<PERSON> mit einem Option-61-Tag, das eine Client-ID enthält. Der DHCP-Server weist die mit dem Client-ID-Wert verknüpfte IP-Adress<PERSON> zu, sofern verfügbar.", "dhcp_client_type": "DHCP-Client-Kennungstyp", "dhcp_client_value": "DHCP-Client-Kennungswert"}, "management_interface": {"page_title": "Verwaltungsschnittstelle", "user_interface": "Benutzeroberfläche", "interface": "Schnittstelle", "enable_http": "HTTP", "http_port": "HTTP - TCP-Port", "enable_https": "HTTPS", "https_port": "HTTPS - TCP-Port", "enable_telnet": "Telnet", "telnet_port": "Telnet - TCP-Port", "ssh_port": "SSH - TCP-Port", "enable_snmp_V1V2c": "SNMP-Version V1, V2c", "snmp_protocol": "SNMP - Transport Layer Protocol", "udp": "UDP", "tcp": "TCP", "snmp_udp_port": "SNMP - UDP-Port", "snmp_tcp_port": "SNMP - TCP-Port", "enable_moxa_service": "Moxa-Service", "moxa_tcp_port": "Moxa-Dienst (verschlüsselt) - TCP-Port", "moxa_udp_port": "Moxa-Dienst (verschlüsselt) - UDP-Port", "max_session_http": "Maximale Anzahl von Anmeldesitzungen für HTTP+HTTPS", "max_session_terminal": "Maximale Anzahl von Anmeldesitzungen für Telnet+SSH", "enable_nonsecure_interface_warning_title": "Aktivieren Sie die {{ interfaceType }} Schnittstelle", "enable_nonsecure_interface_warning": "<PERSON>d <PERSON> sic<PERSON>, dass Sie die unsichere Schnittstelle ({{ interfaceType }}) aktivieren möchten?"}, "hareward_interface": {"page_title": "Hardware-Schnittstellen", "dip_switch": "<PERSON><PERSON>", "usb_function": "USB-Schnittstelle", "micro_sd_function": "MicroSD-Schnittstelle"}, "account_management": {"page_title": "Benutzerkonten", "admin": "Admin", "supervisor": "Supervisor", "user": "User", "new_password": "Neues Passwort", "title_edit_account": "<PERSON><PERSON>", "title_add_account": "Ein neues Konto erstellen", "title_edit_account_password": "Kontopasswort bearbeiten", "new_pwd_not_match": "Das Passwort stimmt nicht überein.", "tech_account_add_error": "Das Konto \"moxasupport\" kann nicht erstellt werden, da es für den technischen Support von Moxa reserviert ist.", "tech_account_remove_error": "Das Konto \"moxasupport\" kann nicht bearbeitet oder entfernt werden, da es für den technischen Support von Moxa reserviert ist.", "account_name_taken": "Der Benutzername dieses Kontos ist bereits vergeben", "size_limitation": "Die maximale Anzahl an Benutzerkonten für dieses Gerät beträgt {{ size }}."}, "time": {"page_title": "Systemzeit", "sntp": "SNTP", "ntp": "NTP", "time_zone": "Zeitzone", "current_time": "Aktuelle Uhrzeit", "daylight_saving": "Sommerzeit", "end_date": "Enddatum", "offset": "Offset", "ntp_authentication": "NTP-Authentifizierung", "query_interval": "Query Interval", "ptp": "PTP", "start": "Start", "end": "<PERSON><PERSON>", "date": "Datum", "month": "<PERSON><PERSON>", "week": "<PERSON><PERSON><PERSON>", "day": "Tag", "hour": "Stunde", "minute": "Minute", "jan": "<PERSON><PERSON><PERSON>", "feb": "<PERSON><PERSON><PERSON>", "mar": "<PERSON><PERSON>", "apr": "April", "may": "<PERSON>", "jun": "<PERSON><PERSON>", "jul": "<PERSON><PERSON>", "aug": "August", "sep": "September", "oct": "Oktober", "nov": "November", "dec": "Dezember", "1st": "1", "2nd": "2.", "3rd": "3.", "4th": "4.", "last": "letzte", "sun": "Sonntag", "mon": "Montag", "tue": "Dienstag", "wed": "Mittwoch", "thu": "Don<PERSON><PERSON>", "fri": "Freitag", "sat": "Samstag", "time_server_number": "Zeitserver {{ number }}", "time_server_1": "1. Zeitserver: IP-Adresse/Domänenname", "time_server_2": "2. Zeitserver: IP-Adresse/Domänenname", "clock_source": "<PERSON><PERSON><PERSON>", "key_string": "Schlüsselzeichenfolge", "delete_entry_confirm_desc": "Sind <PERSON> sic<PERSON>, dass Sie die ausgewählte(n) Schlüsselzeichenfolge(n) löschen möchten?", "size_limitation": "Die maximale Anzahl an NTP-Authentifizierungsschlüsseln für dieses Gerät beträgt {{ size }}."}, "ntp_server": {"page_title": "NTP-Server"}, "ssh_ssl": {"page_title": "SSH & SSL", "ssh": "SSH", "ssl": "SSL", "regen_ssh_key": "SSH-Schlüssel neu generieren", "regen_ssl_cert": "SSL-Zertifikat neu generieren", "export_ssl_cert": "SSL-Zertifikat exportieren", "import_ssl_cert": "Importzertifikat", "ssl_info": "Zertifikatsinformationen", "ca_name": "CA-Name", "title_export_ssl_certificate": "SSL-Zertifikat exportieren"}, "dhcp": {"page_title": "DHCP-Server", "dhcp": "DHCP", "ntp_server": "NTP-Server-IP-Adresse", "dhcp_pool_settings": "DHCP-Serverpooleinstellungen", "static_ip_assignment_table": "Static IP Assignment Table", "start_ip": "Start-IP-<PERSON><PERSON><PERSON>", "end_ip": "End-IP-<PERSON><PERSON><PERSON>", "lease_time": "Lease Time", "hostname": "Hostname", "log_server": "IP-Adresse des Protokollservers", "gateway": "Gateway", "matching_rule": "Matching Rule", "client_id_type": "Client-Identifier Type", "client_id_value": "Client-Identifier Value", "circuit_id_type": "Option 82 Circuit ID Type", "circuit_id_value": "Option 82 Circuit ID Value", "remote_id_type": "Option 82 Remote ID Type", "remote_id_value": "Option 82 Remote ID Value", "hostname_hint": "Der Hostname stellt den Namen des DHCP-Clients dar und wird in das Option 12-Tag des DHCP-Angebotspakets kodiert.", "time_left": "Verbleibende Zeit", "dhcp_ip_mac": "DHCP/MAC-basierte IP-Zuweisung", "dhcp_static_ip": "DHCP/Static IP Assignment", "lease_table": "<PERSON><PERSON>", "ip_mac_binding": "MAC-basierte IP-Zuweisung", "ip_port_binding": "Portbasierte IP-Zuweisung", "classless_static_route_table": "Klassenlose statische Routentabelle", "delete_dhcp_entry_confirm_desc": "Möchten Sie diesen DHCP-Serverpool wirklich löschen?", "delete_static_ip_entry_confirm_desc": "Are you sure you want to delete the selected static IP entry(s)?", "delete_ip_port_entry_confirm_desc": "Sind <PERSON> sic<PERSON>, dass Sie die ausgewählten portbasierten IP-Zuweisungen löschen möchten?", "delete_ip_mac_entry_confirm_desc": "Sind <PERSON> sic<PERSON>, dass Sie die ausgewählten MAC-basierten IP-Zuweisungen löschen möchten?", "dhcp_size_limitation": "Die maximale Anzahl an DHCP-Serverpools für dieses Gerät beträgt {{ size }}.", "invalid_dhcp_pool_range": "Ungültig: Die Verwaltungs-IP-Adresse des Switches sollte im Bereich des IP-Subnetzes liegen.", "delete_static_route_entry_confirm_desc": "Sind <PERSON> sicher, dass Sie die ausgewählte Route löschen möchten?", "default_gateway_setting_hint": "Das Standard-Gateway für klassenlose statische Routen verwendet die Standard-Gateway-Adresse, die im Konfigurationsabschnitt \"Portbasierte IP-Zuweisung\" konfiguriert wurde."}, "dhcp_relay": {"page_title": "DHCP-Re<PERSON>-Agent", "option82": "Option 82", "server1": "IP-Adresse 1. Server", "server2": "IP-Adresse 2. Server", "server3": "IP-Adresse 3. Server", "server4": "IP-Adresse 4. Server", "remote_id_type": "Remote-ID-Typ", "remote_id_value": "Remote-ID-Wert", "remote_id_display": "Fern-ID-Anzeige", "client_id": "Client ID", "relay": "<PERSON><PERSON><PERSON>"}, "ping": {"page_title": "<PERSON>", "ping_result": "Ping {{ targetHost }} Ergebnis"}, "email_settings": {"page_title": "E-Mail-Einstellungen", "tls_enable": "TLS", "sender_address": "Absenderad<PERSON>e", "recipient_1": "1. <PERSON><PERSON><PERSON>nger-E-Mail-Adresse", "recipient_2": "2. <PERSON><PERSON><PERSON><PERSON>-E-Mail-Adresse", "recipient_3": "3. <PERSON><PERSON><PERSON><PERSON>-E-Mail-Adresse", "recipient_4": "4. <PERSON><PERSON><PERSON><PERSON>-E-Mail-Adresse", "recipient_5": "5. <PERSON><PERSON><PERSON><PERSON>-E-Mail-Adresse"}, "snmp": {"page_title": "SNMP", "v1": "V1, V2c, V3", "v2c": "V1, V2c", "v3": "V3 Only", "snmp_version": "SNMP-Version", "snmp_account": "SNMP-Konto", "read_community": "Read Community", "read_write_community": "Read/Write Community", "read_write": "Read/Write", "des": "DES", "aes": "AES", "snmp_account_size_limitation": "Die maximale Anzahl an SNMP-Konten für dieses Gerät beträgt {{ size }}.", "snmp_warning_dialog_V1V2c_title": "Legen Sie die SNMP-Version fest", "snmp_warning_dialog_V1V2c_desc": "<PERSON>d <PERSON><PERSON> sic<PERSON>, dass Sie die nicht sichere Schnittstelle (SNMP-Version V1, V2c) aktivieren möchten?", "snmp_warning_dialog_authMD5_title": "Legen Sie den Authentifizierungstyp MD5 fest", "snmp_warning_dialog_authMD5_desc": "Die MD5-Authentifizierung bietet nur begrenzte Sicherheit. Sind Si<PERSON> sicher, dass Sie fortfahren möchten?", "change_password_title": "Ändern Sie das Authentifizierungspasswort", "change_key_title": "Ändern Sie den Verschlüsselungsschlüssel", "change_key_button": "VERSCHLÜSSELUNGSSCHLÜSSEL ÄNDERN", "create_v3_account": "<PERSON><PERSON><PERSON><PERSON> ein SNMP-Konto", "edit_v3_account": "Dieses SNMP-<PERSON><PERSON> bear<PERSON>ten"}, "snmp_trap": {"page_title": "SNMP Trap/Inform", "snmp_trap_inform_recipient": "SNMP-Trap/Empfänger informieren", "snmp_inform_settings": "SNMP-Informationseinstellungen", "inform_retry": "Inform Retries", "inform_timeout": "Inform Timeout", "recipient_name": "Empfänger-IP-Adresse/Domänenname", "trap_community": "Trap Community", "snmp_trap_inform_account": "SNMP Trap/Inform Accounts", "trap_v1": "Trap V1", "trap_v2c": "Trap V2c", "inform_v2c": "Inform V2c", "trap_v3": "Trap V3", "inform_v3": "Inform V3", "title_delete_host_dialog_title": "Diesen Host löschen", "title_delete_host_dialog_desc": "Sind <PERSON> sicher, dass Si<PERSON> diesen Host löschen möchten?", "snmp_trap_account_size_limitation": "Die maximale Anzahl an SNMP-Trap/Inform-Konten für dieses Gerät beträgt {{ size }}.", "snmp_host_size_limitation": "Die maximale An<PERSON>hl von SNMP-Trap-Hosts für dieses Gerät beträgt {{ size }}.", "create_v3_trap_account": "<PERSON><PERSON><PERSON><PERSON> ein SNMP-Trap-Konto", "edit_v3_trap_account": "Dieses <PERSON>MP-Trap-<PERSON><PERSON> bearbeiten", "create_host_table": "<PERSON> erstellen", "edit_host_table": "<PERSON>sen Host bearbeiten"}, "arp": {"page_title": "ARP-Ta<PERSON><PERSON>", "title_clear_message": "Alle ARP-Einträge löschen", "clear_confirmation_message": "Sind <PERSON> sicher, dass Sie alle ARP-Einträge löschen möchten?"}, "event_log": {"page_title": "Ereignisprotokolle", "boot": "Bootup<PERSON><PERSON><PERSON><PERSON>", "progname": "Programmname", "timestamp": "Zeitstempel", "uptime": "Betriebszeit", "message": "Nachricht", "severity_emerg": "Emergency", "severity_alert": "<PERSON><PERSON>", "severity_info": "Info", "severity_debug": "Debug", "flush_log_entry_confirmation_message": "Sind <PERSON> sic<PERSON>, dass Sie alle Protokolleinträge löschen möchten?", "total_entries": "Einträge insgesamt: ", "clear_all_logs": "Alle Protokolle löschen", "capacity_warning": "Kapazitätswarnung", "capacity_warning_hint": "Die registrierte Aktion kann für einzelne Ereignisse auf der Seite \"Ereignisbenachrichtigungen\" konfiguriert werden.", "warning_threshold": "Warnschwelle", "oversize_action": "Aktion bei Überschreitung", "oversize_action_overwrite": "Overwrite the oldest event log", "oversize_action_stop": "Stop recording event logs", "title_clear_all_logs": "Alle Protokolle löschen", "debug_hint": "{{ number }} Protokolle sind für den internen Gebrauch bestimmt.", "hash_value": "Hashwert", "auto_backup_of_event_log": "Automatische Sicherung des Ereignisprotokolls"}, "trust_access": {"page_title": "Vertrauenswürdiger Zugriff", "size_limitation": "Die maximale Anzahl vertrauenswürdiger Zugriffseinträge für dieses Gerät beträgt {{ size }}.", "delete_all_warning_title": "<PERSON>s können nicht alle vertrauenswürdigen Zugriffseinträge gelöscht werden", "delete_all_warning_1": "<PERSON><PERSON><PERSON><PERSON>, er<PERSON>ert Trusted Access mindestens einen aktiven Eintrag. Es wird dringend em<PERSON>, Folgendes aufzubewahren", "delete_all_warning_2": "Ihre aktuelle IP-Adresse", "delete_all_warning_3": " als aktiver Eintrag."}, "utilization": {"page_title": "Ressourcenauslastung", "cpu_utilization": "CPU auslastung", "cpu_historical_record": "CPU-Nutzungsverlauf", "mem_utilization": "Speichernutzung", "mem_historical_record": "Verlauf der Speichernutzung", "power_utilization": "Energieverbrauch", "power_historical_record": "Verlauf des Energieverbrauchs", "last_update_time": "Letzte Aktualisierung ", "used": "<PERSON><PERSON><PERSON>", "free": "<PERSON><PERSON>", "past_10_second": "Letzte 10 Sekunden", "past_30_second": "Letzte 30 Sekunden", "past_300_second": "Letzte 300 Sekunden", "selecting_visible_polyline": "Sichtbare Polylinien bearbeiten", "polyline_display_hint": "Klicken Sie auf das Symbol in der oberen rechten Ecke des Widgets, um auszuwählen, welche Daten angezeigt werden sollen."}, "tacacs_server": {"page_title": "TACACS+ Server", "tacacs": "TACACS+", "auth_type_asc_two": "ASCII"}, "syslog_server": {"page_title": "Syslog", "syslog_server": "Syslog-Server", "auth_disable_hint": "Das Zertifikat und der Schlüssel können nicht aktiviert werden, da sie nicht vorhanden sind.", "tls": "TLS", "common_name": "Allgemeiner Name", "expireTime": "Verfallszeit", "key_limitation": "Die maximale Anzahl an Zertifizierungen und Schlüsselsätzen für dieses Gerät beträgt {{ size }}.", "title_add_key": "Zertifikat und Schlüsselsatz hinzufügen", "title_edit_key": "<PERSON><PERSON> Zertifika<PERSON> und diesen Schlüssel bearbeiten", "delete_key_desc": "Sind <PERSON> sic<PERSON>, dass Sie das Zertifikat und den Schlüssel löschen möchten?", "client_certificate": "Client-Zertif<PERSON><PERSON>", "client_key": "Client-Schlüssel", "ca_key": "CA-Schlüssel"}, "radius": {"page_title": "Radius-Server", "radius": "RADIUS", "server_address_number": "Server IP Adresse {{ number }}", "mschap": "MS-CHAPv1", "mschap_v2": "MS-CHAPv2"}, "config_bk_res": {"page_title": "Konfigurationssicherung und -wiederherstellung", "menu_title": "Sicherung und Wiederherstellung konfigurieren", "file_encryption": "Dateiverschlüsselung", "file_signature": "Datei Signatur", "config_name": "Configuration Name", "config_file_encryption": "Verschlüsselung von Konfigurationsdateien", "configuration_selection": "Konfiguration auswählen", "running_configuration": "Running Configuration", "startup_configuration": "Startup Configuration", "default_configuration": "Standardkonfiguration", "not_included": "Not Included", "included": "Included", "signed_config": "Signierte Konfiguration", "sign_hint": "<PERSON>n diese Option aktiviert ist, wird eine digitale Signatur hinzugefügt, wenn ein Administrator die Konfiguration sichert oder wiederherstellt.", "sign_disable_hint": "Diese Funktion kann nicht aktiviert werden, da der private und der öffentliche Schlüssel leer sind.", "private": "Privat", "certificate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "Etikett", "length": "<PERSON><PERSON><PERSON>", "key_limitation": "Die maximale Anzahl an Schlüsselpaaren für dieses Gerät beträgt {{ size }}.", "title_add_key": "Hinzufügen eines benutzerdefinierten Schlüssels", "title_edit_key": "<PERSON>sen benutzerdefinierten Schlüssel bearbeiten", "delete_key_desc": "Sind <PERSON> sic<PERSON>, dass Sie dieses Schlüsselpaar löschen möchten?", "auto_bk_of_config": "Automatische Konfigurationssicherung", "auto_load_of_config": "Automatische Konfigurationswiederherstellung", "auto_restore": "Automatische Konfigurationswiederherstellung", "auto_restore_hint": "<PERSON><PERSON>n Sie die Konfiguration beim Booten automatisch von einem externen Speichergerät wieder her.", "encrypt_whole_file": "Encrypt the entire file", "encrypt_sensitive_information_only": "Encrypt sensitive information only", "encrypt_hint": "If \"Encrypt sensitive information only\" is selected and the Encryption Key field is left blank, the Moxa encryption key will be used instead."}, "firmware_upgrade": {"page_title": "Firmware-Upgrade"}, "module_information": {"page_title": "Modulinformationen", "module_name": "Modulname", "no_module_msg": "<PERSON><PERSON>"}, "event_notification": {"page_title": "Ereignisbenachrichtigungen", "group": "Gruppe", "event_name": "Ereignissname", "system_and_function": "System und Funktionen", "registered_event": "<PERSON><PERSON><PERSON><PERSON>", "registered_action": "Registrierte Aktion", "registered_port": "Registrierter Port", "group_general": "Allgemein", "group_switching": "Umschalten", "group_poe": "PoE", "group_routing": "Routing", "group_tracking": "Tracking", "notification_loginSuccess": "Erfolgreiche Anmeldung", "notification_loginFail": "Anmeldung fehlgeschlagen", "notification_loginLockout": "Login lockout", "notification_accountChanged": "Kontoeinstellungen geändert", "notification_certificationChanged": "SSL-Zertifizierung geändert", "notification_passwordChanged": "Passwort geändert", "notification_coldStart": "Cold start", "notification_warmStart": "Warm start", "notification_configurationChanged": "Configuration changed", "notification_configurationImported": "Configuration imported", "notification_logCapacityThreshold": "Log capacity threshold", "notification_powerOff": "Power On->Off", "notification_powerOn": "Power Off->On", "notification_diOn": "DI on", "notification_diOff": "DI off", "notification_topologyChanged": "Topology changed", "notification_couplingChanged": "Coupling changed", "notification_masterChanged": "Master changed", "notification_masterMismatch": "Master mismatch", "notification_rstpTopologyChanged": "RSTP topology changed", "notification_rstpRootChanged": "RSTP root changed", "notification_rstpMigration": "RSTP migration", "notification_rstpInvalidBpdu": "RSTP invalid BPDU", "notification_rstpNewPortRole": "RSTP new port role", "notification_mstpTopologyChanged": "MSTP topology changed", "notification_mstpRootChanged": "MSTP root changed", "notification_mstpNewPortRole": "MSTP new port role", "notification_linkHealthyCheckFail": "Redundant port health check failed", "notification_dualHomingPathSwitched": "Dual homing path changed", "notification_dot1xAuthFail": "802.1X auth failed", "notification_lldpTableChanged": "LLDP table changed", "notification_rmonRaisingAlarm": "RMON raising alarm", "notification_rmonFallingAlarm": "RMON failing alarm", "notification_macsecInterfaceMKAFail": "MACsec MKA failed", "notification_dhcpsnpDynamicEntrySetFailed": "Binding Status dynamic entry failed", "notification_dhcpsnpUntrustMacDiscard": "DHCP client ingress discards packets due to the DHCP Snooping rule", "notification_dhcpsnpUntrustServerDiscard": "DHCP server discards packets due to the DHCP Snooping rule", "notification_multipleCouplingPathChanged": "Multiple coupling path changed", "notification_dhcpBootfileFail": "DHCP Bootfile failed", "notification_trackingStatusChanged": "Tracking Status Changed", "notification_trackingReactionPort": "Tracking Action Triggered on Port", "notification_trackingReactionStaticRoute": "Tracking Action Triggered on Static Route", "notification_trackingReactionVrrp": "Tracking Action Triggered on VRRP", "notification_pdPowerOn": "PD power on", "notification_pdPowerOff": "PD power off", "notification_lowInputVoltage": "Low input voltage", "notification_pdOverCurrent": "PD over-current", "notification_pdNoResponse": "PD no response", "notification_overPowerBudgetLimit": "Over power budget limit", "notification_powerDetectionFailure": "Power detection failure", "notification_nonPdOrPdShort": "Non-PD or PD short circuit", "notification_portOn": "Port On", "notification_portOff": "Port Off", "notification_rateLimitedOn": "Port shut down by Rate Limit", "notification_rateLimitedOff": "Port recovered by Rate Limit", "notification_psecViolationPortDown": "Port shut down by Port Security", "notification_fiberWarning": "Fiber Check warning", "notification_linkUp": "Interface up", "notification_linkDown": "Interface down", "notification_adjacencyChanged": "OSPF adjacency changed", "notification_drChanged": "OSPF DR changed", "notification_becomeDR": "OSPF become DR", "notification_vrrpMasterChanged": "VRRP virtual router master changed", "notification_pimSmDrChanged": "PIM-SM DR changed", "notification_pimSmRpAdded": "PIM-SM RP added by BSM", "notification_pimSmRpDeleted": "PIM-SM RP deleted by BSM", "notification_supABportTimediff": "A PHR Supervision frame time difference event occurred on ports A, B", "notification_gcTimeout": "GOOSE Check entry counter timeout", "notification_gcTimeoutClear": "GOOSE Check entry counter timeout clear", "notification_gcPortTampered": "GOOSE Check tampered ingress port", "notification_gcAddrTampered": "GOOSE Check tampered source MAC address", "notification_gcLockViolation": "GOOSE Check Lock violation", "notification_gcEntryReset": "GOOSE Check entry reset", "notification_gcEntryDelete": "GOOSE Check entry delete", "action_trap": "Trap", "information": "Information", "title_edit_event_notification": "Diese E<PERSON>ignisbenachrichtigung bearbeiten"}, "relay_output": {"page_title": "Relais-Alarm", "relay_alarm_cut_off": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "relay_alarm_settings": "Relais-Alarmeinstellungen", "fault_led_display": "Fehler-LED-Anzeige", "cut_off": "Abschaltung"}, "statistics": {"page_title": "Netzwerk-Statistik", "bandwidth_utilization": "Bandbreitennutzung", "packet_counter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rxTotalOctets": "Rx-Gesamtoktette", "collisionPackets": "Kollisionspakete", "dropPackets": "Verworfene Pakete", "dropPacketsHint": "Die standardmäßige Aktualisierungszeit des Zählers für verlorene Pakete beträgt etwa 5 Sekunden und erhöht sich mit der Anzahl der Ports des Geräts.", "rxPausePackets": "Rx-Pause-Pakete", "txTotalOctets": "Tx Gesamtoktette", "txUnicastPackets": "Tx-Unicast-Pakete", "crcAlignErrorPackets": "CRC-Align-Fehlerpakete", "txMulticastPackets": "Tx-Multicast-Pakete", "rxBroadcastPackets": "Rx-Broadcast-Pakete", "rxUnicastPackets": "Rx-Unicast-Pakete", "jabberPackets": "Jabber-Pakete", "excessiveCollisionPackets": "Übermäßige Kollisionspakete", "txTotalPackets": "Tx-Gesamtpakete", "fragmentPackets": "Fragmentierte Pakete", "rxTotalPackets": "Rx Gesamtpakete", "lateCollisionPackets": "Späte Kollisionspakete", "oversizePackets": "Übergroße Pakete", "rxMulticastPackets": "Rx-Multicast-Pakete", "txBroadcastPackets": "Tx-Broadcast-Pakete", "undersizePackets": "Unterdimensionierte Pakete", "txptpPackets": "Tx PTP-Pakete", "rxptpPackets": "Rx PTP-Pakete", "displayMode": "<PERSON><PERSON>ige<PERSON><PERSON>", "packetCounter": "Packet Counter", "bandwidthUtilization": "Bandwidth Usage", "line_num_target_port": "<PERSON><PERSON> {{ number }} Überwachungsport", "line_num_target_sniffer": "<PERSON><PERSON> {{ number }} <PERSON><PERSON><PERSON><PERSON><PERSON>", "txandrx": "Tx/Rx", "txonly": "Tx", "rxonly": "Rx", "all_port": "Alle Ports", "all_ge_port": "Alle GE-Ports", "all_fe_port": "Alle FE-Ports", "line_num": "<PERSON><PERSON>{{ number }}", "clear_graph_desc": "Sind <PERSON> sicher, dass Sie alle Diagrammdaten löschen möchten?", "clear_table_desc": "Sind <PERSON> sicher, dass Sie alle Tabellendaten löschen möchten?", "benchmark_line": "Benchmark", "benchmark_line_time": "Benchmarklinie - Zeit", "comparison_line": "Vergleich", "comparison_line_time": "Vergleichslinie - Zeit", "selecting_visible_columns": "Sichtbare Spalten bearbeiten", "title_comparison": "Daten vergleichen", "title_reset_statistics_graph": "Statistikdiagramm zurücksetzen", "title_edit_statistics_setting": "Bildschirmeinstellungen", "title_clear_statistics_counter": "Zähler löschen"}, "mab": {"page_title": "Umgehen der MAC-Authentifizierung", "page_title_abbr": "MAB", "auth_mode": "Authentifizierungsmodus", "local_database": "Local Database", "quiet_period": "Ruhezeit", "reauthentication": "Neuauthentifizierung", "reauth_period": "Reauth-<PERSON><PERSON>", "size_limitation": "Die maximale Anzahl an MAC-Adresseinträgen für dieses Gerät beträgt {{ size }}.", "retransmit": "Erneut übertragen", "force_unauthorized": "Force Unauthorized", "force_Authorized": "Force Authorized", "reauth_port_confirm": "Sind <PERSON> sic<PERSON>, dass Si<PERSON> den Port erneut authentifizieren möchten?", "authorized": "Autorisiert", "unauthorized": "Nicht autorisiert", "title_reauth_port": "Reauth Port", "port_setting": "Port {{ portIndex }} Einstellungen", "account_setting": "Konto {{ userName }} Einstellungen", "timeout_retransmit_hint": "Alle Wiederholungszeiten dürfen den Timeout-Wert des Dot1x-Servers nicht überschreiten. Notiz: Alle Wiederholungszeiten = Timeout * (Neuübertragung + 1). Empfohlener Wert: {{ number }}", "clear_button_disabled_hint": "Löschen Sie alle über MAC Authentication Bypass erfassten MAC-Adresseinträge, wenn die Anzahl der Einträge die maximale Kapazität erreicht.", "clear_warning_hint": "Die Anzahl der über MAC Authentication Bypass gesammelten MAC-Adresseinträge ist maximal ausgelastet.", "title_clear_mac_address": "MAC-Adressen löschen", "clear_mac_address_message": "Sind <PERSON> sic<PERSON>, dass Sie alle über MAC Authentication Bypass erfassten MAC-Adressen löschen möchten?", "radius_hint": "802.1X und MAC-Authentication Bypass verwenden denselben RADIUS-Server"}, "modbus_tcp": {"page_title": "Modbus TCP", "enable_modbus_tcpo_title": "Modbus TCP aktivieren", "enable_modbus_tcp_warning": "Sind <PERSON> sic<PERSON>, dass Sie das nicht sichere Protokoll (Modbus TCP) aktivieren möchten?"}, "fiber_check": {"page_title": "Faserprüfung", "threshold_settings": "Schwellenwerteinstellungen", "model_name": "<PERSON><PERSON><PERSON>", "serial_number": "Seriennummer", "wavelength": "<PERSON><PERSON><PERSON><PERSON>nge", "voltage": "Spannung", "temperature": "Temperatur", "tx_power": "Tx Power", "rx_power": "Rx-Leistung", "temperature_limit": "Temperaturschwelle", "tx_power_threshold_low": "Tx-Schwellenwert niedrig", "tx_power_threshold_high": "Tx-Schwellenwert hoch", "rx_power_threshold_low": "Rx-Schwellenwert niedrig", "rx_power_threshold_high": "Rx-Schwellenwert hoch", "temp_over_spec": "Die Temperatur ist über dem Grenzwert.", "tx_power_over_spec": "Die Sendeleistung liegt über dem Schwellenwert", "tx_power_under_spec": "Die Sendeleistung liegt unter dem Schwellenwert.", "rx_power_over_spec": "Die Rx-Leistung liegt über dem Schwellenwert.", "rx_power_under_spec": "Die Rx-Leistung liegt unter dem Schwellenwert.", "port_copy_to_copper_port_disable": "Konfigurationen können nicht auf einen Kabel-Port kopiert werden", "warning": "<PERSON><PERSON><PERSON>", "reset_title": "Port {{ portIndex }} Schwellenwerteinstellungen zurücksetzen", "reset_desc": "Sind <PERSON><PERSON> sic<PERSON>, dass Si<PERSON> diesen Port auf den automatischen Modus (Standard) zurücksetzen und alle Schwellenwerteinstellungen löschen möchten?", "reset_all_ports": "Alle Ports zurücksetzen", "reset_all_ports_title": "Schwellenwerteinstellungen für alle Ports zurücksetzen", "reset_all_ports_desc": "Sind <PERSON><PERSON> sic<PERSON>, dass alle Ports auf den automatischen Modus (Standard) zurückgesetzt und alle Schwellenwerteinstellungen gelöscht werden?"}, "dai": {"page_title": "Dynamische ARP-Inspektion", "port_is_la_member": "Dieser Port ist Mitglied des Port-Channels. Die dynamische ARP-Inspektion kann auf dem Mitglieds-Port nicht aktiviert werden.", "port_is_trusted": "Dieser Port ist ein vertrauenswürdiger Port für DHCP-Snooping. Für die dynamische ARP-Inspektion können nur nicht vertrauenswürdige Ports aktiviert werden."}, "mac_sec": {"page_title": "MAC-Sicherheit", "mka_status": "MKA-Status", "participant_ckn": "Teilnehmer CKN", "participant_cak": "Teilnehmer CAK", "key_server": "Schlüsselserver", "peer_list_mi": "Peer List Member Identifier (MI)", "peer_list_mn": "Peer List Message Number (MN)", "sci": "<PERSON><PERSON><PERSON> Kanal-ID (SCI)", "peer_list_type": "Peer-Listentyp", "live_peer_list": "Live-Peer-<PERSON><PERSON>", "potential_peer_list": "Potenzielle Peer-Liste", "peer_list_mi_hint": "MI: Die MKA-Nachrichten enthalten die eigene Mitglieds-ID des Absenders und die IDs anderer potenzieller Peers, von denen er Nachrichten erhalten hat.", "not_support_10G_port": "MACsec wird auf 10G-Ports nicht unterstützt.", "member_port_disable": "Dieser Port ist Mitglied eines Port-Kanals. Die Media Access Control Security kann auf einem Mitgliedsport nicht bearbeitet werden."}, "profinet": {"page_title": "PROFINET", "protocol_information": "Protokoll-Informationen", "enable_profinet_title": "PROFINET aktivieren", "enable_profinet_warning": "<PERSON>d <PERSON> sic<PERSON>, dass Si<PERSON> ein nicht sicheres Protokoll (PROFINET) aktivieren möchten?", "vlan_not_exist": "Die ausgewählte Schnittstelle existiert nicht.", "label_format_hint": "Dies ist das Format für die Beschriftung. Jedes Label sollte durch ein “.”.  getrennt werden. Ein Label darf maximal 63 Zeichen enthalten"}, "ethernet_ip": {"page_title": "EtherNet/IP", "enable_eip_title": "EtherNet/IP aktivieren", "enable_eip_warning": "<PERSON>d <PERSON> sic<PERSON>, dass Si<PERSON> ein nicht sicheres Protokoll (EtherNet/IP) aktivieren möchten?"}, "multi_coupling": {"page_title": "Multiple Network Coupling", "main_ring_protocol": "Hauptring-Protokoll", "switch_role": "Coupling Switch <PERSON>e", "group_id": "Coupling Gruppen-ID", "polling_interval": "Coupling Abfrageintervall", "polling_interval_hint": "<PERSON><PERSON><PERSON> den Hauptring beträgt die maximale Anzahl an Pfad-IDs 16, wenn ein Abfrageintervall von 80 ms verwendet wird. Bei Verwendung eines Abfrageintervalls von 40 ms empfehlen wir die Verwendung von 8 Pfad-IDs.", "table_settings": "Coupling-Tabelle Einstellungen", "table_status": "Coupling-Tabelle Status", "path_id": "Pfad-ID", "coupling_port": "Coupling Port", "coupling_port_state": "Zustand des Coupling Port", "partner_mac": "Partner MAC", "connect_status": "Verbindungss<PERSON>us", "error_status": "<PERSON><PERSON><PERSON><PERSON>", "multiple_active": "Mehrere aktive Schalter", "multiple_backup": "<PERSON><PERSON><PERSON>-Switches", "role_information": "Rolleninformationen", "edit_path_title": "Pfad-ID {{ pathId }} Einstellungen bearbeiten", "multi_coupling_enable_hint": "Diese Funktion setzt voraus, dass der Turbo Ring v2, MRP oder HSR Hauptring aktiviert ist", "is_coupling_port": "Dies wurde als Kopplungsport ausgewählt.", "is_selected_path": "Diese Pfad-ID wurde bereits ausgewählt.", "error_status_message": "Es wurden mehrere aktive/Backup-Switches erkannt. Bitte überprüfen Sie dieses Gerät und das Partnergerät, um sicherzustellen, dass es keine Duplikate gibt."}, "pim_dm": {"page_title": "PIM-DM", "state_refresh": "Zustand aktualisieren", "state_refresh_interval": "Intervall für die Aktualisierung des Zustands", "pim_dm_hint": "Wenn PIM-DM aktiviert ist und eine IGMP-An<PERSON><PERSON> vorliegt, kann es zusammen mit IGMP-Snooping verwendet werden.", "state_refresh_hint": "Alle Router im Netzwerk im PIM-DM-Modus müssen die Statusaktualisierung aktivieren und das gleiche Intervall konfigurieren."}, "pim_sm": {"page_title": "PIM-SM", "pim_sm_settings": "PIM-SM-Einstellungen", "pim_sm_hint": "Wenn PIM-SM aktiviert ist, stellen <PERSON> bitte sicher, dass Sie IGMP-Snooping und die zugehörigen Konfigurationen entsprechend Ihren Anforderungen aktivieren.", "spt_method": "Methode zum Umschalten des kürzesten Pfadbaums", "join_prune_interval": "Intervall für Beitritt/Löschen", "dr_priority": "DR-Priorität", "bsr": "BSR", "bsr_candidate": "BSR-Kandidat", "bsr_address": "BSR-<PERSON><PERSON><PERSON>", "bsr_priority": "BSR-Priorität", "bsr_hash_mask_length": "Länge der BSR-Hash-Maske", "dr_priority_hint": "Der DR mit dem höheren Prioritätswert wird bei der DR-Wahl bevorzugt.", "bsr_priority_hint": "Der BSR mit dem höheren Prioritätswert wird bei der BSR-Wahl bevorzugt.", "rp": "RP", "static_rp": "Statisches RP", "candidate_rp": "Kandidat-RP", "group_address": "Gruppenadresse", "group_mask": "Gruppenmaske", "rp_address": "RP-<PERSON><PERSON><PERSON>", "override": "Überschreiben", "interface_not_created": "Die PIM-SM-Schnittstelle wurde noch nicht erstellt.", "rp_if_name": "Name der RP-Schnittstelle", "rp_priority": "RP-Priorität", "static_rp_size_limitation": "Die maximale Anzahl statischer RP für dieses Gerät beträgt {{ size }}.", "candidate_rp_size_limitation": "Die maximale Anzahl von RP-Kandidaten für dieses Gerät beträgt {{ size }}.", "ssm": "SSM", "pim_ssm": "PIM-SSM", "pim_ssm_size_limitation": "Die maximale Anzahl PIM-SSM für dieses Gerät beträgt {{ size }}.", "title_create_static_rp": "Statisches RP erstellen", "title_edit_static_rp": "Statisches RP bearbeiten", "title_delete_static_rp": "Statisches RP löschen", "delete_static_rp_desc": "Sind <PERSON> sicher, dass Sie die ausgewählte statische RP löschen wollen?", "override_hint": "Überschreiben bedeutet, dass dieses statische RP bei Konflikten vor dem dynamisch gelernten (BSR) verwendet wird.", "title_create_candidate_rp": "Kandidaten-RP er<PERSON>llen", "title_edit_candidate_rp": "Kandidaten-RP bearbeiten", "title_delete_candidate_rp": "Kandidaten-RP löschen", "delete_candidate_rp_desc": "Sind <PERSON> sic<PERSON>, dass Sie den ausgewählten RP-Kandidaten löschen wollen?", "rp_priority_hint": "Das RP mit dem niedrigeren Prioritätswert wird bei der RP-Wahl bevorzugt.", "rp_if_name_address": "RP-Schnittstellenname (RP-Adresse)", "title_add_ssm_range": "SSM-Be<PERSON>ich hi<PERSON>ufü<PERSON>", "title_edit_ssm_range": "SSM-Be<PERSON>ich bear<PERSON>", "title_delete_ssm": "SSM löschen", "delete_ssm_desc": "Sind <PERSON> sicher, dass Sie die ausgewählte SSM löschen wollen?", "static_ssm_entry_hint": "Dieser Bereich ist in RFC 7761 für SSM reserviert.", "pim_sm_status": "PIM-SM-Status", "dr_address": "DR<PERSON><PERSON><PERSON><PERSON>", "bsr_rp": "BSR / RP", "elected_bsr": "Gewählter BSR", "rp_mapping": "RP-Mapping", "rp_mapping_result": "RP-Mapping-<PERSON><PERSON><PERSON><PERSON>"}, "multicast_routing_table": {"page_title": "Multicast-Routing-<PERSON><PERSON><PERSON>", "multicast_group": "Multicast-Gruppe", "upstream_neighbor": "Upstream Neighbor", "incoming_interface": "Eingehende Schnittstelle", "outgoing_interface": "Ausgehende Schnittstelle", "prune": "Löschen", "assert": "Bestätigen"}, "prp_hsr": {"page_title": "PRP/HSR", "prp_hsr_protocol": "PRP/HSR-Protokoll", "entry_forget_time": "Ein<PERSON>g vergessen", "net_id": "Net ID", "lan_id": "LAN ID", "prp": "PRP", "hsr": "HSR", "coupling": "Coupling", "enable_prp_hsr_title": "PRP/HSR aktivieren", "enable_prp_hsr_warning": "Durch Aktivieren von PRP/HSR wird die Konfiguration aller Ports auf dem PRP/HSR-Modul auf ihre Standardwerte zurückgesetzt. Sind Sie sicher?", "no_phr_module_warning": "<PERSON>in gültiges PHR-Modul erkannt. PHR-Modul prüfen."}, "tracking": {"page_title": "Tracking", "tracking_list_of_interface": "Tracking List of Interface", "tracking_list_of_ping": "Tracking List of Ping", "tracking_list_of_logical": "Tracking List of Logical", "tracking_list_of_all": "Tracking List of All", "tid": "Tracking ID", "down_to_up": "Von unten nach oben", "up_delay": "Up-Verzögerung", "up_to_down": "Von oben nach unten", "down_delay": "Down-Verzögerung", "received": "Empfangen", "lost": "Verloren", "and": "AND", "or": "OR", "nand": "NAND", "nor": "NOR", "entry_state": "Zustand", "interface_tracking": "Interface Tracking", "ping_tracking": "Ping Tracking", "logical_tracking": "Logisches Tracking", "create_interface_tracking_entry_title": "<PERSON><PERSON>ellen Sie einen Tracking-Eintrag", "edit_interface_tracking_entry_title": "Diesen Eintrag zur Tracking-Eintrag bearbeiten", "create_ping_tracking_entry_title": "<PERSON><PERSON> Ping-Tracking-Ein<PERSON>g erstellen", "edit_ping_tracking_entry_title": "<PERSON><PERSON> Ping-Tracking-Eintrag bearbeiten", "create_logical_tracking_entry_title": "Einen logischen Tracking-Eintrag erstellen", "edit_logical_tracking_entry_title": "Diesen logischen Tracking-Eintrag bearbeiten", "interface_type": "Schnittstellentyp", "port": "Port", "ping": "<PERSON>", "logical": "Logical", "interface": "Interface", "network_interface": "Network Interface", "status_change_from_down_to_up": "Zustandsänderung (von unten nach oben)", "status_change_from_up_to_down": "Zustandsänderung (von oben nach unten)", "logical_list": "Logische Liste", "logical_oper": "Logischer Operator", "interfaec_ip_logic": "Schnittstelle/IP-Adresse/Logische Liste", "time_since_last_change": "Zeit seit der letzten Änderung", "no_of_change": "Anzahl der Änderungen", "only_select_four": "Es können maximal vier ausgewählt werden", "require_tid_larger": "Die Tracking-ID muss größer sein als die TID der logischen Liste.", "require_at_least_two_tid": "<PERSON>s sind mehr als zwei TIDs erforderlich", "duplicated_tid": "Doppelte Tracking-ID", "tracking_size_limitation": "Die maximale Anzahl von Tracking-Einträgen für dieses Gerät beträgt {{ size }}.", "sync_to_latest_status": "Mit letztem Status synchronisieren"}, "auto_config": {"page_title": "Auto Configuration", "cdu_port": "Control Unit Port", "auto_config_info": "Auto Configuration Information", "import_mode_hint": "It requires DHCP Client and LLDP to be enabled, and the DHCP Client boot file and Client ID to be preconfigured. In this mode, Auto Configuration only sends Option 61 packets over the Control Unit port.", "propagate_mode_hint": "It requires IP Configuration to be set to manual and LLDP to be enabled. In this mode, the DHCP server assigns IP addresses based on LLDP information."}, "multi_local_route": {"page_title": "Lokale Multicast-Route", "routes": "Routen", "macl": "MACL", "vrrp_master_only": "Nur VRRP-Master", "multi_local_route_hint": "Wenn die lokale Multicast-Route aktiviert ist, muss auch IGMP-Snooping aktiviert sein.", "vrrp_master_only_hint": "<PERSON><PERSON> diese Option aktiviert ist, kann der Switch nur dann Multicast-Streams routen, wenn er als VRRP-Master fungiert", "source_vlan": "Quell-VLAN", "downstream_vlan": "Downstream-VLAN", "multi_local_route_size_limitation": "Die maximale Anzahl von Einträgen für die lokale Multicast-Route beträgt {{ size }}.", "create_route_msg": "Lokale Multicast-Route erstellen", "edit_route_msg": "Quell-VLAN bearbeiten {{ vid }}", "macl_id": "MACL ID", "title_create_macl_rule": "Multicast-ACL erstellen", "title_edit_macl_rule": "MACL-ID bearbeiten {{ maclId }}", "only_select_sixteen": "Maximal sechzehn können ausgewählt werden", "delete_session_title": "Lokale Multicast-Route löschen", "delete_session_content": "Sind <PERSON> sicher, dass Sie die ausgewählte lokale Multicast-Route löschen möchten?", "source_vlan_cannot_set": "Das Quell-VLAN kann nicht festgelegt werden."}, "supervision_frame": {"page_title": "Supervision Frame", "supervision_frame_enable_hint": "Das PRP/HSR-Protokoll muss aktiviert sein, bevor der Supervision Frame aktiviert wird.", "life_check_interval": "Life Check Interval", "destination_address": "Zieladresse", "forward_to_interlink": "Überwachungsweiterleitung an Interlink", "nodes_table": "Knotentabelle", "forget_time": "Knoten-Vergessenszeit", "node_type": "Knoten-Typ", "time_last_seen_a": "Zuletzt gesehen A", "time_last_seen_b": "Zuletzt gesehen B"}, "goose_check": {"page_title": "GOOSE-Prüfung", "goose_lock": "GOOSE-Sperre", "goose_lock_hint": "Wenn die GOOSE-Sperre aktiviert ist, werden GOOSE-Pakete, die nicht in der Überwachungstabelle angezeigt werden, gelöscht.", "tamper_response": "Manipulationsreaktion", "tamper_response_hint": "<PERSON><PERSON> <PERSON> „Verwerfen“ wählen, werden alle manipulierten GOOSE-Pakete gelöscht. <PERSON><PERSON> „Port deaktivieren“ wählen, wird der Eingangsport der manipulierten GOOSE-Pakete deaktiviert.", "port_disable": "Port deaktivieren", "app_id": "APP-ID", "goose_address": "GOOSE-Ad<PERSON>e (DA)", "monitoring_table_status": "Status der Überwachungstabelle", "goose_lock_status": "GOOSE-Sperrstatus", "lock_violation_status": "Lcok-Verstoßstatus", "goose_name": "GoCB-Name", "rx_counter": "Rx-<PERSON><PERSON><PERSON>", "create_goose": "Statischen GOOSE-Eintrag erstellen", "port_tampered": "Manipulierter Port", "sa_tampered": "Manipulierter SA", "duplicate_goose": "Ein identischer statischer GOOSE-Eintrag ist bereits vorhanden", "exist_dynamic_entry": "Ein identischer dynamisch gelernter GOOSE-Eintrag existiert bereits. Durch Klicken auf Anwenden wird dieser Eintrag in einen statischen Eintrag umgewandelt", "lock_violation_normal_hint": "Alle erkannten GOOSE-Pakete werden in der Überwachungstabelle angezeigt.", "lock_violation_warning_hint": "<PERSON>s wurden unerwartete GOOSE-Pakete erkannt, die nicht in der Überwachungstabelle angezeigt werden.", "goose_table_max": "<PERSON>. {{ size }} der Überwachungstabelle", "size_limitation": "Die maximale <PERSON><PERSON><PERSON> von <PERSON>-Check-Einträgen für dieses Gerät beträgt {{ size }}."}}, "request_handler": {"action_saving": "Speichern ...", "action_loading": "Wird geladen ...", "action_upgrading": "Upgrade durchführen ...", "action_ping": "Pinging …"}, "response_handler": {"res_server_error": "Server-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "res_global_success": "Erfolgreich aktualisiert.", "res_complete_refresh": "Aktualisierung abgeschlossen.", "res_complete_encrypt_data": "Verschlüsselung der Daten abgeschlossen.", "res_port_success": "Die Port-Einstellungen wurden erfolgreich aktualisiert", "res_entry_create_success": "Der Eintrag wurde erfolgreich erstellt.", "res_entry_update_success": "Der Eintrag wurde erfolgreich aktualisiert.", "res_entry_delete_success": "Der Eintrag wurde erfolgreich gelöscht.", "res_port_enable_success": "Port {{ portIndex }} wurde erfolgreich aktiviert", "res_port_disable_success": "Port {{ portIndex }} wurde erfolgreich deaktiviert.", "res_dscp_success": "Die DSCP-Einstellungen wurden erfolgreich aktualisiert.", "res_cos_success": "CoS-Einstellungen erfolgreich aktualisiert.", "res_regen_ssh_success": "Der SSH-Schlüssel wurde erfolgreich neu generiert.", "res_regen_ssl_success": "Das SSL-Zertifikat wurde erfolgreich neu generiert.", "export_ssl_cert": "Das SSL-Zertifikat wurde erfolgreich exportiert.", "import_ssl_cert": "Das SSL-Zertifikat wurde erfolgreich importiert.", "import_config": "Konfiguration erfolgreich importiert.", "res_copy": "<PERSON><PERSON><PERSON>.", "res_ping_success": "<PERSON> beendet.", "res_auto_save_mode_success": "Der automatische Speichermodus wurde erfolgreich aktualisiert", "res_switch_browse_mode_success": "Modus erfolgreich gewechselt", "res_v3_account_update_success": "Das Authentifizierungskonto wurde erfolgreich aktualisiert.", "res_host_update_success": "Der SNMP-Host wurde erfolgreich aktualisiert.", "res_upgrading_firmware_success": "Die Firmware wurde erfolgreich aktualisiert. Das Gerät wird nun neu gestartet.", "res_event_notification_success": "Ereignisbenachrichtigungen erfolgreich aktualisiert.", "res_save_to_startup_success": "Die laufende Konfiguration wurde erfolgreich in der Startkonfiguration gespeichert.", "clear_success": "Erfolgreich <PERSON>.", "res_factory_default_success": "Erfolgreich auf Werkseinstellungen zurückgesetzt.", "backup_success": "Sicherung erfolgreich.", "res_custom_default_success": "Successfully reset to custom defaults.", "download_success": "Download erfolgreich.", "locator": "Die Gerätesuche wurde erfolgreich ausgelöst.", "re_auth_port_success": "Der Port wurde erfolgreich reauthentifiziert", "res_recovery_port_success": "Der Port wurde erfolgreich wiederhergestellt"}, "error_handler": {"error_session_expired_dialog": "Diese Sitzung ist abgelaufen. Das System kehrt zur Anmeldeseite zurück."}, "validators": {"required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "require_min_length": "Mindestens {{ number }} <PERSON><PERSON><PERSON>", "require_range_between": "{{ rangeBegin }} - {{ rangeEnd }}", "require_range_single_between": "{{ singleNum }}, {{ rangeBegin }} - {{ rangeEnd }}", "require_hexadecimal": "<PERSON><PERSON><PERSON>", "require_unicast": "Unicast IP only", "required_at_least_two": "Mindestens 2 Ausgangsports", "required_at_least_one_overwrite": "Mindestens ein Element überschreiben", "required_priority_multiple": "Die Priorität muss ein Vielfaches von {{ num }} sein", "required_label_max_length": "Die maximale Anzahl der Zeichen für eine Bezeichnung ist {{ number }}", "required_interval_multiple": "Das Timeout-Intervall muss ein Vielfaches von {{ num }} sein", "required_timeout_multiple": "Der Timeout-<PERSON><PERSON><PERSON> muss ein Vielfaches von {{ num }} sein", "invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "invalid_format": "Ungültiges Format", "invalid_positive_integer": "Ungültige positive <PERSON><PERSON><PERSON><PERSON>", "invalid_hex": "Ungültige Hexzahl", "invalid_range": "Der gültige Bereich reicht von {{ rangeBegin }} bis {{ rangeEnd }}", "invalid_single_range": "<PERSON><PERSON>ich ist {{ singleNum }} oder {{ rangeBegin }} bis {{ rangeEnd }}", "invalid_mac_address": "Ungültige MAC-Adresse", "invalid_ip_address": "Ungültige IP-Adresse", "invalid_area_id": "Ungültige Bereichs-ID", "invalid_router_id": "Ungültige Router-ID", "invalid_vlan_port": "Ungültiger VLAN-Mitgliedsport", "invalid_vlan_output_port": "Ungültiges VLAN für den Ausgangsport", "invalid_netmask": "Ungültige Netzmaske", "invalid_char": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON>-<PERSON>, 0-9 sind erlaubt", "invalid_email": "Ungültige E-Mail", "invalid_regex_level_1": "<PERSON>ur a-z, A-<PERSON>, 0-9 oder . - _ sind erlaubt", "invalid_regex_level_2": "Nur a-z, A-Z, 0-9 oder . , - _ + = | : ; @ ! ~ # % ^ * ( ) [ ] { } sind erlaubt", "invalid_sys_desc": "Nur a-z, A-Z, 0-9 oder ~ ! @ # $ % ^ & * ( ) { } [ ] < > _ + - = \\ : ; , . / sind zulässig", "invalid_login_failure_message": "Nur a-z, A-Z, 0-9 oder ! # $ % & ' ( ) * + , \\ - . / : ; < = > @ [ ] ^ _ ` { | } ~ sind zulässig", "invalid_regex_macsec_cak_and_ckn": "Nur a-z, A-Z, 0-9 oder @ % $ ^ * ' ` ( ) _ + = { } : . , ~ [ ] - sind erlaubt.", "invalid_char_and_dash": "<PERSON><PERSON> a-<PERSON>, A-<PERSON>, 0-9 oder - sind erlaubt", "invalid_char_and_dot_dash": "<PERSON><PERSON> a-z, A-<PERSON>, 0-9 oder . - sind erlaubt", "invalid_lowercase_and_dash_dot": "<PERSON><PERSON> <PERSON><PERSON>, 0-9 oder . - sind erlaubt", "invalid_file_name": "Nur a-z, A-<PERSON>, 0-9 oder / ( ) . - _ sind erlaubt", "duplicate_ip": "Doppelte IP", "duplicate_ip_range": "Doppelter IP-Bereich", "duplicate_vrid": "Doppelte VRID", "duplicate_stream": "Dieser Stream existiert bereits", "duplicate_id": "Doppelte ID", "duplicate_input_ports": "Doppelte Eingangsports", "duplicate_loopback_id": "<PERSON><PERSON>-<PERSON> wird bereits verwendet", "duplicate_vlan_id": "Diese VLAN-ID wird bereits verwendet.", "duplicate_vlan_and_mac": "Diese Kombination aus VLAN-ID und MAC-Adresse existiert bereits", "duplicate_group_address_and_netmask": "Diese Kombination aus Gruppenadresse und Gruppennetzmaske existiert bereits", "two_deciaml_palce": "Maximal 2 Dezimalstellen", "duplicate_ip_and_netmask": "Diese Kombination aus IP-Adresse und Subnetzmaske existiert bereits", "three_deciaml_palce": "Maximal 3 Dezimalstellen", "same_as_ingress_stream": "Gleich wie der Ingress-Stream"}}