{"__readme__": {"__1__": "All key naming is snake_case, in addition to using uri key, e.g.'ospfRRDStatic'", "__2__": "In 'common', words are used repeatedly. It can be reclassified and named 'common_xxx', e.g.'common_port', 'common_account'...", "__3__": "In 'dialog', words are used repeatedly in the dialog.", "__4__": "In 'button', put all button words in and must be capitalized"}, "login": {"title_login_records": "Login Records", "redirected_message": "Logout successful", "login_success_records_greeting": "Welcome {{ name }}", "login_success_records_datetime": "The last successful login time was {{ time }}.", "login_fail_records": "The latest login failure record(s).", "modify_password_notification": "Please change the default username and password in order to enhance security.", "password_expire_notification": "Your password has expired. Please change your password.", "daylight_saving_upgrade": "The Daylight Saving feature has been upgraded.", "daylight_saving_notification": "Please update your configurations.", "factory_default_note": "Please note that you need to use the default network settings to re-establish a web-browser connection with your switch.", "firmware_upgrade_note": "Please note that you need to re-establish a web-browser connection with your switch."}, "general": {"top_nav": {"config_change": {"start_up_save_tip": "Configurations have not been saved to start-up configurations.", "confirmation_message": "Are you sure you want to apply running configurations to start-up configurations?", "title_apply_to_start_config": "Apply to Start-up Configurations"}, "user_profile": {"greeting": "Hi, {{ username }}", "enable_auto_save": "Enable Auto Save", "disable_auto_save": "Disable Auto Save", "disable_auto_save_hint": "When applied, configurations will save to running configurations instead of start-up configurations.", "change_language": "Change Language", "change_mode": "Change Mode", "locator": "Locator", "reset_factory_default": "Reset to <PERSON><PERSON><PERSON>s", "save_custom_default": "Save Custom Default", "standard_mode": "Standard", "advanced_mode": "Advanced", "standard_mode_tooltip": "Standard Mode: Some of the features/parameters will be hidden to make it simpler.", "advanced_mode_tooltip": "Advanced Mode: The advanced features/parameters will be available for users that want to adjust these settings."}, "auto_save_mode": {"enable_auto_save_title": "Enable Auto Save Mode", "enable_auto_save_msg": "Are you sure you want to enable Auto Save mode?", "disable_auto_save_title": "Disable Auto Save Mode", "disable_auto_save_msg": "Are you sure you want to disable Auto Save mode?"}, "advanced_browse_mode": {"advanced_notification_title": "Change to Advanced Mode", "advanced_notification_msg": "Are you sure you want to change from Standard mode to Advanced mode?", "basic_notification_title": "Change to Standard Mode", "basic_notification_msg": "Are you sure you want to change from Advanced mode to Standard mode?"}, "locator": {"title_switch_locator": "Switch Locator", "duration": "Duration", "hint": "Trigger the LEDs to start blinking on the device to make it easier to locate."}, "restart_machine": {"confirmation_title": "Reboot", "confirmation_msg": "Are you sure you want to reboot the device?"}, "factory_default": {"confirmation_title": "Factory Default", "confirmation_msg": "Are you sure you want to reset the system configurations to factory default?", "factory_default_hint": "Resetting to the factory default settings will clear the custom default configurations.", "custom_default": "Custom Default", "confirmation_msg_custom_default": "Are you sure you want to reset the system configurations to the custom default?", "custom_default_not_exist": "Custom default can NOT be executed when its status is \"No configuration found!\".", "saved_config_name": "Saved Configuration name", "config_name_hint": "The custom default configuration name is saved in the non-volatile memory.", "clear_all_config": "Delete configurations, log files, and credential keys"}, "save_custom_default": {"confirmation_msg": "Are you sure you want to save the startup configuration as the custom default?", "current_config_name": "Current Configuration Name", "config_name_hint": "The configuration name can be modified on the \"Config Backup and Restore\" page."}, "logout": {"confirmation_title": "Log Out", "confirmation_msg": "Are you sure you want to log out?"}}, "page_state": {"page_not_found": "Page Not Found", "page_not_found_desc": "The requested URL was not found on this server.", "back_link": "Back to Index Page"}, "menu_tree": {"jump_page_placeholder": "Search for function", "system": "System", "system_management": "System Management", "account_management": "Account Management", "provisioning": "Provisioning", "port_interface": "Port Interface", "l2_switching": "Layer 2 Switching", "unicast_route": "Unicast Route", "multicast_route": "Multicast Route", "mac": "MAC", "qos": "QoS", "redundancy": "Redundancy", "l2_redundancy": "Layer 2 Redundancy", "l3_redundancy": "Layer 3 Redundancy", "network_service": "Network Service", "routing": "Routing", "network_management": "Network Management", "device_security": "Device Security", "network_security": "Network Security", "diagnostics": "Diagnostics", "network_status": "Network Status", "tools": "Tools", "log_and_event_notification": "Event Logs and Notifications", "application": "Industrial Application", "iec61850": "IEC 61850", "iec62439_3": "IEC 62439-3"}, "dialog": {"title_refresh_browser": "<PERSON><PERSON><PERSON>er", "title_change_default_password": "Change Default Password", "title_change_password": "Change Password", "title_session_expired": "Session Expired", "title_notification": "Notification", "title_edit_interface": "Edit Interface {{ interfaceName }}", "edit_port_msg": "Edit Port {{ portIndex }} Settings", "edit_vlan_msg": "Edit VLAN {{ vlanIndex }} Settings", "create_entry_msg": "Create Entry", "edit_entry_msg": "Edit Entry Settings", "delete_entry_msg": "Delete Entry", "title_delete_key": "Delete Certificate and Key", "title_delete_account": "Delete Account", "delete_entry_confirm_desc": "Are you sure you want to delete the selected entry?", "title_select_file": "Select File", "title_device_unplugged": "{{ device }} Is Unplugged", "desc_device_unplugged": "Please check if the {{ device }} is plugged in.", "redirect_to_ip": "Redirect to {{ ipAddress }}", "page_redirect_content": "The page will redirect after 5 seconds.", "redirect_failed_hint": "If redirecting fails, please check your network settings.", "after_seconds": "After {{ second }} seconds", "redirecting": "Redirecting...", "title_choose_tracking_id": "Choose a Tracking ID"}, "common": {"network": "Network", "enable_header": "Enable", "enabled": "Enabled", "disable_header": "Disable", "disabled": "Disabled", "none": "None", "authentication": "Authentication", "active": "Active", "inactive": "Inactive", "passive": "Passive", "ip": "IP", "ip_address": "IP Address", "mac": "MAC", "mac_address": "MAC Address", "server_address": "Server IP Address", "subnet_mask": "Subnet Mask", "domain_name": "Domain Name", "ip_or_domain": "IP Address/ Domain Name", "general": "General", "normal": "Normal", "type": "Type", "mode": "Mode", "yes": "Yes", "no": "No", "auto": "Auto", "user_defined": "User Defined", "valid": "<PERSON><PERSON>", "invalid": "Invalid", "required": "Required", "version": "Version", "unknown": "Unknown", "read_only": "Read Only", "refresh": "Refresh", "reset": "Reset", "add": "Add", "delete": "Delete", "export": "Export", "up": "Up", "down": "Down", "index": "Index", "name": "Name", "method": "Method", "file_name": "File Name", "file_server": "File Server", "select_file": "Select File", "action": "Action", "authority": "Authority", "any": "Any", "all": "All", "unselect": "Unselect", "settings": "Settings", "status": "Status", "local": "Local", "usb": "USB", "usb_hint": "The USB port is compatible with the ABC-02 automatic backup configurator.", "usb_hw_disabled_hint": "The USB is not valid because the external storage function is disabled.", "micro_sd": "microSD", "micro_sd_hw_disabled_hint": "The microSD is not valid because the external storage function is disabled.", "location": "Location", "time": "Time", "start_date": "Start Date", "start_time": "Start Time", "end_time": "End Time", "timeout": "Timeout", "interface": "Interface", "threshold": "<PERSON><PERSON><PERSON><PERSON>", "broadcast": "Broadcast", "multicast": "Multicast", "algorithm": "Algorithm", "manual": "Manual", "master": "Master", "priority": "Priority", "permanent": "Permanent", "queue": "Queue", "netmask": "Netmask", "backup": "Backup", "backup_en": "Backup", "restroe": "Rest<PERSON>", "broken": "Broken", "learning": "Learning", "listening": "Listening", "discarding": "Discarding", "forwarding": "Forwarding", "blocking": "Blocking", "packets": "Packets", "notice": "Notice", "warning": "Warning", "critical": "Critical", "error": "Error", "security": "Security", "slave": "Slave", "slot": "Slot", "simple": "Simple", "state": "State", "subtype": "Subtype", "protocol": "Protocol", "init": "Init", "retry": "Retry", "severity": "Severity", "destination": "Destination", "description": "Description", "distance": "Distance", "expired_date": "Expiration Date", "delay_time": "Delay Time", "serial_number": "Serial Number", "product_revision": "Product Revision", "quick_setting": "Quick Settings", "admin_status": "Admin Status", "link_status": "Link Status", "system_status": "System Status", "link_up": "Link Up", "link_down": "Link Down", "point_to_point": "Point-to-point", "ethertype": "EtherType", "auth_type": "Auth Type", "authentication_type": "Authentication Type", "default_gateway": "Default Gateway", "encryprion_key": "Encryption Key", "encryption_method": "Encryption Method", "authentication_password": "Authentication Password", "server_ip_address": "Server IP Address", "key": "Key", "key_id": "Key ID", "vlan_id": "VLAN ID", "vlan_vid": "VLAN {{ vid }}", "vlan_list_info": "Multiple VLANs can be set, and should be entered as a single number or a range e.g. 2, 4-8, 10-13.", "share_key": "Share Key", "share_key_hint": "After leaving this page or refreshing, the Share Key will automatically be cleared to enhance security.", "auto_backup": "Automatically Back Up", "auto_backup_hint": "Back up to external storage when configurations change.", "dynamic": "Dynamic", "static": "Static", "ref_manual_hint": "Please refer to the user's manual for information about this setting.", "default": "<PERSON><PERSON><PERSON>", "other": "Other", "trusted": "Trusted", "untrusted": "Untrusted", "infinite": "Infinite", "source": "Source", "low": "Low", "high": "High", "connected": "Connected", "disconnected": "Disconnected", "incorrect_connected": "Connected incorrectly ", "set_event_notifications": "Set Event Notifications", "include": "Include", "exclude": "Exclude", "immediate": "Immediate", "never": "Never", "interface_name": "Interface Name", "interface_alias": "Interface <PERSON>", "hello_interval": "Hello Interval", "neighbor": "Neighbor", "current": "Current", "ping": "<PERSON>", "logical": "Logical", "interval": "Interval", "route": "Route", "open": "Open", "short": "Short", "role": "Role", "speed": "Speed", "failed": "Failed", "successful": "Successful", "idle": "Idle", "protection": "Protection", "pending": "Pending", "supported": "Supported", "not_supported": "Not Supported", "import": "Import", "propagate": "Propagate", "ascii": "ASCII", "hex": "HEX", "zone": "Zone"}, "common_account": {"account": "Account", "username": "Username", "pwd_mask": "********", "password": "Password", "confirm_password": "Confirm Password", "email": "Email", "delete_account_desc": "Are you sure you want to delete the selected account?"}, "common_abbrev": {"md5": "MD5", "sha": "SHA", "sha-224": "SHA-224", "sha-256": "SHA-256", "sha-384": "SHA-384", "sha-512": "SHA-512", "sftp": "SFTP", "tftp": "TFTP", "pap": "PAP", "chap": "CHAP", "cos": "CoS", "dscp": "DSCP", "cir": "CIR", "cir_full": "Committed Information Rate", "cbs": "CBS", "cbs_full": "Committed <PERSON><PERSON><PERSON> Size"}, "common_port": {"port": "Port", "ports": "Ports", "port_name": "Port {{ portName }}", "tcp_port": "TCP Port", "udp_port": "UDP Port", "port_state": "Port State", "port_status": "Port Status", "port_settings": "Port Settings", "port_shutdown": "Port Shutdown", "destination_port": "Destination Port", "reflect_port": "Reflect Port", "all_port": "All Ports", "member_port": "Member Port", "also_apply_port": "Copy configurations to ports", "also_apply_port_hint": "Copy the configurations to the ports you select from the drop-down box.", "ingress_port": "Ingress Port"}, "button": {"cancel": "CANCEL", "apply": "APPLY", "create": "CREATE", "edit": "EDIT", "delete": "DELETE", "sync_from_browser": "SYNC FROM BROWSER", "regenerate": "REGENERATE", "import": "IMPORT", "export": "EXPORT", "expand": "EXPAND", "collapse": "COLLAPSE", "copy": "COPY", "close": "CLOSE", "download": "DOWNLOAD", "ping": "PING", "clear": "CLEAR", "backup": "BACK UP", "restore": "RESTORE", "upgrade": "UPGRADE", "reset": "RESET", "locate": "LOCATE", "disable": "DISABLE", "enable": "ENABLE", "change": "CHANGE", "logout": "LOG OUT", "reboot": "REBOOT", "confirm": "CONFIRM", "remove": "REMOVE", "back": "BACK", "log_in": "LOG IN", "re_auth": "RE-AUTH", "recovery": "RECOVERY", "export_cid_file": "EXPORT CID FILE", "export_server_ca": "EXPORT SERVER CA", "change_password": "CHANGE PASSWORD", "export_server_cert": "EXPORT SERVER CERTIFICATE", "view_all_event_logs": "VIEW ALL EVENT LOGS", "update_daylight_saving": "UPDATE DAYLIGHT SAVING", "select": "SELECT", "retry": "RETRY", "encrypt": "ENCRYPT", "change_cak": "CHANGE CKN AND CAK", "save": "SAVE", "find_rp": "FIND RP", "cut_off": "CUT OFF", "go_to": "GO TO", "got_it": "GOT IT"}, "tooltip": {"edit": "Edit", "delete": "Delete", "copy": "Copy", "copy_config": "Copy Configurations", "close": "Close", "clear": "Clear", "clear_graph": "Clear Graph", "remove": "Remove", "reorder": "Reorder", "recovery": "Recovery", "select_tracking": "Select Tracking ID", "remove_tracking": "Remove Tracking ID", "vlan_size_limitation": "This device only allows {{ size }} VLANs.", "size_limitation": "The maximum number of user accounts for this device is {{ size }}.", "reorder_priority": "Reorder Priority", "reorder_finish": "Reorder Finished", "export_pdf_all_acl": "Export all ACL to PDF", "export_pdf_only_this_port": "Only export the information of this port to PDF", "export_pdf_only_this_vlan": "Only export the information of this VLAN to PDF", "clear_counter": "Clear counter", "unicast_mac_address_hint": "Only allow unicast MAC address.", "disable_la_member_hint": "This is a member port of link aggregation port channel.", "duplicate_redundant_port": "Cannot duplicate redundant ports", "auto_refresh_enabled": "Auto Refresh: Enabled", "auto_refresh_disabled": "Auto Refresh: Disabled", "manually_refersh_disabled": "Cannot manually refresh if 'Auto refresh' is activated", "integration_3A_3B_hint": "This port is integration of 3/A and 3/B.", "set_to_static": "Set to static"}, "unit": {"fps": "fps", "min": "min.", "sec": "sec.", "ms": "ms", "byte": "byte", "m": "M", "kbyte": "Kbyte", "mbps": "Mbps", "w": "W", "watt": "<PERSON>", "watts": "<PERSON>", "percent": "%", "day": "day", "times": "times", "mb": "MB", "ns": "ns", "microsecond": "μs", "ppm": "PPM", "packets": "packets", "v": "V", "dbm": "dBm", "c": "°C", "f": "°F", "nm": "nm"}, "speed": {"10m": "10M", "100m": "100M", "1g": "1G", "10g": "10G", "40g": "40G", "56g": "56G", "2500m": "2500M", "full": "Full", "half": "Half"}, "table_function": {"filter_desc": "Search", "export_pdf": "Export PDF", "export_csv": "Export CSV", "selected_count": "Selected", "row_count": "Total", "limit_count": "<PERSON>."}, "led": {"status_unknown_hint": "Status Unknown.", "state_green_on": "Normal Operation.", "state_green_blink4hz": "The system is booting up.", "state_red_on": "The system has failed to initialize.", "state_off": "The system is off.", "fault_red_on": "The system has experienced a failure, please check the system log for details.", "fault_red_blink4Hz": "The switch has booted up and the firmware has loaded to the memory.", "fault_off": "Normal Operation.", "mh_turbo_chain_head_green_on": "The switch is the Head of Turbo Chain.", "mh_turbo_ring_green_on": "The switch is the Master of Turbo Ring 1 or Turbo Ring 2.", "mh_mrp_green_on": "The switch is the Manager of MRP ring.", "mh_turbo_ring_green_blink2hz_mds": "The switch is the Master of Turbo Ring 1 or Turbo Ring 2 and at least one ring is broken.", "mh_turbo_chain_head_green_blink2hz_mds": "The switch is the Head of Turbo Chain and the chain is broken.", "mh_mrp_green_blink2hz_mds": "The switch is the Manager of MRP ring and the ring is open.", "mh_turbo_chain_member_green_blink2hz_mds": "The switch is the Member of Turbo Chain and the corresponding Member Port 1 is link down.", "mh_turbo_chain_tail_green_blink2hz_mds": "The switch the Tail of Turbo Chain and the corresponding Member Port is link down.", "mh_turbo_ring_green_blink4hz": "The switch is the Master of Turbo Ring 1 or Turbo Ring 2 and at least one ring is broken.", "mh_turbo_chain_head_green_blink4hz": "The switch is the Head of Turbo Chain and the chain is broken.", "mh_mrp_green_blink4hz": "The switch is the Manager of MRP ring and the ring is open.", "mh_turbo_chain_member_green_blink4hz": "The switch is the Member of Turbo Chain and the corresponding Member Port 1 is link down.", "mh_turbo_chain_tail_green_blink4hz": "The switch the Tail of Turbo Chain and the corresponding Member Port is link down.", "mh_off": "The switch is not the Master of Turbo Ring 1 or Turbo Ring 2, Head of Turbo Chain, or Manager of the MRP ring.", "ct_turbo_ring_green_on": "The switch's coupling function is enabled to form a back-up path.", "ct_mrp_green_on": "The switch enables a coupling function to form a back-up path.", "ct_turbo_chain_tail_green_on": "The switch is the tail of Turbo Chain.", "ct_dual_homing_green_on": "The switch's dual homing function is enabled.", "ct_turbo_ring_dual_homing_green_on": "The switch's coupling and dual homing functions are enabled.", "ct_turbo_chain_dual_homing_green_on": "The switch's dual homing function is enabled and the switch is the Tail of Turbo Chain.", "ct_mrp_dual_homing_green_on": "The switch's MRP connection and dual homing function are enabled.", "ct_turbo_chain_head_green_blink2hz_mds": "The switch is the Head of Turbo Chain and the corresponding Member Port is link down.", "ct_turbo_chain_member_green_blink2hz_mds": "The switch is the Member of Turbo Chain and the corresponding Member Port 2 is link down.", "ct_turbo_chain_tail_green_blink2hz_mds": "The switch is the Tail of Turbo Chain and the chain is broken.", "ct_turbo_chain_head_green_blink4hz": "The switch is the Head of Turbo Chain and the corresponding Member Port is link down.", "ct_turbo_chain_member_green_blink4hz": "The switch is the Member of Turbo Chain and the corresponding Member Port 2 is link down.", "ct_turbo_chain_tail_green_blink4hz": "The switch is the Tail of Turbo Chain and the chain is broken.", "ct_off": "The switch's coupling or dual homing function is disabled or the switch is not the tail of Turbo Chain.", "sync_amber_on": "The PTP function is enabled.", "sync_amber_blink4hz": "The switch has received sync packets.", "sync_green_on": "The PTP function has successfully converged.", "sync_off": "The PTP function is disabled.", "ms_green_on": "Normal Operation.", "ms_green_blink2hz": "This module is booting up.", "ms_off": "The module is out of service.", "ms_red_on": "The module has failed to initialize, or the user has inserted the wrong module.", "eps_amber_on": "The external power supply is ready to supply power to the PoE port.", "eps_off": "There is no external power supply for the PoE device.", "pwr_eps_amber_on": "The external power supply is being supplied to the module's EPS input.", "pwr_eps_off": "There is no external power supply for the PoE device.", "pwr_amber_on": "Power is being supplied to the module's power input.", "pwr_off": "Power is not being supplied to the module's power input.", "port_poe_green_on": "The port is connected to an IEEE 802.3at powered device (PD).", "port_poe_green_on_poebt": "The port is connected to an IEEE 802.3bt powered device (PD).", "port_poe_amber_on": "The port is connected to an IEEE 802.3af powered device (PD).", "port_poe_amber_on_poebt": "The port is connected to an IEEE 802.3af/at powered device (PD).", "port_poe_amber_blink4hz": "The PoE power has been shut off because the power budget is not sufficient.", "port_poe_red_on": "Powered device (PD) detection failure.", "port_poe_red_blink4hz": "Over current or short circuit has been detected on the powered device (PD).", "port_poe_off": "The power is not being supplied to the powered device (PD).", "port_link_up_hint": "The port is active and is linking on {{ operSpeed }}bps.", "port_link_down_hint": "The port is inactive or link down.", "prp_green_on": "The PRP function is enabled.", "prp_off": "The PRP function is disabled.", "hsr_green_on": "The HSR function is enabled.", "hsr_off": "The HSR function is disabled.", "coup_green_on": "The Coupling function is enabled.", "coup_off": "The Coupling function is disabled."}}, "features": {"storm_control": {"page_title": "Traffic Storm Control", "dlf": "DLF"}, "la": {"page_title": "Link Aggregation", "port_channel": "Port Channel (Trunk)", "wait_time": "Wait Time", "configure_member": "Configure Member", "active_member": "Active Member", "la_group_status": "LA Group Status", "lacp": "LACP", "smac": "SMAC", "dmac": "DMAC", "smac_dmac": "SMAC + DMAC", "config_member_port": "Config Member Port", "config_member_port_hint": "Keep at least one port that cannot be added to a port channel.", "delete_port_channel_confirm_desc_1": "Warning:", "delete_port_channel_confirm_desc_2": "Some features (such as RSTP and VLAN) that are related to selected Link Aggregation will be set to default values.", "delete_port_channel_confirm_desc_3": "Are you sure you want to delete the selected Link Aggregation?", "la_size_limitation": "This device only allows {{ size }} port channels.", "create_la_msg": "Create Link Aggregation", "edit_la_pre_msg": "Edit Port Channel {{ portChannel }} Settings", "delete_la_msg": "Delete Link Aggregation", "only_select_eight": "A maximum of eight can be selected."}, "scheduler": {"page_title": "Scheduler", "strict_priority": "Strict Priority", "weight_round_robin": "Weighted Round Robin", "sp": "SP", "wrr": "WRR", "wfq": "WFQ"}, "egress_shaper": {"page_title": "Egress <PERSON>", "egress_rate": "Egress Rate (CIR)"}, "rate_limit": {"page_title": "Ingress Rate Limit", "ingress_rate": "Ingress Rate (CIR)", "ebs": "EBS", "ebs_full": "Excess <PERSON><PERSON><PERSON>", "conform_action": "Conform Action", "exceed_action": "Exceed Action", "violate_action": "Violate Action", "blind": "Color Blind", "aware": "Color Aware", "do_nothing": "Do Nothing", "drop": "Drop", "remark_cos": "Remark CoS", "remark_dscp": "Remark DSCP", "simple_token_bucket": "Simple Token Bucket", "sr_tcm": "SrTCM", "remark_value": "Remark Value", "release_interval": "Release Interval", "rate_limit_port_shutdown": "Rate Limit Port Shutdown"}, "classification": {"page_title": "Classification", "cos_priority": "CoS Priority", "preference_type": "Trust Type", "dhcp_mapping": "DSCP Mapping", "cos_mapping": "CoS Mapping", "untag_default_priority": "Untag Default Priority", "edit_dscp_msg": "Edit DSCP {{ dscpIndex }} Settings", "edit_cos_msg": "Edit CoS {{ cosIndex }} Settings"}, "linkup_delay": {"page_title": "Linkup Delay", "remaining_time": "Remaining Time"}, "port_mirror": {"page_title": "Port Mirroring", "span": "SPAN", "rspan": "RSPAN", "session_id": "Session ID", "reflect_port_mode": "Reflect Port Mode", "rspan_type": "RSPAN Type", "rspan_vid": "RSPAN VLAN ID", "rspan_settings": "RSPAN Intermediate Settings", "rspan_setting_hint": "Make sure that any ports used for RSPAN communication are added to the appropriate RSPAN VLAN.", "rspan_vlan_setting_hint": "To prevent tagged frames from being dropped, only select a VLAN port in Trunk or Hybrid mode as the Reflect port.", "duplicate_intermediate_vlan": "This Intermediate VLAN ID is already in use.", "rspan_role": "RSPAN Intermediate Role", "rspan_intermediate_vid1": "RSPAN Intermediate 1st VLAN ID", "rspan_intermediate_vid2": "RSPAN Intermediate 2nd VLAN ID", "enable_rspan_title": "Enable RSPAN Intermediate Role", "enable_rspan_warning": "If this setting is applied, all existing RSPAN Sessions will be deleted.", "rx_source_port": "Rx Source Port(s)", "tx_source_port": "Tx Source Port(s)", "designated_port": "Designated Port", "destination_ports": "Destination Port(s)", "destination_port_info": "For access ports, the port's PVID will be set to the RSPAN VLAN ID.\nFor hybrid or trunk ports, the port will be made a member of the RSPAN VLAN.", "destination_port_hint": "Destination ports will be added to RSPAN VLAN.", "destination_ports_or_designated_port": "Destination Port(s) or Designated Port", "source_port_two_field_invalid": "Required to select Tx or Rx source port", "create_mirror_msg": "Create Session", "edit_mirror_msg": "Edit Session {{ sessionIndex }} Settings", "select_tx_or_rx_hint": "Either the TX or RX source ports need to be selected.", "is_not_access_port": "This port is not an access port.", "is_not_trunk_port": "This port is not a trunk port.", "source_port_must_be_access_port": "Source port must be access port when Reflect Port Mode is enabled.", "reflect_port_must_be_access_port": "Reflect port must be access port when Reflect Port Mode is enabled.", "reflect_port_must_be_trunk_hybrid_port": "Reflect port must be trunk/hybrid port when Reflect Port Mode is enabled.", "pvid_is_not_rspan_vid": "This port's PVID is not the RSPAN VLAN.", "rspan_source_session_exist": "The RSPAN source session already exists.", "rspan_destination_session_exist": "The RSPAN destination session already exists.", "rspan_cannot_create": "RSPAN session can't be created when RSPAN Intermediate Role is enabled.", "session_span_size_limitation": "The maximum number of SPAN entries is {{ size }}.", "session_rspan_size_limitation": "The maximum number of RSPAN entries is {{ size }}.", "delete_session_title": "Delete Session", "delete_session_content": "Are you sure you want to delete the selected session?", "rspan_vid_hint_l2": "Using the Management VLAN or VLAN Assignment-configured VLANs for RSPAN is not recommended.", "rspan_vid_hint_l3": "Using VLAN interfaces or VLAN Assignment-configured VLANs for RSPAN is not recommended."}, "vlan": {"page_title": "VLAN", "vlan": "VLAN", "global": "Global", "management_vlan": "Management VLAN", "management_port": "Management Port", "mgmt_vlan_settings": "Management VLAN Settings", "management_vlan_port_setting_hint": "Please select the port that your computer is connected to and ensure the settings are correct to avoid being disconnected from the switch.", "port_mode_table_title": "VLAN Switchport Mode Table", "egress_tagged_table_title": "VLAN Membership Table", "gvrp": "GVRP", "vlan_unaware": "VLAN Unaware", "vlan_unaware_gvrp_error": "GVRP cannot be enabled while VLAN Unaware is active.", "vlan_unaware_active_disable_hint": "VLAN cannot be modified while VLAN Unaware is active.", "all_member_vlan": "All Member VLAN IDs", "dynamic_gvrp": "Dynamic GVRP", "egress_port": "Egress Port", "tagged_port": "Tagged Port", "untagged_port": "Untagged Port", "forbidden_port": "Forbidden Port", "vid_exist_warning": "VLAN already exists", "vlan_max_warning": "Max. 10 VLANs per once", "vlan_max_hint": "Max. 10 VLANs", "pvid": "PVID", "tagged_vlan": "Tagged VLAN", "untagged_vlan": "Untagged VLAN", "access": "Access", "access_port": "Access Port", "trunk": "Trunk", "trunk_port": "Trunk Port", "hybrid": "Hybrid", "hybrid_port": "Hybrid Port", "vlan_assignment": "VLAN Assignment", "delete_vlan_confirm_desc": "Are you sure you want to delete the selected VLAN?", "mgmt_setting_disabled_pvid": "PVID is binding to this VLAN, so it cannot be deleted.", "mgmt_setting_disabled_access_mode": "If the port is using access mode, it cannot change to this VLAN.", "mgmt_setting_disabled_forbidden": "This port is a forbidden port.", "mgmt_setting_disabled_egress": "This port is a member port.", "port_setting_disabled_tagged": "This VLAN is a tagged VLAN.", "port_setting_disabled_untagged": "This VLAN is an untagged VLAN.", "port_setting_disabled_forbidden": "This port is a forbidden port for this VLAN.", "port_setting_disabled_pvid_member": "This PVID cannot bind to this VLAN because this port is not a member port.", "port_setting_disabled_pvid_forbidden": "This PVID cannot bind to this VLAN because this port is a forbidden port.", "port_setting_error_pvid_member": "Not Tagged or Untagged VLAN", "port_setting_error_pvid_forbidden": "This is a forbidden port and the settings cannot be applied", "vlan_setting_vid_info": "Multiple VLANs can be created, and should be entered as a single number or a range e.g. 2, 4-8, 10-13.", "te_mstid": "TE-MSTID", "temstid_member": "TE-MSTID Member", "temstid_info": "For the VLAN join TE-MSTID member, the stream will be forwarded by static forwarding rule instead of MAC learning/forwarding mechanism.", "create_vlan_msg": "Create VLAN", "delete_vlan_msg": "Delete VLAN", "vlan_setting_info_title": "How to Set Up", "example_scenario": "Example <PERSON><PERSON><PERSON>", "example_scenario_info_1": "Port 1: Hybrid Mode, PVID 1, TAG VLAN 3-5, and UNTAG VLAN 1", "example_scenario_info_2": "Port 2: Trunk Mode, PVID 2, and TAG VLAN 2-5", "example_scenario_info_3": "Port 3: Access Mode, PVID 1, and UNTAG VLAN 1", "example_scenario_info_4": "Switch-A Settings: Management VLAN 1", "setup_flow": "Set Up Flow", "vlan_port_mode_setup": "VLAN Port Mode setup", "port_number_setting": "Port {{ portNumber }} Settings", "setup_list_hybrid": "- Mode select 'Hybrid'", "setup_list_apply": "- Apply", "setup_list_trunk": "- Mode select 'Trunk'", "setup_list_access": "- The default mode is 'Access' and there is no need to change anything.", "setup_list_pvid2": "- PVID 2", "vlan_create_member_setup": "VLAN create/VLAN member setup", "vlan_create_member_setup_info_part_1": "Click", "vlan_create_member_setup_info_part_2": ", add VLAN ID {{ vlanIndex }} , add Member Port {{ portIndex }}", "vlan_port_pvid_setup": "VLAN port PVID setup", "vlan_port_pvid_setup_info": "Ready and don't need to change."}, "l3_interface": {"page_title": "Network Interface", "loopback_size_limitation": "This device only allows {{ size }} loopbacks.", "operStatus": "Operation Status", "loopback_id": "Loopback ID", "vlan_interface": "VLAN Interface", "loopback_interface": "Loopback Interface", "alias": "<PERSON><PERSON>", "mtu": "MTU", "proxy_arp": "Proxy ARP", "vlan_id_hint": "The interface VLAN ID should be equal to the Layer 2 VLAN to make Layer 3 routing work.", "delete_ip_interface_desc": "Are you sure you want to delete this entry?", "vlan_card_title": "VLAN Interface", "loopback_card_title": "Loopback Interface", "delete_ip_interface": "Delete Interface", "add_l3_vlan_ip_interface": "Create VLAN Interface Settings", "edit_l3_vlan_ip_interface": "Edit VLAN Interface Settings", "add_l3_loopback_ip_interface": "Create Loopback Interface Settings", "edit_l3_loopback_ip_interface": "Edit Loopback Interface Settings", "from_dcp": "(From PROFINET DCP)"}, "stp": {"page_title": "Spanning Tree", "stp_mode": "STP Mode", "compatibility": "Compatibility", "stp": "STP", "rstp": "RSTP", "mstp": "MSTP", "stp_rstp": "STP/RSTP", "bridge_priority": "Bridge Priority", "error_recovery_time": "Error Recovery Time", "forward_delay_time": "Forward Delay Time", "hello_time": "Hello Time", "max_age": "Max. Age", "edge": "Edge", "guard": "Guard", "path_cost": "Path Cost", "multiples_of_number": "Multiples of {{ number }}", "path_cost_help_info": "The path cost value will be automatically assigned according to the different port speed if the value is set to zero.", "bpdu_guard": "BPDU Guard", "root_guard": "Root Guard", "loop_guard": "Loop Guard", "bpdu_filter": "BPDU Filter", "root_information": "Root Information", "bridge_id": "Bridge ID", "root_path_cost": "Root Path Cost", "bridge_information": "Bridge Information", "running_protocol": "Running Protocol", "port_role": "Port Role", "link_type": "Link Type", "shared": "Shared", "bpdu_inconsistency": "BPDU Inconsistency", "root_inconsistency": "Root Inconsistency", "loop_inconsistency": "Loop Inconsistency", "link_type_shared_lan": "Shared LAN", "alternate": "Alternate", "root": "Root", "designated": "Designated", "instance": "Instance", "instance_index": "Instance {{ instId }}", "all_instances": "All Instances", "instance_list": "Instance List", "instance_id": "Instance ID", "mstp_size_limitation": "The maximum number of instances for this device is {{ size }} except for CIST.", "vlan_list": "VLAN List", "port_table_of_cist": "Port Table of CIST", "port_table_of_instance": "Port Table of Instance {{ instId }}", "information_of_cist": "Information of CIST", "information_of_instance": "Information of Instance {{ instId }}", "region_name": "Region Name", "region_revision": "Region Revision", "max_hops": "<PERSON><PERSON>", "instance_id_duplicate": "The instance ID had been created.", "except_for_cist": "Except for CIST.", "copy_port_config": "Copy the port configurations​", "select_from_port_of_inst": "From ports of {{ instName }}", "select_to_inst": "To instances", "general_information": "General Information", "regional_root_id": "Regional Root ID", "cist_root_id": "CIST Root ID", "cist_path_cost": "CIST Path Cost", "designated_root_id": "Designated Root ID", "other_vlans": "Other VLANs", "create_instance": "Create Instance", "edit_instance": "Edit Instance {{ instId }} Settings", "delete_instance": "Delete Instance", "edit_cist": "Edit CIST Settings", "edit_instance_port": "Edit Instance {{ instId }} Port {{ portIndex }} Settings", "edit_cist_port": "Edit CIST Port {{ portIndex }} Settings"}, "port_security": {"page_title": "Port Security", "port_security_mode": "Port Security mode", "port_security_mode_help_info": "Changing the Port Security mode will reset all settings.", "mac_sticky": "MAC Sticky", "static_port_lock": "Static Port Lock", "address_limit": "Address Limit", "secure_action": "Secure Action", "current_address": "Current Address", "configured_address": "Manually Configured Address", "violation": "Violation", "effective": "Effective", "secure_pack_drop": "Packet Drop", "total_entry": "Total Trust Hosts", "max_address": "The max. number of addresses in the system", "sticky_configured": "Sticky Configured", "lock_configured": "Lock Configured", "sticky_dynamic": "Sticky Dynamic", "address_limit_hint": "If the address limit value has changed, all the MAC addresses on the port will be deleted."}, "garp": {"page_title": "GARP", "join_time": "Join Time", "leave_time": "Leave Time", "leave_all_time": "Leave All Time", "required_join_time_multiple": "The join time must be a multiple of {{ num }}", "required_leave_time_multiple": "The leave time must be a multiple of {{ num }}", "required_leave_all_time_multiple": "The leave all time must be a multiple of {{ num }}"}, "lldp": {"page_title": "LLDP", "neighbor_status": "Neighbor Status", "sidenav_header": "Detailed Information", "port_local_intf_status": "Port Local Interface", "port_id_subtype": "Port ID SubType", "port_id": "Port ID", "port_desc": "Port Description", "dot1_tlv_info": "Extended 802.1 TLV", "dot3_tlv_info": "Extended 802.3 TLV", "port_vlan_id": "Port VLAN ID", "vlan_name": "VLAN Name", "vlan_tx_status": "VLAN ID / Name", "aggregated_and_status": "Link Aggregation Status", "aggregated_port_id": "Aggregated Port ID", "max_frame_size": "Maximum Frame <PERSON>", "port_traffic_statistics": "Port Traffic Statistics", "total_frame_out": "Total Frames Out", "total_entries_aged": "Total Entries Aged", "total_frame_in": "Total Frames In", "total_frame_receviced_in_error": "Total Frames Received In Error", "total_frame_discarded": "Total Frames Discarded", "total_tlvs_unrecognized": "Total TLVS Unrecognized", "total_tlv_discarded": "Total TLVs Discarded", "management_address_table": "Management Address Table", "management_address": "Management Address", "extended_eip_tlv": "Extended Ethernet/IP TLV", "vendor_id": "Vendor ID", "device_type": "Device Type", "product_code": "Product Code", "major_revision": "Major Revision", "minor_revision": "Minor Revision", "interface_id": "Interface ID", "lldp_version": "LLDP Version", "transmit_interval": "Transmit Interval", "notification_interval": "Notification Interval", "reinit_delay": "Reinitialization Delay", "holdtime_multiplier": "Holdtime Multiplier", "chass_id_subtype": "Chassis ID Subtype", "tx_delay": "Tx Delay", "subtype_chassis_component": "<PERSON><PERSON>s-Component", "subtype_if_alias": "<PERSON>-<PERSON><PERSON>", "subtype_port_component": "Port-Component", "subtype_mac_addr": "MAC-Address", "subtype_network_address": "Network-Address", "subtype_if_name": "If-Name", "subtype_unknown": "Unknown Subtype", "chassis_id": "<PERSON><PERSON>s <PERSON>", "tlv": "TLV", "local_info": "Local Information", "local_timer": "Local Timer", "remote_table_statistics": "Remote Table Statistics", "statistics_last_change_time": "Last Change Time (ms)", "statistics_insert": "Inserts", "statistics_drops": "Drops", "statistics_ageout": "Ageouts", "tx_status": "Tx Status", "rx_status": "Rx Status", "nbr_port_id": "Neighbor Port ID", "nbr_chassis_id": "Neighbor Chassis ID", "tx_only": "Tx Only", "rx_only": "Rx Only", "tx_and_rx": "Tx and Rx", "basic": "Basic", "basic_transmit_tlvs": "Basic Transmit TLVs", "8021_transmit_tlvs": "802.1 Transmit TLVs", "8023_transmit_tlvs": "802.3 Transmit TLVs", "port_component_description": "Port Component Description.", "system_name": "System Name", "system_desc": "System Description", "system_capability": "System Capability", "la_statistics": "Link Aggregation Statistics", "lldp_update_success": "Successfully Updated LLDP Global Settings.", "local_port": "Local Port", "sys_capability": "System Capability", "hold_time": "Hold Time", "repeater": "<PERSON><PERSON><PERSON>", "bridge": "Bridge", "vlan_access_point": "Vlan Access Point", "telephone": "Telephone", "docsis_cable_device": "Docsis Cable Device", "station_only": "Station Only"}, "mac_address_table": {"page_title": "MAC Address Table", "independent_vlan_learning": "Independent VLAN Learning", "mac_learning_mode": "MAC Learning Mode", "mac_learning_mode_help_info": "Note that change mode will reset related L2 module.", "aging_time": "Aging Time", "learnt_unicast": "Learnt Unicast", "learnt_multicast": "Learnt Multicast"}, "port_setting": {"page_title": "Port Settings", "admin": "Admin", "media_type": "Media Type", "10m_half": "10M Half", "10m_full": "10M Full", "100m_half": "100M Half", "100m_full": "100M Full", "speed_duplex": "Speed/Duplex", "flow_control": "Flow Control", "flow_control_hint1": "Flow Control can be enabled/disabled, but it is only effective at full duplex.", "flow_control_hint2": "Back Pressure can be enabled/disabled, but it is only effective at half duplex.", "mdi_mdix": "MDI/MDIX", "mdi": "MDI", "mdix": "MDIX", "enabled_xmit": "Enabled Transmit", "enabled_rcv": "Enabled Receive", "fiber_speed_disable": "The Fiber port cannot set Speed/Duplex.", "fiber_mdi_disable": "The Fiber port cannot set MDI/MDIX.", "fiber_copy_to_other_port_disable": "The Fiber port cannot copy configurations to other ports.", "port_copy_to_fiber_port_disable": "Cannot copy configurations to fiber ports."}, "dashboard": {"page_title": "<PERSON><PERSON>", "system_info": "System Information", "panel_status": "Panel Status", "panel_view": "Panel View", "link_up_port": "Link Up Ports", "link_down_port": "Link Down Ports", "module": "Module {{ index }} - {{ name }}", "product_model": "Product Model", "firmware_version": "Firmware Version", "system_uptime": "System Uptime", "ip_address_v4": "IPv4 Address", "ip_address_v6": "IPv6 Address", "l3_ip_address_list": "Interface IP Address list", "redundant_protocol": "Redundant Protocol", "power_model": "Power Model", "external_storage": "External Storage", "iec62439_3_protocol": "IEC 62439-3 Protocol", "event_summary": "Event Summary", "event_summary_hint": "(Last 3 days)", "top_5_interface_error_packet": "Top 5 Interface Error Packets", "top_5_interface_utilization": "Top 5 Interface Utilization", "critical_hint": "An abnormality has occurred and the system is at risk of functioning abnormally in the future", "error_hint": "An abnormality has occurred, but system operations have not been affected", "warning_hint": "The information contains a warning/reminder but it does not affect functions or system operations", "notice_hint": "The information denotes that the function is working correctly and the device is operating normally", "tx_error": "Tx Error", "rx_error": "<PERSON><PERSON>", "unsupported_module_warning": "Caution: Unsupported module detected. Remove unsupported module to maintain normal function."}, "igmp_snooping": {"page_title": "IGMP Snooping", "vlan_setting": "VLAN Settings", "group_table": "Group Table", "forwarding_table": "Forwarding Table", "query_interval": "Query Interval", "static_router_port": "Static Router Port", "dynamic_router_port": "Dynamic Router Port", "config_role": "Config Role", "active_role": "Active Role", "startup_query_interval": "Startup Query Interval", "startup_query_count": "Startup Query Count", "other_quer_present_interval": "Other Query Present Interval", "group_address": "Group Address", "filter_mode": "Filter Mode", "source_address": "Source Address", "querier": "<PERSON><PERSON>", "non_querier": "Non-Querier"}, "turbo_ring_v2": {"page_title": "Turbo Ring V2", "ring_coupling_mode": "Ring Coupling Mode", "static_ring_coupling": "Static Ring Coupling", "dynamic_ring_coupling": "Dynamic Ring Coupling", "ring_id": "Ring ID", "master_id": "Master ID", "ring_port": "Ring Port", "coupling_mode": "Coupling Mode", "coupling_port": "Coupling Port", "primary_path": "Primary Path", "backup_path": "Backup Path", "ring_setting": "Ring Settings", "ring_coupling_setting": "Ring Coupling Settings", "coupling_setting": "Coupling Group {{ id }} Settings", "static_ring_coupling_setting": "Static Ring Coupling Settings", "dynamic_ring_coupling_setting": "Dynamic Ring Coupling Settings", "coupling_group_id": "Coupling Group ID", "coupling_group_status": "Coupling Group Status", "group_id": "Group {{ id }}", "ring_status": "Ring Status", "ring_index": "Ring Index", "total_ring_number": "Total Ring Number", "healthy": "Healthy", "break": "Break", "ring_coupling_status": "Ring Coupling Status", "static_ring_coupling_status": "Static Ring Coupling Status", "dynamic_ring_coupling_status": "Dynamic Ring Coupling Status", "coupling_mode_primary": "Coupling Primary Path", "coupling_mode_backup": "Coupling Backup Path", "coupling_port_status": "Coupling Port Status", "primary_mac": "Primary MAC", "primary_port": "Primary Port", "primary_port_status": "Primary Port Status", "backup_mac": "Backup MAC", "backup_port": "Backup Port", "backup_port_status": "Backup Port Status", "ring_setting_dialog_title": "{{ portIndex }} Settings", "dip_lock_hint": "Turbo Ring V2 is locked due to DIP configurations."}, "8021x": {"page_title": "IEEE 802.1X", "auth_mode": "Authentication Mode", "local_database": "Local Database", "re_auth": "Reauthenticate", "port_control": "Port Control", "auth_session_type": "Authentication Session Type", "max_request": "Max. Request", "quiet_period": "Quiet Period", "reauthentication": "Reauthentication", "reauth_period": "Reauthentication Period", "server_timeout": "Server Timeout", "supp_timeout": "Supp Timeout", "tx_period": "Tx Period", "auth_port": "Auth Port", "retransmit": "Retransmit", "force_unauthorized": "Force Unauthorized", "force_Authorized": "Force Authorized", "reauth_port_confirm": "Are you sure you want to reauthenticate the port {{ port }}?", "authorized": "Authorized", "unauthorized": "Unauthorized", "title_reauth_port": "Reauthenticate the Port", "port_setting": "Port {{ portIndex }} Settings", "account_setting": "Account {{ userName }} Settings", "timeout_retransmit_hint": "All of the retry times cannot exceed the Dot1x server timeout value. Note: All of the retry times = Timeout * (Retransmit + 1). Recommend value: {{ number }}", "session_status": "Session Status", "auth_table_of_port_based": "Authentication Table of Port-Based", "auth_table_of_mac_based": "Authentication Table of MAC-Based"}, "dual_homing": {"page_title": "Dual Homing", "multi_dual_homing": "Multiple Dual Homing", "dual_homing_table_settings": "Dual Homing Table Settings", "primary_port": "Primary Port", "primary_link_status": "Primary Link Status", "primary_port_status": "Primary Port Status", "secondary_port": "Secondary Port", "secondary_link_status": "Secondary Link Status", "secondary_port_status": "Secondary Port Status", "secondary_port_hint": "The secondary port cannot be the same as the primary port.", "path_switching_mode": "Path Switching Mode", "primary_path_always_first": "Primary path always first", "maintain_current_path": "Maintain current path", "maintain_current_path_hint": "Maintain current path until it is disconnected", "primary_path_sensing_recovery": "Primary path sensing recovery", "path_switching_mode_hint": "It is recommended to enable the Linkup Delay function of ", "path_switching_mode_hint_2": " port.", "path": "Path", "linkup_delay_warning_title": "Linkup Delay is Disabled"}, "poe": {"page_title": "PoE", "power_output": "Power Output", "poe_supported": "PoE Supported", "scheduling": "Scheduling", "pd_failure_check": "PD Failure Check", "auto_power_cutting": "Auto Power Cutting", "auto_power_cutting_hint": "Auto Power Cutting removes the lowest priority and smallest index port power output if the power consumption exceeds the system's power budget.", "system_power_budget": "System Power Budget", "system_power_budget_hint": "The system power budget depends on the source capability of the external power supply (EPS).", "actual_power_budget": "Actual Power Budget", "actual_power_budget_hint": "The lower value between \"Actual Power Budget\" and \"System Power Budget\" will become the \"Power Budget Limit\".", "output_mode": "Output Mode", "high_power": "High Power", "force": "Force", "power_allocation": "Power Allocation", "legacy_pd_detection": "Legacy PD Detection", "critical": "Critical", "low": "Low", "high": "High", "rule": "Rule", "also_apply_port": "Apply the rule to the port", "device_ip": "Device IP", "check_frequency": "Check Frequency", "no_response_times": "No Response Times", "no_action": "No Action", "restart_pd": "Restart PD", "shutdown_pd": "Shut down PD", "system_time_status": "System Time Status", "system_time": "System Time", "local_timeZone": "Local TimeZone", "daylight_saving_time": "Daylight Saving Time", "off": "Off", "on": "On", "rule_name": "Rule Name", "schedule_time": "Schedule Time", "repeat_execution": "Repeat Execution", "daily": "Daily", "weekly": "Weekly", "weekdays": "Weekdays", "weekend": "Weekend", "sun": "Sun", "mon": "Mon", "tue": "<PERSON><PERSON>", "wed": "Wed", "thu": "<PERSON>hu", "fri": "<PERSON><PERSON>", "sat": "Sat", "sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "maximum_input_power": "Maximum Input Power", "power_budget_limit": "Power Budget Limit", "power_management_mode": "Power Management Mode", "allocated_power": "Allocated Power", "consumed_power": "Consumed Power", "remaining_available_power": "Remaining Power Available", "classification": "Classification", "over_current": "Over current", "current_ma": "Current (mA)", "voltage_v": "Voltage (V)", "consumption_w": "Consumption (W)", "device_type": "Device Type", "not_present": "Not present", "legacy": "Legacy PD", "dot3af": "802.3 af", "dot3at": "802.3 at", "dot3bt": "802.3 bf", "dot3btss": "802.3 bt SS", "dot3btds": "802.3 bt DS", "na": "N/A", "configuration_suggestion": "Configuration suggestion", "no_suggestion": "No suggestion", "enable_poe": "Enable PoE power output", "disable_poe": "Disable PoE power output", "select_auto": "Select output mode \"Auto\"", "select_high_power": "Select output mode \"High power\"", "select_force": "Select output mode \"Force\"", "enable_legacy": "Enable legacy PD detection", "raise_eps_voltage": "Raise the external power supply voltage so that it is greater than 46 VDC", "pd_failure_check_status": "PD Failure Check Status", "alive": "Alive", "not_alive": "Not alive", "schedule_size_limitation": "This device only allows {{ size }} schedules.", "title_create_rule": "Create Rule", "title_edit_rule": "Edit Rule", "allocated_power_hint": "Calculate power budget of all ports and ensure the total allocated power is under the power budget limit.", "consumed_power_hint": "Calculate real-time power consumption of all ports.", "change_power_mode_dialog_title": "Set the Power Management Mode", "select_allocated_power_mode": "Are you sure you want to select the “Allocated Power” mode? If so, the “Auto Power Cutting” will be disabled.", "select_consumed_power_mode": "Are you sure you want to select the “Consumed Power” mode? If so, the “Auto Power Cutting” will be enabled.", "change_power_cutting_dialog_title": "Set the Auto Power Cutting", "disable_auto_power_cutting": "Are you sure you want to disable the “Auto Power Cutting”? If so, the “Power Management Mode” will become “Allocated Power” mode.", "enable_auto_power_cutting": "Are you sure you want to enable the “Auto Power Cutting”? If so, the “Power Management Mode” will become “Consumed Power” mode.", "avaliable_power_hint": "“Remaining Available Power” is “Maximum Input Power” minus “{{ power }}”."}, "turbo_chain": {"page_title": "Turbo Chain", "chain_role": "Chain Role", "head": "Head", "member": "Member", "tail": "Tail", "head_port": "Head Port", "tail_port": "Tail Port", "member_port_number": "Member Port {{ portIndex }}", "chain_information": "Chain Information", "head_port_status": "Head Port Status", "member_port_status": "Member Port Status", "tail_port_status": "Tail Port Status", "member_number_port_status": "Member {{ number }} Port Status", "initiated": "Initiated"}, "mrp": {"menu_title": "MRP", "page_title": "Media Redundancy Protocol", "mrp_role": "Role", "ring_manager": "Ring Manager", "ring_client": "Ring Client", "domain_uuid": "Domain UUID", "domain_id": "Domain ID", "react_on_link_change": "React on Link Change", "ring_port": "Ring Port {{ portIndex }}", "ring_status": "Ring Status", "mrp_ring": "MRP Ring", "ring_state": "Ring State", "vid_hint": "The VLAN ID must align with the Redundant Port's settings.", "react_on_link_change_hint": "This function is only available on MRP Ring Manager Switch. Once enabled, the Ring Manager will react immediately on link change, and the MRP Topology will converge faster.", "initiation": "Initiation", "awaiting_connection": "Awaiting Connection", "primary_ring_port_link_up": "Primary Ring Port Link Up", "ring_open": "Ring Open", "ring_closed": "Ring Closed", "data_exchange_idle": "Data Exchange Idle", "pass_through": "Pass Through", "data_exchange": "Data Exchange", "pass_through_idle": "Pass Through Idle", "port_pvid_warning": "The VLAN ID must align with the Redundant Port's settings", "port_mode_warning": "MRP Ring ports will not function unless set to VLAN Trunk mode or VLAN Hybrid mode.", "interconnection_port_mode_warning": "MRP Interconnection ports will not function unless set to VLAN Trunk mode or VLAN Hybrid mode.", "interconnection": "Interconnection", "interconnection_role": "Interconnection Role", "interconnection_manager": "Interconnection Manager", "interconnection_client": "Interconnection Client", "interconnection_mode": "Interconnection Mode", "lc_mode": "LC-Mode", "rc_mode": "RC-Mode", "interconnection_id": "Interconnection ID", "interconnection_port": "Interconnection Port", "interconnection_status": "Interconnection Status", "interconnection_state": "Interconnection State", "interconnection_open": "Interconnection Open", "interconnection_closed": "Interconnection Closed", "interconnection_port_idle": "Interconnection Port Idle"}, "unicast_table": {"page_title": "Unicast Table", "static_unicast": "Static Unicast", "edit_static_unicast_entry": "Edit This Static Unicast Entry", "add_static_unicast_entry": "Add a Static Unicast Entry", "size_limitation": "The maximum number of static unicast entries for this device is {{ size }}."}, "static_forwarding_table": {"page_title": "Static Forwarding Table", "menu_title": "Static Forwarding", "size_limitation": "The maximum number of static unicast entries for this device is {{ size }}.", "title_add_static_forwarding": "Create a Static Forwarding Entry", "title_edit_static_forwarding": "Edit This Static Forwarding Entry", "title_delete_static_forwarding": "Delete This Static Forwarding Entry"}, "multicast_table": {"page_title": "Static Multicast Table", "static_multicast": "Static Multicast", "delete_on_reset": "Delete On Reset", "delete_on_timeout": "Delete On Timeout", "add_static_multicast_entry": "Add a Static Multicast Entry", "edit_static_multicast_entry": "Edit This Static Multicast Entry", "size_limitation": "The maximum number of static multicast entries for this device is {{ size }}."}, "gmrp": {"page_title": "GMRP", "group_restrict": "Group Restrict"}, "time_sync": {"page_title": "Time Synchronization", "page_title_abbr": "Time Sync.", "mds_m2_insert_warning": "To use Time Synchronization, a compatible module must be inserted in the M2 slot.", "profile": "Profile", "8021as": "IEEE 802.1AS-2011 Profile", "8021as_abbr": "IEEE 802.1AS-2011", "1588default": "IEEE 1588 Default-2008 Profile", "1588default_abbr": "IEEE 1588 Default-2008", "iec61850": "IEC 61850-9-3-2016 Profile", "iec61850_abbr": "IEC 61850-9-3-2016", "c37238": "IEEE C37.238-2017 Profile", "c37238_abbr": "IEEE C37.238-2017", "priority_number": "Priority {{ number }}", "clock_type": "Clock Type", "clock_type_bc": "Boundary Clock", "clock_type_tc": "Transparent Clock", "delay_mechanism": "Delay Mechanism", "e2e": "End-to-End", "p2p": "Peer-to-Peer", "transport_mode": "Transport Mode", "8023ehternet": "IEEE 802.3 Ethernet", "udp_ipv4": "UDP IPv4", "udp_ipv6": "UDP IPv6", "domain_number": "Domain Number", "clock_mode": "Clock Mode", "two_step": "Two-step", "one_step": "One-step", "accuracy_alert": "Accuracy <PERSON>", "bmca": "BMCA", "bmca_hint": "Best Master Clock Algorithm (BMCA) prevents cyclical path looping when using Transparent Clock. We recommend enabling this function.", "max_steps_removed": "Maximum Steps Removed", "grandmaster_id": "Grandmaster ID", "announce_interval": "Announce Interval", "announce_receipt_timeout": "Announce Receipt Timeout", "sync_interval": "Sync Interval", "sync_receipt_timeout": "Sync Receipt Timeout", "delay_req_interval": "Delay-Request Interval", "pdelay_req_interval": "Pdelay-Request Interval", "neighbor_rate_ratio": "Neighbor Rate Ratio", "neighbor_prop_delay": "Neighbor Propagation Delay", "path_delay": "Path Delay", "neighbor_prop_delay_thresh": "Neighbor Propagation Delay Threshold", "synchronization_status": "Synchronization Status", "transport_type": "Transport Type", "current_data_set": "Current Data Set", "parent_data_set": "Parent Data Set", "locked": "Locked", "unlocked": "Unlocked", "freerun": "<PERSON><PERSON>", "syncing": "Syncing", "browser_time": "Browser Time", "ptp_clock_time": "PTP Clock Time (TAI)", "ptp_slave_port": "PTP Slave Port", "offset_from_master": "Offset From Master", "mean_path_delay": "Mean Path Delay", "steps_removed": "Steps Removed", "parent_identity": "Parent Identity", "grandmaster_identity": "Grandmaster Identity", "cumulative_rate_ratio": "Cumulative Rate Ratio", "grandmaster_priority_number": "Grandmaster Priority {{ number }}", "grandmaster_clock_class": "Grandmaster Clock Class", "grandmaster_clock_accuracy": "Grandmaster Clock Accuracy", "8021as_capable": "802.1AS-capable", "initializing": "Initializing ...", "res_ptp_initializing_done": "The PTP service is ready.", "faulty": "<PERSON><PERSON><PERSON>", "pre_master": "PreMaster", "uncalibrated": "Uncalibrated", "sync_transmit": "Transmitting Synchronized", "sync_receive": "Receiving Synchronized", "edit_port_profile_setting": "Edit the Port {{ portIndex }} IEEE 1588v2 Default Profile Settings"}, "stream_adapter": {"page_title": "Priority Management", "pcp": "Priority Code Point (PCP)", "ingress_table_limit_hint": "Each port supports a maximum of 10 entries.", "port_size_limitation": "The maximum number of entries (10) for this port has been reached.", "hexadecimal": "Hex digit", "egress_untag": "Egress Untag", "ingress": "Ingress", "egress": "Egress", "per_stream_priority_title": "Per-stream Priority", "port_default_priority_title": "Port Default Priority", "per_stream_priority_hint": "Untagged streams will be processed based on the per-stream priority if they match any user-defined rules. If the streams do not match any rule, they will be processed based on the default port priority.", "add_stream_adapter_entry": "Add a Per-stream Priority Entry", "edit_stream_adapter_entry": "Edit This Per-stream Priority Entry", "edit_port_def_priority": "Edit the Default Priority of Port {{ portIndex }}"}, "static_route": {"page_title": "Static Routing", "size_limitation": "The maximum number of static routes for this device is {{ size }}.", "next_hop_IP": "Next Hop IP", "next_hop_interface": "Next Hop Interface", "delete_static_route_desc": "Are you sure you want to delete the selected route?", "next_hop_type": "Next Hop Type *", "next_hop_type_hint": "You can select a previously created VLAN interface or leave this field blank.", "next_hop_two_field_invalid": "You must specify either the IP or the interface of the next hop", "add_static_route": "Create a Static Route", "edit_static_route": "Edit This Static Route", "delete_static_route": "Delete This Static Route"}, "routing_table": {"page_title": "Routing Table", "static": "Static", "next_hop": "Next Hop", "ad_metric": "AD/Metric"}, "online_accounts": {"page_title": "Online Accounts", "idle_time": "Idle Time", "remove_account_dialog_title": "Remove This Online Account", "remove_account_dialog_desc": "Are you sure you want to remove this online account?"}, "8021qbv": {"page_title": "Time-aware <PERSON><PERSON><PERSON>", "cycle_time": "Cycle Time", "start_time_hint": "The Time-aware Shaper is based on the current PTP time. You can determine the start time or can set the time now, which will determine when the function started.", "config_change_time": "Configuration Change Time", "default_setting": "<PERSON><PERSON><PERSON>", "gate_control_list": "Gate Control List", "totla_slot": "Total Slots", "interval": "Interval", "selected_queue_summary": "Selected Queue Summary", "interval_hint": "The gate interval indicates when the gate will open and how long it will remain open for. The minimum interval for Ethernet packets is listed below:", "interval_hint_1G": "1G ports: 1µs,", "interval_hint_100M": "100M ports: 10µs,", "interval_hint_10M": "10M ports: 100µs", "port_status": "Port {{ portIndex }} Status", "select_port": "Select the Port"}, "ospf": {"page_title": "OSPF", "ospf_settings": "OSPF Settings", "ospf_status": "OSPF Status", "area": "Area", "neighbor": "Neighbor", "aggregation": "Aggregation", "virtual_link": "Virtual Link", "router_id": "Router ID", "current_router_id": "Current Router ID", "current_router_id_hint": "If the Router ID is set to 0.0.0.0, the lowest interface IP address will automatically be assigned as the router ID.", "compatible_rfc_1583": "RFC 1583 Compatibility", "spf_hold_time": "SPF Hold Time", "redistribute": "Redistribute", "metric": "Metric", "ospfRRDStatic": "Static", "ospfRRDConnected": "Connected", "ospfRRDRip": "RIP", "area_size_limitation": "The maximum number of areas for this device is {{ size }}.", "area_id": "Area ID", "area_type": "Area Type", "normal": "Normal", "stub": "<PERSON><PERSON>", "nssa": "NSSA", "summary": "Summary", "no_summary": "No Summary", "delete_area_desc": "Are you sure you want to delete the selected area?", "dead_interval": "Dead Interval", "cost": "Cost", "network_type": "Network Type", "passive_interface": "Passive Interface", "neighbor_ip_address": "Neighbor IP Address", "summary_hint": "Summary is unavailable if the Area Type is set to Normal.", "broadcast": "Broadcast", "non_broadcast": "Non-broadcast", "point_to_point": "Point-to-point", "point_to_multipoint": "Point-to-multipoint", "nbr_ip_address": "Neighbor IP Address", "nbr_size_limitation": "The maximum number of neighbors for this device is {{ size }}.", "delete_nbr_desc": "Are you sure you want to delete the selected neighbor?", "lsa_type": "LSA Type", "type_7": "Type 7", "aggregation_size_limitation": "The maximum number of aggregations for this device is {{ size }}.", "delete_aggregation_desc": "Are you sure you want to delete the selected aggregation?", "vLink_size_limitation": "The maximum number of virtual links for this device is {{ size }}.", "delete_vLink_desc": "Are you sure you want to delete the selected virtual link?", "loopback": "Loopback", "waiting": "Waiting", "dr": "DR", "bdr": "BDR", "dr_other": "DR Other", "dr_router_id": "DR Router ID", "bdr_router_id": "BDR Router ID", "neighbor_id": "Neighbor ID", "neighbor_state": "Neighbor State", "dead_time": "Dead Time", "accempt": "Attempt", "two_way": "2-Way", "exstart": "Exstart", "exange": "Exchange", "loading": "Loading", "full": "Full", "database": "Database", "link_id": "Link ID", "adv_router": "ADV Router", "age": "Age", "events": "Events", "ls_retrans_queue_len": "LSA Retransmission Queue Length", "hello_suppressed": "Hello Suppressed", "router": "Router", "asbr_summary": "ASBR Summary", "as_external": "AS External", "group_member": "Group Member", "nssa_external": "NSSA External", "title_edit_ospf_redistribute": "Edit Redistribute {{ protocol }}", "title_create_ospf_area": "Create an Area", "title_edit_ospf_area": "Edit This Area", "title_delete_ospf_area": "Delete This Area", "title_create_ospf_nbr": "Create a Neighbor", "title_edit_ospf_nbr": "Edit This Neighbor", "title_delete_ospf_nbr": "Delete This Neighbor", "title_create_ospf_aggregation": "Create an Aggregation", "title_edit_ospf_aggregation": "Edit This Aggregation", "title_delete_ospf_aggregation": "Delete This Aggregation", "title_create_ospf_vlink": "Create a Virtual Link", "title_edit_ospf_vlink": "Edit This Virtual Link", "title_delete_ospf_vlink": "Delete This Virtual Link"}, "vrrp": {"page_title": "VRRP", "v2": "V2", "v3": "V3", "virtual_router_enable": "Virtual Router", "vrid": "VRID", "decrement": "Decrement", "primary_ip": "Virtual Router IP Address", "adv_int": "Advertisement Interval", "preempt_mode": "Preempt Mode", "preempt_delay": "<PERSON><PERSON><PERSON>", "accept_mode": "Accept Mode", "auth_key": "Authentication Key", "size_limitation": "The maximum number of VRRP entries for this device is {{ size }}.", "delete_vrrp_desc": "Are you sure you want to delete the selected virtual router?", "master_address": "Master Address", "master_adv_int": "Master Advertisement Interval (ms)", "master_down_int": "Master Down Interval (ms)", "title_add_vrrp": "Create a Virtual Router", "title_edit_vrrp": "Edit This Virtual Router", "title_delete_vrrp": "Delete This Virtual Router", "require_decrement_less_than_priority": "The value must be smaller than the Priority value"}, "dns": {"page_title": "DNS Settings", "primary_dns_server": "Primary DNS Server", "secondary_dns_server": "Secondary DNS Server", "dns_server_number": "DNS Server IP Address{{ number }}", "dns_server": "DNS Server", "dns_reverse_lookup": "DNS Reverse Lookup", "zone_table": "Zone Table", "dns_table_for_name": "DNS Table for {{ zoneName }}", "dns_server_summary": "DNS Server Summary", "fqdn": "FQDN", "fqdn_hint": "FQDN (Full qualified domain name) is \"Host Name\".\"Domain Name\"", "dns_forwarding": "DNS Forwarding", "dns_forwarding_hint": "An active DNS server is required to enable DNS Forwarding.", "default_forwarder_ip": "Default Forwarder IP Address", "default_forwarder_ip_hint": "If the default forwarder is specified, DNS queries for zones not listed in the Forwarders Table will be forwarded to the default forwarder.", "forwarder_ip": "Forwarder IP Address", "forwarders_table": "Forwarders Table", "zone_hint": "'.' can be used to forwarding any Zone.", "zone_size_limitation": "The maximum number of zone entries is {{ size }}.", "dns_size_limitation": "The maximum number of DNS entries is {{ size }}.", "title_create_zone": "Create a Zone", "title_edit_zone": "Edit {{ zoneName }} Settings", "title_delete_zone": "Delete Zone(s)", "delete_zone_desc": "Are you sure you want to delete the selected zone(s)?", "title_create_dns": "Create Resource Record for {{ zoneName }}", "title_edit_dns": "Edit Resource Record for {{ zoneName }}", "title_delete_dns": "Delete Resource Record(s)", "delete_dns_desc": "Are you sure you want to delete the selected resource record(s)?", "title_create_forwarding": "Create a DNS Forwarding entry", "title_edit_forwarding": "Edit DNS Forwarding entry", "title_delete_forwarding": "Delete DNS Forwarding entry", "delete_forwarding_desc": "Are you sure you want to delete the selected DNS Forwarding entry?", "duplicate_hostname": "The same hostname has already exist", "duplicate_domain_name": "The same domain name has already exist", "duplicate_zone": "This zone already exists"}, "acl": {"page_title": "Access Control List", "access_list_type": "Access List Type", "access_list_type_hint": "For the same index, the MAC address has a higher priority than the IP address.", "access_list_index_hint": "A lower index represents a higher priority.", "ip_based": "IP-based", "mac_based": "MAC-based", "acl_size_limitation": "The maximum number of ACL entries is {{ size }}.", "acl_ip_based_size_limitation": "The maximum number of IP-based ACL rules is {{ size }}.", "acl_mac_based_size_limitation": "The maximum number of MAC-based ACL rules is {{ size }}.", "acl_rule_size_limitation": "The maximum number of ACL rules is {{ size }}.", "active_interface_type": "Active Interface Type", "vlan_based": "VLAN-based", "port_based": "Port-based", "active_ingress_vlan": "Active Ingress VLAN", "active_egress_vlan": "Active Egress VLAN", "active_ingress_port": "Active Ingress Ports", "active_egress_port": "Active Egress Ports", "ingress_setting_hint": "Active VLAN and Rule VLAN must be the same.", "egress_setting_hint": "Rules with a Redirect action cannot be applied to egress interfaces.", "ingress_setting_vlan_hint": "Active VLAN and Rule VLAN must be the same.\nRules with a Remark action cannot be applied to ingress interfaces.", "egress_setting_vlan_hint": "Active VLAN and Rule VLAN must be the same.\nRules with a Redirect action cannot be applied to egress interfaces.", "rule_type": "Rule Type", "index_priority_hint": "Rules with a lower index have a higher priority.", "acl_rule": "ACL Rule", "rule": "Rule", "rule_index": "Rule Index {{ruleIndex}}", "permit": "Permit", "deny": "<PERSON><PERSON>", "ethertype_value": "EtherType Value", "goose": "GOOSE", "smv": "SMV", "protocol_number": "Protocol Number", "user_defined": "User-defined", "source": "Source", "source_port": "Source Port", "source_ip_addr": "Source IP Address", "source_ip_mask": "Source IP Mask", "source_mac_addr": "Source MAC Address", "source_mac_mask": "Source MAC Mask", "destination_port": "Destination Port", "destination_ip_addr": "Destination IP Address", "destination_ip_mask": "Destination IP Mask", "destination_mac_addr": "Destination MAC Address", "destination_mac_mask": "Destination MAC Mask", "cos_remark": "CoS Remark", "dscp_remark": "DSCP Remark", "optional_parameter": "Optional Parameter", "log": "Log", "logging": "Logging", "logging_enable": "Logging Enable", "src_port": "Src. Port", "dst_port": "Dst. Port", "icmp_type": "ICMP Type", "icmp_code": "ICMP Code", "igmp_type": "IGMP Type", "redirect": "Redirect", "redirect_mirror": "Redirect/Mirror", "redirect_enable": "Redirect", "redirect_port": "Redirect Port", "redirect_port_name": "Redirect to port {{ portName }}", "mirror": "Mirror", "session_id": "Session {{id}}", "mirror_disable_hint": "The Mirror action is unavailable if the mirroring function is disabled on the port.", "session_disable_hint": "The 'mirror' action cannot take effect on a port mirror session that is disabled.", "mirror_sesstion": "Mirror to session {{ sesstionId }}", "remark_cos": "Remark CoS to {{ cos }}", "remark_dscp": "Remark DSCP to {{ dscp }}", "acl_table_of_name": "ACL Table of {{ aclName }}", "delete_acl_list_desc": "Are you sure you want to delete the selected access control list(s)?", "delete_acl_rule_desc": "Are you sure you want to delete the selected rule(s)?", "any_hint": "If no value is entered, it will be considered as set to Any.", "log_interval": "Logging Interval", "log_threshold": "Logging Threshold", "acl_summary": "ACL Summary", "number_of_activate_acl": "Number of activated ACLs (Max. 16)", "activate_direct": "Direction", "ingress": "Ingress", "egress": "Egress", "both": "Both", "activated": "Activated", "inactivated": "Deactivated", "hit_count": "Hit Count", "counter": "Counter", "view_list": "View List", "view_by_acl": "View by ACL", "view_by_port": "View by Port", "view_by_vlan": "View by VLAN", "acl_table_of_type": "ACL Table of {{typeIndex}}", "no_activated_acl_port": "There is no ACL activated on this port.", "no_activated_acl_vlan": "There is no ACL activated on this VLAN.", "status_hint": "Rules with a lower index have a higher priority. The device will start matching packets to all rules in numerical order, starting from the lowest index. If the packet matches a rule, the corresponding rule will be applied.", "title_create_access_list": "Create an Access List", "title_edit_access_list": "Edit {{ typeIndex }} Access List Settings", "title_delete_acl_list": "Delete Access List(s)", "title_create_acl_rule": "Create Rule Index {{ ruleIndex }} for {{ typeIndex }}", "title_edit_acl_rule": "Edit the Rule Index {{ ruleIndex }} of {{ typeIndex }}", "title_delete_acl_rule": "Delete Rule(s)", "title_clear_acl_counter": "Clear Counters", "desc_clear_all_acl_counter_desc": "Are you sure you want to reset all counters?", "desc_clear_single_acl_counter_desc": "Are you sure you want to reset the counter of the {{ typeIndex }} ACL?", "blacklist_udp_port_dhcp_server": "DHCP server is not allowed", "blacklist_udp_port_dhcp_client": "DHCP client is not allowed", "blacklist_udp_port_moxa_command": "Moxa Service is not allowed", "blacklist_ether_type_eth_confg_test_protocol": "Ethernet Configuration Testing Protocol is not allowed", "blacklist_ether_type_lldp": "LLDP is not allowed", "blacklist_ether_type_eapol": "EAPOL is not allowed", "blacklist_ether_type_lacp": "LACP is not allowed", "blacklist_ether_type_llc_jumbo_frame": "LLC Jumbo frame is not allowed", "blacklist_ether_type_arp": "ARP is not allowed", "blacklist_ether_type_mrp": "MRP is not allowed", "blacklist_ether_type_profinet": "PROFINET is not allowed", "blacklist_ether_type_ptp": "PTP is not allowed", "blacklist_ether_type_goose": "GOOSE is not allowed", "blacklist_ether_type_smv": "SMV is not allowed", "blacklist_mac_ieee_reserved_multicast": "IEEE reserved multicast MAC Address is not allowed", "blacklist_mac_ip_multicast": "IP multicast MAC Address is not allowed", "blacklist_mac_broadcast": "Broadcast MAC Address is not allowed", "blacklist_mac_l2_multicast": "L2 Multicast MAC Address is not allowed", "blacklist_mac_device": "Device MAC Address is not allowed", "blacklist_dest_ip_multicast": "Multicast IP Address is not allowed", "overwrite_vlan_dialog_title": "Overwrite Rule VLAN with Active VLAN", "overwrite_vlan_dialog_content": "Active VLAN and Rule VLAN must be the same. Are you sure you want the Active VLAN to overwrite the Rule VLAN?"}, "stream_id": {"page_title": "Stream Identification", "title_create_stream_id": "Create a Stream", "title_edit_stream_id": "Edit This Stream", "title_delete_stream_id": "Delete Stream(s)", "delete_stream_id_desc": "Are you sure you want to delete the selected stream(s)?"}, "8021cb": {"page_title": "Frame Replication and Elimination for Reliability (FRER)", "frer": "FRER", "split": "Split", "forward": "Forward", "merge": "<PERSON><PERSON>", "stream_vid_mac": "Stream (VLAN / MAC Address)", "input_port": "Input Port", "input_ports": "Input Ports", "output_port_index": "Output Port {{ portIndex }}", "output_port": "Output Port", "output_ports": "Output Ports", "ingress_stream": "Ingress Stream", "egress_stream": "Egress Stream", "vlan_overwrite": "VLAN Overwrite", "mac_address_overwrite": "MAC Address Overwrite", "priority_overwrite": "Priority Overwrite", "overwrite": "Overwrite", "disable_port_input_hint": "This is a selected input port.", "disable_port_vlan_hint": "This port is not a member of the corresponding VLAN.", "disable_vid_overwrite_hint": "The output port is not a member of the corresponding VLAN.", "disable_exist_stream_hint": "There already exists a FRER entry for this stream.", "disable_select_stream_hint": "The stream has already been selected.", "to_end_device": "To End Device", "ingress_size_limitation": "The maximum ingress stream is {{ size }}.", "title_create_frer_entry": "Create a FRER Entry", "title_edit_frer_entry": "Edit This FRER Entry", "title_delete_frer_entry": "Delete This FRER Entry", "delete_frer_entry_desc": "Are you sure you want to delete the selected FRER entries?"}, "loop_protection": {"page_title": "Network Loop Protection", "detect_interval": "Detect Interval", "loop_status": "Loop Status", "peer_port": "Peer Port", "looping": "Looping"}, "binding_database": {"page_title": "Binding Database", "binding_settings": "Binding Settings", "binding_status": "Binding Status", "binding_status_hint": "Dynamic binding is learning from DHCP snooping.", "binding_status_hint_2": "The binding status will not be updated if the VLAN ID and MAC address combination of the static entry already exists.", "title_create_entry": "Create a Binding Database Static Entry", "title_edit_entry": "Edit This Binding Database Static Entry", "duplicate_of_dynamic_entry": "The VLAN ID and MAC address combination already exists. This new entry will overwrite the initial dynamic entry.", "size_limitation": "The maximum number of binding status entries is {{ size }}.", "binding_table_max": "Max. {{ size }} of Binding Status table", "dai": "DAI", "ipsg": "IPSG", "ipsg_dai": "IPSG, DAI"}, "dhcp_snooping": {"page_title": "DHCP Snooping", "port_is_ip_sg_enable": "This port is enabled for IP Source Guard. IP Source Guard can only be enabled on an untrusted port.", "port_is_dai_enable": "This port is enabled for Dynamic ARP Inspection. Dynamic ARP Inspection can only be enabled on an untrusted port.", "port_is_ip_sg_and_dai_enable": "This port is enabled for Dynamic ARP Inspection and IP Source Guard. Dynamic ARP Inspection and IP Source Guard can only be enabled on an untrusted port."}, "ip_source_guard": {"page_title": "IP Source Guard", "port_is_trusted": "This port is a trusted port for DHCP Snooping. Only untrusted ports can be enabled for IP Source Guard.", "port_is_la_member": "This port is a member of port channel. IP Source Guard can not be enabled on member port.", "binding_status_single_empty": "The binding status of the port {{ port }} is empty.", "binding_status_multiple_empty": "The binding status of the port {{ port }} are empty.", "binding_setting_hint": "You should enable DHCP snooping to get the dynamic binding or configure the data in the Binding Database -> Binding Settings."}, "mms": {"page_title": "MMS", "ied_name": "IED Name", "cid_file_settings": "CID File Settings", "report_control_block": "Report Control Block", "data_change": "Data Change", "data_update": "Data Update", "quality_change": "Quality Change", "integrity": "Integrity", "buffer_time": "Buffer Time", "integrity_period": "Integrity Period", "t_profile_cert_info": "T-Profile Certificate Information", "a_profile_cert_info": "A-Profile Certificate Information", "ca_name": "CA Name", "t_profile_security": "T-Profile Security", "a_profile_security": "A-Profile Security", "import_client_ca": "Import Client CA", "import_client_cert": "Import Client Certificate", "title_edit_name": "Edit {{ name }}", "title_mms_enable_warning": "Enable the MMS Protocol", "mms_enable_warning_desc": "Are you sure you want to enable the non-secure protocol (MMS)?"}, "password_policy": {"page_title": "Password Policy", "minimum_length": "Minimum Password Length", "policy_numbers": "Must contain at least one digit (0-9)", "policy_uppercase": "Must contain at least one uppercase letter (A-Z)", "policy_lowercase": "Must contain at least one lowercase letter (a-z)", "policy_symbols": "Must contain at least one special character ({}[]()|:;~!@#%^*-_+=,.)", "max_life_time": "Maximum Password Lifetime", "password_complexity_strengh_check": "Password Complexity Strength Check"}, "system_info": {"page_title": "Information Settings", "system_name": "Device Name", "contact_information": "Contact Information", "sync_to_chassis_id_hint": "When the LLDP chassis ID subtype is set to \"local\", modifying the device name will result in the simultaneous modification of the LLDP chassis ID."}, "login_authentication": {"page_title": "Login Authentication", "authentication_protocol": "Authentication Protocol", "radius_local": "RADIUS, Local", "tacacs_local": "TACACS+, Local"}, "login_policy": {"page_title": "Login Policy", "login_message": "Login Message", "auth_fail_message": "Login Authentication Failure Message", "failure_lockout": "Account Login Failure Lockout", "retry_failure_threshold": "Retry Failure Threshold", "lockouttime": "Lockout Duration", "auto_logout_setting": "Auto Logout After", "auto_logout_warring_title": "Disable Auto Logout", "auto_logout_setting_alert": "If the Auto Logout value is set to 0, the session will never time out. Please log out before closing your browser."}, "ip_settings": {"page_title": "IP Configuration", "ip_settings": "IP Settings", "ip_status": "IP Status", "get_ip_from": "Get IP From", "dns_server": "DNS Server IP Address", "ipv6": "IPv6", "ipv6_global_unicast_address_prefix": "IPv6 Global Unicast Address Prefix", "ipv6_dns_server_number": "IPv6 DNS Server {{ number }}", "ipv6_dns_server": "IPv6 DNS Server", "ipv6_global_unicast_address": "IPv6 Global Unicast Address", "ipv6_link_local_address": "IPv6 Link-Local Address", "profinet_dcp": "PROFINET DCP", "dhcp_bootfile": "DHCP Bootfile", "dhcp_bootfile_hint": "If enabled, the system will automatically download and restore the configuration settings of the bootfile described in Option 67 from the file server described in Option 66.", "dhcp_client": "DHCP Client-Identifier", "dhcp_client_hint": "If enabled, the system will send DHCP client messages with an Option 61 tag including a client ID. The DHCP server will assign the IP address associated with the client ID value, if available.", "dhcp_client_type": "DHCP Client-Identifier Type", "dhcp_client_value": "DHCP Client-Identifier Value"}, "management_interface": {"page_title": "Management Interface", "user_interface": "User Interface", "interface": "interface", "enable_http": "HTTP", "http_port": "HTTP - TCP Port", "enable_https": "HTTPS", "https_port": "HTTPS - TCP Port", "enable_telnet": "Telnet", "telnet_port": "Telnet - TCP Port", "ssh_port": "SSH - TCP Port", "enable_snmp_V1V2c": "SNMP version V1, V2c", "snmp_protocol": "SNMP - Transport Layer Protocol", "udp": "UDP", "tcp": "TCP", "snmp_udp_port": "SNMP - UDP Port", "snmp_tcp_port": "SNMP - TCP Port", "enable_moxa_service": "Moxa Service", "moxa_tcp_port": "Moxa Service (Encrypted) - TCP Port", "moxa_udp_port": "Moxa Service (Encrypted) - UDP Port", "max_session_http": "Maximum Number of Login Sessions for HTTP+HTTPS", "max_session_terminal": "Maximum Number of Login Sessions for Telnet+SSH", "enable_nonsecure_interface_warning_title": "Enable the {{ interfaceType }} Interface", "enable_nonsecure_interface_warning": "Are you sure you want to enable the non-secure interface ({{ interfaceType }}) ?"}, "hareward_interface": {"page_title": "Hardware Interfaces", "dip_switch": "DIP Switch", "usb_function": "USB Interface", "micro_sd_function": "MicroSD Interface"}, "account_management": {"page_title": "User Accounts", "admin": "Admin", "supervisor": "Supervisor", "user": "User", "new_password": "New Password", "title_edit_account": "Edit This Account", "title_add_account": "Create a New Account", "title_edit_account_password": "Edit the Account Password", "new_pwd_not_match": "The password does not match.", "tech_account_add_error": "The account \"moxasupport\" cannot be created because it is reserved for Moxa technical support.", "tech_account_remove_error": "The account \"moxasupport\" cannot be edited or removed because it is reserved for Moxa technical support.", "account_name_taken": "This account username is already taken", "size_limitation": "The maximum number of user accounts for this device is {{ size }}."}, "time": {"page_title": "System Time", "sntp": "SNTP", "ntp": "NTP", "time_zone": "Time Zone", "current_time": "Current Time", "daylight_saving": "Daylight Saving", "end_date": "End Date", "offset": "Offset", "ntp_authentication": "NTP Authentication", "query_interval": "Query Interval", "ptp": "PTP", "start": "Start", "end": "End", "date": "Date", "month": "Month", "week": "Week", "day": "Day", "hour": "Hour", "minute": "Minute", "jan": "Jan", "feb": "Feb", "mar": "Mar", "apr": "Apr", "may": "May", "jun": "Jun", "jul": "Jul", "aug": "Aug", "sep": "Sep", "oct": "Oct", "nov": "Nov", "dec": "Dec", "1st": "1st", "2nd": "2nd", "3rd": "3rd", "4th": "4th", "last": "last", "sun": "Sun", "mon": "Mon", "tue": "<PERSON><PERSON>", "wed": "Wed", "thu": "<PERSON>hu", "fri": "<PERSON><PERSON>", "sat": "Sat", "time_server_number": "Time Server {{ number }}", "time_server_1": "1st Time Server: IP Address/Domain Name", "time_server_2": "2nd Time Server: IP Address/Domain Name", "clock_source": "Clock Source", "key_string": "Key String", "delete_entry_confirm_desc": "Are you sure you want to delete the selected key string(s)?", "size_limitation": "The maximum number of NTP authentication keys for this device is {{ size }}."}, "ntp_server": {"page_title": "NTP Server"}, "ssh_ssl": {"page_title": "SSH & SSL", "ssh": "SSH", "ssl": "SSL", "regen_ssh_key": "Regenerate SSH Key", "regen_ssl_cert": "Regenerate SSL Certificate", "export_ssl_cert": "Export SSL Certificate", "import_ssl_cert": "Import Certificate", "ssl_info": "Certificate Information", "ca_name": "CA Name", "title_export_ssl_certificate": "Export SSL Certificate"}, "dhcp": {"page_title": "DHCP Server", "dhcp": "DHCP", "ntp_server": "NTP Server IP Address", "dhcp_pool_settings": "DHCP Pool Settings", "static_ip_assignment_table": "Static IP Assignment Table", "start_ip": "Starting IP Address", "end_ip": "Ending IP Address", "lease_time": "Lease Time", "hostname": "Hostname", "log_server": "Log Server IP Address", "gateway": "Gateway", "matching_rule": "Matching Rule", "client_id_type": "Client-Identifier Type", "client_id_value": "Client-Identifier Value", "circuit_id_type": "Option 82 Circuit ID Type", "circuit_id_value": "Option 82 Circuit ID Value", "remote_id_type": "Option 82 Remote ID Type", "remote_id_value": "Option 82 Remote ID Value", "hostname_hint": "The hostname represents the name of the DHCP client and will be encoded into the Option 12 tag of the DHCP offer packet.", "time_left": "Time Left", "dhcp_ip_mac": "DHCP/MAC-based IP Assignment", "dhcp_static_ip": "DHCP/Static IP Assignment", "lease_table": "Lease Table", "ip_mac_binding": "MAC-based IP Assignment", "ip_port_binding": "Port-based IP Assignment", "classless_static_route_table": "Classless Static Route Table", "delete_dhcp_entry_confirm_desc": "Are you sure you want to delete the selected DHCP server pool(s)?", "delete_static_ip_entry_confirm_desc": "Are you sure you want to delete the selected static IP entry(s)?", "delete_ip_port_entry_confirm_desc": "Are you sure you want to delete the selected port-based IP assignment(s)?", "delete_ip_mac_entry_confirm_desc": "Are you sure you want to delete the selected MAC-based IP assignment(s)?", "dhcp_size_limitation": "The maximum number of DHCP server pools for this device is {{ size }}.", "invalid_dhcp_pool_range": "Invalid: The management IP address of the switch should be within the range of the IP subnet.", "delete_static_route_entry_confirm_desc": "Are you sure you want to delete the selected route?", "default_gateway_setting_hint": "The default gateway for classless static routes uses the default gateway address configured in the Port-based IP Assignment configuration section."}, "dhcp_relay": {"page_title": "DHCP Relay Agent", "option82": "Option 82", "server1": "1st Server IP Address", "server2": "2nd Server IP Address", "server3": "3rd Server IP Address", "server4": "4th Server IP Address", "remote_id_type": "Remote ID Type", "remote_id_value": "Remote ID Value", "remote_id_display": "Remote ID Display", "client_id": "Client ID", "relay": "<PERSON><PERSON>"}, "ping": {"page_title": "<PERSON>", "ping_result": "Ping {{ targetHost }} result"}, "email_settings": {"page_title": "<PERSON><PERSON>s", "tls_enable": "TLS", "sender_address": "Sender Address", "recipient_1": "1st Recipient Email Address", "recipient_2": "2nd Recipient Email Address", "recipient_3": "3rd Recipient Email Address", "recipient_4": "4th Recipient Email Address", "recipient_5": "5th Recipient Email Address"}, "snmp": {"page_title": "SNMP", "v1": "V1, V2c, V3", "v2c": "V1, V2c", "v3": "V3 Only", "snmp_version": "SNMP Version", "snmp_account": "SNMP Account", "read_community": "Read Community", "read_write_community": "Read/Write Community", "read_write": "Read/Write", "des": "DES", "aes": "AES", "snmp_account_size_limitation": "The maximum number of SNMP accounts for this device is {{ size }}.", "snmp_warning_dialog_V1V2c_title": "Set the SNMP Version", "snmp_warning_dialog_V1V2c_desc": "Are you sure you want to enable the non-secure interface (SNMP version V1, V2c)?", "snmp_warning_dialog_authMD5_title": "Set the Authentication Type MD5", "snmp_warning_dialog_authMD5_desc": "MD5 authentication only provides limited security. Are you sure you want to proceed?", "change_password_title": "Change the Authentication Password", "change_key_title": "Change the Encryption Key", "change_key_button": "CHANGE ENCRYPTION KEY", "create_v3_account": "Create an SNMP Account", "edit_v3_account": "Edit This SNMP Account"}, "snmp_trap": {"page_title": "SNMP Trap/Inform", "snmp_trap_inform_recipient": "SNMP Trap/Inform Recipient", "snmp_inform_settings": "SNMP Inform Settings", "inform_retry": "Inform Retries", "inform_timeout": "Inform Timeout", "recipient_name": "Recipient IP Address/ Domain Name", "trap_community": "Trap Community", "snmp_trap_inform_account": "SNMP Trap/Inform Accounts", "trap_v1": "Trap V1", "trap_v2c": "Trap V2c", "inform_v2c": "Inform V2c", "trap_v3": "Trap V3", "inform_v3": "Inform V3", "title_delete_host_dialog_title": "Delete This Host", "title_delete_host_dialog_desc": "Are you sure you want to delete this host?", "snmp_trap_account_size_limitation": "The maximum number of SNMP trap/inform accounts for this device is {{ size }}.", "snmp_host_size_limitation": "The maximum number of SNMP trap hosts for this device is {{ size }}.", "create_v3_trap_account": "Create an SNMP Trap Account", "edit_v3_trap_account": "Edit This SNMP Trap Account", "create_host_table": "Create a Host", "edit_host_table": "Edit This Host"}, "arp": {"page_title": "ARP Table", "title_clear_message": "Clear All ARP Entries", "clear_confirmation_message": "Are you sure you want to clear all ARP entries?"}, "event_log": {"page_title": "Event Logs", "boot": "Bootup Number", "progname": "Program Name", "timestamp": "Timestamp", "uptime": "Uptime", "message": "Message", "severity_emerg": "Emergency", "severity_alert": "<PERSON><PERSON>", "severity_info": "Info", "severity_debug": "Debug", "flush_log_entry_confirmation_message": "Are you sure you want to clear all log entries?", "total_entries": "Total Entries:", "clear_all_logs": "Clear All Logs", "capacity_warning": "Capacity Warning", "capacity_warning_hint": "The Registered Action can be configured for individual events on the Event Notifications page.", "warning_threshold": "Warning Threshold", "oversize_action": "Oversize Action", "oversize_action_overwrite": "Overwrite the oldest event log", "oversize_action_stop": "Stop recording event logs", "title_clear_all_logs": "Clear All Logs", "debug_hint": "{{ number }} logs are for internal use.", "hash_value": "Hash Value", "auto_backup_of_event_log": "Auto Event Log Backup"}, "trust_access": {"page_title": "Trusted Access", "size_limitation": "The maximum number of trusted access entries for this device is {{ size }}.", "delete_all_warning_title": "Cannot Delete All Trusted Access Entries", "delete_all_warning_1": "If enabled, Trusted Access requires at least one active entry. It is highly recommended to keep", "delete_all_warning_2": "your current IP address", "delete_all_warning_3": " as an active entry."}, "utilization": {"page_title": "Resource Utilization", "cpu_utilization": "CPU Usage", "cpu_historical_record": "CPU Usage History", "mem_utilization": "Memory Usage", "mem_historical_record": "Memory Usage History", "power_utilization": "Power Consumption", "power_historical_record": "Power Consumption History", "last_update_time": "Last updated ", "used": "Used", "free": "Free", "past_10_second": "Past 10 Second", "past_30_second": "Past 30 Second", "past_300_second": "Past 300 Second", "selecting_visible_polyline": "Edit Visible Polylines", "polyline_display_hint": "Click the icon in the top-right corner of the widget to select which data to display."}, "tacacs_server": {"page_title": "TACACS+ Server", "tacacs": "TACACS+", "auth_type_asc_two": "ASCII"}, "syslog_server": {"page_title": "Syslog", "syslog_server": "Syslog Server", "auth_disable_hint": "The certificate and key cannot be enabled because they do not exist.", "tls": "TLS", "common_name": "Common Name", "expireTime": "Expiration Time", "key_limitation": "The maximum number of certification and key sets for this device is {{ size }}.", "title_add_key": "Add a Certificate and Key Set", "title_edit_key": "Edit This Certificate and Key", "delete_key_desc": "Are you sure you want to delete the certificate and key?", "client_certificate": "Client Certificate", "client_key": "Client Key", "ca_key": "CA Key"}, "radius": {"page_title": "RADIUS Server", "radius": "RADIUS", "server_address_number": "Server IP Address {{ number }}", "mschap": "MS-CHAPv1", "mschap_v2": "MS-CHAPv2"}, "config_bk_res": {"page_title": "Configuration Backup and Restore", "menu_title": "Config Backup and Restore", "file_encryption": "File Encryption", "file_signature": "File Signature", "config_name": "Configuration Name", "config_file_encryption": "Configuration File Encryption", "configuration_selection": "Select Configuration", "running_configuration": "Running Configuration", "startup_configuration": "Startup Configuration", "default_configuration": "Default Configuration", "not_included": "Not Included", "included": "Included", "signed_config": "Signed Configuration", "sign_hint": "When enabled, a digital signature is added when an administrator backs up or restores the configuration.", "sign_disable_hint": "This function cannot be enabled because the private and public keys are empty.", "private": "Private", "certificate": "Certificate", "label": "Label", "length": "Length", "key_limitation": "The maximum number of key pairs for this device is {{ size }}.", "title_add_key": "Add a Custom Key", "title_edit_key": "Edit This Custom Key", "delete_key_desc": "Are you sure you want to delete this key pair?", "auto_bk_of_config": "Auto Configuration Backup", "auto_load_of_config": "Auto Configuration Restore", "auto_restore": "Auto Configuration Restore", "auto_restore_hint": "Automatically restore the configuration from an external storage device during bootup.", "encrypt_whole_file": "Encrypt the entire file", "encrypt_sensitive_information_only": "Encrypt sensitive information only", "encrypt_hint": "If \"Encrypt sensitive information only\" is selected and the Encryption Key field is left blank, the Moxa encryption key will be used instead."}, "firmware_upgrade": {"page_title": "Firmware Upgrade"}, "module_information": {"page_title": "Module Information", "module_name": "Module Name", "no_module_msg": "No module installed"}, "event_notification": {"page_title": "Event Notifications", "group": "Group", "event_name": "Event Name", "system_and_function": "System and Functions", "registered_event": "Registered Event", "registered_action": "Registered Action", "registered_port": "Registered Port", "group_general": "General", "group_switching": "Switching", "group_poe": "PoE", "group_routing": "Routing", "group_tracking": "Tracking", "notification_loginSuccess": "Login success", "notification_loginFail": "<PERSON><PERSON> failed", "notification_loginLockout": "Login lockout", "notification_accountChanged": "Account settings changed", "notification_certificationChanged": "SSL certification changed", "notification_passwordChanged": "Password changed", "notification_coldStart": "Cold start", "notification_warmStart": "Warm start", "notification_configurationChanged": "Configuration changed", "notification_configurationImported": "Configuration imported", "notification_logCapacityThreshold": "Log capacity threshold", "notification_powerOff": "Power On->Off", "notification_powerOn": "Power Off->On", "notification_diOn": "DI on", "notification_diOff": "DI off", "notification_topologyChanged": "Topology changed", "notification_couplingChanged": "Coupling changed", "notification_masterChanged": "Master changed", "notification_masterMismatch": "Master mismatch", "notification_rstpTopologyChanged": "RSTP topology changed", "notification_rstpRootChanged": "RSTP root changed", "notification_rstpMigration": "RSTP migration", "notification_rstpInvalidBpdu": "RSTP invalid BPDU", "notification_rstpNewPortRole": "RSTP new port role", "notification_mstpTopologyChanged": "MSTP topology changed", "notification_mstpRootChanged": "MSTP root changed", "notification_mstpNewPortRole": "MSTP new port role", "notification_linkHealthyCheckFail": "Redundant port health check failed", "notification_dualHomingPathSwitched": "Dual homing path changed", "notification_dot1xAuthFail": "802.1X auth failed", "notification_lldpTableChanged": "LLDP table changed", "notification_rmonRaisingAlarm": "RMON raising alarm", "notification_rmonFallingAlarm": "RMON failing alarm", "notification_macsecInterfaceMKAFail": "MACsec MKA failed", "notification_dhcpsnpDynamicEntrySetFailed": "Binding Status dynamic entry failed", "notification_dhcpsnpUntrustMacDiscard": "DHCP client ingress discards packets due to the DHCP Snooping rule", "notification_dhcpsnpUntrustServerDiscard": "DHCP server discards packets due to the DHCP Snooping rule", "notification_multipleCouplingPathChanged": "Multiple coupling path changed", "notification_dhcpBootfileFail": "DHCP Bootfile failed", "notification_trackingStatusChanged": "Tracking Status Changed", "notification_trackingReactionPort": "Tracking Action Triggered on Port", "notification_trackingReactionStaticRoute": "Tracking Action Triggered on Static Route", "notification_trackingReactionVrrp": "Tracking Action Triggered on VRRP", "notification_pdPowerOn": "PD power on", "notification_pdPowerOff": "PD power off", "notification_lowInputVoltage": "Low input voltage", "notification_pdOverCurrent": "PD over-current", "notification_pdNoResponse": "PD no response", "notification_overPowerBudgetLimit": "Over power budget limit", "notification_powerDetectionFailure": "Power detection failure", "notification_nonPdOrPdShort": "Non-PD or PD short circuit", "notification_portOn": "Port On", "notification_portOff": "Port Off", "notification_rateLimitedOn": "Port shut down by Rate Limit", "notification_rateLimitedOff": "Port recovered by Rate Limit", "notification_psecViolationPortDown": "Port shut down by Port Security", "notification_fiberWarning": "Fiber Check warning", "notification_linkUp": "Interface up", "notification_linkDown": "Interface down", "notification_adjacencyChanged": "OSPF adjacency changed", "notification_drChanged": "OSPF DR changed", "notification_becomeDR": "OSPF become DR", "notification_vrrpMasterChanged": "VRRP virtual router master changed", "notification_pimSmDrChanged": "PIM-SM DR changed", "notification_pimSmRpAdded": "PIM-SM RP added by BSM", "notification_pimSmRpDeleted": "PIM-SM RP deleted by BSM", "notification_supABportTimediff": "A PHR Supervision frame time difference event occurred on ports A, B", "notification_gcTimeout": "GOOSE Check entry counter timeout", "notification_gcTimeoutClear": "GOOSE Check entry counter timeout clear", "notification_gcPortTampered": "GOOSE Check tampered ingress port", "notification_gcAddrTampered": "GOOSE Check tampered source MAC address", "notification_gcLockViolation": "GOOSE Check Lock violation", "notification_gcEntryReset": "GOOSE Check entry reset", "notification_gcEntryDelete": "GOOSE Check entry delete", "action_trap": "Trap", "information": "Information", "title_edit_event_notification": "Edit This Event Notification"}, "relay_output": {"page_title": "<PERSON><PERSON>", "relay_alarm_cut_off": "<PERSON><PERSON> Cut-Off", "relay_alarm_settings": "<PERSON><PERSON> Alarm Settings", "fault_led_display": "Fault LED Display", "cut_off": "Cut-off"}, "statistics": {"page_title": "Network Statistics", "bandwidth_utilization": "Bandwidth Usage", "packet_counter": "Packet Counter", "rxTotalOctets": "Rx Total Octets", "collisionPackets": "Collision Packets", "dropPackets": "Dropped Packets", "dropPacketsHint": "The default update time of the Dropped Packet counter is about 5 seconds, and increases based on the number of ports on the device.", "rxPausePackets": "Rx Pause Packets", "txTotalOctets": "Tx Total Octets", "txUnicastPackets": "Tx Unicast Packets", "crcAlignErrorPackets": "CRC Align Error Packets", "txMulticastPackets": "Tx Multicast Packets", "rxBroadcastPackets": "Rx Broadcast Packets", "rxUnicastPackets": "Rx Unicast Packets", "jabberPackets": "Jabber Packets", "excessiveCollisionPackets": "Excessive Collision Packets", "txTotalPackets": "Tx Total Packets", "fragmentPackets": "Fragmented Packets", "rxTotalPackets": "Rx Total Packets", "lateCollisionPackets": "Late Collision Packets", "oversizePackets": "Oversized Packets", "rxMulticastPackets": "Rx Multicast Packets", "txBroadcastPackets": "Tx Broadcast Packets", "undersizePackets": "Undersize Packets", "txptpPackets": "Tx PTP Packets", "rxptpPackets": "Rx PTP Packets", "displayMode": "Display Mode", "packetCounter": "Packet Counter", "bandwidthUtilization": "Bandwidth Usage", "line_num_target_port": "Line {{ number }} Monitoring Port", "line_num_target_sniffer": "Line {{ number }} Sniffer", "txandrx": "Tx/Rx", "txonly": "Tx", "rxonly": "Rx", "all_port": "All Ports", "all_ge_port": "All GE Ports,", "all_fe_port": "All FE Ports,", "line_num": "Line{{ number }}", "clear_graph_desc": "Are you sure you want to clear all graph data?", "clear_table_desc": "Are you sure you want to clear all table data?", "benchmark_line": "Benchmark", "benchmark_line_time": "Benchmark Line - Time", "comparison_line": "Comparison", "comparison_line_time": "Comparison Line - Time", "selecting_visible_columns": "Edit Visible Columns", "title_comparison": "Compare Data", "title_reset_statistics_graph": "Reset the Statistics Graph", "title_edit_statistics_setting": "Display Settings", "title_clear_statistics_counter": "Clear Counters"}, "mab": {"page_title": "MAC Authentication Bypass", "page_title_abbr": "MAB", "auth_mode": "Authentication Mode", "local_database": "Local Database", "quiet_period": "Quiet Period", "reauthentication": "Reauthentication", "reauth_period": "<PERSON>auth Period", "size_limitation": "The maximum number of MAC address entries for this device is {{ size }}.", "retransmit": "Retransmit", "force_unauthorized": "Force Unauthorized", "force_Authorized": "Force Authorized", "reauth_port_confirm": "Are you sure you want to reauthenticate the port?", "authorized": "Authorized", "unauthorized": "Unauthorized", "title_reauth_port": "Reauth Port", "port_setting": "Port {{ portIndex }} Settings", "account_setting": "Account {{ userName }} Settings", "timeout_retransmit_hint": "All of the retry times cannot exceed the Dot1x server timeout value. Note: All of the retry times = Timeout * (Retransmit + 1). Recommend value: {{ number }}", "clear_button_disabled_hint": "Clear all MAC address entries gathered through MAC Authentication Bypass when the number of entries reaches maximum capacity.", "clear_warning_hint": "The number of MAC address entries gathered through MAC Authentication Bypass is at maximum capacity.", "title_clear_mac_address": "Clear MAC Addresses", "clear_mac_address_message": "Are you sure you want to clear all MAC addresses gathered through MAC Authentication Bypass?", "radius_hint": "802.1X and MAC Authentication Bypass share the same RADIUS server."}, "modbus_tcp": {"page_title": "Modbus TCP", "enable_modbus_tcpo_title": "Enable Modbus TCP", "enable_modbus_tcp_warning": "Are you sure you want to enable non-secure protocol (Modbus TCP)?"}, "fiber_check": {"page_title": "Fiber Check", "threshold_settings": "<PERSON><PERSON><PERSON><PERSON>", "model_name": "Model Name", "serial_number": "Serial Number", "wavelength": "Wavelength", "voltage": "Voltage", "temperature": "Temperature", "tx_power": "Tx Power", "rx_power": "Rx Power", "temperature_limit": "Temperature Threshold", "tx_power_threshold_low": "Tx Power Threshold Low", "tx_power_threshold_high": "Tx Power Threshold High", "rx_power_threshold_low": "Rx Power Threshold Low", "rx_power_threshold_high": "Rx Power Threshold High", "temp_over_spec": "Temperature is over the threshold.", "tx_power_over_spec": "Tx power is over the threshold.", "tx_power_under_spec": "Tx power is under the threshold.", "rx_power_over_spec": "Rx power is over the threshold.", "rx_power_under_spec": "Rx power is under the threshold.", "port_copy_to_copper_port_disable": " Cannot copy configurations to copper port.", "warning": "Warning", "reset_title": "Reset Port {{ portIndex }} Threshold Settings", "reset_desc": "Are you sure you want to reset this port to auto mode (default) and clear all of the threshold settings?", "reset_all_ports": "Reset All Ports", "reset_all_ports_title": "Reset All Ports Threshold Settings", "reset_all_ports_desc": "Are you sure to reset all the ports to auto mode (default) and clear all of the threshold settings?"}, "dai": {"page_title": "Dynamic ARP Inspection", "port_is_la_member": "This port is a member of port channel. Dynamic ARP Inspection can not be enabled on member port.", "port_is_trusted": "This port is a trusted port for DHCP Snooping. Only untrusted ports can be enabled for Dynamic ARP Inspection."}, "mac_sec": {"page_title": "MAC security", "mka_status": "MKA Status", "participant_ckn": "Participant CKN", "participant_cak": "Participant CAK", "key_server": "Key Server", "peer_list_mi": "Peer List Member Identifier (MI)", "peer_list_mn": "Peer List Message Number (MN)", "sci": "Secure Channel ID (SCI)", "peer_list_type": "Peer List Type", "live_peer_list": "Live Peer List", "potential_peer_list": "Potential Peer List", "peer_list_mi_hint": "MI: The MKA messages include the sender's own member ID and the IDs of other potential peers that it has received messages from.", "not_support_10G_port": "MACsec is not supported on 10G ports.", "member_port_disable": "This port is a member of a port channel. Media Access Control Security cannot be edited on a member port."}, "profinet": {"page_title": "PROFINET", "protocol_information": "Protocol Information", "enable_profinet_title": "Enable PROFINET", "enable_profinet_warning": "Are you sure you want to enable a non-secure protocol (PROFINET)?", "vlan_not_exist": "The selected interface does not exist.", "label_format_hint": "This is the format for the label. Each label should be separated by a “.”. The maximum characters for a label is 63."}, "ethernet_ip": {"page_title": "EtherNet/IP", "enable_eip_title": "Enable EtherNet/IP", "enable_eip_warning": "Are you sure you want to enable a non-secure protocol (EtherNet/IP)?"}, "multi_coupling": {"page_title": "Multiple Network Coupling", "main_ring_protocol": "Main Ring Protocol", "switch_role": "Coupling switch role", "group_id": "Coupling group ID", "polling_interval": "Coupling polling interval", "polling_interval_hint": "For the main ring, the maximum number of path IDs is 16 when using 80ms polling interval. When using 40ms polling interval, we suggest using 8 path IDs.", "table_settings": "Coupling Table Settings", "table_status": "Coupling Table Status", "path_id": "Path ID", "coupling_port": "Coupling Port", "coupling_port_state": "Coupling port state", "partner_mac": "Partner MAC", "connect_status": "Connection Status", "error_status": "Error Status", "multiple_active": "Multiple active switches", "multiple_backup": "Multiple backup switches", "role_information": "Role Information", "edit_path_title": "Edit Path ID {{ pathId }} Settings", "multi_coupling_enable_hint": "This feature requires Turbo Ring v2, MRP, or HSR main ring to be enabled.", "is_coupling_port": "This has been selected as a coupling port.", "is_selected_path": "This path ID has already been selected.", "error_status_message": "Multiple active/backup switches have been detected. Please check this device and the partner device to ensure there are no duplicates."}, "pim_dm": {"page_title": "PIM-DM", "state_refresh": "State Refresh", "state_refresh_interval": "State Refresh Interval", "pim_dm_hint": "When PIM-DM is enabled, if there is a request for IGMP, it can be used together with IGMP snooping.", "state_refresh_hint": "All routers in PIM-DM mode network must enable State Refresh and configure the same interval."}, "pim_sm": {"page_title": "PIM-SM", "pim_sm_settings": "PIM-SM Settings", "pim_sm_hint": "When PIM-SM is enabled, please make sure you enable IGMP snooping and the related configurations based on your needs.", "spt_method": "Shortest-path Tree Switchover Method", "join_prune_interval": "Join/<PERSON><PERSON>e <PERSON>", "dr_priority": "DR Priority", "bsr": "BSR", "bsr_candidate": "BSR Candidate", "bsr_address": "BSR Address", "bsr_priority": "BSR Priority", "bsr_hash_mask_length": "BSR Hash Mask Length", "dr_priority_hint": "The DR with the larger priority value is preferred in DR election.", "bsr_priority_hint": "The BSR with the larger priority value is preferred in BSR election.", "rp": "RP", "static_rp": "Static RP", "candidate_rp": "Candidate RP", "group_address": "Group Address", "group_mask": "Group Mask", "rp_address": "RP Address", "override": "Override", "interface_not_created": "The PIM-SM interface has not been created yet.", "rp_if_name": "RP Interface Name", "rp_priority": "RP Priority", "static_rp_size_limitation": "The maximum number of Static RP for this device is {{ size }}.", "candidate_rp_size_limitation": "The maximum number of Candidate RP for this device is {{ size }}.", "ssm": "SSM", "pim_ssm": "PIM-SSM", "pim_ssm_size_limitation": "The maximum number of PIM-SSM for this device is {{ size }}.", "title_create_static_rp": "Create Static RP", "title_edit_static_rp": "Edit Static RP", "title_delete_static_rp": "Delete Static RP", "delete_static_rp_desc": "Are you sure you want to delete the selected static RP?", "override_hint": "Override means this static RP will be used prior to dynamic learned(BSR) when conflict.", "title_create_candidate_rp": "Create Candidate RP", "title_edit_candidate_rp": "Edit Candidate RP", "title_delete_candidate_rp": "Delete Candidate RP", "delete_candidate_rp_desc": "Are you sure you want to delete the selected candidate R<PERSON>?", "rp_priority_hint": "The RP with the smaller priority value is preferred in RP election.", "rp_if_name_address": "RP Interface Name (RP Address)", "title_add_ssm_range": "Add SSM Range", "title_edit_ssm_range": "Edit SSM Range", "title_delete_ssm": "Delete SSM", "delete_ssm_desc": "Are you sure you want to delete the selected SSM?", "static_ssm_entry_hint": "This range is reserved for SSM in RFC 7761.", "pim_sm_status": "PIM-SM Status", "dr_address": "DR Address", "bsr_rp": "BSR / RP", "elected_bsr": "Elected BSR", "rp_mapping": "RP Mapping", "rp_mapping_result": "RP Mapping Result"}, "multicast_routing_table": {"page_title": "Multicast Routing Table", "multicast_group": "Multicast Group", "upstream_neighbor": "Upstream Neighbor", "incoming_interface": "Incoming Interface", "outgoing_interface": "Outgoing Interface", "prune": "<PERSON><PERSON><PERSON>", "assert": "Assert"}, "prp_hsr": {"page_title": "PRP/HSR", "prp_hsr_protocol": "PRP/HSR Protocol", "entry_forget_time": "Entry Forget Time", "net_id": "Net ID", "lan_id": "LAN ID", "prp": "PRP", "hsr": "HSR", "coupling": "Coupling", "enable_prp_hsr_title": "Enable PRP/HSR", "enable_prp_hsr_warning": "Enabling PRP/HSR will reset the configuration of all the ports on the PRP/HSR module to their default values. Are you sure?", "no_phr_module_warning": "No valid PHR module detected. Check PHR module."}, "tracking": {"page_title": "Tracking", "tracking_list_of_interface": "Tracking List of Interface", "tracking_list_of_ping": "Tracking List of Ping", "tracking_list_of_logical": "Tracking List of Logical", "tracking_list_of_all": "Tracking List of All", "tid": "Tracking ID", "down_to_up": "Down to Up", "up_delay": "Up Delay", "up_to_down": "Up to Down", "down_delay": "Down Delay", "received": "Received", "lost": "Lost", "and": "AND", "or": "OR", "nand": "NAND", "nor": "NOR", "entry_state": "State", "interface_tracking": "Interface Tracking", "ping_tracking": "Ping Tracking", "logical_tracking": "Logical Tracking", "create_interface_tracking_entry_title": "Create an Interface Tracking Entry", "edit_interface_tracking_entry_title": "Edit This Interface Tracking Entry", "create_ping_tracking_entry_title": "Create a Ping Tracking Entry", "edit_ping_tracking_entry_title": "Edit This Ping Tracking Entry", "create_logical_tracking_entry_title": "Create a Logical Tracking Entry", "edit_logical_tracking_entry_title": "Edit This Logical Tracking Entry", "interface_type": "Interface Type", "port": "Port", "ping": "<PERSON>", "logical": "Logical", "interface": "Interface", "network_interface": "Network Interface", "status_change_from_down_to_up": "State Change (Down to Up)", "status_change_from_up_to_down": "State Change (Up to Down)", "logical_list": "Logical List", "logical_oper": "Logical Operator", "interfaec_ip_logic": "Interface/IP Address/Logical List", "time_since_last_change": "Time Since Last Change", "no_of_change": "No. of Changes", "only_select_four": "A maximum of four can be selected", "require_tid_larger": "The Tracking ID must be larger than the TID of the Logical List.", "require_at_least_two_tid": "More than two TIDs are required", "duplicated_tid": "Duplicated Tracking ID", "tracking_size_limitation": "The maximum number of Tracking entries for this device is {{ size }}.", "sync_to_latest_status": "Sync. to Lastest State"}, "auto_config": {"page_title": "Auto Configuration", "cdu_port": "Control Unit Port", "auto_config_info": "Auto Configuration Information", "import_mode_hint": "It requires DHCP Client and LLDP to be enabled, and the DHCP Client boot file and Client ID to be preconfigured. In this mode, Auto Configuration only sends Option 61 packets over the Control Unit port.", "propagate_mode_hint": "It requires IP Configuration to be set to manual and LLDP to be enabled. In this mode, the DHCP server assigns IP addresses based on LLDP information."}, "multi_local_route": {"page_title": "Multicast Local Route", "routes": "Routes", "macl": "MACL", "vrrp_master_only": "VRRP Master Only", "multi_local_route_hint": "If Multicast Local Route is enabled, IGMP Snooping must also be enabled.", "vrrp_master_only_hint": "If enabled, the switch can only route multicast streams when it acts as the VRRP master.", "source_vlan": "Source VLAN", "downstream_vlan": "Downstream VLAN", "multi_local_route_size_limitation": "The maximum number of Multicast Local Route entries is {{ size }}.", "create_route_msg": "Create Multicast Local Route", "edit_route_msg": "Edit Source VLAN {{ vid }}", "macl_id": "MACL ID", "title_create_macl_rule": "Create Multicast ACL", "title_edit_macl_rule": "Edit MACL ID {{ maclId }}", "only_select_sixteen": "A maximum of sixteen can be selected", "delete_session_title": "Delete Multicast Local Route", "delete_session_content": "Are you sure you want to delete the selected Multicast Local Route?", "source_vlan_cannot_set": "The Source VLAN cannot be set."}, "supervision_frame": {"page_title": "Supervision Frame", "supervision_frame_enable_hint": "PRP/HSR protocol must be enabled before Supervision Frame is enabled.", "life_check_interval": "Life Check Interval", "destination_address": "Destination Address", "forward_to_interlink": "Supervision Forward To Interlink", "nodes_table": "Nodes Table", "forget_time": "Node Forget Time", "node_type": "Node Type", "time_last_seen_a": "Time Last Seen A", "time_last_seen_b": "Time Last Seen B"}, "goose_check": {"page_title": "GOOSE Check", "goose_lock": "GOOSE Lock", "goose_lock_hint": "If GOOSE Lock is enabled, GOOSE packets that are not shown in the monitoring table will be dropped.", "tamper_response": "Tamper Response", "tamper_response_hint": "Choosing \"drop\" will drop any tampered GOOSE packets.\nChoosing \"port disable\" will disable the ingress port of the tampered GOOSE packets. ", "port_disable": "Port Disable", "app_id": "APP ID", "goose_address": "GOOSE Address (DA)", "monitoring_table_status": "Monitoring Table Status", "goose_lock_status": "GOOSE Lock Status", "lock_violation_status": "Lock Violation Status", "goose_name": "GoCB Name", "rx_counter": "Rx Counter", "create_goose": "Create Static GOOSE Entry", "port_tampered": "Tampered Port", "sa_tampered": "Tampered SA", "duplicate_goose": "An identical static GOOSE entry already exists", "exist_dynamic_entry": "An identical dynamically learned GOOSE entry already exists. Clicking Apply will change this entry to static.", "lock_violation_normal_hint": "All detected GOOSE packets are shown in the monitoring table.", "lock_violation_warning_hint": "Unexpected GOOSE packets were detected that are not be shown in the monitoring table.", "goose_table_max": "Max. {{ size }} of Monitoring table", "size_limitation": "The maximum number of goose check entries for this device is {{ size }}."}}, "request_handler": {"action_saving": "Saving ...", "action_loading": "Loading ...", "action_upgrading": "Upgrading ...", "action_ping": "Pinging ..."}, "response_handler": {"res_server_error": "Server connection error.", "res_global_success": "Successfully updated.", "res_complete_refresh": "Refresh completed.", "res_complete_encrypt_data": "Encrypt data completed.", "res_port_success": "Successfully updated the Port settings.", "res_entry_create_success": "Successfully created the entry.", "res_entry_update_success": "Successfully updated the entry.", "res_entry_delete_success": "Successfully deleted the entry.", "res_port_enable_success": "Port {{ portIndex }} has been successfully enabled.", "res_port_disable_success": "Port {{ portIndex }} has been successfully disabled.", "res_dscp_success": "Successfully updated the DSCP Settings.", "res_cos_success": "Successfully updated the CoS settings.", "res_regen_ssh_success": "Successfully regenerated the SSH Key.", "res_regen_ssl_success": "Successfully regenerated the SSL certificate.", "export_ssl_cert": "Successfully exported the SSL certificate.", "import_ssl_cert": "Successfully imported the SSL certificate.", "import_config": "Successfully imported configuration.", "res_copy": "Copied.", "res_ping_success": "<PERSON> finished.", "res_auto_save_mode_success": "Successfully updated Auto Save Mode.", "res_switch_browse_mode_success": "Successfully switched modes.", "res_v3_account_update_success": "Successfully updated the authentication account.", "res_host_update_success": "Successfully updated the SNMP Host.", "res_upgrading_firmware_success": "Successfully upgraded the firmware. The device will now restart.", "res_event_notification_success": "Successfully updated Event Notifications.", "res_save_to_startup_success": "Successfully saved the running configuration to the startup configuration.", "clear_success": "Successfully cleared.", "res_factory_default_success": "Successfully reset to factory defaults.", "backup_success": "Successfully backed up.", "res_custom_default_success": "Successfully reset to custom defaults.", "download_success": "Successfully downloaded.", "locator": "Successfully triggered the Device Locator.", "re_auth_port_success": "Successfully reauthenticated the port.", "res_recovery_port_success": "The port successfully recovered."}, "error_handler": {"error_session_expired_dialog": "This session has expired. The system will return to the login page."}, "validators": {"required": "Required", "require_min_length": "Minimum {{ number }} characters", "require_range_between": "{{ rangeBegin }} - {{ rangeEnd }}", "require_range_single_between": "{{ singleNum }}, {{ rangeBegin }} - {{ rangeEnd }}", "require_hexadecimal": "Hex digits only", "require_unicast": "Unicast IP only", "required_at_least_two": "At least 2 output ports", "required_at_least_one_overwrite": "At least one overwrite item", "required_priority_multiple": "The priority must be a multiple of {{ num }}", "required_label_max_length": "The maximum characters for a label is {{ number }}", "required_interval_multiple": "The interval must be a multiple of {{ num }}", "required_timeout_multiple": "The timeout inteval must be a multiple of {{ num }}", "invalid": "Invalid", "invalid_format": "Invalid format", "invalid_positive_integer": "Invalid positive integer", "invalid_hex": "Invalid hex number", "invalid_range": "The valid range is from {{ rangeBegin }} to {{ rangeEnd }}", "invalid_single_range": "Range is {{ singleNum }} or {{ rangeBegin }} to {{ rangeEnd }}", "invalid_mac_address": "Invalid MAC address", "invalid_ip_address": "Invalid IP address", "invalid_area_id": "Invalid Area ID", "invalid_router_id": "Invalid Router ID", "invalid_vlan_port": "Invalid VLAN member port", "invalid_vlan_output_port": "Invalid VLAN for the output port", "invalid_netmask": "Invalid <PERSON>", "invalid_char": "Only a-z, A-Z, 0-9 are allowed", "invalid_email": "Invalid email", "invalid_regex_level_1": "Only a-z, A-Z, 0-9 or . - _ are allowed", "invalid_regex_level_2": "Only a-z, A-Z, 0-9 or . , - _ + = | : ; @ ! ~ # % ^ * ( ) [ ] { } are allowed", "invalid_sys_desc": "Only a-z, A-Z, 0-9 or ~ ! @ # $ % ^ & * ( ) { } [ ] < > _ + - = \\ : ; , . / are allowed", "invalid_login_failure_message": "Only a-z, A-Z, 0-9 or ! # $ % & ' ( ) * + , \\ - . / : ; < = > @ [ ] ^ _ ` { | } ~ are allowed", "invalid_regex_macsec_cak_and_ckn": "Only a-z, A-Z, 0-9 or @ % $ ^ * ' ` ( ) _ + = { } : . , ~ [ ] - are allowed.", "invalid_char_and_dash": "Only a-z, A-Z, 0-9 or - are allowed", "invalid_char_and_dot_dash": "Only a-z, A-Z, 0-9 or . - are allowed", "invalid_lowercase_and_dash_dot": "Only a-z, 0-9 or . - are allowed", "invalid_file_name": "Only a-z, A-Z, 0-9 or / ( ) . - _ are allowed", "duplicate_ip": "Duplicate IP", "duplicate_ip_range": "Duplicate IP range", "duplicate_vrid": "Duplicate VRID", "duplicate_stream": "This stream already exists", "duplicate_id": "Duplicate ID", "duplicate_input_ports": "Duplicate input ports", "duplicate_loopback_id": "This Loopback ID is already in use", "duplicate_vlan_id": "This VLAN ID is already in use.", "duplicate_vlan_and_mac": "This VLAN ID and MAC address combination already exists", "duplicate_group_address_and_netmask": "This group address and group netmask combination already exists", "two_deciaml_palce": "Maximum 2 decimal places", "duplicate_ip_and_netmask": "This IP Address and Subnet Mask combination already exists", "three_deciaml_palce": "Maximum 3 decimal places", "same_as_ingress_stream": "Same as the ingress stream"}}