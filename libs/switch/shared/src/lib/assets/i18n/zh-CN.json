{"__readme__": {"__1__": "All key naming is snake_case, in addition to using uri key, e.g.'ospfRRDStatic'", "__2__": "In 'common', words are used repeatedly. It can be reclassified and named 'common_xxx', e.g.'common_port', 'common_account'...", "__3__": "In 'dialog', words are used repeatedly in the dialog.", "__4__": "In 'button', put all button words in and must be capitalized"}, "login": {"title_login_records": "登录记录", "redirected_message": "注销成功", "login_success_records_greeting": "欢迎 {{ name }}", "login_success_records_datetime": "上次成功登录时间是 {{ time }}.", "login_fail_records": "最新登录失败记录", "modify_password_notification": "请更改默认用户名和密码以增强安全性。", "password_expire_notification": "您的密码已过期。请更改您的密码。", "daylight_saving_upgrade": "夏令时功能已升级。", "daylight_saving_notification": "请更新您的配置。", "factory_default_note": "请注意，您需要使用默认网络设置来重新建立与交换机的网页连接。", "firmware_upgrade_note": "请注意，您需要重新建立与交换机的网页连接。"}, "general": {"top_nav": {"config_change": {"start_up_save_tip": "配置尚未保存到启动配置。", "confirmation_message": "您确定要将运行配置应用到启动配置吗？", "title_apply_to_start_config": "应用于启动配置"}, "user_profile": {"greeting": "你好， {{ username }}", "enable_auto_save": "启用自动保存", "disable_auto_save": "禁用自动保存", "disable_auto_save_hint": "应用后，配置将保存到运行配置而不是启动配置。", "change_language": "更改语言", "change_mode": "改变模式", "locator": "定位器", "reset_factory_default": "重置为默认设置", "save_custom_default": "保存自定义默认值", "standard_mode": "标准", "advanced_mode": "进阶", "standard_mode_tooltip": "标准模式：  一些功能/参数将被隐藏以使其更简单。", "advanced_mode_tooltip": "进阶模式：  想要调整这些设置的用户将可以使用进阶功能/参数。"}, "auto_save_mode": {"enable_auto_save_title": "启用自动保存模式", "enable_auto_save_msg": "您确定要启用自动保存模式吗？", "disable_auto_save_title": "禁用自动保存模式", "disable_auto_save_msg": "您确定要禁用自动保存模式吗？"}, "advanced_browse_mode": {"advanced_notification_title": "更改为进阶模式", "advanced_notification_msg": "您确定要从标准模式更改为进阶模式吗？", "basic_notification_title": "更改为标准模式", "basic_notification_msg": "您确定要从进阶模式更改为标准模式吗？"}, "locator": {"title_switch_locator": "交换机定位器", "duration": "持续时间", "hint": "触发设备上的 LED 开始闪烁，以便更容易定位。"}, "restart_machine": {"confirmation_title": "重启", "confirmation_msg": "您确定要重新启动设备吗？"}, "factory_default": {"confirmation_title": "出厂默认值", "confirmation_msg": "您确定要将系统配置重置为出厂默认设置吗？", "factory_default_hint": "重置为出厂默认设置将清除自定义默认配置。", "custom_default": "自定义默认值", "confirmation_msg_custom_default": "您确定要将系统配置重置为自定义默认值吗？", "custom_default_not_exist": "当自定义默认值的状态为 \"未找到配置！\"时,无法执行自定义默认值。", "saved_config_name": "保存的配置名称", "config_name_hint": "自定义默认配置名称保存在非易失性存储器中。", "clear_all_config": "删除配置、日志记录与授信密码"}, "save_custom_default": {"confirmation_msg": "您确定要将启动配置保存为自定义默认值吗？", "current_config_name": "当前配置名称", "config_name_hint": "可以在\"Config Backup and Restore\"页面上修改配置名称。"}, "logout": {"confirmation_title": "退出", "confirmation_msg": "您确定要退出吗？"}}, "page_state": {"page_not_found": "无法找到页面", "page_not_found_desc": "此服务器上找不到所请求的 URL。", "back_link": "返回索引页"}, "menu_tree": {"jump_page_placeholder": "搜索功能", "system": "系统", "system_management": "系统管理", "account_management": "帐户管理", "provisioning": "正在配置", "port_interface": "端口接口", "l2_switching": "二层交换", "unicast_route": "单播路由", "multicast_route": "多播路由", "mac": "MAC", "qos": "QoS", "redundancy": "冗余", "l2_redundancy": "二层冗余", "l3_redundancy": "三层冗余", "network_service": "网络服务", "routing": "路由", "network_management": "网络管理", "device_security": "设备安全", "network_security": "网络安全", "diagnostics": "诊断", "network_status": "网络状态", "tools": "工具", "log_and_event_notification": "事件日志和通知", "application": "工业应用", "iec61850": "IEC 61850", "iec62439_3": "IEC 62439-3"}, "dialog": {"title_refresh_browser": "刷新浏览器", "title_change_default_password": "更改默认密码", "title_change_password": "更改密码", "title_session_expired": "会话已过期", "title_notification": "通知", "title_edit_interface": "编辑界面 {{ interfaceName }}", "edit_port_msg": "编辑端口 {{ portIndex }} 设置", "edit_vlan_msg": "编辑VLAN {{ vlanIndex }} 设置", "create_entry_msg": "创建条目", "edit_entry_msg": "编辑条目设置", "delete_entry_msg": "删除条目", "title_delete_key": "删除证书和密钥", "title_delete_account": "删除帐户", "delete_entry_confirm_desc": "您确定要删除所选条目吗？", "title_select_file": "选择文件", "title_device_unplugged": "{{ device }} 已拔掉插头", "desc_device_unplugged": "请检查 {{ device }} 是否已插入。", "redirect_to_ip": "重定向至 {{ ipAddress }}", "page_redirect_content": "页面将在5秒后重定向。", "redirect_failed_hint": "如果重定向失败，请检查您的网络设置。", "after_seconds": "{{ second }} 秒后", "redirecting": "正在重定向……", "title_choose_tracking_id": "选择Tracking ID"}, "common": {"network": "网络", "enable_header": "启用", "enabled": "Enabled", "disable_header": "禁用", "disabled": "Disabled", "none": "None", "authentication": "验证", "active": "Active", "inactive": "非活动", "passive": "Passive", "ip": "IP", "ip_address": "IP地址", "mac": "MAC", "mac_address": "MAC地址", "server_address": "服务器IP地址", "subnet_mask": "子网掩码", "domain_name": "域名", "ip_or_domain": "IP地址/域名", "general": "基本", "normal": "正常", "type": "类型", "mode": "模式", "yes": "是", "no": "否", "auto": "Auto", "user_defined": "User Defined", "valid": "有效的", "invalid": "无效的", "required": "必需的", "version": "版本", "unknown": "未知", "read_only": "Read Only", "refresh": "刷新", "reset": "重置", "add": "添加", "delete": "删除", "export": "导出", "up": "向上", "down": "向下", "index": "索引", "name": "名称", "method": "方法", "file_name": "文件名", "file_server": "文件服务器", "select_file": "选择文件", "action": "行动", "authority": "权限", "any": "Any", "all": "全部", "unselect": "Unselect", "settings": "设置", "status": "状态", "local": "Local", "usb": "USB", "usb_hint": "USB端口与ABC-02自动备份配置器兼容。", "usb_hw_disabled_hint": "由于外部存储功能被禁用，USB无效。", "micro_sd": "microSD", "micro_sd_hw_disabled_hint": "由于外部存储功能被禁用，microSD 无效。", "location": "位置", "time": "时间", "start_date": "开始日期", "start_time": "开始时间", "end_time": "结束时间", "timeout": "暂停", "interface": "接口", "threshold": "临界点", "broadcast": "广播", "multicast": "组播", "algorithm": "算法", "manual": "Manual", "master": "Master", "priority": "优先级", "permanent": "永久的", "queue": "队列", "netmask": "掩码", "backup": "备份", "backup_en": "Backup", "restroe": "恢复", "broken": "断线", "learning": "学习", "listening": "倾听", "discarding": "丢弃", "forwarding": "传送", "blocking": "封锁", "packets": "数据包", "notice": "Notice", "warning": "Warning", "critical": "Critical", "error": "Error", "security": "安全", "slave": "从", "slot": "槽", "simple": "Simple", "state": "状态", "subtype": "子类型", "protocol": "协议", "init": "发起", "retry": "重试", "severity": "严重性", "destination": "终点", "description": "描述", "distance": "距离", "expired_date": "截止日期", "delay_time": "延迟时间", "serial_number": "序列号", "product_revision": "产品修订", "quick_setting": "快速设置", "admin_status": "管理员状态", "link_status": "连接状态", "system_status": "系统状况", "link_up": "连接", "link_down": "连接中断", "point_to_point": "Point-to-point", "ethertype": "以太类型", "auth_type": "验证类型", "authentication_type": "身份验证类型", "default_gateway": "默认网关", "encryprion_key": "加密密钥", "encryption_method": "加密方法", "authentication_password": "验证密码", "server_ip_address": "服务器IP地址", "key": "密钥", "key_id": "密钥 ID", "vlan_id": "VLAN ID", "vlan_vid": "VLAN {{ vid }}", "vlan_list_info": "可以设置多个 VLAN，并且应输入单个数字或范围，例如 2、4-8、10-13。", "share_key": "共享密钥", "share_key_hint": "离开本页面或刷新后，共享密钥将自动清除，以增强安全性。", "auto_backup": "自动备份", "auto_backup_hint": "配置更改时备份到外部存储。", "dynamic": "动态", "static": "静态", "ref_manual_hint": "有关此设置的信息，请参阅用户手册。", "default": "<PERSON><PERSON><PERSON>", "other": "Other", "trusted": "Trusted", "untrusted": "Untrusted", "infinite": "无穷", "source": "资源", "low": "低", "high": "高", "connected": "连接", "disconnected": "断开连接", "incorrect_connected": "连接不正确", "set_event_notifications": "设置事件通知", "include": "包括", "exclude": "排除", "immediate": "Immediate", "never": "Never", "interface_name": "接口名称", "interface_alias": "接口别名", "hello_interval": "问候间隔", "neighbor": "邻居", "current": "当前", "ping": "<PERSON>", "logical": "逻辑", "interval": "间隔", "route": "路由", "open": "Open", "short": "Short", "role": "角色", "speed": "速度", "failed": "失败", "successful": "成功", "idle": "闲置", "protection": "保护", "pending": "待处理", "supported": "支持", "not_supported": "不支持", "import": "导入", "propagate": "传播", "ascii": "ASCII", "hex": "HEX", "zone": "区域"}, "common_account": {"account": "帐户", "username": "用户名", "pwd_mask": "********", "password": "密码", "confirm_password": "确认密码", "email": "Email", "delete_account_desc": "您确定要删除所选帐户吗？"}, "common_abbrev": {"md5": "MD5", "sha": "SHA", "sha-224": "SHA-224", "sha-256": "SHA-256", "sha-384": "SHA-384", "sha-512": "SHA-512", "sftp": "SFTP", "tftp": "TFTP", "pap": "PAP", "chap": "CHAP", "cos": "CoS", "dscp": "DSCP", "cir": "CIR", "cir_full": "承诺信息速率", "cbs": "CBS", "cbs_full": "承诺突发大小"}, "common_port": {"port": "端口", "ports": "端口", "port_name": "端口 {{ portName }}", "tcp_port": "TCP 端口", "udp_port": "UDP 端口", "port_state": "端口状态", "port_status": "端口状态", "port_settings": "端口设置", "port_shutdown": "Port Shutdown", "destination_port": "目的端口", "reflect_port": "反射端口", "all_port": "All Ports", "member_port": "成员端口", "also_apply_port": "将配置复制到端口", "also_apply_port_hint": "将配置复制到您从下拉框中选择的端口。", "ingress_port": "入流端口"}, "button": {"cancel": "取消", "apply": "应用", "create": "创建", "edit": "编辑", "delete": "删除", "sync_from_browser": "从浏览器同步", "regenerate": "重建", "import": "导入", "export": "导出", "expand": "扩展", "collapse": "折叠", "copy": "复制", "close": "关闭", "download": "下载", "ping": "PING", "clear": "清除", "backup": "备份", "restore": "恢复", "upgrade": "升级", "reset": "重置", "locate": "定位", "disable": "禁用", "enable": "开启", "change": "改变", "logout": "登出", "reboot": "重启", "confirm": "确认", "remove": "消除", "back": "后退", "log_in": "登录", "re_auth": "重新授权", "recovery": "恢复", "export_cid_file": "导出CID文件", "export_server_ca": "导出服务器 CA", "change_password": "更改密码", "export_server_cert": "导出服务器证书", "view_all_event_logs": "查看所有事件日志", "update_daylight_saving": "更新夏令时", "select": "选择", "retry": "重试", "encrypt": "加密", "change_cak": "更改 CKN 和 CAK", "save": "保存", "find_rp": "查找 RP", "cut_off": "切断", "go_to": "转到", "got_it": "收到"}, "tooltip": {"edit": "编辑", "delete": "删除", "copy": "复制", "copy_config": "复制配置", "close": "关闭", "clear": "清除", "clear_graph": "清晰的图表", "remove": "消除", "reorder": "重新排序", "recovery": "恢复", "select_tracking": "选择 Tracking ID", "remove_tracking": "删除 Tracking ID", "vlan_size_limitation": "该设备只允许 {{ size }} VLAN。", "size_limitation": "该设备的最大用户帐户数为 {{ size }}.", "reorder_priority": "重新排序优先级", "reorder_finish": "重新排序完成", "export_pdf_all_acl": "将所有 ACL 导出为 PDF", "export_pdf_only_this_port": "仅导出该端口的信息为PDF", "export_pdf_only_this_vlan": "仅导出该VLAN的信息为PDF", "clear_counter": "清除计数器", "unicast_mac_address_hint": "仅允许单播 MAC 地址。", "disable_la_member_hint": "这是链路聚合端口通道的成员端口。", "duplicate_redundant_port": "无法复制冗余端口", "auto_refresh_enabled": "自动刷新：  已启用", "auto_refresh_disabled": "自动刷新：  已禁用", "manually_refersh_disabled": "如果激活‘自动刷新’，则无法手动刷新", "integration_3A_3B_hint": "此端口是 3/A 和 3/B 的集成。", "set_to_static": "设置为静态"}, "unit": {"fps": "fps", "min": "min.", "sec": "sec.", "ms": "ms", "byte": "byte", "m": "M", "kbyte": "Kbyte", "mbps": "Mbps", "w": "W", "watt": "W", "watts": "W", "percent": "％", "day": "天", "times": "次", "mb": "MB", "ns": "ns", "microsecond": "μs", "ppm": "PPM", "packets": "数据包", "v": "V", "dbm": "dBm", "c": "°C", "f": "°F", "nm": "nm"}, "speed": {"10m": "10M", "100m": "100M", "1g": "1G", "10g": "10G", "40g": "40G", "56g": "56G", "2500m": "2500M", "full": "全", "half": "半"}, "table_function": {"filter_desc": "搜索", "export_pdf": "导出 PDF", "export_csv": "导出 CSV", "selected_count": "选定", "row_count": "全部的", "limit_count": "最大"}, "led": {"status_unknown_hint": "状态未知。", "state_green_on": "正常运行", "state_green_blink4hz": "系统正在启动。", "state_red_on": "系统初始化失败。", "state_off": "系统已关闭。", "fault_red_on": "系统出现故障，请检查系统日志以了解详细信息。", "fault_red_blink4Hz": "此交换机已启动，固件已加载到内存中。", "fault_off": "正常运行。", "mh_turbo_chain_head_green_on": "此交换机是Turbo Chain的Head。", "mh_turbo_ring_green_on": "此交换机是Turbo Ring 1或Turbo Ring 2的Master。", "mh_mrp_green_on": "此交换机是MRP冗余的Manager。", "mh_turbo_ring_green_blink2hz_mds": "此交换机是Turbo Ring 1或Turbo Ring 2的Master并且至少有一个环网中断。", "mh_turbo_chain_head_green_blink2hz_mds": "此交换机是Turbo Chain的Head且链路中断。", "mh_mrp_green_blink2hz_mds": "此交换机是MRP冗余的Manager且MRP环开放。", "mh_turbo_chain_member_green_blink2hz_mds": "此交换机是Turbo Chain成员且对应的成员端口1链路断开。", "mh_turbo_chain_tail_green_blink2hz_mds": "此交换机是Turbo Chain的Tail且对应成员端口链路断开。", "mh_turbo_ring_green_blink4hz": "此交换机是Turbo Ring 1或Turbo Ring 2的Master并且至少有一个环网中断。", "mh_turbo_chain_head_green_blink4hz": "此交换机是Turbo Chain的Head且链路中断。", "mh_mrp_green_blink4hz": "此交换机是MRP冗余的Manager且MRP环开放。", "mh_turbo_chain_member_green_blink4hz": "此交换机是Turbo Chain的成员且对应的成员端口1链路断开。", "mh_turbo_chain_tail_green_blink4hz": "此交换机是Turbo Chai的Tail且对应成员连接端口链路断开。", "mh_off": "此交换机不是 Turbo Ring 1 或 Turbo Ring 2 的 Master、Turbo Chain 的 Head 或MRP冗余的Manager。", "ct_turbo_ring_green_on": "这台交换机开启coupling功能，形成备用路径。", "ct_mrp_green_on": "这台交换机开启coupling功能，形成备用路径。", "ct_turbo_chain_tail_green_on": "此交换机是Turbo Chain的Tail。", "ct_dual_homing_green_on": "交换机的 dual homing 功能已启用。", "ct_turbo_ring_dual_homing_green_on": "交换机的 coupling 和 dual homing 功能已启用。", "ct_turbo_chain_dual_homing_green_on": "交换机的 dual homing 功能已启用，交换机是 Turbo Chain 的Tail。", "ct_mrp_dual_homing_green_on": "交换机的 MRP connection 和 dual homing 功能已启用。", "ct_turbo_chain_head_green_blink2hz_mds": "此交换机是Turbo Chain的成员且对应的成员端口2链路断开。", "ct_turbo_chain_member_green_blink2hz_mds": "此交换机是Turbo Chain的Head且对应的Member端口链路断开。", "ct_turbo_chain_tail_green_blink2hz_mds": "此交换机是Turbo Chain的Tail且链路断开。", "ct_turbo_chain_head_green_blink4hz": "此交换机是Turbo Chain的Head且对应的Member端口链路断开。", "ct_turbo_chain_member_green_blink4hz": "此交换机是Turbo Chain的成员且对应的成员端口2链路断开。", "ct_turbo_chain_tail_green_blink4hz": "交换机是Turbo Chain的Tail且链路断开。", "ct_off": "交换机已禁用 Turbo Chain 的Coupling或Turbo Chain的Tail。", "sync_amber_on": "PTP功能已启用。", "sync_amber_blink4hz": "交换机已收到同步数据包。", "sync_green_on": "PTP功能已成功收敛。", "sync_off": "PTP功能已关闭。", "ms_green_on": "正常运行", "ms_green_blink2hz": "该模块正在启动。", "ms_off": "模块已停止服务。", "ms_red_on": "模块初始化失败，或者用户插入了错误的模块。", "eps_amber_on": "外部电源已准备好向PoE端口供电。", "eps_off": "PoE设备没有外部电源。", "pwr_eps_amber_on": "外部电源正在向模块的 EPS 输入供电。", "pwr_eps_off": "PoE设备没有外部电源。", "pwr_amber_on": "正在向模块的电源输入供电。", "pwr_off": "模块的电源输入未供电。", "port_poe_green_on": "该端口已连接到 IEEE 802.3at 受电设备 (PD)。", "port_poe_green_on_poebt": "该端口已连接到 IEEE 802.3bt 受电设备 (PD)。", "port_poe_amber_on": "该端口已连接到 IEEE 802.3af 受电设备 (PD)。", "port_poe_amber_on_poebt": "该端口已连接到 IEEE 802.3af/at 受电设备 (PD)。", "port_poe_amber_blink4hz": "由于功率预算不足，PoE 电源已被关闭。", "port_poe_red_on": "用电设备 (PD) 检测失败。", "port_poe_red_blink4hz": "在受电设备 (PD) 上检测到过电流或短路。", "port_poe_off": "未向受电设备 (PD) 供电。", "port_link_up_hint": "该端口处于活动状态并且正在连接 {{ operSpeed }}bps。", "port_link_down_hint": "端口处于非活动状态或链路已关闭。", "prp_green_on": "PRP 功能已启用。", "prp_off": "PRP 功能已禁用。", "hsr_green_on": "HSR 功能已启用。", "hsr_off": "HSR 功能已禁用。", "coup_green_on": "Coupling 功能已启用。", "coup_off": "Coupling 功能已禁用。"}}, "features": {"storm_control": {"page_title": "流量风暴控制", "dlf": "DLF"}, "la": {"page_title": "链路聚合", "port_channel": "端口通道（Trunk）", "wait_time": "等待时间", "configure_member": "配置成员", "active_member": "活跃成员", "la_group_status": "链路聚合组状态", "lacp": "LACP", "smac": "SMAC", "dmac": "DMAC", "smac_dmac": "SMAC+DMAC", "config_member_port": "配置成员端口", "config_member_port_hint": "至少保留一个不能添加到端口通道的端口。", "delete_port_channel_confirm_desc_1": "警告：", "delete_port_channel_confirm_desc_2": "与所选链路聚合相关的某些功能（例如 RSTP 和 VLAN）将设置为默认值。", "delete_port_channel_confirm_desc_3": "您确定要删除所选的链路聚合吗？", "la_size_limitation": "该设备只允许 {{ size }} 組端口通道。", "create_la_msg": "创建链路聚合", "edit_la_pre_msg": "编辑端口通道 {{ portChannel }} 设置", "delete_la_msg": "删除链路聚合", "only_select_eight": "最多可以选择8个。"}, "scheduler": {"page_title": "调度员", "strict_priority": "Strict Priority", "weight_round_robin": "Weighted Round Robin", "sp": "SP", "wrr": "WRR", "wfq": "WFQ"}, "egress_shaper": {"page_title": "出流整形器", "egress_rate": "出口率 (CIR)"}, "rate_limit": {"page_title": "入流速率限制", "ingress_rate": "进入率（CIR）", "ebs": "EBS", "ebs_full": "超额突发大小", "conform_action": "符合行动", "exceed_action": "超越行动", "violate_action": "违规行为", "blind": "色盲", "aware": "色彩意识", "do_nothing": "Do Nothing", "drop": "Drop", "remark_cos": "Remark CoS", "remark_dscp": "Remark DSCP", "simple_token_bucket": "Simple Token Bucket", "sr_tcm": "SrTCM", "remark_value": "备注值", "release_interval": "释放间隔", "rate_limit_port_shutdown": "端口关闭速率限制"}, "classification": {"page_title": "分类", "cos_priority": "CoS 优先级", "preference_type": "信任类型", "dhcp_mapping": "DSCP 映射", "cos_mapping": "CoS 映射", "untag_default_priority": "取消标记默认优先级", "edit_dscp_msg": "编辑 DSCP {{ dscpIndex }} 设置", "edit_cos_msg": "编辑 CoS {{ cosIndex }} 设置"}, "linkup_delay": {"page_title": "连线延迟", "remaining_time": "剩余时间"}, "port_mirror": {"page_title": "端口镜像", "span": "SPAN", "rspan": "RSPAN", "session_id": "会话ID", "reflect_port_mode": "反映端口模式", "rspan_type": "RSPAN 类型", "rspan_vid": "RSPAN VLAN ID", "rspan_settings": "RSPAN 中间设置", "rspan_setting_hint": "所有 VLAN Trunk端口都将添加到 RSPAN VLAN 中。", "rspan_vlan_setting_hint": "为防止标记帧被丢弃，请仅选择设置为 Trunk 或 Hybrid 模式的 VLAN 端口作为反射端口。", "duplicate_intermediate_vlan": "中间 VLAN ID 已被使用。", "rspan_role": "RSPAN 中间角色", "rspan_intermediate_vid1": "RSPAN 中间第 1 个 VLAN ID", "rspan_intermediate_vid2": "RSPAN 中间第 2 个 VLAN ID", "enable_rspan_title": "启用 RSPAN 中间角色", "enable_rspan_warning": "此设置将删除所有现有的 RSPAN 会话。您确定要继续吗？", "rx_source_port": "接收源端口", "tx_source_port": "发送源端口", "designated_port": "指定端口", "destination_ports": "目的端口", "destination_port_info": "对于接入端口，端口的 PVID 将设置为 RSPAN VLAN ID。\n对于混合或中继端口，端口将成为 RSPAN VLAN 的成员。", "destination_port_hint": "目标端口将添加到 RSPAN VLAN。", "destination_ports_or_designated_port": "目的連接埠或指定連接埠", "source_port_two_field_invalid": "需要选择 Tx 或 Rx 源端口", "create_mirror_msg": "创建会话", "edit_mirror_msg": "编辑会话 {{ sessionIndex }} 设置", "select_tx_or_rx_hint": "需要选择 TX 或 RX 源端口。", "is_not_access_port": "此端口不是Access端口。", "is_not_trunk_port": "此端口不是Trunk端口。", "source_port_must_be_access_port": "启用反射端口模式时，源端口必须是访问端口。", "reflect_port_must_be_access_port": "启用反射端口模式时，反射端口必须是访问端口。", "reflect_port_must_be_trunk_hybrid_port": "启用反射端口模式时，反射端口必须是中继/混合端口。", "pvid_is_not_rspan_vid": "该端口的PVID不是RSPAN VLAN。", "rspan_source_session_exist": "RSPAN 源会话已存在。", "rspan_destination_session_exist": "RSPAN 目标会话已存在。", "rspan_cannot_create": "启用 RSPAN 中间角色后，无法创建 RSPAN 会话。", "session_span_size_limitation": "SPAN 条目的最大数量为 {{ size }}.", "session_rspan_size_limitation": "RSPAN 条目的最大数量为 {{ size }}.", "delete_session_title": "删除会话", "delete_session_content": "您确定要删除所选会话吗？", "rspan_vid_hint_l2": "不建议对 RSPAN 使用管理 VLAN 或 VLAN 分配配置的 VLAN。", "rspan_vid_hint_l3": "不建议将 VLAN 接口或 VLAN 分配配置的 VLAN 用于 RSPAN。"}, "vlan": {"page_title": "VLAN", "vlan": "VLAN", "global": "全域", "management_vlan": "管理VLAN", "management_port": "管理端口", "mgmt_vlan_settings": "管理端口快速设置", "management_vlan_port_setting_hint": "请选择您的计算机连接的端口并确保设置正确，以免与交换机断开连接。", "port_mode_table_title": "VLAN 交换机端口模式表", "egress_tagged_table_title": "VLAN成员表", "gvrp": "GVRP", "vlan_unaware": "VLAN Unaware", "vlan_unaware_gvrp_error": "GVRP cannot be enabled while VLAN Unaware is active.", "vlan_unaware_active_disable_hint": "VLAN cannot be modified while VLAN Unaware is active.", "all_member_vlan": "所有成员 VLAN ID", "dynamic_gvrp": "动态 GVRP", "egress_port": "出口端口", "tagged_port": "标记端口", "untagged_port": "未标记的端口", "forbidden_port": "禁用端口", "vid_exist_warning": "VLAN已存在", "vlan_max_warning": "每次最多 10 个 VLAN", "vlan_max_hint": "最多 10 个 VLAN", "pvid": "PVID", "tagged_vlan": "标记的 VLAN", "untagged_vlan": "未标记的 VLAN", "access": "Access", "access_port": "Access端口", "trunk": "Trunk", "trunk_port": "Trunk端口", "hybrid": "Hybrid", "hybrid_port": "Hybrid端口", "vlan_assignment": "VLAN 分配", "delete_vlan_confirm_desc": "您确定要删除所选的 VLAN 吗？", "mgmt_setting_disabled_pvid": "PVID已绑定到该VLAN，因此无法删除。", "mgmt_setting_disabled_access_mode": "如果端口使用的是Access模式，则不能切换到该VLAN。", "mgmt_setting_disabled_forbidden": "此端口为禁止端口。", "mgmt_setting_disabled_egress": "该端口为成员端口。", "port_setting_disabled_tagged": "此 VLAN 是标记 VLAN。", "port_setting_disabled_untagged": "此 VLAN 是未标记的 VLAN。", "port_setting_disabled_forbidden": "此端口是该 VLAN 的禁止端口。", "port_setting_disabled_pvid_member": "该PVID无法绑定到该VLAN，因为该端口不是成员端口。", "port_setting_disabled_pvid_forbidden": "该PVID无法绑定到该VLAN，因为该端口是禁止端口。", "port_setting_error_pvid_member": "未标记或未标记 VLAN", "port_setting_error_pvid_forbidden": "这是禁止端口，无法应用设置", "vlan_setting_vid_info": "可以创建多个 VLAN，并且应输入单个数字或范围，例如 2、4-8、10-13。", "te_mstid": "TE-MSTID", "temstid_member": "TE-MSTID 会员", "temstid_info": "对于VLAN加入TE-MSTID成员，流将通过静态传送规则转发，而不是MAC学习/传送机制。", "create_vlan_msg": "创建VLAN", "delete_vlan_msg": "删除VLAN", "vlan_setting_info_title": "如何设置", "example_scenario": "示例场景", "example_scenario_info_1": "端口1：  Hybrid模式、PVID 1、TAG VLAN 3-5 和 UNTAG VLAN 1", "example_scenario_info_2": "端口2：  Trunk模式、PVID 2 和 TAG VLAN 2-5", "example_scenario_info_3": "端口3：  Access模式、PVID 1 和 UNTAG VLAN 1", "example_scenario_info_4": "交换机-A 设置：  管理 VLAN 1", "setup_flow": "设置流程", "vlan_port_mode_setup": "VLAN 端口模式设置", "port_number_setting": "端口 {{ portNumber }} 设置", "setup_list_hybrid": "- 模式选择‘Hybrid’", "setup_list_apply": "- 申请", "setup_list_trunk": "- 模式选择‘Trunk’", "setup_list_access": "- 默认模式是'Access'，无需更改任何内容。", "setup_list_pvid2": "-PVID 2", "vlan_create_member_setup": "VLAN创建/VLAN成员设置", "vlan_create_member_setup_info_part_1": "点击", "vlan_create_member_setup_info_part_2": ",添加VLAN ID {{ vlanIndex }} ,添加成员端口 {{ portIndex }}", "vlan_port_pvid_setup": "VLAN端口PVID设置", "vlan_port_pvid_setup_info": "准备好了，不需要改变。"}, "l3_interface": {"page_title": "网络接口", "loopback_size_limitation": "该设备只允许 {{ size }} 环回。", "operStatus": "运行状态", "loopback_id": "环回ID", "vlan_interface": "VLAN 接口", "loopback_interface": "环回接口", "alias": "<PERSON><PERSON>", "mtu": "MTU", "proxy_arp": "代理ARP", "vlan_id_hint": "接口 VLAN ID 应与二层 VLAN 相同，三层路由才能工作。", "delete_ip_interface_desc": "您确定要删除此条目吗？", "vlan_card_title": "VLAN 接口", "loopback_card_title": "环回接口", "delete_ip_interface": "删除接口", "add_l3_vlan_ip_interface": "创建 VLAN 接口设置", "edit_l3_vlan_ip_interface": "编辑 VLAN 接口设置", "add_l3_loopback_ip_interface": "创建环回接口设置", "edit_l3_loopback_ip_interface": "编辑环回接口设置", "from_dcp": "（来自 PROFINET DCP）"}, "stp": {"page_title": "生成树", "stp_mode": "STP模式", "compatibility": "兼容性", "stp": "STP", "rstp": "RSTP", "mstp": "MSTP", "stp_rstp": "STP/RSTP", "bridge_priority": "桥梁优先", "error_recovery_time": "错误恢复时间", "forward_delay_time": "传送延迟时间", "hello_time": "Hello时间", "max_age": "最大年龄", "edge": "边缘", "guard": "防护", "path_cost": "路径成本", "multiples_of_number": " {{ number }}的倍数", "path_cost_help_info": "如果该值设置为零，则将根据不同的端口速度自动分配路径成本值。", "bpdu_guard": "BPDU 防护", "root_guard": "根防护", "loop_guard": "循环防护", "bpdu_filter": "BPDU 过滤器", "root_information": "根信息", "bridge_id": "桥ID", "root_path_cost": "根路径成本", "bridge_information": "桥信息", "running_protocol": "运行协议", "port_role": "端口角色", "link_type": "连线类型", "shared": "Shared", "bpdu_inconsistency": "BPDU 不一致", "root_inconsistency": "根不一致", "loop_inconsistency": "循环不一致", "link_type_shared_lan": "Shared LAN", "alternate": "备用", "root": "根", "designated": "指定的", "instance": "实例", "instance_index": "实例 {{ instId }}", "all_instances": "所有实例", "instance_list": "实例列表", "instance_id": "实例ID", "mstp_size_limitation": "该设备的最大实例数是 {{ size }} 除了 CIST。", "vlan_list": "VLAN 列表", "port_table_of_cist": "CIST端口表", "port_table_of_instance": "实例端口表 {{ instId }}", "information_of_cist": "CIST信息", "information_of_instance": "实例信息 {{ instId }}", "region_name": "地区名称", "region_revision": "区域修订", "max_hops": "最大跳跃数", "instance_id_duplicate": "实例ID已创建。", "except_for_cist": "CIST 除外。", "copy_port_config": "复制端口配置​", "select_from_port_of_inst": "从端口 {{ instName }}", "select_to_inst": "到实例", "general_information": "一般信息", "regional_root_id": "区域根ID", "cist_root_id": "CIST根ID", "cist_path_cost": "CIST路径成本", "designated_root_id": "指定根ID", "other_vlans": "其他 VLAN", "create_instance": "创建实例", "edit_instance": "编辑实例 {{ instId }} 设置", "delete_instance": "删除实例", "edit_cist": "编辑 CIST 设置", "edit_instance_port": "编辑实例 {{ instId }} 端口 {{ portIndex }} 设置", "edit_cist_port": "编辑CIST端口 {{ portIndex }} 设置"}, "port_security": {"page_title": "端口安全", "port_security_mode": "端口安全模式", "port_security_mode_help_info": "更改端口安全模式将重置所有设置。", "mac_sticky": "MAC Sticky", "static_port_lock": "Static Port Lock", "address_limit": "地址限制", "secure_action": "安全行动", "current_address": "当前地址", "configured_address": "手动配置地址", "violation": "违反", "effective": "有效的", "secure_pack_drop": "Packet Drop", "total_entry": "完全信任主机", "max_address": "系统中的最大地址数", "sticky_configured": "粘性配置", "lock_configured": "配置已锁定", "sticky_dynamic": "粘性动态", "address_limit_hint": "如果地址限制值发生更改，则该端口上的所有 MAC 地址都将被删除。"}, "garp": {"page_title": "GARP", "join_time": "加入时间", "leave_time": "离开时间", "leave_all_time": "离开时间", "required_join_time_multiple": "加入时间必须是 {{ num }}", "required_leave_time_multiple": "离开时间必须是 {{ num }}", "required_leave_all_time_multiple": "离开时间必须是 {{ num }}"}, "lldp": {"page_title": "LLDP", "neighbor_status": "邻居状态", "sidenav_header": "详细信息", "port_local_intf_status": "端口本地接口", "port_id_subtype": "端口 ID 子类型", "port_id": "端口号", "port_desc": "端口描述", "dot1_tlv_info": "扩展 802.1 TLV", "dot3_tlv_info": "扩展 802.3 TLV", "port_vlan_id": "端口VLAN ID", "vlan_name": "VLAN 名称", "vlan_tx_status": "VLAN ID/名称", "aggregated_and_status": "链路聚合状态", "aggregated_port_id": "聚合端口 ID", "max_frame_size": "最大帧尺寸", "port_traffic_statistics": "端口流量统计", "total_frame_out": "总帧数", "total_entries_aged": "超时项目总数/超时条目总数", "total_frame_in": "总帧数", "total_frame_receviced_in_error": "错误接收的总帧数", "total_frame_discarded": "丢弃的总帧数", "total_tlvs_unrecognized": "无法识别的TLVS总数", "total_tlv_discarded": "丢弃的TLV总数", "management_address_table": "管理地址表", "management_address": "管理地址", "extended_eip_tlv": "扩展Ethernet/IP TLV", "vendor_id": "供应商ID", "device_type": "设备类型", "product_code": "产品代码", "major_revision": "主要修订", "minor_revision": "小修改", "interface_id": "接口ID", "lldp_version": "LLDP版本", "transmit_interval": "传输间隔", "notification_interval": "通知间隔", "reinit_delay": "重新初始化延迟", "holdtime_multiplier": "保持时间乘数", "chass_id_subtype": "<PERSON><PERSON><PERSON> ID 子类型", "tx_delay": "发送延迟", "subtype_chassis_component": "<PERSON><PERSON>s-Component", "subtype_if_alias": "<PERSON>-<PERSON><PERSON>", "subtype_port_component": "Port-Component", "subtype_mac_addr": "MAC-Address", "subtype_network_address": "Network-Address", "subtype_if_name": "If-Name", "subtype_unknown": "未知亚型", "chassis_id": "<PERSON><PERSON>s <PERSON>", "tlv": "TLV", "local_info": "本地信息", "local_timer": "本地计时器", "remote_table_statistics": "远程表统计", "statistics_last_change_time": "最后更改时间（毫秒）", "statistics_insert": "插入", "statistics_drops": "丢弃", "statistics_ageout": "老化", "tx_status": "发送状态", "rx_status": "接收状态", "nbr_port_id": "邻居端口ID", "nbr_chassis_id": "邻居Chessis ID", "tx_only": "Tx Only", "rx_only": "Rx Only", "tx_and_rx": "Tx and Rx", "basic": "基本的", "basic_transmit_tlvs": "基本传输 TLV", "8021_transmit_tlvs": "802.1 传输 TLV", "8023_transmit_tlvs": "802.3 传输 TLV", "port_component_description": "端口组件描述。", "system_name": "系统名称", "system_desc": "系统描述", "system_capability": "系统能力", "la_statistics": "链路聚合统计", "lldp_update_success": "成功更新 LLDP 全局设置。", "local_port": "本地端口", "sys_capability": "系统能力", "hold_time": "保持时间", "repeater": "中继器", "bridge": "桥", "vlan_access_point": "VLAN Access点", "telephone": "电话", "docsis_cable_device": "Docsis 电缆设备", "station_only": "仅限设备"}, "mac_address_table": {"page_title": "MAC地址表", "independent_vlan_learning": "独立VLAN学习", "mac_learning_mode": "MAC学习模式", "mac_learning_mode_help_info": "请注意，更改模式将重置相关的L2模块。", "aging_time": "老化时间", "learnt_unicast": "学习单播", "learnt_multicast": "学习组播"}, "port_setting": {"page_title": "端口设置", "admin": "管理员", "media_type": "介质类型", "10m_half": "10M Half", "10m_full": "10M Full", "100m_half": "100M Half", "100m_full": "100M Full", "speed_duplex": "速率/双工", "flow_control": "流量控制", "flow_control_hint1": "流量控制可以启用/禁用，但仅在全双工时有效。", "flow_control_hint2": "可以启用/禁用背压，但仅在半双工时有效。", "mdi_mdix": "MDI/MDIX", "mdi": "MDI", "mdix": "MDIX", "enabled_xmit": "启用传输", "enabled_rcv": "启用接收", "fiber_speed_disable": "光纤端口无法设置速率/双工。", "fiber_mdi_disable": "光纤端口无法设置MDI/MDIX。", "fiber_copy_to_other_port_disable": "光纤端口无法将配置复制到其他端口。", "port_copy_to_fiber_port_disable": "无法将配置复制到光纤端口。"}, "dashboard": {"page_title": "设备摘要", "system_info": "系统信息", "panel_status": "面板状态", "panel_view": "面板视图", "link_up_port": "已连接端口", "link_down_port": "未连接端口", "module": "模块 {{ index }} - {{ name }}", "product_model": "产品型号", "firmware_version": "固件版本", "system_uptime": "系统正常运行时间", "ip_address_v4": "IPv4 地址", "ip_address_v6": "IPv6 地址", "l3_ip_address_list": "接口IP地址列表", "redundant_protocol": "冗余协议", "power_model": "电力模型", "external_storage": "外置储存", "iec62439_3_protocol": "IEC 62439-3 协议", "event_summary": "事件概要", "event_summary_hint": "（最近 3 天）", "top_5_interface_error_packet": "前 5 个接口错误数据包", "top_5_interface_utilization": "使用率最高的 5 个接口", "critical_hint": "发生异常，系统未来存在运行异常的风险", "error_hint": "发生异常，但系统运行未受影响", "warning_hint": "该信息包含警告/提醒，但不影响功能或系统操作", "notice_hint": "该信息表明该功能工作正常，设备运行正常", "tx_error": "发送错误", "rx_error": "接收错误", "unsupported_module_warning": "注意：检测到不支持的模块。卸下不支持的模块以保持正常功能。"}, "igmp_snooping": {"page_title": "IGMP Snooping", "vlan_setting": "VLAN设置", "group_table": "组表", "forwarding_table": "传送表", "query_interval": "查询间隔", "static_router_port": "静态路由器端口", "dynamic_router_port": "动态路由器端口", "config_role": "配置角色", "active_role": "现行角色", "startup_query_interval": "启动查询间隔", "startup_query_count": "启动查询计数", "other_quer_present_interval": "其他查询存在间隔", "group_address": "群组地址", "filter_mode": "过滤模式", "source_address": "源地址", "querier": "<PERSON><PERSON>", "non_querier": "Non-Querier"}, "turbo_ring_v2": {"page_title": "Turbo Ring V2", "ring_coupling_mode": "Ring Coupling模式", "static_ring_coupling": "静态环耦合", "dynamic_ring_coupling": "动态环耦合", "ring_id": "环网ID", "master_id": "主环网ID", "ring_port": "环网端口", "coupling_mode": "Ring Coupling模式", "coupling_port": "Coupling端口", "primary_path": "主路径", "backup_path": "备份路径", "ring_setting": "环网设置", "ring_coupling_setting": "Ring Coupling设置", "coupling_setting": "耦合组 {{ id }} 设置", "static_ring_coupling_setting": "静态环耦合设置", "dynamic_ring_coupling_setting": "动态环耦合设置", "coupling_group_id": "耦合组 ID", "coupling_group_status": "耦合组状态", "group_id": "组 {{ id }}", "ring_status": "环网状态", "ring_index": "Ring Index", "total_ring_number": "总环数", "healthy": "健康", "break": "断线", "ring_coupling_status": "Ring Coupling模式状态", "static_ring_coupling_status": "静态环耦合状态", "dynamic_ring_coupling_status": "动态Ring Coupling状态", "coupling_mode_primary": "Coupling Primary Path", "coupling_mode_backup": "Coupling Backup Path", "coupling_port_status": "耦合端口状态", "primary_mac": "Primary MAC", "primary_port": "主端口", "primary_port_status": "主端口状态", "backup_mac": "备份 MAC", "backup_port": "备份端口", "backup_port_status": "备份端口状态", "ring_setting_dialog_title": "{{ portIndex }} 设置", "dip_lock_hint": "Turbo Ring V2 由于 DIP 配置已被锁定。"}, "8021x": {"page_title": "IEEE 802.1X", "auth_mode": "身份验证模式", "local_database": "Local Database", "re_auth": "重新验证", "port_control": "端口控制", "auth_session_type": "身份验证会话类型", "max_request": "最大请求", "quiet_period": "安静时期", "reauthentication": "重新验证", "reauth_period": "重新验证期", "server_timeout": "服务器超时", "supp_timeout": "支持超时", "tx_period": "发送周期", "auth_port": "验证端口", "retransmit": "重传", "force_unauthorized": "Force Unauthorized", "force_Authorized": "Force Authorized", "reauth_port_confirm": "您确定要重新验证端口 {{ port }}吗？", "authorized": "授权", "unauthorized": "取消授权", "title_reauth_port": "重新验证端口", "port_setting": "端口 {{ portIndex }} 设置", "account_setting": "帐户 {{ userName }} 设置", "timeout_retransmit_hint": "所有重试次数不能超过 Dot1x 服务器超时值。注意：  所有重试次数=超时*（重传+1）。建议值：  {{ number }}", "session_status": "会话状态", "auth_table_of_port_based": "基于端口的身份验证表", "auth_table_of_mac_based": "基于 MAC 的身份验证表"}, "dual_homing": {"page_title": "Dual Homing", "multi_dual_homing": "Multiple Dual Homing", "dual_homing_table_settings": "Dual Homing 表设置", "primary_port": "主端口", "primary_link_status": "主链路状态", "primary_port_status": "主端口状态", "secondary_port": "备援端口", "secondary_link_status": "备援链路状态", "secondary_port_status": "备援端口状态", "secondary_port_hint": "备援端口不能与主端口相同。", "path_switching_mode": "路径切换模式", "primary_path_always_first": "Primary path always first", "maintain_current_path": "Maintain current path", "maintain_current_path_hint": "保持当前路径直到断开", "primary_path_sensing_recovery": "Primary path sensing recovery", "path_switching_mode_hint": "建议启用", "path_switching_mode_hint_2": "端口的 Linkup Delay 功能", "path": "路径", "linkup_delay_warning_title": "Linkup Delay 已禁用"}, "poe": {"page_title": "PoE", "power_output": "功率输出", "poe_supported": "支持 PoE", "scheduling": "调度", "pd_failure_check": "PD故障检查", "auto_power_cutting": "自动断电", "auto_power_cutting_hint": "如果功耗超出系统的功率预算，自动断电功能将删除最低优先级和最小索引端口功率输出。", "system_power_budget": "系统功率预算", "system_power_budget_hint": "系统功率预算取决于外部电源 (EPS) 的电源能力。", "actual_power_budget": "实际功率预算", "actual_power_budget_hint": "\"实际功率预算\"和\"系统功率预算\"之间的较低值将成为\"功率预算限制\"。", "output_mode": "输出模式", "high_power": "大功率", "force": "强制", "power_allocation": "电量分配", "legacy_pd_detection": "传统 PD 检测", "critical": "重要", "low": "低", "high": "高", "rule": "规则", "also_apply_port": "将规则应用于端口", "device_ip": "设备IP", "check_frequency": "检查频率", "no_response_times": "无响应时间", "no_action": "无行动", "restart_pd": "重新启动PD", "shutdown_pd": "关闭PD", "system_time_status": "系统时间状态", "system_time": "系统时间", "local_timeZone": "本地时区", "daylight_saving_time": "夏令时", "off": "关闭", "on": "打开", "rule_name": "规则名称", "schedule_time": "安排时间", "repeat_execution": "重复执行", "daily": "日常的", "weekly": "每周", "weekdays": "工作日", "weekend": "周末", "sun": "星期日", "mon": "星期一", "tue": "星期二", "wed": "星期三", "thu": "星期四", "fri": "星期五", "sat": "星期六", "sunday": "星期日", "monday": "星期一", "tuesday": "星期二", "wednesday": "星期三", "thursday": "星期四", "friday": "星期五", "saturday": "星期六", "maximum_input_power": "最大输入功率", "power_budget_limit": "功率预算限制", "power_management_mode": "电源管理模式", "allocated_power": "Allocated Power", "consumed_power": "Consumed Power", "remaining_available_power": "剩余可用电量", "classification": "分类", "over_current": "过流", "current_ma": "电流（毫安）", "voltage_v": "电压（V）", "consumption_w": "耗电量（W）", "device_type": "设备类型", "not_present": "不存在", "legacy": "旧版PD", "dot3af": "802.3 af", "dot3at": "802.3 at", "dot3bt": "802.3 bf", "dot3btss": "802.3 bt SS", "dot3btds": "802.3 bt DS", "na": "不适用", "configuration_suggestion": "配置建议", "no_suggestion": "没有建议", "enable_poe": "启用 PoE 电源输出", "disable_poe": "禁用 PoE 电源输出", "select_auto": "选择输出模式\"自动\"", "select_high_power": "选择输出模式\"高功率\"", "select_force": "选择输出模式\"强制\"", "enable_legacy": "启用旧版 PD 检测", "raise_eps_voltage": "提高外部电源电压，使其大于 46 VDC", "pd_failure_check_status": "PD故障检查状态", "alive": "激活", "not_alive": "未激活", "schedule_size_limitation": "该设备只允许 {{ size }} 时间表。", "title_create_rule": "创建规则", "title_edit_rule": "编辑规则", "allocated_power_hint": "计算所有端口的功率预算，并确保分配的总功率低于功率预算限制。", "consumed_power_hint": "计算所有端口的实时功耗。", "change_power_mode_dialog_title": "设置电源管理模式", "select_allocated_power_mode": "您确定要选择\"分配功率\"模式吗？如果是这样，\"自动断电\"将被禁用。", "select_consumed_power_mode": "您确定要选择\"消耗电量\"模式吗？如果是这样，\"自动断电\"将启用。", "change_power_cutting_dialog_title": "设置自动断电", "disable_auto_power_cutting": "您确定要禁用\"自动断电\"吗？如果是这样，\"电源管理模式\"将变为\"分配电源\"模式。", "enable_auto_power_cutting": "您确定要启用\"自动断电\"功能吗？如果是这样，\"电源管理模式\"将变成\"消耗电源\"模式。", "avaliable_power_hint": "\"剩余可用功率\"是\"最大输入功率\"减去\"{{ power }}\"。"}, "turbo_chain": {"page_title": "Turbo Chain", "chain_role": "链路角色", "head": "Head", "member": "Member", "tail": "Tail", "head_port": "Head端口", "tail_port": "Tail端口", "member_port_number": "成员端口 {{ portIndex }}", "chain_information": "链路信息", "head_port_status": "Head端口状态", "member_port_status": "成员端口状态", "tail_port_status": "Tail端口状态", "member_number_port_status": "成员 {{ number }} 端口状态", "initiated": "发起"}, "mrp": {"menu_title": "MRP", "page_title": "介质冗余协议(MRP)", "mrp_role": "角色", "ring_manager": "Ring Manager", "ring_client": "Ring Client", "domain_uuid": "域UUID", "domain_id": "域ID", "react_on_link_change": "对连线更改做出反应", "ring_port": "环网端口 {{ portIndex }}", "ring_status": "环网状态", "mrp_ring": "MRP环网", "ring_state": "环网状态", "vid_hint": "VLAN ID 必须与冗余端口的设置一致。", "react_on_link_change_hint": "此功能仅在 MRP Ring Manager 交换机上可用。一旦启用，环管理器将立即对链路变化做出反应，并且MRP拓扑将更快地收敛。", "initiation": "引发", "awaiting_connection": "等待连接", "primary_ring_port_link_up": "主环连接口已连接", "ring_open": "环开路", "ring_closed": "环闭合", "data_exchange_idle": "数据交换空闲", "pass_through": "通过", "data_exchange": "数据交换", "pass_through_idle": "通过空闲", "port_pvid_warning": "VLAN ID 必须与冗余端口的设置一致", "port_mode_warning": "除非设置为 VLAN 中继模式或 VLAN 混合模式，否则 MRP 环端口将不起作用。", "interconnection_port_mode_warning": "除非设置为 VLAN 中继模式或 VLAN 混合模式,否则 MRP 互连端口将不起作用。", "interconnection": "互连", "interconnection_role": "互连角色", "interconnection_manager": "Interconnection Manager", "interconnection_client": "Interconnection Client", "interconnection_mode": "互联模式", "lc_mode": "LC-Mode", "rc_mode": "RC-Mode", "interconnection_id": "互连ID", "interconnection_port": "互连端口", "interconnection_status": "互连状态", "interconnection_state": "互连状态", "interconnection_open": "互联开放", "interconnection_closed": "互连已关闭", "interconnection_port_idle": "互连端口空闲"}, "unicast_table": {"page_title": "单播表", "static_unicast": "静态单播", "edit_static_unicast_entry": "编辑此静态单播条目", "add_static_unicast_entry": "添加静态单播条目", "size_limitation": "该设备的静态单播条目的最大数量为 {{ size }}"}, "static_forwarding_table": {"page_title": "静态传送表", "menu_title": "静态传送", "size_limitation": "该设备的静态单播条目的最大数量为 {{ size }}.", "title_add_static_forwarding": "创建静态传送条目", "title_edit_static_forwarding": "编辑此静态传送条目", "title_delete_static_forwarding": "删除该静态传送条目"}, "multicast_table": {"page_title": "静态组播表", "static_multicast": "静态组播", "delete_on_reset": "重置时删除", "delete_on_timeout": "超时删除", "add_static_multicast_entry": "添加静态组播条目", "edit_static_multicast_entry": "编辑此静态组播条目", "size_limitation": "该设备的静态组播条目的最大数量为 {{ size }}"}, "gmrp": {"page_title": "GMRP", "group_restrict": "群组限制"}, "time_sync": {"page_title": "时间同步", "page_title_abbr": "时间同步。", "mds_m2_insert_warning": "要使用时间同步，必须在 M2 插槽中插入兼容的模块。", "profile": "规范", "8021as": "IEEE 802.1AS-2011 规范", "8021as_abbr": "IEEE 802.1AS-2011", "1588default": "IEEE 1588 Default-2008 配置规范", "1588default_abbr": "IEEE 1588 Default-2008", "iec61850": "IEC 61850-9-3-2016 规范", "iec61850_abbr": "IEC 61850-9-3-2016", "c37238": "IEEE C37.238-2017 规范", "c37238_abbr": "IEEE C37.238-2017", "priority_number": "优先级 {{ number }}", "clock_type": "时钟类型", "clock_type_bc": "Boundary Clock", "clock_type_tc": "Transparent Clock", "delay_mechanism": "延迟机制", "e2e": "End-to-End", "p2p": "Peer-to-Peer", "transport_mode": "传输模式", "8023ehternet": "IEEE 802.3 Ethernet", "udp_ipv4": "UDP IPv4", "udp_ipv6": "UDP IPv6", "domain_number": "域名号", "clock_mode": "时钟模式", "two_step": "Two-step", "one_step": "One-step", "accuracy_alert": "准确性警报", "bmca": "BMCA", "bmca_hint": "最佳主时钟算法 (BMCA) 可防止使用透明时钟时出现循环路径循环。我们建议启用此功能。", "max_steps_removed": "删除的最大步数", "grandmaster_id": "主时钟ID", "announce_interval": "通知间隔", "announce_receipt_timeout": "通知接收超时", "sync_interval": "同步间隔", "sync_receipt_timeout": "同步接收超时", "delay_req_interval": "延迟请求间隔", "pdelay_req_interval": "Pdelay-请求间隔", "neighbor_rate_ratio": "邻区比率", "neighbor_prop_delay": "邻居传播延迟", "path_delay": "路径延迟", "neighbor_prop_delay_thresh": "邻居传播延迟阈值", "synchronization_status": "同步状态", "transport_type": "运输类型", "current_data_set": "当前数据集", "parent_data_set": "父数据集", "locked": "锁定", "unlocked": "解锁", "freerun": "自由运行模式", "syncing": "同步", "browser_time": "浏览器时间", "ptp_clock_time": "PTP 时钟时间（TAI）", "ptp_slave_port": "PTP从端口", "offset_from_master": "与主时钟的偏移", "mean_path_delay": "平均路径延迟", "steps_removed": "步骤已删除", "parent_identity": "父时钟辨识", "grandmaster_identity": "主时钟辨识", "cumulative_rate_ratio": "累计比率", "grandmaster_priority_number": "主时钟优先级 {{ number }}", "grandmaster_clock_class": "主时钟等级", "grandmaster_clock_accuracy": "主时钟精准度", "8021as_capable": "支持 802.1AS", "initializing": "正在初始化...", "res_ptp_initializing_done": "PTP服务已准备就绪。", "faulty": "有故障", "pre_master": "预备主时钟", "uncalibrated": "未校准", "sync_transmit": "同步传输", "sync_receive": "同步接收", "edit_port_profile_setting": "编辑端口 {{ portIndex }} IEEE 1588v2 默认配置文件设置"}, "stream_adapter": {"page_title": "优先管理", "pcp": "优先级代码点（PCP）", "ingress_table_limit_hint": "每个端口最多支持10个条目。", "port_size_limitation": "已达到此端口的最大条目数 (10)。", "hexadecimal": "十六进制数字", "egress_untag": "出口未标记", "ingress": "入口", "egress": "出口", "per_stream_priority_title": "每个流优先级", "port_default_priority_title": "端口默认优先级", "per_stream_priority_hint": "如果未标记的流符合任何用户定义的规则，则将根据每个流的优先级进行处理。如果流不匹配任何规则，将根据默认端口优先级进行处理。", "add_stream_adapter_entry": "添加每个流优先级条目", "edit_stream_adapter_entry": "编辑此每流优先级条目", "edit_port_def_priority": "编辑端口默认优先级 {{ portIndex }}"}, "static_route": {"page_title": "静态路由", "size_limitation": "该设备的最大静态路由数为 {{ size }}.", "next_hop_IP": "下一跳IP", "next_hop_interface": "下一跳接口", "delete_static_route_desc": "您确定要删除所选路线吗？", "next_hop_type": "下一跳类型*", "next_hop_type_hint": "您可以选择之前创建的 VLAN 接口或将此字段留空。", "next_hop_two_field_invalid": "您必须指定下一跳的 IP 或接口", "add_static_route": "创建静态路由", "edit_static_route": "编辑此静态路由", "delete_static_route": "删除该静态路由"}, "routing_table": {"page_title": "路由表", "static": "静态", "next_hop": "下一跳", "ad_metric": "AD/公制"}, "online_accounts": {"page_title": "在线帐户", "idle_time": "空闲时间", "remove_account_dialog_title": "删除此在线帐户", "remove_account_dialog_desc": "您确定要删除此在线帐户吗？"}, "8021qbv": {"page_title": "时间感知整形器", "cycle_time": "周期", "start_time_hint": "时间感知整形器基于当前 PTP 时间。您可以确定开始时间，也可以立即设置时间，这将确定该功能何时开始。", "config_change_time": "配置更改时间", "default_setting": "默认设置", "gate_control_list": "门控清单", "totla_slot": "总槽位", "interval": "间隔", "selected_queue_summary": "选定的队列摘要", "interval_hint": "闸门间隔表示闸门何时打开以及将保持打开状态多长时间。以太网数据包的最小间隔如下：", "interval_hint_1G": "1G端口：  1μs，", "interval_hint_100M": "100M端口：  10μs，", "interval_hint_10M": "10M端口：  100μs", "port_status": "端口 {{ portIndex }} 地位", "select_port": "选择端口"}, "ospf": {"page_title": "OSPF", "ospf_settings": "OSPF 设置", "ospf_status": "OSPF 状态", "area": "区域", "neighbor": "邻居", "aggregation": "聚合", "virtual_link": "虚拟连线", "router_id": "路由器ID", "current_router_id": "当前路由器ID", "current_router_id_hint": "如果Router ID设置为0.0.0.0，则最低的接口IP地址将自动分配为Router ID。", "compatible_rfc_1583": "RFC 1583 兼容性", "spf_hold_time": "SPF 保持时间", "redistribute": "重新分配", "metric": "度量值", "ospfRRDStatic": "静止的", "ospfRRDConnected": "连接的", "ospfRRDRip": "RIP", "area_size_limitation": "该设备的最大区域数是 {{ size }}.", "area_id": "区域ID", "area_type": "区域类型", "normal": "Normal", "stub": "<PERSON><PERSON>", "nssa": "NSSA", "summary": "Summary", "no_summary": "No Summary", "delete_area_desc": "您确定要删除所选区域吗？", "dead_interval": "Dead间隔", "cost": "成本", "network_type": "网络类型", "passive_interface": "被动接口", "neighbor_ip_address": "邻居IP地址", "summary_hint": "如果区域类型设置为普通，则摘要不可用。", "broadcast": "Broadcast", "non_broadcast": "Non-broadcast", "point_to_point": "Point-to-point", "point_to_multipoint": "Point-to-multipoint", "nbr_ip_address": "邻居IP地址", "nbr_size_limitation": "该设备的最大邻居数是 {{ size }}.", "delete_nbr_desc": "您确定要删除选定的邻居吗？", "lsa_type": "LSA类型", "type_7": "Type 7", "aggregation_size_limitation": "该设备的最大聚合数量为 {{ size }}.", "delete_aggregation_desc": "您确定要删除选定的聚合吗？", "vLink_size_limitation": "该设备的最大虚拟连线数为 {{ size }}.", "delete_vLink_desc": "您确定要删除所选的虚拟连线吗？", "loopback": "环回", "waiting": "等待", "dr": "DR", "bdr": "BDR", "dr_other": "其他DR", "dr_router_id": "DR路由器ID", "bdr_router_id": "BDR 路由器 ID", "neighbor_id": "邻域 ID", "neighbor_state": "邻域状态", "dead_time": "不运行时间", "accempt": "试图", "two_way": "2种方法", "exstart": "开始", "exange": "交换", "loading": "正在加载", "full": "满的", "database": "数据库", "link_id": "连线ID", "adv_router": "ADV路由器", "age": "年龄", "events": "活动", "ls_retrans_queue_len": "LSA重传队列长度", "hello_suppressed": "Hello数据包抑制", "router": "路由器", "asbr_summary": "ASBR摘要", "as_external": "作为外部", "group_member": "团体成员", "nssa_external": "NSSA 外部", "title_edit_ospf_redistribute": "编辑重新分发 {{ protocol }}", "title_create_ospf_area": "创建一个区域", "title_edit_ospf_area": "编辑该区域", "title_delete_ospf_area": "删除该区域", "title_create_ospf_nbr": "创建一个邻居", "title_edit_ospf_nbr": "编辑这个邻居", "title_delete_ospf_nbr": "删除这个邻居", "title_create_ospf_aggregation": "创建一个聚合", "title_edit_ospf_aggregation": "编辑此聚合", "title_delete_ospf_aggregation": "删除此聚合", "title_create_ospf_vlink": "创建虚拟连线", "title_edit_ospf_vlink": "编辑此虚拟连线", "title_delete_ospf_vlink": "删除该虚拟连线"}, "vrrp": {"page_title": "VRRP", "v2": "V2", "v3": "V3", "virtual_router_enable": "虚拟路由器", "vrid": "虚拟ID", "decrement": "减少", "primary_ip": "虚拟路由器IP地址", "adv_int": "广告间隔", "preempt_mode": "抢占模式", "preempt_delay": "抢占延迟", "accept_mode": "接受模式", "auth_key": "验证密钥", "size_limitation": "该设备的 VRRP 表项最大数量为 {{ size }}.", "delete_vrrp_desc": "您确定要删除选定的虚拟路由器吗？", "master_address": "主地址", "master_adv_int": "主广告间隔（毫秒）", "master_down_int": "主控停机间隔（毫秒）", "title_add_vrrp": "创建虚拟路由器", "title_edit_vrrp": "编辑此虚拟路由器", "title_delete_vrrp": "删除该虚拟路由器", "require_decrement_less_than_priority": "该值必须小于优先级值"}, "dns": {"page_title": "DNS 设置", "primary_dns_server": "主 DNS 服务器", "secondary_dns_server": "辅助 DNS 服务器", "dns_server_number": "DNS服务器IP地址{{ number }}", "dns_server": "DNS服务器", "dns_reverse_lookup": "DNS反向查找", "zone_table": "区域表", "dns_table_for_name": "DNS 表 {{ zoneName }}", "dns_server_summary": "DNS服务器摘要", "fqdn": "完全限定域名", "fqdn_hint": "FQDN（完全限定域名）是 \"主机名\".\"域名\"", "dns_forwarding": "DNS 转发", "dns_forwarding_hint": "启用 DNS 转发需要活动的 DNS 服务器。", "default_forwarder_ip": "默认转发器 IP 地址", "default_forwarder_ip_hint": "如果指定了默认转发器，则转发器表中未列出的区域的 DNS 查询将转发到默认转发器。", "forwarder_ip": "转发器 IP 地址", "forwarders_table": "货运代理表", "zone_hint": "'.' 可用于转发任何区域。", "zone_size_limitation": "区域条目的最大数量是 {{ size }}.", "dns_size_limitation": "DNS 条目的最大数量是 {{ size }}.", "title_create_zone": "创建一个区域", "title_edit_zone": "编辑 {{ zoneName }} 设置", "title_delete_zone": "删除区域", "delete_zone_desc": "您确定要删除所选区域吗？", "title_create_dns": "创建 {{ zoneName }}资源记录", "title_edit_dns": "编辑{{ zoneName }}资源记录 ", "title_delete_dns": "删除资源记录", "delete_dns_desc": "您确定要删除选定的资源记录吗？", "title_create_forwarding": "创建 DNS 转发条目", "title_edit_forwarding": "编辑 DNS 转发条目", "title_delete_forwarding": "删除 DNS 转发条目", "delete_forwarding_desc": "您确定要删除选定的 DNS 转发条目吗？", "duplicate_hostname": "相同的主机名已存在", "duplicate_domain_name": "相同的域名已经存在", "duplicate_zone": "此区域已存在"}, "acl": {"page_title": "Access控制列表", "access_list_type": "Access列表类型", "access_list_type_hint": "对于相同的索引，MAC地址的优先级高于IP地址。", "access_list_index_hint": "较低的索引代表较高的优先级。", "ip_based": "IP-based", "mac_based": "MAC-based", "acl_size_limitation": "ACL条目的最大数量是 {{ size }}.", "acl_ip_based_size_limitation": "基于 IP 的 ACL 规则的最大数量为 {{ size }}.", "acl_mac_based_size_limitation": "基于 MAC 的 ACL 规则的最大数量为 {{ size }}.", "acl_rule_size_limitation": "ACL规则的最大数量是 {{ size }}.", "active_interface_type": "活动接口类型", "vlan_based": "VLAN-based", "port_based": "Port-based", "active_ingress_vlan": "启用入流VLAN", "active_egress_vlan": "启用出流VLAN", "active_ingress_port": "启用入流端口", "active_egress_port": "启用出流端口", "ingress_setting_hint": "活动VLAN和规则VLAN必须相同。", "egress_setting_hint": "带有重定向操作的规则不能应用于出流接口。", "ingress_setting_vlan_hint": "活动 VLAN 和规则 VLAN 必须相同。\n带有备注操作的规则不能应用于入流接口。", "egress_setting_vlan_hint": "活动 VLAN 和规则 VLAN 必须相同。\n具有重定向操作的规则不能应用于出流接口。", "rule_type": "规则类型", "index_priority_hint": "索引较低的规则具有较高的优先级。", "acl_rule": "ACL 规则", "rule": "规则", "rule_index": "规则索引 {{ruleIndex}}", "permit": "Permit", "deny": "<PERSON><PERSON>", "ethertype_value": "以太类型值", "goose": "GOOSE", "smv": "SMV", "protocol_number": "协议号", "user_defined": "User-defined", "source": "源", "source_port": "源端口", "source_ip_addr": "源IP地址", "source_ip_mask": "源IP掩码", "source_mac_addr": "源MAC地址", "source_mac_mask": "源 MAC 掩码", "destination_port": "目的端口", "destination_ip_addr": "目标IP地址", "destination_ip_mask": "目标IP掩码", "destination_mac_addr": "目标MAC地址", "destination_mac_mask": "目的MAC掩码", "cos_remark": "CoS标记", "dscp_remark": "DSCP标记", "optional_parameter": "可选参数", "log": "日志", "logging": "记录", "logging_enable": "启用日志记录", "src_port": "源端口", "dst_port": "目地端口", "icmp_type": "ICMP 类型", "icmp_code": "ICMP 代码", "igmp_type": "IGMP 类型", "redirect": "重定向", "redirect_mirror": "重定向/镜像", "redirect_enable": "重定向", "redirect_port": "重定向端口", "redirect_port_name": "重定向到端口 {{ portName }}", "mirror": "镜像", "session_id": "会话 {{id}}", "mirror_disable_hint": "如果端口关闭镜像功能，则无法进行镜像操作。", "session_disable_hint": "'镜像'操作无法对禁用的端口镜像会话生效。", "mirror_sesstion": "镜像到会话 {{ sesstionId }}", "remark_cos": "将 CoS 备注为 {{ cos }}", "remark_dscp": "将 DSCP 备注为 {{ dscp }}", "acl_table_of_name": "ACL表 {{ aclName }}", "delete_acl_list_desc": "您确定要删除选定的Access控制列表吗？", "delete_acl_rule_desc": "您确定要删除所选规则吗？", "any_hint": "如果没有输入任何值，将被视为设置为任意。", "log_interval": "记录间隔", "log_threshold": "记录阈值", "acl_summary": "ACL摘要", "number_of_activate_acl": "激活的 ACL 数量（最大 16）", "activate_direct": "方向", "ingress": "入流", "egress": "出流", "both": "两个都", "activated": "启用", "inactivated": "已停用", "hit_count": "点击数", "counter": "计数器", "view_list": "查看列表", "view_by_acl": "通过ACL查看", "view_by_port": "按端口查看", "view_by_vlan": "按VLAN查看", "acl_table_of_type": "ACL表 {{typeIndex}}", "no_activated_acl_port": "此端口上没有激活 ACL。", "no_activated_acl_vlan": "此 VLAN 上没有激活 ACL。", "status_hint": "索引较低的规则具有较高的优先级。设备将从最低索引开始按数字顺序将数据包与所有规则匹配。如果数据包匹配某个规则，则将应用相应的规则。", "title_create_access_list": "创建Access列表", "title_edit_access_list": "编辑 {{ typeIndex }} Access列表设置", "title_delete_acl_list": "删除Access列表", "title_create_acl_rule": "创建规则索引 {{ ruleIndex }} 为了 {{ typeIndex }}", "title_edit_acl_rule": "编辑规则索引 {{ ruleIndex }} 的 {{ typeIndex }}", "title_delete_acl_rule": "删除规则", "title_clear_acl_counter": "清空计数器", "desc_clear_all_acl_counter_desc": "您确定要重置所有计数器吗？", "desc_clear_single_acl_counter_desc": "您确定要重置计数器吗？ {{ typeIndex }} ACL？", "blacklist_udp_port_dhcp_server": "禁用 DHCP server", "blacklist_udp_port_dhcp_client": "禁用 DHCP client", "blacklist_udp_port_moxa_command": "禁用 Moxa 服务", "blacklist_ether_type_eth_confg_test_protocol": "禁用 以太网配置测试协议", "blacklist_ether_type_lldp": "禁用 LLDP", "blacklist_ether_type_eapol": "禁用 EAPOL", "blacklist_ether_type_lacp": "禁用 LACP", "blacklist_ether_type_llc_jumbo_frame": "禁用 LLC 巨型帧", "blacklist_ether_type_arp": "禁用 ARP", "blacklist_ether_type_mrp": "禁用 MRP", "blacklist_ether_type_profinet": "禁用 PROFINET", "blacklist_ether_type_ptp": "禁用 PTP", "blacklist_ether_type_goose": "禁用 GOOSE", "blacklist_ether_type_smv": "禁用 SMV", "blacklist_mac_ieee_reserved_multicast": "禁用 IEEE 保留组播 MAC 地址", "blacklist_mac_ip_multicast": "禁用 IP 组播 MAC 地址", "blacklist_mac_broadcast": "禁用 广播 MAC 地址", "blacklist_mac_l2_multicast": "禁用 L2 组播 MAC 地址", "blacklist_mac_device": "禁用 设备 MAC 地址", "blacklist_dest_ip_multicast": "禁用 组播 IP 地址", "overwrite_vlan_dialog_title": "用活动 VLAN 覆盖规则 VLAN", "overwrite_vlan_dialog_content": "活动 VLAN 和规则 VLAN 必须相同。您确定要让活动 VLAN 覆盖规则 VLAN 吗？"}, "stream_id": {"page_title": "流识别", "title_create_stream_id": "创建流", "title_edit_stream_id": "编辑此流", "title_delete_stream_id": "删除流", "delete_stream_id_desc": "您确定要删除选定的流吗？"}, "8021cb": {"page_title": "帧复制和可靠性消除（FRER）", "frer": "FRER", "split": "分裂", "forward": "传送", "merge": "合并", "stream_vid_mac": "流（VLAN/MAC 地址）", "input_port": "输入端口", "input_ports": "输入端口", "output_port_index": "输出端口 {{ portIndex }}", "output_port": "输出端口", "output_ports": "输出端口", "ingress_stream": "入口流", "egress_stream": "出口流", "vlan_overwrite": "VLAN 覆盖", "mac_address_overwrite": "MAC地址覆盖", "priority_overwrite": "优先覆盖", "overwrite": "覆盖", "disable_port_input_hint": "这是一个选定的输入端口。", "disable_port_vlan_hint": "该端口不是相应VLAN的成员。", "disable_vid_overwrite_hint": "输出端口不是对应VLAN的成员。", "disable_exist_stream_hint": "此流已存在 FRER 条目。", "disable_select_stream_hint": "该流已被选择。", "to_end_device": "至终端设备", "ingress_size_limitation": "最大入口流是 {{ size }}.", "title_create_frer_entry": "创建 FRER 条目", "title_edit_frer_entry": "编辑此 FRER 条目", "title_delete_frer_entry": "删除此 FRER 条目", "delete_frer_entry_desc": "您确定要删除选定的 FRER 条目吗？"}, "loop_protection": {"page_title": "网络环路保护", "detect_interval": "检测间隔", "loop_status": "环路状态", "peer_port": "对等端口", "looping": "Looping"}, "binding_database": {"page_title": "绑定数据库", "binding_settings": "绑定设置", "binding_status": "绑定状态", "binding_status_hint": "动态绑定是向 DHCP Snooping 学习的。", "binding_status_hint_2": "如果静态表项的VLAN ID和MAC地址组合已经存在，则绑定状态不会更新。", "title_create_entry": "创建绑定数据库静态条目", "title_edit_entry": "编辑此绑定数据库静态条目", "duplicate_of_dynamic_entry": "VLAN ID 和 MAC 地址组合已存在。这个新条目将覆盖初始动态条目。", "size_limitation": "绑定状态条目的最大数量为 {{ size }}.", "binding_table_max": "最大限度 {{ size }} 绑定状态表", "dai": "DAI", "ipsg": "IPSG", "ipsg_dai": "IPSG、DAI"}, "dhcp_snooping": {"page_title": "DHCP Snooping", "port_is_ip_sg_enable": "此端口已为 IP 来源保护启用。IP 来源保护只能在不受信任的端口上启用。", "port_is_dai_enable": "此端口已启用动态 ARP 检查。动态 ARP 检查只能在不受信任的端口上启用。", "port_is_ip_sg_and_dai_enable": "此端口已启用动态 ARP 检查和 IP 来源保护。动态 ARP 检查和 IP 来源保护只能在不受信任的端口上启用。"}, "ip_source_guard": {"page_title": "IP来源保护", "port_is_trusted": "此端口是 DHCP Snooping的可信端口。只能为 IP 来源保护启用不受信任的端口。", "port_is_la_member": "该端口是端口通道的成员。成员端口上无法启用IP来源保护。", "binding_status_single_empty": "端口的绑定状态 {{ port }} 是空的。", "binding_status_multiple_empty": "端口的绑定状态 {{ port }} 都是空的。", "binding_setting_hint": "您应该启用 DHCP 监听来获取动态绑定或在绑定数据库 -> 绑定设置中配置数据。"}, "mms": {"page_title": "MMS", "ied_name": "IED名称", "cid_file_settings": "CID文件设置", "report_control_block": "报告控制块", "data_change": "数据变更", "data_update": "数据更新", "quality_change": "质变", "integrity": "数据完整性", "buffer_time": "缓冲时间", "integrity_period": "数据完整性检查间隔时间", "t_profile_cert_info": "T-Profile证书信息", "a_profile_cert_info": "A-Profile证书信息", "ca_name": "CA名称", "t_profile_security": "T-Profile安全加密", "a_profile_security": "A-Profile安全加密", "import_client_ca": "导入客户端CA", "import_client_cert": "导入客户端证书", "title_edit_name": "编辑 {{ name }}", "title_mms_enable_warning": "启用MMS协议", "mms_enable_warning_desc": "您确定要启用非安全协议 (MMS) 吗？"}, "password_policy": {"page_title": "密码制定原则", "minimum_length": "最小密码长度", "policy_numbers": "必须包含至少一位数字（0-9）", "policy_uppercase": "必须包含至少一个大写字母（A-Z）", "policy_lowercase": "必须包含至少一个小写字母 (a-z)", "policy_symbols": "必须至少包含一个特殊字符({}[]()|:;~!@#%^*-_+=,.)", "max_life_time": "密码最长有效期", "password_complexity_strengh_check": "密码复杂度强度检查"}, "system_info": {"page_title": "信息设置", "system_name": "设备名称", "contact_information": "联系信息", "sync_to_chassis_id_hint": "当 LLDP 机箱 ID 子类型设置为 \"local\" 时，修改设备名称将导致同时修改 LLDP 机箱 ID。"}, "login_authentication": {"page_title": "登入验证", "authentication_protocol": "身份验证协议", "radius_local": "RADIUS, Local", "tacacs_local": "TACACS+, Local"}, "login_policy": {"page_title": "登录政策", "login_message": "登录信息", "auth_fail_message": "登录验证失败信息", "failure_lockout": "账户登录失败锁定", "retry_failure_threshold": "重试失败阈值", "lockouttime": "锁定持续时间", "auto_logout_setting": "自动登出时间", "auto_logout_warring_title": "禁用自动登出", "auto_logout_setting_alert": "如果自动登出值设置为 0，会话将永远不会超时。请在关闭浏览器之前先退出。"}, "ip_settings": {"page_title": "IP配置", "ip_settings": "IP设置", "ip_status": "IP状态", "get_ip_from": "获取IP来自", "dns_server": "DNS服务器IP地址", "ipv6": "IPv6", "ipv6_global_unicast_address_prefix": "IPv6 全球单播地址前缀", "ipv6_dns_server_number": "IPv6 DNS 服务器 {{ number }}", "ipv6_dns_server": "IPv6 DNS 服务器", "ipv6_global_unicast_address": "IPv6全球单播地址", "ipv6_link_local_address": "IPv6 链路本地地址", "profinet_dcp": "PROFINET DCP", "dhcp_bootfile": "DHCP 启动文件", "dhcp_bootfile_hint": "如果启用，系统将自动从选项 66 中描述的文件服務器下载并恢复选项 67 中描述的引导文件的配置设置。", "dhcp_client": "DHCP 客户端标识符", "dhcp_client_hint": "如果启用，系统将发送带有选项 61 标签（包括客户端 ID）的 DHCP 客户端消息。DHCP 服务器将分配与客户端 ID 值关联的 IP 地址（如果可用）。", "dhcp_client_type": "DHCP 客户端标识符类型", "dhcp_client_value": "DHCP 客户端标识符值"}, "management_interface": {"page_title": "管理接口", "user_interface": "用户接口", "interface": "接口", "enable_http": "HTTP", "http_port": "HTTP - TCP 端口", "enable_https": "HTTPS", "https_port": "HTTPS - TCP 端口", "enable_telnet": "远程登入", "telnet_port": "Telnet - TCP 端口", "ssh_port": "SSH - TCP 端口", "enable_snmp_V1V2c": "SNMP版本V1、V2c", "snmp_protocol": "SNMP - 传输层协议", "udp": "UDP", "tcp": "TCP", "snmp_udp_port": "SNMP - UDP 端口", "snmp_tcp_port": "SNMP - TCP 端口", "enable_moxa_service": "Moxa服务", "moxa_tcp_port": "Moxa 服务（加密）- TCP 端口", "moxa_udp_port": "Moxa 服务（加密）- UDP 端口", "max_session_http": "HTTP+HTTPS 的最大登录会话数", "max_session_terminal": "Telnet+SSH 的最大登录会话数", "enable_nonsecure_interface_warning_title": "启用 {{ interfaceType }} 接口", "enable_nonsecure_interface_warning": "您确定要启用非安全接口吗（{{ interfaceType }}）？"}, "hareward_interface": {"page_title": "硬件接口", "dip_switch": "DIP开关", "usb_function": "USB接口", "micro_sd_function": "MicroSD 接口"}, "account_management": {"page_title": "用户帐户", "admin": "Admin", "supervisor": "Supervisor", "user": "User", "new_password": "新密码", "title_edit_account": "编辑此帐户", "title_add_account": "创建一个新账户", "title_edit_account_password": "编辑账户密码", "new_pwd_not_match": "密码不匹配。", "tech_account_add_error": "无法创建帐户 \"moxasupport\",因为它是为 Moxa 技术支持保留的。", "tech_account_remove_error": "无法编辑或删除帐户 \"moxasupport\",因为它是为 Moxa 技术支持保留的。", "account_name_taken": "该帐户用户名已被占用", "size_limitation": "该设备的最大用户帐户数为 {{ size }}"}, "time": {"page_title": "系统时间", "sntp": "SNTP", "ntp": "NTP", "time_zone": "时区", "current_time": "当前时间", "daylight_saving": "夏令时", "end_date": "结束日期", "offset": "抵消", "ntp_authentication": "NTP验证", "query_interval": "查询间隔", "ptp": "PTP", "start": "开始", "end": "结尾", "date": "日期", "month": "月", "week": "星期", "day": "天", "hour": "小时", "minute": "分钟", "jan": "一月", "feb": "二月", "mar": "三月", "apr": "四月", "may": "五月", "jun": "六月", "jul": "七月", "aug": "八月", "sep": "九月", "oct": "十月", "nov": "十一月", "dec": "十二月", "1st": "第一", "2nd": "第二", "3rd": "第三", "4th": "第四", "last": "最后的", "sun": "星期日", "mon": "星期一", "tue": "星期二", "wed": "星期三", "thu": "星期四", "fri": "星期五", "sat": "星期六", "time_server_number": "时间服务器 {{ number }}", "time_server_1": "第一时间服务器：IP 地址/域名", "time_server_2": "第二时间服务器：IP 地址/域名", "clock_source": "时钟源", "key_string": "密钥字符串", "delete_entry_confirm_desc": "您确定要删除选定的密钥字符串吗？", "size_limitation": "该设备的 NTP 身份验证密钥的最大数量为 {{ size }}"}, "ntp_server": {"page_title": "NTP服务器"}, "ssh_ssl": {"page_title": "SSH 和 SSL", "ssh": "SSH", "ssl": "SSL", "regen_ssh_key": "重新生成 SSH 密钥", "regen_ssl_cert": "重新生成 SSL 证书", "export_ssl_cert": "导出 SSL 证书", "import_ssl_cert": "导入证明书", "ssl_info": "证书信息", "ca_name": "CA名称", "title_export_ssl_certificate": "导出 SSL 证书"}, "dhcp": {"page_title": "DHCP 服务器", "dhcp": "DHCP", "ntp_server": "NTP服务器IP地址", "dhcp_pool_settings": "DHCP 服务器池设置", "static_ip_assignment_table": "静态 IP 分配表", "start_ip": "起始IP地址", "end_ip": "结束IP地址", "lease_time": "租期", "hostname": "主机名", "log_server": "日志服务器IP地址", "gateway": "网关", "matching_rule": "匹配规则", "client_id_type": "客户端识别码类型", "client_id_value": "客户端识别码值", "circuit_id_type": "选项 82 电路 ID 类型", "circuit_id_value": "选项 82 电路 ID 值", "remote_id_type": "选项 82 远程 ID 类型", "remote_id_value": "选项 82 远程 ID 值", "hostname_hint": "主机名代表 DHCP 客户端的名称，并将被编码到 DHCP Offer 数据包的选项 12 标记中。", "time_left": "剩余时间", "dhcp_ip_mac": "基于 DHCP/MAC 的 IP 分配", "dhcp_static_ip": "DHCP/静态 IP 分配", "lease_table": "租赁表", "ip_mac_binding": "基于MAC的IP分配", "ip_port_binding": "基于端口的IP分配", "classless_static_route_table": "无类别静态路由表", "delete_dhcp_entry_confirm_desc": "您确定要删除此 DHCP 服务器池吗？", "delete_static_ip_entry_confirm_desc": "您确定要删除选定的静态 IP 条目吗？", "delete_ip_port_entry_confirm_desc": "您确定要删除选定的基于端口的 IP 分配吗？", "delete_ip_mac_entry_confirm_desc": "您确定要删除选定的基于 MAC 的 IP 分配吗？", "dhcp_size_limitation": "该设备的 DHCP 服务器池的最大数量为 {{ size }}.", "invalid_dhcp_pool_range": "无效： 交换机的管理IP地址应在IP子网范围内。", "delete_static_route_entry_confirm_desc": "您确定要删除所选路线吗？", "default_gateway_setting_hint": "无类静态路由的默认网关使用在基于端口的 IP 分配配置部分中配置的默认网关地址。"}, "dhcp_relay": {"page_title": "DHCP中继代理", "option82": "选项82", "server1": "第一个服务器IP地址", "server2": "第二个服务器IP地址", "server3": "第三个服务器IP地址", "server4": "第四个服务器IP地址", "remote_id_type": "远程ID类型", "remote_id_value": "远程ID值", "remote_id_display": "远程ID显示", "client_id": "Client ID", "relay": "中继"}, "ping": {"page_title": "<PERSON>", "ping_result": "Ping {{ targetHost }} 结果"}, "email_settings": {"page_title": "电子邮件设置", "tls_enable": "TLS", "sender_address": "发件人地址", "recipient_1": "第一收件人电子邮件地址", "recipient_2": "第二收件人电子邮件地址", "recipient_3": "第三收件人电子邮件地址", "recipient_4": "第四收件人电子邮件地址", "recipient_5": "第五收件人电子邮件地址"}, "snmp": {"page_title": "SNMP", "v1": "V1, V2c, V3", "v2c": "V1, V2c", "v3": "V3 Only", "snmp_version": "SNMP版本", "snmp_account": "SNMP帐户", "read_community": "阅读社区", "read_write_community": "读/写社区", "read_write": "Read/Write", "des": "DES", "aes": "AES", "snmp_account_size_limitation": "该设备的最大 SNMP 帐户数为 {{ size }}.", "snmp_warning_dialog_V1V2c_title": "设置 SNMP 版本", "snmp_warning_dialog_V1V2c_desc": "您确定要启用非安全接口（SNMP 版本 V1、V2c）吗？", "snmp_warning_dialog_authMD5_title": "设置身份验证类型 MD5", "snmp_warning_dialog_authMD5_desc": "MD5 身份验证仅提供有限的安全性。您确定要继续吗？", "change_password_title": "更改验证密码", "change_key_title": "更改加密密钥", "change_key_button": "更改加密密钥", "create_v3_account": "创建 SNMP 帐户", "edit_v3_account": "编辑此 SNMP 帐户"}, "snmp_trap": {"page_title": "SNMP陷阱/通知", "snmp_trap_inform_recipient": "SNMP陷阱/通知收件人", "snmp_inform_settings": "SNMP通知设置", "inform_retry": "通知重试", "inform_timeout": "通知超时", "recipient_name": "收件人IP地址/域名", "trap_community": "陷阱社区", "snmp_trap_inform_account": "SNMP 陷阱/通知帐户", "trap_v1": "Trap V1", "trap_v2c": "Trap V2c", "inform_v2c": "Inform V2c", "trap_v3": "Trap V3", "inform_v3": "Inform V3", "title_delete_host_dialog_title": "删除该主机", "title_delete_host_dialog_desc": "您确定要删除该主机吗？", "snmp_trap_account_size_limitation": "该设备的 SNMP 陷阱/通知帐户的最大数量为 {{ size }}.", "snmp_host_size_limitation": "该设备的 SNMP 陷阱主机的最大数量是 {{ size }}.", "create_v3_trap_account": "创建 SNMP 陷阱帐户", "edit_v3_trap_account": "编辑此 SNMP 陷阱帐户", "create_host_table": "创建主机", "edit_host_table": "编辑该主机"}, "arp": {"page_title": "ARP表", "title_clear_message": "清除所有 ARP 条目", "clear_confirmation_message": "您确定要清除所有 ARP 条目吗？"}, "event_log": {"page_title": "事件日志", "boot": "启动号", "progname": "程序名称", "timestamp": "时间戳", "uptime": "正常运行时间", "message": "信息", "severity_emerg": "Emergency", "severity_alert": "<PERSON><PERSON>", "severity_info": "Info", "severity_debug": "Debug", "flush_log_entry_confirmation_message": "您确定要清除所有日志条目吗？", "total_entries": "总条目：", "clear_all_logs": "清除所有日志", "capacity_warning": "容量警告", "capacity_warning_hint": "可以在事件通知页面上为各个事件配置注册操作。", "warning_threshold": "警告阈值", "oversize_action": "事件超量设定", "oversize_action_overwrite": "Overwrite the oldest event log", "oversize_action_stop": "Stop recording event logs", "title_clear_all_logs": "清除所有日志", "debug_hint": "{{ number }} 日志供内部使用。", "hash_value": "哈希值", "auto_backup_of_event_log": "自动事件日志备份"}, "trust_access": {"page_title": "可信Access", "size_limitation": "该设备的最大可信Access条目数为 {{ size }}.", "delete_all_warning_title": "无法删除所有可信Access条目", "delete_all_warning_1": "如果启用，可信Access需要至少一个活动条目。强烈建议保留", "delete_all_warning_2": "您当前的IP地址", "delete_all_warning_3": "作为活动条目。"}, "utilization": {"page_title": "资源利用率", "cpu_utilization": "CPU使用率", "cpu_historical_record": "CPU 使用历史记录", "mem_utilization": "记忆体使用情况", "mem_historical_record": "记忆体使用历史记录", "power_utilization": "电量消耗", "power_historical_record": "电量消耗历史", "last_update_time": "最近更新时间 ", "used": "用过的", "free": "自由的", "past_10_second": "过去 10 秒", "past_30_second": "过去 30 秒", "past_300_second": "过去 300 秒", "selecting_visible_polyline": "编辑可见折线", "polyline_display_hint": "单击小部件右上角的图标以选择要显示的数据。"}, "tacacs_server": {"page_title": "TACACS+服务器", "tacacs": "TACACS+", "auth_type_asc_two": "ASCII"}, "syslog_server": {"page_title": "系统日志", "syslog_server": "系统日志服务器", "auth_disable_hint": "无法启用证书和密钥，因为它们不存在。", "tls": "TLS", "common_name": "通用名称", "expireTime": "到期时间", "key_limitation": "该设备的认证和密钥集的最大数量是 {{ size }}.", "title_add_key": "添加证书和密钥集", "title_edit_key": "编辑此证书和密钥", "delete_key_desc": "您确定要删除证书和密钥吗？", "client_certificate": "客户端证书", "client_key": "客户端密钥", "ca_key": "CA 密钥"}, "radius": {"page_title": "RADIUS 服务器", "radius": "RADIUS", "server_address_number": "服务器的IP地址 {{ number }}", "mschap": "MS-CHAPv1", "mschap_v2": "MS-CHAPv2"}, "config_bk_res": {"page_title": "配置备份和復原", "menu_title": "配置备份和復原", "file_encryption": "文件加密", "file_signature": "文件签名", "config_name": "配置名称", "config_file_encryption": "配置文件加密", "configuration_selection": "选择配置", "running_configuration": "Running Configuration", "startup_configuration": "Startup Configuration", "default_configuration": "默认配置", "not_included": "Not Included", "included": "Included", "signed_config": "签名配置", "sign_hint": "启用后，当管理员备份或復原配置时会添加数字签名。", "sign_disable_hint": "由于私钥和公钥为空，无法启用此功能。", "private": "私人的", "certificate": "证书", "label": "标签", "length": "长度", "key_limitation": "该设备的密钥对的最大数量是 {{ size }}.", "title_add_key": "添加自定义密钥", "title_edit_key": "编辑此自定义密钥", "delete_key_desc": "您确定要删除该密钥对吗？", "auto_bk_of_config": "自动配置备份", "auto_load_of_config": "自动配置复原", "auto_restore": "自动配置复原", "auto_restore_hint": "启动期间自动从外部存储设备复原配置。", "encrypt_whole_file": "加密整个文件", "encrypt_sensitive_information_only": "仅加密敏感信息", "encrypt_hint": "如果选择了'仅加密敏感信息\"并且加密密钥字段留空，则将使用 Moxa 加密密钥。"}, "firmware_upgrade": {"page_title": "固件升级"}, "module_information": {"page_title": "模块信息", "module_name": "模块名称", "no_module_msg": "未安装模块"}, "event_notification": {"page_title": "事件通知", "group": "组", "event_name": "事件名", "system_and_function": "系统和功能", "registered_event": "注册事件", "registered_action": "注册行动", "registered_port": "注册端口", "group_general": "通用", "group_switching": "交换", "group_poe": "供电", "group_routing": "路由", "group_tracking": "追踪", "notification_loginSuccess": "Login success", "notification_loginFail": "<PERSON><PERSON> failed", "notification_loginLockout": "Login lockout", "notification_accountChanged": "Account settings changed", "notification_certificationChanged": "SSL certification changed", "notification_passwordChanged": "Password changed", "notification_coldStart": "Cold start", "notification_warmStart": "Warm start", "notification_configurationChanged": "Configuration changed", "notification_configurationImported": "Configuration imported", "notification_logCapacityThreshold": "Log capacity threshold", "notification_powerOff": "Power On->Off", "notification_powerOn": "Power Off->On", "notification_diOn": "DI on", "notification_diOff": "DI off", "notification_topologyChanged": "Topology changed", "notification_couplingChanged": "Coupling changed", "notification_masterChanged": "Master changed", "notification_masterMismatch": "Master mismatch", "notification_rstpTopologyChanged": "RSTP topology changed", "notification_rstpRootChanged": "RSTP root changed", "notification_rstpMigration": "RSTP migration", "notification_rstpInvalidBpdu": "RSTP invalid BPDU", "notification_rstpNewPortRole": "RSTP new port role", "notification_mstpTopologyChanged": "MSTP topology changed", "notification_mstpRootChanged": "MSTP root changed", "notification_mstpNewPortRole": "MSTP new port role", "notification_linkHealthyCheckFail": "Redundant port health check failed", "notification_dualHomingPathSwitched": "Dual homing path changed", "notification_dot1xAuthFail": "802.1X auth failed", "notification_lldpTableChanged": "LLDP table changed", "notification_rmonRaisingAlarm": "RMON raising alarm", "notification_rmonFallingAlarm": "RMON failing alarm", "notification_macsecInterfaceMKAFail": "MACsec MKA failed", "notification_dhcpsnpDynamicEntrySetFailed": "Binding Status dynamic entry failed", "notification_dhcpsnpUntrustMacDiscard": "DHCP client ingress discards packets due to the DHCP Snooping rule", "notification_dhcpsnpUntrustServerDiscard": "DHCP server discards packets due to the DHCP Snooping rule", "notification_multipleCouplingPathChanged": "Multiple coupling path changed", "notification_dhcpBootfileFail": "DHCP Bootfile failed", "notification_trackingStatusChanged": "Tracking Status Changed", "notification_trackingReactionPort": "Tracking Action Triggered on Port", "notification_trackingReactionStaticRoute": "Tracking Action Triggered on Static Route", "notification_trackingReactionVrrp": "Tracking Action Triggered on VRRP", "notification_pdPowerOn": "PD power on", "notification_pdPowerOff": "PD power off", "notification_lowInputVoltage": "Low input voltage", "notification_pdOverCurrent": "PD over-current", "notification_pdNoResponse": "PD no response", "notification_overPowerBudgetLimit": "Over power budget limit", "notification_powerDetectionFailure": "Power detection failure", "notification_nonPdOrPdShort": "Non-PD or PD short circuit", "notification_portOn": "Port On", "notification_portOff": "Port Off", "notification_rateLimitedOn": "Port shut down by Rate Limit", "notification_rateLimitedOff": "Port recovered by Rate Limit", "notification_psecViolationPortDown": "Port shut down by Port Security", "notification_fiberWarning": "Fiber Check warning", "notification_linkUp": "Interface up", "notification_linkDown": "Interface down", "notification_adjacencyChanged": "OSPF adjacency changed", "notification_drChanged": "OSPF DR changed", "notification_becomeDR": "OSPF become DR", "notification_vrrpMasterChanged": "VRRP virtual router master changed", "notification_pimSmDrChanged": "PIM-SM DR changed", "notification_pimSmRpAdded": "PIM-SM RP added by BSM", "notification_pimSmRpDeleted": "PIM-SM RP deleted by BSM", "notification_supABportTimediff": "A PHR Supervision frame time difference event occurred on ports A, B", "notification_gcTimeout": "GOOSE Check entry counter timeout", "notification_gcTimeoutClear": "GOOSE Check entry counter timeout clear", "notification_gcPortTampered": "GOOSE Check tampered ingress port", "notification_gcAddrTampered": "GOOSE Check tampered source MAC address", "notification_gcLockViolation": "GOOSE Check Lock violation", "notification_gcEntryReset": "GOOSE Check entry reset", "notification_gcEntryDelete": "GOOSE Check entry delete", "action_trap": "Trap", "information": "Information", "title_edit_event_notification": "编辑此事件通知"}, "relay_output": {"page_title": "继电器警报", "relay_alarm_cut_off": "关闭继电器警报", "relay_alarm_settings": "继电器警报设置", "fault_led_display": "故障 LED 显示", "cut_off": "截止"}, "statistics": {"page_title": "网络统计", "bandwidth_utilization": "频宽使用情况", "packet_counter": "数据包计数器", "rxTotalOctets": "Rx总八位字节", "collisionPackets": "冲突数据包", "dropPackets": "丢弃的数据包", "dropPacketsHint": "丢弃数据包计数器的默认更新时间约为 5 秒，并根据设备上的端口数量增加。", "rxPausePackets": "接收暂停数据包", "txTotalOctets": "Tx总八位字节", "txUnicastPackets": "发送单播数据包", "crcAlignErrorPackets": "CRC 对齐错误数据包", "txMulticastPackets": "发送组播数据包", "rxBroadcastPackets": "接收广播数据包", "rxUnicastPackets": "接收单播数据包", "jabberPackets": "Jabber 数据包", "excessiveCollisionPackets": "冲突数据包过多", "txTotalPackets": "发送总数据包", "fragmentPackets": "分段数据包", "rxTotalPackets": "接收总数据包", "lateCollisionPackets": "延迟冲突封包", "oversizePackets": "过大的封包", "rxMulticastPackets": "接收组播数据包", "txBroadcastPackets": "Tx 广播数据包", "undersizePackets": "数据包过小", "txptpPackets": "发送 PTP 数据包", "rxptpPackets": "接收 PTP 数据包", "displayMode": "显示模式", "packetCounter": "Packet Counter", "bandwidthUtilization": "Bandwidth Usage", "line_num_target_port": "线 {{ number }} 监控端口", "line_num_target_sniffer": "线 {{ number }} 嗅探器", "txandrx": "Tx/Rx", "txonly": "Tx", "rxonly": "Rx", "all_port": "所有端口", "all_ge_port": "所有 GE 端口", "all_fe_port": "所有 FE 端口", "line_num": "线{{ number }}", "clear_graph_desc": "您确定要清除所有图表数据吗？", "clear_table_desc": "您确定要清除所有表数据吗？", "benchmark_line": "基准", "benchmark_line_time": "基准线-时间", "comparison_line": "比较", "comparison_line_time": "比较线-时间", "selecting_visible_columns": "编辑列表", "title_comparison": "比较数据", "title_reset_statistics_graph": "重置统计图表", "title_edit_statistics_setting": "显示设置", "title_clear_statistics_counter": "清空计数器"}, "mab": {"page_title": "MAC旁路认证", "page_title_abbr": "MAB", "auth_mode": "身份验证模式", "local_database": "Local Database", "quiet_period": "安静时期", "reauthentication": "重新验证", "reauth_period": "<PERSON><PERSON><PERSON>时期", "size_limitation": "该设备的MAC地址条目的最大数量是 {{ size }}.", "retransmit": "重传", "force_unauthorized": "Force Unauthorized", "force_Authorized": "Force Authorized", "reauth_port_confirm": "您确定要重新验证端口吗？", "authorized": "授权", "unauthorized": "未经授权", "title_reauth_port": "<PERSON><PERSON><PERSON>端口", "port_setting": "端口 {{ portIndex }} 设置", "account_setting": "帐户 {{ userName }} 设置", "timeout_retransmit_hint": "所有重试次数不能超过 Dot1x 服务器超时值。注意：  所有重试次数=超时*（重传+1）。推荐值：  {{ number }}", "clear_button_disabled_hint": "当条目数量达到最大容量时，清除通过 MAC 认证旁路收集的所有 MAC 地址条目。", "clear_warning_hint": "通过MAC认证绕过收集的MAC地址条目数量已达到最大容量。", "title_clear_mac_address": "清除MAC地址", "clear_mac_address_message": "您确定要清除通过 MAC 身份验证绕过收集的所有 MAC 地址吗？", "radius_hint": "802.1X和MAC旁路认证绕过共享同一个 RADIUS 服务器。"}, "modbus_tcp": {"page_title": "Modbus TCP", "enable_modbus_tcpo_title": "启用 Modbus TCP", "enable_modbus_tcp_warning": "您确定要启用非安全协议 (Modbus TCP) 吗？"}, "fiber_check": {"page_title": "光纤监测", "threshold_settings": "阈值设置", "model_name": "型号名称", "serial_number": "序列号", "wavelength": "波长", "voltage": "电压", "temperature": "温度", "tx_power": "发射功率", "rx_power": "接收功率", "temperature_limit": "温度阈值", "tx_power_threshold_low": "发射功率阈值低", "tx_power_threshold_high": "发射功率阈值高", "rx_power_threshold_low": "接收功率阈值低", "rx_power_threshold_high": "接收功率阈值高", "temp_over_spec": "温度超过阈值。", "tx_power_over_spec": "Tx 功率超过阈值。", "tx_power_under_spec": "Tx 功率低于阈值。", "rx_power_over_spec": "接收功率超过阈值。", "rx_power_under_spec": "接收功率低于阈值。", "port_copy_to_copper_port_disable": " 无法将配置复制到铜端口。", "warning": "警告", "reset_title": "重置端口 {{ portIndex }} 阈值设置", "reset_desc": "您确定要将此端口重置为自动模式（默认）并清除所有阈值设置吗？", "reset_all_ports": "重置所有端口", "reset_all_ports_title": "重置所有端口阈值设置", "reset_all_ports_desc": "您确定将所有端口重置为自动模式（默认）并清除所有阈值设置吗？"}, "dai": {"page_title": "动态ARP检查", "port_is_la_member": "该端口是端口通道的成员。成员端口无法启用动态ARP检测。", "port_is_trusted": "此端口是 DHCP 侦听的可信端口。只有不受信任的端口才能启用动态 ARP 检查。"}, "mac_sec": {"page_title": "MAC 安全", "mka_status": "MKA 状态", "participant_ckn": "参与者CKN", "participant_cak": "参与者CAK", "key_server": "密钥服务器", "peer_list_mi": "对等列表成员标识符（MI）", "peer_list_mn": "对等列表消息号（MN）", "sci": "安全通道 ID (SCI)", "peer_list_type": "对等列表类型", "live_peer_list": "实时同行列表", "potential_peer_list": "潜在同行名单", "peer_list_mi_hint": "MI：  MKA 消息包括发送者自己的成员 ID 以及已从其接收消息的其他潜在对等点的 ID。", "not_support_10G_port": "10G 端口不支持 MACsec。", "member_port_disable": "此端口是端口通道的成员。无法在成员端口上编辑媒体访问控制安全性。"}, "profinet": {"page_title": "PROFINET", "protocol_information": "协议信息", "enable_profinet_title": "启用 PROFINET", "enable_profinet_warning": "您确定要启用非安全协议 (PROFINET) 吗？", "vlan_not_exist": "VLAN不存在。", "label_format_hint": "这是标签的格式。每个标签应该用\".\"分隔。标签的最大字符数为 63。"}, "ethernet_ip": {"page_title": "EtherNet/IP", "enable_eip_title": "启用EtherNet/IP", "enable_eip_warning": "您确定要启用非安全协议 (EtherNet/IP) 吗？"}, "multi_coupling": {"page_title": "多网络耦合", "main_ring_protocol": "主环协议", "switch_role": "Coupling 交换机作用", "group_id": "Coupling 组ID", "polling_interval": "Coupling 轮询间隔", "polling_interval_hint": "对于主环，使用 80ms 轮询间隔时，路径 ID 的最大数量为 16。当使用 40ms 轮询间隔时，我们建议使用 8 个路径 ID。", "table_settings": "Coupling 表设置", "table_status": "Coupling 表状态", "path_id": "路径ID", "coupling_port": "Coupling 端口", "coupling_port_state": "Coupling 端口状态", "partner_mac": "伙伴MAC", "connect_status": "连接状态", "error_status": "错误状态", "multiple_active": "多个运行中交换机", "multiple_backup": "多个备份交换机", "role_information": "角色信息", "edit_path_title": "编辑路径ID {{ pathId }} 设置", "multi_coupling_enable_hint": "必须启用主环协议 Turbo Ring V2 或 MRP。", "is_coupling_port": "这已被选为耦合端口。", "is_selected_path": "该路径 ID 已被选择。", "error_status_message": "已检测到多个主动/备用交换机。请检查此设备和合作伙伴设备，以确保没有重复。"}, "pim_dm": {"page_title": "PIM-DM", "state_refresh": "状态刷新", "state_refresh_interval": "状态刷新间隔", "pim_dm_hint": "当PIM-DM启用时，如果有IGMP请求，可以与IGMP Snooping一起使用。", "state_refresh_hint": "PIM-DM 模式网络中的所有路由器必须启用状态刷新并配置相同的时间间隔。"}, "pim_sm": {"page_title": "PIM-SM", "pim_sm_settings": "PIM-SM 设置", "pim_sm_hint": "启用 PIM-SM 时，请确保根据需要启用 IGMP Snooping和相关配置。", "spt_method": "最短路径树切换方法", "join_prune_interval": "加入/减少间隔", "dr_priority": "DR 优先级", "bsr": "BSR", "bsr_candidate": "BSR 候选", "bsr_address": "BSR 地址", "bsr_priority": "BSR 优先级", "bsr_hash_mask_length": "BSR 哈希掩码长度", "dr_priority_hint": "在 DR 选举中，优先选择优先级值较大的 DR。", "bsr_priority_hint": "在 BSR 选举中，优先选择优先级值较大的 BSR。", "rp": "RP", "static_rp": "静态 RP", "candidate_rp": "候选 RP", "group_address": "组地址", "group_mask": "组掩码", "rp_address": "RP 地址", "override": "覆盖", "interface_not_created": "尚未创建 PIM-SM 接口。", "rp_if_name": "RP 接口名称", "rp_priority": "RP 优先级", "static_rp_size_limitation": "此设备的最大静态 RP 数量为 {{ size }}。", "candidate_rp_size_limitation": "此设备的最大候选 RP 数量为 {{ size }}。", "ssm": "SSM", "pim_ssm": "PIM-SSM", "pim_ssm_size_limitation": "此设备的最大 PIM-SSM 数量为 {{ size }}。", "title_create_static_rp": "创建静态 RP", "title_edit_static_rp": "编辑静态 RP", "title_delete_static_rp": "删除静态 RP", "delete_static_rp_desc": "您确定要删除所选的静态 RP 吗？", "override_hint": "覆盖意味着当发生冲突时，将优先于动态学习 (BSR) 使用此静态 RP。", "title_create_candidate_rp": "创建候选 RP", "title_edit_candidate_rp": "编辑候选 RP", "title_delete_candidate_rp": "删除候选 RP", "delete_candidate_rp_desc": "您确定要删除选定的候选 RP 吗？", "rp_priority_hint": "在 RP 选举中，优先值较小的 RP 优先。", "rp_if_name_address": "RP 接口名称 (RP 地址)", "title_add_ssm_range": "添加 SSM 范围", "title_edit_ssm_range": "编辑 SSM 范围", "title_delete_ssm": "删除 SSM", "delete_ssm_desc": "您确定要删除选定的 SSM 吗？", "static_ssm_entry_hint": "此范围在 RFC 7761 中为 SSM 保留。", "pim_sm_status": "PIM-SM 状态", "dr_address": "DR 地址", "bsr_rp": "BSR / RP", "elected_bsr": "选举的 BSR", "rp_mapping": "RP 映射", "rp_mapping_result": "RP 映射结果"}, "multicast_routing_table": {"page_title": "组播路由表", "multicast_group": "组播组", "upstream_neighbor": "上游邻居", "incoming_interface": "传入接口", "outgoing_interface": "传出接口", "prune": "<PERSON><PERSON><PERSON>", "assert": "Assert"}, "prp_hsr": {"page_title": "PRP/HSR", "prp_hsr_protocol": "PRP/HSR 协议", "entry_forget_time": "条目忘记时间", "net_id": "Net ID", "lan_id": "LAN ID", "prp": "PRP", "hsr": "HSR", "coupling": "Coupling", "enable_prp_hsr_title": "启用 PRP/HSR", "enable_prp_hsr_warning": "启用 PRP/HSR 会将 PRP/HSR 模块上所有端口的配置重置为默认值，是否确认启用？", "no_phr_module_warning": "未检测到有效的 PHR 模块。检查 PHR 模块。"}, "tracking": {"page_title": "追踪", "tracking_list_of_interface": "Tracking List of Interface", "tracking_list_of_ping": "Tracking List of Ping", "tracking_list_of_logical": "Tracking List of Logical", "tracking_list_of_all": "Tracking List of All", "tid": "Tracking ID", "down_to_up": "从下到上", "up_delay": "上延迟", "up_to_down": "上到下", "down_delay": "下延迟", "received": "已接收", "lost": "丢失", "and": "AND", "or": "OR", "nand": "NAND", "nor": "NOR", "entry_state": "状态", "interface_tracking": "接口追踪", "ping_tracking": "<PERSON> 追踪", "logical_tracking": "逻辑追踪", "create_interface_tracking_entry_title": "创建接口追踪条目", "edit_interface_tracking_entry_title": "编辑此接口追踪条目", "create_ping_tracking_entry_title": "创建 Ping 追踪条目", "edit_ping_tracking_entry_title": "编辑此 Ping 追踪条目", "create_logical_tracking_entry_title": "创建逻辑追踪条目", "edit_logical_tracking_entry_title": "编辑此逻辑追踪条目", "interface_type": "接口类型", "port": "Port", "ping": "<PERSON>", "logical": "Logical", "interface": "Interface", "network_interface": "Network Interface", "status_change_from_down_to_up": "状态更改（从下到上）", "status_change_from_up_to_down": "状态更改（从上到下）", "logical_list": "逻辑列表", "logical_oper": "逻辑运算符", "interfaec_ip_logic": "接口/IP 地址/逻辑列表", "time_since_last_change": "自上次更改后的时间", "no_of_change": "更改次数", "only_select_four": "最多可选择4个", "require_tid_larger": "Tracking ID 必须大于逻辑列表的 TID。", "require_at_least_two_tid": "需要两个以上的 TID", "duplicated_tid": "重复的Tracking ID", "tracking_size_limitation": "此设备的最大Tracking条目数为 {{ size }}。", "sync_to_latest_status": "同步到最新状态"}, "auto_config": {"page_title": "自动设定", "cdu_port": "控制单元端口", "auto_config_info": "自动设定信息", "import_mode_hint": "它要求启用 DHCP 客户端和 LLDP，并预配置 DHCP 客户端启动文件和客户端 ID。在此模式下，自动配置仅通过控制单元端口发送选项 61 数据包。", "propagate_mode_hint": "它要求将 IP 配置设置为手动并启用 LLDP。在此模式下，DHCP 服务器根据 LLDP 信息分配 IP 地址。"}, "multi_local_route": {"page_title": "多播本地路由", "routes": "路由", "macl": "MACL", "vrrp_master_only": "仅 VRRP 主控", "multi_local_route_hint": "如果启用了多播本地路由，则还必须启用 IGMP Snooping。", "vrrp_master_only_hint": "如果启用，交换机在充当 VRRP 主控时只能路由多播流。", "source_vlan": "源 VLAN", "downstream_vlan": "下游 VLAN", "multi_local_route_size_limitation": "多播本地路由条目的最大数量为 {{ size }}。", "create_route_msg": "创建多播本地路由", "edit_route_msg": "编辑源 VLAN {{ vid }}", "macl_id": "MACL ID", "title_create_macl_rule": "创建多播 ACL", "title_edit_macl_rule": "编辑 MACL ID {{ maclId }}", "only_select_sixteen": "最多可选择16个", "delete_session_title": "删除多播本地路由", "delete_session_content": "您确定要删除所选的多播本地路由吗？", "source_vlan_cannot_set": "无法设置源 VLAN。"}, "supervision_frame": {"page_title": "监控帧", "supervision_frame_enable_hint": "必须先启用 PRP/HSR 协议才能启用监控帧。", "life_check_interval": "寿命监测间隔", "destination_address": "目标地址", "forward_to_interlink": "监管转发到互连", "nodes_table": "节点表", "forget_time": "节点遗忘时间", "node_type": "节点类型", "time_last_seen_a": "上次看到的时间 A", "time_last_seen_b": "上次看到的时间 B"}, "goose_check": {"page_title": "GOOSE 检查", "goose_lock": "GOOSE 锁定", "goose_lock_hint": "如果启用 GOOSE 锁定，则监控表中未显示的 GOOSE 数据包将被丢弃。", "tamper_response": "篡改响应", "tamper_response_hint": "选择“丢弃”将丢弃任何被篡改的 GOOSE 数据包。选择“端口禁用”将禁用被篡改的 GOOSE 数据包的入口端口。", "port_disable": "Port Disable", "app_id": "APP ID", "goose_address": "GOOSE 地址 (DA)", "monitoring_table_status": "监控表状态", "goose_lock_status": "GOOSE 锁定状态", "lock_violation_status": "Lcok 违规状态", "goose_name": "GoCB 名称", "rx_counter": "Rx 计数器", "create_goose": "创建状态 GOOSE 条目", "port_tampered": "篡改端口", "sa_tampered": "篡改 SA", "duplicate_goose": "相同的静态 GOOSE 条目已存在", "exist_dynamic_entry": "相同的动态学习 GOOSE 条目已存在。单击“应用”将此条目更改为静态。", "lock_violation_normal_hint": "所有检测到的 GOOSE 数据包都显示在监控表中。", "lock_violation_warning_hint": "检测到意外的 GOOSE 数据包，但未显示在监控表中。", "goose_table_max": "监控表的最大 {{ size }}", "size_limitation": "此设备的最大 goose 检查条目数为 {{ size }}。"}}, "request_handler": {"action_saving": "保存……", "action_loading": "正在加载...", "action_upgrading": "正在升级……", "action_ping": "Pinging ..."}, "response_handler": {"res_server_error": "服务器连接错误。", "res_global_success": "成功更新。", "res_complete_refresh": "刷新完成。", "res_complete_encrypt_data": "加密数据完成。", "res_port_success": "成功更新端口设置。", "res_entry_create_success": "成功创建条目。", "res_entry_update_success": "成功更新条目。", "res_entry_delete_success": "删除条目成功。", "res_port_enable_success": "端口 {{ portIndex }} 已成功启用。", "res_port_disable_success": "端口 {{ portIndex }} 已成功禁用。", "res_dscp_success": "成功更新 DSCP 设置。", "res_cos_success": "成功更新 CoS 设置。", "res_regen_ssh_success": "成功重新生成 SSH 密钥。", "res_regen_ssl_success": "成功重新生成 SSL 证书。", "export_ssl_cert": "成功导出SSL证书。", "import_ssl_cert": "成功导入SSL证书。", "import_config": "成功导入配置。", "res_copy": "已复制。", "res_ping_success": "Ping指令已结束。", "res_auto_save_mode_success": "成功更新自动保存模式。", "res_switch_browse_mode_success": "模式切换成功。", "res_v3_account_update_success": "认证账号更新成功。", "res_host_update_success": "成功更新 SNMP 主机。", "res_upgrading_firmware_success": "固件升级成功，设备将重新启动。", "res_event_notification_success": "成功更新事件通知。", "res_save_to_startup_success": "成功将运行配置保存到启动配置中。", "clear_success": "清除成功。", "res_factory_default_success": "成功恢复出厂设置。", "backup_success": "备份成功。", "res_custom_default_success": "已成功重置为自定义默认值。", "download_success": "下载成功。", "locator": "成功触发设备定位器。", "re_auth_port_success": "已成功重新验证端口。", "res_recovery_port_success": "端口恢复成功。"}, "error_handler": {"error_session_expired_dialog": "本次会话已经结束。系统将返回登录页面。"}, "validators": {"required": "必需", "require_min_length": "最少 {{ number }} 字符", "require_range_between": "{{ rangeBegin }} - {{ rangeEnd }}", "require_range_single_between": "{{ singleNum }}, {{ rangeBegin }} - {{ rangeEnd }}", "require_hexadecimal": "仅限十六进制数字", "require_unicast": "仅单播 IP", "required_at_least_two": "至少2个输出端口", "required_at_least_one_overwrite": "至少一项覆盖项目", "required_priority_multiple": "优先级必须是 {{ num }}", "required_label_max_length": "标签的最大字符数为 {{ number }}", "required_interval_multiple": "间隔必须是 {{ num }} 的倍数", "required_timeout_multiple": "超时间隔必须是 {{ num }} 的倍数", "invalid": "无效的", "invalid_format": "无效的格式", "invalid_positive_integer": "无效的正整数", "invalid_hex": "无效的十六进制数", "invalid_range": "有效范围是从 {{ rangeBegin }} 到 {{ rangeEnd }}", "invalid_single_range": "范围是 {{ singleNum }} 或者 {{ rangeBegin }} 到 {{ rangeEnd }}", "invalid_mac_address": "无效的 MAC 地址", "invalid_ip_address": "IP 地址无效", "invalid_area_id": "无效的区域 ID", "invalid_router_id": "无效的路由器 ID", "invalid_vlan_port": "无效的VLAN成员端口", "invalid_vlan_output_port": "无效的输出端口VLAN", "invalid_netmask": "无效的掩码", "invalid_char": "仅允许使用 a-z、A-Z、0-9", "invalid_email": "无效的电子邮箱", "invalid_regex_level_1": "仅允许使用 a-z、A-Z、0-9 或 . - _ ", "invalid_regex_level_2": "仅允许使用 a-z、A-Z、0-9 或 . , - _ + = | : ; @ ! ~ # % ^ * ( ) [ ] { } ", "invalid_sys_desc": "仅允许使用 a-z、A-Z、0-9 或 ~ ! @ # $ % ^ & * ( ) { } [ ] < > _ + - = \\ : ; , . / ", "invalid_login_failure_message": "仅允许使用 a-z、A-Z、0-9 或! # $ % & ' ( ) * + , \\ - . / : ; < = > @ [ ] ^ _ ` { | } ~", "invalid_regex_macsec_cak_and_ckn": "仅允许使用 a-z、A-Z、0-9 或 @ % $ ^ * ' ` ( ) _ + = { } : . , ~ [ ] - ", "invalid_char_and_dash": "仅允许使用 a-z、A-Z、0-9 或 -", "invalid_char_and_dot_dash": "仅允许使用 a-z、A-Z、0-9 或 . -", "invalid_lowercase_and_dash_dot": "仅允许使用 a-z、0-9 或 . -", "invalid_file_name": "仅允许使用 a-z、A-Z、0-9 或 / ( ) . - _", "duplicate_ip": "重复IP", "duplicate_ip_range": "IP范围重复", "duplicate_vrid": "重复的 VRID", "duplicate_stream": "该流已经存在", "duplicate_id": "ID重复", "duplicate_input_ports": "重复的输入端口", "duplicate_loopback_id": "此环回 ID 已被使用", "duplicate_vlan_id": "此 VLAN ID 已被使用。", "duplicate_vlan_and_mac": "此 VLAN ID 和 MAC 地址组合已存在", "duplicate_group_address_and_netmask": "此组地址和组网络掩码组合已存在", "two_deciaml_palce": "最多 2 位小数", "duplicate_ip_and_netmask": "此 IP 地址和子网掩码组合已存在", "three_deciaml_palce": "最多 3 位小数", "same_as_ingress_stream": "与入口流相同"}}