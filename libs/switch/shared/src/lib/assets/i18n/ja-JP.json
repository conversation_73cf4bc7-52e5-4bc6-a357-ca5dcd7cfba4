{"__readme__": {"__1__": "All key naming is snake_case, in addition to using uri key, e.g.'ospfRRDStatic'", "__2__": "In 'common', words are used repeatedly. It can be reclassified and named 'common_xxx', e.g.'common_port', 'common_account'...", "__3__": "In 'dialog', words are used repeatedly in the dialog.", "__4__": "In 'button', put all button words in and must be capitalized"}, "login": {"title_login_records": "ログイン記録", "redirected_message": "ログアウト成功", "login_success_records_greeting": "ようこそ {{ name }}", "login_success_records_datetime": "最後にログインに成功した時刻は {{ time }} です。", "login_fail_records": "最新のログイン失敗記録。", "modify_password_notification": "セキュリティを強化するために、デフォルトのユーザー名とパスワードを変更してください。", "password_expire_notification": "パスワードの有効期限が切れています。パスワードを変更してください。", "daylight_saving_upgrade": "夏時間機能がアップグレードされました。", "daylight_saving_notification": "設定を更新してください。", "factory_default_note": "スイッチとの Web ブラウザ接続を再確立するには、デフォルトのネットワーク設定を使用する必要があることに注意してください。", "firmware_upgrade_note": "スイッチとの Web ブラウザ接続を再確立する必要があることに注意してください。"}, "general": {"top_nav": {"config_change": {"start_up_save_tip": "設定はスタートアップ コンフィギュレーションに保存されていません。", "confirmation_message": "実行コンフィギュレーションをスタートアップ コンフィギュレーションに適用してもよろしいですか?", "title_apply_to_start_config": "スタートアップ コンフィギュレーションに適用"}, "user_profile": {"greeting": "Hi, {{ username }}", "enable_auto_save": "自動保存を有効にする", "disable_auto_save": "自動保存を無効にする", "disable_auto_save_hint": "適用すると、設定はスタートアップ コンフィギュレーションではなく実行コンフィギュレーションに保存されます。", "change_language": "言語を変えてください", "change_mode": "モード変更", "locator": "ロケータ", "reset_factory_default": "デフォルト設定にリセット", "save_custom_default": "カスタムデフォルトを保存", "standard_mode": "標準", "advanced_mode": "アドバンス", "standard_mode_tooltip": "標準モード", "advanced_mode_tooltip": "アドバンスモード"}, "auto_save_mode": {"enable_auto_save_title": "自動保存モードを有効にする", "enable_auto_save_msg": "自動保存モードを有効にしてもよろしいですか?", "disable_auto_save_title": "自動保存モードを無効にする", "disable_auto_save_msg": "自動保存モードを無効にしてもよろしいですか?"}, "advanced_browse_mode": {"advanced_notification_title": "アドバンストモードに変更", "advanced_notification_msg": "標準モードからアドバンスモードに変更してもよろしいですか?", "basic_notification_title": "標準モードに変更", "basic_notification_msg": "アドバンスモードから標準モードに変更してもよろしいですか?"}, "locator": {"title_switch_locator": "スイッチロケーター", "duration": "間隔", "hint": "デバイスの LED をトリガーして点滅を開始すると、見つけやすくなります。"}, "restart_machine": {"confirmation_title": "リブート", "confirmation_msg": "デバイスを再起動してもよろしいですか?"}, "factory_default": {"confirmation_title": "工場出荷時の設定", "confirmation_msg": "システム構成を工場出荷時のデフォルトにリセットしてもよろしいですか?", "factory_default_hint": "工場出荷時のデフォルト設定にリセットすると、カスタムのデフォルト設定がクリアされます。", "custom_default": "カスタムデフォルト", "confirmation_msg_custom_default": "システム設定をカスタムデフォルトにリセットしてもよろしいですか?", "custom_default_not_exist": "カスタムデフォルトは,そのステータスが「構成が見つかりません!」の場合,実行できません。", "saved_config_name": "保存された構成名", "config_name_hint": "カスタムのデフォルト構成名は不揮発性メモリに保存されます。", "clear_all_config": "設定、ログファイル、および資格情報キーを削除する"}, "save_custom_default": {"confirmation_msg": "スタートアップ設定をカスタムデフォルトとして保存してもよろしいですか?", "current_config_name": "現在の構成名", "config_name_hint": "構成名は,「構成のバックアップと復元」ページで変更できます。"}, "logout": {"confirmation_title": "ログアウト", "confirmation_msg": "ログアウトしてもよろしいですか?"}}, "page_state": {"page_not_found": "ページが見つかりません", "page_not_found_desc": "要求された URL はこのサーバー上に見つかりませんでした。", "back_link": "インデックスページに戻る"}, "menu_tree": {"jump_page_placeholder": "関数の検索", "system": "システム", "system_management": "システムマネジメント", "account_management": "アカウントマネジメント", "provisioning": "プロビジョニング", "port_interface": "ポートインターフェイス", "l2_switching": "レイヤー2スイッチング", "unicast_route": "ユニキャストルート", "multicast_route": "マルチキャストルート", "mac": "MAC", "qos": "QoS", "redundancy": "冗長性", "l2_redundancy": "レイヤー 2 冗長性", "l3_redundancy": "レイヤー3の冗長性", "network_service": "ネットワークサービス", "routing": "ルーティング", "network_management": "ネットワーク管理", "device_security": "デバイスセキュリティ", "network_security": "ネットワークセキュリティー", "diagnostics": "診断", "network_status": "ネットワークステータス", "tools": "ツール", "log_and_event_notification": "イベントログと通知", "application": "産業上の応用", "iec61850": "IEC61850", "iec62439_3": "IEC 62439-3"}, "dialog": {"title_refresh_browser": "ブラウザを更新", "title_change_default_password": "デフォルトのパスワードを変更", "title_change_password": "パスワードを変更する", "title_session_expired": "セッションの有効期限が切れた", "title_notification": "通知", "title_edit_interface": "インターフェースの編集 {{ interfaceName }}", "edit_port_msg": "ポートの編集{{ portIndex }} 設定", "edit_vlan_msg": "VLANの編集{{ vlanIndex }} 設定", "create_entry_msg": "エントリの作成", "edit_entry_msg": "エントリ設定の編集", "delete_entry_msg": "エントリを削除", "title_delete_key": "証明書とキーを削除", "title_delete_account": "アカウントを削除する", "delete_entry_confirm_desc": "選択したエントリを削除してもよろしいですか?", "title_select_file": "ファイルを選ぶ", "title_device_unplugged": "{{ device }} プラグが抜かれています", "desc_device_unplugged": "{{ device }} が接続されているかどうかを確認してください。", "redirect_to_ip": " {{ ipAddress }}にリダイレクト", "page_redirect_content": "ページは 5 秒後にリダイレクトされます。", "redirect_failed_hint": "リダイレクトが失敗した場合は、ネットワーク設定を確認してください。", "after_seconds": " {{ second }} 秒後", "redirecting": "リダイレクト中...", "title_choose_tracking_id": "Tracking IDを選択してください"}, "common": {"network": "ネットワーク", "enable_header": "有効にする", "enabled": "Enabled", "disable_header": "無効にする", "disabled": "Disabled", "none": "None", "authentication": "認証", "active": "Active", "inactive": "非アクティブ", "passive": "Passive", "ip": "IP", "ip_address": "IPアドレス", "mac": "MAC", "mac_address": "Macアドレス", "server_address": "サーバのIPアドレス", "subnet_mask": "サブネットマスク", "domain_name": "ドメイン名", "ip_or_domain": "IPアドレス/ドメイン名", "general": "全般的", "normal": "普通", "type": "タイプ", "mode": "モード", "yes": "はい", "no": "いいえ", "auto": "Auto", "user_defined": "User Defined", "valid": "有効", "invalid": "無効", "required": "必要", "version": "バージョン", "unknown": "知らない", "read_only": "Read Only", "refresh": "リフレッシュ", "reset": "リセット", "add": "追加", "delete": "削除", "export": "エクスポート", "up": "上", "down": "下", "index": "索引", "name": "名前", "method": "方法", "file_name": "ファイル名", "file_server": "ファイルサーバー", "select_file": "ファイルを選ぶ", "action": "アクション", "authority": "権限", "any": "Any", "all": "全て", "unselect": "Unselect", "settings": "設定", "status": "スターテス", "local": "Local", "usb": "USB", "usb_hint": "USBポートはABC-02自動バックアップコンフィギュレーターに対応しています。", "usb_hw_disabled_hint": "外部ストレージ機能が無効になっているため、USB は無効です。", "micro_sd": "microSD", "micro_sd_hw_disabled_hint": "外部ストレージ機能が無効になっているため、microSD は無効です。", "location": "位置", "time": "時間", "start_date": "開始日", "start_time": "開始時間", "end_time": "終了時間", "timeout": "タイムアウト", "interface": "インターフェース", "threshold": "しきい値", "broadcast": "ブロードキャスト", "multicast": "マルチキャスト", "algorithm": "アルゴリズム", "manual": "Manual", "master": "Master", "priority": "優先順位", "permanent": "永続", "queue": "列", "netmask": "ネットマスク", "backup": "バックアップ", "backup_en": "Backup", "restroe": "復元", "broken": "壊れた", "learning": "ラーニング", "listening": "リスニング", "discarding": "ディスカーディング", "forwarding": "フォワーディング", "blocking": "ブロッキング", "packets": "パケット", "notice": "Notice", "warning": "Warning", "critical": "Critical", "error": "Error", "security": "セキュリティ", "slave": "スレーブ", "slot": "スロット", "simple": "Simple", "state": "州", "subtype": "サブタイプ", "protocol": "プロトコル", "init": "初期化", "retry": "リトライ", "severity": "重大度", "destination": "Destination", "description": "説明", "distance": "距離", "expired_date": "有効期限", "delay_time": "遅延時間", "serial_number": "シリアルナンバー", "product_revision": "製品のリビジョン", "quick_setting": "クイック設定", "admin_status": "管理者のステータス", "link_status": "リンクステータス", "system_status": "システムステータス", "link_up": "リンクアップ", "link_down": "リンクダウン", "point_to_point": "Point-to-point", "ethertype": "イーサタイプ", "auth_type": "認証タイプ", "authentication_type": "認証タイプ", "default_gateway": "デフォルトゲートウェイ", "encryprion_key": "暗号化キー", "encryption_method": "暗号化方式", "authentication_password": "認証パスワード", "server_ip_address": "サーバのIPアドレス", "key": "鍵", "key_id": "鍵ID", "vlan_id": "VLAN ID", "vlan_vid": "VLAN {{ vid }}", "vlan_list_info": "複数の VLAN を設定でき、単一の数値または範囲 (例: 2、4 ～ 8、10 ～ 13) として入力する必要があります。", "share_key": "共有キー", "share_key_hint": "このページを離れるか更新すると、セキュリティを強化するために共有キーが自動的にクリアされます。", "auto_backup": "自動的にバックアップ", "auto_backup_hint": "構成が変更された場合は、外部ストレージにバックアップします。", "dynamic": "動的", "static": "静的", "ref_manual_hint": "この設定についてはユーザーマニュアルを参照してください。", "default": "<PERSON><PERSON><PERSON>", "other": "Other", "trusted": "Trusted", "untrusted": "Untrusted", "infinite": "無限", "source": "Source", "low": "低い", "high": "高い", "connected": "接続されました", "disconnected": "切断されました", "incorrect_connected": "正しく接続されていません", "set_event_notifications": "イベント通知を設定する", "include": "含む", "exclude": "除外する", "immediate": "Immediate", "never": "Never", "interface_name": "インターフェース名", "interface_alias": "インターフェースのエイリアス", "hello_interval": "Hello Interval", "neighbor": "Neighbor", "current": "現在", "ping": "<PERSON>", "logical": "論理", "interval": "間隔", "route": "ルーティング", "open": "Open", "short": "Short", "role": "役割", "speed": "スピード", "failed": "失敗", "successful": "成功しました", "idle": "アイドル", "protection": "プロテクション", "pending": "保留中", "supported": "サポート対象", "not_supported": "サポートされていません", "import": "インポート", "propagate": "伝播", "ascii": "ASCII", "hex": "HEX", "zone": "ゾーン"}, "common_account": {"account": "アカウント", "username": "ユーザー名", "pwd_mask": "********", "password": "パスワード", "confirm_password": "パスワードを認証する", "email": "Email", "delete_account_desc": "選択したアカウントを削除してもよろしいですか?"}, "common_abbrev": {"md5": "MD5", "sha": "SHA", "sha-224": "SHA-224", "sha-256": "SHA-256", "sha-384": "SHA-384", "sha-512": "SHA-512", "sftp": "SFTP", "tftp": "TFTP", "pap": "PAP", "chap": "CHAP", "cos": "CoS", "dscp": "DSCP", "cir": "CIR", "cir_full": "認定情報レート", "cbs": "CBS", "cbs_full": "コミットされたバーストサイズ"}, "common_port": {"port": "ポート", "ports": "ポート", "port_name": "ポート {{ portName }}", "tcp_port": "TCP ポート", "udp_port": "UDPポート", "port_state": "ポートステート", "port_status": "ポートステータス", "port_settings": "ポート設定", "port_shutdown": "Port Shutdown", "destination_port": "宛先ポート", "reflect_port": "リフレクトポート", "all_port": "All Ports", "member_port": "メンバーポート", "also_apply_port": "設定をポートにコピー", "also_apply_port_hint": "ドロップダウン ボックスから選択したポートに構成をコピーします。", "ingress_port": "イングレスポート"}, "button": {"cancel": "キャンセル", "apply": "適用", "create": "作成", "edit": "編集", "delete": "削除", "sync_from_browser": "ブラウザから同期", "regenerate": "再生", "import": "インポート", "export": "エクスポート", "expand": "拡大", "collapse": "収縮", "copy": "コピー", "close": "クローズ", "download": "ダウンロード", "ping": "PING", "clear": "クリア", "backup": "バックアップ", "restore": "復元", "upgrade": "アップグレード", "reset": "リセット", "locate": "位置を特定する", "disable": "無効にする", "enable": "有効にする", "change": "変更", "logout": "ログアウト", "reboot": "リブート", "confirm": "確認", "remove": "削除", "back": "戻る", "log_in": "ログイン", "re_auth": "再認証", "recovery": "回復", "export_cid_file": "CID ファイルのエクスポート", "export_server_ca": "サーバーCAのエクスポート", "change_password": "パスワードを変更する", "export_server_cert": "サーバー証明書のエクスポート", "view_all_event_logs": "すべてのイベントログを表示", "update_daylight_saving": "夏時間の更新", "select": "選択する", "retry": "リトライ", "encrypt": "暗号化", "change_cak": "CKNとCAKを変更する", "save": "保存", "find_rp": "RPを探す", "cut_off": "切り落とす", "go_to": "GO TO", "got_it": "GOT IT"}, "tooltip": {"edit": "編集", "delete": "消去", "copy": "コピー", "copy_config": "設定のコピー", "close": "クローズ", "clear": "クリア", "clear_graph": "グラフをクリア", "remove": "削除", "reorder": "並べ替える", "recovery": "回復", "select_tracking": "Tracking IDを選択してください", "remove_tracking": "Tracking IDを削除する", "vlan_size_limitation": "このデバイスでは、 {{ size }} VLAN。", "size_limitation": "このデバイスのユーザー アカウントの最大数は {{ size }}。", "reorder_priority": "優先順位を並べ替える", "reorder_finish": "並べ替えが完了しました", "export_pdf_all_acl": "すべての ACL を PDF にエクスポート", "export_pdf_only_this_port": "このポートの情報のみを PDF にエクスポートします", "export_pdf_only_this_vlan": "この VLAN の情報のみを PDF にエクスポートします", "clear_counter": "カウンターをクリア", "unicast_mac_address_hint": "ユニキャスト MAC アドレスのみを許可します。", "disable_la_member_hint": "これはリンク アグリゲーション ポート チャネルのメンバー ポートです。", "duplicate_redundant_port": "冗長ポートを複製できません", "auto_refresh_enabled": "自動更新：  有効", "auto_refresh_disabled": "自動更新：  無効", "manually_refersh_disabled": "『自動更新』が有効になっている場合は手動で更新できません", "integration_3A_3B_hint": "このポートは 3/A と 3/B を統合したものです。", "set_to_static": "Set to static"}, "unit": {"fps": "fps", "min": "min.", "sec": "sec.", "ms": "ms", "byte": "byte", "m": "M", "kbyte": "Kbyte", "mbps": "Mbps", "w": "W", "watt": "ワット", "watts": "ワット", "percent": "%", "day": "日", "times": "回", "mb": "MB", "ns": "ns", "microsecond": "μs", "ppm": "PPM", "packets": "パケット", "v": "V", "dbm": "dBm", "c": "°C", "f": "°F", "nm": "nm"}, "speed": {"10m": "10M", "100m": "100M", "1g": "1G", "10g": "10G", "40g": "40G", "56g": "56G", "2500m": "2500M", "full": "Full", "half": "Half"}, "table_function": {"filter_desc": "検索", "export_pdf": "PDF をエクスポート", "export_csv": "CSV をエクスポート", "selected_count": "選択済み", "row_count": "合計", "limit_count": "マックス"}, "led": {"status_unknown_hint": "ステータス不明。", "state_green_on": "通常動作", "state_green_blink4hz": "システムが起動中です。", "state_red_on": "システムの初期化に失敗しました。", "state_off": "システムがオフになっています。", "fault_red_on": "システムに障害が発生しました。詳細についてはシステム ログを確認してください。", "fault_red_blink4Hz": "スイッチが起動し、ファームウェアがメモリにロードされました。", "fault_off": "通常動作。", "mh_turbo_chain_head_green_on": "スイッチはターボ チェーンのヘッドです。", "mh_turbo_ring_green_on": "スイッチはターボ リング 1 またはターボ リング 2 のマスターです。", "mh_mrp_green_on": "スイッチは MRP リングのマネージャーです。", "mh_turbo_ring_green_blink2hz_mds": "スイッチはターボ リング 1 またはターボ リング 2 のマスターであり、少なくとも 1 つのリングが壊れています。", "mh_turbo_chain_head_green_blink2hz_mds": "スイッチはターボチェーンのヘッドであり、チェーンが切れています。", "mh_mrp_green_blink2hz_mds": "スイッチは MRP リングのマネージャーであり、リングは開いています。", "mh_turbo_chain_member_green_blink2hz_mds": "スイッチはターボ チェーンのメンバーであり、対応するメンバー ポート 1 はリンク ダウンしています。", "mh_turbo_chain_tail_green_blink2hz_mds": "ターボ チェーンのテールと対応するメンバー ポートのスイッチがリンク ダウンしています。", "mh_turbo_ring_green_blink4hz": "スイッチはターボ リング 1 またはターボ リング 2 のマスターであり、少なくとも 1 つのリングが壊れています。", "mh_turbo_chain_head_green_blink4hz": "スイッチはターボチェーンのヘッドであり、チェーンが切れています。", "mh_mrp_green_blink4hz": "スイッチは MRP リングのマネージャーであり、リングは開いています。", "mh_turbo_chain_member_green_blink4hz": "スイッチはターボ チェーンのメンバーであり、対応するメンバー ポート 1 はリンク ダウンしています。", "mh_turbo_chain_tail_green_blink4hz": "ターボ チェーンのテールと対応するメンバー ポートのスイッチがリンク ダウンしています。", "mh_off": "スイッチはターボ リング 1 またはターボ リング 2 のマスター、ターボ チェーンのヘッド、または MRP リングのマネージャーではありません。", "ct_turbo_ring_green_on": "スイッチのカップリング機能が有効になり、バックアップ パスが形成されます。", "ct_mrp_green_on": "スイッチのカップリング機能が有効になり、バックアップ パスが形成されます。", "ct_turbo_chain_tail_green_on": "スイッチはターボチェーンのテールです。", "ct_dual_homing_green_on": "スイッチの dual homing 機能が有効になっています。", "ct_turbo_ring_dual_homing_green_on": "スイッチの coupling 機能と dual homing  機能が有効になります。", "ct_turbo_chain_dual_homing_green_on": "スイッチのdual homing 機能が有効になっており、スイッチはターボチェーンのテールです。", "ct_mrp_dual_homing_green_on": "スイッチの MRP connection および dual homing 機能が有効になります。", "ct_turbo_chain_head_green_blink2hz_mds": "スイッチはターボ チェーンのヘッドであり、対応するメンバー ポートはリンク ダウンしています。", "ct_turbo_chain_member_green_blink2hz_mds": "スイッチはターボ チェーンのメンバーであり、対応するメンバー ポート 2 はリンク ダウンしています。", "ct_turbo_chain_tail_green_blink2hz_mds": "スイッチはターボチェーンのテールで、チェーンが切れています。", "ct_turbo_chain_head_green_blink4hz": "スイッチはターボ チェーンのヘッドであり、対応するメンバー ポートはリンク ダウンしています。", "ct_turbo_chain_member_green_blink4hz": "スイッチはターボ チェーンのメンバーであり、対応するメンバー ポート 2 はリンク ダウンしています。", "ct_turbo_chain_tail_green_blink4hz": "スイッチはターボチェーンのテールで、チェーンが切れています。", "ct_off": "スイッチのカップリングまたはdual homing 機能が無効になっているか、スイッチがターボ チェーンのテールではありません。", "sync_amber_on": "PTP 機能が有効になっています。", "sync_amber_blink4hz": "スイッチは同期パケットを受信しました。", "sync_green_on": "PTP 関数は正常に収束しました。", "sync_off": "PTP 機能が無効になっています。", "ms_green_on": "通常動作。", "ms_green_blink2hz": "このモジュールは起動中です。", "ms_off": "モジュールはサービス停止中です。", "ms_red_on": "モジュールの初期化に失敗したか、ユーザーが間違ったモジュールを挿入しました。", "eps_amber_on": "外部電源装置は PoE ポートに電力を供給する準備ができています。", "eps_off": "PoE デバイス用の外部電源がありません。", "pwr_eps_amber_on": "外部電源がモジュールの EPS 入力に供給されています。", "pwr_eps_off": "PoE デバイス用の外部電源がありません。", "pwr_amber_on": "モジュールの電源入力に電力が供給されています。", "pwr_off": "モジュールの電源入力に電力が供給されていません。", "port_poe_green_on": "ポートは IEEE 802.3at 受電装置 (PD) に接続されています。", "port_poe_green_on_poebt": "ポートは IEEE 802.3bt 受電装置 (PD) に接続されています。", "port_poe_amber_on": "ポートは IEEE 802.3af 受電装置 (PD) に接続されています。", "port_poe_amber_on_poebt": "ポートは IEEE 802.3af/at 受電装置 (PD) に接続されています。", "port_poe_amber_blink4hz": "電力バジェットが十分でないため、PoE 電力は遮断されました。", "port_poe_red_on": "受電装置 (PD) の検出に失敗しました。", "port_poe_red_blink4hz": "受電装置 (PD) で過電流または短絡が検出されました。", "port_poe_off": "受電装置 (PD) に電力が供給されていません。", "port_link_up_hint": "ポートはアクティブであり、リンクしています {{ operSpeed }}bps。", "port_link_down_hint": "ポートが非アクティブであるか、リンクがダウンしています。", "prp_green_on": "PRP機能が有効になっています。", "prp_off": "PRP 機能が無効になっています。", "hsr_green_on": "HSR機能が有効になっています。", "hsr_off": "HSR 機能が無効になっています。", "coup_green_on": "Coupling機能が有効になっています。", "coup_off": "Coupling 機能が無効になっています。"}}, "features": {"storm_control": {"page_title": "トラフィックストームコントロール", "dlf": "DLF"}, "la": {"page_title": "リンクアグリゲーション", "port_channel": "ポートチャネル（トランク）", "wait_time": "待ち時間", "configure_member": "メンバーの構成", "active_member": "アクティブメンバー", "la_group_status": "LAグループのステータス", "lacp": "LACP", "smac": "SMAC", "dmac": "DMAC", "smac_dmac": "SMAC + DMAC", "config_member_port": "メンバーポート設定", "config_member_port_hint": "ポート チャネルに追加できないポートを少なくとも 1 つ保持します。", "delete_port_channel_confirm_desc_1": "警告：、", "delete_port_channel_confirm_desc_2": "選択したリンク アグリゲーションに関連する一部の機能 (RSTP や VLAN など) はデフォルト値に設定されます。", "delete_port_channel_confirm_desc_3": "選択したリンクアグリゲーションを削除してもよろしいですか?", "la_size_limitation": "このデバイスでは、 {{ size }} ポート チャネル。", "create_la_msg": "リンクアグリゲーションの作成", "edit_la_pre_msg": "ポートチャネルの編集 {{ portChannel }} 設定", "delete_la_msg": "リンクアグリゲーションの削除", "only_select_eight": "最大8つまで選択可能です。"}, "scheduler": {"page_title": "スケジューラー", "strict_priority": "Strict Priority", "weight_round_robin": "Weighted Round Robin", "sp": "SP", "wrr": "WRR", "wfq": "WFQ"}, "egress_shaper": {"page_title": "イグレスシェイパー", "egress_rate": "下り速度（CIR）"}, "rate_limit": {"page_title": "イングレスレート制限", "ingress_rate": "イングレスレート (CIR)", "ebs": "EBS", "ebs_full": "超過バーストサイズ", "conform_action": "適合アクション", "exceed_action": "超越アクション", "violate_action": "違反行為", "blind": "カラーブラインド", "aware": "カラーアウェア", "do_nothing": "Do Nothing", "drop": "Drop", "remark_cos": "Remark CoS", "remark_dscp": "Remark DSCP", "simple_token_bucket": "Simple Token Bucket", "sr_tcm": "SrTCM", "remark_value": "備考値", "release_interval": "リリース間隔", "rate_limit_port_shutdown": "レート制限ポートのシャットダウン"}, "classification": {"page_title": "分類", "cos_priority": "CoS優先度", "preference_type": "信頼タイプ", "dhcp_mapping": "DSCPマッピング", "cos_mapping": "CoS マッピング", "untag_default_priority": "デフォルトの優先順位のタグを解除", "edit_dscp_msg": "DSCP の編集 {{ dscpIndex }} 設定", "edit_cos_msg": "CoS の編集 {{ cosIndex }} 設定"}, "linkup_delay": {"page_title": "リンクアップ遅延", "remaining_time": "残り時間"}, "port_mirror": {"page_title": "ポートミラーリング", "span": "SPAN", "rspan": "RSPAN", "session_id": "セッションID", "reflect_port_mode": "リフレクトポートモード", "rspan_type": "RSPAN タイプ", "rspan_vid": "RSPAN VLAN ID", "rspan_settings": "RSPAN中間設定", "rspan_setting_hint": "すべての VLAN トランク ポートが RSPAN VLAN に追加されます。", "rspan_vlan_setting_hint": "タグ付きフレームがドロップされないようにするには、トランクまたはハイブリッドモードのVLANポートのみをReflectポートとして選択します。", "duplicate_intermediate_vlan": "中間 VLAN ID はすでに使用されています。", "rspan_role": "RSPAN 中間ロール", "rspan_intermediate_vid1": "RSPAN 中間の 1 番目の VLAN ID", "rspan_intermediate_vid2": "RSPAN 中間の第 2 VLAN ID", "enable_rspan_title": "RSPAN 中間ロールを有効にする", "enable_rspan_warning": "この設定により、既存の RSPAN セッションがすべて削除されます。続行してもよろしいですか?", "rx_source_port": "受信元ポート", "tx_source_port": "送信元ポート", "designated_port": "指定ポート", "destination_ports": "宛先ポート", "destination_port_info": "アクセス ポートの場合、ポートの PVID は RSPAN VLAN ID に設定されます。 \nハイブリッド ポートまたはトランク ポートの場合、ポートは RSPAN VLAN のメンバーになります。", "destination_port_hint": "宛先ポートが RSPAN VLAN に追加されます。", "destination_ports_or_designated_port": "宛先ポートまたは指定ポート", "source_port_two_field_invalid": "Tx または Rx ソース ポートを選択する必要があります", "create_mirror_msg": "セッションの作成", "edit_mirror_msg": "セッションの編集 {{ sessionIndex }} 設定", "select_tx_or_rx_hint": "TX または RX ソース ポートを選択する必要があります。", "is_not_access_port": "このポートはアクセス ポートではありません。", "is_not_trunk_port": "このポートはトランク ポートではありません。", "source_port_must_be_access_port": "Reflect Port Modeが有効な場合,ソースポートはアクセスポートである必要があります。", "reflect_port_must_be_access_port": "リフレクトポートモードが有効になっている場合,リフレクトポートはアクセスポートである必要があります。", "reflect_port_must_be_trunk_hybrid_port": "リフレクトポートモードが有効になっている場合,リフレクトポートはトランク/ハイブリッドポートである必要があります。", "pvid_is_not_rspan_vid": "このポートの PVID は RSPAN VLAN ではありません。", "rspan_source_session_exist": "RSPAN 送信元セッションはすでに存在します。", "rspan_destination_session_exist": "RSPAN 宛先セッションはすでに存在します。", "rspan_cannot_create": "RSPAN 中間役割が有効な場合、RSPAN セッションを作成できません。", "session_span_size_limitation": "SPAN エントリの最大数は {{ size }}。", "session_rspan_size_limitation": "RSPAN エントリの最大数は {{ size }}。", "delete_session_title": "セッションを削除", "delete_session_content": "選択したセッションを削除してもよろしいですか?", "rspan_vid_hint_l2": "RSPAN に管理 VLAN または VLAN Assignment-configured VLANs  を使用することはお勧めできません。", "rspan_vid_hint_l3": "RSPAN にVLAN インターフェイス または VLAN Assignment-configured VLANs  を使用することはお勧めできません。"}, "vlan": {"page_title": "VLAN", "vlan": "VLAN", "global": "グローバル", "management_vlan": "管理VLAN", "management_port": "管理ポート", "mgmt_vlan_settings": "管理ポートのクイック設定", "management_vlan_port_setting_hint": "スイッチから切断されないように、コンピューターが接続されているポートを選択し、設定が正しいことを確認してください。", "port_mode_table_title": "VLAN スイッチポート モード テーブル", "egress_tagged_table_title": "VLAN メンバーシップ テーブル", "gvrp": "GVRP", "vlan_unaware": "VLAN Unaware", "vlan_unaware_gvrp_error": "GVRP cannot be enabled while VLAN Unaware is active.", "vlan_unaware_active_disable_hint": "VLAN cannot be modified while VLAN Unaware is active.", "all_member_vlan": "すべてのメンバー VLAN ID", "dynamic_gvrp": "ダイナミックGVRP", "egress_port": "出口ポート", "tagged_port": "タグ付きポート", "untagged_port": "タグなしポート", "forbidden_port": "Forbidden Port", "vid_exist_warning": "VLAN はすでに存在します", "vlan_max_warning": "一度に最大 10 VLAN", "vlan_max_hint": "最大 10 VLAN", "pvid": "PVID", "tagged_vlan": "タグ付きVLAN", "untagged_vlan": "タグなしVLAN", "access": "アクセス", "access_port": "アクセスポート", "trunk": "トランク", "trunk_port": "トランクポート", "hybrid": "ハイブリッド", "hybrid_port": "ハイブリッドポート", "vlan_assignment": "VLANの割り当て", "delete_vlan_confirm_desc": "選択した VLAN を削除してもよろしいですか?", "mgmt_setting_disabled_pvid": "PVID はこの VLAN にバインドされているため、削除できません。", "mgmt_setting_disabled_access_mode": "ポートがアクセス モードを使用している場合、この VLAN に変更することはできません。", "mgmt_setting_disabled_forbidden": "この端口は立ち入り禁止の端口です。", "mgmt_setting_disabled_egress": "このポートはメンバーポートです。", "port_setting_disabled_tagged": "この VLAN はタグ付き VLAN です。", "port_setting_disabled_untagged": "この VLAN はタグなし VLAN です。", "port_setting_disabled_forbidden": "このポートは、この VLAN に対して禁止されたポートです。", "port_setting_disabled_pvid_member": "このポートはメンバー ポートではないため、この PVID はこの VLAN にバインドできません。", "port_setting_disabled_pvid_forbidden": "このポートは禁止されたポートであるため、この PVID はこの VLAN にバインドできません。", "port_setting_error_pvid_member": "タグなしまたはタグなし VLAN", "port_setting_error_pvid_forbidden": "これは禁止されたポートであるため、設定を適用できません", "vlan_setting_vid_info": "複数の VLAN を設定でき、単一の数値または範囲 (例: 2、4 ～ 8、10 ～ 13) として入力する必要があります。", "te_mstid": "TE-MSTID", "temstid_member": "TE-MSTIDメンバー", "temstid_info": "VLAN 参加 TE-MSTID メンバーの場合、ストリームは MAC 学習/転送メカニズムではなく静的転送ルールによって転送されます。", "create_vlan_msg": "VLANの作成", "delete_vlan_msg": "VLAN の削除", "vlan_setting_info_title": "セットアップ方法", "example_scenario": "シナリオ例", "example_scenario_info_1": "ポート1: ハイブリッド モード、PVID 1、TAG VLAN 3 ～ 5、および UNTAG VLAN 1", "example_scenario_info_2": "ポート2: トランク モード、PVID 2、および TAG VLAN 2 ～ 5 インチ、", "example_scenario_info_3": "ポート3: アクセス モード、PVID 1、および UNTAG VLAN 1", "example_scenario_info_4": "スイッチAの設定: 管理 VLAN 1", "setup_flow": "セットアップフロー", "vlan_port_mode_setup": "VLAN ポートモードの設定", "port_number_setting": "ポート {{ portNumber }} 設定", "setup_list_hybrid": "- モードは\"ハイブリッド\"を選択します", "setup_list_apply": "- 適用", "setup_list_trunk": "- モードは\"トランク\"を選択します", "setup_list_access": "- デフォルトのモードは\"アクセス\"なので、何も変更する必要はありません。", "setup_list_pvid2": "-PVID 2", "vlan_create_member_setup": "VLAN作成/VLANメンバー設定", "vlan_create_member_setup_info_part_1": "クリック", "vlan_create_member_setup_info_part_2": ",VLAN IDを追加します {{ vlanIndex }} 、メンバーポートを追加 {{ portIndex }}", "vlan_port_pvid_setup": "VLAN ポートの PVID 設定", "vlan_port_pvid_setup_info": "準備は完了しました。変更する必要はありません。"}, "l3_interface": {"page_title": "ネットワークインターフェース", "loopback_size_limitation": "このデバイスでは、 {{ size }} ループバック。", "operStatus": "動作状況", "loopback_id": "ループバックID", "vlan_interface": "VLANインターフェース", "loopback_interface": "ループバックインターフェイス", "alias": "エイリアス", "mtu": "MTU", "proxy_arp": "プロキシ ARP", "vlan_id_hint": "レイヤ 3 ルーティングを機能させるには、インターフェイス VLAN ID がレイヤ 2 VLAN と同じである必要があります。", "delete_ip_interface_desc": "このエントリを削除してもよろしいですか?", "vlan_card_title": "VLANインターフェース", "loopback_card_title": "ループバックインターフェイス", "delete_ip_interface": "インターフェイスの削除", "add_l3_vlan_ip_interface": "VLAN インターフェイス設定の作成", "edit_l3_vlan_ip_interface": "VLAN インターフェイス設定の編集", "add_l3_loopback_ip_interface": "ループバックインターフェイス設定の作成", "edit_l3_loopback_ip_interface": "ループバックインターフェイス設定の編集", "from_dcp": "(PROFINET DCPより)"}, "stp": {"page_title": "スパニングツリー", "stp_mode": "STPモード", "compatibility": "互換性", "stp": "STP", "rstp": "RSTP", "mstp": "MSTP", "stp_rstp": "STP/RSTP", "bridge_priority": "ブリッジプライオリティ", "error_recovery_time": "エラー回復時間", "forward_delay_time": "転送遅延時間", "hello_time": "ハロータイム", "max_age": "Max. Age", "edge": "エッジ ", "guard": "ガード", "path_cost": "パスコスト", "multiples_of_number": " {{ number }}の倍数", "path_cost_help_info": "値がゼロに設定されている場合、パス コスト値は、異なるポート速度に従って自動的に割り当てられます。", "bpdu_guard": "BPDU ガード", "root_guard": "ルートガード", "loop_guard": "ループガード", "bpdu_filter": "BPDU フィルター", "root_information": "ルート情報", "bridge_id": "ブリッジID", "root_path_cost": "ルートパスコスト", "bridge_information": "ブリッジ情報", "running_protocol": "実行プロトコル", "port_role": "ポートの役割", "link_type": "リンクタイプ", "shared": "Shared", "bpdu_inconsistency": "BPDU の不一致", "root_inconsistency": "ルートの不一致", "loop_inconsistency": "ループの不一致", "link_type_shared_lan": "Shared LAN", "alternate": "Alternate", "root": "ルート", "designated": "指定", "instance": "インスタンス", "instance_index": "インスタンス {{ instId }}", "all_instances": "すべてのインスタンス", "instance_list": "インスタンスリスト", "instance_id": "インスタンスID", "mstp_size_limitation": "このデバイスのインスタンスの最大数は {{ size }} CIST を除く。", "vlan_list": "VLAN リスト", "port_table_of_cist": "CISTのポートテーブル", "port_table_of_instance": "インスタンスのポートテーブル {{ instId }}", "information_of_cist": "CISTの情報", "information_of_instance": "インスタンスの情報 {{ instId }}", "region_name": "地域名", "region_revision": "地域の改訂", "max_hops": "最大ホップ数", "instance_id_duplicate": "インスタンス ID が作成されました。", "except_for_cist": "CIST を除く。", "copy_port_config": "ポート設定をコピーします。", "select_from_port_of_inst": "のポートから {{ instName }}", "select_to_inst": "インスタンスへ", "general_information": "一般情報", "regional_root_id": "リージョンルートID", "cist_root_id": "CIST ルート ID", "cist_path_cost": "CIST パス コスト", "designated_root_id": "指定されたルートID", "other_vlans": "その他の VLAN", "create_instance": "インスタンスの作成", "edit_instance": "インスタンスの編集 {{ instId }} 設定", "delete_instance": "インスタンスの削除", "edit_cist": "CIST 設定の編集", "edit_instance_port": "インスタンスの編集 {{ instId }} ポート {{ portIndex }} 設定", "edit_cist_port": "CIST ポートの編集 {{ portIndex }} 設定"}, "port_security": {"page_title": "ポートセキュリティ", "port_security_mode": "ポートセキュリティモード", "port_security_mode_help_info": "ポート セキュリティ モードを変更すると、すべての設定がリセットされます。", "mac_sticky": "MAC Sticky", "static_port_lock": "Static Port Lock", "address_limit": "アドレス制限", "secure_action": "安全なアクション", "current_address": "現住所", "configured_address": "手動で構成されたアドレス", "violation": "違反", "effective": "効果的", "secure_pack_drop": "Packet Drop", "total_entry": "トータルトラストホスト", "max_address": "システム内のアドレスの最大数", "sticky_configured": "スティッキー構成", "lock_configured": "ロックが設定されました", "sticky_dynamic": "スティッキーダイナミック", "address_limit_hint": "アドレス制限値が変更された場合、ポート上のすべての MAC アドレスが削除されます。"}, "garp": {"page_title": "GARP", "join_time": "Join Time", "leave_time": "Leave Time", "leave_all_time": "Leave All Time", "required_join_time_multiple": "Join Timeは{{ num }}の倍数でなければなりません", "required_leave_time_multiple": "Leave Timeは{{ num }}の倍数でなければなりません", "required_leave_all_time_multiple": "Leave All Timeは{{ num }}の倍数でなければなりません"}, "lldp": {"page_title": "LLDP", "neighbor_status": "近隣ステータス", "sidenav_header": "詳細情報", "port_local_intf_status": "ポートローカルインターフェイス", "port_id_subtype": "ポートIDのサブタイプ", "port_id": "ポートID", "port_desc": "ポートの説明", "dot1_tlv_info": "拡張 802.1 TLV", "dot3_tlv_info": "拡張 802.3 TLV", "port_vlan_id": "ポートVLAN ID", "vlan_name": "VLAN名", "vlan_tx_status": "VLAN ID / 名前", "aggregated_and_status": "リンクアグリゲーションステータス", "aggregated_port_id": "アグリゲイティドポートID", "max_frame_size": "最大フレームサイズ", "port_traffic_statistics": "ポートトラフィック統計", "total_frame_out": "合計フレームアウト", "total_entries_aged": "期限切れのエントリの合計", "total_frame_in": "総フレーム数", "total_frame_receviced_in_error": "エラーで受信した合計フレーム数", "total_frame_discarded": "廃棄された合計フレーム数", "total_tlvs_unrecognized": "認識されない TLVS の合計", "total_tlv_discarded": "破棄された TLV の合計", "management_address_table": "管理アドレステーブル", "management_address": "管理アドレス", "extended_eip_tlv": "拡張イーサネット/IP TLV", "vendor_id": "ベンダーID", "device_type": "デバイスタイプ", "product_code": "製品コード", "major_revision": "大幅な改訂", "minor_revision": "マイナーリビジョン", "interface_id": "インターフェースID", "lldp_version": "LLDPバージョン", "transmit_interval": "送信間隔", "notification_interval": "通知間隔", "reinit_delay": "再初期化の遅延", "holdtime_multiplier": "ホールドタイム乗数", "chass_id_subtype": "シャーシ ID サブタイプ", "tx_delay": "送信遅延", "subtype_chassis_component": "<PERSON><PERSON>s-Component", "subtype_if_alias": "<PERSON>-<PERSON><PERSON>", "subtype_port_component": "Port-Component", "subtype_mac_addr": "MAC-Address", "subtype_network_address": "Network-Address", "subtype_if_name": "If-Name", "subtype_unknown": "不明なサブタイプ", "chassis_id": "シャーシID", "tlv": "TLV", "local_info": "ロカール情報", "local_timer": "ローカルタイマー", "remote_table_statistics": "リモートテーブル統計", "statistics_last_change_time": "最終変更時間 (ミリ秒)", "statistics_insert": "インサート", "statistics_drops": "ドロップ", "statistics_ageout": "エイジアウト", "tx_status": "送信ステータス", "rx_status": "受信ステータス", "nbr_port_id": "近隣ポートID", "nbr_chassis_id": "ネイバーシャーシID", "tx_only": "Tx Only", "rx_only": "Rx Only", "tx_and_rx": "Tx and Rx", "basic": "基本", "basic_transmit_tlvs": "基本的な送信TLV", "8021_transmit_tlvs": "802.1 送信 TLV", "8023_transmit_tlvs": "802.3 送信 TLV", "port_component_description": "ポートコンポーネントの説明。", "system_name": "システム名", "system_desc": "システムの説明", "system_capability": "システム機能", "la_statistics": "リンクアグリゲーション統計", "lldp_update_success": "LLDP グローバル設定が正常に更新されました。", "local_port": "ローカルポート", "sys_capability": "システム機能", "hold_time": "ホールドタイム", "repeater": "リピータ", "bridge": "ブリッジ", "vlan_access_point": "VLANアクセスポイント", "telephone": "電話", "docsis_cable_device": "ドクシスケーブルデバイス", "station_only": "Station Only"}, "mac_address_table": {"page_title": "MAC アドレス テーブル", "independent_vlan_learning": "独立した VLAN 学習", "mac_learning_mode": "MAC学習モード", "mac_learning_mode_help_info": "変更モードでは関連する L2 モジュールがリセットされることに注意してください。", "aging_time": "エージングタイム", "learnt_unicast": "ユニキャストを学習", "learnt_multicast": "マルチキャストを学習する"}, "port_setting": {"page_title": "ポート設定", "admin": "管理者", "media_type": "メディアタイプ", "10m_half": "10M Half", "10m_full": "10M Full", "100m_half": "100M Half", "100m_full": "100M Full", "speed_duplex": "速度/デュプレックス", "flow_control": "フロー制御", "flow_control_hint1": "フロー制御は全二重通信時にのみ有効/無効できます。", "flow_control_hint2": "バックプレッシャーは有効/無効にできますが、半二重でのみ有効です。", "mdi_mdix": "MDI/MDIX", "mdi": "MDI", "mdix": "MDIX", "enabled_xmit": "Enabled Transmit", "enabled_rcv": "Enabled Receive", "fiber_speed_disable": "ファイバーポートは速度/デュプレックスを設定できません。", "fiber_mdi_disable": "ファイバーポートはMDI/MDIXを設定できません。", "fiber_copy_to_other_port_disable": "ファイバー ポートは構成を他のポートにコピーできません。", "port_copy_to_fiber_port_disable": "設定をファイバーポートにコピーできません。"}, "dashboard": {"page_title": "デバイスの概要", "system_info": "システムインフォメーション", "panel_status": "パネルステータス", "panel_view": "パネルビュー", "link_up_port": "リンクアップポート", "link_down_port": "リンクダウンポート", "module": "モジュール {{ index }} - {{ name }}", "product_model": "製品モデル", "firmware_version": "ファームウェアのバージョン", "system_uptime": "システム稼働時間", "ip_address_v4": "IPv4 アドレス", "ip_address_v6": "IPv6アドレス", "l3_ip_address_list": "インターフェースIPアドレスリスト", "redundant_protocol": "冗長プロトコル", "power_model": "パワーモデル", "external_storage": "外部ストレージ", "iec62439_3_protocol": "IEC 62439-3 プロトコル", "event_summary": "イベント概要", "event_summary_hint": "(過去 3 日間)", "top_5_interface_error_packet": "インターフェイス エラー パケットトップ 5", "top_5_interface_utilization": "インターフェイス使用率トップ 5", "critical_hint": "異常が発生し、システムが正常に動作しない可能性があります。", "error_hint": "異常が発生しましたが、システムの動作には影響ありません", "warning_hint": "情報には警告/注意事項が含まれていますが、機能やシステムの動作には影響ありません。", "notice_hint": "この情報は、機能が正しく動作しており、デバイスが正常に動作していることを示しています。", "tx_error": "送信エラー", "rx_error": "受信エラー", "unsupported_module_warning": "注意:サポートされていないモジュールが検出されました。正常な機能を維持するために、サポートされていないモジュールを取り外してください。"}, "igmp_snooping": {"page_title": "IGMPスヌーピング", "vlan_setting": "VLAN設定", "group_table": "グループテーブル", "forwarding_table": "転送テーブル", "query_interval": "クエリ間隔", "static_router_port": "静的ルーターポート", "dynamic_router_port": "ダイナミックルーターポート", "config_role": "構成ロール", "active_role": "アクティブロール", "startup_query_interval": "起動クエリ間隔", "startup_query_count": "起動クエリ数", "other_quer_present_interval": "その他のクエリの存在間隔", "group_address": "グループアドレス", "filter_mode": "フィルターモード", "source_address": "送信元アドレス", "querier": "<PERSON><PERSON>", "non_querier": "Non-Querier"}, "turbo_ring_v2": {"page_title": "ターボリングV2", "ring_coupling_mode": "リングカップリングモード", "static_ring_coupling": "スタティックリングカップリング", "dynamic_ring_coupling": "ダイナミックリングカップリング", "ring_id": "リングID", "master_id": "マスターID", "ring_port": "リングポート", "coupling_mode": "Ring Couplingモード", "coupling_port": "Ring Couplingポート", "primary_path": "プライマリパス", "backup_path": "バックアップパス", "ring_setting": "リングネットワーク設定", "ring_coupling_setting": "Ring Coupling 設定", "coupling_setting": "カップリンググループ {{ id }} 設定", "static_ring_coupling_setting": "スタティックリングカップリング設定", "dynamic_ring_coupling_setting": "ダイナミックリングカップリング設定", "coupling_group_id": "カップリング・グループID", "coupling_group_status": "カップリング・グループ・ステータス", "group_id": "グループ {{ id }}", "ring_status": "リングステータス", "ring_index": "リングインデックス", "total_ring_number": "合計リング番号", "healthy": "ヘルシー", "break": "ブレイク", "ring_coupling_status": "Ring Coupling ステータス", "static_ring_coupling_status": "スタティックリングカップリングステータス", "dynamic_ring_coupling_status": "ダイナミックリングカップリングステータス", "coupling_mode_primary": "Coupling Primary Path", "coupling_mode_backup": "Coupling Backup Path", "coupling_port_status": "カップリングポートのステータス", "primary_mac": "プライマリMAC", "primary_port": "プライマリ ポート", "primary_port_status": "プライマリポートのステータス", "backup_mac": "バックアップMAC", "backup_port": "バックアップポート", "backup_port_status": "バックアップポートのステータス", "ring_setting_dialog_title": "{{ portIndex }} 設定", "dip_lock_hint": "Turbo Ring V2 は DIP スイッチの設定によりロックされています。"}, "8021x": {"page_title": "IEEE 802.1X", "auth_mode": "認証モード", "local_database": "Local Database", "re_auth": "再認証", "port_control": "ポート制御", "auth_session_type": "認証セッションタイプ", "max_request": "最大リクエスト", "quiet_period": "クワイエット期間", "reauthentication": "再認証", "reauth_period": "再認証期間", "server_timeout": "サーバータイムアウト", "supp_timeout": "サップタイムアウト", "tx_period": "送信期間", "auth_port": "認証ポート", "retransmit": "再送信", "force_unauthorized": "Force Unauthorized", "force_Authorized": "Force Authorized", "reauth_port_confirm": "ポートを再認証してもよろしいですか {{ port }}?", "authorized": "承認済み", "unauthorized": "無許可", "title_reauth_port": "ポートを再認証します", "port_setting": "ポート {{ portIndex }} 設定", "account_setting": "アカウント {{ userName }} 設定", "timeout_retransmit_hint": "すべての再試行時間が Dot1x サーバーのタイムアウト値を超えることはできません。ノート：  すべての再試行時間 = タイムアウト * (再送信 + 1)。推奨値：  {{ number }}", "session_status": "セッション ステータス", "auth_table_of_port_based": "ポートベースの認証テーブル", "auth_table_of_mac_based": "MAC ベースの認証テーブル"}, "dual_homing": {"page_title": "デュアルホーミング", "multi_dual_homing": "Multiple Dual Homing", "dual_homing_table_settings": "Dual Homing テーブルの設定", "primary_port": "プライマリポート", "primary_link_status": "プライマリリンクステータス", "primary_port_status": "プライマリポートステータス", "secondary_port": "セカンダリポート", "secondary_link_status": "セカンダリリンクのステータス", "secondary_port_status": "セカンダリポートのステータス", "secondary_port_hint": "セカンダリ ポートをプライマリ ポートと同じにすることはできません。", "path_switching_mode": "パス切り替えモード", "primary_path_always_first": "Primary path always first", "maintain_current_path": "Maintain current path", "maintain_current_path_hint": "切断されるまで現在のパスを維持します", "primary_path_sensing_recovery": "Primary path sensing recovery", "path_switching_mode_hint": "ポート", "path_switching_mode_hint_2": "のLinkup Delay機能を有効にすることを推奨します", "path": "パス", "linkup_delay_warning_title": "リンクアップ遅延が無効になっています"}, "poe": {"page_title": "PoE", "power_output": "電力出力", "poe_supported": "PoE サポート", "scheduling": "スケジュール設定", "pd_failure_check": "PD 障害チェック", "auto_power_cutting": "オートパワーカット", "auto_power_cutting_hint": "自動電源切断は、電力消費がシステムの電力バジェットを超える場合、優先順位が最も低く最小のインデックス ポートの電力出力を削除します。", "system_power_budget": "システム電力バジェット", "system_power_budget_hint": "システムの電力バジェットは、外部電源 (EPS) の電源能力によって異なります。", "actual_power_budget": "実際の電力バジェット", "actual_power_budget_hint": "\"実際の電力バジェット\"と\"システム電力バジェット\"の間の低い値が\"電力バジェットの制限\"になります。", "output_mode": "出力モード", "high_power": "ハイパワー", "force": "Force", "power_allocation": "電力割り当て", "legacy_pd_detection": "レガシー PD 検出", "critical": "クリティカル", "low": "低い", "high": "高い", "rule": "ルール", "also_apply_port": "ルールが適用されるポート", "device_ip": "デバイスIP", "check_frequency": "頻度を確認", "no_response_times": "応答時間なし", "no_action": "何もしない", "restart_pd": "PDを再起動", "shutdown_pd": "PDをシャットダウン", "system_time_status": "システム時間ステータス", "system_time": "システム時刻", "local_timeZone": "ローカルタイムゾーン", "daylight_saving_time": "夏時間", "off": "Off", "on": "On", "rule_name": "ルール名", "schedule_time": "予定時刻", "repeat_execution": "繰り返し実行", "daily": "毎日", "weekly": "ウィークリー", "weekdays": "平日", "weekend": "週末", "sun": "日曜日", "mon": "月曜日", "tue": "火曜日", "wed": "水曜日", "thu": "木曜日", "fri": "金曜日", "sat": "土曜日", "sunday": "日曜日", "monday": "月曜日", "tuesday": "火曜日", "wednesday": "水曜日", "thursday": "木曜日", "friday": "金曜日", "saturday": "土曜日", "maximum_input_power": "最大入力電力", "power_budget_limit": "電力バジェット制限", "power_management_mode": "電源管理モード", "allocated_power": "Allocated Power", "consumed_power": "Consumed Power", "remaining_available_power": "利用可能な残りの電力", "classification": "分類", "over_current": "過電流", "current_ma": "電流 (mA)", "voltage_v": "電圧 (V)", "consumption_w": "消費電力(W)", "device_type": "デバイスタイプ", "not_present": "現在ではない", "legacy": "レガシーPD", "dot3af": "802.3 af", "dot3at": "802.3 at", "dot3bt": "802.3 bf", "dot3btss": "802.3 bt SS", "dot3btds": "802.3 bt DS", "na": "該当なし", "configuration_suggestion": "構成の提案", "no_suggestion": "提案はありません", "enable_poe": "PoE 電力出力を有効にする", "disable_poe": "PoE 電力出力を無効にする", "select_auto": "出力モード \"自動\" を選択", "select_high_power": "出力モード\"ハイパワー\"を選択", "select_force": "出力モード\"強制\"を選択", "enable_legacy": "従来の PD 検出を有効にする", "raise_eps_voltage": "46 VDC より大きくなるように外部電源電圧を上げます", "pd_failure_check_status": "PD障害チェックステータス", "alive": "動作している", "not_alive": "動作していない", "schedule_size_limitation": "このデバイスでは {{ size }} のスケジュールのみが許可されます。", "title_create_rule": "ルールの作成", "title_edit_rule": "ルールの編集", "allocated_power_hint": "すべてのポートの電力バジェットを計算し、割り当てられた電力の合計が電力バジェット制限を下回っていることを確認します。", "consumed_power_hint": "すべてのポートのリアルタイムの消費電力を計算します。", "change_power_mode_dialog_title": "電源管理モードを設定する", "select_allocated_power_mode": "\"割り当てられた電力\"モードを選択してもよろしいですか? その場合、\"オートパワーカット\"は無効になります。", "select_consumed_power_mode": "\"消費電力\"モードを選択してもよろしいですか? その場合、\"オートパワーカット\"が有効になります。", "change_power_cutting_dialog_title": "オートパワーカットを設定する", "disable_auto_power_cutting": "\"オートパワーカット\"を無効にしてもよろしいですか？その場合、\"電源管理モード\"は\"割り当てられた電力\"モードになります。", "enable_auto_power_cutting": "\"オートパワーカット\"を有効にしてもよろしいですか？その場合、\"電力管理モード\"は\"消費電力\"モードになります。", "avaliable_power_hint": "\"残りの利用可能な電力\"は\"最大入力電力\"から\"{{ power }}\"。"}, "turbo_chain": {"page_title": "ターボチェーン", "chain_role": "チェーンロール", "head": "ヘッド", "member": "メンバー", "tail": "テール", "head_port": "ヘッドポート", "tail_port": "テールポート", "member_port_number": "メンバーポート {{ portIndex }}", "chain_information": "チェーン情報", "head_port_status": "ヘッドポートのステータス", "member_port_status": "メンバーポートのステータス", "tail_port_status": "テールポートのステータス", "member_number_port_status": "メンバー {{ number }} ポートステータス", "initiated": "始めました"}, "mrp": {"menu_title": "MRP", "page_title": "メディア冗長プロトコル", "mrp_role": "役割", "ring_manager": "Ring Manager", "ring_client": "Ring Client", "domain_uuid": "ドメイン UUID", "domain_id": "ドメインID", "react_on_link_change": "リンクの変更に反応する", "ring_port": "リングポート {{ portIndex }}", "ring_status": "リングステータス", "mrp_ring": "MRPリング", "ring_state": "リングステート", "vid_hint": "VLAN ID は冗長ポートの設定と一致している必要があります。", "react_on_link_change_hint": "この機能はMRPリングマネージャスイッチでのみ利用可能です。有効にすると、リング マネージャーはリンクの変更に即座に反応し、MRP トポロジはより速く収束します。", "initiation": "イニシエーション", "awaiting_connection": "接続待機中", "primary_ring_port_link_up": "プライマリ リング ポートのリンクアップ", "ring_open": "リングオープン", "ring_closed": "リングクローズ", "data_exchange_idle": "データ交換アイドル", "pass_through": "通過", "data_exchange": "データ交換", "pass_through_idle": "アイドル状態を通過", "port_pvid_warning": "VLAN ID は冗長ポートの設定と一致する必要があります", "port_mode_warning": "MRPリングポートは,VLANトランクモードまたはVLANハイブリッドモードに設定しないと機能しません。", "interconnection_port_mode_warning": "MRP相互接続ポートは,VLANトランクモードまたはVLANハイブリッドモードに設定しない限り機能しません。", "interconnection": "相互接続", "interconnection_role": "相互接続の役割", "interconnection_manager": "Interconnection Manager", "interconnection_client": "Interconnection Client", "interconnection_mode": "相互接続モード", "lc_mode": "LC-Mode", "rc_mode": "RC-Mode", "interconnection_id": "相互接続ID", "interconnection_port": "相互接続ポート", "interconnection_status": "相互接続ステータス", "interconnection_state": "相互接続状態", "interconnection_open": "相互接続オープン", "interconnection_closed": "相互接続が閉じられました", "interconnection_port_idle": "相互接続ポートがアイドル状態"}, "unicast_table": {"page_title": "ユニキャストテーブル", "static_unicast": "静的ユニキャスト", "edit_static_unicast_entry": "この静的ユニキャスト エントリを編集", "add_static_unicast_entry": "静的ユニキャスト エントリの追加", "size_limitation": "このデバイスの静的ユニキャスト エントリの最大数は {{ size }}。"}, "static_forwarding_table": {"page_title": "静的転送テーブル", "menu_title": "静的転送", "size_limitation": "このデバイスの静的ユニキャスト エントリの最大数は {{ size }}。", "title_add_static_forwarding": "静的転送エントリの作成", "title_edit_static_forwarding": "この静的転送エントリを編集", "title_delete_static_forwarding": "この静的転送エントリを削除します"}, "multicast_table": {"page_title": "静的マルチキャスト テーブル", "static_multicast": "静的マルチキャスト", "delete_on_reset": "リセット時に削除", "delete_on_timeout": "タイムアウト時に削除", "add_static_multicast_entry": "静的マルチキャスト エントリの追加", "edit_static_multicast_entry": "この静的マルチキャスト エントリを編集", "size_limitation": "このデバイスの静的マルチキャスト エントリの最大数は {{ size }}。"}, "gmrp": {"page_title": "GMRP", "group_restrict": "グループ制限"}, "time_sync": {"page_title": "時刻同期", "page_title_abbr": "時刻同期", "mds_m2_insert_warning": "時刻同期を使用するには,互換性のあるモジュールをM2スロットに挿入する必要があります。", "profile": "プロフィール", "8021as": "IEEE 802.1AS-2011 プロファイル", "8021as_abbr": "IEEE 802.1AS-2011", "1588default": "IEEE 1588 デフォルト 2008 プロファイル", "1588default_abbr": "IEEE 1588 デフォルト - 2008", "iec61850": "IEC 61850-9-3-2016 プロファイル", "iec61850_abbr": "IEC 61850-9-3-2016", "c37238": "IEEE C37.238-2017 プロファイル", "c37238_abbr": "IEEE C37.238-2017", "priority_number": "優先順位 {{ number }}", "clock_type": "時計の種類", "clock_type_bc": "Boundary Clock", "clock_type_tc": "Transparent Clock", "delay_mechanism": "遅延メカニズム", "e2e": "End-to-End", "p2p": "Peer-to-Peer", "transport_mode": "トランスポートモード", "8023ehternet": "IEEE 802.3 Ethernet", "udp_ipv4": "UDP IPv4", "udp_ipv6": "UDP IPv6", "domain_number": "ドメイン番号", "clock_mode": "時計モード", "two_step": "Two-step", "one_step": "One-step", "accuracy_alert": "精度アラート", "bmca": "BMCA", "bmca_hint": "Best Master Clock Algorithm (BMCA) は、トランスペアレント クロックの使用時にループ パスのループを防止します。この機能を有効にすることをお勧めします。", "max_steps_removed": "削除された最大ステップ数", "grandmaster_id": "グランドマスターID", "announce_interval": "アナウンス間隔", "announce_receipt_timeout": "受信タイムアウトのアナウンス", "sync_interval": "同期間隔", "sync_receipt_timeout": "同期受信タイムアウト", "delay_req_interval": "遅延要求間隔", "pdelay_req_interval": "P遅延要求間隔", "neighbor_rate_ratio": "近傍率比率", "neighbor_prop_delay": "近隣伝播遅延", "path_delay": "パス遅延", "neighbor_prop_delay_thresh": "近隣伝播遅延しきい値", "synchronization_status": "同期ステータス", "transport_type": "トランスポートタイプ", "current_data_set": "現在のデータセット", "parent_data_set": "親データセット", "locked": "ロックされています", "unlocked": "ロック解除済み", "freerun": "フリーラン", "syncing": "同期中", "browser_time": "ブラウザ時間", "ptp_clock_time": "PTP クロック タイム (TAI)", "ptp_slave_port": "PTP スレーブ ポート", "offset_from_master": "マスターからのオフセット", "mean_path_delay": "平均パス遅延", "steps_removed": "ステップが削除されました", "parent_identity": "親のアイデンティティ", "grandmaster_identity": "グランドマスターのアイデンティティ", "cumulative_rate_ratio": "累積レート率", "grandmaster_priority_number": "グランドマスタープライオリティ {{ number }}", "grandmaster_clock_class": "グランドマスタークロッククラス", "grandmaster_clock_accuracy": "グランドマスタークロックの精度", "8021as_capable": "802.1AS 対応", "initializing": "初期化中...", "res_ptp_initializing_done": "PTP サービスの準備ができました。", "faulty": "障害", "pre_master": "プレマスター", "uncalibrated": "未校正", "sync_transmit": "同期送信中", "sync_receive": "同期受信中", "edit_port_profile_setting": "ポートを編集する {{ portIndex }} IEEE 1588v2 デフォルトのプロファイル設定"}, "stream_adapter": {"page_title": "優先順位管理", "pcp": "優先コードポイント (PCP)", "ingress_table_limit_hint": "各ポートは最大 10 個のエントリをサポートします。", "port_size_limitation": "このポートのエントリの最大数 (10) に達しました。", "hexadecimal": "16 進数", "egress_untag": "イグレス　アンタグ", "ingress": "イングレス", "egress": "イグレス", "per_stream_priority_title": "ストリームごとの優先順位", "port_default_priority_title": "ポートのデフォルトの優先順位", "per_stream_priority_hint": "タグなしストリームは、ユーザー定義のルールに一致する場合、ストリームごとの優先順位に基づいて処理されます。ストリームがどのルールにも一致しない場合、ストリームはデフォルトのポート優先度に基づいて処理されます。", "add_stream_adapter_entry": "ストリームごとの優先順位エントリを追加", "edit_stream_adapter_entry": "このストリームごとの優先順位エントリを編集", "edit_port_def_priority": "ポート {{ portIndex }}のデフォルトの優先順位を編集する"}, "static_route": {"page_title": "静的ルーティング", "size_limitation": "このデバイスのスタティック ルートの最大数は {{ size }}。", "next_hop_IP": "ネクストホップIP", "next_hop_interface": "ネクストホップインターフェイス", "delete_static_route_desc": "選択したルートを削除してもよろしいですか?", "next_hop_type": "ネクストホップタイプ*", "next_hop_type_hint": "以前に作成した VLAN インターフェイスを選択するか、このフィールドを空白のままにすることができます。", "next_hop_two_field_invalid": "ネクストホップのIPまたはインターフェイスを指定する必要があります", "add_static_route": "静的ルートの作成", "edit_static_route": "この静的ルートを編集", "delete_static_route": "この静的ルートを削除"}, "routing_table": {"page_title": "ルーティングテーブル", "static": "静的", "next_hop": "ネクストホップ", "ad_metric": "AD/メトリック"}, "online_accounts": {"page_title": "オンラインアカウント", "idle_time": "アイドルタイム", "remove_account_dialog_title": "このオンライン アカウントを削除", "remove_account_dialog_desc": "このオンライン アカウントを削除してもよろしいですか?"}, "8021qbv": {"page_title": "Time-aware <PERSON><PERSON><PERSON>", "cycle_time": "サイクルタイム", "start_time_hint": "Time-aware Shaper は現在の PTP 時間に基づいています。開始時刻を決定することも、関数の開始時刻を決定する今の時刻を設定することもできます。", "config_change_time": "構成変更時間", "default_setting": "デフォルトの設定", "gate_control_list": "ゲート制御リスト", "totla_slot": "総スロット数", "interval": "間隔", "selected_queue_summary": "選択されたキューの概要", "interval_hint": "ゲート間隔は、いつゲートが開き、どれくらいの時間開いたままになるかを示します。イーサネット パケットの最小間隔は次のとおりです。", "interval_hint_1G": "1G ポート: 1μs、", "interval_hint_100M": "100M ポート: 10μs、", "interval_hint_10M": "10M ポート: 100μs", "port_status": "ポート {{ portIndex }} スターテス", "select_port": "ポートを選択してください"}, "ospf": {"page_title": "OSPF", "ospf_settings": "OSPF設定", "ospf_status": "OSPF ステータス", "area": "エリア", "neighbor": "近隣", "aggregation": "アグリゲーション", "virtual_link": "仮想リンク", "router_id": "ルーターID", "current_router_id": "現在のルーターID", "current_router_id_hint": "ルーター ID が 0.0.0.0 に設定されている場合、最も小さいインターフェイス IP アドレスがルーター ID として自動的に割り当てられます。", "compatible_rfc_1583": "RFC 1583 の互換性", "spf_hold_time": "SPFホールドタイム", "redistribute": "再配布", "metric": "メトリック", "ospfRRDStatic": "静的", "ospfRRDConnected": "接続されました", "ospfRRDRip": "RIP", "area_size_limitation": "このデバイスの最大エリア数は {{ size }}。", "area_id": "エリアID", "area_type": "エリアタイプ", "normal": "Normal", "stub": "<PERSON><PERSON>", "nssa": "NSSA", "summary": "Summary", "no_summary": "No Summary", "delete_area_desc": "選択した領域を削除してもよろしいですか?", "dead_interval": "デッドインターバル", "cost": "料金", "network_type": "ネットワークタイプ", "passive_interface": "パッシブインターフェイス", "neighbor_ip_address": "近隣IPアドレス", "summary_hint": "エリア タイプが標準に設定されている場合、概要は利用できません。", "broadcast": "Broadcast", "non_broadcast": "Non-broadcast", "point_to_point": "Point-to-point", "point_to_multipoint": "Point-to-multipoint", "nbr_ip_address": "近隣IPアドレス", "nbr_size_limitation": "このデバイスのネイバーの最大数は次のとおりです {{ size }}。", "delete_nbr_desc": "選択したネイバーを削除してもよろしいですか?", "lsa_type": "LSA タイプ", "type_7": "Type 7", "aggregation_size_limitation": "このデバイスの最大アグリゲーション数は {{ size }}。", "delete_aggregation_desc": "選択したアグリゲーションを削除してもよろしいですか?", "vLink_size_limitation": "このデバイスの仮想リンクの最大数は {{ size }}。", "delete_vLink_desc": "選択した仮想リンクを削除してもよろしいですか?", "loopback": "ループバック", "waiting": "待ち", "dr": "DR", "bdr": "BDR", "dr_other": "DR その他", "dr_router_id": "DRルーターID", "bdr_router_id": "BDRルーターID", "neighbor_id": "近隣ID", "neighbor_state": "近隣状況", "dead_time": "デッドタイム", "accempt": "試み", "two_way": "2ウェイ", "exstart": "エクススタート", "exange": "交換", "loading": "読み込み中", "full": "フル", "database": "データベース", "link_id": "リンクID", "adv_router": "ADVルーター", "age": "Age", "events": "イベント", "ls_retrans_queue_len": "LSA 再送信キューの長さ", "hello_suppressed": "Hello Suppressed", "router": "ルーター", "asbr_summary": "ASBR 概要", "as_external": "AS エクスターナル", "group_member": "グループメンバー", "nssa_external": "NSSA エクスターナル", "title_edit_ospf_redistribute": "再配布 {{ protocol }}を編集", "title_create_ospf_area": "エリアを作成", "title_edit_ospf_area": "このエリアを編集", "title_delete_ospf_area": "このエリアを削除", "title_create_ospf_nbr": "ネイバーを作成", "title_edit_ospf_nbr": "このネイバーを編集", "title_delete_ospf_nbr": "このネイバーを削除", "title_create_ospf_aggregation": "アグリゲーションを作成", "title_edit_ospf_aggregation": "このアグリゲーションを編集", "title_delete_ospf_aggregation": "このアグリゲーションを削除", "title_create_ospf_vlink": "仮想リンクの作成", "title_edit_ospf_vlink": "この仮想リンクを編集", "title_delete_ospf_vlink": "この仮想リンクを削除"}, "vrrp": {"page_title": "VRRP", "v2": "V2", "v3": "V3", "virtual_router_enable": "仮想ルーター", "vrid": "VRID", "decrement": "デクリメント", "primary_ip": "仮想ルーターのIPアドレス", "adv_int": "アドバタイズメント間隔", "preempt_mode": "プリエンプトモード", "preempt_delay": "プリエンプト遅延", "accept_mode": "受け入れモード", "auth_key": "認証キー", "size_limitation": "このデバイスの VRRP エントリの最大数は {{ size }}。", "delete_vrrp_desc": "選択した仮想ルーターを削除してもよろしいですか?", "master_address": "マスターアドレス", "master_adv_int": "マスター アドバタイズメント間隔 (ミリ秒)", "master_down_int": "マスターダウン間隔(ミリ秒)", "title_add_vrrp": "仮想ルーターの作成", "title_edit_vrrp": "この仮想ルーターを編集", "title_delete_vrrp": "この仮想ルーターを削除", "require_decrement_less_than_priority": "この値は優先度の値より小さくなければなりません"}, "dns": {"page_title": "DNS設定", "primary_dns_server": "プライマリDNSサーバー", "secondary_dns_server": "セカンダリDNSサーバー", "dns_server_number": "DNSサーバーのIPアドレス{{ number }}", "dns_server": "DNSサーバー", "dns_reverse_lookup": "DNS 逆引き", "zone_table": "ゾーンテーブル", "dns_table_for_name": "DNS テーブル {{ zoneName }}", "dns_server_summary": "DNS サーバーの概要", "fqdn": "FQDN", "fqdn_hint": "FQDN (完全修飾ドメイン名) は \"ホスト名\".\"ドメイン名\"", "dns_forwarding": "DNSフォワーディング", "dns_forwarding_hint": "DNSフォワーディングを有効にするには,アクティブなDNSサーバーが必要です。", "default_forwarder_ip": "デフォルトのフォワーダIPアドレス", "default_forwarder_ip_hint": "既定のフォワーダーが指定されている場合,フォワーダー テーブルにリストされていないゾーンの DNS クエリは,既定のフォワーダーに転送されます。", "forwarder_ip": "フォワーダーIPアドレス", "forwarders_table": "フォワーダテーブル", "zone_hint": "'.' は任意のゾーンの転送に使用できます。", "zone_size_limitation": "ゾーンエントリの最大数は {{ size }}。", "dns_size_limitation": "DNS エントリの最大数は {{ size }}。", "title_create_zone": "ゾーンを作成する", "title_edit_zone": " {{ zoneName }} 設定を編集", "title_delete_zone": "ゾーンの削除", "delete_zone_desc": "選択したゾーンを削除してもよろしいですか?", "title_create_dns": "リソースレコードの作成 {{ zoneName }}", "title_edit_dns": "リソースレコードの編集 {{ zoneName }}", "title_delete_dns": "リソース レコードの削除", "delete_dns_desc": "選択したリソース レコードを削除してもよろしいですか?", "title_create_forwarding": "DNSフォワーディングエントリを作成する", "title_edit_forwarding": "DNSフォワーディングエントリの編集", "title_delete_forwarding": "DNS転送エントリの削除", "delete_forwarding_desc": "選択したDNSフォワーディングエントリを削除してもよろしいですか?", "duplicate_hostname": "同じホスト名がすでに存在しています", "duplicate_domain_name": "同じドメイン名がすでに存在しています", "duplicate_zone": "このゾーンはすでに存在します"}, "acl": {"page_title": "アクセス制御リスト", "access_list_type": "アクセスリストの種類", "access_list_type_hint": "同じインデックスの場合、MAC アドレスの方が IP アドレスよりも優先されます。", "access_list_index_hint": "インデックスが低いほど優先度が高くなります。", "ip_based": "IP-based", "mac_based": "MAC-based", "acl_size_limitation": "ACL エントリの最大数は {{ size }}。", "acl_ip_based_size_limitation": "IP ベースの ACL ルールの最大数は {{ size }}。", "acl_mac_based_size_limitation": "MAC ベースの ACL ルールの最大数は {{ size }}。", "acl_rule_size_limitation": "ACL ルールの最大数は {{ size }}。", "active_interface_type": "アクティブインターフェースタイプ", "vlan_based": "VLANベース", "port_based": "ポートベース", "active_ingress_vlan": "アクティブなイングレス VLAN", "active_egress_vlan": "アクティブな出力 VLAN", "active_ingress_port": "アクティブなイングレスポート", "active_egress_port": "アクティブな出力ポート", "ingress_setting_hint": "アクティブ VLAN とルール VLAN は同じである必要があります。", "egress_setting_hint": "リダイレクト アクションを含むルールは出力インターフェイスには適用できません。", "ingress_setting_vlan_hint": "アクティブ VLAN とルール VLAN は同じである必要があります。\n注釈アクションを含むルールは入力インターフェイスには適用できません。", "egress_setting_vlan_hint": "アクティブ VLAN とルール VLAN は同じである必要があります。\nリダイレクト アクションを含むルールは出力インターフェイスには適用できません。", "rule_type": "ルールの種類", "index_priority_hint": "インデックスが低いルールの優先順位が高くなります。", "acl_rule": "ACLルール", "rule": "ルール", "rule_index": "ルールインデックス {{ruleIndex}}", "permit": "Permit", "deny": "<PERSON><PERSON>", "ethertype_value": "EtherType 値", "goose": "GOOSE", "smv": "SMV", "protocol_number": "プロトコル番号", "user_defined": "User-defined", "source": "ソース", "source_port": "ソースポート", "source_ip_addr": "ソースIPアドレス", "source_ip_mask": "ソースIPマスク", "source_mac_addr": "送信元MACアドレス", "source_mac_mask": "ソース MAC マスク", "destination_port": "宛先ポート", "destination_ip_addr": "宛先IPアドレス", "destination_ip_mask": "宛先IPマスク", "destination_mac_addr": "宛先MACアドレス", "destination_mac_mask": "宛先MACマスク", "cos_remark": "CoS リマーク", "dscp_remark": "DSCP リマーク", "optional_parameter": "オプションのパラメータ", "log": "ログ", "logging": "ロギング", "logging_enable": "ロギングを有効にする", "src_port": "Src. Port", "dst_port": "Dst. Port", "icmp_type": "ICMP タイプ", "icmp_code": "ICMP コード", "igmp_type": "IGMP タイプ", "redirect": "リダイレクト", "redirect_mirror": "リダイレクト/ミラー", "redirect_enable": "リダイレクト", "redirect_port": "リダイレクトポート", "redirect_port_name": "ポートにリダイレクト {{ portName }}", "mirror": "ミラー", "session_id": "セッション {{id}}", "mirror_disable_hint": "ポートでミラーリング機能が無効になっている場合、ミラーリングアクションは使用できません。", "session_disable_hint": "\"ミラー\"アクションは、無効になっているポート ミラー セッションでは有効になりません。", "mirror_sesstion": "Mirror to session {{ sesstionId }}", "remark_cos": "Remark CoS to {{ cos }}", "remark_dscp": "Remark DSCP to {{ dscp }}", "acl_table_of_name": "ACL表 {{ aclName }}", "delete_acl_list_desc": "選択したアクセス制御リストを削除してもよろしいですか?", "delete_acl_rule_desc": "選択したルールを削除してもよろしいですか?", "any_hint": "値が入力されていない場合は、Any に設定されているものとみなされます。", "log_interval": "ロギング間隔", "log_threshold": "ロギングしきい値", "acl_summary": "ACLの概要", "number_of_activate_acl": "アクティブ化された ACL の数 (最大 16)", "activate_direct": "方向", "ingress": "イングレス", "egress": "エグレス", "both": "両方", "activated": "有効化されました", "inactivated": "無効化されました", "hit_count": "ヒット数", "counter": "カウンター", "view_list": "リストを表示", "view_by_acl": "ACL ごとに表示", "view_by_port": "ポートごとに表示", "view_by_vlan": "VLAN ごとに表示", "acl_table_of_type": "ACL表 {{typeIndex}}", "no_activated_acl_port": "このポートではアクティブ化された ACL がありません。", "no_activated_acl_vlan": "この VLAN ではアクティブ化された ACL がありません。", "status_hint": "インデックスが低いルールの優先順位が高くなります。デバイスは、最も低いインデックスから開始して、番号順にすべてのルールとのパケットの照合を開始します。パケットがルールに一致する場合、対応するルールが適用されます。", "title_create_access_list": "アクセスリストを作成する", "title_edit_access_list": " {{ typeIndex }} アクセスリスト設定を編集する", "title_delete_acl_list": "アクセスリストを削除する", "title_create_acl_rule": "Create Rule Index {{ ruleIndex }} for {{ typeIndex }}", "title_edit_acl_rule": "Edit the Rule Index {{ ruleIndex }} of {{ typeIndex }}", "title_delete_acl_rule": "ルールの削除", "title_clear_acl_counter": "カウンターをクリア", "desc_clear_all_acl_counter_desc": "すべてのカウンターをリセットしてもよろしいですか?", "desc_clear_single_acl_counter_desc": "カウンターをリセットしてもよろしいですか？ {{ typeIndex }} ACL?", "blacklist_udp_port_dhcp_server": "DHCP サーバーは許可されていません", "blacklist_udp_port_dhcp_client": "DHCP クライアントは許可されていません", "blacklist_udp_port_moxa_command": "Moxaサービスは許可されていません", "blacklist_ether_type_eth_confg_test_protocol": "イーサネット構成テスト プロトコルは許可されていません", "blacklist_ether_type_lldp": "LLDP は許可されていません", "blacklist_ether_type_eapol": "EAPOL は許可されていません", "blacklist_ether_type_lacp": "LACP は許可されていません", "blacklist_ether_type_llc_jumbo_frame": "LLC ジャンボ フレームは許可されません", "blacklist_ether_type_arp": "ARP は許可されていません", "blacklist_ether_type_mrp": "MRP は許可されていません", "blacklist_ether_type_profinet": "PROFINET は許可されていません", "blacklist_ether_type_ptp": "PTP は許可されていません", "blacklist_ether_type_goose": "グースは許可されていません", "blacklist_ether_type_smv": "SMV は許可されていません", "blacklist_mac_ieee_reserved_multicast": "IEEE 予約済みマルチキャスト MAC アドレスは許可されていません", "blacklist_mac_ip_multicast": "IP マルチキャスト MAC アドレスは許可されていません", "blacklist_mac_broadcast": "ブロードキャスト MAC アドレスは許可されていません", "blacklist_mac_l2_multicast": "L2 マルチキャスト MAC アドレスは許可されていません", "blacklist_mac_device": "デバイスの MAC アドレスは許可されていません", "blacklist_dest_ip_multicast": "マルチキャスト IP アドレスは許可されていません", "overwrite_vlan_dialog_title": "ルール VLAN をアクティブ VLAN で上書き", "overwrite_vlan_dialog_content": "アクティブ VLAN とルール VLAN は同じである必要があります。アクティブ VLAN でルール VLAN を上書きしてもよろしいですか?"}, "stream_id": {"page_title": "ストリーム識別", "title_create_stream_id": "ストリームの作成", "title_edit_stream_id": "このストリームを編集", "title_delete_stream_id": "ストリームを削除", "delete_stream_id_desc": "選択したストリームを削除してもよろしいですか?"}, "8021cb": {"page_title": "信頼性のためのフレーム複製と排除 (FRER)", "frer": "FRER", "split": "スプリット", "forward": "前方", "merge": "マージ", "stream_vid_mac": "ストリーム(VLAN/MACアドレス)", "input_port": "入力ポート", "input_ports": "入力ポート", "output_port_index": "出力ポート {{ portIndex }}", "output_port": "出力ポート", "output_ports": "出力ポート", "ingress_stream": "イングレスストリーム", "egress_stream": "イグレスストリーム", "vlan_overwrite": "VLAN 上書き", "mac_address_overwrite": "MAC アドレスの上書き", "priority_overwrite": "プライオリティ 上書き", "overwrite": "上書き", "disable_port_input_hint": "これは選択された入力ポートです。", "disable_port_vlan_hint": "このポートは対応する VLAN のメンバーではありません。", "disable_vid_overwrite_hint": "出力ポートは対応する VLAN のメンバーではありません。", "disable_exist_stream_hint": "このストリームには FRER エントリがすでに存在します。", "disable_select_stream_hint": "ストリームはすでに選択されています。", "to_end_device": "端末へ", "ingress_size_limitation": "最大入力ストリームは {{ size }}。", "title_create_frer_entry": "FRER エントリの作成", "title_edit_frer_entry": "この FRER エントリを編集", "title_delete_frer_entry": "この FRER エントリを削除", "delete_frer_entry_desc": "選択した FRER エントリを削除してもよろしいですか?"}, "loop_protection": {"page_title": "ネットワークループ保護", "detect_interval": "検出間隔", "loop_status": "ループステータス", "peer_port": "ピアポート", "looping": "ループ"}, "binding_database": {"page_title": "バインディング データベース", "binding_settings": "バインド設定", "binding_status": "バインドステータス", "binding_status_hint": "動的バインディングは DHCP スヌーピングから学習しています。", "binding_status_hint_2": "静的エントリの VLAN ID と MAC アドレスの組み合わせが既に存在する場合、バインディング ステータスは更新されません。", "title_create_entry": "バインディング データベースの静的エントリの作成", "title_edit_entry": "このバインディング データベースの静的エントリを編集", "duplicate_of_dynamic_entry": "VLAN ID と MAC アドレスの組み合わせはすでに存在します。この新しいエントリは、最初の動的エントリを上書きします。", "size_limitation": "バインディング ステータス エントリの最大数は {{ size }}。", "binding_table_max": "マックス。 {{ size }} バインディング ステータス テーブルの", "dai": "DAI", "ipsg": "IPSG", "ipsg_dai": "IPSG、DAI"}, "dhcp_snooping": {"page_title": "DHCPスヌーピング", "port_is_ip_sg_enable": "このポートは IP ソース ガードに対して有効になっています。IP ソース ガードは、信頼できないポートでのみ有効にできます。", "port_is_dai_enable": "このポートはダイナミック ARP 検査に対して有効になっています。ダイナミック ARP インスペクションは、信頼されていないポートでのみ有効にできます。", "port_is_ip_sg_and_dai_enable": "このポートはダイナミック ARP インスペクションと IP ソース ガードに対して有効になっています。ダイナミック ARP インスペクションと IP ソース ガードは、信頼できないポートでのみ有効にできます。"}, "ip_source_guard": {"page_title": "IPソースガード", "port_is_trusted": "このポートは DHCP スヌーピングの信頼できるポートです。IP ソース ガードに対して信頼できないポートのみを有効にできます。", "port_is_la_member": "このポートはポート チャネルのメンバーです。IP ソース ガードをメンバー ポートで有効にすることはできません。", "binding_status_single_empty": "ポートのバインドステータス {{ port }} 空です。", "binding_status_multiple_empty": "ポートのバインドステータス {{ port }} 空です。", "binding_setting_hint": "DHCP スヌーピングを有効にして動的バインディングを取得するか、[バインディング データベース] -> [バインディング設定] でデータを構成する必要があります。"}, "mms": {"page_title": "MMS", "ied_name": "IED 名", "cid_file_settings": "CID ファイル設定", "report_control_block": "レポート制御ブロック", "data_change": "データ変更", "data_update": "データ更新", "quality_change": "品質の変化", "integrity": "Integrity", "buffer_time": "バッファタイム", "integrity_period": "Integrity Period", "t_profile_cert_info": "T-プロファイル証明書情報", "a_profile_cert_info": "A-プロファイル証明書情報", "ca_name": "CA 名", "t_profile_security": "T-プロファイルセキュリティ", "a_profile_security": "Aプロファイルセキュリティ", "import_client_ca": "クライアント CA のインポート", "import_client_cert": "クライアント証明書のインポート", "title_edit_name": "編集 {{ name }}", "title_mms_enable_warning": "MMS プロトコルを有効にする", "mms_enable_warning_desc": "非セキュア プロトコル (MMS) を有効にしてもよろしいですか?"}, "password_policy": {"page_title": "パスワードポリシー", "minimum_length": "パスワードの最小長", "policy_numbers": "少なくとも 1 つの数字 (0 ～ 9) を含める必要があります", "policy_uppercase": "少なくとも 1 つの大文字 (AZ) が含まれている必要があります", "policy_lowercase": "少なくとも 1 つの小文字 (az) が含まれている必要があります", "policy_symbols": "少なくとも 1 つの特殊文字 ({}[]()|:;~!@#%^*-_+=,.) を含める必要があります", "max_life_time": "パスワードの最大有効期間", "password_complexity_strengh_check": "パスワードの複雑さの強度チェック"}, "system_info": {"page_title": "情報設定", "system_name": "装置名", "contact_information": "連絡先", "sync_to_chassis_id_hint": "LLDP シャーシ ID サブタイプが「local」に設定されている場合、デバイス名を変更すると、LLDP シャーシ ID も変更されます。"}, "login_authentication": {"page_title": "ログイン認証", "authentication_protocol": "認証プロトコル", "radius_local": "RADIUS, Local", "tacacs_local": "TACACS+, Local"}, "login_policy": {"page_title": "ログインポリシー", "login_message": "ログインメッセージ", "auth_fail_message": "ログイン認証失敗メッセージ", "failure_lockout": "ログイン失敗ロック", "retry_failure_threshold": "再試行失敗しきい値", "lockouttime": "ロックアウト期間", "auto_logout_setting": "自動ログアウト後", "auto_logout_warring_title": "自動ログアウトを無効にする", "auto_logout_setting_alert": "自動ログアウト値が 0 に設定されている場合、セッションはタイムアウトしません。ブラウザを閉じる前にログアウトしてください。"}, "ip_settings": {"page_title": "IP 構成", "ip_settings": "IP設定", "ip_status": "IPステータス", "get_ip_from": "IP の取得元", "dns_server": "DNS サーバーの IP アドレス", "ipv6": "IPv6", "ipv6_global_unicast_address_prefix": "IPv6 グローバル ユニキャスト アドレス プレフィックス", "ipv6_dns_server_number": "IPv6 DNSサーバー {{ number }}", "ipv6_dns_server": "IPv6 DNS サーバー", "ipv6_global_unicast_address": "IPv6 グローバル ユニキャスト アドレス", "ipv6_link_local_address": "IPv6 リンクローカル アドレス", "profinet_dcp": "PROFINET DCP", "dhcp_bootfile": "DHCP ブートファイル", "dhcp_bootfile_hint": "有効にすると、システムはオプション67で説明されているブートファイルの設定を、オプション66で説明されているファイルサーバーから自動的にダウンロードして復元します。", "dhcp_client": "DHCPクライアント識別子", "dhcp_client_hint": "有効にすると、システムはクライアント ID を含むオプション 61 タグを含む DHCP クライアント メッセージを送信します。 DHCP サーバーは、クライアント ID 値に関連付けられた IP アドレスを割り当てます（利用可能な場合）。", "dhcp_client_type": "DHCP クライアント識別子のタイプ", "dhcp_client_value": "DHCP クライアント識別子の値"}, "management_interface": {"page_title": "管理インターフェイス", "user_interface": "ユーザーインターフェース", "interface": "インターフェース", "enable_http": "HTTP", "http_port": "HTTP - TCP ポート", "enable_https": "HTTPS", "https_port": "HTTPS - TCP ポート", "enable_telnet": "テルネット", "telnet_port": "Telnet - TCP ポート", "ssh_port": "SSH - TCP ポート", "enable_snmp_V1V2c": "SNMP バージョン V1、V2c", "snmp_protocol": "SNMP - トランスポート層プロトコル", "udp": "UDP", "tcp": "TCP", "snmp_udp_port": "SNMP - UDP ポート", "snmp_tcp_port": "SNMP - TCP ポート", "enable_moxa_service": "お灸サービス", "moxa_tcp_port": "Moxa サービス (暗号化) - TCP ポート", "moxa_udp_port": "Moxa サービス (暗号化) - UDP ポート", "max_session_http": "HTTP+HTTPS のログイン セッションの最大数", "max_session_terminal": "Telnet+SSH の最大ログイン セッション数", "enable_nonsecure_interface_warning_title": "有効にしてください {{ interfaceType }} インターフェース", "enable_nonsecure_interface_warning": "非セキュア インターフェイスを有効にしてもよろしいですか ({{ interfaceType }}）？"}, "hareward_interface": {"page_title": "ハードウェアインターフェイス", "dip_switch": "ディップスイッチ", "usb_function": "USBインターフェース", "micro_sd_function": "MicroSDインターフェース"}, "account_management": {"page_title": "ユーザーアカウント", "admin": "Admin", "supervisor": "Supervisor", "user": "User", "new_password": "新しいパスワード", "title_edit_account": "このアカウントを編集", "title_add_account": "新しいアカウントを作成", "title_edit_account_password": "アカウントのパスワードを編集", "new_pwd_not_match": "パスワードが一致しません。", "tech_account_add_error": "アカウント \"moxasupport\" は Moxa テクニカル サポート用に予約されているため作成できません。", "tech_account_remove_error": "アカウント \"moxasupport\" は Moxa テクニカル サポート用に予約されているため、編集または削除できません。", "account_name_taken": "このアカウントのユーザー名はすでに使用されています", "size_limitation": "このデバイスのユーザー アカウントの最大数は {{ size }}。"}, "time": {"page_title": "システム時刻", "sntp": "SNTP", "ntp": "NTP", "time_zone": "タイムゾーン", "current_time": "現在の時刻", "daylight_saving": "夏時間調整", "end_date": "終了日", "offset": "オフセット", "ntp_authentication": "NTP認証", "query_interval": "クエリ間隔", "ptp": "PTP", "start": "始める", "end": "終わり", "date": "日にち", "month": "月", "week": "週", "day": "日", "hour": "時間", "minute": "分", "jan": "一月", "feb": "二月", "mar": "三月", "apr": "四月", "may": "五月", "jun": "六月", "jul": "七月", "aug": "八月", "sep": "九月", "oct": "十月", "nov": "十一月", "dec": "十二月", "1st": "1番目", "2nd": "2番目", "3rd": "3番目", "4th": "4番目", "last": "最後", "sun": "日曜日", "mon": "月曜日", "tue": "火曜日", "wed": "水曜日", "thu": "木曜日", "fri": "金曜日", "sat": "土曜日", "time_server_number": "タイムサーバー {{ number }}", "time_server_1": "最初のタイムサーバー: IPアドレス/ドメイン名", "time_server_2": "2番目のタイムサーバー: IPアドレス/ドメイン名", "clock_source": "クロックソース", "key_string": "キー文字列", "delete_entry_confirm_desc": "選択したキー文字列を削除してもよろしいですか?", "size_limitation": "このデバイスの NTP 認証キーの最大数は {{ size }}。"}, "ntp_server": {"page_title": "NTPサーバー"}, "ssh_ssl": {"page_title": "SSH と SSL", "ssh": "SSH", "ssl": "SSL", "regen_ssh_key": "SSH キーを再生成", "regen_ssl_cert": "SSL証明書を再生成", "export_ssl_cert": "SSL証明書のエクスポート", "import_ssl_cert": "証明書のインポート", "ssl_info": "証明書情報", "ca_name": "CA 名", "title_export_ssl_certificate": "SSL証明書のエクスポート"}, "dhcp": {"page_title": "DHCPサーバー", "dhcp": "DHCP", "ntp_server": "NTP サーバーの IP アドレス", "dhcp_pool_settings": "DHCP サーバー プール設定", "static_ip_assignment_table": "静的IP割り当てテーブル", "start_ip": "開始IPアドレス", "end_ip": "終了IPアドレス", "lease_time": "リース時間", "hostname": "ホスト名", "log_server": "ログサーバーのIPアドレス", "gateway": "ゲートウェイ", "matching_rule": "一致ルール", "client_id_type": "クライアント識別子の種類", "client_id_value": "クライアント識別子の値", "circuit_id_type": "オプション82 回路IDタイプ", "circuit_id_value": "オプション82 回線ID値", "remote_id_type": "オプション 82 リモート ID タイプ", "remote_id_value": "オプション 82 リモート ID 値", "hostname_hint": "ホスト名は DHCP クライアントの名前を表し、DHCP オファー パケットのオプション 12 タグにエンコードされます。", "time_left": "残り時間", "dhcp_ip_mac": "DHCP/MACベースのIP割り当て", "dhcp_static_ip": "DHCP/静的IP割り当て", "lease_table": "リーステーブル", "ip_mac_binding": "MAC ベースの IP 割り当て", "ip_port_binding": "ポートベースの IP 割り当て", "classless_static_route_table": "クラスレス静的ルート テーブル", "delete_dhcp_entry_confirm_desc": "この DHCP サーバー プールを削除してもよろしいですか?", "delete_static_ip_entry_confirm_desc": "選択した静的IPエントリを削除してもよろしいですか?", "delete_ip_port_entry_confirm_desc": "選択したポートベースの IP 割り当てを削除してもよろしいですか?", "delete_ip_mac_entry_confirm_desc": "選択した MAC ベースの IP 割り当てを削除してもよろしいですか?", "dhcp_size_limitation": "このデバイスの DHCP サーバー プールの最大数は {{ size }}。", "invalid_dhcp_pool_range": "無効：  スイッチの管理 IP アドレスは、IP サブネットの範囲内にある必要があります。", "delete_static_route_entry_confirm_desc": "選択したルートを削除してもよろしいですか?", "default_gateway_setting_hint": "クラスレス静的ルートのデフォルト ゲートウェイは、ポートベースの IP 割り当て構成セクションで構成されたデフォルト ゲートウェイ アドレスを使用します。"}, "dhcp_relay": {"page_title": "DHCP リレー エージェント", "option82": "オプション 82", "server1": "1番目のサーバーのIPアドレス", "server2": "2番目のサーバーのIPアドレス", "server3": "3番目のサーバーのIPアドレス", "server4": "4番目のサーバーIPアドレス", "remote_id_type": "リモート ID タイプ", "remote_id_value": "リモート ID 値", "remote_id_display": "リモートIDディスプレイ", "client_id": "Client ID", "relay": "リレー"}, "ping": {"page_title": "<PERSON>", "ping_result": "Ping {{ targetHost }} 結果"}, "email_settings": {"page_title": "メール設定", "tls_enable": "TLS", "sender_address": "送信者アドレス", "recipient_1": "1番目の受信者のメールアドレス", "recipient_2": "2番目の受信者のメールアドレス", "recipient_3": "3番目の受信者のメールアドレス", "recipient_4": "4番目の受信者のメールアドレス", "recipient_5": "5番目の受信者のメールアドレス"}, "snmp": {"page_title": "SNMP", "v1": "V1, V2c, V3", "v2c": "V1, V2c", "v3": "V3 Only", "snmp_version": "SNMP バージョン", "snmp_account": "SNMPアカウント", "read_community": "読み取りコミュニティ", "read_write_community": "読み/書きコミュニティ", "read_write": "Read/Write", "des": "デス", "aes": "AES", "snmp_account_size_limitation": "このデバイスの SNMP アカウントの最大数は {{ size }}。", "snmp_warning_dialog_V1V2c_title": "SNMP バージョンの設定", "snmp_warning_dialog_V1V2c_desc": "非セキュア インターフェイス (SNMP バージョン V1、V2c) を有効にしてもよろしいですか?", "snmp_warning_dialog_authMD5_title": "認証タイプ MD5 を設定します", "snmp_warning_dialog_authMD5_desc": "MD5 認証は限定的なセキュリティのみを提供します。続行してもよろしいですか?", "change_password_title": "認証パスワードの変更", "change_key_title": "暗号化キーを変更する", "change_key_button": "暗号化キーの変更", "create_v3_account": "SNMP アカウントの作成", "edit_v3_account": "このSNMPアカウントを編集"}, "snmp_trap": {"page_title": "SNMPトラップ/通知", "snmp_trap_inform_recipient": "SNMP トラップ/受信者に通知", "snmp_inform_settings": "SNMP通知設定", "inform_retry": "再試行を通知する", "inform_timeout": "タイムアウトを通知", "recipient_name": "受信者のIPアドレス/ドメイン名", "trap_community": "トラップコミュニティ", "snmp_trap_inform_account": "SNMP トラップ/アカウントへの通知", "trap_v1": "Trap V1", "trap_v2c": "Trap V2c", "inform_v2c": "Inform V2c", "trap_v3": "Trap V3", "inform_v3": "Inform V3", "title_delete_host_dialog_title": "このホストを削除", "title_delete_host_dialog_desc": "このホストを削除してもよろしいですか?", "snmp_trap_account_size_limitation": "このデバイスの SNMP トラップ/インフォーム アカウントの最大数は {{ size }}。", "snmp_host_size_limitation": "このデバイスの SNMP トラップ ホストの最大数は {{ size }}。", "create_v3_trap_account": "SNMP トラップ アカウントの作成", "edit_v3_trap_account": "この SNMP トラップ アカウントを編集", "create_host_table": "ホストの作成", "edit_host_table": "このホストを編集"}, "arp": {"page_title": "ARP テーブル", "title_clear_message": "すべての ARP エントリをクリア", "clear_confirmation_message": "すべての ARP エントリをクリアしてもよろしいですか?"}, "event_log": {"page_title": "イベントログ", "boot": "起動番号", "progname": "プログラム名", "timestamp": "タイムスタンプ", "uptime": "稼働時間", "message": "メッセージ", "severity_emerg": "緊急", "severity_alert": "警告", "severity_info": "情報", "severity_debug": "デバッグ", "flush_log_entry_confirmation_message": "すべてのログ エントリをクリアしてもよろしいですか?", "total_entries": "エントリー総数: ", "clear_all_logs": "すべてのログをクリア", "capacity_warning": "容量の警告", "capacity_warning_hint": "登録済みアクションは、イベント通知ページで個々のイベントに対して構成できます。", "warning_threshold": "警告しきい値", "oversize_action": "オーバーサイズアクション", "oversize_action_overwrite": "Overwrite the oldest event log", "oversize_action_stop": "Stop recording event logs", "title_clear_all_logs": "すべてのログをクリア", "debug_hint": "{{ number }} ログは内部使用用です。", "hash_value": "ハッシュ値", "auto_backup_of_event_log": "イベントログの自動バックアップ"}, "trust_access": {"page_title": "信頼されたアクセス", "size_limitation": "このデバイスの信頼できるアクセス エントリの最大数は {{ size }}。", "delete_all_warning_title": "Trusted Access エントリをすべて削除できません", "delete_all_warning_1": "有効にした場合、Trusted Access には少なくとも 1 つのアクティブなエントリが必要です。保管しておくことを強くお勧めします。", "delete_all_warning_2": "あなたの現在のIPアドレス", "delete_all_warning_3": "アクティブなエントリーとして。"}, "utilization": {"page_title": "リソースの使用率", "cpu_utilization": "CPU使用率", "cpu_historical_record": "CPU使用率履歴", "mem_utilization": "メモリ使用量", "mem_historical_record": "メモリ使用履歴", "power_utilization": "消費電力", "power_historical_record": "消費電力履歴", "last_update_time": "最終更新 ", "used": "使用済み", "free": "フリー", "past_10_second": "過去10秒", "past_30_second": "過去30秒", "past_300_second": "過去300秒", "selecting_visible_polyline": "表示されているポリラインを編集する", "polyline_display_hint": "ウィジェットの右上隅にあるアイコンをクリックして、表示するデータを選択します。"}, "tacacs_server": {"page_title": "TACACS+サーバー", "tacacs": "TACACS+", "auth_type_asc_two": "ASCII"}, "syslog_server": {"page_title": "Syslog", "syslog_server": "Syslog サーバー", "auth_disable_hint": "証明書とキーが存在しないため有効にできません。", "tls": "TLS", "common_name": "一般名", "expireTime": "有効期限", "key_limitation": "このデバイスの証明書とキー セットの最大数は {{ size }}。", "title_add_key": "証明書とキーセットを追加", "title_edit_key": "この証明書とキーを編集", "delete_key_desc": "証明書とキーを削除してもよろしいですか?", "client_certificate": "クライアント証明書", "client_key": "クライアントキー", "ca_key": "CAキー"}, "radius": {"page_title": "RADIUSサーバー", "radius": "RADIUS", "server_address_number": "サーバのIPアドレス {{ number }}", "mschap": "MS-CHAPv1", "mschap_v2": "MS-CHAPv2"}, "config_bk_res": {"page_title": "構成のバックアップと復元", "menu_title": "構成のバックアップと復元", "file_encryption": "ファイルの暗号化", "file_signature": "ファイル署名", "config_name": "構成名", "config_file_encryption": "設定ファイルの暗号化", "configuration_selection": "構成の選択", "running_configuration": "Running Configuration", "startup_configuration": "Startup Configuration", "default_configuration": "デフォルト設定", "not_included": "Not Included", "included": "Included", "signed_config": "署名付き構成", "sign_hint": "有効にすると、管理者が構成をバックアップまたは復元するときにデジタル署名が追加されます。", "sign_disable_hint": "秘密キーと公開キーが空であるため、この機能を有効にすることはできません。", "private": "プライベート", "certificate": "証明書", "label": "ラベル", "length": "長さ", "key_limitation": "このデバイスのキーペアの最大数は {{ size }}。", "title_add_key": "カスタムキーを追加", "title_edit_key": "このカスタムキーを編集", "delete_key_desc": "このキー ペアを削除してもよろしいですか?", "auto_bk_of_config": "自動構成バックアップ", "auto_load_of_config": "自動構成復元", "auto_restore": "自動構成復元", "auto_restore_hint": "起動時に外部ストレージデバイスから構成を自動的に復元します。", "encrypt_whole_file": "ファイル全体を暗号化する", "encrypt_sensitive_information_only": "機密情報のみを暗号化する", "encrypt_hint": "\"機密情報のみを暗号化する\"が選択され、\"暗号化キー\"フィールドが空白のままの場合、代わりにMoxa暗号化キーが使用されます。"}, "firmware_upgrade": {"page_title": "ファームウェアのアップグレード"}, "module_information": {"page_title": "モジュール情報", "module_name": "モジュール名", "no_module_msg": "モジュールがインストールされていません"}, "event_notification": {"page_title": "イベントのお知らせ", "group": "グループ", "event_name": "イベント名", "system_and_function": "システムと機能", "registered_event": "登録されたイベント", "registered_action": "登録されたアクション", "registered_port": "登録されたポート", "group_general": "一般", "group_switching": "スイッチング", "group_poe": "PoE", "group_routing": "ルーティング", "group_tracking": "Tracking", "notification_loginSuccess": "Login success", "notification_loginFail": "<PERSON><PERSON> failed", "notification_loginLockout": "Login lockout", "notification_accountChanged": "Account settings changed", "notification_certificationChanged": "SSL certification changed", "notification_passwordChanged": "Password changed", "notification_coldStart": "Cold start", "notification_warmStart": "Warm start", "notification_configurationChanged": "Configuration changed", "notification_configurationImported": "Configuration imported", "notification_logCapacityThreshold": "Log capacity threshold", "notification_powerOff": "Power On->Off", "notification_powerOn": "Power Off->On", "notification_diOn": "DI on", "notification_diOff": "DI off", "notification_topologyChanged": "Topology changed", "notification_couplingChanged": "Coupling changed", "notification_masterChanged": "Master changed", "notification_masterMismatch": "Master mismatch", "notification_rstpTopologyChanged": "RSTP topology changed", "notification_rstpRootChanged": "RSTP root changed", "notification_rstpMigration": "RSTP migration", "notification_rstpInvalidBpdu": "RSTP invalid BPDU", "notification_rstpNewPortRole": "RSTP new port role", "notification_mstpTopologyChanged": "MSTP topology changed", "notification_mstpRootChanged": "MSTP root changed", "notification_mstpNewPortRole": "MSTP new port role", "notification_linkHealthyCheckFail": "Redundant port health check failed", "notification_dualHomingPathSwitched": "Dual homing path changed", "notification_dot1xAuthFail": "802.1X auth failed", "notification_lldpTableChanged": "LLDP table changed", "notification_rmonRaisingAlarm": "RMON raising alarm", "notification_rmonFallingAlarm": "RMON failing alarm", "notification_macsecInterfaceMKAFail": "MACsec MKA failed", "notification_dhcpsnpDynamicEntrySetFailed": "Binding Status dynamic entry failed", "notification_dhcpsnpUntrustMacDiscard": "DHCP client ingress discards packets due to the DHCP Snooping rule", "notification_dhcpsnpUntrustServerDiscard": "DHCP server discards packets due to the DHCP Snooping rule", "notification_multipleCouplingPathChanged": "Multiple coupling path changed", "notification_dhcpBootfileFail": "DHCP Bootfile failed", "notification_trackingStatusChanged": "Tracking Status Changed", "notification_trackingReactionPort": "Tracking Action Triggered on Port", "notification_trackingReactionStaticRoute": "Tracking Action Triggered on Static Route", "notification_trackingReactionVrrp": "Tracking Action Triggered on VRRP", "notification_pdPowerOn": "PD power on", "notification_pdPowerOff": "PD power off", "notification_lowInputVoltage": "Low input voltage", "notification_pdOverCurrent": "PD over-current", "notification_pdNoResponse": "PD no response", "notification_overPowerBudgetLimit": "Over power budget limit", "notification_powerDetectionFailure": "Power detection failure", "notification_nonPdOrPdShort": "Non-PD or PD short circuit", "notification_portOn": "Port On", "notification_portOff": "Port Off", "notification_rateLimitedOn": "Port shut down by Rate Limit", "notification_rateLimitedOff": "Port recovered by Rate Limit", "notification_psecViolationPortDown": "Port shut down by Port Security", "notification_fiberWarning": "Fiber Check warning", "notification_linkUp": "Interface up", "notification_linkDown": "Interface down", "notification_adjacencyChanged": "OSPF adjacency changed", "notification_drChanged": "OSPF DR changed", "notification_becomeDR": "OSPF become DR", "notification_vrrpMasterChanged": "VRRP virtual router master changed", "notification_pimSmDrChanged": "PIM-SM DR changed", "notification_pimSmRpAdded": "PIM-SM RP added by BSM", "notification_pimSmRpDeleted": "PIM-SM RP deleted by BSM", "notification_supABportTimediff": "A PHR Supervision frame time difference event occurred on ports A, B", "notification_gcTimeout": "GOOSE Check entry counter timeout", "notification_gcTimeoutClear": "GOOSE Check entry counter timeout clear", "notification_gcPortTampered": "GOOSE Check tampered ingress port", "notification_gcAddrTampered": "GOOSE Check tampered source MAC address", "notification_gcLockViolation": "GOOSE Check Lock violation", "notification_gcEntryReset": "GOOSE Check entry reset", "notification_gcEntryDelete": "GOOSE Check entry delete", "action_trap": "Trap", "information": "Information", "title_edit_event_notification": "このイベント通知を編集"}, "relay_output": {"page_title": "リレーアラーム", "relay_alarm_cut_off": "リレーアラームカット オフ", "relay_alarm_settings": "リレーアラーム設定", "fault_led_display": "障害LED表示", "cut_off": "カット オフ"}, "statistics": {"page_title": "ネットワーク統計", "bandwidth_utilization": "帯域幅の使用状況", "packet_counter": "パケットカウンター", "rxTotalOctets": "Rx 合計オクテット", "collisionPackets": "コリジョンパケット", "dropPackets": "ドロップされたパケット", "dropPacketsHint": "Dropped Packet カウンタのデフォルトの更新時間は約 5 秒で、デバイス上のポートの数に基づいて増加します。", "rxPausePackets": "Rx 一時停止パケット", "txTotalOctets": "送信合計オクテット", "txUnicastPackets": "ユニキャストパケットの送信", "crcAlignErrorPackets": "CRC アラインエラーパケット", "txMulticastPackets": "マルチキャストパケットの送信", "rxBroadcastPackets": "Rx ブロードキャスト パケット", "rxUnicastPackets": "Rx ユニキャスト パケット", "jabberPackets": "ジャバーパケット", "excessiveCollisionPackets": "過剰な衝突パケット", "txTotalPackets": "送信パケット総数", "fragmentPackets": "断片化されたパケット", "rxTotalPackets": "受信パケット総数", "lateCollisionPackets": "レイトコリジョンパケット", "oversizePackets": "特大パケット", "rxMulticastPackets": "Rx マルチキャスト パケット", "txBroadcastPackets": "Tx ブロードキャスト パケット", "undersizePackets": "サイズ未満のパケット", "txptpPackets": "送信 PTP パケット", "rxptpPackets": "受信 PTP パケット", "displayMode": "ディスプレイモード", "packetCounter": "Packet Counter", "bandwidthUtilization": "Bandwidth Usage", "line_num_target_port": "ライン {{ number }} モニタリングポート", "line_num_target_sniffer": "ライン {{ number }} スニッファー", "txandrx": "Tx/Rx", "txonly": "Tx", "rxonly": "Rx", "all_port": "すべてのポート", "all_ge_port": "すべての GE ポート", "all_fe_port": "すべての FE ポート,", "line_num": "ライン{{ number }}", "clear_graph_desc": "すべてのグラフ データをクリアしてもよろしいですか?", "clear_table_desc": "すべてのテーブル データをクリアしてもよろしいですか?", "benchmark_line": "ベンチマーク", "benchmark_line_time": "ベンチマーク ライン - 時間", "comparison_line": "比較", "comparison_line_time": "比較 ライン - 時間", "selecting_visible_columns": "表示されている列を編集", "title_comparison": "データの比較", "title_reset_statistics_graph": "統計グラフをリセット", "title_edit_statistics_setting": "ディスプレイの設定", "title_clear_statistics_counter": "カウンターをクリア"}, "mab": {"page_title": "MAC認証バイパス", "page_title_abbr": "MAB", "auth_mode": "認証モード", "local_database": "Local Database", "quiet_period": "クワイエット期間", "reauthentication": "再認証", "reauth_period": "再認証期間", "size_limitation": "このデバイスの MAC アドレス エントリの最大数は {{ size }}。", "retransmit": "再送信", "force_unauthorized": "Force Unauthorized", "force_Authorized": "Force Authorized", "reauth_port_confirm": "ポートを再認証してもよろしいですか?", "authorized": "承認済み", "unauthorized": "無許可", "title_reauth_port": "リオースポート", "port_setting": "ポート {{ portIndex }} 設定", "account_setting": "アカウント {{ userName }} 設定", "timeout_retransmit_hint": "すべての再試行時間が Dot1x サーバーのタイムアウト値を超えることはできません。ノート：  すべての再試行時間 = タイムアウト * (再送信 + 1)。推奨値:  {{ number }}", "clear_button_disabled_hint": "エントリ数が最大容量に達した場合、MAC 認証バイパスを通じて収集されたすべての MAC アドレス エントリをクリアします。", "clear_warning_hint": "MAC 認証バイパスを通じて収集された MAC アドレス エントリの数が最大容量に達しています。", "title_clear_mac_address": "MAC アドレスをクリア", "clear_mac_address_message": "MAC 認証バイパスを通じて収集されたすべての MAC アドレスをクリアしてもよろしいですか?", "radius_hint": "802.1X と MAC 認証バイパスは同じ RADIUS サーバーを共有します。"}, "modbus_tcp": {"page_title": "Modbus TCP", "enable_modbus_tcpo_title": "Modbus TCP を有効にする", "enable_modbus_tcp_warning": "非セキュアプロトコル (Modbus TCP) を有効にしてもよろしいですか?"}, "fiber_check": {"page_title": "ファイバーチェック", "threshold_settings": "しきい値の設定", "model_name": "モデル名", "serial_number": "シリアルナンバー", "wavelength": "波長", "voltage": "電圧", "temperature": "温度", "tx_power": "送信電力", "rx_power": "受信パワー", "temperature_limit": "温度しきい値", "tx_power_threshold_low": "送信電力しきい値が低い", "tx_power_threshold_high": "送信電力しきい値高", "rx_power_threshold_low": "受信電力しきい値が低い", "rx_power_threshold_high": "受信電力しきい値高", "temp_over_spec": "温度がしきい値を超えています。", "tx_power_over_spec": "送信電力がしきい値を超えています。", "tx_power_under_spec": "送信電力がしきい値を下回っています。", "rx_power_over_spec": "受信電力がしきい値を超えています。", "rx_power_under_spec": "受信電力がしきい値を下回っています。", "port_copy_to_copper_port_disable": " 設定を銅線ポートにコピーできません。", "warning": "警告", "reset_title": "リセットポート {{ portIndex }} しきい値設定", "reset_desc": "このポートを自動モード (デフォルト) にリセットし、すべてのしきい値設定をクリアしてもよろしいですか?", "reset_all_ports": "すべてのポートをリセット", "reset_all_ports_title": "すべてのポートのしきい値設定をリセット", "reset_all_ports_desc": "すべてのポートを自動モード (デフォルト) にリセットし、すべてのしきい値設定をクリアしますか?"}, "dai": {"page_title": "動的ARP検査", "port_is_la_member": "このポートはポート チャネルのメンバーです。ダイナミック ARP インスペクションをメンバー ポートで有効にすることはできません。", "port_is_trusted": "このポートは DHCP スヌーピングの信頼できるポートです。信頼できないポートのみをダイナミック ARP インスペクションに対して有効にできます。"}, "mac_sec": {"page_title": "メディアアクセス制御セキュリティ", "mka_status": "MKA ステータス", "participant_ckn": "参加者CKN", "participant_cak": "参加者 CAK", "key_server": "キーサーバー", "peer_list_mi": "ピアリストメンバー識別子(MI)", "peer_list_mn": "ピアリストメッセージ番号(MN)", "sci": "セキュア チャネル ID (SCI)", "peer_list_type": "ピアリストタイプ", "live_peer_list": "ライブピアリスト", "potential_peer_list": "潜在的なピアリスト", "peer_list_mi_hint": "MI：  MKA メッセージには、送信者自身のメンバー ID と、メッセージを受信した他の潜在的なピアの ID が含まれます。", "not_support_10G_port": "MACsec は 10G ポートではサポートされていません。", "member_port_disable": "このポートはポート チャネルのメンバーです。メディア アクセス コントロールのセキュリティはメンバー ポートでは編集できません。"}, "profinet": {"page_title": "プロフィネット", "protocol_information": "プロトコル情報", "enable_profinet_title": "PROFINET を有効にする", "enable_profinet_warning": "安全でないプロトコル (PROFINET) を有効にしてもよろしいですか?", "vlan_not_exist": "VLAN が存在しません。", "label_format_hint": "これがラベルのフォーマットです。各ラベルは\".\"で区切る必要があります。ラベルの最大文字数は 63 文字です。"}, "ethernet_ip": {"page_title": "イーサネット/IP", "enable_eip_title": "EtherNet/IPを有効にする", "enable_eip_warning": "安全でないプロトコル (EtherNet/IP) を有効にしてもよろしいですか?"}, "multi_coupling": {"page_title": "複数のネットワーク結合", "main_ring_protocol": "メインリングプロトコル", "switch_role": "Couplingスイッチの役割", "group_id": "CouplingグループID", "polling_interval": "Couplingポーリング間隔", "polling_interval_hint": "メイン リングの場合、80 ミリ秒のポーリング間隔を使用する場合、パス ID の最大数は 16 です。40 ミリ秒のポーリング間隔を使用する場合は、8 つのパス ID を使用することをお勧めします。", "table_settings": "Coupling テーブルの設定", "table_status": "Coupling テーブルのステータス", "path_id": "パスID", "coupling_port": "Coupling ポート", "coupling_port_state": "Coupling ポートのステータス", "partner_mac": "パートナーMAC", "connect_status": "接続ステータス", "error_status": "エラーステータス", "multiple_active": "複数のアクティブなスイッチ", "multiple_backup": "複数のバックアップ スイッチ", "role_information": "役割情報", "edit_path_title": "パスIDの編集 {{ pathId }} 設定", "multi_coupling_enable_hint": "メイン リング プロトコル Turbo Ring V2 または MRP を有効にする必要があります。", "is_coupling_port": "これはカップリング ポートとして選択されました。", "is_selected_path": "このパス ID はすでに選択されています。", "error_status_message": "複数のアクティブ/バックアップ スイッチが検出されました。このデバイスとパートナー デバイスをチェックして、重複がないことを確認してください。"}, "pim_dm": {"page_title": "PIM-DM", "state_refresh": "状態のリフレッシュ", "state_refresh_interval": "状態の更新間隔", "pim_dm_hint": "PIM-DM が有効な場合、IGMP の要求がある場合、IGMP スヌーピングと併用できます。", "state_refresh_hint": "PIM-DM モード ネットワーク内のすべてのルーターは状態の更新を有効にし、同じ間隔を設定する必要があります。"}, "pim_sm": {"page_title": "PIM-SM", "pim_sm_settings": "PIM-SMの設定", "pim_sm_hint": "PIM-SM を有効にする場合は、必要に応じて IGMP スヌーピングと関連する設定を必ず有効にしてください。", "spt_method": "最短パスツリー切り替え方式", "join_prune_interval": "間隔の追加/トリミング", "dr_priority": "DR優先度", "bsr": "BSR", "bsr_candidate": "BSR候補者", "bsr_address": "BSRアドレス", "bsr_priority": "BSRの優先度", "bsr_hash_mask_length": "BSR ハッシュ マスクの長さ", "dr_priority_hint": "DR の選択では、優先度の値が大きい DR が優先されます。", "bsr_priority_hint": "BSR の選択では、優先度の値が大きい BSR が優先されます。", "rp": "RP", "static_rp": "静的RP", "candidate_rp": "RP候補", "group_address": "グループアドレス", "group_mask": "グループマスク", "rp_address": "RPアドレス", "override": "カバー", "interface_not_created": "PIM-SM インターフェイスはまだ作成されていません。", "rp_if_name": "RPインターフェイス名", "rp_priority": "RPの優先度", "static_rp_size_limitation": "このデバイスの静的 RP の最大数は {{ size }} です。", "candidate_rp_size_limitation": "このデバイスの候補 RP の最大数は {{ size }} です。", "ssm": "SSM", "pim_ssm": "PIM-SSM", "pim_ssm_size_limitation": "このデバイスの PIM-SSM の最大数は {{ size }} です。", "title_create_static_rp": "静的 RPの作成", "title_edit_static_rp": "静的 RP の編集", "title_delete_static_rp": "静的 RP の削除", "delete_static_rp_desc": "選択した静的 RP を削除してもよろしいですか?", "override_hint": "オーバーライドとは、競合が発生した場合に、動的学習 (BSR) よりもこの静的 RP が優先して使用されることを意味します。 」", "title_create_candidate_rp": "候補RPの作成", "title_edit_candidate_rp": "候補RPの編集", "title_delete_candidate_rp": "候補RPの削除", "delete_candidate_rp_desc": "選択した候補 RP を削除してもよろしいですか?", "rp_priority_hint": "RP の選択では、優先度の値が小さい RP が優先されます。", "rp_if_name_address": "RPインターフェイス名（RPアドレス）", "title_add_ssm_range": "SSM スコープの追加", "title_edit_ssm_range": "SSM スコープの編集", "title_delete_ssm": "SSM の削除", "delete_ssm_desc": "選択した SSM を削除してもよろしいですか?", "static_ssm_entry_hint": "この範囲は、RFC 7761 で SSM 用に予約されています。", "pim_sm_status": "PIM-SMステータス", "dr_address": "DRアドレス", "bsr_rp": "BSR / RP", "elected_bsr": "選出されたBSR", "rp_mapping": "RPマッピング", "rp_mapping_result": "RPマッピング結果"}, "multicast_routing_table": {"page_title": "マルチキャストルーティングテーブル", "multicast_group": "マルチキャストグループ", "upstream_neighbor": "上流の隣人", "incoming_interface": "受信インターフェイス", "outgoing_interface": "送信インターフェイス", "prune": "プルーン", "assert": "アサート"}, "prp_hsr": {"page_title": "PRP/HSR", "prp_hsr_protocol": "PRP/HSRプロトコル", "entry_forget_time": "Entry Forget Time", "net_id": "Net ID", "lan_id": "LAN ID", "prp": "PRP", "hsr": "HSR", "coupling": "Coupling", "enable_prp_hsr_title": "PRP/HSR を有効にする", "enable_prp_hsr_warning": "PRP/HSR を有効にすると、PRP/HSR モジュールのすべてのポートの設定がデフォルト値にリセットされます。よろしいですか?", "no_phr_module_warning": "有効な PHR モジュールが検出されませんでした。 PHRモジュールを確認してください。"}, "tracking": {"page_title": "Tracking", "tracking_list_of_interface": "Tracking List of Interface", "tracking_list_of_ping": "Tracking List of Ping", "tracking_list_of_logical": "Tracking List of Logical", "tracking_list_of_all": "Tracking List of All", "tid": "Tracking ID", "down_to_up": "Down to Up", "up_delay": "Up Delay", "up_to_down": "Up to Down", "down_delay": "Down Delay", "received": "Received", "lost": "Lost", "and": "AND", "or": "OR", "nand": "NAND", "nor": "NOR", "entry_state": "状態", "interface_tracking": "Interface Tracking", "ping_tracking": "Ping Tracking", "logical_tracking": "Logical Tracking", "create_interface_tracking_entry_title": "インターフェイス トレース エントリを作成する", "edit_interface_tracking_entry_title": "このインターフェイス トレース エントリを編集します", "create_ping_tracking_entry_title": "Ping追跡エントリを作成する", "edit_ping_tracking_entry_title": "この Ping 追跡エントリを編集します", "create_logical_tracking_entry_title": "論理トレース エントリを作成する", "edit_logical_tracking_entry_title": "このロジック追跡エントリを編集します", "interface_type": "インターフェースの種類", "port": "Port", "ping": "<PERSON>", "logical": "Logical", "interface": "Interface", "network_interface": "Network Interface", "status_change_from_down_to_up": "ステータスの変化 (下から上へ)", "status_change_from_up_to_down": "ステータスの変化（上から下）", "logical_list": "論理リスト", "logical_oper": "論理演算子", "interfaec_ip_logic": "インターフェース/IPアドレス/論理リスト", "time_since_last_change": "前回の変更からの経過時間", "no_of_change": "変更の数", "only_select_four": "最大 4 つまで選択できます", "require_tid_larger": "トラッキング ID は、論理リストの TID より大きくなければなりません。", "require_at_least_two_tid": "3 つ以上の TID が必要です", "duplicated_tid": "追跡IDが重複しています", "tracking_size_limitation": "このデバイスの追跡エントリの最大数は {{ size }} です。", "sync_to_latest_status": "同期します。最新の状態へ"}, "auto_config": {"page_title": "自動設定", "cdu_port": "コントロールユニットポート", "auto_config_info": "自動設定情報", "import_mode_hint": "DHCP クライアントと LLDP を有効にし、DHCP クライアントのブート ファイルとクライアント ID を事前に構成する必要があります。このモードでは、Auto ConfigurationはControl Unitポート経由でOption 61パケットのみを送信します。", "propagate_mode_hint": "IP設定を手動に設定し、LLDPを有効にする必要があります。このモードでは、DHCP サーバは LLDP 情報に基づいて IP アドレスを割り当てます。"}, "multi_local_route": {"page_title": "マルチキャストローカルルーティング", "routes": "ルーティング", "macl": "MACL", "vrrp_master_only": "VRRPマスターのみ", "multi_local_route_hint": "マルチキャスト ローカル ルーティングが有効な場合は、IGMP スヌーピングも有効にする必要があります。", "vrrp_master_only_hint": "有効にすると、スイッチは VRRP マスターとして機能する場合にのみマルチキャスト フローをルーティングできます。", "source_vlan": "送信元VLAN", "downstream_vlan": "ダウンストリーム VLAN", "multi_local_route_size_limitation": "マルチキャスト ローカル ルート エントリの最大数は {{ size }} です。", "create_route_msg": "マルチキャストローカルルートを作成する", "edit_route_msg": "ソース VLAN を編集 {{ vid }}", "macl_id": "MACL ID", "title_create_macl_rule": "マルチキャストACLを作成する", "title_edit_macl_rule": "MACL ID を編集 {{ maclId }}", "only_select_sixteen": "最大16個まで選択可能", "delete_session_title": "マルチキャストローカルルートの削除", "delete_session_content": "選択したマルチキャスト ローカル ルートを削除してもよろしいですか?", "source_vlan_cannot_set": "送信元VLANを設定できません。"}, "supervision_frame": {"page_title": "監視フレーム", "supervision_frame_enable_hint": "モニタリング フレームを有効にする前に、PRP/HSR プロトコルを有効にする必要があります。", "life_check_interval": "ライフチェック間隔", "destination_address": "ターゲットアドレス", "forward_to_interlink": "監視フレームがインターリンクに転送される", "nodes_table": "ノードテーブル", "forget_time": "ノードフォーゲットタイム", "node_type": "ノードの種類", "time_last_seen_a": "最後に見た時間 A", "time_last_seen_b": "最後に見た時間 B"}, "goose_check": {"page_title": "GOOSE Check", "goose_lock": "GOOSE Lock", "goose_lock_hint": "GOOSE ロックが有効な場合、監視テーブルに表示されていない GOOSE パケットはドロップされます。", "tamper_response": "タンパーレスポンス", "tamper_response_hint": "「ドロップ」を選択すると、改ざんされた GOOSE パケットがすべてドロップされます。 [ポートの無効化] を選択すると、改ざんされた GOOSE パケットの入力ポートが無効になります。", "port_disable": "Port Disable", "app_id": "アプリID", "goose_address": "GOOSEアドレス(DA)", "monitoring_table_status": "テーブルのステータスを監視する", "goose_lock_status": "GOOSEロックステータス", "lock_violation_status": "Lcok違反ステータス", "goose_name": "GoCB名", "rx_counter": "受信カウンター", "create_goose": "ステータス GOOSE エントリの作成", "port_tampered": "ポートの改ざん", "sa_tampered": "SAの改ざん", "duplicate_goose": "同じ静的 GOOSE エントリがすでに存在します", "exist_dynamic_entry": "同じ動的学習 GOOSE エントリがすでに存在します。 [適用] をクリックして、このエントリを静的エントリに変更します。", "lock_violation_normal_hint": "検出されたすべての GOOSE パケットが監視テーブルに表示されます。", "lock_violation_warning_hint": "予期しない GOOSE パケットが検出されましたが、監視テーブルには表示されません。", "goose_table_max": "監視テーブルの最大 {{ サイズ }}", "size_limitation": "このデバイスのグース チェック エントリの最大数は {{ size }} です。"}}, "request_handler": {"action_saving": "保存しています...", "action_loading": "読み込み中...", "action_upgrading": "アップグレードしています...", "action_ping": "Pinging ..."}, "response_handler": {"res_server_error": "サーバー接続エラー。", "res_global_success": "正常に更新されました。", "res_complete_refresh": "更新が完了しました。", "res_complete_encrypt_data": "データの暗号化が完了しました。", "res_port_success": "ポート設定が正常に更新されました。", "res_entry_create_success": "エントリが正常に作成されました。", "res_entry_update_success": "エントリが正常に更新されました。", "res_entry_delete_success": "エントリが正常に削除されました。", "res_port_enable_success": "ポート {{ portIndex }} 正常に有効になりました。", "res_port_disable_success": "ポート {{ portIndex }} 正常に無効化されました。", "res_dscp_success": "DSCP 設定が正常に更新されました。", "res_cos_success": "CoS 設定が正常に更新されました。", "res_regen_ssh_success": "SSH キーが正常に再生成されました。", "res_regen_ssl_success": "SSL 証明書が正常に再生成されました。", "export_ssl_cert": "SSL 証明書が正常にエクスポートされました。", "import_ssl_cert": "SSL 証明書が正常にインポートされました。", "import_config": "構成が正常にインポートされました。", "res_copy": "コピーされました。", "res_ping_success": "ping が終了しました。", "res_auto_save_mode_success": "自動保存モードが正常に更新されました。", "res_switch_browse_mode_success": "モードが正常に切り替わりました。", "res_v3_account_update_success": "認証アカウントが正常に更新されました。", "res_host_update_success": "SNMP ホストが正常に更新されました。", "res_upgrading_firmware_success": "ファームウェアが正常にアップグレードされました。デバイスは再起動されます。", "res_event_notification_success": "イベント通知が正常に更新されました。", "res_save_to_startup_success": "実行コンフィギュレーションがスタートアップ コンフィギュレーションに正常に保存されました。", "clear_success": "正常にクリアされました。", "res_factory_default_success": "工場出荷時のデフォルトに正常にリセットされました。", "backup_success": "バックアップが成功しました。", "res_custom_default_success": "カスタムデフォルトに正常にリセットされました。", "download_success": "ダウンロードに成功しました。", "locator": "デバイス ロケーターが正常にトリガーされました。", "re_auth_port_success": "ポートの再認証に成功しました。", "res_recovery_port_success": "ポートは正常に回復しました。"}, "error_handler": {"error_session_expired_dialog": "このセッションは期限切れになりました。ログインページに戻ります。"}, "validators": {"required": "必要", "require_min_length": "最小 {{ number }} キャラクター", "require_range_between": "{{ rangeBegin }} - {{ rangeEnd }}", "require_range_single_between": "{{ singleNum }}、 {{ rangeBegin }} - {{ rangeEnd }}", "require_hexadecimal": "16 進数のみ", "require_unicast": "ユニキャストIPのみ", "required_at_least_two": "少なくとも 2 つの出力ポート", "required_at_least_one_overwrite": "少なくとも 1 つの上書き項目", "required_priority_multiple": "優先順位は次の倍数でなければなりません {{ num }}", "required_label_max_length": "ラベルの最大文字数は {{ number }}", "required_interval_multiple": "間隔は {{ num }} の倍数でなければなりません", "required_timeout_multiple": "タイムアウト間隔は、{{ num }} の倍数である必要があります", "invalid": "無効", "invalid_format": "無効な形式", "invalid_positive_integer": "無効な正の整数", "invalid_hex": "無効な 16 進数", "invalid_range": "有効な範囲は次のとおりです {{ rangeBegin }} に {{ rangeEnd }}", "invalid_single_range": "範囲は {{ singleNum }} また {{ rangeBegin }} に {{ rangeEnd }}", "invalid_mac_address": "無効なMACアドレス", "invalid_ip_address": "無効な IP アドレス", "invalid_area_id": "無効なエリアID", "invalid_router_id": "無効なルーターID", "invalid_vlan_port": "無効な VLAN メンバー ポート", "invalid_vlan_output_port": "出力ポートの VLAN が無効です", "invalid_netmask": "無効なネットマスク", "invalid_char": "a-z、A-Z、0-9 のみが許可されます", "invalid_email": "無効なメール", "invalid_regex_level_1": "a-z、A-Z、0-9、または . - _ は許可されます", "invalid_regex_level_2": "a-z、A-Z、0-9、または . , - _ + = | : ; @ ! ~ # % ^ * ( ) [ ] { } のみが許可されます", "invalid_sys_desc": "a-z、A-Z、0-9 または ~ ! @ # $ % ^ & * ( ) { } [ ] < > _ + - = \\ : ; , . /のみが許可されます", "invalid_login_failure_message": "a-z、A-Z、0-9 または ! # $ % & ' ( ) * + , \\ - : ; < = > @ [ ] ^ _ ` { | } ~ のみが許可されます", "invalid_regex_macsec_cak_and_ckn": "a-z、A-Z、0-9 または @ % $ ^ * ' ` ( ) _ + = { } : . , ~ [ ] - のみが許可されます", "invalid_char_and_dash": "a-z、A-Z、0-9、または - のみが許可されます", "invalid_char_and_dot_dash": "a-z、A-Z、0-9、または . - のみが許可されます", "invalid_lowercase_and_dash_dot": "a-z、0-9、または  . - のみが許可されます", "invalid_file_name": "a-z、A-Z、0-9 または / ( )  . - _ のみは許可されます", "duplicate_ip": "重複したIP", "duplicate_ip_range": "IP 範囲の重複", "duplicate_vrid": "VRIDが重複しています", "duplicate_stream": "このストリームはすでに存在します", "duplicate_id": "重複したID", "duplicate_input_ports": "入力ポートの重複", "duplicate_loopback_id": "このループバック ID はすでに使用されています", "duplicate_vlan_id": "この VLAN ID はすでに使用されています。", "duplicate_vlan_and_mac": "この VLAN ID と MAC アドレスの組み合わせはすでに存在します", "duplicate_group_address_and_netmask": "このグループ アドレスとグループ ネットマスクの組み合わせはすでに存在します", "two_deciaml_palce": "小数点以下 2 桁まで", "duplicate_ip_and_netmask": "この IP アドレスとサブネット マスクの組み合わせは既に存在します", "three_deciaml_palce": "小数点以下 3 桁まで", "same_as_ingress_stream": "イングレス ストリームと同じ"}}