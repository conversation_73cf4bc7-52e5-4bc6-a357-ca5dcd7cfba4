import { AfterViewInit, ChangeDetectionStrategy, Component, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { MatLegacyPaginator as MatPaginator } from '@angular/material/legacy-paginator';
import { MatLegacySnackBar as MatSnackBar } from '@angular/material/legacy-snack-bar';
import { MatLegacyTableDataSource as MatTableDataSource } from '@angular/material/legacy-table';
import { MatSort } from '@angular/material/sort';

import { TranslateService } from '@ngx-translate/core';
import { find, forEach, union } from 'lodash-es';
import { forkJoin, Observable } from 'rxjs';

import * as environment from '@switch-web/shared/environments/environment';
import { AppState } from '@switch-web/shared/app/app.service';
import { GlobalEvent, GlobalEventType } from '@switch-web/shared/app/global.event';
import { GenerateDocService } from '@switch-web/shared/assets/mx-service/generate-doc.service';
import { ErrorService } from '@switch-web/shared/service/error.service';
import { UtilsService } from '@switch-web/shared/service/utils.service';
import { ValidatorPattern } from '@switch-web/shared/validator/validators';

@Component({
  templateUrl: './pim-sm-status.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PimSmStatusComponent implements AfterViewInit {
  @ViewChild('interfaceTableSort') interfaceTableSort: MatSort;
  @ViewChild('interfaceTablePaginator') interfaceTablePaginator: MatPaginator;
  interfaceTableDisplayedColumns: string[] = ['if-name', 'if-alias', 'ip-address', 'enable', 'dr-address', 'dummy'];
  interfaceTableDataSource: MatTableDataSource<any> = new MatTableDataSource();
  interfaceTableSize;
  interfaceTableData = [];
  interfaceTableRows = [];
  interfaceTableDataLength;
  interfaceTablePanelPageSize = 20;

  @ViewChild('neighborTableSort') neighborTableSort: MatSort;
  @ViewChild('neighborTablePaginator') neighborTablePaginator: MatPaginator;
  neighborTableDisplayedColumns: string[] = ['neighbor', 'if-name', 'uptime', 'expires', 'dr-priority', 'dummy'];
  neighborTableDataSource: MatTableDataSource<any> = new MatTableDataSource();
  neighborTableSize;
  neighborTableData = [];
  neighborTableRows = [];
  neighborTableDataLength;
  neighborTablePanelPageSize = 20;

  @ViewChild('rpTableSort') rpTableSort: MatSort;
  @ViewChild('rpTablePaginator') rpTablePaginator: MatPaginator;
  rpTableDisplayedColumns: string[] = [
    'type',
    'group-address',
    'group-mask',
    'rp-address',
    'priority',
    'hold-time',
    'expires',
    'dummy',
  ];
  rpTableDataSource: MatTableDataSource<any> = new MatTableDataSource();
  rpTableSize;
  rpTableData = [];
  rpTableRows = [];
  rpTableDataLength;
  rpTablePanelPageSize = 20;

  statusCardInfo = {
    bsrAddress: null,
    bsrPriority: null,
    bsrHashMaskLength: null,
  };

  rpMappingForm = new UntypedFormGroup({
    groupAddress: new UntypedFormControl('', [Validators.required, Validators.pattern(ValidatorPattern.IPADDR_REGEX)]),
    result: new UntypedFormControl({ value: '', disabled: true }),
  });

  constructor(
    public utils: UtilsService,
    private appState: AppState,
    private http: HttpClient,
    private snackBar: MatSnackBar,
    private translate: TranslateService,
    private errorService: ErrorService,
    private docService: GenerateDocService,
    private activatedRoute: ActivatedRoute
  ) {
    this.activatedRoute.data.subscribe(data => {
      this.interfaceTableSize = data.tableSize.pimSmTable;
      this.neighborTableSize = data.tableSize.neighborTable;
    });
  }

  ngAfterViewInit(): void {
    this.setupTableSetting();
    this.getPageData().subscribe({
      complete: () => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
      },
      error: error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      },
    });
  }

  getPageData(): Observable<any> {
    return new Observable(observer => {
      this.interfaceTableData = [];
      this.neighborTableData = [];
      this.rpTableData = [];
      this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
      forkJoin({
        l3Interface: this.http.get(environment.uriRequestURL + '/setting/data/ipInterface'),
        pimSmStatus: this.http.get(environment.uriRequestURL + '/status/pimSm'),
      }).subscribe(
        (data: any) => {
          const l3Interface = union(data.l3Interface.l3VlanIfTable, data.l3Interface.loopbackIfTable);
          forEach(data.pimSmStatus.ifTable, entry => {
            const interfaceData = find(l3Interface, (l3If: any) => l3If.ifName === entry.ifName);
            this.interfaceTableData.push({
              name: entry.ifName,
              alias: interfaceData.alias === '' ? this.utils.emptyDash : interfaceData.alias,
              ipAddress: interfaceData.ipv4.ipAddress,
              enable: entry.adminState
                ? this.translate.instant('general.common.enabled')
                : this.translate.instant('general.common.disabled'),
              drAddress: entry.drAddr === 'N/A' ? this.utils.emptyDash : entry.drAddr,
            });
          });

          forEach(data.pimSmStatus.neighborTable, entry => {
            this.neighborTableData.push({
              neighbor: entry.neighbor,
              interface: entry.interface,
              uptime: entry.uptime,
              expireTime: entry.expires,
              drPriority: entry.nbrDrPriority,
            });
          });

          forEach(data.pimSmStatus.rpTable, entry => {
            this.rpTableData.push({
              type: entry.static
                ? this.translate.instant('general.common.static')
                : this.translate.instant('features.pim_sm.bsr'),
              groupAddress: entry.groupAddr,
              groupMask: entry.groupMask,
              rpAddress: entry.rpAddr,
              rpPriority: entry.rpPriority,
              holdTime: entry.rpHoldtime === 0 ? this.utils.emptyDash : entry.rpHoldtime,
              expireTime: entry.rpExpires,
            });
          });

          this.statusCardInfo = {
            bsrAddress: data.pimSmStatus.electedBsrAddr === '' ? this.utils.emptyDash : data.pimSmStatus.electedBsrAddr,
            bsrPriority: data.pimSmStatus.electedBsrPriority,
            bsrHashMaskLength: data.pimSmStatus.electedBsrHashMaskLen,
          };

          this.updateTableData();
          observer.complete();
        },
        error => {
          observer.error(error);
        }
      );
    });
  }

  getRpMapping(): void {
    if (this.rpMappingForm.invalid) {
      this.rpMappingForm.markAllAsTouched();
      return;
    }
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    const groupAddress = this.rpMappingForm.get('groupAddress').value;
    this.http.post(environment.uriRequestURL + '/command/pimSm', { groupAddr: groupAddress }).subscribe(
      rpToken => {
        setTimeout(() => {
          this.http.get(environment.uriRequestURL + '/command/' + rpToken).subscribe(
            (result: any) => {
              this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
              this.rpMappingForm.get('result').setValue(result.output);
            },
            error => {
              this.errorService.handleError(error);
              this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            }
          );
        }, 3000);
      },
      error => {
        this.errorService.handleError(error);
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
      }
    );
  }

  refresh(): void {
    this.getPageData().subscribe({
      complete: () => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.snackBar.open(this.translate.instant('response_handler.res_complete_refresh'), '', {
          duration: 3000,
        });
      },
      error: error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      },
    });
  }

  generateDoc(docType: string, tableName: string): void {
    let tableData;
    switch (tableName) {
      case 'PIM-SM-Neighbor-Table':
        tableData = {
          tableTitle: {
            neighbor: this.translate.instant('general.common.neighbor'),
            interface: this.translate.instant('general.common.interface_name'),
            uptime: this.translate.instant('features.event_log.uptime'),
            expireTime: this.translate.instant('features.syslog_server.expireTime'),
            drPriority: this.translate.instant('features.pim_sm.dr_priority'),
          },
          tableContent: this.neighborTableData,
          pdfPageGroup: [['neighbor', 'interface', 'uptime', 'expireTime', 'drPriority']],
        };
        break;
      case 'RP-Table':
        tableData = {
          tableTitle: {
            type: this.translate.instant('general.common.type'),
            groupAddress: this.translate.instant('features.pim_sm.group_address'),
            groupMask: this.translate.instant('features.pim_sm.group_mask'),
            rpAddress: this.translate.instant('features.pim_sm.rp_address'),
            rpPriority: this.translate.instant('general.common.priority'),
            holdTime: this.translate.instant('features.lldp.hold_time'),
            expireTime: this.translate.instant('features.syslog_server.expireTime'),
          },
          tableContent: this.rpTableData,
          pdfPageGroup: [['type', 'groupAddress', 'groupMask', 'rpAddress', 'rpPriority', 'holdTime', 'expireTime']],
        };
        break;
    }
    this.docService.generateDoc(docType, this.utils.getFilename(tableName), tableData);
  }

  updateFilter(dataSource: MatTableDataSource<any>, filterValue: string): void {
    dataSource.filter = filterValue.trim().toLowerCase();
  }

  onInterfaceTablePageChange(event): void {
    this.interfaceTableRows = this.interfaceTableData.slice(
      this.interfaceTablePanelPageSize * event.pageIndex,
      this.interfaceTablePanelPageSize * event.pageIndex + this.interfaceTablePanelPageSize
    );
  }

  onNeighborTablePageChange(event): void {
    this.neighborTableRows = this.neighborTableData.slice(
      this.neighborTablePanelPageSize * event.pageIndex,
      this.neighborTablePanelPageSize * event.pageIndex + this.neighborTablePanelPageSize
    );
  }

  onRpTablePageChange(event): void {
    this.rpTableRows = this.rpTableData.slice(
      this.rpTablePanelPageSize * event.pageIndex,
      this.rpTablePanelPageSize * event.pageIndex + this.rpTablePanelPageSize
    );
  }

  private updateTableData(): void {
    this.interfaceTableRows = this.interfaceTableData.slice(0, this.interfaceTablePanelPageSize);
    this.interfaceTableDataLength = this.interfaceTableData.length;
    this.interfaceTableDataSource.data = this.interfaceTableData;

    this.neighborTableRows = this.neighborTableData.slice(0, this.neighborTablePanelPageSize);
    this.neighborTableDataLength = this.neighborTableData.length;
    this.neighborTableDataSource.data = this.neighborTableData;

    this.rpTableRows = this.rpTableData.slice(0, this.rpTablePanelPageSize);
    this.rpTableDataLength = this.rpTableData.length;
    this.rpTableDataSource.data = this.rpTableData;
  }

  private setupTableSetting(): void {
    this.interfaceTableDataSource.paginator = this.interfaceTablePaginator;
    this.interfaceTableDataSource.sort = this.interfaceTableSort;
    this.interfaceTableDataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'if-name':
          return item.name;
        case 'if-alias':
          return item.alias;
        case 'ip-address':
          return this.utils.convertIpToNum(item.ipAddress);
        case 'dr-address':
          return this.utils.convertIpToNum(item.drAddress);
        default:
          return item[property];
      }
    };
    this.interfaceTableDataSource.filterPredicate = (data: any, filter: string): boolean => {
      return (
        String(data.name).toLowerCase().indexOf(filter) !== -1 ||
        String(data.alias).toLowerCase().indexOf(filter) !== -1 ||
        String(data.ipAddress).toLowerCase().indexOf(filter) !== -1 ||
        String(data.enable).toLowerCase().indexOf(filter) !== -1 ||
        String(data.drAddress).toLowerCase().indexOf(filter) !== -1
      );
    };

    this.neighborTableDataSource.paginator = this.neighborTablePaginator;
    this.neighborTableDataSource.sort = this.neighborTableSort;
    this.neighborTableDataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'neighbor':
          return this.utils.convertIpToNum(item.neighbor);
        case 'if-name':
          return item.interface;
        case 'expires':
          return item.expireTime;
        case 'dr-priority':
          return item.drPriority;
        default:
          return item[property];
      }
    };
    this.neighborTableDataSource.filterPredicate = (data: any, filter: string): boolean => {
      return (
        String(data.neighbor).toLowerCase().indexOf(filter) !== -1 ||
        String(data.interface).toLowerCase().indexOf(filter) !== -1 ||
        String(data.uptime).toLowerCase().indexOf(filter) !== -1 ||
        String(data.expireTime).toLowerCase().indexOf(filter) !== -1 ||
        String(data.drPriority).toLowerCase().indexOf(filter) !== -1
      );
    };

    this.rpTableDataSource.paginator = this.rpTablePaginator;
    this.rpTableDataSource.sort = this.rpTableSort;
    this.rpTableDataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'group-address':
          return this.utils.convertIpToNum(item.groupAddress);
        case 'group-mask':
          return this.utils.convertIpToNum(item.groupMask);
        case 'rp-address':
          return this.utils.convertIpToNum(item.rpAddress);
        case 'priority':
          return item.rpPriority;
        case 'hold-time':
          return item.holdTime;
        case 'expires':
          return item.expireTime;
        default:
          return item[property];
      }
    };
    this.rpTableDataSource.filterPredicate = (data: any, filter: string): boolean => {
      return (
        String(data.type).toLowerCase().indexOf(filter) !== -1 ||
        String(data.groupAddress).toLowerCase().indexOf(filter) !== -1 ||
        String(data.groupMask).toLowerCase().indexOf(filter) !== -1 ||
        String(data.rpAddress).toLowerCase().indexOf(filter) !== -1 ||
        String(data.rpPriority).toLowerCase().indexOf(filter) !== -1 ||
        String(data.holdTime).toLowerCase().indexOf(filter) !== -1 ||
        String(data.expireTime).toLowerCase().indexOf(filter) !== -1
      );
    };
  }
}
