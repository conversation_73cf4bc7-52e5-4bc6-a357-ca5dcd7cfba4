import { ChangeDetectionStrategy, Component, Inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import {
  MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA,
  MatLegacyDialogRef as MatDialogRef,
} from '@angular/material/legacy-dialog';
import { MatLegacySnackBar as MatSnackBar } from '@angular/material/legacy-snack-bar';

import { TranslateService } from '@ngx-translate/core';

import * as environment from '@switch-web/shared/environments/environment';
import { AppState } from '@switch-web/shared/app/app.service';
import { GlobalEvent, GlobalEventType } from '@switch-web/shared/app/global.event';
import { ErrorService } from '@switch-web/shared/service/error.service';
import { ValidatorPattern } from '@switch-web/shared/validator/validators';

import { IfType } from '../tracking.def';

@Component({
  templateUrl: './interface-tracking-setting-dialog.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InterfaceTrackingSettingDialogComponent {
  get IfType() {
    return IfType;
  }

  interfaceTrackingSettingForm = new UntypedFormGroup({
    enable: new UntypedFormControl(false, Validators.required),
    tid: new UntypedFormControl(null, Validators.required),
    ifType: new UntypedFormControl(),
    port: new UntypedFormControl(null),
    vlanIf: new UntypedFormControl(null, [
      Validators.required,
      Validators.min(1),
      Validators.max(4094),
      Validators.pattern(ValidatorPattern.VAILD_NUMBER),
    ]),
    downToUp: new UntypedFormControl(true, Validators.required),
    upDelay: new UntypedFormControl(0, [
      Validators.required,
      Validators.min(0),
      Validators.max(99),
      Validators.pattern(ValidatorPattern.VAILD_NUMBER),
    ]),
    upToDown: new UntypedFormControl(true, Validators.required),
    downDelay: new UntypedFormControl(0, [
      Validators.required,
      Validators.min(0),
      Validators.max(99),
      Validators.pattern(ValidatorPattern.VAILD_NUMBER),
    ]),
  });
  allPortMap = [];
  portMapLength;
  l3VlanIfData = [];
  tidList = [];
  notSupport = {
    networkInterface: false,
  };

  constructor(
    public dialogRef: MatDialogRef<InterfaceTrackingSettingDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public dialogData: any,
    private appState: AppState,
    private http: HttpClient,
    private snackBar: MatSnackBar,
    private translate: TranslateService,
    private errorService: ErrorService
  ) {
    this.notSupport = this.dialogData.notSupport;
    this.allPortMap = this.dialogData.allPortMap;
    this.portMapLength = this.dialogData.portMapLength;
    this.l3VlanIfData = this.dialogData.l3VlanIfData;
    this.tidList = this.dialogData.tidList;
    if (this.dialogData.row) {
      this.interfaceTrackingSettingForm.patchValue({
        enable: this.dialogData.row.enableRaw,
        tid: this.dialogData.row.tid,
        ifType: this.dialogData.row.ifType === IfType.PORT_CHANNEL ? IfType.PORT : this.dialogData.row.ifType,
        downToUp: this.dialogData.row.downToUpRaw,
        upDelay: this.dialogData.row.upDelay,
        upToDown: this.dialogData.row.upToDownRaw,
        downDelay: this.dialogData.row.downDelay,
      });
      switch (this.dialogData.row.ifType) {
        case IfType.PORT:
          this.interfaceTrackingSettingForm.get('port').setValue(this.dialogData.row.interfaceRaw);
          break;
        case IfType.PORT_CHANNEL:
          this.interfaceTrackingSettingForm.get('port').setValue(this.dialogData.row.interfaceRaw + this.portMapLength);
          break;
        case IfType.INTERFACE_VLAN:
          this.interfaceTrackingSettingForm.get('vlanIf').setValue(this.dialogData.row.interfaceRaw);
          break;
      }
      this.ifTypeChange(this.dialogData.row.ifType);
      this.tidList[dialogData.row.tid - 1].used = false;
    }
  }

  ifTypeChange(ifType: IfType): void {
    switch (ifType) {
      case IfType.PORT:
      case IfType.PORT_CHANNEL:
        this.interfaceTrackingSettingForm.get('port').enable();
        this.interfaceTrackingSettingForm.get('vlanIf').disable();
        break;
      case IfType.INTERFACE_VLAN:
        this.interfaceTrackingSettingForm.get('port').disable();
        this.interfaceTrackingSettingForm.get('vlanIf').enable();
        break;
    }
  }

  onDialogSubmit(): void {
    if (this.interfaceTrackingSettingForm.invalid) {
      this.interfaceTrackingSettingForm.markAllAsTouched();
      return;
    }
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    this.snackBar.open(this.translate.instant('request_handler.action_saving'));
    const patchData = {
      enable: this.interfaceTrackingSettingForm.get('enable').value,
      tid: this.interfaceTrackingSettingForm.get('tid').value,
      ifType: this.interfaceTrackingSettingForm.get('ifType').value,
      interface: null,
      downToUp: this.interfaceTrackingSettingForm.get('downToUp').value,
      upDelay: this.interfaceTrackingSettingForm.get('upDelay').value,
      upToDown: this.interfaceTrackingSettingForm.get('upToDown').value,
      downDelay: this.interfaceTrackingSettingForm.get('downDelay').value,
      valid: true,
    };
    switch (patchData.ifType) {
      case IfType.PORT:
        patchData.interface = this.interfaceTrackingSettingForm.get('port').value;
        if (patchData.interface >= this.portMapLength) {
          patchData.ifType = IfType.PORT_CHANNEL;
          patchData.interface = patchData.interface - this.portMapLength;
        }
        break;
      case IfType.INTERFACE_VLAN:
        patchData.interface = this.interfaceTrackingSettingForm.get('vlanIf').value;
        break;
    }
    let request;
    if (this.dialogData.row) {
      request = this.http.patch(
        environment.uriRequestURL + '/setting/data/mxtracking/interfaceTrackingTable/' + this.dialogData.row.key,
        patchData
      );
    } else {
      request = this.http.post(
        environment.uriRequestURL + '/setting/data/mxtracking/interfaceTrackingTable',
        patchData
      );
    }
    request.subscribe(
      () => {
        this.dialogRef.close('submit');
        this.snackBar.open(this.translate.instant('response_handler.res_global_success'), '', {
          duration: 3000,
        });
      },
      error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      }
    );
  }
}
