<h3 *ngIf="!dialogData.row" mat-dialog-title>
  {{ 'features.tracking.create_interface_tracking_entry_title' | translate }}
</h3>
<h3 *ngIf="dialogData.row" mat-dialog-title>
  {{ 'features.tracking.edit_interface_tracking_entry_title' | translate }}
</h3>
<form [formGroup]="interfaceTrackingSettingForm" (ngSubmit)="onDialogSubmit()">
  <div mat-dialog-content>
    <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
      <mat-form-field>
        <mat-select
          id="select-tracking-enable"
          placeholder="{{ 'features.tracking.interface_tracking' | translate }}"
          formControlName="enable"
          required
        >
          <mat-option id="option-tracking-enable" [value]="true">
            {{ 'general.common.enabled' | translate }}</mat-option
          >
          <mat-option id="option-tracking-disable" [value]="false">
            {{ 'general.common.disabled' | translate }}</mat-option
          >
        </mat-select>
      </mat-form-field>
    </div>
    <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
      <mat-form-field>
        <mat-select
          id="select-tid"
          placeholder="{{ 'features.tracking.tid' | translate }}"
          formControlName="tid"
          required
        >
          <ng-container *ngFor="let tracking of tidList">
            <mat-option id="option-tid-{{ tracking.tid }}" *ngIf="!tracking.used" [value]="tracking.tid">
              {{ tracking.tid }}
            </mat-option>
          </ng-container>
        </mat-select>
        <mat-error *ngIf="interfaceTrackingSettingForm.get('tid').errors?.required">
          {{ 'validators.required' | translate }}
        </mat-error>
      </mat-form-field>
    </div>
    <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
      <mat-form-field>
        <mat-select
          id="select-interface-type"
          (selectionChange)="ifTypeChange($event.value)"
          placeholder="{{ 'features.tracking.interface_type' | translate }}"
          formControlName="ifType"
          required
        >
          <mat-option id="option-interface-port" [value]="IfType.PORT">
            {{ 'features.tracking.port' | translate }}
          </mat-option>
          <mat-option
            *ngIf="!notSupport.networkInterface"
            id="option-interface-l3-interface"
            [value]="IfType.INTERFACE_VLAN"
          >
            {{ 'features.tracking.network_interface' | translate }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="interfaceTrackingSettingForm.get('ifType').errors?.required">
          {{ 'validators.required' | translate }}
        </mat-error>
      </mat-form-field>
      <mat-form-field *ngIf="interfaceTrackingSettingForm.get('ifType').value === IfType.PORT">
        <mat-select
          id="select-port"
          placeholder="{{ 'general.common_port.port' | translate }}"
          formControlName="port"
          required
        >
          <mat-option id="option-port-{{ port.name }}" *ngFor="let port of allPortMap" [value]="port.index + 1">
            {{ port.name }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="interfaceTrackingSettingForm.get('port').errors?.required">
          {{ 'validators.required' | translate }}
        </mat-error>
      </mat-form-field>
      <mat-form-field *ngIf="interfaceTrackingSettingForm.get('ifType').value === IfType.INTERFACE_VLAN">
        <mat-select
          id="select-vlan-interface"
          placeholder="{{ 'features.l3_interface.page_title' | translate }}"
          formControlName="vlanIf"
          required
        >
          <mat-option id="option-interface-vlan-{{ vlan.vid }}" *ngFor="let vlan of l3VlanIfData" [value]="vlan.vid">
            {{ vlan.ifName }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="interfaceTrackingSettingForm.get('vlanIf').errors?.required">
          {{ 'validators.required' | translate }}
        </mat-error>
      </mat-form-field>
    </div>
    <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
      <mat-form-field>
        <mat-select
          id="select-down-to-up-enable"
          placeholder="{{ 'features.tracking.status_change_from_down_to_up' | translate }}"
          formControlName="downToUp"
          required
        >
          <mat-option id="option-down-to-up-enable" [value]="true">
            {{ 'general.common.enabled' | translate }}</mat-option
          >
          <mat-option id="option-down-to-up-disable" [value]="false">
            {{ 'general.common.disabled' | translate }}</mat-option
          >
        </mat-select>
      </mat-form-field>
      <mat-form-field>
        <input
          id="input-up-delay"
          matInput
          type="number"
          min="0"
          max="99"
          placeholder="{{ 'features.tracking.up_delay' | translate }}"
          formControlName="upDelay"
          required
        />
        <mat-hint align="begin">
          {{ 'validators.require_range_between' | translate : { rangeBegin: 0, rangeEnd: 99 } }}
        </mat-hint>
        <mat-hint align="end">
          {{ 'general.unit.sec' | translate }}
        </mat-hint>
        <mat-error *ngIf="interfaceTrackingSettingForm.get('upDelay').errors?.required">
          {{ 'validators.required' | translate }}
        </mat-error>
        <mat-error
          *ngIf="
            interfaceTrackingSettingForm.get('upDelay').errors?.min ||
            interfaceTrackingSettingForm.get('upDelay').errors?.max
          "
        >
          {{ 'validators.invalid_range' | translate : { rangeBegin: 0, rangeEnd: 99 } }}
        </mat-error>
        <mat-error *ngIf="interfaceTrackingSettingForm.get('upDelay').errors?.pattern">
          {{ 'validators.invalid_positive_integer' | translate }}
        </mat-error>
      </mat-form-field>
    </div>
    <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
      <mat-form-field>
        <mat-select
          id="select-up-to-down-enable"
          placeholder="{{ 'features.tracking.status_change_from_up_to_down' | translate }}"
          formControlName="upToDown"
          required
        >
          <mat-option id="option-up-to-down-enable" [value]="true">
            {{ 'general.common.enabled' | translate }}</mat-option
          >
          <mat-option id="option-up-to-down-disable" [value]="false">
            {{ 'general.common.disabled' | translate }}</mat-option
          >
        </mat-select>
      </mat-form-field>
      <mat-form-field>
        <input
          id="input-up-delay"
          matInput
          type="number"
          min="0"
          max="99"
          placeholder="{{ 'features.tracking.down_delay' | translate }}"
          formControlName="downDelay"
          required
        />
        <mat-hint align="begin">
          {{ 'validators.require_range_between' | translate : { rangeBegin: 0, rangeEnd: 99 } }}
        </mat-hint>
        <mat-hint align="end">
          {{ 'general.unit.sec' | translate }}
        </mat-hint>
        <mat-error *ngIf="interfaceTrackingSettingForm.get('downDelay').errors?.required">
          {{ 'validators.required' | translate }}
        </mat-error>
        <mat-error
          *ngIf="
            interfaceTrackingSettingForm.get('downDelay').errors?.min ||
            interfaceTrackingSettingForm.get('downDelay').errors?.max
          "
        >
          {{ 'validators.invalid_range' | translate : { rangeBegin: 0, rangeEnd: 99 } }}
        </mat-error>
        <mat-error *ngIf="interfaceTrackingSettingForm.get('downDelay').errors?.pattern">
          {{ 'validators.invalid_positive_integer' | translate }}
        </mat-error>
      </mat-form-field>
    </div>
  </div>
  <div mat-dialog-actions align="end">
    <button id="dialog-button-cancel" mat-button mat-dialog-close color="primary" type="button">
      {{ 'general.button.cancel' | translate }}
    </button>
    <button id="dialog-button-apply" mat-raised-button color="primary" type="submit">
      <ng-container *ngIf="!dialogData.row">{{ 'general.button.create' | translate }}</ng-container>
      <ng-container *ngIf="dialogData.row">{{ 'general.button.apply' | translate }}</ng-container>
    </button>
  </div>
</form>
