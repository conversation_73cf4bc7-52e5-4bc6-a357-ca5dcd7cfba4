import { AfterViewInit, ChangeDetectionStrategy, Component, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { SelectionModel } from '@angular/cdk/collections';
import { MatLegacyDialog as MatDialog } from '@angular/material/legacy-dialog';
import { MatLegacyPaginator as MatPaginator } from '@angular/material/legacy-paginator';
import { MatLegacySnackBar as MatSnackBar } from '@angular/material/legacy-snack-bar';
import { MatLegacyTableDataSource as MatTableDataSource } from '@angular/material/legacy-table';
import { MatSort } from '@angular/material/sort';

import { TranslateService } from '@ngx-translate/core';
import { cloneDeep, filter, find, forEach, orderBy, sortBy } from 'lodash-es';
import { forkJoin, Observable } from 'rxjs';

import * as environment from '@switch-web/shared/environments/environment';
import { AppState } from '@switch-web/shared/app/app.service';
import { GlobalEvent, GlobalEventType } from '@switch-web/shared/app/global.event';
import { mediumDialogConfig, smallDialogConfig } from '@switch-web/shared/dialog/dialog-config.service';
import { AuthService } from '@switch-web/shared/service/auth.service';
import { ErrorService } from '@switch-web/shared/service/error.service';
import { UtilsService } from '@switch-web/shared/service/utils.service';

import { DeleteTrackingDialogComponent } from './delete-tracking-dialog/delete-tracking-dialog.component';
import { InterfaceTrackingSettingDialogComponent } from './interface-tracking-setting-dialog/interface-tracking-setting-dialog.component';
import { LogicalTrackingSettingDialogComponent } from './logical-tracking-setting-dialog/logical-tracking-setting-dialog.component';
import { PingTrackingSettingDialogComponent } from './ping-tracking-setting-dialog/ping-tracking-setting-dialog.component';
import { IfType, LogicalOper, TrackingStatus, TrackingType } from './tracking.def';

@Component({
  templateUrl: './tracking.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TrackingComponent implements AfterViewInit {
  get TrackingType() {
    return TrackingType;
  }

  @ViewChild('trackingTableSort') trackingTableSort: MatSort;
  @ViewChild('trackingTablePaginator') trackingTablePaginator: MatPaginator;
  trackingTableDisplayedColumns = [];
  trackingTableDataSource: MatTableDataSource<any> = new MatTableDataSource();
  trackingTableSelection = new SelectionModel<any>(true, []);
  trackingTableData = [];
  trackingTableRows = [];
  trackingTableDataLength;
  trackingTableSize;
  trackingTablePanelPageSize = 20;

  @ViewChild('trackingStatusTableSort') trackingStatusTableSort: MatSort;
  @ViewChild('trackingStatusTablePaginator') trackingStatusTablePaginator: MatPaginator;
  trackingStatusTableDisplayedColumns = [
    'sync',
    'tid',
    'enable',
    'tracking-type',
    'tracking-func',
    'status',
    'last-change',
    'change-times',
    'dummy',
  ];
  trackingStatusTableDataSource: MatTableDataSource<any> = new MatTableDataSource();
  trackingStatusTableData = [];
  trackingStatusTableDataRaw = [];
  trackinStatusTableRows = [];
  trackingStatusTableDataLength;
  trackingStatusTablePanelPageSize = 20;

  interfaceTrackingData = [];
  pingTrackingData = [];
  logicalTrackingData = [];

  noPermission = false;
  notSupport = {
    networkInterface: false,
  };

  selectedTrackingItem = TrackingType.INTERFACE;
  selectedTrackingStatusItem = TrackingType.ALL;
  trackingListTitle: string;
  trackingStatusListTitle: string;
  allPortMap = [];
  portMapLength;
  l3VlanIfData = [];
  tidList = [];
  usedTid = [];

  trackingForm = new UntypedFormGroup({
    enable: new UntypedFormControl(false, Validators.required),
  });

  private allPortMapData = [];

  readonly trackingMenuList = ['interface', 'ping', 'logical'];
  readonly interfaceTrackingCol = [
    'select',
    'edit',
    'tid',
    'enable',
    'interface',
    'down-to-up',
    'up-delay',
    'up-to-down',
    'down-delay',
    'dummy',
  ];
  readonly pingTrackingCol = [
    'select',
    'edit',
    'tid',
    'enable',
    'ip-address',
    'interval',
    'timeout',
    'lost-to-received',
    'received',
    'received-to-lost',
    'lost',
    'dummy',
  ];
  readonly logicTrackingCol = ['select', 'edit', 'tid', 'enable', 'logic-list', 'dummy'];

  constructor(
    private appState: AppState,
    private http: HttpClient,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private auth: AuthService,
    private translate: TranslateService,
    private errorService: ErrorService,
    public utils: UtilsService,
    private activatedRoute: ActivatedRoute
  ) {
    this.noPermission = this.auth.authNodes.tracking;
    this.activatedRoute.data.subscribe(data => {
      this.trackingTableSize = data.tableSize.trackingTable;
      forEach(data.notSupport, feat => {
        this.notSupport[feat] = true;
      });
    });
    this.allPortMap = JSON.parse(localStorage.getItem('allPortMap'));
    this.allPortMapData = JSON.parse(localStorage.getItem('allPortMapData'));
    this.portMapLength = JSON.parse(localStorage.getItem('portMapData')).length;
    this.trackingListTitle = this.translate.instant('features.tracking.tracking_list_of_interface');
    this.trackingStatusListTitle = this.translate.instant('features.tracking.tracking_list_of_interface');
  }

  ngAfterViewInit(): void {
    this.setupTableSetting();
    this.refreshTabContent().subscribe({
      complete: () => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
      },
      error: error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      },
    });
    this.trackingTableDisplayedColumns = this.interfaceTrackingCol;
  }

  refreshTabContent(): Observable<any> {
    return new Observable(observer => {
      this.trackingTableData = [];
      this.trackingStatusTableDataRaw = [];
      this.interfaceTrackingData = [];
      this.pingTrackingData = [];
      this.logicalTrackingData = [];
      this.l3VlanIfData = [];
      this.tidList = [];
      this.usedTid = [];
      for (let i = 1; i <= 16; i++) {
        this.tidList.push({
          tid: i,
          used: false,
        });
      }
      this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));

      const requests = {
        mxtracking: this.http.get(environment.uriRequestURL + '/setting/data/mxtracking'),
        trackingStatusTable: this.http.get(environment.uriRequestURL + '/status/mxtracking/trackingTable'),
      };

      if (!this.notSupport.networkInterface) {
        requests['l3VlanIfTable'] = this.http.get(
          environment.uriRequestURL + '/setting/data/ipInterface/l3VlanIfTable'
        );
      }

      forkJoin(requests).subscribe(
        (data: any) => {
          forEach(data.l3VlanIfTable, vlan => {
            this.l3VlanIfData.push({
              ifName: vlan.ifName,
              vid: parseInt(vlan.ifName.substring(4, vlan.ifName.length), 10),
            });
          });
          this.trackingForm.patchValue({
            enable: data.mxtracking.globalEnable,
          });
          forEach(data.mxtracking.interfaceTrackingTable, (row, index) => {
            this.interfaceTrackingData.push({
              key: index,
              trackingType: TrackingType.INTERFACE,
              tid: row.tid,
              enable: row.enable
                ? this.translate.instant('general.common.enabled')
                : this.translate.instant('general.common.disabled'),
              enableRaw: row.enable,
              ifType: row.ifType,
              interface: this.getInterface(row.ifType, row.interface),
              interfaceRaw: row.interface,
              downToUp: row.downToUp
                ? this.translate.instant('general.common.enabled')
                : this.translate.instant('general.common.disabled'),
              downToUpRaw: row.downToUp,
              upDelay: row.upDelay,
              upToDown: row.upToDown
                ? this.translate.instant('general.common.enabled')
                : this.translate.instant('general.common.disabled'),
              upToDownRaw: row.upToDown,
              downDelay: row.downDelay,
            });
          });
          this.interfaceTrackingData = sortBy(this.interfaceTrackingData, data => {
            return data.tid;
          });

          forEach(data.mxtracking.pingTrackingTable, (row, index) => {
            this.pingTrackingData.push({
              key: index,
              trackingType: TrackingType.PING,
              tid: row.tid,
              enable: row.enable
                ? this.translate.instant('general.common.enabled')
                : this.translate.instant('general.common.disabled'),
              enableRaw: row.enable,
              ip: row.ipAddress,
              interval: row.interval,
              timeout: row.timeout,
              lostToRecv: row.lostToRecv
                ? this.translate.instant('general.common.enabled')
                : this.translate.instant('general.common.disabled'),
              lostToRecvRaw: row.lostToRecv,
              received: row.received,
              recvToLost: row.recvToLost
                ? this.translate.instant('general.common.enabled')
                : this.translate.instant('general.common.disabled'),
              recvToLostRaw: row.recvToLost,
              lost: row.lost,
            });
          });
          this.pingTrackingData = sortBy(this.pingTrackingData, data => {
            return data.tid;
          });

          forEach(data.mxtracking.logicalTrackingTable, (row, index) => {
            const logicalList = [];
            forEach(row.tidPbmp, (entry, index) => {
              if (entry) {
                logicalList.push(index + 1);
              }
            });
            this.logicalTrackingData.push({
              key: index,
              trackingType: TrackingType.LOGICAL,
              tid: row.tid,
              enable: row.enable
                ? this.translate.instant('general.common.enabled')
                : this.translate.instant('general.common.disabled'),
              enableRaw: row.enable,
              logicalList: this.getLogicOperString(row.logicalOper) + ' TID ' + logicalList.join(', TID '),
              logicalListRaw: logicalList,
              logicalOper: row.logicalOper,
            });
          });
          this.logicalTrackingData = sortBy(this.logicalTrackingData, data => {
            return data.tid;
          });

          forEach(data.trackingStatusTable, row => {
            this.usedTid.push(row.tid);
            this.tidList[row.tid - 1].used = true;
            this.trackingStatusTableDataRaw.push({
              tid: row.tid,
              trackingType: this.getTrackingType(row.trackingType),
              trackingTypeRaw: row.trackingType,
              trackingFunc: row.trackingFunc,
              status:
                !data.mxtracking.globalEnable || !row.enable
                  ? this.utils.emptyDash
                  : row.trackingStatus === TrackingStatus.UP
                  ? this.translate.instant('general.common.up')
                  : this.translate.instant('general.common.down'),
              lastChangeTime: !data.mxtracking.globalEnable || !row.enable ? this.utils.emptyDash : row.lastChangeTime,
              changeTimes: !data.mxtracking.globalEnable || !row.enable ? this.utils.emptyDash : row.changeTimes,
              enable: !data.mxtracking.globalEnable
                ? this.utils.emptyDash
                : row.enable === 1
                ? this.translate.instant('general.common.enabled')
                : this.translate.instant('general.common.disabled'),
            });
          });
          this.trackingStatusTableDataRaw = sortBy(this.trackingStatusTableDataRaw, data => {
            return data.tid;
          });

          this.tidList.sort((a, b) => {
            return a - b;
          });
          this.trackingMenuChange(this.selectedTrackingItem);
          this.trackingStatusMenuChange(this.selectedTrackingStatusItem);
          observer.complete();
        },
        error => {
          observer.error(error);
        }
      );
    });
  }

  trackingMenuChange(item: TrackingType): void {
    this.selectedTrackingItem = item;
    switch (item) {
      case TrackingType.INTERFACE:
        this.trackingTableDisplayedColumns = this.interfaceTrackingCol;
        this.trackingListTitle = this.translate.instant('features.tracking.tracking_list_of_interface');
        this.trackingTableData = this.interfaceTrackingData;
        break;
      case TrackingType.PING:
        this.trackingTableDisplayedColumns = this.pingTrackingCol;
        this.trackingListTitle = this.translate.instant('features.tracking.tracking_list_of_ping');
        this.trackingTableData = this.pingTrackingData;
        break;
      case TrackingType.LOGICAL:
        this.trackingTableDisplayedColumns = this.logicTrackingCol;
        this.trackingListTitle = this.translate.instant('features.tracking.tracking_list_of_logical');
        this.trackingTableData = this.logicalTrackingData;
        break;
    }
    this.updateTableData();
  }

  trackingStatusMenuChange(item: TrackingType): void {
    this.selectedTrackingStatusItem = item;
    this.trackingStatusTableData = cloneDeep(this.trackingStatusTableDataRaw);
    switch (item) {
      case TrackingType.ALL:
        this.trackingStatusListTitle = this.translate.instant('features.tracking.tracking_list_of_all');
        break;
      case TrackingType.INTERFACE:
        this.trackingStatusListTitle = this.translate.instant('features.tracking.tracking_list_of_interface');
        this.trackingStatusTableData = filter(this.trackingStatusTableData, row => {
          return row.trackingTypeRaw === TrackingType.INTERFACE;
        });
        break;
      case TrackingType.PING:
        this.trackingStatusListTitle = this.translate.instant('features.tracking.tracking_list_of_ping');
        this.trackingStatusTableData = filter(this.trackingStatusTableData, row => {
          return row.trackingTypeRaw === TrackingType.PING;
        });
        break;
      case TrackingType.LOGICAL:
        this.trackingStatusListTitle = this.translate.instant('features.tracking.tracking_list_of_logical');
        this.trackingStatusTableData = filter(this.trackingStatusTableData, row => {
          return row.trackingTypeRaw === TrackingType.LOGICAL;
        });
        break;
    }
    this.updateStatusTableData();
  }

  forceSync(row): void {
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    this.http.post(environment.uriRequestURL + '/command/trackingForceSyncStatus', { tid: row.tid }).subscribe(
      () => {
        this.refreshTabContent().subscribe({
          complete: () => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          },
          error: error => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            this.errorService.handleError(error);
          },
        });
        this.snackBar.open(this.translate.instant('response_handler.res_global_success'), '', {
          duration: 3000,
        });
      },
      error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      }
    );
  }

  submitGenaralSetting(): void {
    if (this.trackingForm.invalid) {
      this.trackingForm.markAllAsTouched();
      return;
    }
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    const requestData = {
      globalEnable: this.trackingForm.get('enable').value,
    };
    this.snackBar.open(this.translate.instant('request_handler.action_saving'));
    this.http.patch(environment.uriRequestURL + '/setting/data/mxtracking', requestData).subscribe(
      () => {
        this.refreshTabContent().subscribe({
          complete: () => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          },
          error: error => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            this.errorService.handleError(error);
          },
        });
        this.snackBar.open(this.translate.instant('response_handler.res_global_success'), '', {
          duration: 3000,
        });
      },
      error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      }
    );
  }

  addOrEditTracking(rowData?): void {
    let dialogRef;
    mediumDialogConfig.data = {
      tidList: cloneDeep(this.tidList),
    };
    if (rowData) {
      mediumDialogConfig.data['row'] = rowData;
    }
    switch (this.selectedTrackingItem) {
      case TrackingType.INTERFACE:
        mediumDialogConfig.data = {
          ...mediumDialogConfig.data,
          allPortMap: this.allPortMap,
          portMapLength: this.portMapLength,
          l3VlanIfData: this.l3VlanIfData,
          notSupport: this.notSupport,
        };
        dialogRef = this.dialog.open(InterfaceTrackingSettingDialogComponent, mediumDialogConfig);
        break;
      case TrackingType.PING:
        dialogRef = this.dialog.open(PingTrackingSettingDialogComponent, mediumDialogConfig);
        break;
      case TrackingType.LOGICAL:
        dialogRef = this.dialog.open(LogicalTrackingSettingDialogComponent, mediumDialogConfig);
        break;
    }
    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        this.refreshTabContent().subscribe({
          complete: () => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          },
          error: error => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            this.errorService.handleError(error);
          },
        });
      }
    });
  }

  deleteTracking(row?): void {
    smallDialogConfig.data = {
      selectedData: [],
    };
    if (row) {
      smallDialogConfig.data.selectedData.push(row);
    } else {
      let selectedData = this.trackingTableSelection.selected;
      selectedData = orderBy(selectedData, ['key'], ['desc']);
      smallDialogConfig.data.selectedData = selectedData;
    }
    const dialogRef = this.dialog.open(DeleteTrackingDialogComponent, smallDialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        setTimeout(() => {
          this.refreshTabContent().subscribe({
            complete: () => {
              this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            },
            error: error => {
              this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
              this.errorService.handleError(error);
            },
          });
        }, 1000);
      }
    });
  }

  refresh(): void {
    this.refreshTabContent().subscribe({
      complete: () => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.snackBar.open(this.translate.instant('response_handler.res_complete_refresh'), '', {
          duration: 3000,
        });
      },
      error: error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      },
    });
  }

  getTrackingType(type: TrackingType): string {
    switch (type) {
      case TrackingType.INTERFACE:
        return this.translate.instant('general.common.interface');
      case TrackingType.PING:
        return this.translate.instant('general.common.ping');
      case TrackingType.LOGICAL:
        return this.translate.instant('general.common.logical');
    }
  }

  getInterface(ifType: IfType, ifIndex: number): string {
    switch (ifType) {
      case IfType.PORT:
        return this.translate.instant('general.common_port.port') + ' ' + this.allPortMapData[ifIndex - 1];
      case IfType.PORT_CHANNEL:
        const portChannel = find(this.allPortMap, port => port.index === ifIndex + this.portMapLength - 1);
        return this.translate.instant('general.common_port.port') + ' ' + portChannel.name;
      case IfType.INTERFACE_VLAN:
        return 'vlan' + String(ifIndex);
    }
  }

  getLogicOperString(logicOper: LogicalOper): string {
    let logicString = '[';
    switch (logicOper) {
      case LogicalOper.AND:
        logicString += this.translate.instant('features.tracking.and');
        break;
      case LogicalOper.OR:
        logicString += this.translate.instant('features.tracking.or');
        break;
      case LogicalOper.NAND:
        logicString += this.translate.instant('features.tracking.nand');
        break;
      case LogicalOper.NOR:
        logicString += this.translate.instant('features.tracking.nor');
        break;
    }
    logicString += ']';
    return logicString;
  }

  updateTableData(): void {
    this.trackingTableRows = this.trackingTableData.slice(0, this.trackingTablePanelPageSize);
    this.trackingTableDataLength = this.trackingTableData.length;
    this.trackingTableDataSource.data = this.trackingTableData;
    this.trackingTableSelection.clear();
  }

  updateStatusTableData(): void {
    this.trackinStatusTableRows = this.trackingStatusTableData.slice(0, this.trackingStatusTablePanelPageSize);
    this.trackingStatusTableDataLength = this.trackingStatusTableData.length;
    this.trackingStatusTableDataSource.data = this.trackingStatusTableData;
  }

  setupTableSetting(): void {
    this.trackingTableDataSource.paginator = this.trackingTablePaginator;
    this.trackingTableDataSource.sort = this.trackingTableSort;
    this.trackingTableDataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'down-to-up':
          return item.downToUp;
        case 'up-to-down':
          return item.upToDown;
        case 'up-delay':
          return item.upDelay;
        case 'down-delay':
          return item.downDelay;
        case 'ip-address':
          return item.ip;
        case 'lost-to-received':
          return item.lostToRecv;
        case 'received-to-lost':
          return item.recvToLost;
        case 'logic-list':
          return item.logicalList;
        default:
          return item[property];
      }
    };
    this.trackingTableDataSource.filterPredicate = (data: any, filter: string): boolean => {
      return (
        String(data.tid).toLowerCase().indexOf(filter) !== -1 ||
        String(data.enable).toLowerCase().indexOf(filter) !== -1 ||
        String(data.interface).toLowerCase().indexOf(filter) !== -1 ||
        String(data.interval).toLowerCase().indexOf(filter) !== -1 ||
        String(data.downToUp).toLowerCase().indexOf(filter) !== -1 ||
        String(data.upDelay).toLowerCase().indexOf(filter) !== -1 ||
        String(data.upToDown).toLowerCase().indexOf(filter) !== -1 ||
        String(data.downDelay).toLowerCase().indexOf(filter) !== -1 ||
        String(data.ip).toLowerCase().indexOf(filter) !== -1 ||
        String(data.timeout).toLowerCase().indexOf(filter) !== -1 ||
        String(data.lostToRecv).toLowerCase().indexOf(filter) !== -1 ||
        String(data.received).toLowerCase().indexOf(filter) !== -1 ||
        String(data.recvToLost).toLowerCase().indexOf(filter) !== -1 ||
        String(data.lost).toLowerCase().indexOf(filter) !== -1 ||
        String(data.logicalList).toLowerCase().indexOf(filter) !== -1
      );
    };

    this.trackingStatusTableDataSource.paginator = this.trackingStatusTablePaginator;
    this.trackingStatusTableDataSource.sort = this.trackingStatusTableSort;
    this.trackingStatusTableDataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'tracking-type':
          return item.trackingType;
        case 'tracking-func':
          return item.trackingFunc;
        case 'last-change':
          return item.lastChangeTime;
        case 'change-times':
          return item.changeTimes;
        default:
          return item[property];
      }
    };
    this.trackingStatusTableDataSource.filterPredicate = (data: any, filter: string): boolean => {
      return (
        String(data.tid).toLowerCase().indexOf(filter) !== -1 ||
        String(data.trackingType).toLowerCase().indexOf(filter) !== -1 ||
        String(data.trackingFunc).toLowerCase().indexOf(filter) !== -1 ||
        String(data.status).toLowerCase().indexOf(filter) !== -1 ||
        String(data.lastChangeTime).toLowerCase().indexOf(filter) !== -1 ||
        String(data.changeTimes).toLowerCase().indexOf(filter) !== -1 ||
        String(data.enable).toLowerCase().indexOf(filter) !== -1
      );
    };
  }

  updateFilter(dataSource: MatTableDataSource<any>, filterValue: string): void {
    dataSource.filter = filterValue.trim().toLowerCase();
  }

  onTrackingTablePageChange(event): void {
    this.trackingTableRows = this.trackingTableData.slice(
      this.trackingTablePanelPageSize * event.pageIndex,
      this.trackingTablePanelPageSize * event.pageIndex + this.trackingTablePanelPageSize
    );
  }

  onTrackingStatusTablePageChange(event): void {
    this.trackinStatusTableRows = this.trackingStatusTableData.slice(
      this.trackingStatusTablePanelPageSize * event.pageIndex,
      this.trackingStatusTablePanelPageSize * event.pageIndex + this.trackingStatusTablePanelPageSize
    );
  }

  masterToggle(): void {
    this.isAllSelected()
      ? this.trackingTableSelection.clear()
      : this.trackingTableDataSource.data.forEach(row => this.trackingTableSelection.select(row));
  }

  isAllSelected(): boolean {
    const numSelected = this.trackingTableSelection.selected.length;
    const numRows = this.trackingTableDataSource.data.length;
    return numSelected === numRows;
  }

  trackByFn(index, item) {
    return item.key;
  }
}
