import { ChangeDetectionStrategy, Component, Inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import {
  MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA,
  MatLegacyDialogRef as MatDialogRef,
} from '@angular/material/legacy-dialog';
import { MatLegacySnackBar as MatSnackBar } from '@angular/material/legacy-snack-bar';

import { TranslateService } from '@ngx-translate/core';

import * as environment from '@switch-web/shared/environments/environment';
import { AppState } from '@switch-web/shared/app/app.service';
import { GlobalEvent, GlobalEventType } from '@switch-web/shared/app/global.event';
import { ErrorService } from '@switch-web/shared/service/error.service';
import { ValidatorPattern } from '@switch-web/shared/validator/validators';

@Component({
  templateUrl: './interface-setting-dialog.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InterfaceSettingDialogComponent {
  interfaceSettingForm = new UntypedFormGroup({
    enable: new UntypedFormControl(false, Validators.required),
    interval: new UntypedFormControl(null, [
      Validators.required,
      Validators.min(10),
      Validators.max(3600),
      Validators.pattern(ValidatorPattern.VAILD_NUMBER),
    ]),
  });

  constructor(
    public dialogRef: MatDialogRef<InterfaceSettingDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public dialogData: any,
    private appState: AppState,
    private http: HttpClient,
    private snackBar: MatSnackBar,
    private translate: TranslateService,
    private errorService: ErrorService
  ) {
    this.interfaceSettingForm.patchValue({
      enable: dialogData.row.enableRaw,
      interval: dialogData.row.helloInterval,
    });
  }

  onDialogSubmit(): void {
    if (this.interfaceSettingForm.invalid) {
      this.interfaceSettingForm.markAllAsTouched();
      return;
    }
    const patchData = {
      adminState: this.interfaceSettingForm.get('enable').value,
      helloInterval: this.interfaceSettingForm.get('interval').value,
    };
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    this.http
      .patch(environment.uriRequestURL + '/setting/data/pimDm/ifTable/' + this.dialogData.row.key, patchData)
      .subscribe(
        () => {
          this.dialogRef.close('submit');
          this.snackBar.open(this.translate.instant('response_handler.res_global_success'), '', {
            duration: 3000,
          });
        },
        error => {
          this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          this.errorService.handleError(error);
        }
      );
  }
}
