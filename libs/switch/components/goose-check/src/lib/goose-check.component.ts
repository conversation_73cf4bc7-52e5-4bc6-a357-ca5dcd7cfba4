import { AfterViewInit, ChangeDetectionStrategy, Component, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { SelectionModel } from '@angular/cdk/collections';
import { MatLegacyDialog as MatDialog } from '@angular/material/legacy-dialog';
import { MatLegacyPaginator as MatPaginator } from '@angular/material/legacy-paginator';
import { MatLegacySnackBar as MatSnackBar } from '@angular/material/legacy-snack-bar';
import { MatLegacyTableDataSource as MatTableDataSource } from '@angular/material/legacy-table';
import { MatSort } from '@angular/material/sort';

import { TranslateService } from '@ngx-translate/core';
import { cloneDeep, forEach, orderBy } from 'lodash-es';
import { forkJoin, Observable } from 'rxjs';

import * as environment from '@switch-web/shared/environments/environment';
import { AppState } from '@switch-web/shared/app/app.service';
import { GlobalEvent, GlobalEventType } from '@switch-web/shared/app/global.event';
import { mediumDialogConfig, smallDialogConfig } from '@switch-web/shared/dialog/dialog-config.service';
import { AuthService } from '@switch-web/shared/service/auth.service';
import { ErrorService } from '@switch-web/shared/service/error.service';
import { UtilsService } from '@switch-web/shared/service/utils.service';

import { GooseStatus, GooseType } from './goose-check.def';
import { GooseDeleteDialogComponent } from './goose-delete-dialog/goose-delete-dialog.component';
import { GooseSettingDialogComponent } from './goose-setting-dialog/goose-setting-dialog.component';

@Component({
  templateUrl: './goose-check.component.html',
  styleUrls: ['./goose-check.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GooseCheckComponent implements AfterViewInit {
  @ViewChild('gooseCheckTableSort') gooseCheckTableSort: MatSort;
  @ViewChild('gooseCheckTablePaginator') gooseCheckTablePaginator: MatPaginator;
  gooseCheckTableDisplayedColumns: string[] = ['select', 'app-id', 'goose-address', 'dummy'];
  gooseCheckTableDataSource: MatTableDataSource<any> = new MatTableDataSource();
  gooseCheckTableSelection = new SelectionModel<any>(true, []);
  gooseCheckTableSize;
  gooseCheckTableData = [];
  gooseCheckTableRows = [];
  gooseCheckTableDataLength;
  gooseCheckTablePanelPageSize = 20;

  @ViewChild('monitoringTableSort') monitoringTableSort: MatSort;
  @ViewChild('monitoringTablePaginator') monitoringTablePaginator: MatPaginator;
  monitoringTableDisplayedColumns: string[] = [
    'select',
    'type',
    'app-id',
    'goose-address',
    'goose-name',
    'vid',
    'ingress-port',
    'rx-counter',
    'goose-status',
    'dummy',
  ];
  monitoringTableDataSource: MatTableDataSource<any> = new MatTableDataSource();
  monitoringTableSelection = new SelectionModel<any>(true, []);
  monitoringTableSize;
  monitoringTableData = [];
  monitoringTableRows = [];
  monitoringTableDataLength;
  monitoringTablePanelPageSize = 20;

  gooseCheckForm = new UntypedFormGroup({
    enable: new UntypedFormControl(null, Validators.required),
    gooseLock: new UntypedFormControl(null, Validators.required),
    tamperResponse: new UntypedFormControl(null, Validators.required),
  });

  filterForm = new UntypedFormGroup({
    appId: new UntypedFormControl(null),
    gooseAddress: new UntypedFormControl(null),
  });

  statusCardData = {
    gooseLockStatus: null,
    lockViolationStatus: null,
    lockViolationStatusRaw: null,
  };

  noPermission: boolean;

  private portMapData;
  private uriRequestData = {
    gooseStaticTable: null,
    monitoringTable: null,
  };

  constructor(
    public utils: UtilsService,
    private appState: AppState,
    private http: HttpClient,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private auth: AuthService,
    private translate: TranslateService,
    private errorService: ErrorService,
    private activatedRoute: ActivatedRoute
  ) {
    this.noPermission = this.auth.authNodes.gooseCheck;
    this.activatedRoute.data.subscribe(data => {
      this.gooseCheckTableSize = data.tableSize.gooseCheckTable;
    });
    this.portMapData = JSON.parse(localStorage.getItem('portMapData'));
  }

  ngAfterViewInit(): void {
    this.setupTableSetting();
    this.getPageData().subscribe({
      complete: () => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
      },
      error: error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      },
    });
  }

  getPageData(): Observable<any> {
    return new Observable(observer => {
      this.gooseCheckTableData = [];
      this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
      forkJoin({
        gooseCheck: this.http.get(environment.uriRequestURL + '/setting/data/mxgoosecheck'),
      }).subscribe(
        (data: any) => {
          this.gooseCheckForm.patchValue({
            enable: data.gooseCheck.gooseCheckEnable,
            gooseLock: data.gooseCheck.gooseLockEnable,
            tamperResponse: data.gooseCheck.tamperResponse,
          });
          this.statusCardData.gooseLockStatus = data.gooseCheck.gooseLockEnable
            ? this.translate.instant('general.common.enabled')
            : this.translate.instant('general.common.disabled');
          this.uriRequestData.gooseStaticTable = data.gooseCheck.staticTable;
          forEach(data.gooseCheck.staticTable, (entry, index: number) => {
            this.gooseCheckTableData.push({
              key: index,
              appId: this.utils.convertToHex(entry.appid, 4),
              appIdRaw: entry.appid,
              gooseAddr: this.utils.addMacAddressColons(entry.addr),
              gooseAddrRaw: entry.addr,
            });
          });
          this.gooseCheckTableRows = this.gooseCheckTableData.slice(0, this.gooseCheckTablePanelPageSize);
          this.gooseCheckTableDataLength = this.gooseCheckTableData.length;
          this.gooseCheckTableDataSource.data = this.gooseCheckTableData;
          this.gooseCheckTableSelection.clear();

          this.refreshStatusData().subscribe({
            complete: () => {
              observer.complete();
            },
            error: error => {
              observer.error(error);
            },
          });
        },
        error => {
          observer.error(error);
        }
      );
    });
  }

  refreshStatusData(): Observable<any> {
    this.monitoringTableData = [];
    return new Observable(observer => {
      this.http.get(environment.uriRequestURL + '/status/mxgoosecheck').subscribe(
        (monitoring: any) => {
          this.statusCardData.lockViolationStatusRaw = monitoring.gooseCheckLockViolationStatus;
          this.statusCardData.lockViolationStatus = monitoring.gooseCheckLockViolationStatus
            ? this.translate.instant('general.common.warning')
            : this.translate.instant('general.common.normal');
          this.uriRequestData.monitoringTable = monitoring.monitoringTable;
          forEach(monitoring.monitoringTable, (entry, index: number) => {
            this.monitoringTableData.push({
              key: index,
              type:
                entry.gooseType === GooseType.DYNAMIC
                  ? this.translate.instant('general.common.dynamic')
                  : this.translate.instant('general.common.static'),
              typeRaw: entry.gooseType,
              appId: this.utils.convertToHex(entry.gooseAppId, 4),
              appIdRaw: entry.gooseAppId,
              gooseAddr: entry.gooseAddress,
              gooseAddrRaw: this.utils.removeMacAddressColons(entry.gooseAddress).toLowerCase(),
              gooseName: entry.gooseCbName,
              vid: entry.gooseVid,
              ingressPort: this.utils.change3APortTo3IPort(this.portMapData[entry.gooseIngressPort - 1]),
              rxCounter: entry.gooseRxCounter,
              gooseStatus: this.getGooseStatus(entry.gooseHealthyStatus),
              gooseStatusRaw: entry.gooseHealthyStatus,
            });
          });
          this.monitoringTableRows = this.monitoringTableData.slice(0, this.monitoringTablePanelPageSize);
          this.monitoringTableDataLength = this.monitoringTableData.length;
          this.monitoringTableDataSource.data = this.monitoringTableData;
          this.monitoringTableSelection.clear();

          observer.complete();
        },
        error => {
          observer.error(error);
        }
      );
    });
  }

  createGoose(): void {
    mediumDialogConfig.data = {
      gooseStaticTable: cloneDeep(this.uriRequestData.gooseStaticTable),
      monitoringTable: cloneDeep(this.uriRequestData.monitoringTable),
    };
    const dialogRef = this.dialog.open(GooseSettingDialogComponent, mediumDialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        this.getPageData().subscribe({
          complete: () => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          },
          error: error => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            this.errorService.handleError(error);
          },
        });
      }
    });
  }

  deleteStaticGoose(row?): void {
    let selectionData = this.gooseCheckTableSelection.selected;
    selectionData = orderBy(selectionData, ['key'], ['desc']);
    smallDialogConfig.data = {
      selectedData: row ? [row] : selectionData,
      gooseStaticTable: cloneDeep(this.uriRequestData.gooseStaticTable),
    };
    const dialogRef = this.dialog.open(GooseDeleteDialogComponent, smallDialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        this.getPageData().subscribe({
          complete: () => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          },
          error: error => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            this.errorService.handleError(error);
          },
        });
      }
    });
  }

  deleteGoose(): void {
    let selectionData = this.monitoringTableSelection.selected;
    selectionData = orderBy(selectionData, ['key'], ['desc']);
    smallDialogConfig.data = {
      selectedData: selectionData,
      gooseStaticTable: cloneDeep(this.uriRequestData.gooseStaticTable),
    };
    const dialogRef = this.dialog.open(GooseDeleteDialogComponent, smallDialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        this.getPageData().subscribe({
          complete: () => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          },
          error: error => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            this.errorService.handleError(error);
          },
        });
      }
    });
  }

  resetGoose(): void {
    const request = [];
    forEach(this.monitoringTableSelection.selected, row => {
      const postData = {
        commandType: 1,
        appid: row.appIdRaw,
        addr: row.gooseAddrRaw,
      };
      request.push(this.http.post(environment.uriRequestURL + '/command/gooseCheckStreamControl', postData));
    });
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    forkJoin(request).subscribe(
      () => {
        this.snackBar.open(this.translate.instant('response_handler.res_global_success'), '', {
          duration: 3000,
        });
        this.getPageData().subscribe({
          complete: () => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          },
          error: error => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            this.errorService.handleError(error);
          },
        });
      },
      error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      }
    );
  }

  setGooseStatic(): void {
    const staticTable = cloneDeep(this.uriRequestData.gooseStaticTable);
    forEach(this.monitoringTableSelection.selected, row => {
      staticTable.push({
        appid: row.appIdRaw,
        addr: row.gooseAddrRaw,
      });
    });
    this.http.patch(environment.uriRequestURL + '/setting/data/mxgoosecheck/staticTable/', staticTable).subscribe(
      () => {
        this.snackBar.open(this.translate.instant('response_handler.res_global_success'), '', {
          duration: 3000,
        });
      },
      error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      }
    );
  }

  submitGlobalSetting(): void {
    if (this.gooseCheckForm.invalid) {
      this.gooseCheckForm.markAllAsTouched();
      return;
    }
    const patchData = {
      gooseCheckEnable: this.gooseCheckForm.get('enable').value,
      gooseLockEnable: this.gooseCheckForm.get('gooseLock').value,
      tamperResponse: this.gooseCheckForm.get('tamperResponse').value,
    };
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    this.http.patch(environment.uriRequestURL + '/setting/data/mxgoosecheck', patchData).subscribe({
      complete: () => {
        this.getPageData().subscribe({
          complete: () => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          },
          error: error => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            this.errorService.handleError(error);
          },
        });
        this.snackBar.open(this.translate.instant('response_handler.res_global_success'), '', {
          duration: 3000,
        });
      },
      error: error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      },
    });
  }

  refresh(): void {
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    this.getPageData().subscribe({
      complete: () => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.snackBar.open(this.translate.instant('response_handler.res_complete_refresh'), '', {
          duration: 3000,
        });
      },
      error: error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      },
    });
  }

  masterToggle(selectionTable: string): void {
    switch (selectionTable) {
      case 'gooseCheckTable':
        this.isAllSelected('gooseCheckTable')
          ? this.gooseCheckTableSelection.clear()
          : this.gooseCheckTableDataSource.data.forEach(row => {
              this.gooseCheckTableSelection.select(row);
            });
        break;
      case 'monitoringTable':
        this.isAllSelected('monitoringTable')
          ? this.monitoringTableSelection.clear()
          : this.monitoringTableDataSource.data.forEach(row => {
              this.monitoringTableSelection.select(row);
            });
        break;
    }
  }

  isAllSelected(selectionTable: string): boolean {
    let numSelected;
    let numRows;
    switch (selectionTable) {
      case 'gooseCheckTable':
        numSelected = this.gooseCheckTableSelection.selected.length;
        numRows = this.gooseCheckTableDataSource.data.length;
        break;
      case 'monitoringTable':
        numSelected = this.monitoringTableSelection.selected.length;
        numRows = this.monitoringTableDataSource.data.length;
        break;
    }
    return numSelected === numRows;
  }

  onGooseCheckTablePageChange(event): void {
    this.gooseCheckTableRows = this.gooseCheckTableData.slice(
      this.gooseCheckTablePanelPageSize * event.pageIndex,
      this.gooseCheckTablePanelPageSize * event.pageIndex + this.gooseCheckTablePanelPageSize
    );
  }

  onMonitoringTablePageChange(event): void {
    this.monitoringTableRows = this.monitoringTableData.slice(
      this.monitoringTablePanelPageSize * event.pageIndex,
      this.monitoringTablePanelPageSize * event.pageIndex + this.monitoringTablePanelPageSize
    );
  }

  updateFilter(dataSource: MatTableDataSource<any>, filterValue: string): void {
    dataSource.filter = filterValue.trim().toLowerCase();
  }

  private getGooseStatus(status: GooseStatus) {
    switch (status) {
      case GooseStatus.HEALTHY:
        return this.translate.instant('features.turbo_ring_v2.healthy');
      case GooseStatus.TIMEOUT:
        return this.translate.instant('general.common.timeout');
      case GooseStatus.PORT_TAMPERED:
        return this.translate.instant('features.goose_check.port_tampered');
      case GooseStatus.SA_TAMPERED:
        return this.translate.instant('features.goose_check.sa_tampered');
    }
  }

  private setupTableSetting(): void {
    this.gooseCheckTableDataSource.paginator = this.gooseCheckTablePaginator;
    this.gooseCheckTableDataSource.sort = this.gooseCheckTableSort;
    this.gooseCheckTableDataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'app-id':
          return item.appId;
        case 'goose-address':
          return item.gooseAddr;
        default:
          return item[property];
      }
    };
    this.gooseCheckTableDataSource.filterPredicate = (data: any, filter: string): boolean => {
      return (
        String(data.appId).toLowerCase().indexOf(filter) !== -1 ||
        String(data.gooseAddr).toLowerCase().indexOf(filter) !== -1
      );
    };

    this.monitoringTableDataSource.paginator = this.monitoringTablePaginator;
    this.monitoringTableDataSource.sort = this.monitoringTableSort;
    this.monitoringTableDataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'app-id':
          return item.appId;
        case 'goose-address':
          return item.gooseAddr;
        case 'goose-name':
          return item.gooseName;
        case 'ingress-port':
          return item.ingressPort;
        case 'rx-counter':
          return item.rxCounter;
        case 'goose-status':
          return item.gooseStatus;
        default:
          return item[property];
      }
    };
    this.monitoringTableDataSource.filterPredicate = (data: any, filter: string): boolean => {
      return (
        String(data.type).toLowerCase().indexOf(filter) !== -1 ||
        String(data.appId).toLowerCase().indexOf(filter) !== -1 ||
        String(data.gooseAddr).toLowerCase().indexOf(filter) !== -1 ||
        String(data.gooseName).toLowerCase().indexOf(filter) !== -1 ||
        String(data.vid).toLowerCase().indexOf(filter) !== -1 ||
        String(data.ingressPort).toLowerCase().indexOf(filter) !== -1 ||
        String(data.rxCounter).toLowerCase().indexOf(filter) !== -1 ||
        String(data.gooseStatus).toLowerCase().indexOf(filter) !== -1
      );
    };
  }
}
