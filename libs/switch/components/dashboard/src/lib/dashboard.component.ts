import {
  AfterV<PERSON>wInit,
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  OnDestroy,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { MatLegacyDialog as MatDialog } from '@angular/material/legacy-dialog';
import { MatLegacySnackBar as MatSnackBar } from '@angular/material/legacy-snack-bar';

import { TranslateService } from '@ngx-translate/core';
import Chart from 'chart.js/auto';
import { cloneDeep, filter, forEach, invertBy } from 'lodash-es';
import * as moment from 'moment';
import { concat, forkJoin, Observable } from 'rxjs';

import * as environment from '@switch-web/shared/environments/environment';
import { AppState } from '@switch-web/shared/app/app.service';
import { GlobalEvent, GlobalEventType } from '@switch-web/shared/app/global.event';
import { MxLoadingComponent } from '@switch-web/shared/component/mx-loading/mx-loading.component';
import { mediumDialogConfig, smallDialogConfig } from '@switch-web/shared/dialog/dialog-config.service';
import { AuthService } from '@switch-web/shared/service/auth.service';
import { ErrorService } from '@switch-web/shared/service/error.service';
import { UtilsService } from '@switch-web/shared/service/utils.service';

import { DefaultPasswordDialogComponent } from './default-password-dialog/default-password-dialog.component';
import { DevicePanelService } from './device-panel/device-panel.service';
import { LoginRecordsDialogComponent } from './login-records-dialog/login-records-dialog.component';
import { PasswordExpiredDialogComponent } from './password-expired-dialog/password-expired-dialog.component';
import { UpgradeMessageDialogComponent } from './upgrade-message-dialog/upgrade-message-dialog.component';

@Component({
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DashboardComponent implements OnDestroy, AfterViewInit {
  browseMode = 'dashboard';
  portDetail = false;
  notSupport = {
    managementIp: false,
    oob: false,
    intelTemp: false,
  };

  @ViewChild('panel') panel: ElementRef;
  @ViewChild('panelPos') panelPos: ElementRef;
  @ViewChild('power2Module') power2Module: ElementRef;
  @ViewChild('cpuHistorical') cpuHistoricalCanvas: any;
  // @ViewChild('interfaceErrorPacketChart') interfaceErrorPacketCanvas: any;
  // @ViewChild('interfaceUtilizationChart') interfaceUtilizationCanvas: any;

  @ViewChild('eventSummaryLoading') eventSummaryLoading: MxLoadingComponent;
  @ViewChild('panelLoading') panelLoading: MxLoadingComponent;
  @ViewChild('systemInfoLoading') systemInfoLoading: MxLoadingComponent;
  @ViewChild('cpuHistoricalLoading') cpuHistoricalLoading: MxLoadingComponent;
  // @ViewChild('interfaceErrorPacketLoading') interfaceErrorPacketLoading: MxLoadingComponent;
  // @ViewChild('interfaceUtilizationLoading') interfaceUtilizationLoading: MxLoadingComponent;

  @ViewChild('productModel') mdProductModel: TemplateRef<any>;
  @ViewChild('productRevision') mdProductRevision: TemplateRef<any>;
  @ViewChild('deviceName') mdDeviceName: TemplateRef<any>;
  @ViewChild('ipAddress') mdIpAddress: TemplateRef<any>;
  @ViewChild('l3IpAddress') mdL3IpAddress: TemplateRef<any>;
  @ViewChild('serialNumber') mdSerialNumber: TemplateRef<any>;
  @ViewChild('location') mdLocation: TemplateRef<any>;
  @ViewChild('firmwareVersion') mdFirmwareVersion: TemplateRef<any>;
  @ViewChild('systemUptime') mdSystemUptime: TemplateRef<any>;
  @ViewChild('macAddress') mdMacAddress: TemplateRef<any>;
  @ViewChild('oobIpAddress') mdOobIpAddress: TemplateRef<any>;
  @ViewChild('oobMacAddress') mdOobMacAddress: TemplateRef<any>;
  @ViewChild('redundantProtocol') mdRedundantProtocol: TemplateRef<any>;
  @ViewChild('powerModel') mdPowerModel: TemplateRef<any>;
  @ViewChild('externalStorage') mdExternalStorag: TemplateRef<any>;
  @ViewChild('iec62439_3Protocol') mdIec62439_3Protocol: TemplateRef<any>;
  @ViewChild('systemTemperature') mdSystemTemperature: TemplateRef<any>;
  mdTemplates: TemplateRef<any>[] = [];

  cpuHistoricalChart;
  // interfaceErrorPacketChart;
  // interfaceUtilizationChart;
  cpuUtilizationData;
  cpuUtilizationUpdateAt;
  cpuUtilizationHistoricalData = { labels: [], data: [] };
  cpuUtilizationHistoricalUpdateAt;
  // interfaceErrorPacketData = { labels: [], datasets: [] };
  // interfaceErrorPacketUpdateAt;
  // interfaceUtilizationData = {
  //   labels: ['1/1', '1/2', '1/3', '1/4', '2/1'],
  //   datasets: [{
  //     borderColor: 'rgba(0, 135, 135, 1)', backgroundColor: 'rgba(0, 135, 135, 0.9)',
  //     borderWidth: 1, data: [0, 0, 0, 0, 0]
  //   }]
  // };
  // interfaceUtilizationUpdateAt;
  trafficStatisticsTimeout;

  freeCPU;
  freeMEM;

  panelStatusData;
  panelDescription;
  panelSummary;

  linkupCount = 0;
  linkdownCount = 0;

  secerityNoticeCount = 0;
  secerityWarningCount = 0;
  secerityErrorCount = 0;
  secerityCriticalCount = 0;

  systemInfo;
  // system info data
  modelNameData;
  productRevisionData;
  serialNOData;
  deviceNameData;
  firmwareVersionData: string;
  locationData;
  systemUptimeData;
  ipAddressv4Data;
  ipAddressv6Data;
  redundantProtocolData;
  powerModelData;
  macAddressData;
  externalStorageData;
  iec62439_3ProtocolData;
  oobIpData;
  oobMacData;
  systemTemperatureData;

  private pollingIntervalRef;
  private readonly pollingInterval = 30000;
  private pollingSystemInfoRef;
  private readonly pollingSystemInfoInterval = 605000;
  private readonly cardUpdateInterval = 10000;
  // private portMapData;
  // private portChannelMapData;
  // private interfaceNumber;
  private isUpgradeToV4 = false;

  constructor(
    private appState: AppState,
    private translate: TranslateService,
    private http: HttpClient,
    private snackBar: MatSnackBar,
    private utils: UtilsService,
    private errorService: ErrorService,
    private dialog: MatDialog,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private devicePanelService: DevicePanelService,
    private auth: AuthService
  ) {
    this.initPanelSummary();
    // this.portMapData = JSON.parse(localStorage.getItem('portMapData'));
    // this.portChannelMapData = JSON.parse(localStorage.getItem('portChannelMapData'));
    // this.interfaceUtilizationData = {
    //   labels: this.portMapData.slice(0, 5),
    //   datasets: [{
    //     borderColor: 'rgba(0, 135, 135, 1)', backgroundColor: 'rgba(0, 135, 135, 0.9)',
    //     borderWidth: 1, data: [0, 0, 0, 0, 0]
    //   }]
    // };
    // If switch ports > 5, chart max data row should be 5, otherwise max number should equals to the number of ports.
    // const maxInterfaceNumber = 5;
    // this.interfaceNumber = this.portMapData.length > maxInterfaceNumber ? maxInterfaceNumber : this.portMapData.length;

    // Device Info
    this.activatedRoute.data.subscribe(data => {
      this.systemInfo = data.systemInfo;
      forEach(data.notSupport, feat => {
        this.notSupport[feat] = true;
      });
    });
  }

  ngAfterViewInit(): void {
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
    if (localStorage.getItem('forceReload') === '1') {
      localStorage.setItem('forceReload', '0');
      window.location.reload();
      return;
    }
    if (!this.auth.readLoginRecords) {
      mediumDialogConfig.disableClose = true;
      this.dialog
        .open(LoginRecordsDialogComponent, mediumDialogConfig)
        .afterClosed()
        .subscribe(result => {
          const openUpgradeMessageDialog = () => {
            if (this.isUpgradeToV4) {
              this.dialog.open(UpgradeMessageDialogComponent, mediumDialogConfig);
            }
          };
          if (result === 'passwordExpired') {
            this.dialog
              .open(PasswordExpiredDialogComponent, smallDialogConfig)
              .afterClosed()
              .subscribe(() => {
                openUpgradeMessageDialog();
              });
          } else if (result === 'defaultPasswordFlag') {
            this.dialog
              .open(DefaultPasswordDialogComponent, smallDialogConfig)
              .afterClosed()
              .subscribe(() => {
                openUpgradeMessageDialog();
              });
          } else {
            openUpgradeMessageDialog();
          }
        });
    }
    this.getCPUInfo().subscribe({
      complete: () => {
        this.cpuHistoricalLoading.complete();
        this.initCPUUtilizationHistorical();
      },
      error: error => {
        this.cpuHistoricalLoading.complete();
        this.errorService.handleError(error);
      },
    });
    // this.getInterfaceErrorPacket().then(() => {
    //   this.initInterfaceErrorPacketChart();
    // });
    // this.initInterfaceUtilizationChart();
    // this.getInterfaceUtilization().then(() => {
    //   this.interfaceUtilizationChart.update();
    // });
    this.getSystemInfo().subscribe({
      complete: () => {
        this.systemInfoLoading.complete();
      },
    });
    this.devicePanelService.getDevicePanelDescription().subscribe(
      data => {
        this.panelDescription = data;
        this.getPanelStatus().subscribe({
          complete: () => {
            this.panelLoading.complete();
            this.getEventCount();
          },
          error: error => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            this.errorService.handleError(error);
          },
        });
      },
      error => {
        this.errorService.handleError(error);
        this.panelLoading.complete();
      }
    );

    // Polling data with interval
    // this.pollingUtilizationIntervalRef = setInterval(() => {
    //   Promise.all([this.getCPUInfo(), this.getInterfaceErrorPacket(),
    //   this.getInterfaceUtilization()]).then(() => {
    //     this.refreshCard('pollingAll');
    //   });
    // }, this.pollingUtilizationInterval);
    this.pollingSystemInfoRef = setInterval(() => {
      this.getSystemInfo().subscribe({
        complete: () => {
          this.systemInfoLoading.complete();
        },
      });
    }, this.pollingSystemInfoInterval);
    this.pollingIntervalRef = setInterval(() => {
      this.refreshCard('cpuHistorical');
      setTimeout(() => {
        this.getPanelStatus().subscribe({
          complete: () => {
            this.panelLoading.complete();
            setTimeout(() => {
              this.getEventCount();
            }, this.cardUpdateInterval);
          },
          error: error => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            this.errorService.handleError(error);
          },
        });
      }, this.cardUpdateInterval);
    }, this.pollingInterval);
  }

  ngOnDestroy(): void {
    clearInterval(this.pollingIntervalRef);
    clearInterval(this.pollingSystemInfoRef);
    // clearInterval(this.pollingUtilizationIntervalRef);
    clearTimeout(this.trafficStatisticsTimeout);
  }

  changeBrowseMode(mode): void {
    this.browseMode = mode;
    this.getPanelStatus().subscribe({
      complete: () => {
        this.panelLoading.complete();
      },
      error: error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      },
    });
    return;
  }

  portDetailMode(): void {
    this.portDetail = !this.portDetail;
  }

  getSystemInfo(): Observable<any> {
    return new Observable(observer => {
      const infoRequest = {};
      forEach(this.systemInfo, (item: string) => {
        switch (item) {
          case 'productModel':
          case 'productRevision':
          case 'serialNumber':
          case 'firmwareVersion':
          case 'systemUptime':
          case 'oobMacAddress':
            if (!infoRequest['statusSystemInfo']) {
              infoRequest['statusSystemInfo'] = this.http.get(environment.uriRequestURL + '/status/systemInformation');
            }
            break;
          case 'deviceName':
          case 'location':
            if (!infoRequest['settingSystemInfo']) {
              infoRequest['settingSystemInfo'] = this.http.get(
                environment.uriRequestURL + '/setting/data/systemInformation'
              );
            }
            break;
          case 'ipAddress':
            infoRequest['ipRequest'] = this.http.get(environment.uriRequestURL + '/status/managementIp');
            break;
          case 'l3IpAddress':
            infoRequest['ipRequest'] = this.http.get(environment.uriRequestURL + '/status/l3RouterId');
            break;
          case 'oobIpAddress':
            infoRequest['oob'] = this.http.get(environment.uriRequestURL + '/setting/data/oobmanagementip');
            break;
          case 'redundantProtocol':
            infoRequest['mxL2Redundancy'] = this.http.get(environment.uriRequestURL + '/setting/data/mxL2Redundancy');
            break;
          case 'powerModel':
            // for EDS
            infoRequest['powerModel'] = this.http.get(environment.uriRequestURL + '/status/systemInformation/modules');
            break;
          case 'externalStorage':
            infoRequest['externalStorage'] = this.http.get(
              environment.uriRequestURL + '/status/externalStorage/deviceInfo'
            );
            break;
          case 'iec62439_3Protocol':
            infoRequest['iec62439_3Protocol'] = this.http.get(environment.uriRequestURL + '/setting/data/mxPhr');
            break;
          case 'systemTemperature':
            infoRequest['sysTempStatus'] = this.http.get(environment.uriRequestURL + '/status/intelTempCtrlStatus');
            break;
        }
      });

      this.mdTemplates = [];
      forkJoin(infoRequest).subscribe((data: any) => {
        forEach(this.systemInfo, (item: string) => {
          switch (item) {
            // Get by /status/systemInformation
            case 'productModel':
              this.modelNameData =
                data.statusSystemInfo.modelName === '' ? this.utils.emptyDash : data.statusSystemInfo.modelName;
              localStorage.setItem('mx_modelName', this.modelNameData);
              this.mdTemplates.push(this.mdProductModel);
              break;
            case 'productRevision':
              this.productRevisionData =
                data.statusSystemInfo.productRevision === ''
                  ? this.utils.emptyDash
                  : data.statusSystemInfo.productRevision;
              this.mdTemplates.push(this.mdProductRevision);
              break;
            case 'serialNumber':
              this.serialNOData =
                data.statusSystemInfo.serialNumber === '' ? this.utils.emptyDash : data.statusSystemInfo.serialNumber;
              this.mdTemplates.push(this.mdSerialNumber);
              break;
            case 'firmwareVersion':
              this.firmwareVersionData =
                data.statusSystemInfo.firmwareVersion === ''
                  ? this.utils.emptyDash
                  : data.statusSystemInfo.firmwareVersion;
              const prevFirmwareVersion = localStorage.getItem('mxSw_firmwareVersion');
              this.isUpgradeToV4 = prevFirmwareVersion === null;
              localStorage.setItem('mxSw_firmwareVersion', this.firmwareVersionData);
              this.mdTemplates.push(this.mdFirmwareVersion);
              break;
            case 'systemUptime':
              this.systemUptimeData =
                data.statusSystemInfo.uptime === '' ? this.utils.emptyDash : data.statusSystemInfo.uptime;
              this.mdTemplates.push(this.mdSystemUptime);
              break;
            case 'macAddress':
              this.macAddressData = data.statusSystemInfo.mac === '' ? this.utils.emptyDash : data.statusSystemInfo.mac;
              localStorage.setItem('macAddress', this.macAddressData);
              this.mdTemplates.push(this.mdMacAddress);
              break;
            // Get by /setting/data/systemInformation
            case 'deviceName':
              this.deviceNameData =
                data.settingSystemInfo.deviceName === '' ? this.utils.emptyDash : data.settingSystemInfo.deviceName;
              this.mdTemplates.push(this.mdDeviceName);
              break;
            case 'location':
              this.locationData =
                data.settingSystemInfo.deviceLocation === ''
                  ? this.utils.emptyDash
                  : data.settingSystemInfo.deviceLocation;
              this.mdTemplates.push(this.mdLocation);
              break;
            // Get by other URI
            case 'ipAddress':
              this.ipAddressv4Data = data.ipRequest.ipv4.address;
              localStorage.setItem('mx_ipAddress', this.ipAddressv4Data);
              this.mdTemplates.push(this.mdIpAddress);
              break;
            case 'l3IpAddress':
              // for Layer 3 switch
              this.ipAddressv4Data = data.ipRequest.ipv4.ipAddress;
              localStorage.setItem('mx_ipAddress', this.ipAddressv4Data);
              this.mdTemplates.push(this.mdL3IpAddress);
              break;
            case 'redundantProtocol':
              const redundantProtocols = invertBy(data.mxL2Redundancy);
              this.redundantProtocolData = redundantProtocols.true
                ? redundantProtocols.true.map(itemStr => this.getRedundnatProtocolString(itemStr)).join(', ')
                : this.utils.emptyDash;
              this.mdTemplates.push(this.mdRedundantProtocol);
              break;
            case 'powerModel':
              // for EDS
              this.powerModelData =
                data.powerModel.power[0].moduleName === '' ? this.utils.emptyDash : data.powerModel.power[0].moduleName;
              this.mdTemplates.push(this.mdPowerModel);
              break;
            case 'externalStorage':
              const existedDevArray = [];
              if (data.externalStorage.usb?.present) {
                existedDevArray.push(data.externalStorage.usb.modelName);
              }
              if (data.externalStorage.microSD?.present) {
                existedDevArray.push(data.externalStorage.microSD.modelName);
              }
              this.externalStorageData =
                existedDevArray.length > 0 ? existedDevArray.join(' / ') : this.utils.emptyDash;
              this.mdTemplates.push(this.mdExternalStorag);
              break;
            case 'iec62439_3Protocol':
              this.iec62439_3ProtocolData = !data.iec62439_3Protocol.enable
                ? this.utils.emptyDash
                : this.getIec624393ProtocolString(data.iec62439_3Protocol.protocol);
              this.mdTemplates.push(this.mdIec62439_3Protocol);
              break;
            case 'oobIpAddress':
              this.oobIpData = data.oob.ipv4.ipAddress;
              this.mdTemplates.push(this.mdOobIpAddress);
              break;
            case 'oobMacAddress':
              this.oobMacData =
                data.statusSystemInfo.oob_mac === '' ? this.utils.emptyDash : data.statusSystemInfo.oob_mac;
              this.mdTemplates.push(this.mdOobMacAddress);
              break;
            case 'systemTemperature':
              let tempColor = '';
              const sysTemp = data.sysTempStatus.sysTemperature;
              enum TempStatus {
                NOT_PRESENT = 0,
                GREEN = 1,
                YELLOW = 2,
                RED = 3,
              }
              enum PowerMode {
                NORMAL = 0,
                POWER_CUT_OFF = 1,
              }
              if (sysTemp.status === TempStatus.YELLOW) {
                tempColor = 'warning-color';
              } else if (sysTemp.status === TempStatus.RED) {
                tempColor = 'critical-color';
              }
              this.systemTemperatureData = {
                class: tempColor,
                temperature:
                  sysTemp.temperatureC +
                  this.translate.instant('general.unit.c') +
                  '/ ' +
                  sysTemp.temperatureF +
                  this.translate.instant('general.unit.f'),
              };
              this.mdTemplates.push(this.mdSystemTemperature);
              if (data.sysTempStatus.sysState.status === PowerMode.POWER_CUT_OFF) {
                this.snackBar.open(
                  this.translate.instant('features.dashboard.cut_off_mode'),
                  this.translate.instant('general.button.close'),
                  { panelClass: ['error'] }
                );
              }
              break;
          }
        });
        // show warning message to solve the problem of module incompatibility in the MDS series
        // ref. https://jira-dc.moxa.com/browse/RDNOSVR-158, https://jira-dc.moxa.com/browse/RDNOSVR-140
        if (
          (data.statusSystemInfo.modelName?.match(/^MDS(?!.*-4XGS).*/g) &&
            data.statusSystemInfo?.modules?.power.filter(p => p?.moduleName.includes('-A')).length !== 0) ||
          data.statusSystemInfo?.modules?.ethernet.filter(m => m?.status.includes('Abnormal:')).length !== 0
        ) {
          this.snackBar.open(
            this.translate.instant('features.dashboard.unsupported_module_warning'),
            this.translate.instant('general.button.close'),
            { panelClass: ['warning'] }
          );
        }

        observer.complete();
      });
    });
  }

  getRedundnatProtocolString(redundnatProtocol): string {
    if (redundnatProtocol === 'dualhoming') {
      return this.translate.instant('features.dual_homing.page_title');
    } else if (redundnatProtocol === 'turboringv2') {
      return this.translate.instant('features.turbo_ring_v2.page_title');
    } else if (redundnatProtocol === 'turbochain') {
      return this.translate.instant('features.turbo_chain.page_title');
    } else if (redundnatProtocol === 'iec62439_2') {
      return this.translate.instant('features.mrp.menu_title');
    } else if (redundnatProtocol === 'stprstp') {
      return this.translate.instant('features.stp.stp_rstp');
    } else if (redundnatProtocol === 'mstp') {
      return this.translate.instant('features.stp.mstp');
    } else if (redundnatProtocol === 'mxerpsv2') {
      return this.translate.instant('features.erps_v2.page_title');
    } else {
      return '';
    }
  }

  getIec624393ProtocolString(protocol): string {
    switch (protocol) {
      case 0:
        return this.translate.instant('features.prp_hsr.prp');
      case 1:
        return this.translate.instant('features.prp_hsr.hsr');
      case 2:
        return this.translate.instant('features.prp_hsr.coupling');
    }
  }

  getEventCount(): void {
    const data = {
      entries: null,
      time: null,
    };
    concat(
      this.http.get(environment.uriRequestURL + '/status/logEntry/entries', { observe: 'response' }),
      this.http.get(environment.uriRequestURL + '/status/time', { observe: 'response' })
    ).subscribe({
      next: response => {
        const source = response.url.split('/').pop();
        data[source] = response.body;
      },
      error: error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      },
      complete: () => {
        const currentDatetime = moment().set({
          year: data.time.year,
          month: data.time.month - 1,
          date: data.time.date,
          hour: data.time.hour,
          minute: data.time.minute,
          second: data.time.second,
        });
        const before3days = cloneDeep(currentDatetime).subtract(3, 'days');

        this.secerityNoticeCount = filter(data.entries, entry => {
          const timestamp = moment(entry.timestamp);
          if (entry.severity === '5' && timestamp.isBetween(before3days, currentDatetime)) {
            return entry;
          }
        }).length;
        this.secerityWarningCount = filter(data.entries, entry => {
          const timestamp = moment(entry.timestamp);
          if (entry.severity === '4' && timestamp.isBetween(before3days, currentDatetime)) {
            return entry;
          }
        }).length;
        this.secerityErrorCount = filter(data.entries, entry => {
          const timestamp = moment(entry.timestamp);
          if (entry.severity === '3' && timestamp.isBetween(before3days, currentDatetime)) {
            return entry;
          }
        }).length;
        this.secerityCriticalCount = filter(data.entries, entry => {
          const timestamp = moment(entry.timestamp);
          if (entry.severity === '2' && timestamp.isBetween(before3days, currentDatetime)) {
            return entry;
          }
        }).length;
        this.eventSummaryLoading.complete();
      },
    });
  }

  openEventLog(severity): void {
    localStorage.setItem('eventLogSeverity', severity);
    this.router.navigateByUrl('/pages/event-log');
  }

  getPanelStatus(): Observable<any> {
    return new Observable(observer => {
      const request = {
        portTable: this.http.get(environment.uriRequestURL + '/status/portInfo/portTable'),
        led: this.http.get(environment.uriRequestURL + '/status/led'),
        io: this.http.get(environment.uriRequestURL + '/status/io'),
        portStatus: this.http.get(environment.uriRequestURL + '/status/portStatus'),
        turboChain: this.http.get(environment.uriRequestURL + '/setting/data/turboChain'),
      };
      if (!this.notSupport.oob) {
        request['oobport'] = this.http.get(environment.uriRequestURL + '/status/oobportStatus/oobport');
      }
      if (!this.notSupport.intelTemp) {
        request['intelTempCtrlStatus'] = this.http.get(environment.uriRequestURL + '/status/intelTempCtrlStatus');
      }
      forkJoin(request).subscribe(
        (data: any) => {
          const responseData = {
            portStatus: data.portStatus,
            led: data.led,
            io: data.io,
            portInfo: data.portTable,
            turboChainRole: data.turboChain.role,
            panelDescription: this.panelDescription,
            oobPort: data.oobport,
            intelTempCtrlStatus: data.intelTempCtrlStatus,
          };
          this.panelStatusData = {
            portStatus: data.portStatus,
            devicePanelData: this.devicePanelService.getDevicePanelData(responseData),
          };
          observer.complete();
        },
        error => {
          observer.error(error);
        }
      );
    });
  }

  getCPUInfo(): Observable<any> {
    return new Observable(observer => {
      forkJoin({
        utilization: this.http.get(environment.uriRequestURL + '/status/systemUtilization'),
        timeStatus: this.http.get(environment.uriRequestURL + '/status/time'),
      }).subscribe(
        (data: any) => {
          const now = this.utils.getSystemTime(data.timeStatus);
          this.cpuUtilizationData = data.utilization.cpuUtilization;
          this.cpuUtilizationHistoricalData.labels.push(this.utils.getSystemTime(data.timeStatus, { onlyTime: true }));
          this.cpuUtilizationHistoricalData.data.push(data.utilization.cpuUtilization);
          this.freeCPU = 100 - parseInt(this.cpuUtilizationData, 10);
          this.cpuUtilizationUpdateAt = now;
          this.cpuUtilizationHistoricalUpdateAt = now;
          observer.complete();
        },
        error => {
          observer.error(error);
        }
      );
    });
  }

  // getInterfaceErrorPacket(): Promise<void> {
  //   return new Promise((resolve, reject) => {
  //     this.http.get(environment.uriRequestURL + '/status/trafficStatistics').subscribe((data: any) => {
  //       const labels = [];
  //       const datasets = [];
  //       let txErrors = [];
  //       let rxErrors = [];
  //       let txErrorSupport = true;
  //       let rxErrorSupport = true;
  //       data.portTable.forEach((port, portIndex) => {
  //         // Tx Error
  //         if (portIndex === 0 && port.lateCollisionPackets === undefined && port.excessiveCollisionPackets === undefined) {
  //           txErrorSupport = false;
  //         }
  //         const lateCollisionPackets = port.lateCollisionPackets === undefined ? 0 : port.lateCollisionPackets;
  //         const excessiveCollisionPackets = port.excessiveCollisionPackets === undefined ? 0 : port.excessiveCollisionPackets;
  //         const txErrorData = lateCollisionPackets + excessiveCollisionPackets;
  //         const txErrorObject = {
  //           name: this.portMapData[portIndex],
  //           txError: txErrorData
  //         };
  //         txErrors.push(txErrorObject);

  //         // Rx Error
  //         if (portIndex === 0 &&
  //           port.crcAlignErrorPackets === undefined && port.crcAlignErrorPackets === undefined &&
  //           port.crcAlignErrorPackets === undefined && port.crcAlignErrorPackets === undefined &&
  //           port.crcAlignErrorPackets === undefined && port.crcAlignErrorPackets === undefined) {
  //           rxErrorSupport = false;
  //         }
  //         const crcAlignErrorPackets = port.crcAlignErrorPackets === undefined ? 0 : port.crcAlignErrorPackets;
  //         const dropPackets = port.dropPackets === undefined ? 0 : port.dropPackets;
  //         const undersizePackets = port.undersizePackets === undefined ? 0 : port.undersizePackets;
  //         const oversizePackets = port.oversizePackets === undefined ? 0 : port.oversizePackets;
  //         const fragmentPackets = port.fragmentPackets === undefined ? 0 : port.fragmentPackets;
  //         const jabberPackets = port.jabberPackets === undefined ? 0 : port.jabberPackets;
  //         const rxErrorData = crcAlignErrorPackets + dropPackets + undersizePackets + oversizePackets + fragmentPackets + jabberPackets;
  //         const rxErrorObject = {
  //           name: this.portMapData[portIndex],
  //           rxError: rxErrorData
  //         };
  //         rxErrors.push(rxErrorObject);
  //       });
  //       if (this.utils.supportLA) {
  //         data.portChannelTable.forEach((portChannel, portChannelIndex) => {
  //           // Tx Error
  //           const lateCollisionPackets = portChannel.lateCollisionPackets === undefined ? 0 : portChannel.lateCollisionPackets;
  //           const excessiveCollisionPackets = portChannel.excessiveCollisionPackets === undefined ?
  //             0 : portChannel.excessiveCollisionPackets;

  //           const txErrorData = lateCollisionPackets + excessiveCollisionPackets;
  //           const txErrorObject = {
  //             name: this.portChannelMapData[portChannelIndex],
  //             txError: txErrorData
  //           };
  //           txErrors.push(txErrorObject);

  //           // Rx Error
  //           const crcAlignErrorPackets = portChannel.crcAlignErrorPackets === undefined ? 0 : portChannel.crcAlignErrorPackets;
  //           const dropPackets = portChannel.dropPackets === undefined ? 0 : portChannel.dropPackets;
  //           const undersizePackets = portChannel.undersizePackets === undefined ? 0 : portChannel.undersizePackets;
  //           const oversizePackets = portChannel.oversizePackets === undefined ? 0 : portChannel.oversizePackets;
  //           const fragmentPackets = portChannel.fragmentPackets === undefined ? 0 : portChannel.fragmentPackets;
  //           const jabberPackets = portChannel.jabberPackets === undefined ? 0 : portChannel.jabberPackets;

  //           const rxError = crcAlignErrorPackets + dropPackets + undersizePackets + oversizePackets + fragmentPackets + jabberPackets;
  //           const rxErrorObject = {
  //             name: this.portChannelMapData[portChannelIndex],
  //             txError: rxError
  //           };
  //           rxErrors.push(rxErrorObject);
  //         });
  //       }
  //       txErrors = sortBy(txErrors, [(o) => {
  //         return o.txError;
  //       }]);
  //       rxErrors = sortBy(rxErrors, [(o) => {
  //         return o.rxError;
  //       }]);
  //       const txErrorResult = reduce(txErrors, (sum, n) => {
  //         return sum + n.txError;
  //       }, 0);
  //       if (txErrorResult !== 0) {
  //         txErrors = reverse(txErrors);
  //       }
  //       const rxErrorResult = reduce(rxErrors, (sum, n) => {
  //         return sum + n.rxError;
  //       }, 0);
  //       if (rxErrorResult !== 0) {
  //         rxErrors = reverse(rxErrors);
  //       }
  //       const txErrorDataset = [];
  //       const rxErrorDataset = [];
  //       for (let i = 0; i < txErrors.length; i++) {
  //         if (i >= this.interfaceNumber) {
  //           break;
  //         }
  //         if (txErrorSupport) {
  //           labels.push(txErrors[i].name);
  //         } else {
  //           labels.push(rxErrors[i].name);
  //         }
  //         txErrorDataset.push(txErrors[i].txError);
  //         rxErrorDataset.push(rxErrors[i].rxError);
  //       }
  //       if (txErrorSupport) {
  //         const txDataset = {
  //           label: this.translate.instant('features.dashboard.tx_error'),
  //           borderColor: 'rgba(216, 110, 34, 1)',
  //           backgroundColor: 'rgba(216, 110, 34, 0.9)',
  //           borderWidth: 1,
  //           data: txErrorDataset,
  //         };
  //         datasets.push(txDataset);
  //       }
  //       if (rxErrorSupport) {
  //         const rxDataset = {
  //           label: this.translate.instant('features.dashboard.rx_error'),
  //           borderColor: 'rgba(0, 135, 135, 1)',
  //           backgroundColor: 'rgba(0, 135, 135, 0.9)',
  //           borderWidth: 1,
  //           data: rxErrorDataset,
  //         };
  //         datasets.push(rxDataset);
  //       }
  //       this.interfaceErrorPacketUpdateAt = moment().format('YYYY-MM-DD HH:mm:ss');
  //       this.interfaceErrorPacketData.labels = labels;
  //       this.interfaceErrorPacketData.datasets = datasets;
  //       this.interfaceErrorPacketLoading.complete();
  //       resolve();
  //     }, error => {
  //       reject();
  //       this.errorService.handleError(error);
  //     });
  //   });
  // }

  // getInterfaceUtilization(): Promise<void> {
  //   return new Promise((resolve, reject) => {
  //     this.getTrafficStatistics().then((firstData) => {
  //       this.trafficStatisticsTimeout = setTimeout(() => {
  //         this.getTrafficStatistics().then((secondData) => {
  //           const requests = [this.http.get(environment.uriRequestURL + '/status/portStatus')];
  //           if (this.utils.supportLA) {
  //             requests.push(this.http.get(environment.uriRequestURL + '/status/laStatus'));
  //           }
  //           forkJoin(requests).subscribe((portData: any) => {
  //             const labels = [];
  //             const datasets = [];
  //             let utilizations = [];
  //             for (let portIndex = 0; portIndex < firstData.portTable.length; portIndex++) {
  //               const pktsIncrement = secondData.portTable[portIndex].rxTotalPackets - firstData.portTable[portIndex].rxTotalPackets;
  //               const octetsIncrement = secondData.portTable[portIndex].rxTotalOctets - firstData.portTable[portIndex].rxTotalOctets;
  //               let speed = 10000;
  //               if (portData[0].portTable[portIndex].operSpeed === '1G') {
  //                 speed = 1000000;
  //               } else if (portData[0].portTable[portIndex].operSpeed === '100M') {
  //                 speed = 100000;
  //               }
  //               const interval = 5;
  //               const utilizationData = ((pktsIncrement * (9.6 + 6.4)) + (octetsIncrement * 0.8)) / (interval * speed);
  //               const utilizationObject = {
  //                 name: this.portMapData[portIndex],
  //                 utilization: utilizationData
  //               };
  //               utilizations.push(utilizationObject);
  //             }
  //             if (this.utils.supportLA) {
  //               for (let portChannelIndex = 0; portChannelIndex < firstData.portChannelTable.length; portChannelIndex++) {
  //                 if (portData[1].portChannelTable[portChannelIndex].operSpeed) {
  //                   const pktsIncrement = secondData.portChannelTable[portChannelIndex].rxTotalPackets -
  //                     firstData.portChannelTable[portChannelIndex].rxTotalPackets;
  //                   const octetsIncrement = secondData.portChannelTable[portChannelIndex].rxTotalOctets -
  //                     firstData.portChannelTable[portChannelIndex].rxTotalOctets;
  //                   const speed = portData[1].portChannelTable[portChannelIndex].operSpeed;
  //                   const interval = 5;
  //                   const utilizationData = ((pktsIncrement * (9.6 + 6.4)) + (octetsIncrement * 0.8)) / (interval * speed);
  //                   const utilizationObject = {
  //                     name: this.portMapData[portChannelIndex],
  //                     utilization: utilizationData
  //                   };
  //                   utilizations.push(utilizationObject);
  //                 }
  //               }
  //             }
  //             utilizations = sortBy(utilizations, [(o) => {
  //               return o.utilization;
  //             }]);
  //             const result = reduce(utilizations, (sum, n) => {
  //               return sum + n.utilization;
  //             }, 0);
  //             if (result !== 0) {
  //               utilizations = reverse(utilizations);
  //             }
  //             const dataset = [];
  //             for (let i = 0; i < utilizations.length; i++) {
  //               if (i >= this.interfaceNumber) {
  //                 break;
  //               }
  //               labels.push(utilizations[i].name);
  //               dataset.push(Math.round(utilizations[i].utilization * 100) / 100);
  //             }
  //             const datasetObject = {
  //               borderColor: 'rgba(0, 135, 135, 1)',
  //               backgroundColor: 'rgba(0, 135, 135, 0.9)',
  //               borderWidth: 1,
  //               data: dataset
  //             };
  //             datasets.push(datasetObject);
  //             this.interfaceUtilizationUpdateAt = moment().format('YYYY-MM-DD HH:mm:ss');
  //             this.interfaceUtilizationData.labels = labels;
  //             this.interfaceUtilizationData.datasets = datasets;
  //             this.interfaceUtilizationLoading.complete();
  //             resolve();
  //           });
  //         });
  //       }, 5000);
  //     });
  //   });
  // }

  getTrafficStatistics(): Promise<any> {
    return new Promise((resolve, reject) => {
      this.http.get(environment.uriRequestURL + '/status/trafficStatistics').subscribe(
        data => {
          resolve(data);
        },
        error => {
          reject();
        }
      );
    });
  }

  refreshCard(targetChart): void {
    switch (targetChart) {
      case 'cpuHistorical':
        this.cpuHistoricalLoading.start();
        this.getCPUInfo().subscribe({
          complete: () => {
            this.cpuHistoricalLoading.complete();
            this.cpuHistoricalChart.update();
          },
          error: error => {
            this.cpuHistoricalLoading.complete();
            this.errorService.handleError(error);
          },
        });
        break;
      // case 'interfaceErrorPacket':
      //   this.interfaceErrorPacketLoading.start();
      //   this.getInterfaceErrorPacket().then(() => {
      //     this.interfaceErrorPacketChart.update();
      //   });
      //   break;
      // case 'interfaceUtilization':
      //   this.interfaceUtilizationLoading.start();
      //   this.getInterfaceUtilization().then(() => {
      //     this.interfaceUtilizationChart.update();
      //   });
      //   break;
      case 'pollingAll':
        this.cpuHistoricalChart.update();
        // this.interfaceErrorPacketChart.update();
        // this.interfaceUtilizationChart.update();
        break;
      default:
        break;
    }
  }

  initCPUUtilizationHistorical(): void {
    this.cpuHistoricalChart = new Chart(this.cpuHistoricalCanvas.nativeElement.getContext('2d'), {
      type: 'line',
      data: {
        labels: this.cpuUtilizationHistoricalData.labels,
        datasets: [
          {
            data: this.cpuUtilizationHistoricalData.data,
            fill: 'origin',
            borderColor: 'rgba(0, 135, 135, 0.6)',
            backgroundColor: 'rgba(0, 135, 135, 0.1)',
          },
        ],
      },
      options: {
        responsive: true,
        plugins: {
          tooltip: {
            callbacks: {
              label: tooltipItem => {
                return tooltipItem.formattedValue + '%';
              },
            },
          },
          legend: {
            display: false,
            position: 'right',
          },
        },
        scales: {
          y: {
            suggestedMin: 0,
            suggestedMax: 100,
            ticks: {
              callback: (label, index, labels) => {
                if (Math.floor(+label) === label) {
                  return label;
                }
              },
            },
          },
        },
      },
    });
  }
  /*
    initInterfaceErrorPacketChart(): void {
      const interfaceErrorPacketOpt = {
        legend: {
          position: 'right'
        },
        scales: {
          xAxes: [
            {
              scaleLabel: {
                display: true,
                labelString: this.translate.instant('general.common_port.port'),
              }
            }
          ],
          yAxes: [
            {
              scaleLabel: {
                display: true,
                labelString: this.translate.instant('general.common.packets'),
              },
              ticks: {
                beginAtZero: true,
                userCallback: (label, index, labels) => {
                  if (Math.floor(label) === label) {
                    return label;
                  }
                }
              }
            }
          ]
        }
      };
      const ctx = this.interfaceErrorPacketCanvas.nativeElement.getContext('2d');
      this.interfaceErrorPacketChart = new Chart(ctx, {
        type: 'bar',
        data: this.interfaceErrorPacketData,
        options: interfaceErrorPacketOpt
      });
    }

    initInterfaceUtilizationChart(): void {
      const interfaceUtilizationOpt = {
        responsive: true,
        tooltips: {
          callbacks: {
            label: (tooltipItem) => {
              return tooltipItem.yLabel + '%';
            }
          }
        },
        legend: {
          display: false
        },
        scales: {
          xAxes: [
            {
              scaleLabel: {
                display: true,
                labelString: this.translate.instant('general.common_port.port')
              }
            }
          ],
          yAxes: [
            {
              ticks: {
                beginAtZero: true,
                userCallback: (label, index, labels) => {
                  if (Math.floor(label) === label) {
                    return label;
                  }
                }
              }
            }
          ]
        }
      };
      const ctx = this.interfaceUtilizationCanvas.nativeElement.getContext('2d');
      this.interfaceUtilizationChart = new Chart(ctx, {
        type: 'bar',
        data: this.interfaceUtilizationData,
        options: interfaceUtilizationOpt
      });
    }
    */

  initPanelSummary(): void {
    this.panelSummary = {
      ethernet: [],
      power: [
        {
          moduleName: 'PWR1',
          exist: false,
          leds: {
            pwr: {
              style: '',
              hint: '',
            },
            eps: {
              style: '',
              hint: '',
            },
          },
        },
        {
          moduleName: 'PWR2',
          exist: false,
          leds: {
            pwr: {
              style: '',
              hint: '',
            },
            eps: {
              style: '',
              hint: '',
            },
          },
        },
      ],
      mgmt: {
        moduleName: 'MGMT',
        exist: true,
        leds: {
          sync: {
            style: '',
            hint: '',
          },
          state: {
            style: '',
            hint: '',
          },
          fault: {
            style: '',
            hint: '',
          },
          mh: {
            style: '',
            hint: '',
          },
          ct: {
            style: '',
            hint: '',
          },
        },
      },
    };
  }
}
