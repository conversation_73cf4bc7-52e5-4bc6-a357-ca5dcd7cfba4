<h3 *ngIf="!dialogData.row" mat-dialog-title>
  {{ 'features.port_mirror.create_mirror_msg' | translate }}
</h3>
<h3 *ngIf="dialogData.row" mat-dialog-title>
  {{ 'features.port_mirror.edit_mirror_msg' | translate : { sessionIndex: dialogData.row.sessionid } }}
</h3>
<div mat-dialog-content>
  <form [formGroup]="spanSessionSettingForm">
    <div *ngIf="!dialogData.row" fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
      <mat-form-field>
        <mat-select
          id="select-session-id"
          placeholder="{{ 'features.port_mirror.session_id' | translate }}"
          formControlName="sessionId"
          required
        >
          <mat-option
            id="option-session-{{ sessionId }}"
            *ngFor="let sessionId of selectableSpan"
            [value]="sessionId"
            >{{ sessionId }}</mat-option
          >
        </mat-select>
      </mat-form-field>
    </div>
    <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
      <mat-form-field>
        <mat-select
          id="select-reflect-port-mode"
          (selectionChange)="reflectPortModeChange($event.value)"
          placeholder="{{ 'features.port_mirror.reflect_port_mode' | translate }}"
          formControlName="reflectPortMode"
          required
        >
          <mat-option id="option-reflect-port-mode-enable" [value]="true">
            {{ 'general.common.enabled' | translate }}</mat-option
          >
          <mat-option id="option-reflect-port-mode-disable" [value]="false">
            {{ 'general.common.disabled' | translate }}</mat-option
          >
        </mat-select>
        <mat-error *ngIf="spanSessionSettingForm.get('reflectPortMode').errors?.required">
          {{ 'validators.required' | translate }}
        </mat-error>
      </mat-form-field>
    </div>
    <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
      <mat-form-field>
        <mat-select
          id="select-tx-port"
          placeholder="{{ 'features.port_mirror.tx_source_port' | translate }}"
          formControlName="txSourcePort"
          multiple
        >
          <mat-option
            *ngFor="let port of allPortMap"
            id="option-tx-port-{{ port.name }}"
            [value]="port.index"
            [disabled]="
              !port.isAccessPort &&
              spanSessionSettingForm.get('reflectPortMode').value === true &&
              notSupport.accessPortMirror
            "
            matTooltip="{{ 'features.port_mirror.source_port_must_be_access_port' | translate }}"
            matTooltipPosition="right"
            [matTooltipDisabled]="
              port.isAccessPort ||
              spanSessionSettingForm.get('reflectPortMode').value !== true ||
              !notSupport.accessPortMirror
            "
          >
            {{ port.name }}</mat-option
          >
        </mat-select>
      </mat-form-field>
      <mat-form-field>
        <mat-select
          id="select-rx-port"
          placeholder="{{ 'features.port_mirror.rx_source_port' | translate }}"
          formControlName="rxSourcePort"
          multiple
        >
          <mat-option
            *ngFor="let port of allPortMap"
            id="option-rx-port-{{ port.name }}"
            [value]="port.index"
            [disabled]="
              !port.isAccessPort &&
              spanSessionSettingForm.get('reflectPortMode').value === true &&
              notSupport.accessPortMirror
            "
            matTooltip="{{ 'features.port_mirror.source_port_must_be_access_port' | translate }}"
            matTooltipPosition="right"
            [matTooltipDisabled]="
              port.isAccessPort ||
              spanSessionSettingForm.get('reflectPortMode').value !== true ||
              !notSupport.accessPortMirror
            "
          >
            {{ port.name }}</mat-option
          >
        </mat-select>
      </mat-form-field>
    </div>
    <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
      <mat-form-field>
        <mat-select
          id="select-des-port"
          placeholder="{{ destinationPortPlaceholder }}"
          formControlName="destinationPort"
          required
        >
          <ng-container *ngFor="let port of allPortMapExLa">
            <mat-option
              id="option-des-port-{{ port.name }}"
              [value]="port.index"
              [disabled]="
                !port.isAccessPort &&
                spanSessionSettingForm.get('reflectPortMode').value === true &&
                notSupport.accessPortMirror
              "
              matTooltip="{{ 'features.port_mirror.reflect_port_must_be_access_port' | translate }}"
              matTooltipPosition="right"
              [matTooltipDisabled]="
                port.isAccessPort ||
                spanSessionSettingForm.get('reflectPortMode').value !== true ||
                !notSupport.accessPortMirror
              "
            >
              {{ port.name }}
            </mat-option>
          </ng-container>
        </mat-select>
        <mat-error *ngIf="spanSessionSettingForm.get('destinationPort').errors?.required">
          {{ 'validators.required' | translate }}
        </mat-error>
      </mat-form-field>
      <mat-icon
        class="form-help-tip"
        *ngIf="spanSessionSettingForm.get('reflectPortMode').value"
        matTooltip="{{ dialogData.rspanVidHint }}"
        matTooltipPosition="right"
        >info
      </mat-icon>
    </div>
    <div class="caption-font warning-caption" fxLayout.xs="column" fxLayoutGap="20px">
      {{ 'features.port_mirror.select_tx_or_rx_hint' | translate }}
      <br /><br />
      {{ 'features.port_mirror.rspan_vlan_setting_hint' | translate }}
    </div>
  </form>
</div>
<div mat-dialog-actions align="end">
  <button id="dialog-button-cancel" mat-button mat-dialog-close color="primary">
    {{ 'general.button.cancel' | translate }}
  </button>
  <button id="dialog-button-apply" (click)="onDialogSubmit()" mat-raised-button color="primary">
    <ng-container *ngIf="!dialogData.row">{{ 'general.button.create' | translate }}</ng-container>
    <ng-container *ngIf="dialogData.row">{{ 'general.button.apply' | translate }}</ng-container>
  </button>
</div>
