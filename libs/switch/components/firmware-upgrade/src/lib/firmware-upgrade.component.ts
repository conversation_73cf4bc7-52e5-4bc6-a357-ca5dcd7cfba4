import { AfterViewInit, ChangeDetectionStrategy, Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { MatLegacyDialog as MatDialog } from '@angular/material/legacy-dialog';

import { TranslateService } from '@ngx-translate/core';
import { forEach } from 'lodash-es';
import { concat, forkJoin, Observable } from 'rxjs';

import * as environment from '@switch-web/shared/environments/environment';
import { AppState } from '@switch-web/shared/app/app.service';
import { GlobalEvent, GlobalEventType } from '@switch-web/shared/app/global.event';
import { BrowseFileDialogComponent } from '@switch-web/shared/component/browse-file-dialog/browse-file-dialog.component';
import { NoExternalDevWarningDialogComponent } from '@switch-web/shared/component/no-external-dev-warning-dialog/no-external-dev-warning-dialog';
import { mediumDialogConfig, smallDialogConfig } from '@switch-web/shared/dialog/dialog-config.service';
import { AuthService } from '@switch-web/shared/service/auth.service';
import { ErrorService } from '@switch-web/shared/service/error.service';
import { ValidatorPattern } from '@switch-web/shared/validator/validators';

export enum UpgradeMethod {
  LOCAL,
  TFTP,
  SFTP,
  USB,
  MICRO_SD,
}
@Component({
  templateUrl: './firmware-upgrade.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FirmwareUpgradeComponent implements AfterViewInit {
  get UpdradeMethodType() {
    return UpgradeMethod;
  }

  fwUpgradeForm: UntypedFormGroup;
  notSupport = { usb: false, microSD: false };
  hidePassword = true;
  hwInterfaceEnable = {
    usb: false,
    microSD: false,
  };

  private localFile = null;
  private externalFilePath = '';

  constructor(
    private appState: AppState,
    private auth: AuthService,
    private http: HttpClient,
    private errorService: ErrorService,
    private translate: TranslateService,
    private router: Router,
    private dialog: MatDialog,
    private activatedRoute: ActivatedRoute
  ) {
    let validatorPattern = ValidatorPattern.ALLOW_ALL;
    this.activatedRoute.data.subscribe(data => {
      if (data.parameter) {
        validatorPattern = data.parameter.validator;
      }
      forEach(data.notSupport, feat => {
        this.notSupport[feat] = true;
      });
    });
    this.fwUpgradeForm = new UntypedFormGroup({
      method: new UntypedFormControl(UpgradeMethod.LOCAL, Validators.required),
      fileSelection: new UntypedFormControl(null, Validators.required),
      ipAddress: new UntypedFormControl('', [Validators.required, Validators.pattern(ValidatorPattern.IPADDR_REGEX)]),
      fileName: new UntypedFormControl(null, [
        Validators.required,
        Validators.pattern(ValidatorPattern.VAILD_FILE_NAME_REGEX),
      ]),
      account: new UntypedFormControl(null, [Validators.required, Validators.pattern(validatorPattern)]),
      password: new UntypedFormControl(null, [Validators.required, Validators.pattern(validatorPattern)]),
    });
  }

  ngAfterViewInit(): void {
    this.getPageData().subscribe({
      complete: () => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
      },
      error: error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      },
    });
  }

  getPageData(): Observable<any> {
    return new Observable(observer => {
      this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
      this.onMethodChange(UpgradeMethod.LOCAL);
      if (this.notSupport.usb && this.notSupport.microSD) {
        observer.complete();
        return;
      }
      this.http.get(environment.uriRequestURL + '/setting/data/hardwareInterface').subscribe(
        (data: any) => {
          this.hwInterfaceEnable = {
            usb: data.usb?.enable ?? false,
            microSD: data.microSD?.enable ?? false,
          };
          observer.complete();
        },
        error => {
          observer.error(error);
        }
      );
    });
  }

  onMethodChange(method: UpgradeMethod): void {
    this.fwUpgradeForm.reset();
    this.fwUpgradeForm.get('method').setValue(method);
    switch (method) {
      case UpgradeMethod.LOCAL:
        this.fwUpgradeForm.get('fileSelection').enable();
        this.fwUpgradeForm.get('ipAddress').disable();
        this.fwUpgradeForm.get('fileName').disable();
        this.fwUpgradeForm.get('account').disable();
        this.fwUpgradeForm.get('password').disable();
        break;
      case UpgradeMethod.TFTP:
        this.fwUpgradeForm.get('fileSelection').disable();
        this.fwUpgradeForm.get('ipAddress').enable();
        this.fwUpgradeForm.get('fileName').enable();
        this.fwUpgradeForm.get('account').disable();
        this.fwUpgradeForm.get('password').disable();
        break;
      case UpgradeMethod.SFTP:
        this.fwUpgradeForm.get('fileSelection').disable();
        this.fwUpgradeForm.get('ipAddress').enable();
        this.fwUpgradeForm.get('fileName').enable();
        this.fwUpgradeForm.get('account').enable();
        this.fwUpgradeForm.get('password').enable();
        break;
      case UpgradeMethod.USB:
        this.fwUpgradeForm.get('fileSelection').enable();
        this.fwUpgradeForm.get('ipAddress').disable();
        this.fwUpgradeForm.get('fileName').disable();
        this.fwUpgradeForm.get('account').disable();
        this.fwUpgradeForm.get('password').disable();
        break;
      case UpgradeMethod.MICRO_SD:
        this.fwUpgradeForm.get('fileSelection').enable();
        this.fwUpgradeForm.get('ipAddress').disable();
        this.fwUpgradeForm.get('fileName').disable();
        this.fwUpgradeForm.get('account').disable();
        this.fwUpgradeForm.get('password').disable();
        break;
    }
  }

  saveImportSelection(event): void {
    // append file name to input field
    this.fwUpgradeForm.get('fileSelection').setValue(event.target.files[0].name);
    this.localFile = event.target.files[0];
  }

  onBrowseExternal(method: UpgradeMethod): void {
    let requestPath: 'usb' | 'microSD';
    let deviceName;
    switch (method) {
      case UpgradeMethod.USB:
        requestPath = 'usb';
        deviceName = this.translate.instant('general.common.usb');
        break;
      case UpgradeMethod.MICRO_SD:
        requestPath = 'microSD';
        deviceName = this.translate.instant('general.common.micro_sd');
        break;
    }
    const request = {
      enable: this.http.get(environment.uriRequestURL + '/setting/data/hardwareInterface/' + requestPath + '/enable'),
      isPlugged: this.http.get(
        environment.uriRequestURL + '/status/externalStorage/deviceInfo/' + requestPath + '/present'
      ),
      files: this.http.get(environment.uriRequestURL + '/status/externalStorage/fileInfo/' + requestPath),
    };
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    forkJoin(request).subscribe(
      (data: any) => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        if (data.enable && data.isPlugged && data.files) {
          mediumDialogConfig.data = { device: deviceName, fileData: data.files };
          const dialogRef = this.dialog.open(BrowseFileDialogComponent, mediumDialogConfig);
          dialogRef.afterClosed().subscribe((dialogResult: any) => {
            if (dialogResult && dialogResult.path !== '') {
              this.fwUpgradeForm.get('fileSelection').setValue(dialogResult.fileName);
              this.externalFilePath = dialogResult.path;
            }
          });
        } else {
          smallDialogConfig.data = { device: deviceName };
          const dialogRef = this.dialog.open(NoExternalDevWarningDialogComponent, smallDialogConfig);
          dialogRef.afterClosed().subscribe(result => {
            if (result === 'retry') {
              this.onBrowseExternal(method);
            }
          });
        }
      },
      error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      }
    );
  }

  upgrade(): void {
    if (this.fwUpgradeForm.invalid) {
      this.fwUpgradeForm.markAllAsTouched();
      return;
    }
    const loadingTitle = this.translate.instant('request_handler.action_upgrading');
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true, title: loadingTitle }));
    let request;
    switch (this.fwUpgradeForm.get('method').value) {
      case UpgradeMethod.LOCAL:
        const formData: FormData = new FormData();
        formData.append('file', this.localFile);
        formData.append('request', '{"file_parameter": {}}');
        request = concat(
          this.http.post(environment.uriRequestURL + '/file/import/prestart', ''),
          this.http.post(environment.uriRequestURL + '/file/import/http/system/firmware.rom', formData)
        );
        break;
      case UpgradeMethod.TFTP:
        const tftpBody = {
          parameter: {
            host: this.fwUpgradeForm.get('ipAddress').value,
            port: 69,
            path: this.fwUpgradeForm.get('fileName').value,
          },
        };
        request = this.http.post(environment.uriRequestURL + '/file/import/tftp/system/firmware.rom', tftpBody);
        break;
      case UpgradeMethod.SFTP:
        const sftpBody = {
          parameter: {
            host: this.fwUpgradeForm.get('ipAddress').value,
            port: 22,
            path: this.fwUpgradeForm.get('fileName').value,
            username: this.fwUpgradeForm.get('account').value,
            password: this.fwUpgradeForm.get('password').value,
          },
        };
        request = this.http.post(environment.uriRequestURL + '/file/import/sftp/system/firmware.rom', sftpBody);
        break;
      case UpgradeMethod.USB:
        const usbBody = {
          parameter: {
            path: this.externalFilePath,
          },
        };
        request = this.http.post(environment.uriRequestURL + '/file/import/usb/system/firmware.rom', usbBody);
        break;
      case UpgradeMethod.MICRO_SD:
        const microSdBody = {
          parameter: {
            path: this.externalFilePath,
          },
        };
        request = this.http.post(environment.uriRequestURL + '/file/import/microsd/system/firmware.rom', microSdBody);
        break;
    }
    request.subscribe(
      () => {},
      error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      },
      () => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.auth.clearLocalData();
        this.router.navigateByUrl('/login/firmwareUpgrade');
      }
    );
  }
}
