import { ChangeDetectionStrategy, Component, Inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import {
  MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA,
  MatLegacyDialogRef as MatDialogRef,
} from '@angular/material/legacy-dialog';
import { MatLegacySnackBar as MatSnackBar } from '@angular/material/legacy-snack-bar';

import { TranslateService } from '@ngx-translate/core';

import * as environment from '@switch-web/shared/environments/environment';
import { AppState } from '@switch-web/shared/app/app.service';
import { GlobalEvent, GlobalEventType } from '@switch-web/shared/app/global.event';
import { ErrorService } from '@switch-web/shared/service/error.service';
import { UtilsService } from '@switch-web/shared/service/utils.service';
import { ValidatorPattern } from '@switch-web/shared/validator/validators';

import { requireGroupIpAndNetmaskUnique } from '../pim-sm-setting.validator';

@Component({
  templateUrl: './static-rp-setting-dialog.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StaticRpSettingDialogComponent {
  staticRpSettingForm: UntypedFormGroup;
  netmaskOption = [];

  constructor(
    public dialogRef: MatDialogRef<StaticRpSettingDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public dialogData: any,
    private appState: AppState,
    private http: HttpClient,
    private snackBar: MatSnackBar,
    private translate: TranslateService,
    private utils: UtilsService,
    private errorService: ErrorService
  ) {
    this.netmaskOption = this.utils.getSubMaskOption({ rangeStart: 4, rangeEnd: 32 });
    this.staticRpSettingForm = new UntypedFormGroup(
      {
        groupAddress: new UntypedFormControl('', [
          Validators.required,
          Validators.pattern(ValidatorPattern.IPADDR_REGEX),
        ]),
        netmask: new UntypedFormControl('', Validators.required),
        rpAddress: new UntypedFormControl('', [Validators.required, Validators.pattern(ValidatorPattern.IPADDR_REGEX)]),
        override: new UntypedFormControl(null, Validators.required),
      },
      requireGroupIpAndNetmaskUnique(dialogData.staticRpTable)
    );
    if (dialogData.row) {
      this.staticRpSettingForm.patchValue({
        groupAddress: dialogData.row.groupAddress,
        netmask: dialogData.row.groupMask,
        rpAddress: dialogData.row.rpAddress,
        override: dialogData.row.overrideRaw,
      });
      // For combination validator
      this.dialogData.staticRpTable.splice(dialogData.row.key, 1);
    }
  }

  onDialogSubmit(): void {
    if (this.staticRpSettingForm.invalid) {
      this.staticRpSettingForm.markAllAsTouched();
      return;
    }
    const patchData = {
      groupAddr: this.staticRpSettingForm.get('groupAddress').value,
      groupMask: this.staticRpSettingForm.get('netmask').value,
      staticRp: this.staticRpSettingForm.get('rpAddress').value,
      override: this.staticRpSettingForm.get('override').value,
    };
    let request;
    if (!this.dialogData.row) {
      request = this.http.post(environment.uriRequestURL + '/setting/data/pimSm/staticRpTable', patchData);
    } else {
      request = this.http.patch(
        environment.uriRequestURL + '/setting/data/pimSm/staticRpTable/' + this.dialogData.row.key,
        patchData
      );
    }
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    request.subscribe(
      () => {
        this.dialogRef.close('submit');
        this.snackBar.open(
          this.translate.instant(`${this.dialogData.row ? 'res_entry_update_success' : 'res_entry_create_success'}`),
          '',
          {
            duration: 3000,
          }
        );
      },
      error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      }
    );
  }
}
