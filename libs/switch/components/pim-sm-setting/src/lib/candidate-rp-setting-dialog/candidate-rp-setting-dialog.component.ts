import { ChangeDetectionStrategy, Component, Inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import {
  MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA,
  MatLegacyDialogRef as MatDialogRef,
} from '@angular/material/legacy-dialog';
import { MatLegacySnackBar as MatSnackBar } from '@angular/material/legacy-snack-bar';

import { TranslateService } from '@ngx-translate/core';
import { concat } from 'rxjs';

import * as environment from '@switch-web/shared/environments/environment';
import { AppState } from '@switch-web/shared/app/app.service';
import { GlobalEvent, GlobalEventType } from '@switch-web/shared/app/global.event';
import { ErrorService } from '@switch-web/shared/service/error.service';
import { UtilsService } from '@switch-web/shared/service/utils.service';
import { ValidatorPattern } from '@switch-web/shared/validator/validators';

import { requireGroupIpAndNetmaskUnique } from '../pim-sm-setting.validator';

@Component({
  templateUrl: './candidate-rp-setting-dialog.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CandidateRpSettingDialogComponent {
  candidateRpSettingForm: UntypedFormGroup;
  netmaskOption = [];
  ifTable = [];

  constructor(
    public dialogRef: MatDialogRef<CandidateRpSettingDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public dialogData: any,
    private appState: AppState,
    private http: HttpClient,
    private snackBar: MatSnackBar,
    private utils: UtilsService,
    private translate: TranslateService,
    private errorService: ErrorService
  ) {
    this.netmaskOption = this.utils.getSubMaskOption({ rangeStart: 4, rangeEnd: 32 });
    this.ifTable = dialogData.ifTable;
    this.candidateRpSettingForm = new UntypedFormGroup(
      {
        groupAddress: new UntypedFormControl('', [
          Validators.required,
          Validators.pattern(ValidatorPattern.IPADDR_REGEX),
        ]),
        netmask: new UntypedFormControl('', Validators.required),
        ifName: new UntypedFormControl('', Validators.required),
        rpPriority: new UntypedFormControl(null, [
          Validators.required,
          Validators.min(0),
          Validators.max(255),
          Validators.pattern(ValidatorPattern.VAILD_NUMBER),
        ]),
      },
      requireGroupIpAndNetmaskUnique(dialogData.candidateRpTable)
    );
    if (dialogData.row) {
      this.candidateRpSettingForm.patchValue({
        groupAddress: dialogData.row.groupAddress,
        netmask: dialogData.row.groupMask,
        ifName: dialogData.row.ifName,
        rpPriority: dialogData.row.rpPriority,
      });
      // For combination validator
      this.dialogData.candidateRpTable.splice(dialogData.row.key, 1);
    }
  }

  onDialogSubmit(): void {
    if (this.candidateRpSettingForm.invalid) {
      this.candidateRpSettingForm.markAllAsTouched();
      return;
    }
    const patchData = {
      groupAddr: this.candidateRpSettingForm.get('groupAddress').value,
      groupMask: this.candidateRpSettingForm.get('netmask').value,
      ifName: this.candidateRpSettingForm.get('ifName').value,
      cRpPriority: this.candidateRpSettingForm.get('rpPriority').value,
    };
    const request = [];
    if (!this.dialogData.row) {
      request.push(this.http.post(environment.uriRequestURL + '/setting/data/pimSm/candidateRpTable', patchData));
      request.push(
        this.http.post(environment.uriRequestURL + '/setting/agent/pimSm/candidateRpTablePriority', {
          ifName: this.candidateRpSettingForm.get('ifName').value,
          cRpPriority: this.candidateRpSettingForm.get('rpPriority').value,
        })
      );
    } else {
      request.push(
        this.http.patch(
          environment.uriRequestURL + '/setting/data/pimSm/candidateRpTable/' + this.dialogData.row.key,
          patchData
        )
      );
      request.push(
        this.http.post(environment.uriRequestURL + '/setting/agent/pimSm/candidateRpTablePriority', {
          ifName: this.candidateRpSettingForm.get('ifName').value,
          cRpPriority: this.candidateRpSettingForm.get('rpPriority').value,
        })
      );
    }
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    concat(...request).subscribe(
      () => {},
      error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      },
      () => {
        this.dialogRef.close('submit');
        this.snackBar.open(
          this.translate.instant(`${this.dialogData.row ? 'res_entry_update_success' : 'res_entry_create_success'}`),
          '',
          {
            duration: 3000,
          }
        );
      }
    );
  }
}
