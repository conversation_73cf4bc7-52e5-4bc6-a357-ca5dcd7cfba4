<div class="app-content-container">
  <h2 class="page-title">{{ 'features.vlan.page_title' | translate }}</h2>
  <div class="page-function">
    <mat-tab-group>
      <mat-tab label="{{ 'features.vlan.global' | translate }}">
        <mat-card *ngIf="!notSupport.gvrp">
          <form [formGroup]="vlanGlobalForm" (ngSubmit)="submitVlanGlobalSetting()">
            <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
              <mat-form-field>
                <mat-select
                  id="slelect-gvrp"
                  placeholder="{{ 'features.vlan.gvrp' | translate }}"
                  formControlName="vlanGvrp"
                  required
                >
                  <mat-option id="option-gvrp-enable" [value]="true">
                    {{ 'general.common.enabled' | translate }}
                  </mat-option>
                  <mat-option id="option-gvrp-disable" [value]="false">
                    {{ 'general.common.disabled' | translate }}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="vlanGlobalForm.get('vlanGvrp').errors?.['vlanUnawareEnable']">
                  {{ 'features.vlan.vlan_unaware_gvrp_error' | translate }}</mat-error
                >
              </mat-form-field>
            </div>
            <div *ngIf="!notSupport.vlanUnaware" fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
              <mat-form-field>
                <mat-select
                  id="slelect-vlan-unaware"
                  placeholder="{{ 'features.vlan.vlan_unaware' | translate }}"
                  formControlName="vlanUnaware"
                  required
                >
                  <mat-option id="option-vlan-unaware-enable" [value]="true">
                    {{ 'general.common.enabled' | translate }}
                  </mat-option>
                  <mat-option id="option-vlan-unaware-disable" [value]="false">
                    {{ 'general.common.disabled' | translate }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div>
              <button
                id="button-gvrp-apply"
                [ngClass]="{ 'not-editable': noPermission }"
                [disabled]="noPermission"
                mat-raised-button
                color="primary"
                type="submit"
              >
                {{ 'general.button.apply' | translate }}
              </button>
            </div>
          </form>
        </mat-card>
        <mat-card *ngIf="!notSupport.mgmtVlan">
          <mat-card-title fxLayout="row" fxLayoutAlign="space-between center">
            <div ngClass="card-title">{{ 'features.vlan.mgmt_vlan_settings' | translate }}</div>
          </mat-card-title>
          <form [formGroup]="mgmtVlanForm">
            <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
              <mat-form-field>
                <mat-select
                  id="select-mgmt-vlan"
                  placeholder="{{ 'features.vlan.management_vlan' | translate }}"
                  formControlName="mgmtVlan"
                  required
                >
                  <mat-option id="option-mgmt-vlan-{{ vid }}" *ngFor="let vid of vlanList" [value]="vid"
                    >{{ vid }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
              <mat-form-field>
                <mat-select
                  id="select-mgmt-port"
                  placeholder="{{ 'features.vlan.management_port' | translate }}"
                  formControlName="mgmtPort"
                >
                  <mat-option id="option-mgmt-port-{{ row.port }}" *ngFor="let row of portEntryTableData" [value]="row">
                    {{ row.port }}</mat-option
                  >
                </mat-select>
              </mat-form-field>
              <mat-icon
                class="form-help-tip"
                fxHide.xs="true"
                matTooltip="{{ 'features.vlan.management_vlan_port_setting_hint' | translate }}"
                matTooltipPosition="right"
              >
                info
              </mat-icon>
            </div>
            <div>
              <button
                id="button-mgmt-vlan-apply"
                [ngClass]="{ 'not-editable': noPermission }"
                [disabled]="noPermission || vlanUnawareActive"
                matTooltip="{{ 'features.vlan.vlan_unaware_active_disable_hint' | translate }}"
                [matTooltipDisabled]="!vlanUnawareActive || noPermission"
                matTooltipPosition="right"
                (click)="submitMgmtVlanSetting()"
                mat-raised-button
                color="primary"
                type="button"
              >
                {{ 'general.button.apply' | translate }}
              </button>
            </div>
          </form>
        </mat-card>
      </mat-tab>
      <mat-tab label="{{ 'general.common.settings' | translate }}">
        <mat-card>
          <div fxLayout="row" fxLayoutAlign="space-between center">
            <div class="table-icon-tool-bar-div" fxFlex="70" fxFlex.lt-lg="50">
              <span
                *ngIf="vlanMgmtSelection.selected.length === 0"
                [matTooltipDisabled]="!vlanUnawareActive && vlanMgmtTableDataLength < vlanMgmtTableSize.max"
                matTooltip="{{
                  vlanUnawareActive
                    ? ('features.vlan.vlan_unaware_active_disable_hint' | translate)
                    : ('general.tooltip.vlan_size_limitation' | translate : { size: vlanMgmtTableSize.max })
                }}"
              >
                <button
                  id="button-add"
                  [matTooltipDisabled]="vlanUnawareActive || vlanMgmtTableDataLength >= vlanMgmtTableSize.max"
                  [disabled]="vlanUnawareActive || vlanMgmtTableDataLength >= vlanMgmtTableSize.max || noPermission"
                  [ngClass]="{ 'not-editable': vlanMgmtTableDataLength >= vlanMgmtTableSize.max || noPermission }"
                  (click)="createVlan()"
                  mat-icon-button
                  matTooltip="{{ 'general.common.add' | translate }}"
                >
                  <mat-icon>add_box</mat-icon>
                </button>
              </span>
              <span
                [matTooltipDisabled]="!vlanUnawareActive"
                matTooltip="{{ 'features.vlan.vlan_unaware_active_disable_hint' | translate }}"
              >
                <button
                  id="button-delete"
                  [matTooltipDisabled]="vlanUnawareActive"
                  *ngIf="vlanMgmtSelection.selected.length >= 1"
                  [ngClass]="{ 'not-editable': noPermission }"
                  [disabled]="noPermission || vlanUnawareActive"
                  (click)="deleteVlanMgmt()"
                  mat-icon-button
                  matTooltip="{{ 'general.tooltip.delete' | translate }}"
                >
                  <mat-icon>delete</mat-icon>
                </button>
              </span>
              <button
                id="button-export"
                *ngIf="vlanMgmtSelection.selected.length === 0"
                [matMenuTriggerFor]="vlanMgmtExportMenu"
                mat-icon-button
                matTooltip="{{ 'general.common.export' | translate }}"
              >
                <mat-icon svgIcon="table_arrow_down"></mat-icon>
              </button>
              <button
                id="button-help"
                *ngIf="vlanMgmtSelection.selected.length === 0"
                (click)="openSettingInfo()"
                mat-icon-button
                matTooltip="{{ 'features.vlan.vlan_setting_info_title' | translate }}"
              >
                <mat-icon>help</mat-icon>
              </button>
              <mat-menu #vlanMgmtExportMenu="matMenu" [overlapTrigger]="false">
                <button id="button-export-csv" (click)="generateDoc('csv', 'VLAN-Table')" mat-menu-item>
                  <span>{{ 'general.table_function.export_csv' | translate }}</span>
                </button>
                <button id="button-export-pdf" (click)="generateDoc('pdf', 'VLAN-Table')" mat-menu-item>
                  <span>{{ 'general.table_function.export_pdf' | translate }}</span>
                </button>
              </mat-menu>
            </div>
            <div class="filter-input-block" fxFlex="30" fxFlex.lt-lg="50">
              <mat-form-field class="form-field-search">
                <mat-icon class="icon-search" matPrefix>search</mat-icon>
                <input
                  (keyup)="updateVlanMgmtFilter($event.target.value)"
                  matInput
                  placeholder="{{ 'general.table_function.filter_desc' | translate }}"
                />
              </mat-form-field>
            </div>
          </div>
          <div id="dataContentVlan">
            <div fxShow.xs="true" fxHide="true">
              <mat-expansion-panel
                class="expansion-panel-wrapper"
                *ngFor="let row of vlanMgmtTableRows"
                hideToggle="true"
              >
                <mat-expansion-panel-header>
                  <mat-panel-title>{{ 'general.common.vlan_id' | translate }}: {{ row.vid }}</mat-panel-title>
                </mat-expansion-panel-header>
                <p>{{ 'general.common.vlan_id' | translate }} : {{ row.vid }}</p>
                <p>{{ 'general.common.name' | translate }} : {{ row.name }}</p>
                <p>{{ 'general.common_port.member_port' | translate }} : {{ row.memberPort }}</p>
                <p>{{ 'features.vlan.forbidden_port' | translate }} : {{ row.forbiddenPort }}</p>
                <mat-action-row>
                  <span
                    [matTooltipDisabled]="!vlanUnawareActive"
                    matTooltip="{{ 'features.vlan.vlan_unaware_active_disable_hint' | translate }}"
                  >
                    <button
                      [ngClass]="{ 'not-editable': noPermission }"
                      [disabled]="noPermission || vlanUnawareActive"
                      (click)="editVlanMgmt(row)"
                      mat-raised-button
                      color="primary"
                    >
                      {{ 'general.button.edit' | translate }}
                    </button>
                  </span>
                  <button
                    [ngClass]="{ 'not-editable': noPermission }"
                    [disabled]="noPermission"
                    (click)="deleteVlanMgmt(row)"
                    mat-raised-button
                    color="warn"
                  >
                    {{ 'general.button.delete' | translate }}
                  </button>
                </mat-action-row>
              </mat-expansion-panel>
              <mat-paginator
                [length]="vlanMgmtTableDataLength"
                [pageSize]="vlanMgmtTablePanelPageSize"
                [hidePageSize]="true"
                (page)="onVlanMgmtTablePageChange($event)"
              >
              </mat-paginator>
            </div>
            <div class="app-table-wrapper" fxShow="true" fxHide.xs="true">
              <table #vlanMgmtTableSort="matSort" [dataSource]="vlanMgmtTableDataSource" mat-table matSort>
                <ng-container matColumnDef="select">
                  <th *matHeaderCellDef mat-header-cell>
                    <mat-checkbox
                      id="checkbox-select-all"
                      [checked]="vlanMgmtSelection.hasValue() && isAllVlanMgmtSelected()"
                      [indeterminate]="vlanMgmtSelection.hasValue() && !isAllVlanMgmtSelected()"
                      [disabled]="vlanMgmtTableDataLength === 0"
                      (change)="$event ? vlanMgmtMasterToggle() : null"
                      color="primary"
                    >
                    </mat-checkbox>
                  </th>
                  <td *matCellDef="let row" mat-cell>
                    <mat-checkbox
                      id="checkbox-{{ row.vid }}"
                      [checked]="vlanMgmtSelection.isSelected(row)"
                      (click)="$event.stopPropagation()"
                      (change)="$event ? vlanMgmtSelection.toggle(row) : null"
                      color="primary"
                    >
                    </mat-checkbox>
                  </td>
                </ng-container>
                <ng-container matColumnDef="edit">
                  <th *matHeaderCellDef mat-header-cell></th>
                  <td *matCellDef="let row" mat-cell>
                    <mat-icon
                      class="table-icon-action"
                      *ngIf="!noPermission && !vlanUnawareActive"
                      (click)="editVlanMgmt(row)"
                      matTooltip="{{ 'general.tooltip.edit' | translate }}"
                      attr.id="button-edit-vlan-mgmt-{{ row.vid }}"
                      >edit</mat-icon
                    >
                    <mat-icon
                      class="table-not-editable"
                      *ngIf="vlanUnawareActive || noPermission"
                      matTooltip="{{ 'features.vlan.vlan_unaware_active_disable_hint' | translate }}"
                      [matTooltipDisabled]="!vlanUnawareActive"
                      matTooltipPosition="right"
                      >edit</mat-icon
                    >
                  </td>
                </ng-container>
                <ng-container matColumnDef="vid">
                  <th *matHeaderCellDef mat-header-cell mat-sort-header>{{ 'general.common.vlan_id' | translate }}</th>
                  <td *matCellDef="let row" mat-cell>{{ row.vid }}</td>
                </ng-container>
                <ng-container matColumnDef="name">
                  <th *matHeaderCellDef mat-header-cell mat-sort-header>{{ 'general.common.name' | translate }}</th>
                  <td *matCellDef="let row" mat-cell>{{ row.name }}</td>
                </ng-container>
                <ng-container matColumnDef="member-port">
                  <th *matHeaderCellDef mat-header-cell mat-sort-header>
                    {{ 'general.common_port.member_port' | translate }}
                  </th>
                  <td *matCellDef="let row" mat-cell>{{ row.memberPort }}</td>
                </ng-container>
                <ng-container matColumnDef="forbidden-port">
                  <th *matHeaderCellDef mat-header-cell mat-sort-header>
                    {{ 'features.vlan.forbidden_port' | translate }}
                  </th>
                  <td *matCellDef="let row" mat-cell>{{ row.forbiddenPort }}</td>
                </ng-container>
                <ng-container matColumnDef="dummy">
                  <th *matHeaderCellDef mat-header-cell></th>
                  <td *matCellDef="let row" mat-cell></td>
                </ng-container>
                <tr *matHeaderRowDef="vlanMgmtTableDisplayedColumns; sticky: true" mat-header-row></tr>
                <tr
                  *matRowDef="let row; columns: vlanMgmtTableDisplayedColumns"
                  [class.selected-row]="vlanMgmtSelection.isSelected(row)"
                  mat-row
                ></tr>
              </table>
              <mat-paginator
                class="vlanMgmtTablePaginator"
                #vlanMgmtTablePaginator
                [pageSizeOptions]="[5, 10, 100, 1000]"
                [pageSize]="5"
                showFirstLastButtons
              ></mat-paginator>
              <div class="table-max-count">
                {{ 'general.table_function.limit_count' | translate }} {{ vlanMgmtTableSize.max }}
              </div>
            </div>
          </div>
        </mat-card>
        <mat-card>
          <div fxLayout="row" fxLayoutAlign="space-between center">
            <div class="table-icon-tool-bar-div" fxFlex="70" fxFlex.lt-lg="50">
              <button
                id="button-port-entry-refresh"
                (click)="refresh()"
                mat-icon-button
                matTooltip="{{ 'general.common.refresh' | translate }}"
              >
                <mat-icon>refresh</mat-icon>
              </button>
            </div>
            <div class="filter-input-block" fxFlex="30" fxFlex.lt-lg="50">
              <mat-form-field class="form-field-search">
                <mat-icon class="icon-search" matPrefix>search</mat-icon>
                <input
                  (keyup)="updatePortEntryFilter($event.target.value)"
                  matInput
                  placeholder="{{ 'general.table_function.filter_desc' | translate }}"
                />
              </mat-form-field>
            </div>
          </div>
          <div id="dataContent">
            <div fxShow.xs="true" fxHide="true">
              <mat-expansion-panel class="expansion-panel-wrapper" *ngFor="let row of portEntryTableRows">
                <mat-expansion-panel-header>
                  <mat-panel-title [ngClass]="utils.getPortClass(row)"
                    >{{ 'general.common_port.port' | translate }}: {{ row.port }}</mat-panel-title
                  >
                </mat-expansion-panel-header>
                <p>{{ 'general.common_port.port' | translate }}: {{ row.port }}</p>
                <p>{{ 'general.common.mode' | translate }}: {{ row.mode }}</p>
                <p>{{ 'features.vlan.pvid' | translate }}: {{ row.pvid }}</p>
                <p>{{ 'features.vlan.gvrp' | translate }}: {{ row.gvrp }}</p>
                <p>{{ 'features.vlan.untagged_vlan' | translate }}: {{ row.untaggedVlan }}</p>
                <p>{{ 'features.vlan.tagged_vlan' | translate }}: {{ row.taggedVlan }}</p>
                <mat-action-row>
                  <span
                    [matTooltipDisabled]="!vlanUnawareActive"
                    matTooltip="{{ 'features.vlan.vlan_unaware_active_disable_hint' | translate }}"
                  >
                    <button
                      [ngClass]="{ 'not-editable': noPermission }"
                      [disabled]="noPermission || vlanUnawareActive"
                      (click)="editPortEntry(row)"
                      color="primary"
                      mat-raised-button
                    >
                      {{ 'general.button.edit' | translate }}
                    </button>
                  </span>
                </mat-action-row>
              </mat-expansion-panel>
              <mat-paginator
                [length]="portEntryTableDataLength"
                [pageSize]="portEntryTablePanelPageSize"
                [hidePageSize]="true"
                (page)="onPortEntryTablePageChange($event)"
              >
              </mat-paginator>
            </div>
            <div fxShow="true" fxHide.xs="true">
              <div class="app-table-wrapper">
                <table #portEntryTableSort="matSort" [dataSource]="portEntryTableDataSource" mat-table matSort>
                  <ng-container matColumnDef="edit">
                    <th *matHeaderCellDef mat-header-cell></th>
                    <td *matCellDef="let row" mat-cell>
                      <mat-icon
                        class="table-icon-action"
                        *ngIf="!noPermission && !vlanUnawareActive"
                        (click)="editPortEntry(row)"
                        matTooltip="{{ 'general.tooltip.edit' | translate }}"
                        attr.id="button-edit-port-{{ row.port }}"
                      >
                        edit
                      </mat-icon>
                      <mat-icon
                        class="table-not-editable"
                        *ngIf="vlanUnawareActive || noPermission"
                        matTooltip="{{ 'features.vlan.vlan_unaware_active_disable_hint' | translate }}"
                        [matTooltipDisabled]="!vlanUnawareActive"
                        matTooltipPosition="right"
                        >edit</mat-icon
                      >
                    </td>
                  </ng-container>
                  <ng-container matColumnDef="port">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'general.common_port.port' | translate }}
                    </th>
                    <td *matCellDef="let row" [ngClass]="utils.getPortClass(row)" mat-cell>{{ row.port }}</td>
                  </ng-container>
                  <ng-container matColumnDef="vlan-mode">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>{{ 'general.common.mode' | translate }}</th>
                    <td *matCellDef="let row" mat-cell>{{ row.mode }}</td>
                  </ng-container>
                  <ng-container matColumnDef="pvid">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>{{ 'features.vlan.pvid' | translate }}</th>
                    <td *matCellDef="let row" mat-cell>{{ row.pvid }}</td>
                  </ng-container>
                  <ng-container matColumnDef="gvrp">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>{{ 'features.vlan.gvrp' | translate }}</th>
                    <td *matCellDef="let row" mat-cell>{{ row.gvrp }}</td>
                  </ng-container>
                  <ng-container matColumnDef="untagged-vlan">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.vlan.untagged_vlan' | translate }}
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.untaggedVlan }}</td>
                  </ng-container>
                  <ng-container matColumnDef="tagged-vlan">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.vlan.tagged_vlan' | translate }}
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.taggedVlan }}</td>
                  </ng-container>
                  <ng-container matColumnDef="dummy">
                    <th *matHeaderCellDef mat-header-cell></th>
                    <td *matCellDef="let row" mat-cell></td>
                  </ng-container>
                  <tr *matHeaderRowDef="portEntryTableDisplayedColumns; sticky: true" mat-header-row></tr>
                  <tr *matRowDef="let row; columns: portEntryTableDisplayedColumns" mat-row></tr>
                </table>
              </div>
              <mat-paginator
                #portEntryTablePaginator
                [ngClass]="{ disableChangePage: portEntryTableDataLength <= 50 }"
                [hidePageSize]="true"
                [showFirstLastButtons]="false"
              ></mat-paginator>
            </div>
          </div>
        </mat-card>
      </mat-tab>
      <mat-tab label="{{ 'general.common.status' | translate }}">
        <mat-card>
          <mat-card-header fxLayout="column">
            <mat-card-title fxLayout="row" fxLayoutAlign="space-between center">
              <div ngClass="card-title">{{ 'features.vlan.port_mode_table_title' | translate }}</div>
            </mat-card-title>
          </mat-card-header>
          <div fxLayout="row" fxLayoutAlign="space-between center">
            <div class="table-icon-tool-bar-div" fxFlex="70" fxFlex.lt-lg="50">
              <button
                id="button-port-entry-refresh"
                (click)="refresh()"
                mat-icon-button
                matTooltip="{{ 'general.common.refresh' | translate }}"
              >
                <mat-icon>refresh</mat-icon>
              </button>
              <button
                id="button-export"
                [matMenuTriggerFor]="portModeStatusExportMenu"
                mat-icon-button
                matTooltip="{{ 'general.common.export' | translate }}"
              >
                <mat-icon svgIcon="table_arrow_down"></mat-icon>
              </button>
              <mat-menu #portModeStatusExportMenu="matMenu" [overlapTrigger]="false">
                <button
                  id="button-export-csv"
                  (click)="generateDoc('csv', 'Port-Mode-VLAN-Mapping-Table')"
                  mat-menu-item
                >
                  <span>{{ 'general.table_function.export_csv' | translate }}</span>
                </button>
                <button
                  id="button-export-pdf"
                  (click)="generateDoc('pdf', 'Port-Mode-VLAN-Mapping-Table')"
                  mat-menu-item
                >
                  <span>{{ 'general.table_function.export_pdf' | translate }}</span>
                </button>
              </mat-menu>
            </div>
            <div class="filter-input-block" fxFlex="30" fxFlex.lt-lg="50">
              <mat-form-field class="form-field-search">
                <mat-icon class="icon-search" matPrefix>search</mat-icon>
                <input
                  (keyup)="updatePortModeStatusFilter($event.target.value)"
                  matInput
                  placeholder="{{ 'general.table_function.filter_desc' | translate }}"
                />
              </mat-form-field>
            </div>
          </div>
          <div id="dataContentPortModeTable">
            <div fxShow.xs="true" fxHide="true">
              <mat-expansion-panel
                class="expansion-panel-wrapper"
                *ngFor="let row of portModeStatusTableRows"
                hideToggle="true"
              >
                <mat-expansion-panel-header>
                  <mat-panel-title>{{ 'general.common.vlan_id' | translate }}: {{ row.vid }}</mat-panel-title>
                </mat-expansion-panel-header>
                <p>{{ 'general.common.vlan_id' | translate }} : {{ row.vid }}</p>
                <p>{{ 'general.common.name' | translate }} : {{ row.name }}</p>
                <p>{{ 'general.common.status' | translate }} : {{ row.status }}</p>
                <p>{{ 'features.vlan.hybrid_port' | translate }} : {{ row.hybridPort }}</p>
                <p>{{ 'features.vlan.trunk_port' | translate }} : {{ row.trunkPort }}</p>
                <p>{{ 'features.vlan.access_port' | translate }} : {{ row.accessPort }}</p>
              </mat-expansion-panel>
              <mat-paginator
                [length]="portModeStatusTableDataLength"
                [pageSize]="portModeStatusTablePanelPageSize"
                [hidePageSize]="true"
                (page)="onPortModeStatusTablePageChange($event)"
              >
              </mat-paginator>
            </div>
            <div class="app-table-wrapper" fxShow="true" fxHide.xs="true">
              <table #portModeStatusTableSort="matSort" [dataSource]="portModeStatusTableDataSource" mat-table matSort>
                <ng-container matColumnDef="vid">
                  <th *matHeaderCellDef mat-header-cell mat-sort-header>{{ 'general.common.vlan_id' | translate }}</th>
                  <td *matCellDef="let row" mat-cell>{{ row.vid }}</td>
                </ng-container>
                <ng-container matColumnDef="name">
                  <th *matHeaderCellDef mat-header-cell mat-sort-header>{{ 'general.common.name' | translate }}</th>
                  <td *matCellDef="let row" mat-cell>{{ row.name }}</td>
                </ng-container>
                <ng-container matColumnDef="status">
                  <th *matHeaderCellDef mat-header-cell mat-sort-header>{{ 'general.common.status' | translate }}</th>
                  <td *matCellDef="let row" mat-cell>{{ row.status }}</td>
                </ng-container>
                <ng-container matColumnDef="hybrid-port">
                  <th *matHeaderCellDef mat-header-cell mat-sort-header>
                    {{ 'features.vlan.hybrid_port' | translate }}
                  </th>
                  <td *matCellDef="let row" mat-cell>{{ row.hybridPort }}</td>
                </ng-container>
                <ng-container matColumnDef="trunk-port">
                  <th *matHeaderCellDef mat-header-cell mat-sort-header>
                    {{ 'features.vlan.trunk_port' | translate }}
                  </th>
                  <td *matCellDef="let row" mat-cell>{{ row.trunkPort }}</td>
                </ng-container>
                <ng-container matColumnDef="access-port">
                  <th *matHeaderCellDef mat-header-cell mat-sort-header>
                    {{ 'features.vlan.access_port' | translate }}
                  </th>
                  <td *matCellDef="let row" mat-cell>
                    <div *ngIf="row.accessPortRaw.length > 0">{{ row.accessPort }}</div>
                    <div *ngIf="row.vaPortRaw.length > 0" fxLayout="row">
                      <div fxLayout="row" fxFlex="120px">
                        <div>{{ 'features.vlan.vlan_assignment' | translate }}:</div>
                      </div>
                      <div>{{ row.vaPort }}</div>
                    </div>
                  </td>
                </ng-container>
                <ng-container matColumnDef="dummy">
                  <th *matHeaderCellDef mat-header-cell></th>
                  <td *matCellDef="let row" mat-cell></td>
                </ng-container>
                <tr *matHeaderRowDef="portModeStatusTableDisplayedColumns; sticky: true" mat-header-row></tr>
                <tr *matRowDef="let row; columns: portModeStatusTableDisplayedColumns" mat-row></tr>
              </table>
              <mat-paginator
                class="portModeStatusTablePaginator"
                #portModeStatusTablePaginator
                [pageSizeOptions]="[5, 10, 100, 1000]"
                [pageSize]="5"
                showFirstLastButtons
              ></mat-paginator>
            </div>
          </div>
        </mat-card>
        <mat-card>
          <mat-card-header fxLayout="column">
            <mat-card-title fxLayout="row" fxLayoutAlign="space-between center">
              <div ngClass="card-title">{{ 'features.vlan.egress_tagged_table_title' | translate }}</div>
            </mat-card-title>
          </mat-card-header>
          <div fxLayout="row" fxLayoutAlign="space-between center">
            <div class="table-icon-tool-bar-div" fxFlex="70" fxFlex.lt-lg="50">
              <button
                id="button-port-entry-refresh"
                (click)="refresh()"
                mat-icon-button
                matTooltip="{{ 'general.common.refresh' | translate }}"
              >
                <mat-icon>refresh</mat-icon>
              </button>
              <button
                id="button-export"
                [matMenuTriggerFor]="egressPortStatusExportMenu"
                mat-icon-button
                matTooltip="{{ 'general.common.export' | translate }}"
              >
                <mat-icon svgIcon="table_arrow_down"></mat-icon>
              </button>
              <mat-menu #egressPortStatusExportMenu="matMenu" [overlapTrigger]="false">
                <button
                  id="button-export-csv"
                  (click)="generateDoc('csv', 'Egress-Untagged-Tagged-Table')"
                  mat-menu-item
                >
                  <span>{{ 'general.table_function.export_csv' | translate }}</span>
                </button>
                <button
                  id="button-export-pdf"
                  (click)="generateDoc('pdf', 'Egress-Untagged-Tagged-Table')"
                  mat-menu-item
                >
                  <span>{{ 'general.table_function.export_pdf' | translate }}</span>
                </button>
              </mat-menu>
            </div>
            <div class="filter-input-block" fxFlex="30" fxFlex.lt-lg="50">
              <mat-form-field class="form-field-search">
                <mat-icon class="icon-search" matPrefix>search</mat-icon>
                <input
                  (keyup)="updateEgressPortStatusFilter($event.target.value)"
                  matInput
                  placeholder="{{ 'general.table_function.filter_desc' | translate }}"
                />
              </mat-form-field>
            </div>
          </div>
          <div id="dataContentEgressPortTable">
            <div fxShow.xs="true" fxHide="true">
              <mat-expansion-panel
                class="expansion-panel-wrapper"
                *ngFor="let row of egressPortStatusTableRows"
                hideToggle="true"
              >
                <mat-expansion-panel-header>
                  <mat-panel-title>{{ 'general.common.vlan_id' | translate }}: {{ row.vid }}</mat-panel-title>
                </mat-expansion-panel-header>
                <p>{{ 'general.common.vlan_id' | translate }} : {{ row.vid }}</p>
                <p>{{ 'general.common.name' | translate }} : {{ row.name }}</p>
                <p>{{ 'general.common.status' | translate }} : {{ row.status }}</p>
                <p>{{ 'features.vlan.untagged_port' | translate }} : {{ row.untaggedPort }}</p>
                <p>{{ 'features.vlan.tagged_port' | translate }} : {{ row.taggedPort }}</p>
                <p>{{ 'features.vlan.forbidden_port' | translate }} : {{ row.forbiddenPort }}</p>
              </mat-expansion-panel>
              <mat-paginator
                [length]="egressPortStatusTableDataLength"
                [pageSize]="egressPortStatusTablePanelPageSize"
                [hidePageSize]="true"
                (page)="onEgressPortStatusTablePageChange($event)"
              >
              </mat-paginator>
            </div>
            <div class="app-table-wrapper" fxShow="true" fxHide.xs="true">
              <table
                #egressPortStatusTableSort="matSort"
                [dataSource]="egressPortStatusTableDataSource"
                mat-table
                matSort
              >
                <ng-container matColumnDef="vid">
                  <th *matHeaderCellDef mat-header-cell mat-sort-header>{{ 'general.common.vlan_id' | translate }}</th>
                  <td *matCellDef="let row" mat-cell>{{ row.vid }}</td>
                </ng-container>
                <ng-container matColumnDef="name">
                  <th *matHeaderCellDef mat-header-cell mat-sort-header>{{ 'general.common.name' | translate }}</th>
                  <td *matCellDef="let row" mat-cell>{{ row.name }}</td>
                </ng-container>
                <ng-container matColumnDef="status">
                  <th *matHeaderCellDef mat-header-cell mat-sort-header>{{ 'general.common.status' | translate }}</th>
                  <td *matCellDef="let row" mat-cell>{{ row.status }}</td>
                </ng-container>
                <ng-container matColumnDef="untagged-port">
                  <th *matHeaderCellDef mat-header-cell mat-sort-header>
                    {{ 'features.vlan.untagged_port' | translate }}
                  </th>
                  <td *matCellDef="let row" mat-cell>{{ row.untaggedPort }}</td>
                </ng-container>
                <ng-container matColumnDef="tagged-port">
                  <th *matHeaderCellDef mat-header-cell mat-sort-header>
                    {{ 'features.vlan.tagged_port' | translate }}
                  </th>
                  <td *matCellDef="let row" mat-cell>{{ row.taggedPort }}</td>
                </ng-container>
                <ng-container matColumnDef="forbidden-port">
                  <th *matHeaderCellDef mat-header-cell mat-sort-header>
                    {{ 'features.vlan.forbidden_port' | translate }}
                  </th>
                  <td *matCellDef="let row" mat-cell>{{ row.forbiddenPort }}</td>
                </ng-container>
                <ng-container matColumnDef="dummy">
                  <th *matHeaderCellDef mat-header-cell></th>
                  <td *matCellDef="let row" mat-cell></td>
                </ng-container>
                <tr *matHeaderRowDef="egressPortStatusTableDisplayedColumns; sticky: true" mat-header-row></tr>
                <tr *matRowDef="let row; columns: egressPortStatusTableDisplayedColumns" mat-row></tr>
              </table>
              <mat-paginator
                class="egressPortStatusTablePaginator"
                #egressPortStatusTablePaginator
                [pageSizeOptions]="[5, 10, 100, 1000]"
                [pageSize]="5"
                showFirstLastButtons
              ></mat-paginator>
            </div>
          </div>
        </mat-card>
      </mat-tab>
    </mat-tab-group>
  </div>
</div>
