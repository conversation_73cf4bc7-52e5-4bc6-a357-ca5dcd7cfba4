import { AfterViewInit, ChangeDetectionStrategy, Component, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { FormGroup, UntypedFormControl, UntypedFormGroup, ValidatorFn, Validators } from '@angular/forms';
import { SelectionModel } from '@angular/cdk/collections';
import { MatLegacyDialog as MatDialog } from '@angular/material/legacy-dialog';
import { MatLegacyPaginator as MatPaginator } from '@angular/material/legacy-paginator';
import { MatLegacySnackBar as MatSnackBar } from '@angular/material/legacy-snack-bar';
import { MatLegacyTableDataSource as MatTableDataSource } from '@angular/material/legacy-table';
import { MatSort } from '@angular/material/sort';

import { TranslateService } from '@ngx-translate/core';
import { cloneDeep, differenceBy, find, findIndex, forEach, sortBy, without } from 'lodash-es';
import { concat, forkJoin, Observable } from 'rxjs';

import * as environment from '@switch-web/shared/environments/environment';
import { AppState } from '@switch-web/shared/app/app.service';
import { GlobalEvent, GlobalEventType } from '@switch-web/shared/app/global.event';
import { ColumnWidth, GenerateDocService } from '@switch-web/shared/assets/mx-service/generate-doc.service';
import { mediumDialogConfig, smallDialogConfig } from '@switch-web/shared/dialog/dialog-config.service';
import { AuthService } from '@switch-web/shared/service/auth.service';
import { ErrorService } from '@switch-web/shared/service/error.service';
import { UtilsService } from '@switch-web/shared/service/utils.service';

import { VlanMgmtDeleteDialogComponent } from './vlan-mgmt-delete-dialog/vlan-mgmt-delete-dialog';
import { VlanMgmtSettingDialogComponent } from './vlan-mgmt-setting-dialog/vlan-mgmt-setting-dialog';
import { VlanPortEntrySettingDialogComponent } from './vlan-port-entry-setting-dialog/vlan-port-entry-setting-dialog';
import { VlanSettingInfoComponent } from './vlan-setting-info/vlan-setting-info';
import { VlanPortMode } from './vlan.def';

@Component({
  templateUrl: './vlan.component.html',
  styleUrls: ['./vlan.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class VlanComponent implements AfterViewInit {
  @ViewChild('vlanMgmtTableSort') vlanMgmtTableSort: MatSort;
  @ViewChild('vlanMgmtTablePaginator') vlanMgmtTablePaginator: MatPaginator;
  @ViewChild('portEntryTableSort') portEntryTableSort: MatSort;
  @ViewChild('portEntryTablePaginator') portEntryTablePaginator: MatPaginator;
  @ViewChild('portModeStatusTableSort') portModeStatusTableSort: MatSort;
  @ViewChild('portModeStatusTablePaginator') portModeStatusTablePaginator: MatPaginator;
  @ViewChild('egressPortStatusTableSort') egressPortStatusTableSort: MatSort;
  @ViewChild('egressPortStatusTablePaginator') egressPortStatusTablePaginator: MatPaginator;

  isAdvancedMode = false;
  noPermission = false;
  vlanUnawareActive = false;
  notSupport = {
    gvrp: false,
    forbiddenPort: false,
    mgmtVlan: false,
    vlanUnaware: false,
  };
  portMap;
  vlanList;
  vlanPortMode = VlanPortMode;
  vlanGlobalForm = new UntypedFormGroup(
    {
      vlanGvrp: new UntypedFormControl(false, Validators.required),
      vlanUnaware: new UntypedFormControl(false, Validators.required),
    },
    this.checkVlanUnaware()
  );
  mgmtVlanForm = new UntypedFormGroup({
    mgmtVlan: new UntypedFormControl(null, Validators.required),
    mgmtPort: new UntypedFormControl(null),
  });

  vlanMgmtTableDisplayedColumns: string[] = ['select', 'edit', 'vid', 'name', 'member-port', 'forbidden-port', 'dummy'];
  vlanMgmtTableDataSource: MatTableDataSource<any> = new MatTableDataSource();
  vlanMgmtSelection = new SelectionModel<any>(true, []);
  vlanMgmtTableSize;
  vlanMgmtTableData = [];
  vlanMgmtTableRows = [];
  vlanMgmtTableDataLength;
  vlanMgmtTablePanelPageSize = 5;

  portEntryTableDisplayedColumns: string[] = [
    'edit',
    'port',
    'vlan-mode',
    'pvid',
    'gvrp',
    'untagged-vlan',
    'tagged-vlan',
    'dummy',
  ];
  portEntryTableDataSource: MatTableDataSource<any> = new MatTableDataSource();
  portEntryTableData = [];
  portEntryTableRows = [];
  portEntryTableDataLength;
  portEntryTablePanelPageSize = 20;

  portModeStatusTableDisplayedColumns: string[] = [
    'vid',
    'name',
    'status',
    'hybrid-port',
    'trunk-port',
    'access-port',
    'dummy',
  ];
  portModeStatusTableDataSource: MatTableDataSource<any> = new MatTableDataSource();
  portModeStatusTableData = [];
  portModeStatusTableRows = [];
  portModeStatusTableDataLength;
  portModeStatusTablePanelPageSize = 20;

  egressPortStatusTableDisplayedColumns: string[] = [
    'vid',
    'name',
    'status',
    'untagged-port',
    'tagged-port',
    'forbidden-port',
    'dummy',
  ];
  egressPortStatusTableDataSource: MatTableDataSource<any> = new MatTableDataSource();
  egressPortStatusTableData = [];
  egressPortStatusTableRows = [];
  egressPortStatusTableDataLength;
  egressPortStatusTablePanelPageSize = 20;

  private portSize;
  private portChannelMapData;
  private allPortMapData;
  private allPortMap;
  private vlanAllPortMap;
  private uriRequestData = {
    stdvlan: null,
    mxvlan: null,
    portTable: null,
    vlanCurrentTable: null,
  };

  constructor(
    public utils: UtilsService,
    private appState: AppState,
    private auth: AuthService,
    private http: HttpClient,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private errorService: ErrorService,
    private translate: TranslateService,
    private docService: GenerateDocService,
    private activatedRoute: ActivatedRoute
  ) {
    this.isAdvancedMode = this.utils.getCurrentBrowseMode();
    this.noPermission = this.auth.authNodes.vlan;
    this.activatedRoute.data.subscribe(data => {
      this.vlanMgmtTableSize = data.tableSize.vlanTable;
      forEach(data.notSupport, feat => {
        this.notSupport[feat] = true;
      });
    });
    if (!this.isAdvancedMode) {
      this.vlanMgmtTableDisplayedColumns = without(this.vlanMgmtTableDisplayedColumns, 'forbidden-port');
      this.egressPortStatusTableDisplayedColumns = without(
        this.egressPortStatusTableDisplayedColumns,
        'forbidden-port'
      );
    }
    if (this.notSupport.gvrp) {
      this.portEntryTableDisplayedColumns = without(this.portEntryTableDisplayedColumns, 'gvrp');
    }
    if (this.notSupport.vlanUnaware) {
      this.vlanGlobalForm.get['vlanUnaware'].disable();
    }
    if (this.notSupport.forbiddenPort) {
      this.vlanMgmtTableDisplayedColumns = without(this.vlanMgmtTableDisplayedColumns, 'forbidden-port');
      this.egressPortStatusTableDisplayedColumns = without(
        this.egressPortStatusTableDisplayedColumns,
        'forbidden-port'
      );
    }
    this.portMap = JSON.parse(localStorage.getItem('portMap'));
    this.portChannelMapData = JSON.parse(localStorage.getItem('portChannelMapData'));
    this.allPortMapData = JSON.parse(localStorage.getItem('allPortMapData'));
    this.allPortMap = JSON.parse(localStorage.getItem('allPortMap'));
    this.vlanAllPortMap = this.allPortMap;
  }

  ngAfterViewInit(): void {
    this.setupTableSetting();
    this.refreshSettingTable().subscribe({
      complete: () => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
      },
      error: error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      },
    });
  }

  refreshSettingTable(): Observable<any> {
    return new Observable(observer => {
      this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
      const portData = {
        portTable: [],
        portChannelTable: [],
      };
      this.vlanMgmtTableData = [];
      this.portEntryTableData = [];
      this.portModeStatusTableData = [];
      this.egressPortStatusTableData = [];
      this.vlanList = [];

      forkJoin([
        this.http.get(environment.uriRequestURL + '/setting/data/stdvlan'),
        this.http.get(environment.uriRequestURL + '/setting/data/mxvlan'),
        this.http.get(environment.uriRequestURL + '/status/portInfo/portTable'),
        this.http.get(environment.uriRequestURL + '/status/dot1qInfo/vlanCurrentTable'),
      ]).subscribe(
        (data: any) => {
          this.uriRequestData['stdvlan'] = data[0];
          this.uriRequestData['mxvlan'] = data[1];
          this.uriRequestData['portTable'] = data[2];
          this.uriRequestData['vlanCurrentTable'] = data[3];
          this.portSize = this.uriRequestData['stdvlan'].portTable.length;

          // Global setting
          this.vlanGlobalForm.patchValue({
            vlanGvrp: this.uriRequestData['stdvlan'].gvrpEnable,
            vlanUnaware: this.uriRequestData['mxvlan'].vlanUnawareEnable,
          });
          this.vlanUnawareActive = this.uriRequestData['mxvlan'].vlanUnawareEnable ?? false;
          if (!this.notSupport.mgmtVlan) {
            this.mgmtVlanForm.patchValue({
              mgmtVlan: this.uriRequestData['mxvlan'].mgmtVlan[0],
              mgmtPort: null,
            });
          }

          // Vlan mgmt table & status table
          this.uriRequestData['stdvlan'].vlanTable.forEach((vlan, index) => {
            if (!vlan.valid) {
              return true;
            }
            const egressPortName = [];
            const taggedPortName = [];
            const untaggedPortName = [];
            const forbiddenPortName = [];
            const vlanDataFromVlanCurrentTable = cloneDeep(
              find(this.uriRequestData['vlanCurrentTable'], vlanEntry => vlanEntry.vid === vlan.vid)
            );
            this.vlanList.push(vlan.vid);

            vlan.egressPortsPbmp.forEach((egressPort, pbmpIndex) => {
              let portType;
              let portIndex;
              if (pbmpIndex < this.portSize) {
                portType = 'portTable';
                portIndex = +pbmpIndex;
              } else {
                portType = 'portChannelTable';
                portIndex = this.utils.getAliasNumberByIndex(+pbmpIndex);
              }
              if (portIndex !== null) {
                if (index === 0) {
                  portData[portType][portIndex] = new Object();
                  portData[portType][portIndex].untaggedVlan = [];
                  portData[portType][portIndex].taggedVlan = [];
                }

                if (egressPort) {
                  egressPortName.push(this.allPortMapData[pbmpIndex]);
                  if (vlan.untaggedPortsPbmp[pbmpIndex]) {
                    portData[portType][portIndex].untaggedVlan.push(vlan.vid);
                  } else {
                    portData[portType][portIndex].taggedVlan.push(vlan.vid);
                  }
                }

                if (vlanDataFromVlanCurrentTable.egressPortsPbmp[pbmpIndex]) {
                  if (vlanDataFromVlanCurrentTable.untaggedPortsPbmp[pbmpIndex]) {
                    untaggedPortName.push(this.allPortMapData[pbmpIndex]);
                  } else {
                    taggedPortName.push(this.allPortMapData[pbmpIndex]);
                  }
                }

                if (!this.notSupport.forbiddenPort && vlan.forbiddenEgressPortsPbmp[pbmpIndex]) {
                  forbiddenPortName.push(this.allPortMapData[pbmpIndex]);
                }
              }
            });

            this.vlanMgmtTableData.push({
              key: index,
              vid: vlan.vid,
              name: vlan.vlanName,
              memberPort: this.utils.addSpace(egressPortName),
              forbiddenPort: this.utils.addSpace(forbiddenPortName),
              egressPortsPbmpRaw: vlan.egressPortsPbmp,
              untaggedPortsPbmpRaw: vlan.untaggedPortsPbmp,
              forbiddenEgressPortsPbmpRaw: vlan.forbiddenEgressPortsPbmp,
            });
            this.portModeStatusTableData.push({
              vid: vlan.vid,
              name: vlan.vlanName,
              status: this.translate.instant('general.common.permanent'),
              hybridPort: '',
              hybridPortRaw: [],
              trunkPort: '',
              trunkPortRaw: [],
              accessPort: '',
              accessPortRaw: [],
              vaPort: '',
              vaPortRaw: [],
              egressPortsPbmpRaw: vlanDataFromVlanCurrentTable.egressPortsPbmp,
            });
            this.egressPortStatusTableData.push({
              vid: vlan.vid,
              name: vlan.vlanName,
              status: this.translate.instant('general.common.permanent'),
              untaggedPort: this.utils.addSpace(untaggedPortName),
              taggedPort: this.utils.addSpace(taggedPortName),
              forbiddenPort: this.utils.addSpace(forbiddenPortName),
            });
          });

          // dynamic GVRP = status table
          if (!this.notSupport.gvrp) {
            const dynamicGvrp = differenceBy(this.uriRequestData['vlanCurrentTable'], this.vlanMgmtTableData, 'vid');
            if (dynamicGvrp.length > 0) {
              dynamicGvrp.forEach((gvrpVlan: any) => {
                const untaggedPortsName = [];
                const taggedPortName = [];

                gvrpVlan.egressPortsPbmp.forEach((egressPort, pbmpIndex) => {
                  if (egressPort) {
                    if (gvrpVlan.untaggedPortsPbmp[pbmpIndex]) {
                      untaggedPortsName.push(this.allPortMapData[pbmpIndex]);
                    } else {
                      taggedPortName.push(this.allPortMapData[pbmpIndex]);
                    }
                  }
                });

                this.portModeStatusTableData.push({
                  vid: gvrpVlan.vid,
                  name: '',
                  status: this.translate.instant('features.vlan.dynamic_gvrp'),
                  hybridPort: '',
                  hybridPortRaw: [],
                  trunkPort: '',
                  trunkPortRaw: [],
                  accessPort: '',
                  accessPortRaw: [],
                  vaPort: '',
                  vaPortRaw: [],
                  egressPortsPbmpRaw: gvrpVlan.egressPortsPbmp,
                });
                this.egressPortStatusTableData.push({
                  vid: gvrpVlan.vid,
                  name: '',
                  status: this.translate.instant('features.vlan.dynamic_gvrp'),
                  untaggedPort: this.utils.addSpace(untaggedPortsName),
                  taggedPort: this.utils.addSpace(taggedPortName),
                });
              });
            }
          }

          // Sort by vid
          this.vlanMgmtTableData = sortBy(this.vlanMgmtTableData, [
            o => {
              return o.vid;
            },
          ]);
          this.portModeStatusTableData = sortBy(this.portModeStatusTableData, [
            o => {
              return o.vid;
            },
          ]);
          this.egressPortStatusTableData = sortBy(this.egressPortStatusTableData, [
            o => {
              return o.vid;
            },
          ]);
          this.vlanList = sortBy(this.vlanList);

          // Port entry table - stdvlan URI
          for (const portType of ['portTable', 'portChannelTable']) {
            if (this.uriRequestData['stdvlan'][portType] === undefined) {
              continue;
            }
            this.uriRequestData['stdvlan'][portType].forEach((port, index) => {
              let portName = '';
              let pbmpIndex;
              let taggedVlan = [];
              let untaggedVlan = [];
              let isTrunkData;

              if (portType === 'portTable') {
                isTrunkData = false;
                portName = this.utils.getAllPortMapName(index);
                pbmpIndex = index;
              } else if (portType === 'portChannelTable') {
                isTrunkData = true;
                portName = this.portChannelMapData[index];
                pbmpIndex = this.utils.getIndexByAliasNumber(index);
              }

              if (pbmpIndex !== null && portName !== null) {
                taggedVlan = sortBy(portData[portType][index].taggedVlan);
                untaggedVlan = sortBy(portData[portType][index].untaggedVlan);

                // port entry table - mxvlan URI
                const modeRawData = this.uriRequestData['mxvlan'][portType][index].vlanPortType;
                let modeData = '';
                switch (modeRawData) {
                  case VlanPortMode.ACCESS:
                    modeData = this.translate.instant('features.vlan.access');
                    this.portModeStatusTableData.forEach(vlan => {
                      if (vlan.egressPortsPbmpRaw[pbmpIndex]) {
                        if (this.uriRequestData.portTable[index].vlanAssignment) {
                          vlan.vaPortRaw.push(portName);
                        } else {
                          vlan.accessPortRaw.push(portName);
                        }
                      }
                    });
                    break;
                  case VlanPortMode.TRUNK:
                    modeData = this.translate.instant('features.vlan.trunk');
                    this.portModeStatusTableData.forEach(vlan => {
                      if (vlan.egressPortsPbmpRaw[pbmpIndex]) {
                        vlan.trunkPortRaw.push(portName);
                      }
                    });
                    break;
                  case VlanPortMode.HYBRID:
                    modeData = this.translate.instant('features.vlan.hybrid');
                    this.portModeStatusTableData.forEach(vlan => {
                      if (vlan.egressPortsPbmpRaw[pbmpIndex]) {
                        vlan.hybridPortRaw.push(portName);
                      }
                    });
                    break;
                }

                // vlanAllPortMap is for vlan-mgmt setting dialog
                const allPortMapIndex = this.utils.getAllPortMapIndex(pbmpIndex);
                this.vlanAllPortMap[allPortMapIndex]['portMode'] = modeRawData;
                this.vlanAllPortMap[allPortMapIndex]['pvid'] = port.pvid;

                this.portEntryTableData.push({
                  key: index,
                  port: portName,
                  mode: modeData,
                  modeRaw: modeRawData,
                  pvid: port.pvid,
                  gvrp: port.portGvrpEnable
                    ? this.translate.instant('general.common.enabled')
                    : this.translate.instant('general.common.disabled'),
                  gvrpRaw: port.portGvrpEnable,
                  taggedVlan: this.utils.convertNumberArrayToRangeStringArray(taggedVlan),
                  taggedVlanRaw: taggedVlan,
                  untaggedVlan: this.utils.convertNumberArrayToRangeStringArray(untaggedVlan),
                  untaggedVlanRaw: untaggedVlan,
                  acceptframetypeRaw: port.acceptableFrameTypes,
                  isTrunk: isTrunkData,
                });
              }
            });
          }

          // Converting the portMode array of portModeStatusTableData to string
          this.portModeStatusTableData.forEach(vlan => {
            vlan.trunkPort = this.utils.addSpace(vlan.trunkPortRaw);
            vlan.accessPort = this.utils.addSpace(vlan.accessPortRaw);
            vlan.hybridPort = this.utils.addSpace(vlan.hybridPortRaw);
            vlan.vaPort = this.utils.addSpace(vlan.vaPortRaw);
          });

          // Set not existed port colour to grey
          this.uriRequestData['portTable'].forEach((portInfo, portStatusIndex) => {
            const portEntryTableDataIndex = findIndex(this.portEntryTableData, (o: any) => {
              return o.key === portStatusIndex && !o.isTrunk;
            });
            if (portEntryTableDataIndex !== -1) {
              this.portEntryTableData[portEntryTableDataIndex]['exist'] = portInfo.exist;
            }
          });

          this.updateTable();
          observer.complete();
        },
        error => {
          observer.error(error);
        }
      );
    });
  }

  setupTableSetting(): void {
    this.vlanMgmtTableDataSource.sort = this.vlanMgmtTableSort;
    this.vlanMgmtTableDataSource.paginator = this.vlanMgmtTablePaginator;
    this.vlanMgmtTableDataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'member-port':
          return item.memberPort;
        default:
          return item[property];
      }
    };
    this.vlanMgmtTableDataSource.filterPredicate = (data: any, filter: string): boolean => {
      return (
        String(data.vid).toLowerCase().indexOf(filter) !== -1 ||
        String(data.name).toLowerCase().indexOf(filter) !== -1 ||
        String(data.memberPort).toLowerCase().indexOf(filter) !== -1 ||
        String(data.forbiddenPort).toLowerCase().indexOf(filter) !== -1
      );
    };

    this.portEntryTableDataSource.paginator = this.portEntryTablePaginator;
    this.portEntryTableDataSource.sort = this.portEntryTableSort;
    this.portEntryTableDataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'vlan-mode':
          return item.mode;
        case 'tagged-vlan':
          return item.taggedVlan;
        case 'untagged-vlan':
          return item.untaggedVlan;
        default:
          return item[property];
      }
    };
    this.portEntryTableDataSource.filterPredicate = (data: any, filter: string): boolean => {
      return (
        String(data.port).toLowerCase().indexOf(filter) !== -1 ||
        String(data.mode).toLowerCase().indexOf(filter) !== -1 ||
        String(data.pvid).toLowerCase().indexOf(filter) !== -1 ||
        String(data.gvrp).toLowerCase().indexOf(filter) !== -1 ||
        String(data.taggedVlan).toLowerCase().indexOf(filter) !== -1 ||
        String(data.untaggedVlan).toLowerCase().indexOf(filter) !== -1
      );
    };

    this.portModeStatusTableDataSource.paginator = this.portModeStatusTablePaginator;
    this.portModeStatusTableDataSource.sort = this.portModeStatusTableSort;
    this.portModeStatusTableDataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'access-port':
          return item.accessPort;
        case 'trunk-port':
          return item.trunkPort;
        case 'hybrid-port':
          return item.hybridPort;
        default:
          return item[property];
      }
    };
    this.portModeStatusTableDataSource.filterPredicate = (data: any, filter: string): boolean => {
      return (
        String(data.vid).toLowerCase().indexOf(filter) !== -1 ||
        String(data.name).toLowerCase().indexOf(filter) !== -1 ||
        String(data.status).toLowerCase().indexOf(filter) !== -1 ||
        String(data.accessPort).toLowerCase().indexOf(filter) !== -1 ||
        String(data.trunkPort).toLowerCase().indexOf(filter) !== -1 ||
        String(data.hybridPort).toLowerCase().indexOf(filter) !== -1
      );
    };

    this.egressPortStatusTableDataSource.paginator = this.egressPortStatusTablePaginator;
    this.egressPortStatusTableDataSource.sort = this.egressPortStatusTableSort;
    this.egressPortStatusTableDataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'tagged-port':
          return item.taggedPort;
        case 'untagged-port':
          return item.untaggedPort;
        case 'forbidden-port':
          return item.forbiddenPort;
        default:
          return item[property];
      }
    };
    this.egressPortStatusTableDataSource.filterPredicate = (data: any, filter: string): boolean => {
      return (
        String(data.vid).toLowerCase().indexOf(filter) !== -1 ||
        String(data.name).toLowerCase().indexOf(filter) !== -1 ||
        String(data.status).toLowerCase().indexOf(filter) !== -1 ||
        String(data.taggedPort).toLowerCase().indexOf(filter) !== -1 ||
        String(data.untaggedPort).toLowerCase().indexOf(filter) !== -1 ||
        String(data.forbiddenPort).toLowerCase().indexOf(filter) !== -1
      );
    };
  }

  updateTable(): void {
    this.vlanMgmtTableRows = this.vlanMgmtTableData.slice(0, this.vlanMgmtTablePanelPageSize);
    this.vlanMgmtTableDataLength = this.vlanMgmtTableData.length;
    this.vlanMgmtTableDataSource.data = this.vlanMgmtTableData;
    this.vlanMgmtSelection.clear();

    this.portEntryTableRows = this.portEntryTableData.slice(0, this.portEntryTablePanelPageSize);
    this.portEntryTableDataLength = this.portEntryTableData.length;
    this.portEntryTableDataSource.data = this.portEntryTableData;

    this.portModeStatusTableRows = this.portModeStatusTableData.slice(0, this.portModeStatusTablePanelPageSize);
    this.portModeStatusTableDataLength = this.portModeStatusTableData.length;
    this.portModeStatusTableDataSource.data = this.portModeStatusTableData;

    this.egressPortStatusTableRows = this.egressPortStatusTableData.slice(0, this.egressPortStatusTablePanelPageSize);
    this.egressPortStatusTableDataLength = this.egressPortStatusTableData.length;
    this.egressPortStatusTableDataSource.data = this.egressPortStatusTableData;
  }

  isAllVlanMgmtSelected(): boolean {
    const numSelected = this.vlanMgmtSelection.selected.length;
    const numRows = this.vlanMgmtTableDataSource.data.length;
    return numSelected === numRows;
  }

  vlanMgmtMasterToggle(): void {
    this.isAllVlanMgmtSelected()
      ? this.vlanMgmtSelection.clear()
      : this.vlanMgmtTableDataSource.data.forEach(row => this.vlanMgmtSelection.select(row));
  }

  submitVlanGlobalSetting(): void {
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    const request = [
      this.http.patch(
        environment.uriRequestURL + '/setting/data/stdvlan/gvrpEnable',
        this.vlanGlobalForm.value.vlanGvrp
      ),
    ];
    if (!this.notSupport.vlanUnaware) {
      request.push(
        this.http.patch(
          environment.uriRequestURL + '/setting/data/mxvlan/vlanUnawareEnable',
          this.vlanGlobalForm.value.vlanUnaware
        )
      );
    }

    forkJoin(request).subscribe(
      () => {
        this.refreshSettingTable().subscribe({
          complete: () => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          },
          error: error => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            this.errorService.handleError(error);
          },
        });
        this.snackBar.open(this.translate.instant('response_handler.res_global_success'), '', {
          duration: 3000,
        });
      },
      error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      }
    );
  }

  submitMgmtVlanSetting(): void {
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    const mgmtVlan = this.mgmtVlanForm.value.mgmtVlan;
    const mgmtPortData = this.mgmtVlanForm.value.mgmtPort;
    let request;
    if (!mgmtPortData) {
      request = this.http.patch(environment.uriRequestURL + '/setting/data/mxvlan/mgmtVlan', [mgmtVlan]);
    } else {
      const portKey = mgmtPortData.isTrunk ? this.utils.getIndexByAliasNumber(mgmtPortData.key) : mgmtPortData.key;
      const portConfigData = {
        pvid: mgmtVlan,
        vlanPortType: mgmtPortData.modeRaw,
      };
      const vlanTable = cloneDeep(this.uriRequestData.stdvlan.vlanTable);
      forEach(vlanTable, vlan => {
        if (vlan.vid === mgmtVlan) {
          vlan.egressPortsPbmp[portKey] = true;
          vlan.untaggedPortsPbmp[portKey] = true;
          return;
        }
      });
      request = concat(
        forkJoin([
          this.http.patch(environment.uriRequestURL + '/setting/data/stdvlan/vlanTable?setonly', vlanTable),
          this.http.patch(environment.uriRequestURL + '/setting/data/mxvlan/mgmtVlan?setonly', [mgmtVlan]),
        ]),
        this.http.post(
          environment.uriRequestURL + '/setting/agent/vlan/portConfigs?ifindices=' + portKey,
          portConfigData
        )
      );
    }

    request.subscribe(
      () => {},
      error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      },
      () => {
        this.refreshSettingTable().subscribe({
          complete: () => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          },
          error: error => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            this.errorService.handleError(error);
          },
        });
        this.snackBar.open(this.translate.instant('response_handler.res_port_success'), '', {
          duration: 3000,
        });
      }
    );
  }

  openSettingInfo(): void {
    this.dialog.open(VlanSettingInfoComponent);
  }

  createVlan(): void {
    mediumDialogConfig.data = {
      vlanList: this.vlanList,
      vlanAllPortMap: cloneDeep(this.vlanAllPortMap),
      allPortMapData: this.allPortMapData,
      notSupport: this.notSupport,
    };
    const dialogRef = this.dialog.open(VlanMgmtSettingDialogComponent, mediumDialogConfig);

    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        setTimeout(() => {
          this.refreshSettingTable().subscribe({
            complete: () => {
              this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            },
            error: error => {
              this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
              this.errorService.handleError(error);
            },
          });
        }, 2000);
      }
    });
  }

  editVlanMgmt(rowData): void {
    mediumDialogConfig.data = {
      row: rowData,
      vlanList: this.vlanList,
      vlanAllPortMap: cloneDeep(this.vlanAllPortMap),
      allPortMapData: this.allPortMapData,
      vlanMgmtTableData: this.vlanMgmtTableData,
      notSupport: this.notSupport,
    };
    const dialogRef = this.dialog.open(VlanMgmtSettingDialogComponent, mediumDialogConfig);

    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        this.refreshSettingTable().subscribe({
          complete: () => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          },
          error: error => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            this.errorService.handleError(error);
          },
        });
      }
    });
  }

  deleteVlanMgmt(row?: any): void {
    const vids = [];
    if (row) {
      vids.push(row.vid);
    } else {
      this.vlanMgmtSelection.selected.forEach(e => {
        vids.push(e.vid);
      });
    }
    smallDialogConfig.data = vids;
    const dialogRef = this.dialog.open(VlanMgmtDeleteDialogComponent, smallDialogConfig);

    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
        // setTimeout due to /status/dot1qInfo/vlanCurrentTable not update-to-date
        setTimeout(() => {
          this.refreshSettingTable().subscribe({
            complete: () => {
              this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            },
            error: error => {
              this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
              this.errorService.handleError(error);
            },
          });
        }, 500);
      }
    });
  }

  editPortEntry(rowData): void {
    mediumDialogConfig.data = {
      row: rowData,
      vlanList: this.vlanList,
      stdvlan: this.uriRequestData['stdvlan'],
      mxvlan: this.uriRequestData['mxvlan'],
      allPortMap: this.allPortMap,
      notSupport: this.notSupport,
    };
    const dialogRef = this.dialog.open(VlanPortEntrySettingDialogComponent, mediumDialogConfig);

    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        this.refreshSettingTable().subscribe({
          complete: () => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          },
          error: error => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            this.errorService.handleError(error);
          },
        });
      }
    });
  }

  updateVlanMgmtFilter(filterValue): void {
    this.vlanMgmtTableDataSource.filter = filterValue.trim().toLowerCase();
  }

  updatePortEntryFilter(filterValue): void {
    this.portEntryTableDataSource.filter = filterValue.trim().toLowerCase();
  }

  updatePortModeStatusFilter(filterValue): void {
    this.portModeStatusTableDataSource.filter = filterValue.trim().toLowerCase();
  }

  updateEgressPortStatusFilter(filterValue): void {
    this.egressPortStatusTableDataSource.filter = filterValue.trim().toLowerCase();
  }

  onVlanMgmtTablePageChange(event): void {
    this.vlanMgmtTableRows = this.vlanMgmtTableData.slice(
      this.vlanMgmtTablePanelPageSize * event.pageIndex,
      this.vlanMgmtTablePanelPageSize * event.pageIndex + this.vlanMgmtTablePanelPageSize
    );
  }

  onPortEntryTablePageChange(event): void {
    this.portEntryTableRows = this.portEntryTableData.slice(
      this.portEntryTablePanelPageSize * event.pageIndex,
      this.portEntryTablePanelPageSize * event.pageIndex + this.portEntryTablePanelPageSize
    );
  }

  onPortModeStatusTablePageChange(event): void {
    this.portModeStatusTableRows = this.portModeStatusTableData.slice(
      this.portModeStatusTablePanelPageSize * event.pageIndex,
      this.portModeStatusTablePanelPageSize * event.pageIndex + this.portModeStatusTablePanelPageSize
    );
  }

  onEgressPortStatusTablePageChange(event): void {
    this.egressPortStatusTableRows = this.egressPortStatusTableData.slice(
      this.egressPortStatusTablePanelPageSize * event.pageIndex,
      this.egressPortStatusTablePanelPageSize * event.pageIndex + this.egressPortStatusTablePanelPageSize
    );
  }

  generateDoc(docType: string, functionName: string): void {
    let tableData;
    switch (functionName) {
      case 'Port-Mode-VLAN-Mapping-Table':
        tableData = {
          tableTitle: {
            vid: this.translate.instant('general.common.vlan_id'),
            name: this.translate.instant('general.common.name'),
            status: this.translate.instant('general.common.status'),
            hybridPort: this.translate.instant('features.vlan.hybrid_port'),
            trunkPort: this.translate.instant('features.vlan.trunk_port'),
            accessPort: this.translate.instant('features.vlan.access_port'),
          },
          tableContent: this.portModeStatusTableData,
          pdfPageGroup: [
            ['vid', 'name', 'status', 'hybridPort'],
            ['vid', 'trunkPort', 'accessPort'],
          ],
          pdfColumnWidth: {
            vid: ColumnWidth.SM,
            name: ColumnWidth.MD,
            hybridPort: ColumnWidth.LG,
            trunkPort: ColumnWidth.LG,
            accessPort: ColumnWidth.LG,
          },
        };
        break;

      case 'Egress-Untagged-Tagged-Table':
        tableData = {
          tableTitle: {
            vid: this.translate.instant('general.common.vlan_id'),
            name: this.translate.instant('general.common.name'),
            status: this.translate.instant('general.common.status'),
            untaggedPort: this.translate.instant('features.vlan.untagged_port'),
            taggedPort: this.translate.instant('features.vlan.tagged_port'),
            forbiddenPort: this.translate.instant('features.vlan.forbidden_port'),
          },
          tableContent: this.egressPortStatusTableData,
          pdfPageGroup: [
            ['vid', 'name', 'status', 'untaggedPort'],
            ['vid', 'taggedPort', 'forbiddenPort'],
          ],
          pdfColumnWidth: {
            vid: ColumnWidth.SM,
            name: ColumnWidth.MD,
            untaggedPort: ColumnWidth.LG,
            taggedPort: ColumnWidth.LG,
            forbiddenPort: ColumnWidth.LG,
          },
        };
        if (this.notSupport.forbiddenPort) {
          tableData.pdfPageGroup[1] = without(tableData.pdfPageGroup[1], 'forbiddenPort');
          delete tableData.tableTitle['forbiddenPort'];
        }
        break;

      case 'VLAN-Table':
        tableData = {
          tableTitle: {
            vid: this.translate.instant('general.common.vlan_id'),
            name: this.translate.instant('general.common.name'),
            memberPort: this.translate.instant('general.common_port.member_port'),
            forbiddenPort: this.translate.instant('features.vlan.forbidden_port'),
          },
          tableContent: this.vlanMgmtTableData,
          pdfPageGroup: [['vid', 'name', 'memberPort', 'forbiddenPort']],
          pdfColumnWidth: {
            vid: ColumnWidth.SM,
            name: ColumnWidth.MD,
            memberPort: ColumnWidth.LG,
            forbiddenPort: ColumnWidth.LG,
          },
        };
        if (this.notSupport.forbiddenPort) {
          tableData.pdfPageGroup[1] = without(tableData.pdfPageGroup[1], 'forbiddenPort');
          delete tableData.tableTitle['forbiddenPort'];
        }
        break;
    }
    this.docService.generateDoc(docType, this.utils.getFilename(functionName), tableData);
  }

  refresh(): void {
    this.refreshSettingTable().subscribe({
      complete: () => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.snackBar.open(this.translate.instant('response_handler.res_complete_refresh'), '', {
          duration: 3000,
        });
      },
      error: error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      },
    });
  }

  checkVlanUnaware(): ValidatorFn {
    return (formGroup: FormGroup) => {
      const vlanUnawareEnable = formGroup.get('vlanUnaware').value;
      const gvrpEnable = formGroup.get('vlanGvrp').value;
      if (vlanUnawareEnable && gvrpEnable) {
        formGroup.get('vlanGvrp').setErrors({ vlanUnawareEnable: true });
      } else if (formGroup.get('vlanGvrp').hasError('vlanUnawareEnable')) {
        formGroup.get('vlanGvrp').setErrors(null);
      }
      return null;
    };
  }
}
