<div class="app-content-container">
  <h2 class="page-title">{{ 'features.time_sync.page_title' | translate }}</h2>
  <div class="page-function">
    <mat-tab-group (selectedTabChange)="changeTab($event.tab.textLabel)">
      <mat-tab label="{{ 'general.common.general' | translate }}">
        <mat-card>
          <p class="text-content warning-caption" *ngIf="mdsM2moduleNotInserted">
            {{ 'features.time_sync.mds_m2_insert_warning' | translate }}
          </p>
          <form [formGroup]="timeSyncGlobalForm">
            <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
              <mat-form-field>
                <mat-select
                  id="select-time-sync"
                  placeholder="{{ 'features.time_sync.page_title' | translate }}"
                  formControlName="timeSyncEnable"
                  required
                >
                  <mat-option id="option-time-sync-enable" [value]="true">
                    {{ 'general.common.enabled' | translate }}
                  </mat-option>
                  <mat-option id="option-time-sync-disable" [value]="false">
                    {{ 'general.common.disabled' | translate }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
              <mat-form-field class="form-field-lg">
                <mat-select
                  id="select-profile"
                  placeholder="{{ 'features.time_sync.profile' | translate }}"
                  formControlName="profile"
                  required
                >
                  <mat-option
                    id="option-profile-8021as"
                    *ngIf="!notSupport.profile8021as"
                    [value]="ProfileType.PROFILE_8021AS"
                  >
                    {{ 'features.time_sync.8021as_abbr' | translate }}
                  </mat-option>
                  <mat-option
                    id="option-profile-1588v2default"
                    *ngIf="!notSupport.profile1588v2Default"
                    [value]="ProfileType.PROFILE_1588V2_DEFAULT"
                  >
                    {{ 'features.time_sync.1588default_abbr' | translate }}
                  </mat-option>
                  <mat-option
                    id="option-profile-1588Iec61850"
                    *ngIf="!notSupport.profile1588Iec61850"
                    [value]="ProfileType.PROFILE_IEC61850"
                  >
                    {{ 'features.time_sync.iec61850_abbr' | translate }}
                  </mat-option>
                  <mat-option
                    id="option-profile-c37238"
                    *ngIf="!notSupport.profileC37238"
                    [value]="ProfileType.PROFILE_C37_238"
                  >
                    {{ 'features.time_sync.c37238_abbr' | translate }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </form>
          <!-- Stdot1as Profile -->
          <ng-container *ngIf="timeSyncGlobalForm.get('profile').value === ProfileType.PROFILE_8021AS">
            <form [formGroup]="stdot1asProfileForm">
              <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
                <mat-form-field>
                  <input
                    id="input-priority-1"
                    matInput
                    type="number"
                    min="0"
                    max="255"
                    placeholder="{{ 'features.time_sync.priority_number' | translate : { number: 1 } }}"
                    formControlName="priority1"
                    required
                  />
                  <mat-hint align="begin">
                    {{ 'validators.require_range_between' | translate : { rangeBegin: 0, rangeEnd: 255 } }}</mat-hint
                  >
                  <mat-error *ngIf="stdot1asProfileForm.get('priority1').errors?.required">
                    {{ 'validators.required' | translate }}
                  </mat-error>
                  <mat-error
                    *ngIf="
                      stdot1asProfileForm.get('priority1').errors?.min ||
                      stdot1asProfileForm.get('priority1').errors?.max
                    "
                  >
                    {{ 'validators.invalid_range' | translate : { rangeBegin: 0, rangeEnd: 255 } }}
                  </mat-error>
                  <mat-error *ngIf="stdot1asProfileForm.get('priority1').errors?.pattern">
                    {{ 'validators.invalid_positive_integer' | translate }}
                  </mat-error>
                </mat-form-field>
                <mat-form-field>
                  <input
                    id="input-priority-2"
                    matInput
                    type="number"
                    min="0"
                    max="255"
                    placeholder="{{ 'features.time_sync.priority_number' | translate : { number: 2 } }}"
                    formControlName="priority2"
                    required
                  />
                  <mat-hint align="begin">
                    {{ 'validators.require_range_between' | translate : { rangeBegin: 0, rangeEnd: 255 } }}</mat-hint
                  >
                  <mat-error *ngIf="stdot1asProfileForm.get('priority2').errors?.required">
                    {{ 'validators.required' | translate }}
                  </mat-error>
                  <mat-error
                    *ngIf="
                      stdot1asProfileForm.get('priority2').errors?.min ||
                      stdot1asProfileForm.get('priority2').errors?.max
                    "
                  >
                    {{ 'validators.invalid_range' | translate : { rangeBegin: 0, rangeEnd: 255 } }}
                  </mat-error>
                  <mat-error *ngIf="stdot1asProfileForm.get('priority2').errors?.pattern">
                    {{ 'validators.invalid_positive_integer' | translate }}
                  </mat-error>
                </mat-form-field>
              </div>
              <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
                <mat-form-field>
                  <input
                    id="input-accuracy-alert"
                    matInput
                    type="number"
                    min="50"
                    max="250000000"
                    placeholder="{{ 'features.time_sync.accuracy_alert' | translate }}"
                    formControlName="accuracyAlert"
                    required
                  />
                  <mat-hint align="begin">
                    {{ 'validators.require_range_between' | translate : { rangeBegin: 50, rangeEnd: 250000000 } }}
                  </mat-hint>
                  <mat-hint align="end">{{ 'general.unit.ns' | translate }}</mat-hint>
                  <mat-error *ngIf="stdot1asProfileForm.get('accuracyAlert').errors?.required">
                    {{ 'validators.required' | translate }}</mat-error
                  >
                  <mat-error
                    *ngIf="
                      stdot1asProfileForm.get('accuracyAlert').errors?.min ||
                      stdot1asProfileForm.get('accuracyAlert').errors?.max
                    "
                  >
                    {{ 'validators.invalid_range' | translate : { rangeBegin: 50, rangeEnd: 250000000 } }}
                  </mat-error>
                  <mat-error *ngIf="stdot1asProfileForm.get('accuracyAlert').errors?.pattern">
                    {{ 'validators.invalid_positive_integer' | translate }}
                  </mat-error>
                </mat-form-field>
              </div>
            </form>
          </ng-container>
          <!-- Mx 1588v2 Default Profile -->
          <ng-container *ngIf="timeSyncGlobalForm.get('profile').value === ProfileType.PROFILE_1588V2_DEFAULT">
            <form [formGroup]="mx1588DefaultProfileForm">
              <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
                <mat-form-field>
                  <mat-select
                    id="select-clock-type"
                    (selectionChange)="selectClockType(ProfileType.PROFILE_1588V2_DEFAULT, $event.value)"
                    placeholder="{{ 'features.time_sync.clock_type' | translate }}"
                    formControlName="clockType"
                    required
                  >
                    <mat-option id="option-clock-type-bc" [value]="ClockType.BC">
                      {{ 'features.time_sync.clock_type_bc' | translate }}</mat-option
                    >
                    <mat-option id="option-clock-type-tc" [value]="ClockType.TC">
                      {{ 'features.time_sync.clock_type_tc' | translate }}</mat-option
                    >
                  </mat-select>
                </mat-form-field>
                <mat-form-field>
                  <mat-select
                    id="select-delay-mechanism"
                    formControlName="delayMechanism"
                    required
                    placeholder="{{ 'features.time_sync.delay_mechanism' | translate }}"
                  >
                    <mat-option id="option-delay-mechanism-e2e" [value]="DelayMechanism.E2E">
                      {{ 'features.time_sync.e2e' | translate }}</mat-option
                    >
                    <mat-option id="option-delay-mechanism-p2p" [value]="DelayMechanism.P2P">
                      {{ 'features.time_sync.p2p' | translate }}</mat-option
                    >
                  </mat-select>
                </mat-form-field>
                <mat-form-field>
                  <mat-select
                    id="select-delay-mechanism"
                    required
                    placeholder="{{ 'features.time_sync.transport_mode' | translate }}"
                    formControlName="transportType"
                  >
                    <mat-option id="option-delay-mechanism-8023ehternet" [value]="TransportType.L2">
                      {{ 'features.time_sync.8023ehternet' | translate }}</mat-option
                    >
                    <mat-option id="option-delay-mechanism-udp-ipv4" [value]="TransportType.IPV4">
                      {{ 'features.time_sync.udp_ipv4' | translate }}</mat-option
                    >
                    <!-- <mat-option id="option-delay-mechanism-udp-ipv6" [value]="TransportType.IPV6">
                      {{ 'features.time_sync.udp_ipv6' | translate }}</mat-option> -->
                  </mat-select>
                </mat-form-field>
              </div>
              <div *ngIf="selectClockTypeBC.mx1588Default" fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
                <mat-form-field>
                  <input
                    id="input-priority-1"
                    matInput
                    type="number"
                    min="0"
                    max="255"
                    placeholder="{{ 'features.time_sync.priority_number' | translate : { number: 1 } }}"
                    formControlName="priority1"
                    required
                  />
                  <mat-hint align="begin">
                    {{ 'validators.require_range_between' | translate : { rangeBegin: 0, rangeEnd: 255 } }}</mat-hint
                  >
                  <mat-error *ngIf="mx1588DefaultProfileForm.get('priority1').errors?.required">
                    {{ 'validators.required' | translate }}
                  </mat-error>
                  <mat-error
                    *ngIf="
                      mx1588DefaultProfileForm.get('priority1').errors?.min ||
                      mx1588DefaultProfileForm.get('priority1').errors?.max
                    "
                  >
                    {{ 'validators.invalid_range' | translate : { rangeBegin: 0, rangeEnd: 255 } }}
                  </mat-error>
                  <mat-error *ngIf="mx1588DefaultProfileForm.get('priority1').errors?.pattern">
                    {{ 'validators.invalid_positive_integer' | translate }}
                  </mat-error>
                </mat-form-field>
                <mat-form-field>
                  <input
                    id="input-priority-2"
                    matInput
                    type="number"
                    min="0"
                    max="255"
                    placeholder="{{ 'features.time_sync.priority_number' | translate : { number: 2 } }}"
                    formControlName="priority2"
                    required
                  />
                  <mat-hint align="begin">
                    {{ 'validators.require_range_between' | translate : { rangeBegin: 0, rangeEnd: 255 } }}</mat-hint
                  >
                  <mat-error *ngIf="mx1588DefaultProfileForm.get('priority2').errors?.required">
                    {{ 'validators.required' | translate }}</mat-error
                  >
                  <mat-error
                    *ngIf="
                      mx1588DefaultProfileForm.get('priority2').errors?.min ||
                      mx1588DefaultProfileForm.get('priority2').errors?.max
                    "
                  >
                    {{ 'validators.invalid_range' | translate : { rangeBegin: 0, rangeEnd: 255 } }}
                  </mat-error>
                  <mat-error *ngIf="mx1588DefaultProfileForm.get('priority2').errors?.pattern">
                    {{ 'validators.invalid_positive_integer' | translate }}
                  </mat-error>
                </mat-form-field>
              </div>
              <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
                <mat-form-field>
                  <input
                    id="input-domain-number"
                    matInput
                    type="number"
                    min="0"
                    max="255"
                    required
                    placeholder="{{ 'features.time_sync.domain_number' | translate }}"
                    formControlName="domainNumber"
                  />
                  <mat-hint align="begin">
                    {{ 'validators.require_range_between' | translate : { rangeBegin: 0, rangeEnd: 255 } }}</mat-hint
                  >
                  <mat-error *ngIf="mx1588DefaultProfileForm.get('domainNumber').errors?.required">
                    {{ 'validators.required' | translate }}</mat-error
                  >
                  <mat-error
                    *ngIf="
                      mx1588DefaultProfileForm.get('domainNumber').errors?.min ||
                      mx1588DefaultProfileForm.get('domainNumber').errors?.max
                    "
                  >
                    {{ 'validators.invalid_range' | translate : { rangeBegin: 0, rangeEnd: 255 } }}
                  </mat-error>
                  <mat-error *ngIf="mx1588DefaultProfileForm.get('domainNumber').errors?.pattern">
                    {{ 'validators.invalid_positive_integer' | translate }}
                  </mat-error>
                </mat-form-field>
                <mat-form-field>
                  <mat-select
                    id="select-clock-mode"
                    placeholder="{{ 'features.time_sync.clock_mode' | translate }}"
                    formControlName="twoStepFlag"
                    required
                  >
                    <mat-option id="option-clock-mode-one-step" [value]="false">
                      {{ 'features.time_sync.one_step' | translate }}</mat-option
                    >
                    <mat-option id="option-clock-mode-two-step" [value]="true">
                      {{ 'features.time_sync.two_step' | translate }}</mat-option
                    >
                  </mat-select>
                </mat-form-field>
              </div>
              <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
                <mat-form-field>
                  <input
                    id="input-accuracy-alert"
                    matInput
                    type="number"
                    min="50"
                    max="250000000"
                    placeholder="{{ 'features.time_sync.accuracy_alert' | translate }}"
                    formControlName="accuracyAlert"
                    required
                  />
                  <mat-hint align="begin">
                    {{ 'validators.require_range_between' | translate : { rangeBegin: 50, rangeEnd: 250000000 } }}
                  </mat-hint>
                  <mat-hint align="end">{{ 'general.unit.ns' | translate }}</mat-hint>
                  <mat-error *ngIf="mx1588DefaultProfileForm.get('accuracyAlert').errors?.required">
                    {{ 'validators.required' | translate }}</mat-error
                  >
                  <mat-error
                    *ngIf="
                      mx1588DefaultProfileForm.get('accuracyAlert').errors?.min ||
                      mx1588DefaultProfileForm.get('accuracyAlert').errors?.max
                    "
                  >
                    {{ 'validators.invalid_range' | translate : { rangeBegin: 50, rangeEnd: 250000000 } }}
                  </mat-error>
                  <mat-error *ngIf="mx1588DefaultProfileForm.get('accuracyAlert').errors?.pattern">
                    {{ 'validators.invalid_positive_integer' | translate }}
                  </mat-error>
                </mat-form-field>
                <mat-form-field *ngIf="selectClockTypeBC.mx1588Default && !notSupport.maximumStepsRemoved">
                  <input
                    id="input-maximum-steps-removed"
                    matInput
                    type="number"
                    min="2"
                    max="255"
                    placeholder="{{ 'features.time_sync.max_steps_removed' | translate }}"
                    formControlName="maximumStepsRemoved"
                    required
                  />
                  <mat-hint align="begin">
                    {{ 'validators.require_range_between' | translate : { rangeBegin: 2, rangeEnd: 255 } }}</mat-hint
                  >
                  <mat-error *ngIf="mx1588DefaultProfileForm.get('maximumStepsRemoved').errors?.required">
                    {{ 'validators.required' | translate }}</mat-error
                  >
                  <mat-error
                    *ngIf="
                      mx1588DefaultProfileForm.get('maximumStepsRemoved').errors?.min ||
                      mx1588DefaultProfileForm.get('maximumStepsRemoved').errors?.max
                    "
                  >
                    {{ 'validators.invalid_range' | translate : { rangeBegin: 2, rangeEnd: 255 } }}
                  </mat-error>
                  <mat-error *ngIf="mx1588DefaultProfileForm.get('maximumStepsRemoved').errors?.pattern">
                    {{ 'validators.invalid_positive_integer' | translate }}
                  </mat-error>
                </mat-form-field>
              </div>
              <div
                *ngIf="isAdvancedMode && !selectClockTypeBC.mx1588Default"
                fxLayout="row"
                fxLayout.xs="column"
                fxLayoutGap="20px"
              >
                <mat-form-field>
                  <mat-select
                    id="select-bmca"
                    placeholder="{{ 'features.time_sync.bmca' | translate }}"
                    formControlName="bmca"
                    required
                  >
                    <mat-option id="option-bmca-enable" [value]="true">
                      {{ 'general.common.enabled' | translate }}
                    </mat-option>
                    <mat-option id="option-bmca-disable" [value]="false">
                      {{ 'general.common.disabled' | translate }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
                <mat-icon
                  class="form-help-tip"
                  fxHide.xs="true"
                  matTooltip="{{ 'features.time_sync.bmca_hint' | translate }}"
                  matTooltipPosition="right"
                  >info</mat-icon
                >
              </div>
            </form>
          </ng-container>
          <!-- IEC61850-9-3 2016 Profile -->
          <ng-container *ngIf="timeSyncGlobalForm.get('profile').value === ProfileType.PROFILE_IEC61850">
            <form [formGroup]="iec61850ProfileForm">
              <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
                <mat-form-field>
                  <mat-select
                    id="select-clock-type"
                    (selectionChange)="selectClockType(ProfileType.PROFILE_IEC61850, $event.value)"
                    placeholder="{{ 'features.time_sync.clock_type' | translate }}"
                    formControlName="clockType"
                    required
                  >
                    <mat-option id="option-clock-type-bc" [value]="ClockType.BC">
                      {{ 'features.time_sync.clock_type_bc' | translate }}</mat-option
                    >
                    <mat-option id="option-clock-type-tc" [value]="ClockType.TC">
                      {{ 'features.time_sync.clock_type_tc' | translate }}</mat-option
                    >
                  </mat-select>
                </mat-form-field>
                <mat-form-field>
                  <mat-select
                    id="select-delay-mechanism"
                    formControlName="delayMechanism"
                    required
                    placeholder="{{ 'features.time_sync.delay_mechanism' | translate }}"
                  >
                    <mat-option id="option-delay-mechanism-e2e" [value]="DelayMechanism.E2E">
                      {{ 'features.time_sync.e2e' | translate }}</mat-option
                    >
                    <mat-option id="option-delay-mechanism-p2p" [value]="DelayMechanism.P2P">
                      {{ 'features.time_sync.p2p' | translate }}</mat-option
                    >
                  </mat-select>
                </mat-form-field>
                <mat-form-field>
                  <mat-select
                    id="select-delay-mechanism"
                    required
                    placeholder="{{ 'features.time_sync.transport_mode' | translate }}"
                    formControlName="transportType"
                  >
                    <mat-option id="option-delay-mechanism-8023ehternet" [value]="TransportType.L2">
                      {{ 'features.time_sync.8023ehternet' | translate }}</mat-option
                    >
                    <mat-option id="option-delay-mechanism-udp-ipv4" [value]="TransportType.IPV4">
                      {{ 'features.time_sync.udp_ipv4' | translate }}</mat-option
                    >
                    <!-- <mat-option id="option-delay-mechanism-udp-ipv6" [value]="TransportType.IPV6">
                      {{ 'features.time_sync.udp_ipv6' | translate }}</mat-option> -->
                  </mat-select>
                </mat-form-field>
              </div>
              <div *ngIf="selectClockTypeBC.iec61850" fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
                <mat-form-field>
                  <input
                    id="input-priority-1"
                    matInput
                    type="number"
                    min="0"
                    max="255"
                    placeholder="{{ 'features.time_sync.priority_number' | translate : { number: 1 } }}"
                    formControlName="priority1"
                    required
                  />
                  <mat-hint align="begin">
                    {{ 'validators.require_range_between' | translate : { rangeBegin: 0, rangeEnd: 255 } }}</mat-hint
                  >
                  <mat-error *ngIf="iec61850ProfileForm.get('priority1').errors?.required">
                    {{ 'validators.required' | translate }}
                  </mat-error>
                  <mat-error
                    *ngIf="
                      iec61850ProfileForm.get('priority1').errors?.min ||
                      iec61850ProfileForm.get('priority1').errors?.max
                    "
                  >
                    {{ 'validators.invalid_range' | translate : { rangeBegin: 0, rangeEnd: 255 } }}
                  </mat-error>
                  <mat-error *ngIf="iec61850ProfileForm.get('priority1').errors?.pattern">
                    {{ 'validators.invalid_positive_integer' | translate }}
                  </mat-error>
                </mat-form-field>
                <mat-form-field>
                  <input
                    id="input-priority-2"
                    matInput
                    type="number"
                    min="0"
                    max="255"
                    placeholder="{{ 'features.time_sync.priority_number' | translate : { number: 2 } }}"
                    formControlName="priority2"
                    required
                  />
                  <mat-hint align="begin">
                    {{ 'validators.require_range_between' | translate : { rangeBegin: 0, rangeEnd: 255 } }}</mat-hint
                  >
                  <mat-error *ngIf="iec61850ProfileForm.get('priority2').errors?.required">
                    {{ 'validators.required' | translate }}</mat-error
                  >
                  <mat-error
                    *ngIf="
                      iec61850ProfileForm.get('priority2').errors?.min ||
                      iec61850ProfileForm.get('priority2').errors?.max
                    "
                  >
                    {{ 'validators.invalid_range' | translate : { rangeBegin: 0, rangeEnd: 255 } }}
                  </mat-error>
                  <mat-error *ngIf="iec61850ProfileForm.get('priority2').errors?.pattern">
                    {{ 'validators.invalid_positive_integer' | translate }}
                  </mat-error>
                </mat-form-field>
              </div>
              <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
                <mat-form-field>
                  <input
                    id="input-domain-number"
                    matInput
                    type="number"
                    min="0"
                    max="255"
                    required
                    placeholder="{{ 'features.time_sync.domain_number' | translate }}"
                    formControlName="domainNumber"
                  />
                  <mat-hint align="begin">
                    {{ 'validators.require_range_between' | translate : { rangeBegin: 0, rangeEnd: 255 } }}</mat-hint
                  >
                  <mat-error *ngIf="iec61850ProfileForm.get('domainNumber').errors?.required">
                    {{ 'validators.required' | translate }}</mat-error
                  >
                  <mat-error
                    *ngIf="
                      iec61850ProfileForm.get('domainNumber').errors?.min ||
                      iec61850ProfileForm.get('domainNumber').errors?.max
                    "
                  >
                    {{ 'validators.invalid_range' | translate : { rangeBegin: 0, rangeEnd: 255 } }}
                  </mat-error>
                  <mat-error *ngIf="iec61850ProfileForm.get('domainNumber').errors?.pattern">
                    {{ 'validators.invalid_positive_integer' | translate }}
                  </mat-error>
                </mat-form-field>
                <mat-form-field>
                  <mat-select
                    id="select-clock-mode"
                    placeholder="{{ 'features.time_sync.clock_mode' | translate }}"
                    formControlName="twoStepFlag"
                    required
                  >
                    <mat-option id="option-clock-mode-one-step" [value]="false">
                      {{ 'features.time_sync.one_step' | translate }}</mat-option
                    >
                    <mat-option id="option-clock-mode-two-step" [value]="true">
                      {{ 'features.time_sync.two_step' | translate }}</mat-option
                    >
                  </mat-select>
                </mat-form-field>
              </div>
              <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
                <mat-form-field>
                  <input
                    id="input-accuracy-alert"
                    matInput
                    type="number"
                    min="50"
                    max="250000000"
                    placeholder="{{ 'features.time_sync.accuracy_alert' | translate }}"
                    formControlName="accuracyAlert"
                    required
                  />
                  <mat-hint align="begin">
                    {{ 'validators.require_range_between' | translate : { rangeBegin: 50, rangeEnd: 250000000 } }}
                  </mat-hint>
                  <mat-hint align="end">{{ 'general.unit.ns' | translate }}</mat-hint>
                  <mat-error *ngIf="iec61850ProfileForm.get('accuracyAlert').errors?.required">
                    {{ 'validators.required' | translate }}</mat-error
                  >
                  <mat-error
                    *ngIf="
                      iec61850ProfileForm.get('accuracyAlert').errors?.min ||
                      iec61850ProfileForm.get('accuracyAlert').errors?.max
                    "
                  >
                    {{ 'validators.invalid_range' | translate : { rangeBegin: 50, rangeEnd: 250000000 } }}
                  </mat-error>
                  <mat-error *ngIf="iec61850ProfileForm.get('accuracyAlert').errors?.pattern">
                    {{ 'validators.invalid_positive_integer' | translate }}
                  </mat-error>
                </mat-form-field>
                <mat-form-field *ngIf="selectClockTypeBC.iec61850">
                  <input
                    id="input-maximum-steps-removed"
                    matInput
                    type="number"
                    min="2"
                    max="255"
                    placeholder="{{ 'features.time_sync.max_steps_removed' | translate }}"
                    formControlName="maximumStepsRemoved"
                    required
                  />
                  <mat-hint align="begin">
                    {{ 'validators.require_range_between' | translate : { rangeBegin: 2, rangeEnd: 255 } }}</mat-hint
                  >
                  <mat-error *ngIf="iec61850ProfileForm.get('maximumStepsRemoved').errors?.required">
                    {{ 'validators.required' | translate }}</mat-error
                  >
                  <mat-error
                    *ngIf="
                      iec61850ProfileForm.get('maximumStepsRemoved').errors?.min ||
                      iec61850ProfileForm.get('maximumStepsRemoved').errors?.max
                    "
                  >
                    {{ 'validators.invalid_range' | translate : { rangeBegin: 2, rangeEnd: 255 } }}
                  </mat-error>
                  <mat-error *ngIf="iec61850ProfileForm.get('maximumStepsRemoved').errors?.pattern">
                    {{ 'validators.invalid_positive_integer' | translate }}
                  </mat-error>
                </mat-form-field>
              </div>
              <div
                *ngIf="isAdvancedMode && !selectClockTypeBC.iec61850"
                fxLayout="row"
                fxLayout.xs="column"
                fxLayoutGap="20px"
              >
                <mat-form-field>
                  <mat-select
                    id="select-bmca"
                    placeholder="{{ 'features.time_sync.bmca' | translate }}"
                    formControlName="bmca"
                    required
                  >
                    <mat-option id="option-bmca-enable" [value]="true">
                      {{ 'general.common.enabled' | translate }}
                    </mat-option>
                    <mat-option id="option-bmca-disable" [value]="false">
                      {{ 'general.common.disabled' | translate }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
                <mat-icon
                  class="form-help-tip"
                  fxHide.xs="true"
                  matTooltip="{{ 'features.time_sync.bmca_hint' | translate }}"
                  matTooltipPosition="right"
                  >info</mat-icon
                >
              </div>
            </form>
          </ng-container>
          <!-- C37.238 2017 Profile -->
          <ng-container *ngIf="timeSyncGlobalForm.get('profile').value === ProfileType.PROFILE_C37_238">
            <form [formGroup]="c37238ProfileForm">
              <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
                <mat-form-field>
                  <mat-select
                    id="select-clock-type"
                    (selectionChange)="selectClockType(ProfileType.PROFILE_C37_238, $event.value)"
                    placeholder="{{ 'features.time_sync.clock_type' | translate }}"
                    formControlName="clockType"
                    required
                  >
                    <mat-option id="option-clock-type-bc" [value]="ClockType.BC">
                      {{ 'features.time_sync.clock_type_bc' | translate }}</mat-option
                    >
                    <mat-option id="option-clock-type-tc" [value]="ClockType.TC">
                      {{ 'features.time_sync.clock_type_tc' | translate }}</mat-option
                    >
                  </mat-select>
                </mat-form-field>
                <mat-form-field>
                  <mat-select
                    id="select-delay-mechanism"
                    formControlName="delayMechanism"
                    required
                    placeholder="{{ 'features.time_sync.delay_mechanism' | translate }}"
                  >
                    <mat-option id="option-delay-mechanism-e2e" [value]="DelayMechanism.E2E">
                      {{ 'features.time_sync.e2e' | translate }}</mat-option
                    >
                    <mat-option id="option-delay-mechanism-p2p" [value]="DelayMechanism.P2P">
                      {{ 'features.time_sync.p2p' | translate }}</mat-option
                    >
                  </mat-select>
                </mat-form-field>
                <mat-form-field>
                  <mat-select
                    id="select-delay-mechanism"
                    required
                    placeholder="{{ 'features.time_sync.transport_mode' | translate }}"
                    formControlName="transportType"
                  >
                    <mat-option id="option-delay-mechanism-8023ehternet" [value]="TransportType.L2">
                      {{ 'features.time_sync.8023ehternet' | translate }}</mat-option
                    >
                    <mat-option id="option-delay-mechanism-udp-ipv4" [value]="TransportType.IPV4">
                      {{ 'features.time_sync.udp_ipv4' | translate }}</mat-option
                    >
                    <!-- <mat-option id="option-delay-mechanism-udp-ipv6" [value]="TransportType.IPV6">
                      {{ 'features.time_sync.udp_ipv6' | translate }}</mat-option> -->
                  </mat-select>
                </mat-form-field>
              </div>
              <div *ngIf="selectClockTypeBC.c37238" fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
                <mat-form-field>
                  <input
                    id="input-priority-1"
                    matInput
                    type="number"
                    min="0"
                    max="255"
                    placeholder="{{ 'features.time_sync.priority_number' | translate : { number: 1 } }}"
                    formControlName="priority1"
                    required
                  />
                  <mat-hint align="begin">
                    {{ 'validators.require_range_between' | translate : { rangeBegin: 0, rangeEnd: 255 } }}</mat-hint
                  >
                  <mat-error *ngIf="c37238ProfileForm.get('priority1').errors?.required">
                    {{ 'validators.required' | translate }}
                  </mat-error>
                  <mat-error
                    *ngIf="
                      c37238ProfileForm.get('priority1').errors?.min || c37238ProfileForm.get('priority1').errors?.max
                    "
                  >
                    {{ 'validators.invalid_range' | translate : { rangeBegin: 0, rangeEnd: 255 } }}
                  </mat-error>
                  <mat-error *ngIf="c37238ProfileForm.get('priority1').errors?.pattern">
                    {{ 'validators.invalid_positive_integer' | translate }}
                  </mat-error>
                </mat-form-field>
                <mat-form-field>
                  <input
                    id="input-priority-2"
                    matInput
                    type="number"
                    min="0"
                    max="255"
                    placeholder="{{ 'features.time_sync.priority_number' | translate : { number: 2 } }}"
                    formControlName="priority2"
                    required
                  />
                  <mat-hint align="begin">
                    {{ 'validators.require_range_between' | translate : { rangeBegin: 0, rangeEnd: 255 } }}</mat-hint
                  >
                  <mat-error *ngIf="c37238ProfileForm.get('priority2').errors?.required">
                    {{ 'validators.required' | translate }}</mat-error
                  >
                  <mat-error
                    *ngIf="
                      c37238ProfileForm.get('priority2').errors?.min || c37238ProfileForm.get('priority2').errors?.max
                    "
                  >
                    {{ 'validators.invalid_range' | translate : { rangeBegin: 0, rangeEnd: 255 } }}
                  </mat-error>
                  <mat-error *ngIf="c37238ProfileForm.get('priority2').errors?.pattern">
                    {{ 'validators.invalid_positive_integer' | translate }}
                  </mat-error>
                </mat-form-field>
              </div>
              <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
                <mat-form-field>
                  <input
                    id="input-domain-number"
                    matInput
                    type="number"
                    min="0"
                    max="255"
                    required
                    placeholder="{{ 'features.time_sync.domain_number' | translate }}"
                    formControlName="domainNumber"
                  />
                  <mat-hint align="begin">
                    {{
                      'validators.require_range_single_between'
                        | translate : { singleNum: 254, rangeBegin: 0, rangeEnd: 127 }
                    }}</mat-hint
                  >
                  <mat-error *ngIf="c37238ProfileForm.get('domainNumber').errors?.required">
                    {{ 'validators.required' | translate }}</mat-error
                  >
                  <mat-error *ngIf="c37238ProfileForm.get('domainNumber').errors?.pattern">
                    {{ 'validators.invalid_positive_integer' | translate }}
                  </mat-error>
                  <mat-error *ngIf="c37238ProfileForm.get('domainNumber').errors?.wrongRange">
                    {{
                      'validators.invalid_single_range' | translate : { singleNum: 254, rangeBegin: 0, rangeEnd: 127 }
                    }}
                  </mat-error>
                </mat-form-field>
                <mat-form-field>
                  <mat-select
                    id="select-clock-mode"
                    placeholder="{{ 'features.time_sync.clock_mode' | translate }}"
                    formControlName="twoStepFlag"
                    required
                  >
                    <mat-option id="option-clock-mode-one-step" [value]="false">
                      {{ 'features.time_sync.one_step' | translate }}</mat-option
                    >
                    <mat-option id="option-clock-mode-two-step" [value]="true">
                      {{ 'features.time_sync.two_step' | translate }}</mat-option
                    >
                  </mat-select>
                </mat-form-field>
              </div>
              <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
                <mat-form-field>
                  <input
                    id="input-accuracy-alert"
                    matInput
                    type="number"
                    min="50"
                    max="250000000"
                    placeholder="{{ 'features.time_sync.accuracy_alert' | translate }}"
                    formControlName="accuracyAlert"
                    required
                  />
                  <mat-hint align="begin">
                    {{ 'validators.require_range_between' | translate : { rangeBegin: 50, rangeEnd: 250000000 } }}
                  </mat-hint>
                  <mat-hint align="end">{{ 'general.unit.ns' | translate }}</mat-hint>
                  <mat-error *ngIf="c37238ProfileForm.get('accuracyAlert').errors?.required">
                    {{ 'validators.required' | translate }}</mat-error
                  >
                  <mat-error
                    *ngIf="
                      c37238ProfileForm.get('accuracyAlert').errors?.min ||
                      c37238ProfileForm.get('accuracyAlert').errors?.max
                    "
                  >
                    {{ 'validators.invalid_range' | translate : { rangeBegin: 50, rangeEnd: 250000000 } }}
                  </mat-error>
                  <mat-error *ngIf="c37238ProfileForm.get('accuracyAlert').errors?.pattern">
                    {{ 'validators.invalid_positive_integer' | translate }}
                  </mat-error>
                </mat-form-field>
                <mat-form-field *ngIf="selectClockTypeBC.c37238">
                  <input
                    id="input-maximum-steps-removed"
                    matInput
                    type="number"
                    min="2"
                    max="255"
                    placeholder="{{ 'features.time_sync.max_steps_removed' | translate }}"
                    formControlName="maximumStepsRemoved"
                    required
                  />
                  <mat-hint align="begin">
                    {{ 'validators.require_range_between' | translate : { rangeBegin: 2, rangeEnd: 255 } }}</mat-hint
                  >
                  <mat-error *ngIf="c37238ProfileForm.get('maximumStepsRemoved').errors?.required">
                    {{ 'validators.required' | translate }}</mat-error
                  >
                  <mat-error
                    *ngIf="
                      c37238ProfileForm.get('maximumStepsRemoved').errors?.min ||
                      c37238ProfileForm.get('maximumStepsRemoved').errors?.max
                    "
                  >
                    {{ 'validators.invalid_range' | translate : { rangeBegin: 2, rangeEnd: 255 } }}
                  </mat-error>
                  <mat-error *ngIf="c37238ProfileForm.get('maximumStepsRemoved').errors?.pattern">
                    {{ 'validators.invalid_positive_integer' | translate }}
                  </mat-error>
                </mat-form-field>
              </div>
              <div *ngIf="selectClockTypeBC.c37238" fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
                <mat-form-field>
                  <input
                    id="input-grandmaster-id"
                    matInput
                    type="number"
                    min="0"
                    max="65535"
                    placeholder="{{ 'features.time_sync.grandmaster_id' | translate }}"
                    formControlName="grandmasterId"
                    required
                  />
                  <mat-hint align="begin">
                    {{ 'validators.require_range_between' | translate : { rangeBegin: 0, rangeEnd: 65535 } }}
                  </mat-hint>
                  <mat-error *ngIf="c37238ProfileForm.get('grandmasterId').errors?.required">
                    {{ 'validators.required' | translate }}</mat-error
                  >
                  <mat-error
                    *ngIf="
                      c37238ProfileForm.get('grandmasterId').errors?.min ||
                      c37238ProfileForm.get('grandmasterId').errors?.max
                    "
                  >
                    {{ 'validators.invalid_range' | translate : { rangeBegin: 0, rangeEnd: 65535 } }}
                  </mat-error>
                  <mat-error *ngIf="c37238ProfileForm.get('grandmasterId').errors?.pattern">
                    {{ 'validators.invalid_positive_integer' | translate }}
                  </mat-error>
                </mat-form-field>
              </div>
              <div
                *ngIf="isAdvancedMode && !selectClockTypeBC.c37238"
                fxLayout="row"
                fxLayout.xs="column"
                fxLayoutGap="20px"
              >
                <mat-form-field>
                  <mat-select
                    id="select-bmca"
                    placeholder="{{ 'features.time_sync.bmca' | translate }}"
                    formControlName="bmca"
                    required
                  >
                    <mat-option id="option-bmca-enable" [value]="true">
                      {{ 'general.common.enabled' | translate }}
                    </mat-option>
                    <mat-option id="option-bmca-disable" [value]="false">
                      {{ 'general.common.disabled' | translate }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
                <mat-icon
                  class="form-help-tip"
                  fxHide.xs="true"
                  matTooltip="{{ 'features.time_sync.bmca_hint' | translate }}"
                  matTooltipPosition="right"
                  >info</mat-icon
                >
              </div>
            </form>
          </ng-container>
          <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="20px">
            <button
              id="button-general-apply"
              [ngClass]="{ 'not-editable': noPermission }"
              [disabled]="noPermission"
              (click)="updateGlobalSetting()"
              mat-raised-button
              color="primary"
              type="button"
            >
              {{ 'general.button.apply' | translate }}
            </button>
          </div>
        </mat-card>
      </mat-tab>
      <mat-tab label="{{ 'general.common_port.port_settings' | translate }}">
        <mat-card *ngIf="hasProfile(ProfileType.PROFILE_8021AS)">
          <mat-card-header fxLayout="column">
            <mat-card-title fxLayout="row" fxLayoutAlign="space-between center">
              <div ngClass="card-title">{{ 'features.time_sync.8021as' | translate }}</div>
            </mat-card-title>
          </mat-card-header>
          <div fxLayout="row" fxLayoutAlign="space-between center">
            <div class="table-icon-tool-bar-div" fxFlex="70" fxFlex.lt-lg="50"></div>
            <div class="filter-input-block" fxFlex="30" fxFlex.lt-lg="50">
              <mat-form-field class="form-field-search">
                <mat-icon class="icon-search" matPrefix>search</mat-icon>
                <input
                  (keyup)="updateTableFilter(ProfileType.PROFILE_8021AS, 'status', $event.target.value)"
                  matInput
                  placeholder="{{ 'general.table_function.filter_desc' | translate }}"
                />
              </mat-form-field>
            </div>
          </div>
          <div id="dataContent">
            <div fxShow.xs="true" fxHide="true">
              <mat-expansion-panel class="expansion-panel-wrapper" *ngFor="let row of port8021asSettingTableRows">
                <mat-expansion-panel-header>
                  <mat-panel-title [ngClass]="utils.getPortClass(row)"
                    >{{ row.port }} / {{ row.enable }}</mat-panel-title
                  >
                </mat-expansion-panel-header>
                <p>{{ 'general.common_port.port' | translate }} : {{ row.port }}</p>
                <p>{{ 'features.time_sync.page_title_abbr' | translate }} : {{ row.enable }}</p>
                <p>{{ 'features.time_sync.announce_interval' | translate }} : {{ row.announceInterval }}</p>
                <p>
                  {{ 'features.time_sync.announce_receipt_timeout' | translate }} : {{ row.announceReceiptTimeout }}
                </p>
                <p>{{ 'features.time_sync.sync_interval' | translate }} : {{ row.syncInterval }}</p>
                <p>{{ 'features.time_sync.sync_receipt_timeout' | translate }} : {{ row.syncReceiptTimeout }}</p>
                <p>{{ 'features.time_sync.pdelay_req_interval' | translate }} : {{ row.pdelayReqInterval }}</p>
                <p>
                  {{ 'features.time_sync.neighbor_prop_delay_thresh' | translate }} ({{
                    'general.unit.ns' | translate
                  }}) :
                  {{ row.neighborPropDelayThresh }}
                </p>
                <mat-action-row>
                  <button
                    [ngClass]="{ 'not-editable': noPermission }"
                    [disabled]="noPermission"
                    (click)="editPortTable(row)"
                    color="primary"
                    mat-raised-button
                  >
                    {{ 'general.button.edit' | translate }}
                  </button>
                </mat-action-row>
              </mat-expansion-panel>
              <mat-paginator
                [length]="port8021asSettingTableDataLength"
                [pageSize]="port8021asSettingTablePanelPageSize"
                [hidePageSize]="true"
                (page)="onPanelPageChange(ProfileType.PROFILE_8021AS, 'setting', $event)"
              >
              </mat-paginator>
            </div>
            <div fxShow="true" fxHide.xs="true">
              <div class="app-table-wrapper">
                <table
                  #port8021asSettingTableSort="matSort"
                  [dataSource]="port8021asSettingTableDataSource"
                  mat-table
                  matSort
                >
                  <ng-container matColumnDef="edit">
                    <th *matHeaderCellDef mat-header-cell></th>
                    <td *matCellDef="let row" mat-cell>
                      <mat-icon
                        class="table-icon-action"
                        *ngIf="!noPermission && !row.isMemberPort"
                        (click)="editPortTable(row)"
                        matTooltip="{{ 'general.tooltip.edit' | translate }}"
                        attr.id="button-edit-port-{{ row.port }}"
                      >
                        edit
                      </mat-icon>
                      <mat-icon
                        class="table-not-editable"
                        *ngIf="noPermission || row.isMemberPort"
                        [matTooltipDisabled]="noPermission"
                        matTooltip="{{ 'general.tooltip.disable_la_member_hint' | translate }}"
                        matTooltipPosition="right"
                        >edit</mat-icon
                      >
                    </td>
                  </ng-container>
                  <ng-container matColumnDef="port">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'general.common_port.port' | translate }}
                    </th>
                    <td *matCellDef="let row" [ngClass]="utils.getPortClass(row)" mat-cell>{{ row.port }}</td>
                  </ng-container>
                  <ng-container matColumnDef="time-sync-enable">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.page_title' | translate }}
                    </th>
                    <td *matCellDef="let row" [ngClass]="utils.getTextClass(row.enable)" mat-cell>{{ row.enable }}</td>
                  </ng-container>
                  <ng-container matColumnDef="announce-interval">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.announce_interval' | translate }}
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.announceInterval }}</td>
                  </ng-container>
                  <ng-container matColumnDef="announce-receipt-timeout">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.announce_receipt_timeout' | translate }}
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.announceReceiptTimeout }}</td>
                  </ng-container>
                  <ng-container matColumnDef="sync-interval">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.sync_interval' | translate }}
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.syncInterval }}</td>
                  </ng-container>
                  <ng-container matColumnDef="sync-receipt-timeout">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.sync_receipt_timeout' | translate }}
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.syncReceiptTimeout }}</td>
                  </ng-container>
                  <ng-container matColumnDef="pdelay-req-interval">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.pdelay_req_interval' | translate }}
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.pdelayReqInterval }}</td>
                  </ng-container>
                  <ng-container matColumnDef="neighbor-prop-delay-thresh">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.neighbor_prop_delay_thresh' | translate }}
                      ({{ 'general.unit.ns' | translate }})
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.neighborPropDelayThresh }}</td>
                  </ng-container>
                  <ng-container matColumnDef="dummy">
                    <th *matHeaderCellDef mat-header-cell></th>
                    <td *matCellDef="let row" mat-cell></td>
                  </ng-container>
                  <tr *matHeaderRowDef="port8021asSettingTableDisplayedColumns; sticky: true" mat-header-row></tr>
                  <tr *matRowDef="let row; columns: port8021asSettingTableDisplayedColumns" mat-row></tr>
                </table>
              </div>
              <mat-paginator
                #port8021asSettingTablePaginator
                [ngClass]="{ disableChangePage: port8021asSettingTableDataLength <= 50 }"
                [hidePageSize]="true"
                [showFirstLastButtons]="false"
              ></mat-paginator>
            </div>
          </div>
        </mat-card>
        <mat-card *ngIf="hasProfile(ProfileType.PROFILE_1588V2_DEFAULT)">
          <mat-card-header fxLayout="column">
            <mat-card-title fxLayout="row" fxLayoutAlign="space-between center">
              <div ngClass="card-title">{{ 'features.time_sync.1588default' | translate }}</div>
            </mat-card-title>
          </mat-card-header>
          <div fxLayout="row" fxLayoutAlign="space-between center">
            <div class="table-icon-tool-bar-div" fxFlex="70" fxFlex.lt-lg="50"></div>
            <div class="filter-input-block" fxFlex="30" fxFlex.lt-lg="50">
              <mat-form-field class="form-field-search">
                <mat-icon class="icon-search" matPrefix>search</mat-icon>
                <input
                  (keyup)="updateTableFilter(ProfileType.PROFILE_1588V2_DEFAULT, 'setting', $event.target.value)"
                  matInput
                  placeholder="{{ 'general.table_function.filter_desc' | translate }}"
                />
              </mat-form-field>
            </div>
          </div>
          <div id="dataContent">
            <div fxShow.xs="true" fxHide="true">
              <mat-expansion-panel class="expansion-panel-wrapper" *ngFor="let row of port1588defaultSettingTableRows">
                <mat-expansion-panel-header>
                  <mat-panel-title [ngClass]="utils.getPortClass(row)"
                    >{{ row.port }} / {{ row.enable }}</mat-panel-title
                  >
                </mat-expansion-panel-header>
                <p>{{ 'general.common_port.port' | translate }} : {{ row.port }}</p>
                <p>{{ 'features.time_sync.page_title_abbr' | translate }} : {{ row.enable }}</p>
                <p *ngIf="settingMx1588Parms.isClockTypeBC">
                  {{ 'features.time_sync.announce_interval' | translate }} : {{ row.announceInterval }}
                </p>
                <p *ngIf="settingMx1588Parms.isClockTypeBC">
                  {{ 'features.time_sync.announce_receipt_timeout' | translate }} : {{ row.announceReceiptTimeout }}
                  {{ 'general.unit.times' | translate }}
                </p>
                <p *ngIf="settingMx1588Parms.isClockTypeBC">
                  {{ 'features.time_sync.sync_interval' | translate }} : {{ row.syncInterval }}
                </p>
                <p *ngIf="settingMx1588Parms.isClockTypeBC && settingMx1588Parms.isDelayMechanismE2E">
                  {{ 'features.time_sync.delay_req_interval' | translate }} : {{ row.delayReqInterval }}
                </p>
                <p *ngIf="settingMx1588Parms.isDelayMechanismP2P">
                  {{ 'features.time_sync.pdelay_req_interval' | translate }} : {{ row.pdelayReqInterval }}
                </p>
                <mat-action-row>
                  <button
                    [ngClass]="{ 'not-editable': noPermission }"
                    [disabled]="noPermission"
                    (click)="editPortTable(row)"
                    color="primary"
                    mat-raised-button
                  >
                    {{ 'general.button.edit' | translate }}
                  </button>
                </mat-action-row>
              </mat-expansion-panel>
              <mat-paginator
                [length]="port1588defaultSettingTableDataLength"
                [pageSize]="port1588defaultSettingTablePanelPageSize"
                [hidePageSize]="true"
                (page)="onPanelPageChange(ProfileType.PROFILE_1588V2_DEFAULT, 'setting', $event)"
              >
              </mat-paginator>
            </div>
            <div fxShow="true" fxHide.xs="true">
              <div class="app-table-wrapper">
                <table
                  #port1588defaultSettingTableSort="matSort"
                  [dataSource]="port1588defaultSettingTableDataSource"
                  mat-table
                  matSort
                >
                  <ng-container matColumnDef="edit">
                    <th *matHeaderCellDef mat-header-cell></th>
                    <td *matCellDef="let row" mat-cell>
                      <mat-icon
                        class="table-icon-action"
                        *ngIf="!noPermission && !row.isMemberPort"
                        (click)="editPortTable(row)"
                        matTooltip="{{ 'general.tooltip.edit' | translate }}"
                        attr.id="button-edit-port-{{ row.port }}"
                      >
                        edit</mat-icon
                      >
                      <mat-icon
                        class="table-not-editable"
                        *ngIf="noPermission || row.isMemberPort"
                        [matTooltipDisabled]="noPermission"
                        matTooltip="{{ 'general.tooltip.disable_la_member_hint' | translate }}"
                        matTooltipPosition="right"
                        >edit</mat-icon
                      >
                    </td>
                  </ng-container>
                  <ng-container matColumnDef="port">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'general.common_port.port' | translate }}
                    </th>
                    <td *matCellDef="let row" [ngClass]="utils.getPortClass(row)" mat-cell>{{ row.port }}</td>
                  </ng-container>
                  <ng-container matColumnDef="time-sync-enable">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.page_title' | translate }}
                    </th>
                    <td *matCellDef="let row" [ngClass]="utils.getTextClass(row.enable)" mat-cell>{{ row.enable }}</td>
                  </ng-container>
                  <ng-container matColumnDef="announce-interval">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.announce_interval' | translate }}
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.announceInterval }}</td>
                  </ng-container>
                  <ng-container matColumnDef="announce-receipt-timeout">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.announce_receipt_timeout' | translate }}
                      ({{ 'general.unit.times' | translate }})
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.announceReceiptTimeout }}</td>
                  </ng-container>
                  <ng-container matColumnDef="sync-interval">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.sync_interval' | translate }}
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.syncInterval }}</td>
                  </ng-container>
                  <ng-container matColumnDef="delay-req-interval">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.delay_req_interval' | translate }}
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.delayReqInterval }}</td>
                  </ng-container>
                  <ng-container matColumnDef="pdelay-req-interval">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.pdelay_req_interval' | translate }}
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.pdelayReqInterval }}</td>
                  </ng-container>
                  <ng-container matColumnDef="dummy">
                    <th *matHeaderCellDef mat-header-cell></th>
                    <td *matCellDef="let row" mat-cell></td>
                  </ng-container>
                  <tr *matHeaderRowDef="port1588defaultSettingTableDisplayedColumns; sticky: true" mat-header-row></tr>
                  <tr *matRowDef="let row; columns: port1588defaultSettingTableDisplayedColumns" mat-row></tr>
                </table>
              </div>
              <mat-paginator
                #port1588defaultSettingTablePaginator
                [ngClass]="{ disableChangePage: port1588defaultSettingTableDataLength <= 50 }"
                [hidePageSize]="true"
                [showFirstLastButtons]="false"
              ></mat-paginator>
            </div>
          </div>
        </mat-card>
        <mat-card *ngIf="hasProfile(ProfileType.PROFILE_IEC61850)">
          <mat-card-header fxLayout="column">
            <mat-card-title fxLayout="row" fxLayoutAlign="space-between center">
              <div ngClass="card-title">{{ 'features.time_sync.iec61850' | translate }}</div>
            </mat-card-title>
          </mat-card-header>
          <div fxLayout="row" fxLayoutAlign="space-between center">
            <div class="table-icon-tool-bar-div" fxFlex="70" fxFlex.lt-lg="50"></div>
            <div class="filter-input-block" fxFlex="30" fxFlex.lt-lg="50">
              <mat-form-field class="form-field-search">
                <mat-icon class="icon-search" matPrefix>search</mat-icon>
                <input
                  (keyup)="updateTableFilter(ProfileType.PROFILE_IEC61850, 'setting', $event.target.value)"
                  matInput
                  placeholder="{{ 'general.table_function.filter_desc' | translate }}"
                />
              </mat-form-field>
            </div>
          </div>
          <div id="dataContent">
            <div fxShow.xs="true" fxHide="true">
              <mat-expansion-panel class="expansion-panel-wrapper" *ngFor="let row of portIec61850SettingTableRows">
                <mat-expansion-panel-header>
                  <mat-panel-title [ngClass]="utils.getPortClass(row)"
                    >{{ row.port }} / {{ row.enable }}</mat-panel-title
                  >
                </mat-expansion-panel-header>
                <p>{{ 'general.common_port.port' | translate }} : {{ row.port }}</p>
                <p>{{ 'features.time_sync.page_title_abbr' | translate }} : {{ row.enable }}</p>
                <p *ngIf="settingIec61850Parms.isClockTypeBC">
                  {{ 'features.time_sync.announce_interval' | translate }} : {{ row.announceInterval }}
                </p>
                <p *ngIf="settingIec61850Parms.isClockTypeBC">
                  {{ 'features.time_sync.announce_receipt_timeout' | translate }} : {{ row.announceReceiptTimeout }}
                  {{ 'general.unit.times' | translate }}
                </p>
                <p *ngIf="settingIec61850Parms.isClockTypeBC">
                  {{ 'features.time_sync.sync_interval' | translate }} : {{ row.syncInterval }}
                </p>
                <p *ngIf="settingIec61850Parms.isClockTypeBC && settingIec61850Parms.isDelayMechanismE2E">
                  {{ 'features.time_sync.delay_req_interval' | translate }} : {{ row.delayReqInterval }}
                </p>
                <p *ngIf="settingIec61850Parms.isDelayMechanismP2P">
                  {{ 'features.time_sync.pdelay_req_interval' | translate }} : {{ row.pdelayReqInterval }}
                </p>
                <mat-action-row>
                  <button
                    [ngClass]="{ 'not-editable': noPermission }"
                    [disabled]="noPermission"
                    (click)="editPortTable(row)"
                    color="primary"
                    mat-raised-button
                  >
                    {{ 'general.button.edit' | translate }}
                  </button>
                </mat-action-row>
              </mat-expansion-panel>
              <mat-paginator
                [length]="portIec61850SettingTableDataLength"
                [pageSize]="portIec61850SettingTablePanelPageSize"
                [hidePageSize]="true"
                (page)="onPanelPageChange(ProfileType.PROFILE_IEC61850, 'setting', $event)"
              >
              </mat-paginator>
            </div>
            <div fxShow="true" fxHide.xs="true">
              <div class="app-table-wrapper">
                <table
                  #portIec61850SettingTableSort="matSort"
                  [dataSource]="portIec61850SettingTableDataSource"
                  mat-table
                  matSort
                >
                  <ng-container matColumnDef="edit">
                    <th *matHeaderCellDef mat-header-cell></th>
                    <td *matCellDef="let row" mat-cell>
                      <mat-icon
                        class="table-icon-action"
                        *ngIf="!noPermission && !row.isMemberPort"
                        (click)="editPortTable(row)"
                        matTooltip="{{ 'general.tooltip.edit' | translate }}"
                        attr.id="button-edit-port-{{ row.port }}"
                      >
                        edit</mat-icon
                      >
                      <mat-icon
                        class="table-not-editable"
                        *ngIf="noPermission || row.isMemberPort"
                        [matTooltipDisabled]="noPermission"
                        matTooltip="{{ 'general.tooltip.disable_la_member_hint' | translate }}"
                        matTooltipPosition="right"
                        >edit</mat-icon
                      >
                    </td>
                  </ng-container>
                  <ng-container matColumnDef="port">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'general.common_port.port' | translate }}
                    </th>
                    <td *matCellDef="let row" [ngClass]="utils.getPortClass(row)" mat-cell>{{ row.port }}</td>
                  </ng-container>
                  <ng-container matColumnDef="time-sync-enable">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.page_title' | translate }}
                    </th>
                    <td *matCellDef="let row" [ngClass]="utils.getTextClass(row.enable)" mat-cell>{{ row.enable }}</td>
                  </ng-container>
                  <ng-container matColumnDef="announce-interval">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.announce_interval' | translate }}
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.announceInterval }}</td>
                  </ng-container>
                  <ng-container matColumnDef="announce-receipt-timeout">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.announce_receipt_timeout' | translate }}
                      ({{ 'general.unit.times' | translate }})
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.announceReceiptTimeout }}</td>
                  </ng-container>
                  <ng-container matColumnDef="sync-interval">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.sync_interval' | translate }}
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.syncInterval }}</td>
                  </ng-container>
                  <ng-container matColumnDef="delay-req-interval">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.delay_req_interval' | translate }}
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.delayReqInterval }}</td>
                  </ng-container>
                  <ng-container matColumnDef="pdelay-req-interval">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.pdelay_req_interval' | translate }}
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.pdelayReqInterval }}</td>
                  </ng-container>
                  <ng-container matColumnDef="dummy">
                    <th *matHeaderCellDef mat-header-cell></th>
                    <td *matCellDef="let row" mat-cell></td>
                  </ng-container>
                  <tr *matHeaderRowDef="portIec61850SettingTableDisplayedColumns; sticky: true" mat-header-row></tr>
                  <tr *matRowDef="let row; columns: portIec61850SettingTableDisplayedColumns" mat-row></tr>
                </table>
              </div>
              <mat-paginator
                #portIec61850SettingTablePaginator
                [ngClass]="{ disableChangePage: portIec61850SettingTableDataLength <= 50 }"
                [hidePageSize]="true"
                [showFirstLastButtons]="false"
              ></mat-paginator>
            </div>
          </div>
        </mat-card>
        <mat-card *ngIf="hasProfile(ProfileType.PROFILE_C37_238)">
          <mat-card-header fxLayout="column">
            <mat-card-title fxLayout="row" fxLayoutAlign="space-between center">
              <div ngClass="card-title">{{ 'features.time_sync.c37238' | translate }}</div>
            </mat-card-title>
          </mat-card-header>
          <div fxLayout="row" fxLayoutAlign="space-between center">
            <div class="table-icon-tool-bar-div" fxFlex="70" fxFlex.lt-lg="50"></div>
            <div class="filter-input-block" fxFlex="30" fxFlex.lt-lg="50">
              <mat-form-field class="form-field-search">
                <mat-icon class="icon-search" matPrefix>search</mat-icon>
                <input
                  (keyup)="updateTableFilter(ProfileType.PROFILE_C37_238, 'setting', $event.target.value)"
                  matInput
                  placeholder="{{ 'general.table_function.filter_desc' | translate }}"
                />
              </mat-form-field>
            </div>
          </div>
          <div id="dataContent">
            <div fxShow.xs="true" fxHide="true">
              <mat-expansion-panel class="expansion-panel-wrapper" *ngFor="let row of portC37238SettingTableRows">
                <mat-expansion-panel-header>
                  <mat-panel-title [ngClass]="utils.getPortClass(row)"
                    >{{ row.port }} / {{ row.enable }}</mat-panel-title
                  >
                </mat-expansion-panel-header>
                <p>{{ 'general.common_port.port' | translate }} : {{ row.port }}</p>
                <p>{{ 'features.time_sync.page_title_abbr' | translate }} : {{ row.enable }}</p>
                <p *ngIf="settingC37238Parms.isClockTypeBC">
                  {{ 'features.time_sync.announce_interval' | translate }} : {{ row.announceInterval }}
                </p>
                <p *ngIf="settingC37238Parms.isClockTypeBC">
                  {{ 'features.time_sync.announce_receipt_timeout' | translate }} : {{ row.announceReceiptTimeout }}
                  {{ 'general.unit.times' | translate }}
                </p>
                <p *ngIf="settingC37238Parms.isClockTypeBC">
                  {{ 'features.time_sync.sync_interval' | translate }} : {{ row.syncInterval }}
                </p>
                <p *ngIf="settingC37238Parms.isClockTypeBC && settingC37238Parms.isDelayMechanismE2E">
                  {{ 'features.time_sync.delay_req_interval' | translate }} : {{ row.delayReqInterval }}
                </p>
                <p *ngIf="settingC37238Parms.isDelayMechanismP2P">
                  {{ 'features.time_sync.pdelay_req_interval' | translate }} : {{ row.pdelayReqInterval }}
                </p>
                <mat-action-row>
                  <button
                    [ngClass]="{ 'not-editable': noPermission }"
                    [disabled]="noPermission"
                    (click)="editPortTable(row)"
                    color="primary"
                    mat-raised-button
                  >
                    {{ 'general.button.edit' | translate }}
                  </button>
                </mat-action-row>
              </mat-expansion-panel>
              <mat-paginator
                [length]="portC37238SettingTableDataLength"
                [pageSize]="portC37238SettingTablePanelPageSize"
                [hidePageSize]="true"
                (page)="onPanelPageChange(ProfileType.PROFILE_C37_238, 'setting', $event)"
              >
              </mat-paginator>
            </div>
            <div fxShow="true" fxHide.xs="true">
              <div class="app-table-wrapper">
                <table
                  #portC37238SettingTableSort="matSort"
                  [dataSource]="portC37238SettingTableDataSource"
                  mat-table
                  matSort
                >
                  <ng-container matColumnDef="edit">
                    <th *matHeaderCellDef mat-header-cell></th>
                    <td *matCellDef="let row" mat-cell>
                      <mat-icon
                        class="table-icon-action"
                        *ngIf="!noPermission && !row.isMemberPort"
                        (click)="editPortTable(row)"
                        matTooltip="{{ 'general.tooltip.edit' | translate }}"
                        attr.id="button-edit-port-{{ row.port }}"
                      >
                        edit</mat-icon
                      >
                      <mat-icon
                        class="table-not-editable"
                        *ngIf="noPermission || row.isMemberPort"
                        [matTooltipDisabled]="noPermission"
                        matTooltip="{{ 'general.tooltip.disable_la_member_hint' | translate }}"
                        matTooltipPosition="right"
                        >edit</mat-icon
                      >
                    </td>
                  </ng-container>
                  <ng-container matColumnDef="port">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'general.common_port.port' | translate }}
                    </th>
                    <td *matCellDef="let row" [ngClass]="utils.getPortClass(row)" mat-cell>{{ row.port }}</td>
                  </ng-container>
                  <ng-container matColumnDef="time-sync-enable">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.page_title' | translate }}
                    </th>
                    <td *matCellDef="let row" [ngClass]="utils.getTextClass(row.enable)" mat-cell>{{ row.enable }}</td>
                  </ng-container>
                  <ng-container matColumnDef="announce-interval">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.announce_interval' | translate }}
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.announceInterval }}</td>
                  </ng-container>
                  <ng-container matColumnDef="announce-receipt-timeout">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.announce_receipt_timeout' | translate }}
                      ({{ 'general.unit.times' | translate }})
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.announceReceiptTimeout }}</td>
                  </ng-container>
                  <ng-container matColumnDef="sync-interval">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.sync_interval' | translate }}
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.syncInterval }}</td>
                  </ng-container>
                  <ng-container matColumnDef="delay-req-interval">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.delay_req_interval' | translate }}
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.delayReqInterval }}</td>
                  </ng-container>
                  <ng-container matColumnDef="pdelay-req-interval">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.pdelay_req_interval' | translate }}
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.pdelayReqInterval }}</td>
                  </ng-container>
                  <ng-container matColumnDef="dummy">
                    <th *matHeaderCellDef mat-header-cell></th>
                    <td *matCellDef="let row" mat-cell></td>
                  </ng-container>
                  <tr *matHeaderRowDef="portC37238SettingTableDisplayedColumns; sticky: true" mat-header-row></tr>
                  <tr *matRowDef="let row; columns: portC37238SettingTableDisplayedColumns" mat-row></tr>
                </table>
              </div>
              <mat-paginator
                #portC37238SettingTablePaginator
                [ngClass]="{ disableChangePage: portC37238SettingTableDataLength <= 50 }"
                [hidePageSize]="true"
                [showFirstLastButtons]="false"
              ></mat-paginator>
            </div>
          </div>
        </mat-card>
      </mat-tab>
      <mat-tab #statusTab label="{{ 'general.common.status' | translate }}">
        <mat-card *ngIf="hasProfile(ProfileType.PROFILE_8021AS) && port8021asStatusCardData">
          <mat-card-header fxLayout="row" fxLayoutAlign="space-between center">
            <mat-card-title>{{ 'features.time_sync.8021as' | translate }}</mat-card-title>
            <div fxLayout="row" fxLayoutAlign="start center">
              <div
                class="last-update-text"
                fxHide.xs="true"
                matTooltip="{{ 'features.utilization.last_update_time' | translate }}"
              >
                {{ port8021asStatusCardData.chartUpdateTime }}
              </div>
              <button class="mat-card-title-icon" (click)="clearGraph(ProfileType.PROFILE_8021AS)" mat-icon-button>
                <mat-icon matTooltip="{{ 'general.tooltip.clear_graph' | translate }}"> delete_sweep</mat-icon>
              </button>
            </div>
          </mat-card-header>
          <mat-card-content fxLayout="column">
            <div class="chart-container">
              <canvas #port8021asChart></canvas>
            </div>
            <div>
              <div class="card-subtitle">{{ 'general.common.status' | translate }}</div>
              <div fxLayout="row wrap">
                <div fxFlex="220px">
                  <dt mat-line>{{ 'features.time_sync.page_title' | translate }}</dt>
                  <dd mat-line>{{ port8021asStatusCardData.enable }}</dd>
                </div>
                <div fxFlex="220px">
                  <dt mat-line>{{ 'features.time_sync.synchronization_status' | translate }}</dt>
                  <dd mat-line>{{ port8021asStatusCardData.syncLocked }}</dd>
                </div>
                <div fxFlex="220px">
                  <dt mat-line>{{ 'features.time_sync.ptp_slave_port' | translate }}</dt>
                  <dd mat-line>{{ port8021asStatusCardData.slavePort }}</dd>
                </div>
                <div fxFlex="200px">
                  <dt mat-line>{{ 'features.time_sync.ptp_clock_time' | translate }}</dt>
                  <dd mat-line>{{ port8021asStatusCardData.clockTime }}</dd>
                </div>
              </div>
            </div>
            <div>
              <div class="card-subtitle">{{ 'features.time_sync.current_data_set' | translate }}</div>
              <div fxLayout="row wrap">
                <div fxFlex="220px">
                  <dt mat-line>
                    {{ 'features.time_sync.offset_from_master' | translate }} ({{ 'general.unit.ns' | translate }})
                  </dt>
                  <dd mat-line>{{ port8021asStatusCardData.offsetFromMaster }}</dd>
                </div>
                <div fxFlex="220px">
                  <dt mat-line>
                    {{ 'features.time_sync.mean_path_delay' | translate }} ({{ 'general.unit.ns' | translate }})
                  </dt>
                  <dd mat-line>{{ port8021asStatusCardData.meanPathDelay }}</dd>
                </div>
                <div fxFlex="220px">
                  <dt mat-line>{{ 'features.time_sync.steps_removed' | translate }}</dt>
                  <dd mat-line>{{ port8021asStatusCardData.stepsRemoved }}</dd>
                </div>
              </div>
            </div>
            <div>
              <div class="card-subtitle">{{ 'features.time_sync.parent_data_set' | translate }}</div>
              <div fxLayout="row wrap">
                <div fxFlex="220px">
                  <dt mat-line>{{ 'features.time_sync.parent_identity' | translate }}</dt>
                  <dd mat-line>{{ port8021asStatusCardData.parentIdentity }}</dd>
                </div>
                <div fxFlex="220px">
                  <dt mat-line>{{ 'features.time_sync.grandmaster_identity' | translate }}</dt>
                  <dd mat-line>{{ port8021asStatusCardData.gmIdentity }}</dd>
                </div>
                <div fxFlex="220px">
                  <dt mat-line>{{ 'features.time_sync.grandmaster_priority_number' | translate : { number: 1 } }}</dt>
                  <dd mat-line>{{ port8021asStatusCardData.gmPriority1 }}</dd>
                </div>
                <div fxFlex="220px">
                  <dt mat-line>{{ 'features.time_sync.grandmaster_priority_number' | translate : { number: 2 } }}</dt>
                  <dd mat-line>{{ port8021asStatusCardData.gmPriority2 }}</dd>
                </div>
              </div>
              <div fxLayout="row wrap">
                <div fxFlex="220px">
                  <dt mat-line>{{ 'features.time_sync.grandmaster_clock_class' | translate }}</dt>
                  <dd mat-line>{{ port8021asStatusCardData.gmClockClass }}</dd>
                </div>
                <div fxFlex="220px">
                  <dt mat-line>{{ 'features.time_sync.grandmaster_clock_accuracy' | translate }}</dt>
                  <dd mat-line>{{ port8021asStatusCardData.gmClockAccuracy }}</dd>
                </div>
                <div fxFlex="220px">
                  <dt mat-line>{{ 'features.time_sync.cumulative_rate_ratio' | translate }}</dt>
                  <dd mat-line>{{ port8021asStatusCardData.cumulativeRateRatio }}</dd>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
        <mat-card *ngIf="hasProfile(ProfileType.PROFILE_1588V2_DEFAULT) && port1588defaultCardData">
          <mat-card-header fxLayout="row" fxLayoutAlign="space-between center">
            <mat-card-title>{{ 'features.time_sync.1588default' | translate }}</mat-card-title>
            <div fxLayout="row" fxLayoutAlign="start center">
              <div
                class="last-update-text"
                fxHide.xs="true"
                matTooltip="{{ 'features.utilization.last_update_time' | translate }}"
              >
                {{ port1588defaultCardData.chartUpdateTime }}
              </div>
              <button
                class="mat-card-title-icon"
                *ngIf="settingMx1588Parms.isClockTypeBC"
                (click)="clearGraph(ProfileType.PROFILE_1588V2_DEFAULT)"
                mat-icon-button
              >
                <mat-icon matTooltip="{{ 'general.tooltip.clear_graph' | translate }}"> delete_sweep</mat-icon>
              </button>
            </div>
          </mat-card-header>
          <div *ngIf="settingMx1588Parms.isClockTypeBC">
            <div class="chart-container">
              <canvas #port1588defaultChart></canvas>
            </div>
            <mat-card-content fxLayout="column">
              <div>
                <div class="card-subtitle">{{ 'general.common.status' | translate }}</div>
                <div fxLayout="row wrap">
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.page_title' | translate }}</dt>
                    <dd mat-line>{{ port1588defaultCardData.enable }}</dd>
                  </div>
                  <div *ngIf="!notSupport.syncStatus" fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.synchronization_status' | translate }}</dt>
                    <dd mat-line>{{ port1588defaultCardData.syncStatus }}</dd>
                  </div>
                  <div *ngIf="!notSupport.syncLocked" fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.synchronization_status' | translate }}</dt>
                    <dd mat-line>{{ port1588defaultCardData.syncLocked }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.clock_type' | translate }}</dt>
                    <dd mat-line>{{ port1588defaultCardData.clockType }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.ptp_slave_port' | translate }}</dt>
                    <dd mat-line>{{ port1588defaultCardData.slavePort }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.transport_type' | translate }}</dt>
                    <dd mat-line>{{ port1588defaultCardData.transportType }}</dd>
                  </div>
                  <div fxFlex="200px">
                    <dt mat-line>{{ 'features.time_sync.ptp_clock_time' | translate }}</dt>
                    <dd mat-line>{{ port1588defaultCardData.clockTime }}</dd>
                  </div>
                </div>
              </div>
              <div>
                <div class="card-subtitle">{{ 'features.time_sync.current_data_set' | translate }}</div>
                <div fxLayout="row wrap">
                  <div fxFlex="220px">
                    <dt mat-line>
                      {{ 'features.time_sync.offset_from_master' | translate }} ({{ 'general.unit.ns' | translate }})
                    </dt>
                    <dd mat-line>{{ port1588defaultCardData.offsetFromMaster }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>
                      {{ 'features.time_sync.mean_path_delay' | translate }} ({{ 'general.unit.ns' | translate }})
                    </dt>
                    <dd mat-line>{{ port1588defaultCardData.meanPathDelay }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.steps_removed' | translate }}</dt>
                    <dd mat-line>{{ port1588defaultCardData.stepsRemoved }}</dd>
                  </div>
                </div>
              </div>
              <div>
                <div class="card-subtitle">{{ 'features.time_sync.parent_data_set' | translate }}</div>
                <div fxLayout="row wrap">
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.parent_identity' | translate }}</dt>
                    <dd mat-line>{{ port1588defaultCardData.parentIdentity }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.grandmaster_identity' | translate }}</dt>
                    <dd mat-line>{{ port1588defaultCardData.gmIdentity }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.grandmaster_priority_number' | translate : { number: 1 } }}</dt>
                    <dd mat-line>{{ port1588defaultCardData.gmPriority1 }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.grandmaster_priority_number' | translate : { number: 2 } }}</dt>
                    <dd mat-line>{{ port1588defaultCardData.gmPriority2 }}</dd>
                  </div>
                </div>
                <div fxLayout="row wrap">
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.grandmaster_clock_class' | translate }}</dt>
                    <dd mat-line>{{ port1588defaultCardData.gmClockClass }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.grandmaster_clock_accuracy' | translate }}</dt>
                    <dd mat-line>{{ port1588defaultCardData.gmClockAccuracy }}</dd>
                  </div>
                </div>
              </div>
            </mat-card-content>
          </div>
          <div *ngIf="settingMx1588Parms.isClockTypeTC">
            <mat-card-content fxLayout="column">
              <div class="card-subtitle">{{ 'general.common.status' | translate }}</div>
              <div fxLayout="row wrap">
                <div fxFlex="220px">
                  <dt mat-line>{{ 'features.time_sync.page_title' | translate }}</dt>
                  <dd mat-line>{{ port1588defaultCardData.enable }}</dd>
                </div>
                <div fxFlex="200px">
                  <dt mat-line>{{ 'features.time_sync.synchronization_status' | translate }}</dt>
                  <dd mat-line>{{ port1588defaultCardData.syncStatus }}</dd>
                </div>
                <div fxFlex="220px">
                  <dt mat-line>{{ 'features.time_sync.clock_type' | translate }}</dt>
                  <dd mat-line>{{ port1588defaultCardData.clockType }}</dd>
                </div>
                <div fxFlex="220px">
                  <dt mat-line>{{ 'features.time_sync.transport_type' | translate }}</dt>
                  <dd mat-line>{{ port1588defaultCardData.transportType }}</dd>
                </div>
              </div>
            </mat-card-content>
          </div>
        </mat-card>
        <mat-card *ngIf="hasProfile(ProfileType.PROFILE_IEC61850) && portIec61850CardData">
          <mat-card-header fxLayout="row" fxLayoutAlign="space-between center">
            <mat-card-title>{{ 'features.time_sync.iec61850' | translate }}</mat-card-title>
            <div fxLayout="row" fxLayoutAlign="start center">
              <div
                class="last-update-text"
                fxHide.xs="true"
                matTooltip="{{ 'features.utilization.last_update_time' | translate }}"
              >
                {{ portIec61850CardData.chartUpdateTime }}
              </div>
              <button
                class="mat-card-title-icon"
                *ngIf="settingIec61850Parms.isClockTypeBC"
                (click)="clearGraph(ProfileType.PROFILE_IEC61850)"
                mat-icon-button
              >
                <mat-icon matTooltip="{{ 'general.tooltip.clear_graph' | translate }}"> delete_sweep</mat-icon>
              </button>
            </div>
          </mat-card-header>
          <div *ngIf="settingIec61850Parms.isClockTypeBC">
            <div class="chart-container">
              <canvas #portIec61850Chart></canvas>
            </div>
            <mat-card-content fxLayout="column">
              <div>
                <div class="card-subtitle">{{ 'general.common.status' | translate }}</div>
                <div fxLayout="row wrap">
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.page_title' | translate }}</dt>
                    <dd mat-line>{{ portIec61850CardData.enable }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.synchronization_status' | translate }}</dt>
                    <dd mat-line>{{ portIec61850CardData.syncStatus }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.clock_type' | translate }}</dt>
                    <dd mat-line>{{ portIec61850CardData.clockType }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.ptp_slave_port' | translate }}</dt>
                    <dd mat-line>{{ portIec61850CardData.slavePort }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.transport_type' | translate }}</dt>
                    <dd mat-line>{{ portIec61850CardData.transportType }}</dd>
                  </div>
                  <div fxFlex="200px">
                    <dt mat-line>{{ 'features.time_sync.ptp_clock_time' | translate }}</dt>
                    <dd mat-line>{{ portIec61850CardData.clockTime }}</dd>
                  </div>
                </div>
              </div>
              <div>
                <div class="card-subtitle">{{ 'features.time_sync.current_data_set' | translate }}</div>
                <div fxLayout="row wrap">
                  <div fxFlex="220px">
                    <dt mat-line>
                      {{ 'features.time_sync.offset_from_master' | translate }} ({{ 'general.unit.ns' | translate }})
                    </dt>
                    <dd mat-line>{{ portIec61850CardData.offsetFromMaster }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>
                      {{ 'features.time_sync.mean_path_delay' | translate }} ({{ 'general.unit.ns' | translate }})
                    </dt>
                    <dd mat-line>{{ portIec61850CardData.meanPathDelay }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.steps_removed' | translate }}</dt>
                    <dd mat-line>{{ portIec61850CardData.stepsRemoved }}</dd>
                  </div>
                </div>
              </div>
              <div>
                <div class="card-subtitle">{{ 'features.time_sync.parent_data_set' | translate }}</div>
                <div fxLayout="row wrap">
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.parent_identity' | translate }}</dt>
                    <dd mat-line>{{ portIec61850CardData.parentIdentity }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.grandmaster_identity' | translate }}</dt>
                    <dd mat-line>{{ portIec61850CardData.gmIdentity }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.grandmaster_priority_number' | translate : { number: 1 } }}</dt>
                    <dd mat-line>{{ portIec61850CardData.gmPriority1 }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.grandmaster_priority_number' | translate : { number: 2 } }}</dt>
                    <dd mat-line>{{ portIec61850CardData.gmPriority2 }}</dd>
                  </div>
                </div>
                <div fxLayout="row wrap">
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.grandmaster_clock_class' | translate }}</dt>
                    <dd mat-line>{{ portIec61850CardData.gmClockClass }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.grandmaster_clock_accuracy' | translate }}</dt>
                    <dd mat-line>{{ portIec61850CardData.gmClockAccuracy }}</dd>
                  </div>
                </div>
              </div>
            </mat-card-content>
          </div>
          <div *ngIf="settingIec61850Parms.isClockTypeTC">
            <mat-card-content fxLayout="column">
              <div class="card-subtitle">{{ 'general.common.status' | translate }}</div>
              <div fxLayout="row wrap">
                <div fxFlex="220px">
                  <dt mat-line>{{ 'features.time_sync.page_title' | translate }}</dt>
                  <dd mat-line>{{ portIec61850CardData.enable }}</dd>
                </div>
                <div fxFlex="200px">
                  <dt mat-line>{{ 'features.time_sync.synchronization_status' | translate }}</dt>
                  <dd mat-line>{{ portIec61850CardData.syncStatus }}</dd>
                </div>
                <div fxFlex="220px">
                  <dt mat-line>{{ 'features.time_sync.clock_type' | translate }}</dt>
                  <dd mat-line>{{ portIec61850CardData.clockType }}</dd>
                </div>
                <div fxFlex="220px">
                  <dt mat-line>{{ 'features.time_sync.transport_type' | translate }}</dt>
                  <dd mat-line>{{ portIec61850CardData.transportType }}</dd>
                </div>
              </div>
            </mat-card-content>
          </div>
        </mat-card>
        <mat-card *ngIf="hasProfile(ProfileType.PROFILE_C37_238) && portC37238CardData">
          <mat-card-header fxLayout="row" fxLayoutAlign="space-between center">
            <mat-card-title>{{ 'features.time_sync.c37238' | translate }}</mat-card-title>
            <div fxLayout="row" fxLayoutAlign="start center">
              <div
                class="last-update-text"
                fxHide.xs="true"
                matTooltip="{{ 'features.utilization.last_update_time' | translate }}"
              >
                {{ portC37238CardData.chartUpdateTime }}
              </div>
              <button
                class="mat-card-title-icon"
                *ngIf="settingC37238Parms.isClockTypeBC"
                (click)="clearGraph(ProfileType.PROFILE_C37_238)"
                mat-icon-button
              >
                <mat-icon matTooltip="{{ 'general.tooltip.clear_graph' | translate }}"> delete_sweep</mat-icon>
              </button>
            </div>
          </mat-card-header>
          <div *ngIf="settingC37238Parms.isClockTypeBC">
            <div class="chart-container">
              <canvas #portC37238Chart></canvas>
            </div>
            <mat-card-content fxLayout="column">
              <div>
                <div class="card-subtitle">{{ 'general.common.status' | translate }}</div>
                <div fxLayout="row wrap">
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.page_title' | translate }}</dt>
                    <dd mat-line>{{ portC37238CardData.enable }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.synchronization_status' | translate }}</dt>
                    <dd mat-line>{{ portC37238CardData.syncStatus }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.clock_type' | translate }}</dt>
                    <dd mat-line>{{ portC37238CardData.clockType }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.ptp_slave_port' | translate }}</dt>
                    <dd mat-line>{{ portC37238CardData.slavePort }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.transport_type' | translate }}</dt>
                    <dd mat-line>{{ portC37238CardData.transportType }}</dd>
                  </div>
                  <div fxFlex="200px">
                    <dt mat-line>{{ 'features.time_sync.ptp_clock_time' | translate }}</dt>
                    <dd mat-line>{{ portC37238CardData.clockTime }}</dd>
                  </div>
                </div>
              </div>
              <div>
                <div class="card-subtitle">{{ 'features.time_sync.current_data_set' | translate }}</div>
                <div fxLayout="row wrap">
                  <div fxFlex="220px">
                    <dt mat-line>
                      {{ 'features.time_sync.offset_from_master' | translate }} ({{ 'general.unit.ns' | translate }})
                    </dt>
                    <dd mat-line>{{ portC37238CardData.offsetFromMaster }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>
                      {{ 'features.time_sync.mean_path_delay' | translate }} ({{ 'general.unit.ns' | translate }})
                    </dt>
                    <dd mat-line>{{ portC37238CardData.meanPathDelay }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.steps_removed' | translate }}</dt>
                    <dd mat-line>{{ portC37238CardData.stepsRemoved }}</dd>
                  </div>
                </div>
              </div>
              <div>
                <div class="card-subtitle">{{ 'features.time_sync.parent_data_set' | translate }}</div>
                <div fxLayout="row wrap">
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.parent_identity' | translate }}</dt>
                    <dd mat-line>{{ portC37238CardData.parentIdentity }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.grandmaster_identity' | translate }}</dt>
                    <dd mat-line>{{ portC37238CardData.gmIdentity }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.grandmaster_priority_number' | translate : { number: 1 } }}</dt>
                    <dd mat-line>{{ portC37238CardData.gmPriority1 }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.grandmaster_priority_number' | translate : { number: 2 } }}</dt>
                    <dd mat-line>{{ portC37238CardData.gmPriority2 }}</dd>
                  </div>
                </div>
                <div fxLayout="row wrap">
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.grandmaster_clock_class' | translate }}</dt>
                    <dd mat-line>{{ portC37238CardData.gmClockClass }}</dd>
                  </div>
                  <div fxFlex="220px">
                    <dt mat-line>{{ 'features.time_sync.grandmaster_clock_accuracy' | translate }}</dt>
                    <dd mat-line>{{ portC37238CardData.gmClockAccuracy }}</dd>
                  </div>
                </div>
              </div>
            </mat-card-content>
          </div>
          <div *ngIf="settingC37238Parms.isClockTypeTC">
            <mat-card-content fxLayout="column">
              <div class="card-subtitle">{{ 'general.common.status' | translate }}</div>
              <div fxLayout="row wrap">
                <div fxFlex="220px">
                  <dt mat-line>{{ 'features.time_sync.page_title' | translate }}</dt>
                  <dd mat-line>{{ portC37238CardData.enable }}</dd>
                </div>
                <div fxFlex="200px">
                  <dt mat-line>{{ 'features.time_sync.synchronization_status' | translate }}</dt>
                  <dd mat-line>{{ portC37238CardData.syncStatus }}</dd>
                </div>
                <div fxFlex="220px">
                  <dt mat-line>{{ 'features.time_sync.clock_type' | translate }}</dt>
                  <dd mat-line>{{ portC37238CardData.clockType }}</dd>
                </div>
                <div fxFlex="220px">
                  <dt mat-line>{{ 'features.time_sync.transport_type' | translate }}</dt>
                  <dd mat-line>{{ portC37238CardData.transportType }}</dd>
                </div>
              </div>
            </mat-card-content>
          </div>
        </mat-card>
      </mat-tab>
      <mat-tab label="{{ 'general.common_port.port_status' | translate }}">
        <mat-card *ngIf="hasProfile(ProfileType.PROFILE_8021AS)">
          <mat-card-header fxLayout="column">
            <mat-card-title fxLayout="row" fxLayoutAlign="space-between center">
              <div ngClass="card-title">{{ 'features.time_sync.8021as' | translate }}</div>
            </mat-card-title>
          </mat-card-header>
          <div fxLayout="row" fxLayoutAlign="space-between center">
            <div class="table-icon-tool-bar-div" fxFlex="70" fxFlex.lt-lg="50">
              <button id="button-refresh" [disabled]="refreshDisable" (click)="refresh()" mat-icon-button>
                <mat-icon matTooltip="{{ 'general.common.refresh' | translate }}">refresh</mat-icon>
              </button>
            </div>
            <div class="filter-input-block" fxFlex="30" fxFlex.lt-lg="50">
              <mat-form-field class="form-field-search">
                <mat-icon class="icon-search" matPrefix>search</mat-icon>
                <input
                  (keyup)="updateTableFilter(ProfileType.PROFILE_8021AS, 'status', $event.target.value)"
                  matInput
                  placeholder="{{ 'general.table_function.filter_desc' | translate }}"
                />
              </mat-form-field>
            </div>
          </div>
          <div id="dataContent">
            <div fxShow.xs="true" fxHide="true">
              <mat-expansion-panel class="expansion-panel-wrapper" *ngFor="let row of port8021asStatusTableRows">
                <mat-expansion-panel-header>
                  <mat-panel-title [ngClass]="utils.getPortClass(row)">{{ row.port }}</mat-panel-title>
                </mat-expansion-panel-header>
                <p>{{ 'general.common_port.port' | translate }} : {{ row.port }}</p>
                <p>{{ 'general.common_port.port_status' | translate }} : {{ row.portRole }}</p>
                <p>{{ 'features.time_sync.8021as_capable' | translate }} : {{ row.asCapable }}</p>
                <p>
                  {{ 'features.time_sync.neighbor_prop_delay' | translate }} ({{ 'general.unit.ns' | translate }}) :
                  {{ row.neighborPropDelay }}
                </p>
                <p>{{ 'features.time_sync.neighbor_rate_ratio' | translate }} : {{ row.neighborRateRatio }}</p>
              </mat-expansion-panel>
              <mat-paginator
                [length]="port8021asStatusTableDataLength"
                [pageSize]="port8021asStatusTablePanelPageSize"
                [hidePageSize]="true"
                (page)="onPanelPageChange(ProfileType.PROFILE_8021AS, 'status', $event)"
              >
              </mat-paginator>
            </div>
            <div fxShow="true" fxHide.xs="true">
              <div class="app-table-wrapper">
                <table
                  #port8021asStatusTableSort="matSort"
                  [dataSource]="port8021asStatusTableDataSource"
                  mat-table
                  matSort
                >
                  <ng-container matColumnDef="port">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'general.common_port.port' | translate }}
                    </th>
                    <td *matCellDef="let row" [ngClass]="utils.getPortClass(row)" mat-cell>{{ row.port }}</td>
                  </ng-container>
                  <ng-container matColumnDef="port-status">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'general.common_port.port_state' | translate }}
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.portRole }}</td>
                  </ng-container>
                  <ng-container matColumnDef="8021as-capable">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.8021as_capable' | translate }}
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.asCapable }}</td>
                  </ng-container>
                  <ng-container matColumnDef="neighbor-prop-delay">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.neighbor_prop_delay' | translate }} ({{ 'general.unit.ns' | translate }})
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.neighborPropDelay }}</td>
                  </ng-container>
                  <ng-container matColumnDef="neighbor-rate-ratio">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.neighbor_rate_ratio' | translate }}
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.neighborRateRatio }}</td>
                  </ng-container>
                  <ng-container matColumnDef="dummy">
                    <th *matHeaderCellDef mat-header-cell></th>
                    <td *matCellDef="let row" mat-cell></td>
                  </ng-container>
                  <tr *matHeaderRowDef="port8021asStatusTableDisplayedColumns; sticky: true" mat-header-row></tr>
                  <tr *matRowDef="let row; columns: port8021asStatusTableDisplayedColumns" mat-row></tr>
                </table>
              </div>
              <mat-paginator
                #port8021asStatusTablePaginator
                [ngClass]="{ disableChangePage: port8021asStatusTableDataLength <= 50 }"
                [hidePageSize]="true"
                [showFirstLastButtons]="false"
              ></mat-paginator>
            </div>
          </div>
        </mat-card>
        <mat-card *ngIf="hasProfile(ProfileType.PROFILE_1588V2_DEFAULT)">
          <mat-card-header fxLayout="column">
            <mat-card-title fxLayout="row" fxLayoutAlign="space-between center">
              <div ngClass="card-title">{{ 'features.time_sync.1588default' | translate }}</div>
            </mat-card-title>
          </mat-card-header>
          <div fxLayout="row" fxLayoutAlign="space-between center">
            <div class="table-icon-tool-bar-div" fxFlex="70" fxFlex.lt-lg="50">
              <button id="button-refresh" (click)="refresh()" mat-icon-button>
                <mat-icon matTooltip="{{ 'general.common.refresh' | translate }}">refresh</mat-icon>
              </button>
            </div>
            <div class="filter-input-block" fxFlex="30" fxFlex.lt-lg="50">
              <mat-form-field class="form-field-search">
                <mat-icon class="icon-search" matPrefix>search</mat-icon>
                <input
                  (keyup)="updateTableFilter(ProfileType.PROFILE_1588V2_DEFAULT, 'status', $event.target.value)"
                  matInput
                  placeholder="{{ 'general.table_function.filter_desc' | translate }}"
                />
              </mat-form-field>
            </div>
          </div>
          <div id="dataContent">
            <div fxShow.xs="true" fxHide="true">
              <mat-expansion-panel class="expansion-panel-wrapper" *ngFor="let row of port1588defaultStatusTableRows">
                <mat-expansion-panel-header>
                  <mat-panel-title [ngClass]="utils.getPortClass(row)">{{ row.port }}</mat-panel-title>
                </mat-expansion-panel-header>
                <p>{{ 'general.common_port.port' | translate }} : {{ row.port }}</p>
                <p>{{ 'general.common_port.port_status' | translate }} : {{ row.portState }}</p>
                <p>
                  {{ 'features.time_sync.path_delay' | translate }} ({{ 'general.unit.ns' | translate }}) :
                  {{ row.pathDelay }}
                </p>
              </mat-expansion-panel>
              <mat-paginator
                [length]="port1588defaultStatusTableDataLength"
                [pageSize]="port1588defaultStatusTablePanelPageSize"
                [hidePageSize]="true"
                (page)="onPanelPageChange(ProfileType.PROFILE_1588V2_DEFAULT, 'status', $event)"
              >
              </mat-paginator>
            </div>
            <div fxShow="true" fxHide.xs="true">
              <div class="app-table-wrapper">
                <table
                  #port1588defaultStatusTableSort="matSort"
                  [dataSource]="port1588defaultStatusTableDataSource"
                  mat-table
                  matSort
                >
                  <ng-container matColumnDef="port">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'general.common_port.port' | translate }}
                    </th>
                    <td *matCellDef="let row" [ngClass]="utils.getPortClass(row)" mat-cell>{{ row.port }}</td>
                  </ng-container>
                  <ng-container matColumnDef="port-state">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'general.common_port.port_state' | translate }}
                    </th>
                    <td *matCellDef="let row" [ngClass]="utils.getTextClass(row.portState)" mat-cell>
                      {{ row.portState }}
                    </td>
                  </ng-container>
                  <ng-container *ngIf="!notSupport.pathDelay" matColumnDef="path-delay">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.path_delay' | translate }} ({{ 'general.unit.ns' | translate }})
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.pathDelay }}</td>
                  </ng-container>
                  <ng-container *ngIf="!notSupport.neighborPropDelay" matColumnDef="neighbor-prop-delay">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.neighbor_prop_delay_thresh' | translate }}
                      ({{ 'general.unit.ns' | translate }})
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.pathDelay }}</td>
                  </ng-container>
                  <ng-container matColumnDef="dummy">
                    <th *matHeaderCellDef mat-header-cell></th>
                    <td *matCellDef="let row" mat-cell></td>
                  </ng-container>
                  <tr *matHeaderRowDef="port1588defaultStatusTableDisplayedColumns; sticky: true" mat-header-row></tr>
                  <tr *matRowDef="let row; columns: port1588defaultStatusTableDisplayedColumns" mat-row></tr>
                </table>
              </div>
              <mat-paginator
                #port1588defaultStatusTablePaginator
                [ngClass]="{ disableChangePage: port1588defaultStatusTableDataLength <= 50 }"
                [hidePageSize]="true"
                [showFirstLastButtons]="false"
              ></mat-paginator>
            </div>
          </div>
        </mat-card>
        <mat-card *ngIf="hasProfile(ProfileType.PROFILE_IEC61850)">
          <mat-card-header fxLayout="column">
            <mat-card-title fxLayout="row" fxLayoutAlign="space-between center">
              <div ngClass="card-title">{{ 'features.time_sync.iec61850' | translate }}</div>
            </mat-card-title>
          </mat-card-header>
          <div fxLayout="row" fxLayoutAlign="space-between center">
            <div class="table-icon-tool-bar-div" fxFlex="70" fxFlex.lt-lg="50">
              <button id="button-refresh" (click)="refresh()" mat-icon-button>
                <mat-icon matTooltip="{{ 'general.common.refresh' | translate }}">refresh</mat-icon>
              </button>
            </div>
            <div class="filter-input-block" fxFlex="30" fxFlex.lt-lg="50">
              <mat-form-field class="form-field-search">
                <mat-icon class="icon-search" matPrefix>search</mat-icon>
                <input
                  (keyup)="updateTableFilter(ProfileType.PROFILE_IEC61850, 'status', $event.target.value)"
                  matInput
                  placeholder="{{ 'general.table_function.filter_desc' | translate }}"
                />
              </mat-form-field>
            </div>
          </div>
          <div id="dataContent">
            <div fxShow.xs="true" fxHide="true">
              <mat-expansion-panel class="expansion-panel-wrapper" *ngFor="let row of portIec61850StatusTableRows">
                <mat-expansion-panel-header>
                  <mat-panel-title [ngClass]="utils.getPortClass(row)">{{ row.port }}</mat-panel-title>
                </mat-expansion-panel-header>
                <p>{{ 'general.common_port.port' | translate }} : {{ row.port }}</p>
                <p>{{ 'general.common_port.port_status' | translate }} : {{ row.portState }}</p>
                <p>
                  {{ 'features.time_sync.path_delay' | translate }} ({{ 'general.unit.ns' | translate }}) :
                  {{ row.pathDelay }}
                </p>
              </mat-expansion-panel>
              <mat-paginator
                [length]="portIec61850StatusTableDataLength"
                [pageSize]="portIec61850StatusTablePanelPageSize"
                [hidePageSize]="true"
                (page)="onPanelPageChange(ProfileType.PROFILE_IEC61850, 'status', $event)"
              >
              </mat-paginator>
            </div>
            <div fxShow="true" fxHide.xs="true">
              <div class="app-table-wrapper">
                <table
                  #portIec61850StatusTableSort="matSort"
                  [dataSource]="portIec61850StatusTableDataSource"
                  mat-table
                  matSort
                >
                  <ng-container matColumnDef="port">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'general.common_port.port' | translate }}
                    </th>
                    <td *matCellDef="let row" [ngClass]="utils.getPortClass(row)" mat-cell>{{ row.port }}</td>
                  </ng-container>
                  <ng-container matColumnDef="port-state">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'general.common_port.port_state' | translate }}
                    </th>
                    <td *matCellDef="let row" [ngClass]="utils.getTextClass(row.portState)" mat-cell>
                      {{ row.portState }}
                    </td>
                  </ng-container>
                  <ng-container matColumnDef="path-delay">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.path_delay' | translate }} ({{ 'general.unit.ns' | translate }})
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.pathDelay }}</td>
                  </ng-container>
                  <ng-container matColumnDef="dummy">
                    <th *matHeaderCellDef mat-header-cell></th>
                    <td *matCellDef="let row" mat-cell></td>
                  </ng-container>
                  <tr *matHeaderRowDef="portIec61850StatusTableDisplayedColumns; sticky: true" mat-header-row></tr>
                  <tr *matRowDef="let row; columns: portIec61850StatusTableDisplayedColumns" mat-row></tr>
                </table>
              </div>
              <mat-paginator
                #portIec61850StatusTablePaginator
                [ngClass]="{ disableChangePage: portIec61850StatusTableDataLength <= 50 }"
                [hidePageSize]="true"
                [showFirstLastButtons]="false"
              ></mat-paginator>
            </div>
          </div>
        </mat-card>
        <mat-card *ngIf="hasProfile(ProfileType.PROFILE_C37_238)">
          <mat-card-header fxLayout="column">
            <mat-card-title fxLayout="row" fxLayoutAlign="space-between center">
              <div ngClass="card-title">{{ 'features.time_sync.c37238' | translate }}</div>
            </mat-card-title>
          </mat-card-header>
          <div fxLayout="row" fxLayoutAlign="space-between center">
            <div class="table-icon-tool-bar-div" fxFlex="70" fxFlex.lt-lg="50">
              <button id="button-refresh" (click)="refresh()" mat-icon-button>
                <mat-icon matTooltip="{{ 'general.common.refresh' | translate }}">refresh</mat-icon>
              </button>
            </div>
            <div class="filter-input-block" fxFlex="30" fxFlex.lt-lg="50">
              <mat-form-field class="form-field-search">
                <mat-icon class="icon-search" matPrefix>search</mat-icon>
                <input
                  (keyup)="updateTableFilter(ProfileType.PROFILE_C37_238, 'status', $event.target.value)"
                  matInput
                  placeholder="{{ 'general.table_function.filter_desc' | translate }}"
                />
              </mat-form-field>
            </div>
          </div>
          <div id="dataContent">
            <div fxShow.xs="true" fxHide="true">
              <mat-expansion-panel class="expansion-panel-wrapper" *ngFor="let row of portC37238StatusTableRows">
                <mat-expansion-panel-header>
                  <mat-panel-title [ngClass]="utils.getPortClass(row)">{{ row.port }}</mat-panel-title>
                </mat-expansion-panel-header>
                <p>{{ 'general.common_port.port' | translate }} : {{ row.port }}</p>
                <p>{{ 'general.common_port.port_status' | translate }} : {{ row.portState }}</p>
                <p>
                  {{ 'features.time_sync.path_delay' | translate }} ({{ 'general.unit.ns' | translate }}) :
                  {{ row.pathDelay }}
                </p>
              </mat-expansion-panel>
              <mat-paginator
                [length]="portC37238StatusTableDataLength"
                [pageSize]="portC37238StatusTablePanelPageSize"
                [hidePageSize]="true"
                (page)="onPanelPageChange(ProfileType.PROFILE_C37_238, 'status', $event)"
              >
              </mat-paginator>
            </div>
            <div fxShow="true" fxHide.xs="true">
              <div class="app-table-wrapper">
                <table
                  #portC37238StatusTableSort="matSort"
                  [dataSource]="portC37238StatusTableDataSource"
                  mat-table
                  matSort
                >
                  <ng-container matColumnDef="port">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'general.common_port.port' | translate }}
                    </th>
                    <td *matCellDef="let row" [ngClass]="utils.getPortClass(row)" mat-cell>{{ row.port }}</td>
                  </ng-container>
                  <ng-container matColumnDef="port-state">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'general.common_port.port_state' | translate }}
                    </th>
                    <td *matCellDef="let row" [ngClass]="utils.getTextClass(row.portState)" mat-cell>
                      {{ row.portState }}
                    </td>
                  </ng-container>
                  <ng-container matColumnDef="path-delay">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'features.time_sync.path_delay' | translate }} ({{ 'general.unit.ns' | translate }})
                    </th>
                    <td *matCellDef="let row" mat-cell>{{ row.pathDelay }}</td>
                  </ng-container>
                  <ng-container matColumnDef="dummy">
                    <th *matHeaderCellDef mat-header-cell></th>
                    <td *matCellDef="let row" mat-cell></td>
                  </ng-container>
                  <tr *matHeaderRowDef="portC37238StatusTableDisplayedColumns; sticky: true" mat-header-row></tr>
                  <tr *matRowDef="let row; columns: portC37238StatusTableDisplayedColumns" mat-row></tr>
                </table>
              </div>
              <mat-paginator
                #portC37238StatusTablePaginator
                [ngClass]="{ disableChangePage: portC37238StatusTableDataLength <= 50 }"
                [hidePageSize]="true"
                [showFirstLastButtons]="false"
              ></mat-paginator>
            </div>
          </div>
        </mat-card>
      </mat-tab>
    </mat-tab-group>
  </div>
</div>
