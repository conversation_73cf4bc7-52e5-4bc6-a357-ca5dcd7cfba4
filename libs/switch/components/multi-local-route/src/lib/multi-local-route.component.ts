import { AfterViewInit, ChangeDetectionStrategy, Component, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { SelectionModel } from '@angular/cdk/collections';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { MatLegacyDialog as MatDialog } from '@angular/material/legacy-dialog';
import { MatLegacyPaginator as MatPaginator } from '@angular/material/legacy-paginator';
import { MatLegacySnackBar as MatSnackBar } from '@angular/material/legacy-snack-bar';
import { MatLegacyTableDataSource as MatTableDataSource } from '@angular/material/legacy-table';
import { MatSort } from '@angular/material/sort';

import { TranslateService } from '@ngx-translate/core';
import { cloneDeep, orderBy, sortBy } from 'lodash-es';
import { forkJoin, Observable } from 'rxjs';

import * as environment from '@switch-web/shared/environments/environment';
import { AppState } from '@switch-web/shared/app/app.service';
import { GlobalEvent, GlobalEventType } from '@switch-web/shared/app/global.event';
import { mediumDialogConfig, smallDialogConfig } from '@switch-web/shared/dialog/dialog-config.service';
import { AuthService } from '@switch-web/shared/service/auth.service';
import { ErrorService } from '@switch-web/shared/service/error.service';
import { UtilsService } from '@switch-web/shared/service/utils.service';

import { MaclRuleDeleteDialogComponent } from './macl-rule-delete-dialog/macl-rule-delete-dialog';
import { MaclRuleSettingDialogComponent } from './macl-rule-setting-dialog/macl-rule-setting-dialog';
import { RouteDeleteDialogComponent } from './route-delete-dialog/route-delete-dialog.component';
import { RouteSettingDialogComponent } from './route-setting-dialog/route-setting-dialog.component';

@Component({
  templateUrl: './multi-local-route.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MultiLocalRouteComponent implements AfterViewInit {
  @ViewChild('multiLocalRouteTableSort') multiLocalRouteTableSort: MatSort;
  @ViewChild('multiLocalRouteTablePaginator') multiLocalRouteTablePaginator: MatPaginator;
  multiLocalRouteTableDisplayedColumns: string[] = ['select', 'edit', 'src-vlan', 'downstream-vlan', 'dummy'];
  multiLocalRouteTableDataSource: MatTableDataSource<any> = new MatTableDataSource();
  multiLocalRouteTableSelection = new SelectionModel<any>(true, []);
  multiLocalRouteTableSize;
  multiLocalRouteTableData = [];
  multiLocalRouteTableRows = [];
  multiLocalRouteTableDataLength;

  @ViewChild('mAclTableSort') mAclTableSort: MatSort;
  @ViewChild('mAclTablePaginator') mAclTablePaginator: MatPaginator;
  mAclTableDisplayedColumns: string[] = [
    'select',
    'edit',
    'macl-id',
    'multicast-group',
    'group-mask',
    'src-ip',
    'src-mask',
    'src-vlan',
    'macl-downstream-vlan',
    'action',
    'dummy',
  ];
  mAclTableDataSource: MatTableDataSource<any> = new MatTableDataSource();
  mAclTableSelection = new SelectionModel<any>(true, []);
  mAclTableSize;
  mAclTableData = [];
  mAclTableRows = [];
  mAclTableDataLength;

  generalSettingForm = new UntypedFormGroup({
    enable: new UntypedFormControl(null, Validators.required),
    vrrpMasterOnly: new UntypedFormControl(null, Validators.required),
  });

  isDraggingRule = false;
  noPermission = false;

  private aclIdList = [];
  private dragIndex;
  private uriRequestData = {
    ifTable: null,
    vlanRouteTable: null,
    ruleTable: null,
  };

  private readonly selectCol = 'select';
  private readonly dragCol = 'drag';

  constructor(
    public utils: UtilsService,
    private appState: AppState,
    private auth: AuthService,
    private http: HttpClient,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private errorService: ErrorService,
    private translate: TranslateService,
    private activatedRoute: ActivatedRoute
  ) {
    this.noPermission = this.auth.authNodes.multiLocalRoute;
    this.activatedRoute.data.subscribe(data => {
      this.multiLocalRouteTableSize = data.tableSize.vlanRouteTable;
      this.mAclTableSize = data.tableSize.aclRuleTable;
    });
  }

  ngAfterViewInit(): void {
    this.setupTableSetting();
    this.getPageData().subscribe({
      complete: () => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
      },
      error: error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      },
    });
  }

  getPageData(): Observable<any> {
    return new Observable(observer => {
      this.aclIdList = Array(16)
        .fill(0)
        .map((x, idx) => idx + 1);
      this.multiLocalRouteTableData = [];
      this.mAclTableData = [];
      this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
      forkJoin({
        mxMulticastRouting: this.http.get(environment.uriRequestURL + '/setting/data/mxMulticastRouting'),
        mxMcastLocalRoute: this.http.get(environment.uriRequestURL + '/setting/data/mxMcastLocalRoute'),
        ifTable: this.http.get(environment.uriRequestURL + '/status/ipInterface/ifTable'),
      }).subscribe(
        (data: any) => {
          this.uriRequestData.ifTable = data.ifTable;

          // general setting
          this.generalSettingForm.patchValue({
            enable: data.mxMulticastRouting.mcastLocalRoute,
            vrrpMasterOnly: data.mxMcastLocalRoute.vrrpMasterOnly,
          });

          this.uriRequestData.vlanRouteTable = data.mxMcastLocalRoute.vlanRouteTable;
          data.mxMcastLocalRoute.vlanRouteTable.forEach((route, index: number) => {
            this.multiLocalRouteTableData.push({
              key: index,
              srcVlan: 'vlan' + route.sourceVlan,
              srcVlanRaw: route.sourceVlan,
              downstreamVlan: route.downstreamVlan.map(vid => 'vlan' + vid).join(', '),
              downstreamVlanRaw: route.downstreamVlan,
            });
          });

          this.uriRequestData.ruleTable = data.mxMcastLocalRoute.aclRuleTable;
          data.mxMcastLocalRoute.aclRuleTable.forEach((rule, index: number) => {
            this.aclIdList.splice(
              this.aclIdList.findIndex(id => id === rule.aclId),
              1
            );
            this.mAclTableData.push({
              key: index,
              maclId: rule.aclId,
              groupIp: rule.groupAddr === '0.0.0.0' ? this.translate.instant('general.common.any') : rule.groupAddr,
              groupIpRaw: rule.groupAddr,
              groupMask: rule.groupMask === '0.0.0.0' ? this.translate.instant('general.common.any') : rule.groupMask,
              groupMaskRaw: rule.groupMask,
              sourceIp: rule.sourceAddr === '0.0.0.0' ? this.translate.instant('general.common.any') : rule.sourceAddr,
              sourceIpRaw: rule.sourceAddr,
              sourceMask:
                rule.sourceMask === '0.0.0.0' ? this.translate.instant('general.common.any') : rule.sourceMask,
              sourceMaskRaw: rule.sourceMask,
              sourceVid: rule.sourceVlan === 0 ? this.translate.instant('general.common.any') : rule.sourceVlan,
              sourceVidRaw: rule.sourceVlan,
              downstreamVid:
                rule.downstreamVlan === 0 ? this.translate.instant('general.common.any') : rule.downstreamVlan,
              downstreamVidRaw: rule.downstreamVlan,
              action: rule.action
                ? this.translate.instant('features.acl.permit')
                : this.translate.instant('features.acl.deny'),
              actionRaw: rule.action,
            });
          });
          this.mAclTableData = sortBy(this.mAclTableData, entry => entry.maclId);

          this.multiLocalRouteTableDataSource.data = this.multiLocalRouteTableData;
          this.multiLocalRouteTableRows = this.multiLocalRouteTableData;
          this.multiLocalRouteTableDataLength = this.multiLocalRouteTableData.length;
          this.multiLocalRouteTableSelection.clear();

          this.mAclTableDataSource.data = this.mAclTableData;
          this.mAclTableRows = this.mAclTableData;
          this.mAclTableDataLength = this.mAclTableData.length;
          this.mAclTableSelection.clear();

          observer.complete();
        },
        error => {
          observer.error(error);
        }
      );
    });
  }

  settingMultiLocalRoute(rowData?) {
    mediumDialogConfig.data = {
      row: rowData,
      vlanRouteTable: cloneDeep(this.uriRequestData.vlanRouteTable),
      ifTable: cloneDeep(this.uriRequestData.ifTable),
    };
    const dialogRef = this.dialog.open(RouteSettingDialogComponent, mediumDialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        this.getPageData().subscribe({
          complete: () => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          },
          error: error => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            this.errorService.handleError(error);
          },
        });
      }
    });
  }

  deleteMultiLocalRoute(row?: any): void {
    smallDialogConfig.data = {
      selectedRows: [],
    };
    if (row) {
      smallDialogConfig.data.selectedRows.push(row);
    } else {
      let selectedData = this.multiLocalRouteTableSelection.selected;
      selectedData = orderBy(selectedData, ['key'], ['desc']);
      smallDialogConfig.data.selectedRows = selectedData;
    }

    const dialogRef = this.dialog.open(RouteDeleteDialogComponent, smallDialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        this.getPageData().subscribe({
          complete: () => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          },
          error: error => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            this.errorService.handleError(error);
          },
        });
      }
    });
  }

  settingRule(rowData?) {
    mediumDialogConfig.data = {
      row: rowData,
      mAclIdList: this.aclIdList,
    };
    const dialogRef = this.dialog.open(MaclRuleSettingDialogComponent, mediumDialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        this.getPageData().subscribe({
          complete: () => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          },
          error: error => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            this.errorService.handleError(error);
          },
        });
      }
    });
  }

  deleteRule(row?: any): void {
    smallDialogConfig.data = {
      selectedRows: [],
      ruleTable: cloneDeep(this.uriRequestData.ruleTable),
    };
    if (row) {
      smallDialogConfig.data.selectedRows.push(row);
    } else {
      let selectedData = this.mAclTableSelection.selected;
      selectedData = orderBy(selectedData, ['key'], ['desc']);
      smallDialogConfig.data.selectedRows = selectedData;
    }
    const dialogRef = this.dialog.open(MaclRuleDeleteDialogComponent, smallDialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result === 'submit') {
        this.getPageData().subscribe({
          complete: () => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
          },
          error: error => {
            this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
            this.errorService.handleError(error);
          },
        });
      }
    });
  }

  updateSetting(): void {
    if (this.generalSettingForm.invalid) {
      this.generalSettingForm.markAllAsTouched();
      return;
    }
    this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
    forkJoin([
      this.http.patch(
        environment.uriRequestURL + '/setting/data/mxMulticastRouting/mcastLocalRoute',
        this.generalSettingForm.value.enable
      ),
      this.http.patch(
        environment.uriRequestURL + '/setting/data/mxMcastLocalRoute/vrrpMasterOnly',
        this.generalSettingForm.value.vrrpMasterOnly
      ),
    ]).subscribe(
      () => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.snackBar.open(this.translate.instant('response_handler.res_global_success'), '', {
          duration: 3000,
        });
      },
      error => {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
        this.errorService.handleError(error);
      }
    );
  }

  setupTableSetting(): void {
    this.multiLocalRouteTableDataSource.paginator = this.multiLocalRouteTablePaginator;
    this.multiLocalRouteTableDataSource.sort = this.multiLocalRouteTableSort;
    this.multiLocalRouteTableDataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'src-vlan':
          return item.srcVlanRaw;
        case 'downstream-vlan':
          return item.downstreamVlan;
        default:
          return item[property];
      }
    };
    this.multiLocalRouteTableDataSource.filterPredicate = (data: any, filter: string): boolean => {
      return (
        String(data.srcVlan).toLowerCase().indexOf(filter) !== -1 ||
        String(data.downstreamVlan).toLowerCase().indexOf(filter) !== -1
      );
    };

    this.mAclTableDataSource.paginator = this.mAclTablePaginator;
    this.mAclTableDataSource.sort = this.mAclTableSort;
    this.mAclTableDataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'macl-id':
          return item.maclId;
        case 'multicast-group':
          return item.groupIp;
        case 'group-mask':
          return item.groupMask;
        case 'src-ip':
          return item.sourceIp;
        case 'src-mask':
          return item.sourceMask;
        case 'src-vlan':
          return item.sourceVid;
        case 'macl-downstream-vlan':
          return item.downstreamVid;
        default:
          return item[property];
      }
    };
    this.mAclTableDataSource.filterPredicate = (data: any, filter: string): boolean => {
      return (
        String(data.maclId).toLowerCase().indexOf(filter) !== -1 ||
        String(data.groupIp).toLowerCase().indexOf(filter) !== -1 ||
        String(data.groupMask).toLowerCase().indexOf(filter) !== -1 ||
        String(data.sourceIp).toLowerCase().indexOf(filter) !== -1 ||
        String(data.sourceMask).toLowerCase().indexOf(filter) !== -1 ||
        String(data.sourceVid).toLowerCase().indexOf(filter) !== -1 ||
        String(data.downstreamVid).toLowerCase().indexOf(filter) !== -1 ||
        String(data.action).toLowerCase().indexOf(filter) !== -1
      );
    };
  }

  masterToggle(selectionTable: string): void {
    switch (selectionTable) {
      case 'multiLocalRouteTable':
        this.isAllSelected('multiLocalRouteTable')
          ? this.multiLocalRouteTableSelection.clear()
          : this.multiLocalRouteTableDataSource.data.forEach(row => this.multiLocalRouteTableSelection.select(row));
        break;
      case 'mAclTable':
        this.isAllSelected('mAclTable')
          ? this.mAclTableSelection.clear()
          : this.mAclTableDataSource.data.forEach(row => this.mAclTableSelection.select(row));
        break;
    }
  }

  isAllSelected(selectionTable: string): boolean {
    let numSelected;
    let numRows;
    switch (selectionTable) {
      case 'multiLocalRouteTable':
        numSelected = this.multiLocalRouteTableSelection.selected.length;
        numRows = this.multiLocalRouteTableDataSource.data.length;
        break;
      case 'mAclTable':
        numSelected = this.mAclTableSelection.selected.length;
        numRows = this.mAclTableDataSource.data.length;
        break;
    }
    return numSelected === numRows;
  }

  updateFilter(dataSource: MatTableDataSource<any>, filterValue: string): void {
    dataSource.filter = filterValue.trim().toLowerCase();
  }

  reorderRule(): void {
    if (!this.isDraggingRule) {
      this.mAclTableSort.sort({ id: '', start: 'asc', disableClear: false });
      this.mAclTableSort.disabled = true;
      this.mAclTableDisplayedColumns[0] = this.dragCol;
    } else {
      let isDirty = false;
      const patchData = [];
      const maclIds = this.mAclTableData.map(r => r.maclId).sort((a, b) => a - b);
      this.mAclTableData.forEach((row, rowIndex) => {
        if (row.maclId !== rowIndex + 1) {
          isDirty = true;
        }
        const newRow = { ...this.uriRequestData.ruleTable[row.key] };
        newRow.aclId = maclIds[rowIndex];
        patchData.push(newRow);
      });
      if (isDirty) {
        this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: true }));
        this.http
          .patch(environment.uriRequestURL + '/setting/data/mxMcastLocalRoute/aclRuleTable', patchData)
          .subscribe(
            () => {
              this.getPageData().subscribe({
                complete: () => {
                  this.snackBar.open(this.translate.instant('response_handler.res_global_success'), '', {
                    duration: 3000,
                  });
                  this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
                },
                error: error => {
                  this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
                  this.errorService.handleError(error);
                },
              });
            },
            error => {
              this.appState.emitEvent(new GlobalEvent(GlobalEventType.LOADING, { show: false }));
              this.errorService.handleError(error);
            }
          );
      }
      this.mAclTableSort.disabled = false;
      this.mAclTableDisplayedColumns[0] = this.selectCol;
    }
    this.isDraggingRule = !this.isDraggingRule;
  }

  dragStarted(row): void {
    this.dragIndex = this.mAclTableData.findIndex(data => data.key === row.key);
  }

  dropListDropped(event: CdkDragDrop<string[]>): void {
    moveItemInArray(this.mAclTableData, this.dragIndex, event.currentIndex);
    this.mAclTableDataSource.data = this.mAclTableData;
  }
}
